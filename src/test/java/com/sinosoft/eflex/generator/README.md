# MyBatis-Plus代码生成器使用说明

## 概述

本项目提供了两个MyBatis-Plus代码生成器：
- `MybatisPlusGenerator.java` - 基础版代码生成器
- `CodeGenerator.java` - 增强版代码生成器（推荐使用）

## 功能特性

### ✨ 主要功能
- 🚀 自动生成Entity、Mapper、Service、Controller等完整代码
- 📁 生成路径与项目现有结构保持一致
- 🏷️ 支持常用注解（@RestController、@Service、@Mapper等）
- 📝 支持Lombok注解，简化代码
- 📚 支持Swagger2注解，便于API文档生成
- ⏰ 自动配置字段填充（创建时间、更新时间等）
- 🎯 支持指定表名生成，避免生成所有表
- 🔧 支持表前缀过滤（fc_、fd_等）

### 📂 生成目录结构
```
src/main/java/com/sinosoft/eflex/
├── model/           # 实体类（Entity）
├── dao/             # Mapper接口
├── service/         # Service接口
├── service/impl/    # Service实现类
└── ctrl/            # Controller控制器

src/main/resources/
└── mappers/         # Mapper XML文件
```

## 环境要求

### 📋 依赖配置
确保pom.xml中包含以下依赖：

```xml
<!-- MyBatis-Plus核心依赖 -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.4.1</version>
</dependency>

<!-- MyBatis-Plus代码生成器依赖 -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-generator</artifactId>
    <version>3.5.3.2</version>
    <scope>test</scope>
</dependency>

<!-- 代码生成器模板引擎 -->
<dependency>
    <groupId>org.apache.velocity</groupId>
    <artifactId>velocity-engine-core</artifactId>
    <version>2.3</version>
    <scope>test</scope>
</dependency>
```

### 🗄️ 数据库配置
代码生成器支持以下数据库环境：

**开发环境：**
- 地址：`10.9.10.164:3306/hqeflexdb`
- 用户名：`eflex`
- 密码：`eflex`

**UAT环境：**
- 地址：`rm-j5e34e8dmh87222f0.mysql.rds.aliyuncs.com:3306/uflexdb`
- 用户名：`u_uflexdb_user`
- 密码：`ZcZYOm1YKVDV`

## 使用方法

### 🚀 快速开始

1. **运行代码生成器**
   ```bash
   # 在IDE中运行
   src/test/java/com/sinosoft/eflex/generator/CodeGenerator.java
   ```

2. **选择数据库环境**
   ```
   请选择数据库环境：
   1. 开发环境 (10.9.10.164:3306/hqeflexdb)
   2. UAT环境 (rm-j5e34e8dmh87222f0.mysql.rds.aliyuncs.com:3306/uflexdb)
   请输入选择 (1-2): 1
   ```

3. **选择生成模式**
   ```
   请选择生成模式：
   1. 指定表名生成（推荐）
   2. 生成所有表（谨慎使用）
   请输入选择 (1-2): 1
   ```

4. **输入表名**
   ```
   请输入要生成的表名：
   • 多个表名用逗号分隔，如：fc_user,fc_role,fc_permission
   • 支持表前缀：fc_, fd_, t_
   表名: fc_user,fc_role
   ```

5. **确认生成**
   ```
   生成配置确认：
   数据库环境：开发环境
   数据库地址：***************************************?...
   生成表名：[fc_user, fc_role]
   生成路径：/path/to/your/project
   生成时间：2024-01-01 10:00:00
   确认生成代码？(y/n): y
   ```

### 📝 使用示例

#### 示例1：生成单个表
```
表名: fc_user
```
生成文件：
- `User.java` (Entity)
- `UserMapper.java` (Mapper接口)
- `UserMapper.xml` (Mapper XML)
- `UserService.java` (Service接口)
- `UserServiceImpl.java` (Service实现)
- `UserController.java` (Controller)

#### 示例2：生成多个表
```
表名: fc_user,fc_role,fc_permission
```
将为每个表生成完整的代码文件。

#### 示例3：生成带前缀的表
```
表名: fd_com,fc_grp_info
```
生成的类名会自动去掉前缀：
- `fd_com` → `Com.java`
- `fc_grp_info` → `GrpInfo.java`

## 生成的代码特性

### 🏷️ 注解支持
- **Entity类**：`@Data`、`@TableName`、`@TableId`、`@TableField`
- **Mapper接口**：`@Mapper`
- **Service接口**：无特殊注解
- **Service实现**：`@Service`
- **Controller**：`@RestController`、`@RequestMapping`、`@Api`

### ⏰ 字段填充
自动配置以下字段的填充策略：
- `create_time` - 插入时填充
- `update_time` - 插入和更新时填充
- `create_user` - 插入时填充
- `update_user` - 插入和更新时填充

### 🎯 命名策略
- **表名映射**：下划线转驼峰（`user_info` → `UserInfo`）
- **字段映射**：下划线转驼峰（`user_name` → `userName`）
- **Controller映射**：驼峰转连字符（`UserInfo` → `/user-info`）

## 注意事项

### ⚠️ 重要提醒
1. **备份现有代码**：生成前请备份现有文件，避免覆盖重要代码
2. **检查生成结果**：生成后请检查代码是否符合业务需求
3. **调整业务逻辑**：根据实际情况添加业务逻辑和数据验证
4. **数据库权限**：确保数据库用户有足够的权限读取表结构

### 🔧 自定义配置
如需修改配置，请编辑以下常量：

```java
// 包配置
private static final String PACKAGE_PARENT = "com.sinosoft.eflex";
private static final String PACKAGE_ENTITY = "model";
private static final String PACKAGE_MAPPER = "dao";
// ...

// 表前缀配置
private static final String[] TABLE_PREFIXES = {"fc_", "fd_", "t_"};

// 作者信息
private static final String AUTHOR = "CodeGenerator";
```

### 🐛 常见问题

**Q: 生成失败，提示数据库连接错误？**
A: 请检查数据库连接配置是否正确，确保网络连通性。

**Q: 生成的文件路径不正确？**
A: 请确保在项目根目录下运行代码生成器。

**Q: 表名输入后提示找不到表？**
A: 请检查表名是否正确，注意大小写和前缀。

**Q: 生成的代码编译错误？**
A: 请确保项目中已添加MyBatis-Plus相关依赖。

## 版本信息

- **MyBatis-Plus**: 3.5.4.1
- **代码生成器**: 3.5.3.2
- **模板引擎**: Velocity 2.3
- **支持的数据库**: MySQL 8.0+

## 更新日志

### v1.0 (2024-01-01)
- ✨ 初始版本发布
- 🚀 支持基础代码生成功能
- 📁 配置项目目录结构
- 🏷️ 添加常用注解支持
- ⏰ 配置字段自动填充

## 文件说明

### 📁 代码生成器文件
- `MybatisPlusGenerator.java` - 基础版代码生成器
- `CodeGenerator.java` - 增强版代码生成器（推荐使用）
- `QuickGenerator.java` - 快速代码生成器

### 📁 配置和示例文件
- `MybatisPlusConfig.java` - MyBatis-Plus配置示例
- `GeneratorTest.java` - 测试用例和使用示例
- `README.md` - 本说明文档

### 📁 生成的代码示例
生成的代码将包含以下特性：

**Entity示例：**
```java
@Data
@TableName("fc_user")
@ApiModel(value = "User对象", description = "用户表")
public class User implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
```

**Controller示例：**
```java
@RestController
@RequestMapping("/user")
@Api(tags = "用户管理")
public class UserController {

    @Autowired
    private UserService userService;

    @GetMapping("/list")
    @ApiOperation("查询用户列表")
    public Result<List<User>> list() {
        return Result.success(userService.list());
    }

    @PostMapping
    @ApiOperation("新增用户")
    public Result<Boolean> save(@RequestBody User user) {
        return Result.success(userService.save(user));
    }
}
```

## 完整使用流程

### 🚀 第一次使用
1. **检查依赖**：运行 `GeneratorTest.verifyDependencies()` 检查依赖是否完整
2. **测试连接**：运行 `GeneratorTest.testDatabaseConnection()` 测试数据库连接
3. **生成代码**：选择合适的生成器运行代码生成
4. **配置项目**：将 `MybatisPlusConfig.java` 复制到项目配置目录
5. **测试验证**：编译项目并测试生成的代码

### 🔄 日常使用
1. **快速生成**：修改 `QuickGenerator.TABLE_NAMES` 常量，直接运行
2. **批量生成**：使用 `CodeGenerator` 进行交互式生成
3. **代码调整**：根据业务需求调整生成的代码

---

## 技术支持

如有问题或建议，请联系开发团队。

**常用命令：**
```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test -Dtest=GeneratorTest

# 检查依赖
mvn dependency:tree | grep mybatis-plus
```
