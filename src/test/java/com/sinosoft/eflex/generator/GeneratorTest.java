package com.sinosoft.eflex.generator;

import org.junit.Test;

/**
 * 代码生成器测试类
 * 
 * 提供了多种测试方法来验证代码生成器的功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class GeneratorTest {

    /**
     * 测试基础代码生成器
     * 生成指定表的代码
     */
    @Test
    public void testMybatisPlusGenerator() {
        System.out.println("=== 测试基础代码生成器 ===");
        
        // 注意：这里需要手动输入表名，或者修改生成器代码以支持程序化输入
        // MybatisPlusGenerator.main(new String[]{});
        
        System.out.println("请手动运行 MybatisPlusGenerator.main() 方法");
    }

    /**
     * 测试增强代码生成器
     * 提供更多配置选项和交互功能
     */
    @Test
    public void testCodeGenerator() {
        System.out.println("=== 测试增强代码生成器 ===");
        
        // 注意：这里需要手动输入配置，或者修改生成器代码以支持程序化输入
        // CodeGenerator.main(new String[]{});
        
        System.out.println("请手动运行 CodeGenerator.main() 方法");
    }

    /**
     * 测试快速代码生成器
     * 适合快速生成少量表的代码
     */
    @Test
    public void testQuickGenerator() {
        System.out.println("=== 测试快速代码生成器 ===");
        
        try {
            // 直接运行快速生成器
            QuickGenerator.main(new String[]{});
            System.out.println("✅ 快速代码生成器测试完成");
        } catch (Exception e) {
            System.err.println("❌ 快速代码生成器测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试数据库连接
     * 验证数据库连接配置是否正确
     */
    @Test
    public void testDatabaseConnection() {
        System.out.println("=== 测试数据库连接 ===");
        
        // 开发环境连接测试
        testConnection(
            "**********************************************************************************************************************************************************************************",
            "eflex",
            "eflex",
            "开发环境"
        );
        
        // UAT环境连接测试（注释掉以避免不必要的连接）
        /*
        testConnection(
            "****************************************************************************************************************************************************************************************************************",
            "u_uflexdb_user",
            "ZcZYOm1YKVDV",
            "UAT环境"
        );
        */
    }

    /**
     * 测试数据库连接
     */
    private void testConnection(String url, String username, String password, String env) {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            java.sql.Connection connection = java.sql.DriverManager.getConnection(url, username, password);
            
            if (connection != null && !connection.isClosed()) {
                System.out.println("✅ " + env + " 数据库连接成功");
                
                // 测试查询表信息
                java.sql.DatabaseMetaData metaData = connection.getMetaData();
                java.sql.ResultSet tables = metaData.getTables(null, null, "fc_%", new String[]{"TABLE"});
                
                int tableCount = 0;
                System.out.println("📋 " + env + " 中的fc_开头的表：");
                while (tables.next() && tableCount < 5) { // 只显示前5个表
                    String tableName = tables.getString("TABLE_NAME");
                    System.out.println("  • " + tableName);
                    tableCount++;
                }
                
                if (tableCount == 0) {
                    System.out.println("  未找到fc_开头的表");
                } else if (tableCount == 5) {
                    System.out.println("  ... (还有更多表)");
                }
                
                connection.close();
            } else {
                System.err.println("❌ " + env + " 数据库连接失败");
            }
        } catch (Exception e) {
            System.err.println("❌ " + env + " 数据库连接异常: " + e.getMessage());
        }
    }

    /**
     * 打印使用说明
     */
    @Test
    public void printUsageInstructions() {
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                    代码生成器使用说明                          ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();
        
        System.out.println("📚 可用的代码生成器：");
        System.out.println();
        
        System.out.println("1️⃣  MybatisPlusGenerator.java - 基础版代码生成器");
        System.out.println("   • 功能：基础的代码生成功能");
        System.out.println("   • 适用：简单的代码生成需求");
        System.out.println("   • 使用：运行main方法，根据提示输入表名");
        System.out.println();
        
        System.out.println("2️⃣  CodeGenerator.java - 增强版代码生成器（推荐）");
        System.out.println("   • 功能：完整的交互式代码生成");
        System.out.println("   • 适用：生产环境使用，支持多种配置");
        System.out.println("   • 使用：运行main方法，按照向导操作");
        System.out.println();
        
        System.out.println("3️⃣  QuickGenerator.java - 快速代码生成器");
        System.out.println("   • 功能：快速生成指定表的代码");
        System.out.println("   • 适用：开发过程中快速生成代码");
        System.out.println("   • 使用：修改TABLE_NAMES常量，直接运行");
        System.out.println();
        
        System.out.println("🔧 配置文件：");
        System.out.println("   • MybatisPlusConfig.java - MyBatis-Plus配置示例");
        System.out.println("   • README.md - 详细使用说明文档");
        System.out.println();
        
        System.out.println("📁 生成的代码结构：");
        System.out.println("   src/main/java/com/sinosoft/eflex/");
        System.out.println("   ├── model/           # 实体类");
        System.out.println("   ├── dao/             # Mapper接口");
        System.out.println("   ├── service/         # Service接口");
        System.out.println("   ├── service/impl/    # Service实现类");
        System.out.println("   └── ctrl/            # Controller控制器");
        System.out.println();
        System.out.println("   src/main/resources/");
        System.out.println("   └── mappers/         # Mapper XML文件");
        System.out.println();
        
        System.out.println("⚠️  注意事项：");
        System.out.println("   • 生成前请备份现有代码");
        System.out.println("   • 确保数据库连接配置正确");
        System.out.println("   • 生成后请检查代码是否符合业务需求");
        System.out.println("   • 根据实际情况调整业务逻辑");
        System.out.println();
        
        System.out.println("🚀 快速开始：");
        System.out.println("   1. 运行 testDatabaseConnection() 测试数据库连接");
        System.out.println("   2. 修改 QuickGenerator 中的 TABLE_NAMES 常量");
        System.out.println("   3. 运行 testQuickGenerator() 生成代码");
        System.out.println("   4. 检查生成的代码并进行必要的调整");
    }

    /**
     * 验证项目依赖
     */
    @Test
    public void verifyDependencies() {
        System.out.println("=== 验证项目依赖 ===");
        
        // 检查MyBatis-Plus核心依赖
        try {
            Class.forName("com.baomidou.mybatisplus.core.MybatisConfiguration");
            System.out.println("✅ MyBatis-Plus核心依赖正常");
        } catch (ClassNotFoundException e) {
            System.err.println("❌ 缺少MyBatis-Plus核心依赖");
        }
        
        // 检查代码生成器依赖
        try {
            Class.forName("com.baomidou.mybatisplus.generator.AutoGenerator");
            System.out.println("✅ MyBatis-Plus代码生成器依赖正常");
        } catch (ClassNotFoundException e) {
            System.err.println("❌ 缺少MyBatis-Plus代码生成器依赖");
        }
        
        // 检查模板引擎依赖
        try {
            Class.forName("org.apache.velocity.app.VelocityEngine");
            System.out.println("✅ Velocity模板引擎依赖正常");
        } catch (ClassNotFoundException e) {
            System.err.println("❌ 缺少Velocity模板引擎依赖");
        }
        
        // 检查MySQL驱动
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            System.out.println("✅ MySQL驱动正常");
        } catch (ClassNotFoundException e) {
            System.err.println("❌ 缺少MySQL驱动");
        }
        
        System.out.println();
        System.out.println("如果有依赖缺失，请检查pom.xml中的依赖配置。");
    }
}
