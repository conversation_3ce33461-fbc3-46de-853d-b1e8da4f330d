package com.sinosoft.eflex.generator;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;
import com.baomidou.mybatisplus.core.toolkit.StringPool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Scanner;

/**
 * MyBatis-Plus代码生成器
 * 
 * 功能说明：
 * 1. 自动生成Entity、Mapper、Service、Controller等代码
 * 2. 支持指定表名生成，避免生成所有表
 * 3. 生成的代码包含常用注解和规范命名
 * 4. 支持自定义包名和路径配置
 * 
 * 使用方法：
 * 1. 运行main方法
 * 2. 根据提示输入要生成的表名（多个表名用逗号分隔）
 * 3. 代码将自动生成到指定目录
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class MybatisPlusGenerator {

    /**
     * 数据库配置
     */
    private static final String DB_URL = "**********************************************************************************************************************************************************************************";
    private static final String DB_USERNAME = "eflex";
    private static final String DB_PASSWORD = "eflex";
    private static final String DB_DRIVER = "com.mysql.cj.jdbc.Driver";

    /**
     * 包配置
     */
    private static final String PACKAGE_PARENT = "com.sinosoft.eflex";
    private static final String PACKAGE_ENTITY = "model";
    private static final String PACKAGE_MAPPER = "dao";
    private static final String PACKAGE_SERVICE = "service";
    private static final String PACKAGE_SERVICE_IMPL = "service.impl";
    private static final String PACKAGE_CONTROLLER = "ctrl";

    /**
     * 作者信息
     */
    private static final String AUTHOR = "MybatisPlusGenerator";

    /**
     * 项目路径配置
     */
    private static final String PROJECT_PATH = System.getProperty("user.dir");
    private static final String JAVA_PATH = "/src/main/java";
    private static final String RESOURCES_PATH = "/src/main/resources";

    public static void main(String[] args) {
        // 获取用户输入的表名
        Scanner scanner = new Scanner(System.in);
        System.out.println("=== MyBatis-Plus代码生成器 ===");
        System.out.println("请输入要生成的表名（多个表名用逗号分隔，如：user,role,permission）：");
        String tableNames = scanner.nextLine();
        
        if (tableNames == null || tableNames.trim().isEmpty()) {
            System.out.println("表名不能为空！");
            return;
        }

        // 解析表名
        String[] tables = tableNames.split(",");
        for (int i = 0; i < tables.length; i++) {
            tables[i] = tables[i].trim();
        }

        System.out.println("将要生成以下表的代码：" + Arrays.toString(tables));
        System.out.println("确认生成？(y/n)：");
        String confirm = scanner.nextLine();
        
        if (!"y".equalsIgnoreCase(confirm) && !"yes".equalsIgnoreCase(confirm)) {
            System.out.println("已取消生成。");
            return;
        }

        // 执行代码生成
        generateCode(tables);
        
        scanner.close();
        System.out.println("代码生成完成！");
    }

    /**
     * 执行代码生成
     * 
     * @param tableNames 表名数组
     */
    private static void generateCode(String[] tableNames) {
        // 创建代码生成器
        AutoGenerator mpg = new AutoGenerator();

        // 全局配置
        GlobalConfig gc = new GlobalConfig();
        gc.setOutputDir(PROJECT_PATH + JAVA_PATH);
        gc.setAuthor(AUTHOR);
        gc.setOpen(false); // 生成后不打开文件夹
        gc.setFileOverride(true); // 覆盖已有文件
        gc.setServiceName("%sService"); // Service接口命名方式
        gc.setServiceImplName("%sServiceImpl"); // Service实现类命名方式
        gc.setMapperName("%sMapper"); // Mapper接口命名方式
        gc.setXmlName("%sMapper"); // Mapper XML文件命名方式
        gc.setControllerName("%sController"); // Controller命名方式
        gc.setIdType(IdType.AUTO); // 主键策略
        gc.setDateType(DateType.ONLY_DATE); // 日期类型
        gc.setSwagger2(true); // 开启Swagger2注解
        mpg.setGlobalConfig(gc);

        // 数据源配置
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setUrl(DB_URL);
        dsc.setDriverName(DB_DRIVER);
        dsc.setUsername(DB_USERNAME);
        dsc.setPassword(DB_PASSWORD);
        mpg.setDataSource(dsc);

        // 包配置
        PackageConfig pc = new PackageConfig();
        pc.setParent(PACKAGE_PARENT);
        pc.setEntity(PACKAGE_ENTITY);
        pc.setMapper(PACKAGE_MAPPER);
        pc.setService(PACKAGE_SERVICE);
        pc.setServiceImpl(PACKAGE_SERVICE_IMPL);
        pc.setController(PACKAGE_CONTROLLER);
        pc.setXml("mappers"); // XML文件包名
        mpg.setPackageInfo(pc);

        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                // 自定义属性注入
            }
        };

        // 自定义输出配置
        List<FileOutConfig> focList = new ArrayList<>();
        
        // 自定义Mapper XML生成路径
        focList.add(new FileOutConfig("/templates/mapper.xml.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return PROJECT_PATH + RESOURCES_PATH + "/mappers/" 
                    + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
            }
        });
        
        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);

        // 配置模板
        TemplateConfig templateConfig = new TemplateConfig();
        templateConfig.setXml(null); // 禁用默认XML生成，使用自定义路径
        mpg.setTemplate(templateConfig);

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setNaming(NamingStrategy.underline_to_camel); // 数据库表映射到实体的命名策略
        strategy.setColumnNaming(NamingStrategy.underline_to_camel); // 数据库表字段映射到实体的命名策略
        strategy.setEntityLombokModel(true); // 使用Lombok
        strategy.setRestControllerStyle(true); // 生成@RestController控制器
        strategy.setInclude(tableNames); // 需要生成的表名
        strategy.setControllerMappingHyphenStyle(true); // 驼峰转连字符
        strategy.setTablePrefix("fc_", "fd_"); // 表前缀，生成时会去掉
        
        // 字段填充配置
        List<TableFill> tableFillList = new ArrayList<>();
        tableFillList.add(new TableFill("create_time", FieldFill.INSERT));
        tableFillList.add(new TableFill("update_time", FieldFill.INSERT_UPDATE));
        tableFillList.add(new TableFill("create_user", FieldFill.INSERT));
        tableFillList.add(new TableFill("update_user", FieldFill.INSERT_UPDATE));
        strategy.setTableFillList(tableFillList);
        
        mpg.setStrategy(strategy);
        
        // 选择模板引擎
        mpg.setTemplateEngine(new VelocityTemplateEngine());
        
        // 执行生成
        mpg.execute();
    }
}
