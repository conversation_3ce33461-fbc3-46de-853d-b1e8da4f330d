package com.sinosoft.eflex.generator;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Scanner;

/**
 * MyBatis-Plus增强代码生成器
 * 
 * 功能特性：
 * 1. 自动生成Entity、Mapper、Service、Controller等完整代码
 * 2. 支持指定表名生成，避免生成所有表
 * 3. 生成的代码包含常用注解（@RestController、@Service、@Mapper等）
 * 4. 支持Lombok注解，简化代码
 * 5. 支持Swagger2注解，便于API文档生成
 * 6. 自动配置字段填充（创建时间、更新时间等）
 * 7. 生成路径与项目现有结构保持一致
 * 
 * 生成目录结构：
 * - Entity：     src/main/java/com/sinosoft/eflex/model/
 * - Mapper：     src/main/java/com/sinosoft/eflex/dao/
 * - Service：    src/main/java/com/sinosoft/eflex/service/
 * - ServiceImpl：src/main/java/com/sinosoft/eflex/service/impl/
 * - Controller： src/main/java/com/sinosoft/eflex/ctrl/
 * - Mapper XML： src/main/resources/mappers/
 * 
 * 使用方法：
 * 1. 确保数据库连接配置正确
 * 2. 运行main方法
 * 3. 根据提示选择生成模式
 * 4. 输入要生成的表名
 * 5. 确认生成配置
 * 6. 代码将自动生成到指定目录
 * 
 * 注意事项：
 * - 生成前请备份现有代码，避免覆盖重要文件
 * - 表名支持前缀过滤（fc_、fd_等）
 * - 生成的代码需要根据实际业务需求进行调整
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class CodeGenerator {

    // ==================== 数据库配置 ====================
    
    /**
     * 数据库连接配置
     * 注意：请根据实际环境修改数据库连接信息
     */
    private static final DatabaseConfig DEV_CONFIG = new DatabaseConfig(
        "**********************************************************************************************************************************************************************************",
        "eflex",
        "eflex",
        "开发环境"
    );
    
    private static final DatabaseConfig UAT_CONFIG = new DatabaseConfig(
        "****************************************************************************************************************************************************************************************************************",
        "u_uflexdb_user",
        "ZcZYOm1YKVDV",
        "UAT环境"
    );

    // ==================== 包配置 ====================
    
    private static final String PACKAGE_PARENT = "com.sinosoft.eflex";
    private static final String PACKAGE_ENTITY = "model";
    private static final String PACKAGE_MAPPER = "dao";
    private static final String PACKAGE_SERVICE = "service";
    private static final String PACKAGE_SERVICE_IMPL = "service.impl";
    private static final String PACKAGE_CONTROLLER = "ctrl";

    // ==================== 路径配置 ====================
    
    private static final String PROJECT_PATH = System.getProperty("user.dir");
    private static final String JAVA_PATH = "/src/main/java";
    private static final String RESOURCES_PATH = "/src/main/resources";

    // ==================== 其他配置 ====================
    
    private static final String AUTHOR = "CodeGenerator";
    private static final String[] TABLE_PREFIXES = {"fc_", "fd_", "t_"}; // 表前缀，生成时会去掉

    /**
     * 主方法
     */
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        try {
            printWelcome();
            
            // 选择数据库环境
            DatabaseConfig dbConfig = selectDatabase(scanner);
            
            // 选择生成模式
            GenerateMode mode = selectGenerateMode(scanner);
            
            // 获取表名
            String[] tableNames = getTableNames(scanner, mode);
            
            // 确认生成配置
            if (confirmGeneration(scanner, dbConfig, tableNames)) {
                generateCode(dbConfig, tableNames);
                printSuccess();
            } else {
                System.out.println("已取消生成。");
            }
            
        } catch (Exception e) {
            System.err.println("代码生成失败：" + e.getMessage());
            e.printStackTrace();
        } finally {
            scanner.close();
        }
    }

    /**
     * 打印欢迎信息
     */
    private static void printWelcome() {
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                    MyBatis-Plus代码生成器                     ║");
        System.out.println("║                        Version 1.0                          ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();
        System.out.println("功能说明：");
        System.out.println("• 自动生成Entity、Mapper、Service、Controller等代码");
        System.out.println("• 支持Lombok、Swagger2注解");
        System.out.println("• 自动配置字段填充和命名策略");
        System.out.println("• 生成路径与项目结构保持一致");
        System.out.println();
    }

    /**
     * 选择数据库环境
     */
    private static DatabaseConfig selectDatabase(Scanner scanner) {
        System.out.println("请选择数据库环境：");
        System.out.println("1. 开发环境 (10.9.10.164:3306/hqeflexdb)");
        System.out.println("2. UAT环境 (rm-j5e34e8dmh87222f0.mysql.rds.aliyuncs.com:3306/uflexdb)");
        System.out.print("请输入选择 (1-2): ");
        
        String choice = scanner.nextLine().trim();
        switch (choice) {
            case "1":
                return DEV_CONFIG;
            case "2":
                return UAT_CONFIG;
            default:
                System.out.println("无效选择，使用默认开发环境");
                return DEV_CONFIG;
        }
    }

    /**
     * 选择生成模式
     */
    private static GenerateMode selectGenerateMode(Scanner scanner) {
        System.out.println("\n请选择生成模式：");
        System.out.println("1. 指定表名生成（推荐）");
        System.out.println("2. 生成所有表（谨慎使用）");
        System.out.print("请输入选择 (1-2): ");
        
        String choice = scanner.nextLine().trim();
        return "2".equals(choice) ? GenerateMode.ALL_TABLES : GenerateMode.SPECIFIC_TABLES;
    }

    /**
     * 获取表名
     */
    private static String[] getTableNames(Scanner scanner, GenerateMode mode) {
        if (mode == GenerateMode.ALL_TABLES) {
            System.out.println("\n⚠️  警告：将生成所有表的代码，这可能会覆盖现有文件！");
            return new String[0]; // 空数组表示生成所有表
        }
        
        System.out.println("\n请输入要生成的表名：");
        System.out.println("• 多个表名用逗号分隔，如：fc_user,fc_role,fc_permission");
        System.out.println("• 支持表前缀：fc_, fd_, t_");
        System.out.print("表名: ");
        
        String tableNames = scanner.nextLine().trim();
        if (tableNames.isEmpty()) {
            throw new IllegalArgumentException("表名不能为空！");
        }

        String[] tables = tableNames.split(",");
        for (int i = 0; i < tables.length; i++) {
            tables[i] = tables[i].trim();
        }
        
        return tables;
    }

    /**
     * 确认生成配置
     */
    private static boolean confirmGeneration(Scanner scanner, DatabaseConfig dbConfig, String[] tableNames) {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("生成配置确认：");
        System.out.println("数据库环境：" + dbConfig.getDescription());
        System.out.println("数据库地址：" + dbConfig.getUrl().replaceAll("password=[^&]*", "password=***"));
        
        if (tableNames.length == 0) {
            System.out.println("生成范围：所有表");
        } else {
            System.out.println("生成表名：" + Arrays.toString(tableNames));
        }
        
        System.out.println("生成路径：" + PROJECT_PATH);
        System.out.println("生成时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("=".repeat(60));
        
        System.out.print("确认生成代码？(y/n): ");
        String confirm = scanner.nextLine().trim();
        return "y".equalsIgnoreCase(confirm) || "yes".equalsIgnoreCase(confirm);
    }

    /**
     * 执行代码生成
     */
    private static void generateCode(DatabaseConfig dbConfig, String[] tableNames) {
        System.out.println("\n开始生成代码...");
        
        // 创建代码生成器
        AutoGenerator mpg = new AutoGenerator();

        // 全局配置
        configureGlobal(mpg);
        
        // 数据源配置
        configureDataSource(mpg, dbConfig);
        
        // 包配置
        configurePackage(mpg);
        
        // 自定义配置
        configureCustom(mpg);
        
        // 模板配置
        configureTemplate(mpg);
        
        // 策略配置
        configureStrategy(mpg, tableNames);
        
        // 设置模板引擎
        mpg.setTemplateEngine(new VelocityTemplateEngine());
        
        // 执行生成
        mpg.execute();
    }

    /**
     * 全局配置
     */
    private static void configureGlobal(AutoGenerator mpg) {
        GlobalConfig gc = new GlobalConfig();
        gc.setOutputDir(PROJECT_PATH + JAVA_PATH);
        gc.setAuthor(AUTHOR);
        gc.setOpen(false); // 生成后不打开文件夹
        gc.setFileOverride(true); // 覆盖已有文件
        gc.setServiceName("%sService"); // Service接口命名
        gc.setServiceImplName("%sServiceImpl"); // Service实现类命名
        gc.setMapperName("%sMapper"); // Mapper接口命名
        gc.setXmlName("%sMapper"); // Mapper XML文件命名
        gc.setControllerName("%sController"); // Controller命名
        gc.setIdType(IdType.AUTO); // 主键策略
        gc.setDateType(DateType.ONLY_DATE); // 日期类型
        gc.setSwagger2(true); // 开启Swagger2注解
        mpg.setGlobalConfig(gc);
    }

    /**
     * 数据源配置
     */
    private static void configureDataSource(AutoGenerator mpg, DatabaseConfig dbConfig) {
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setUrl(dbConfig.getUrl());
        dsc.setDriverName("com.mysql.cj.jdbc.Driver");
        dsc.setUsername(dbConfig.getUsername());
        dsc.setPassword(dbConfig.getPassword());
        mpg.setDataSource(dsc);
    }

    /**
     * 包配置
     */
    private static void configurePackage(AutoGenerator mpg) {
        PackageConfig pc = new PackageConfig();
        pc.setParent(PACKAGE_PARENT);
        pc.setEntity(PACKAGE_ENTITY);
        pc.setMapper(PACKAGE_MAPPER);
        pc.setService(PACKAGE_SERVICE);
        pc.setServiceImpl(PACKAGE_SERVICE_IMPL);
        pc.setController(PACKAGE_CONTROLLER);
        pc.setXml("mappers");
        mpg.setPackageInfo(pc);
    }

    /**
     * 自定义配置
     */
    private static void configureCustom(AutoGenerator mpg) {
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                // 自定义属性注入
            }
        };

        // 自定义输出配置
        List<FileOutConfig> focList = new ArrayList<>();
        
        // 自定义Mapper XML生成路径
        focList.add(new FileOutConfig("/templates/mapper.xml.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return PROJECT_PATH + RESOURCES_PATH + "/mappers/" 
                    + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
            }
        });
        
        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);
    }

    /**
     * 模板配置
     */
    private static void configureTemplate(AutoGenerator mpg) {
        TemplateConfig templateConfig = new TemplateConfig();
        templateConfig.setXml(null); // 禁用默认XML生成，使用自定义路径
        mpg.setTemplate(templateConfig);
    }

    /**
     * 策略配置
     */
    private static void configureStrategy(AutoGenerator mpg, String[] tableNames) {
        StrategyConfig strategy = new StrategyConfig();
        strategy.setNaming(NamingStrategy.underline_to_camel); // 表名映射策略
        strategy.setColumnNaming(NamingStrategy.underline_to_camel); // 字段映射策略
        strategy.setEntityLombokModel(true); // 使用Lombok
        strategy.setRestControllerStyle(true); // 生成@RestController
        strategy.setControllerMappingHyphenStyle(true); // 驼峰转连字符
        strategy.setTablePrefix(TABLE_PREFIXES); // 表前缀
        
        // 设置要生成的表
        if (tableNames.length > 0) {
            strategy.setInclude(tableNames);
        }
        
        // 字段填充配置
        List<TableFill> tableFillList = new ArrayList<>();
        tableFillList.add(new TableFill("create_time", FieldFill.INSERT));
        tableFillList.add(new TableFill("update_time", FieldFill.INSERT_UPDATE));
        tableFillList.add(new TableFill("create_user", FieldFill.INSERT));
        tableFillList.add(new TableFill("update_user", FieldFill.INSERT_UPDATE));
        strategy.setTableFillList(tableFillList);
        
        mpg.setStrategy(strategy);
    }

    /**
     * 打印成功信息
     */
    private static void printSuccess() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("🎉 代码生成完成！");
        System.out.println();
        System.out.println("生成的文件位置：");
        System.out.println("• Entity：     " + PROJECT_PATH + JAVA_PATH + "/" + PACKAGE_PARENT.replace(".", "/") + "/" + PACKAGE_ENTITY);
        System.out.println("• Mapper：     " + PROJECT_PATH + JAVA_PATH + "/" + PACKAGE_PARENT.replace(".", "/") + "/" + PACKAGE_MAPPER);
        System.out.println("• Service：    " + PROJECT_PATH + JAVA_PATH + "/" + PACKAGE_PARENT.replace(".", "/") + "/" + PACKAGE_SERVICE);
        System.out.println("• ServiceImpl：" + PROJECT_PATH + JAVA_PATH + "/" + PACKAGE_PARENT.replace(".", "/") + "/" + PACKAGE_SERVICE_IMPL);
        System.out.println("• Controller： " + PROJECT_PATH + JAVA_PATH + "/" + PACKAGE_PARENT.replace(".", "/") + "/" + PACKAGE_CONTROLLER);
        System.out.println("• Mapper XML： " + PROJECT_PATH + RESOURCES_PATH + "/mappers");
        System.out.println();
        System.out.println("注意事项：");
        System.out.println("• 请检查生成的代码是否符合业务需求");
        System.out.println("• 根据实际情况调整Controller的请求映射");
        System.out.println("• 添加必要的业务逻辑和数据验证");
        System.out.println("=".repeat(60));
    }

    // ==================== 内部类 ====================

    /**
     * 数据库配置类
     */
    private static class DatabaseConfig {
        private final String url;
        private final String username;
        private final String password;
        private final String description;

        public DatabaseConfig(String url, String username, String password, String description) {
            this.url = url;
            this.username = username;
            this.password = password;
            this.description = description;
        }

        public String getUrl() { return url; }
        public String getUsername() { return username; }
        public String getPassword() { return password; }
        public String getDescription() { return description; }
    }

    /**
     * 生成模式枚举
     */
    private enum GenerateMode {
        SPECIFIC_TABLES, // 指定表生成
        ALL_TABLES       // 所有表生成
    }
}
