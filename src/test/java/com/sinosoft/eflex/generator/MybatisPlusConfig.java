package com.sinosoft.eflex.generator;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

/**
 * MyBatis-Plus配置类
 * 
 * 此配置类展示了如何在项目中配置MyBatis-Plus的常用功能：
 * 1. 分页插件配置
 * 2. 字段自动填充配置
 * 3. 其他常用配置
 * 
 * 使用说明：
 * 1. 将此文件复制到 src/main/java/com/sinosoft/eflex/config/ 目录下
 * 2. 根据项目需求调整配置
 * 3. 确保Spring Boot能够扫描到此配置类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
public class MybatisPlusConfig {

    /**
     * MyBatis-Plus拦截器配置
     * 主要用于配置分页插件等功能
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 分页插件
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor();
        paginationInterceptor.setDbType(DbType.MYSQL); // 数据库类型
        paginationInterceptor.setMaxLimit(1000L); // 单页最大数量限制
        paginationInterceptor.setOverflow(false); // 溢出总页数后是否进行处理
        
        interceptor.addInnerInterceptor(paginationInterceptor);
        
        return interceptor;
    }

    /**
     * 字段自动填充配置
     * 用于自动填充创建时间、更新时间、创建人、更新人等字段
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            
            /**
             * 插入时自动填充
             */
            @Override
            public void insertFill(MetaObject metaObject) {
                // 自动填充创建时间
                this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
                this.strictInsertFill(metaObject, "create_time", LocalDateTime.class, LocalDateTime.now());
                
                // 自动填充更新时间
                this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
                this.strictInsertFill(metaObject, "update_time", LocalDateTime.class, LocalDateTime.now());
                
                // 自动填充创建人（需要从当前登录用户获取）
                String currentUser = getCurrentUser();
                if (currentUser != null) {
                    this.strictInsertFill(metaObject, "createUser", String.class, currentUser);
                    this.strictInsertFill(metaObject, "create_user", String.class, currentUser);
                    this.strictInsertFill(metaObject, "updateUser", String.class, currentUser);
                    this.strictInsertFill(metaObject, "update_user", String.class, currentUser);
                }
            }

            /**
             * 更新时自动填充
             */
            @Override
            public void updateFill(MetaObject metaObject) {
                // 自动填充更新时间
                this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
                this.strictUpdateFill(metaObject, "update_time", LocalDateTime.class, LocalDateTime.now());
                
                // 自动填充更新人（需要从当前登录用户获取）
                String currentUser = getCurrentUser();
                if (currentUser != null) {
                    this.strictUpdateFill(metaObject, "updateUser", String.class, currentUser);
                    this.strictUpdateFill(metaObject, "update_user", String.class, currentUser);
                }
            }
            
            /**
             * 获取当前登录用户
             * 这里需要根据项目的用户认证方式来实现
             */
            private String getCurrentUser() {
                // TODO: 根据项目实际情况实现获取当前用户的逻辑
                // 例如：从Spring Security、Session、JWT Token等获取
                
                // 示例实现（请根据实际情况修改）：
                /*
                try {
                    // 从Spring Security获取
                    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                    if (authentication != null && authentication.isAuthenticated()) {
                        return authentication.getName();
                    }
                    
                    // 或者从Session获取
                    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
                    HttpSession session = request.getSession(false);
                    if (session != null) {
                        return (String) session.getAttribute("currentUser");
                    }
                } catch (Exception e) {
                    // 忽略异常，返回默认值
                }
                */
                
                return "system"; // 默认用户
            }
        };
    }
}

/**
 * 使用示例和说明
 * 
 * 1. 分页查询示例：
 * ```java
 * @Service
 * public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
 *     
 *     public IPage<User> getUserPage(int current, int size) {
 *         Page<User> page = new Page<>(current, size);
 *         return this.page(page);
 *     }
 * }
 * ```
 * 
 * 2. 字段自动填充示例：
 * ```java
 * @Data
 * @TableName("fc_user")
 * public class User {
 *     @TableId(type = IdType.AUTO)
 *     private Long id;
 *     
 *     private String username;
 *     
 *     @TableField(fill = FieldFill.INSERT)
 *     private LocalDateTime createTime;
 *     
 *     @TableField(fill = FieldFill.INSERT_UPDATE)
 *     private LocalDateTime updateTime;
 *     
 *     @TableField(fill = FieldFill.INSERT)
 *     private String createUser;
 *     
 *     @TableField(fill = FieldFill.INSERT_UPDATE)
 *     private String updateUser;
 * }
 * ```
 * 
 * 3. Controller分页查询示例：
 * ```java
 * @RestController
 * @RequestMapping("/user")
 * public class UserController {
 *     
 *     @Autowired
 *     private UserService userService;
 *     
 *     @GetMapping("/page")
 *     public Result<IPage<User>> getUserPage(
 *             @RequestParam(defaultValue = "1") int current,
 *             @RequestParam(defaultValue = "10") int size) {
 *         IPage<User> page = userService.getUserPage(current, size);
 *         return Result.success(page);
 *     }
 * }
 * ```
 * 
 * 4. 条件查询示例：
 * ```java
 * @Service
 * public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
 *     
 *     public List<User> getUsersByCondition(String username, String status) {
 *         QueryWrapper<User> queryWrapper = new QueryWrapper<>();
 *         queryWrapper.like(StringUtils.isNotBlank(username), "username", username)
 *                    .eq(StringUtils.isNotBlank(status), "status", status);
 *         return this.list(queryWrapper);
 *     }
 * }
 * ```
 * 
 * 5. 配置文件示例（application.yml）：
 * ```yaml
 * mybatis-plus:
 *   configuration:
 *     map-underscore-to-camel-case: true
 *     cache-enabled: false
 *     call-setters-on-nulls: true
 *   mapper-locations: classpath*:/mappers/**/*.xml
 *   type-aliases-package: com.sinosoft.eflex.model
 *   global-config:
 *     db-config:
 *       id-type: auto
 *       table-underline: true
 *       logic-delete-field: deleted
 *       logic-delete-value: 1
 *       logic-not-delete-value: 0
 * ```
 */
