package com.sinosoft.eflex.generator;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;

import java.util.ArrayList;
import java.util.List;

/**
 * MyBatis-Plus快速代码生成器
 * 
 * 简化版代码生成器，用于快速生成指定表的代码
 * 适合开发过程中快速生成单个或少量表的代码
 * 
 * 使用方法：
 * 1. 修改 TABLE_NAMES 常量，指定要生成的表名
 * 2. 根据需要修改数据库连接配置
 * 3. 运行main方法即可生成代码
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class QuickGenerator {

    // ==================== 快速配置区域 ====================
    
    /**
     * 要生成的表名（修改这里即可）
     * 支持多个表名，用逗号分隔
     */
    private static final String TABLE_NAMES = "fc_user,fc_role"; // 👈 在这里修改表名
    
    /**
     * 数据库连接配置（根据需要修改）
     */
    private static final String DB_URL = "**********************************************************************************************************************************************************************************";
    private static final String DB_USERNAME = "eflex";
    private static final String DB_PASSWORD = "eflex";
    
    /**
     * 作者信息（可选修改）
     */
    private static final String AUTHOR = "QuickGenerator";

    // ==================== 固定配置（一般不需要修改） ====================
    
    private static final String PACKAGE_PARENT = "com.sinosoft.eflex";
    private static final String PROJECT_PATH = System.getProperty("user.dir");
    private static final String JAVA_PATH = "/src/main/java";
    private static final String RESOURCES_PATH = "/src/main/resources";

    /**
     * 主方法 - 直接运行即可生成代码
     */
    public static void main(String[] args) {
        System.out.println("=== MyBatis-Plus快速代码生成器 ===");
        System.out.println("生成表名: " + TABLE_NAMES);
        System.out.println("数据库: " + DB_URL.substring(DB_URL.indexOf("//") + 2, DB_URL.indexOf("?")));
        System.out.println("项目路径: " + PROJECT_PATH);
        System.out.println();
        
        try {
            generateCode();
            System.out.println("✅ 代码生成完成！");
            System.out.println();
            printGeneratedFiles();
        } catch (Exception e) {
            System.err.println("❌ 代码生成失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 执行代码生成
     */
    private static void generateCode() {
        // 解析表名
        String[] tableNames = TABLE_NAMES.split(",");
        for (int i = 0; i < tableNames.length; i++) {
            tableNames[i] = tableNames[i].trim();
        }

        // 创建代码生成器
        AutoGenerator mpg = new AutoGenerator();

        // 全局配置
        GlobalConfig gc = new GlobalConfig();
        gc.setOutputDir(PROJECT_PATH + JAVA_PATH);
        gc.setAuthor(AUTHOR);
        gc.setOpen(false);
        gc.setFileOverride(true);
        gc.setServiceName("%sService");
        gc.setServiceImplName("%sServiceImpl");
        gc.setMapperName("%sMapper");
        gc.setXmlName("%sMapper");
        gc.setControllerName("%sController");
        gc.setIdType(IdType.AUTO);
        gc.setDateType(DateType.ONLY_DATE);
        gc.setSwagger2(true);
        mpg.setGlobalConfig(gc);

        // 数据源配置
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setUrl(DB_URL);
        dsc.setDriverName("com.mysql.cj.jdbc.Driver");
        dsc.setUsername(DB_USERNAME);
        dsc.setPassword(DB_PASSWORD);
        mpg.setDataSource(dsc);

        // 包配置
        PackageConfig pc = new PackageConfig();
        pc.setParent(PACKAGE_PARENT);
        pc.setEntity("model");
        pc.setMapper("dao");
        pc.setService("service");
        pc.setServiceImpl("service.impl");
        pc.setController("ctrl");
        pc.setXml("mappers");
        mpg.setPackageInfo(pc);

        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                // 自定义属性注入
            }
        };

        // 自定义输出配置
        List<FileOutConfig> focList = new ArrayList<>();
        focList.add(new FileOutConfig("/templates/mapper.xml.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return PROJECT_PATH + RESOURCES_PATH + "/mappers/" 
                    + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
            }
        });
        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);

        // 配置模板
        TemplateConfig templateConfig = new TemplateConfig();
        templateConfig.setXml(null);
        mpg.setTemplate(templateConfig);

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setNaming(NamingStrategy.underline_to_camel);
        strategy.setColumnNaming(NamingStrategy.underline_to_camel);
        strategy.setEntityLombokModel(true);
        strategy.setRestControllerStyle(true);
        strategy.setInclude(tableNames);
        strategy.setControllerMappingHyphenStyle(true);
        strategy.setTablePrefix("fc_", "fd_", "t_");
        
        // 字段填充配置
        List<TableFill> tableFillList = new ArrayList<>();
        tableFillList.add(new TableFill("create_time", FieldFill.INSERT));
        tableFillList.add(new TableFill("update_time", FieldFill.INSERT_UPDATE));
        tableFillList.add(new TableFill("create_user", FieldFill.INSERT));
        tableFillList.add(new TableFill("update_user", FieldFill.INSERT_UPDATE));
        strategy.setTableFillList(tableFillList);
        
        mpg.setStrategy(strategy);
        mpg.setTemplateEngine(new VelocityTemplateEngine());
        
        // 执行生成
        mpg.execute();
    }

    /**
     * 打印生成的文件信息
     */
    private static void printGeneratedFiles() {
        String[] tableNames = TABLE_NAMES.split(",");
        
        System.out.println("📁 生成的文件：");
        System.out.println();
        
        for (String tableName : tableNames) {
            tableName = tableName.trim();
            String entityName = convertToEntityName(tableName);
            
            System.out.println("📋 表: " + tableName + " → 实体: " + entityName);
            System.out.println("  ├── " + entityName + ".java (Entity)");
            System.out.println("  ├── " + entityName + "Mapper.java (Mapper接口)");
            System.out.println("  ├── " + entityName + "Mapper.xml (Mapper XML)");
            System.out.println("  ├── " + entityName + "Service.java (Service接口)");
            System.out.println("  ├── " + entityName + "ServiceImpl.java (Service实现)");
            System.out.println("  └── " + entityName + "Controller.java (Controller)");
            System.out.println();
        }
        
        System.out.println("📂 文件位置：");
        System.out.println("  Entity:      " + PROJECT_PATH + JAVA_PATH + "/com/sinosoft/eflex/model/");
        System.out.println("  Mapper:      " + PROJECT_PATH + JAVA_PATH + "/com/sinosoft/eflex/dao/");
        System.out.println("  Service:     " + PROJECT_PATH + JAVA_PATH + "/com/sinosoft/eflex/service/");
        System.out.println("  ServiceImpl: " + PROJECT_PATH + JAVA_PATH + "/com/sinosoft/eflex/service/impl/");
        System.out.println("  Controller:  " + PROJECT_PATH + JAVA_PATH + "/com/sinosoft/eflex/ctrl/");
        System.out.println("  Mapper XML:  " + PROJECT_PATH + RESOURCES_PATH + "/mappers/");
        System.out.println();
        System.out.println("💡 提示：");
        System.out.println("  • 请检查生成的代码是否符合业务需求");
        System.out.println("  • 根据实际情况调整Controller的请求映射");
        System.out.println("  • 添加必要的业务逻辑和数据验证");
    }

    /**
     * 将表名转换为实体名
     */
    private static String convertToEntityName(String tableName) {
        // 去掉前缀
        String[] prefixes = {"fc_", "fd_", "t_"};
        for (String prefix : prefixes) {
            if (tableName.toLowerCase().startsWith(prefix)) {
                tableName = tableName.substring(prefix.length());
                break;
            }
        }
        
        // 下划线转驼峰，首字母大写
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;
        
        for (char c : tableName.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else {
                if (capitalizeNext) {
                    result.append(Character.toUpperCase(c));
                    capitalizeNext = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            }
        }
        
        return result.toString();
    }
}
