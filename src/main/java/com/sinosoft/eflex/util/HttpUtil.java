package com.sinosoft.eflex.util;


import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.InetAddress;
import java.net.URL;
import java.net.URLConnection;

@Deprecated
public class HttpUtil {

    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param) {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("contentType", "utf-8");
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), "utf-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            log.info("发送 POST 请求出现异常！" + e);
        }
        //使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                log.info("", ex);
            }
        }
        return result;
    }

    public static String postHttpRequestJson(String url, String requestJson) {
        log.info("请求地址:" + url);
        //接收结果
        String tResult = "";
        try {
            //创建HttpClient对象
            HttpClient client = new HttpClient();
            //加载地址以POST方式提交
            PostMethod method = new PostMethod(url);
            //设置编码
            method.setRequestHeader("Accept", "application/json");
            method.setRequestHeader("Content-Type", "application/json");
            method.setRequestHeader("Encoding", "UTF-8");
            method.setRequestEntity(new StringRequestEntity(requestJson, "application/json", "UTF-8"));
            //执行
            int result = client.executeMethod(method);
            if (result != HttpStatus.SC_OK) {
                log.info("请求失败！" + result);
            } else {
                log.info("请求成功！" + result);
            }
            tResult = new String(method.getResponseBody(), "UTF-8");
            log.info("返回报文:" + tResult);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return tResult;
    }

    public static String postHttpRequestJson22(String url, String requestJson) {
        log.info("请求地址:" + url);
        //接收结果
        String tResult = "";
        try {
            //创建HttpClient对象
            HttpClient client = new HttpClient();
            //加载地址以POST方式提交
            PostMethod method = new PostMethod(url);
            //设置编码
            method.setRequestHeader("Accept", "application/json");
            method.setRequestHeader("Content-Type", "application/json");
            method.setRequestHeader("Encoding", "UTF-8");
            method.setRequestEntity(new StringRequestEntity(requestJson, "application/json", "UTF-8"));
            method.getParams().setContentCharset("UTF-8");
            //执行
            int result = client.executeMethod(method);
            if (result != HttpStatus.SC_OK) {
                log.info("请求失败！" + result);
            } else {
                log.info("请求成功！" + result);
            }
            tResult = new String(method.getResponseBody(), "UTF-8");
            log.info("返回报文:" + tResult);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return tResult;
    }

    public static String postHttpRequestJson(String url, String requestJson, String app_id, String app_secret) {
        //接收结果
        String tResult = "";
        try {
            //创建HttpClient对象
            HttpClient client = new HttpClient();
            //加载地址以POST方式提交
            PostMethod method = new PostMethod(url);
            //设置编码
            method.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
            method.setRequestHeader("app_id", app_id);
            method.setRequestHeader("app_secret", app_secret);
            method.setRequestEntity(new StringRequestEntity(requestJson, "application/json", "UTF-8"));
            //执行
            int result = client.executeMethod(method);
            if (result != HttpStatus.SC_OK) {
                log.info("请求失败！" + result);
            } else {
                log.info("请求成功！" + result);
            }
            tResult = method.getResponseBodyAsString();
            log.info("返回报文:" + tResult);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return tResult;
    }

    //HTTP post请求 xml入参的形式
    public static String postHttpRequestXMl(String url, String xmlString) {
        //接收结果
        String tResult = "";
        try {
            //创建HttpClient对象
            HttpClient client = new HttpClient();
            //加载地址以POST方式提交
            PostMethod methodPost = new PostMethod(url);
            //设置请求头部类型
            methodPost.setRequestHeader("Content-Type", "text/xml");
            methodPost.setRequestHeader("charset", "utf-8");
            methodPost.setRequestEntity(new StringRequestEntity(xmlString, "text/xml", "utf-8"));
            //执行
            int result = client.executeMethod(methodPost);
            if (result != HttpStatus.SC_OK) {
                log.info("请求失败！" + result);
            } else {
                log.info("请求成功！" + result);
                tResult = methodPost.getResponseBodyAsString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return tResult;
    }

    public static String post(String apiUrl, String params) throws IOException {
        HttpPost httpPost = new HttpPost(apiUrl);
        CloseableHttpClient client = HttpClients.createDefault();
        String respContent = null;
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.addTextBody("charset", "UTF-8");
        builder.addTextBody("result", params);
        HttpEntity multipart = builder.build();
        HttpResponse resp = null;
        try {
            httpPost.setEntity(multipart);
            resp = client.execute(httpPost);
            HttpEntity he = resp.getEntity();
            //注意，返回的结果的状态码是302，非200
            if (resp.getStatusLine().getStatusCode() == 302) {
                respContent = EntityUtils.toString(he, "UTF-8");
            }
            respContent = EntityUtils.toString(he, "UTF-8");
            log.info("请求成功！" + respContent);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return respContent;
    }

    //获取当前服务器ip
    public static String getIp() {
        try {
            InetAddress ia = InetAddress.getLocalHost();
            String localip = ia.getHostAddress();
            return localip;
        } catch (Exception e) {
            e.printStackTrace();
            return "500";
        }
    }
}
