package com.sinosoft.eflex.util;

import java.io.PrintWriter;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xml.sax.InputSource;

import com.sinosoft.eflex.model.Message;
import com.thoughtworks.xstream.XStream;

public class XmlUtil {

	private static Logger Log = (Logger) LoggerFactory.getLogger(XmlUtil.class);
	public static XStream xStream = new XStream();

	/**
	 * javabean转换XML并生成文件
	 * 
	 * <AUTHOR>
	 * @param xs,o,filePath
	 * @return Boolean
	 */
	public static Boolean createXml(XStream xs, Object o, String filePath) {
		try {
			PrintWriter writer = new PrintWriter(filePath, "GBK");
			writer.write("<?xml version=\"1.0\" encoding=\"GBK\" ?>\n");
			xs.toXML(o, writer);
			Log.info("XML文件：" + filePath + " 生成成功！");
		} catch (Exception e) {
			Log.info("XML文件：" + filePath + " 生成失败！", e);
			e.printStackTrace();
		}
		return true;
	}

	/**
	 * javabean转换XML字符串
	 * 
	 * <AUTHOR>
	 * @param o
	 * @return Boolean
	 */
	public static String objToXmlStr(Object o) {
		StringBuffer sb = new StringBuffer();
		sb.append("<?xml version=\"1.0\" encoding=\"GBK\" ?>");
		sb.append("\n");
		String className = o.getClass().getSimpleName();
		if (isBeanClass(className)) {
			aliasAll(className);
			String tmpXml = xStream.toXML(o);
			tmpXml = tmpXml.replace("com.sinosoft.eflex.model.BatchInsureInterface.", "");
			tmpXml = tmpXml.replace("com.sinosoft.eflex.model.", "");
			sb.append(tmpXml);
		}
		return sb.toString();
	}

	private static boolean aliasAll(String className) {
		Object o = null;
		if (isBeanClass(className)) {
			o = getClass(className);
			xStream.alias(className, o.getClass());
		}
		return true;
	}

	/**
	 * 
	 * 判断实体类是否存在
	 * 
	 * 该方法只支持对指定目录下的实体类进行转换
	 * 
	 * @param className
	 * @return
	 */
	private static boolean isBeanClass(String className) {
		// 该方法要求所有待转换的实体类必须在指定的目录下。
		try {
			Class.forName("com.sinosoft.eflex.model." + className).newInstance();
		} catch (InstantiationException e) {
			Log.info("",e);
			return false;
		} catch (IllegalAccessException e) {
		    Log.info("",e);
			return false;
		} catch (ClassNotFoundException e) {
			try {
				Class.forName("com.sinosoft.eflex.model.BatchInsureInterface." + className).newInstance();
			} catch (InstantiationException ee) {
			    //Log.info("",ee);
				return false;
			} catch (IllegalAccessException ee) {
			    //Log.info("",ee);
				return false;
			} catch (ClassNotFoundException ee) {
//			    Log.info("",ee);
				return false;
			} catch (NoClassDefFoundError ee) {
			    //Log.info("",ee);
				return false;
			}
		} catch (NoClassDefFoundError e) {
		    //Log.info("",e);
			return false;
		}
		return true;
	}

	/**
	 * 获取实体类
	 * 
	 * @param className
	 * @return
	 */
	private static Object getClass(String className) {
		Object o = null;
		try {
			o = Class.forName("com.sinosoft.eflex.model." + className).newInstance();
		} catch (InstantiationException e) {
		    Log.info("",e);
			return null;
		} catch (IllegalAccessException e) {
		    Log.info("",e);
			return null;
		} catch (ClassNotFoundException e) {
//		    Log.info("",e);
			try {
				o = Class.forName("com.sinosoft.eflex.model.BatchInsureInterface." + className).newInstance();
			} catch (InstantiationException ee) {
			    Log.info("",ee);
				return null;
			} catch (IllegalAccessException ee) {
			    Log.info("",ee);
				return null;
			} catch (ClassNotFoundException ee) {
//			    Log.info("",ee);
				return null;
			}
		}
		return o;
	}

	/**
	 * 解析返回报文
	 * 
	 * @param responseXml
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public static Message parseResponseXml(String responseXml) {
		Message message = new Message();
		if (responseXml == null || responseXml.length() == 0) {
			message.setmSuccess(false);
			message.setmCErrors("返回报文为空！");
			return message;
		}

		if(responseXml.indexOf("<FpList>") != -1){
		// 针对 InsureController/invoiceQuery 发票短链接查询接口 单独加工
			StringBuffer stringBuilder = new StringBuffer(responseXml);
			int start = stringBuilder.indexOf("<FpList>");
			stringBuilder.insert(start,"<InvoiceList>");
			int end = stringBuilder.lastIndexOf("</FpList>");
			stringBuilder.insert(end+9,"</InvoiceList>");
			responseXml = stringBuilder.toString();
			//message.setmSuccess(true);
		}



		responseXml = responseXml.replace("transResult", "TransResult");
		responseXml = responseXml.replace("resultCode", "ResultCode");
		responseXml = responseXml.replace("resultInfo", "ResultInfo");
		responseXml = responseXml.replace("HEAD", "Head");
		responseXml = responseXml.replace("BODY", "Body");
		responseXml = responseXml.replace("body", "Body");
		responseXml = responseXml.replace("ClaimInfos", "ClaimInfoList");
		SAXReader reader = new SAXReader(false);
		reader.setEncoding("UTF-8");
		Document doc;
		Element ele = null;
		String eleName = "";
		Map<String, Object> aResultData = new HashMap<>();
		try {
			doc = reader.read(new InputSource(new StringReader(responseXml)));
			Element root = doc.getRootElement();
//			String rootName = root.getName();
//			if (isBeanClass(rootName)) {// 是Bean报文实体类
//				aliasAll(root);
//				Object obj = xStream.fromXML(root.asXML());
//				aResultData.put(rootName, obj);
//			} else {
				List elements = root.elements();
				for (int i = 0; i < elements.size(); i++) {
					ele = (Element) elements.get(i);
					eleName = ele.getName();
					if (isBeanClass(eleName)) {// 是Bean报文实体类
						aliasAll(ele);
						Object obj = xStream.fromXML(ele.asXML());
						aResultData.put(eleName, obj);
					} else if (eleName.endsWith("List")) {// 实体类集合
						List eles = ele.elements();
						List<Object> beanList = new ArrayList<Object>();
						String beanName = null;
						for (int j = 0; j < eles.size(); j++) {
							Element e = (Element) eles.get(j);
							beanName = e.getName();
							if (isBeanClass(beanName)) {
								Object bean = getClass(beanName);
								aliasAll(e);
								bean = xStream.fromXML(e.asXML());
								beanList.add(bean);
							}
						}
						aResultData.put(eleName, beanList);
					} else {
						aResultData.put(eleName, ele.getData());
					}
				}
//			}

			message.setmResult(aResultData);
			message.setmSuccess(true);
		} catch (Exception e) {
		    Log.info("返回报文不符合规范，解析失败！",e);
			message.setmSuccess(false);
			message.setmCErrors("返回报文不符合规范，解析失败！");
			return message;
		}
		return message;
	}

	/**
	 * 
	 * 遍历XML报文，迭代设置alias。
	 * 
	 * @param rootEle
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	private static boolean aliasAll(Element rootEle) {
		Object o = null;
		String eleName = rootEle.getName();
		if (isBeanClass(eleName)) {
			o = getClass(eleName);
			xStream.alias(eleName, o.getClass());
		}
		List elements = rootEle.elements();
		for (int i = 0; i < elements.size(); i++) {
			Element ele = (Element) elements.get(i);
			aliasAll(ele);
		}
		return true;
	}

}
