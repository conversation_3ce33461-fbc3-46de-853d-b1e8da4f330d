package com.sinosoft.eflex.util;

import com.sinosoft.eflex.EflexApplication;
import com.sinosoft.eflex.dao.FDPlaceMapper;
import com.sinosoft.eflex.service.AddressCheckService;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @version 创建时间: 2019年4月22日 下午5:25:01
 * @date 2019年4月22日
 */
@Slf4j
public class CheckUtils {

    /**
     * 这里用Map为了提高扩展性 一期优化公共的人员(员工,家属)校验方法 map中 添加对象，sign（1：员工 2：家属 3：HR）
     */

    public static String checkSinglePeople(Map<String, String> map) {
        String errorMsg = "";
        //校验参数
        log.info("校验入参:{}", map.toString());
        String errMsg = checkParm(map);
        if (StringUtils.isNotBlank(errMsg)) {
            return errMsg;
        }
        try {
            //获取数据
            String sign = map.get("sign");//1：员工  2：家属 3：HR
            String sex = map.get("sex");//性别
            String occupationCode = map.get("occupationCode");//职业代码
            String birthDay = map.get("birthDay");//出生日期
            String idType = map.get("idType");//证件类型
            String idno = map.get("idNo");//证件号
            String ensurevaliDate = map.get("ensurevaliDate");
            if (StringUtils.isNotBlank(occupationCode) && occupationCode.contains(".")) {
                occupationCode = occupationCode.substring(0, occupationCode.indexOf("."));
            }
            Integer age;
            if (StringUtils.isNotBlank(ensurevaliDate)) {
                age = DateTimeUtil.getCurrentAge(birthDay, ensurevaliDate);//年龄
            } else {
                age = CheckUtils.checkStaffAge(birthDay);//年龄
            }
            String nativeplace = map.get("nativeplace");//国籍
            String idTypeEndDate = map.get("idTypeEndDate");//证件有效期

            // 校验邮箱
            String email = map.get("email");
            if (StringUtils.isNotBlank(email) && !CheckUtils.checkEmail(email)) {
                throw new SystemException("联系人邮箱录入有误，请检查！");
            }

            /**校验员工*/
            if (sign.equals("1") || sign.equals("3") || sign.equals("4")) {
                // 校验员工周岁，放开等于16周岁的校验。
                if (age < 16) {
                    errorMsg = "员工年龄需要在16周岁以上！";
                    if (StringUtils.isNotBlank(errorMsg)) return errorMsg;
                }
                if (StringUtils.isNotBlank(occupationCode) && occupationCode.equals("2099908")) {
                    errorMsg = "职业信息不允许“2099908-学龄前儿童&婴幼儿";
                    if (StringUtils.isNotBlank(errorMsg)) return errorMsg;
                }
                if (sign.equals("3")) {
                    // 校验手机号
                    if (!CheckUtils.checkMobilePhone(map.get("mobilePhone")) && !CheckUtils.checkMobilePhone(map.get("mobile"))) {
                        return "手机号格式错误！";
                    }
                    // 校验证件有效期
                    if (StringUtils.isBlank(map.get("idTypeEndDate"))) {
                        return "证件有效期不能为空！";
                    }
                    // 校验国籍
                    if (StringUtils.isBlank(nativeplace)) {
                        return "国籍不能为空！";
                    }
                }
                /**校验家属*/
            } else {
                //校验家属年龄和职业信息的关系
                if (age > 6 && occupationCode.equals("2099908")) {
                    errorMsg = "年龄大于6周岁，职业信息不允许“2099908-学龄前儿童&婴幼儿";
                    if (StringUtils.isNotBlank(errorMsg)) return errorMsg;
                }
                if ((age >= 7 && age < 16 && !occupationCode.equals("")
                        && !Arrays.asList(new String[]{"2099907", "3020109", "8000101", "8000102"})
                        .contains(occupationCode))) {
                    errorMsg = "年龄年龄为7-15周岁的未成年人，职业信息仅可选择为“2099907-一般学生”、“3020109-警校学生”、“8000101-特殊运动班学生（拳击、摔跤、跆拳道等）”、“8000102-武术学校学生";
                    if (StringUtils.isNotBlank(errorMsg)) return errorMsg;
                }
                if (age <= 6 && !occupationCode.equals("2099908")) {
                    errorMsg = "年龄小于等于6周岁，客户职业信息需为“2099908-学龄前儿童&婴幼儿";
                    if (StringUtils.isNotBlank(errorMsg)) return errorMsg;
                }
            }
            /**员工和家属都应有的校验*/
            //校验性别与职业
            if (!sign.equals("3") && !sign.equals("4") && sex.equals("0") && occupationCode.equals("4071203")) {
                errorMsg = "职业信息与性别不匹配，请重新确认！";
                if (StringUtils.isNotBlank(errorMsg)) return errorMsg;
            }
            if (StringUtils.isNotBlank(errorMsg)) return errorMsg;
            //校验国籍与证件类型，证件号码以及年龄的关系
            errorMsg = checkNativeplaceByIdTypeandIdNoandAge(sign, nativeplace, idType, idno, age, birthDay, sex);
            if (StringUtils.isNotBlank(errorMsg)) return errorMsg;
            //校验证件有效期
            if (StringUtils.isNotBlank(idTypeEndDate)) {
                errorMsg = checkIdtypeEndDate(age, idType, idTypeEndDate, birthDay);
            }
            if (StringUtils.isNotBlank(errorMsg)) return errorMsg;
            return errorMsg;
        } catch (Exception e) {
            log.info("人员校验异常！error:{}", e);
            errorMsg = "人员校验异常！";
            return errorMsg;
        }
    }

    ;

    //checkParm
    public static String checkParm(Map<String, String> map) {
        if (map == null || map.get("sign") == null || map.get("sign").equals("")) {
            return "校验人员参数为空！";
        }
 		/*if(StringUtils.isBlank(map.get("nativeplace"))) {
 			return "校验人员参数缺失：国籍不能为空！";
 		}*/
        if ((!map.get("sign").equals("3") && !map.get("sign").equals("4") && StringUtils.isBlank(map.get("occupationCode")))) {
            return "校验人员参数缺失：职业代码不能为空！";
        }
        if (StringUtils.isBlank(map.get("idType"))) {
            return "校验人员参数缺失：证件类型不能为空！";
        }
        if (StringUtils.isBlank(map.get("idNo"))) {
            return "校验人员参数缺失：证件号不能为空！";
        }
        if (StringUtils.isNotBlank(map.get("idTypeEndDate"))) {
            if (!DateTimeUtil.isDate(map.get("idTypeEndDate"))) {
                return "证件有效期格式有误！";
            }
        }
        if (StringUtils.isBlank(map.get("sex"))) {
            return "校验人员参数缺失：性别不能为空！";
        }
        if (StringUtils.isBlank(map.get("birthDay"))) {
            return "校验人员参数缺失：出生日期不能为空！";
        } else {
            if (!DateTimeUtil.isDate(map.get("birthDay"))) {
                return "出生日期格式有误！";
            } else {
                if (!DateTimeUtil.checkDate(map.get("birthDay"), DateTimeUtil.getCurrentDate())) {
                    return "出生日期不得大于当前日期！";
                }
            }
        }
        return "";
    }

    ;

    //获取员工年龄周岁
    public static int checkStaffAge(String birthday) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Calendar curr = Calendar.getInstance();
            Calendar born = Calendar.getInstance();
            curr.setTime(new Date());
            born.setTime(sdf.parse(birthday));
            int age = curr.get(Calendar.YEAR) - born.get(Calendar.YEAR);
            if (age <= 0) {
                return 0;
            }
            int currMonth = curr.get(Calendar.MONTH);
            int currDay = curr.get(Calendar.DAY_OF_MONTH);
            int bornMonth = born.get(Calendar.MONTH);
            int bornDay = born.get(Calendar.DAY_OF_MONTH);
            if ((currMonth < bornMonth) || (currMonth == bornMonth && currDay < bornDay)) {
                age--;
            }
            return Math.max(age, 0);
        } catch (ParseException e) {
            return -2;
        }
    }

    //校验国籍与证件类型，证件号码以及年龄的关系
    public static String checkNativeplaceByIdTypeandIdNoandAge(String sign, String nativeplace, String idType, String idno, Integer age, String birthDay, String gender) {
        log.info("校验国籍与证件类型，证件号码以及年龄的关系,nativeplace:" + nativeplace + ",idType:" + idType + ",idno:" + idno + ",age:" + age + ",birthDay:" + birthDay);
        String errorMsg = "";
        if (StringUtils.isBlank(idType) || StringUtils.isBlank(idno) || age == null || StringUtils.isBlank(birthDay)) {
            errorMsg = "校验国籍与证件类型，证件号码以及年龄的关系失败，参数有误！";
            return errorMsg;
        }
        //中国
        if (StringUtils.isNotBlank(nativeplace)) {
            AddressCheckService addressCheckService = EflexApplication.ac.getBean(AddressCheckService.class);
            String checkNationality = addressCheckService.checkNationalityCode(nativeplace);
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(checkNationality)) {
                errorMsg += checkNationality;
            }
            if (idType.matches("^[047]+$")) {
                if (!nativeplace.equals("CHN")) {
                    /*if (sign.equals("1") || sign.equals("3")) {
                        errorMsg += "国籍为中国时 证件类型仅允许选择 身份证！";
                    } else {
                        //"国籍为中国时 证件类型仅允许选择 身份证、户口本、出生证！";
                        errorMsg += "证件类型为：0-身份证、4-户口本、7-出生证时，国籍需为：中国";
                    }*/
                    errorMsg += "证件类型为：0-身份证、4-户口本、7-出生证时，国籍需为：中国;";

                }
//                else {
//                    //"国籍为中国时 证件类型仅允许选择 身份证、户口本、出生证！";
//                    errorMsg += "证件类型为：0-身份证、4-户口本、7-出生证时，国籍需为：中国";
//                }
            }
            //非中国、中国香港、中国澳门、中国台湾国籍
            if (idType.equals("1") || idType.equals("I")) {
                if (Arrays.asList(new String[]{"CHN", "HKG", "MAC", "TWN"}).contains(nativeplace)) {
                    //"非中国大陆居民、中国香港、中国澳门、中国台湾居民的外籍客户 证件类型暂只接受 外国公民护照，外国人永久居留身份证！";
                    errorMsg += "证件类型为：1-外国公民护照、I-外国人永久居留身份证时，国籍不允许为：中国、中国香港、中国澳门、中国台湾;";
                }
            }
            //中国香港、中国澳门
            if (idType.matches("^[BG]+$")) {
                if (!Arrays.asList(new String[]{"HKG", "MAC"}).contains(nativeplace)) {
                    //"国籍为中国香港、中国澳门时 证件类型仅允许选择 港澳居民来往大陆通行证和港澳居民居住证";
                    errorMsg += "证件类型为：B-港澳居民来往大陆通行证、G-港澳居民居住证时，国籍需为：中国香港、中国澳门;";
                }
            }
            //中国台湾
            if (idType.matches("^[EH]+$")) {
                if (!nativeplace.equals("TWN")) {
                    //"国籍为中国台湾时 证件类型仅允许选择 台湾居民来往大陆通行证和台湾居民居住证";
                    errorMsg += "证件类型为：E-台湾居民来往大陆通行证、H-台湾居民居住证，国籍需为：中国台湾;";
                }
            }
        }
        if (idType.matches("^[047]+$")) {
            FDPlaceMapper fdPlaceMapper = EflexApplication.ac.getBean(FDPlaceMapper.class);
            String substring = idno.substring(0, 2);
            String like = fdPlaceMapper.selectLike(substring);
            switch (idType) {
                case "4":
                    if (age >= 16) {
                        errorMsg += "16周岁以下证件类型才可选户口本！";
                    } else {
                        if (!idno.matches("(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)")) {
                            errorMsg += "证件类型为：4-户口本时，则需录入18位数字或者（17位数字+尾号为大字X）！";
                        }
                        if (StringUtils.isEmpty(like)) {
                            errorMsg += "证件号码中的前两位非有效的省份代码，请确认";
                        }
                    }
                    break;
                case "0":
                    if (!idno.matches("(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)")) {
                        errorMsg += "证件类型为：0-身份证时，则需录入18位数字或者（18位数字或17位数字+X）！";
                    }
                    if (StringUtils.isEmpty(like)) {
                        errorMsg += "证件号码中的前两位非有效的省份代码，请确认";
                    }
                    char genderDigit = idno.charAt(idno.length() - 2);

                    int genderDigitValue = Character.getNumericValue(genderDigit);

                    if (genderDigitValue % 2 == 0) {
                        if (!gender.equals("1")) {
                            errorMsg += "证件号码无效，倒数第二位性别位错误，请确认";
                        }
                    } else {
                        if (!gender.equals("0")) {
                            errorMsg += "证件号码无效，倒数第二位性别位错误，请确认";
                        }
                    }
                    break;
                case "7":
                    if (age >= 2) {
                        errorMsg += "2周岁以下证件类型才可选出生证！";
                    } else {
                        if (!idno.matches("[a-zA-Z]{1}[0-9]{9}")) {
                            errorMsg += "证件类型为：7-出生证时，则需录入“字母+9位数字”的有效证件号码”！";
                        }
                    }
                    break;
            }
        } else if (idType.equals("1")) {
            if (idno.length() > 18 || idno.length() < 8) {
                errorMsg += "证件类型为：1-外国公民护照时，则需录入为8-18位的数字和字母的有效证件号码";
            }
        } else if (idType.equals("I")) {//先保留不删除
            if (!idno.matches("^([0-9A-Za-z]{15})|([0-9A-Za-z]{18})")) {
                errorMsg += "证件类型为：I-外国人永久居留身份证时，则需录入为15或18位的数字和字母的有效证件号码！";
            }
        } else if (idType.equals("B")) {
            if (nativeplace != null && !"".equals(nativeplace)) {
                if (nativeplace.equals("HKG")) {
                    if (!idno.matches("[H]{1}[0-9]{8}")) {
                        errorMsg += "证件类型为：B-港澳居民来往大陆通行证时，同时国籍为中国香港，则需录入“H+8位数字”！";
                    }
                } else if (nativeplace.equals("MAC")) {
                    if (!idno.matches("[M]{1}[0-9]{8}")) {
                        errorMsg += "证件类型为：B-港澳居民来往大陆通行证时，同时国籍为中国澳门，则需录入“M+8位数字”！";
                    }
                }
            }
        } else if (idType.equals("G")) {

            /**
             * (!idno.substring(0, 6).equals("810000")
             *                             || !idno.substring(6, 14).equals(birthDay.replace("-", ""))
             *                             || !idno.substring(14, 18).matches("[0-9A-Za-z]{4}"))
             *
             *                             (!idno.substring(0, 6).equals("820000")
             *                             || !idno.substring(6, 14).equals(birthDay.replace("-", ""))
             *                             || !idno.substring(14, 18).matches("[0-9A-Za-z]{4}"))
             */
            if (nativeplace != null && !"".equals(nativeplace)) {
                if (nativeplace.equals("HKG") && (!idno.substring(0, 6).equals("810000") || idno.length() != 18)) {
                    errorMsg += "证件类型为：G-港澳居民居住证时，同时国籍为中国香港，则需录入“810000”开头的18位有效证件号码！";
                }
                if (nativeplace.equals("MAC") && (!idno.substring(0, 6).equals("820000") || idno.length() != 18)) {
                    errorMsg += "证件类型为：G-港澳居民居住证时，同时国籍为中国澳门，则需录入“820000”开头的18位有效证件号码！";
                }
            }

        } else if (idType.equals("E")) {
            if (!idno.matches("^\\d{8}$")) {
                errorMsg += "证件类型为：E-台湾居民来往大陆通行证时，则需录入8位数字的有效证件号码！";
            }
        } else if (idType.equals("H")) {
            /*if (idno.length() != 18) {
                errorMsg += "证件类型为：H-台湾居民居住证，证件号码需录入“830000”开头的18位有效证件号码！";

            } else {*/
            if (!idno.substring(0, 6).equals("830000")
                    || idno.length() != 18) {
                errorMsg += "证件类型为：H-台湾居民居住证，则需录入“830000”开头的18位有效证件号码！";
            }
            //}
        }
        return errorMsg;
    }

    //校验证件有效期
    public static String checkIdtypeEndDate(Integer age, String idType, String idTypeEndDate, String birthDay) {
        if (age == null || StringUtils.isBlank(idType)) {
            return "校验证件有效期失败：参数有误！";
        }
        if (!DateTimeUtil.checkFutureDate(idTypeEndDate)) {
            return "证件有效期填写有误，不得小于当天日期！";
        }
        if (!DateTimeUtil.checkDate(idTypeEndDate, "9999-12-31")) {
            System.out.println("证件有效期填写有误，不得大于9999-12-31(即长期)！");
        }
        try {
            if (idType.equals("0")) {
                if (age < 16 && !DateTimeUtil.checkDate(idTypeEndDate, DateTimeUtil.getAppointDate(new Date(), 5, 1))) {
                    return "由于当前年龄为16周岁（不含）以下且证件类型为“0-身份证”时，则证件有效期不得超过业务办理日期+5年！";
                }
                if (age >= 16 && age <= 25 && !DateTimeUtil.checkDate(idTypeEndDate, DateTimeUtil.getAppointDate(new Date(), 10, 1))) {
                    return "由于当前年龄为16周岁（含）-25周岁（含）且证件类型为“0-身份证”时，则证件有效期不得超过“业务办理日期+10年！";
                }
                if (age >= 26 && age <= 45 && !DateTimeUtil.checkDate(idTypeEndDate, DateTimeUtil.getAppointDate(new Date(), 20, 1))) {
                    return "由于当前年龄为26周岁（含）-45周岁（含）且证件类型为“0-身份证”时，则证件有效期不得超过“业务办理日期+20年！";
                }
            }
            if (idType.matches("^[1BEGHI]+$") && idTypeEndDate.equals("9999-12-31")) {
                return "证件有效期不得选择'长期'！";
            }

            String appointDate = DateTimeUtil.getAppointDate(new SimpleDateFormat("yyyy-MM-dd").parse(birthDay), 3, 1);
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            Date d = df.parse(appointDate);
            Calendar cal = Calendar.getInstance();
            cal.setTime(d);
            cal.add(Calendar.DATE, -1);
            String date = df.format(cal.getTime());
            if (idType.equals("7") && !idTypeEndDate.equals(date)) {
                return "证件类型为“出生证”时，则证件有效期默认“客户出生日期+3年-1天”且不允许修改；";
            }
            String appointDate2 = DateTimeUtil.getAppointDate(new SimpleDateFormat("yyyy-MM-dd").parse(birthDay), 17, 1);
            Date dd = df.parse(appointDate2);
            cal.setTime(dd);
            cal.add(Calendar.DATE, -1);
            String date2 = df.format(cal.getTime());
            if (idType.equals("4") && !idTypeEndDate.equals(date2)) {
                return "证件类型为“户口本”时，则证件有效期默认“客户出生日期+17年-1天”且不允许修改。";
            }
            if (idType.matches("^[BI]+$")) {
                if (age < 16 && !DateTimeUtil.checkDate(idTypeEndDate, DateTimeUtil.getAppointDate(new Date(), 5, 1))) {
                    return "证件类型为B-港澳居民来往大陆通行证、I-外国人永久居留身份证，同时客户年龄为0-17周岁(含），则证件有效期不得大于（业务受理日期+5年）";
                }
                if (age >= 18 && !DateTimeUtil.checkDate(idTypeEndDate, DateTimeUtil.getAppointDate(new Date(), 10, 1))) {
                    return "证件类型为B-港澳居民来往大陆通行证、I-外国人永久居留身份证，同时客户年龄大于等于18周岁时，证件有效期不得大于（业务受理日期+10年）";
                }

            }
            if (idType.matches("^[EGH]+$")) {
                if (!DateTimeUtil.checkDate(idTypeEndDate, DateTimeUtil.getAppointDate(new Date(), 5, 1))) {
                    return "E-台湾居民来往大陆通行证、G-港澳居民居住证时、H-台湾居民居住证，证件有效期不得大于（业务受理日期+5年）";
                }

            }
        } catch (ParseException e) {
            return "校验证件有效期失败！";
        }
        return "";
    }

    //手机号校验
    public static boolean checkMobilePhone(String phone) {
        String regex = "^((13[0-9])|(14[01456879])|(15([0-35-9]))|(16([2567]))|(17[0-8])|(18[0-9])|(19[0-35-9]))\\d{8}$";
        if (StringUtil.isEmpty(phone) || phone.length() != 11) {
            return false;
        } else {
            Pattern p = Pattern.compile(regex);
            Matcher m = p.matcher(phone);
            return m.matches();
        }
    }

    //校验证件有效期
    public static String checkDailyIdtypeEndDate(String idType, String idTypeEndDate, String birthDay) {
        if (StringUtils.isBlank(birthDay) || StringUtils.isBlank(idType)) {
            return "校验证件有效期失败：参数有误！";
        }
        if (!DateTimeUtil.checkFutureDate(idTypeEndDate)) {
            return "证件有效期填写有误，不得小于当天日期！";
        }
        if (!DateTimeUtil.checkDate(idTypeEndDate, "9999-12-31")) {
            System.out.println("证件有效期填写有误，不得大于9999-12-31(即长期)！");
            return "证件有效期填写有误，不得大于9999-12-31(即长期)！";
        }
        int age = Integer.parseInt(DateTimeUtil.getAge(birthDay));
        try {
            if (idType.equals("0")) {
                if (age < 16 && !DateTimeUtil.checkDate(idTypeEndDate, DateTimeUtil.getAppointDate(new Date(), 5, 1))) {
                    return "由于当前年龄为16周岁（不含）以下且证件类型为“0-身份证”时，则证件有效期不得超过业务办理日期+5年！";
                }
                if (age >= 16 && age <= 25 && !DateTimeUtil.checkDate(idTypeEndDate, DateTimeUtil.getAppointDate(new Date(), 10, 1))) {
                    return "由于当前年龄为16周岁（含）-25周岁（含）且证件类型为“0-身份证”时，则证件有效期不得超过“业务办理日期+10年！";
                }
                if (age >= 26 && age <= 45 && !DateTimeUtil.checkDate(idTypeEndDate, DateTimeUtil.getAppointDate(new Date(), 20, 1))) {
                    return "由于当前年龄为26周岁（含）-45周岁（含）且证件类型为“0-身份证”时，则证件有效期不得超过“业务办理日期+20年！";
                }
            }
            if (idType.matches("^[1BEGHI]+$") && idTypeEndDate.equals("9999-12-31")) {
                return "证件有效期不得选择'长期'！";
            }
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String appointDate = DateTimeUtil.getAppointDate(dateFormat.parse(birthDay), 3, 1);
            Date parse = dateFormat.parse(appointDate);
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(parse);
            calendar.add(Calendar.DATE, -1);
            if (idType.equals("7") && !idTypeEndDate.equals(dateFormat.format(calendar.getTime()))) {
                return "证件类型为“出生证”时，则证件有效期默认“客户出生日期+3年-1天”且不允许修改；";
            }
            String appointDate1 = DateTimeUtil.getAppointDate(new SimpleDateFormat("yyyy-MM-dd").parse(birthDay), 17, 1);
            Date parse1 = dateFormat.parse(appointDate1);
            Calendar calendar1 = new GregorianCalendar();
            calendar1.setTime(parse1);
            calendar1.add(Calendar.DATE, -1);
            if (idType.equals("4") && !idTypeEndDate.equals(calendar1)) {
                return "证件类型为“户口本”时，则证件有效期默认“客户出生日期+17年-1天”且不允许修改。";
            }
        } catch (ParseException e) {
            return "校验证件有效期失败！";
        }
        return "";
    }

    public static String changePayStatus(String payStatus, String orderStatus) {
        String orderStatu = "";
        switch (payStatus) {
            case "S":
                orderStatu = "06";
                break;//支付成功
            case "P":
                orderStatu = "01";
                break;//提交批扣
            case "F":
                orderStatu = "07";
                break;//支付失败
            case "R":
                orderStatu = "05";
                break;//支付中
            case "PI":
                orderStatu = "04";
                break;//待支付
        }
        if (payStatus.equals("P") && orderStatus.equals("015")) {
            orderStatu = "015";
        }
        return orderStatu;
    }


    /**
     * 校验中文姓名
     * <p>
     * 中文姓名仅限：全角汉字（例如：李明）、全角点号“·”（例如：阿凡提·莫汉莫德）、全角点号“．”(例如：阿拉汉．西提木)。
     *
     * @param params
     * @return
     */
    public static String checkChineseName(String params) {
        if (StringUtils.isNotBlank(params) && StringUtils.isNotBlank(params.trim())) {
            // Pattern CHINESENAME_PATTERN = Pattern.compile("^[\u4e00-\u9fa5．·]{0,}$");
            // Pattern CHINESENAME_PATTERN = Pattern.compile("^[u2E80-uFE4F．·]{0,}$");
            //Pattern CHINESENAME_PATTERN = Pattern.compile("^[\u2E80-\uFE4F．·]{1,}+$");
            //Pattern CHINESENAME_PATTERN = Pattern.compile("^([a-zA-Z0-9\\u4e00-\\u9fa5\\·]{1,10})$");
            Pattern CHINESENAME_PATTERN = Pattern.compile("^([\\u4e00-\\u9fa5．·]{1,20})$");
            if (params.trim().length() > 20) {
                return params + "姓名长度不能超过20个字符";
            }
            Matcher m = CHINESENAME_PATTERN.matcher(params.trim());//params.trim()   去除字符串两端的空格
            if (!m.matches()) {
                return params + "中文姓名仅限：全角汉字、全角点号“·”、全角点号“．”";
            }
//            利用String.replaceAll()将所有的汉字替换成空字符，利用其与原字符串的长度差得到汉字的个数。
            String regex = "[\u2E80-\uFE4F]";
            if (params.length() - params.replaceAll(regex, "").length() < 2) {
                return "中文姓名长度至少为两个汉字";
            }
            return checkFirstChinese(params);
        }
        return "";
    }

    public static String checkChineseName2(String params) {
        if (StringUtils.isNotBlank(params) && StringUtils.isNotBlank(params.trim())) {
            // Pattern CHINESENAME_PATTERN = Pattern.compile("^[\u4e00-\u9fa5．·]{0,}$");
            // Pattern CHINESENAME_PATTERN = Pattern.compile("^[u2E80-uFE4F．·]{0,}$");
            //Pattern CHINESENAME_PATTERN = Pattern.compile("^[\u2E80-\uFE4F．·]{1,}+$");
            //Pattern CHINESENAME_PATTERN = Pattern.compile("^([a-zA-Z0-9\\u4e00-\\u9fa5\\·]{1,10})$");
            Pattern CHINESENAME_PATTERN = Pattern.compile("^([\\u4e00-\\u9fa5．·]{1,20})$");
            if (params.trim().length() > 20) {
                return params + "姓名长度不能超过20个字符";
            }
            Matcher m = CHINESENAME_PATTERN.matcher(params.trim());//params.trim()   去除字符串两端的空格
            if (!m.matches()) {
                return "中文姓名仅限：全角汉字、全角点号“·”、全角点号“．”";
            }
//            利用String.replaceAll()将所有的汉字替换成空字符，利用其与原字符串的长度差得到汉字的个数。
            String regex = "[\u2E80-\uFE4F]";
            if (params.length() - params.replaceAll(regex, "").length() < 2) {
                return "中文姓名长度至少为两个汉字";
            }
            return checkFirstChinese(params);
        }
        return "";
    }

    /**
     * 校验英文姓名
     * <p>
     * 中文姓名仅限：半角英文、半角点号“.”（例如：Jenny.Kitty）、半角空格号(例如：Jenny Kitty）、半角点号“-” （例如：Jenny-Kitty）。
     *
     * @param params
     * @return
     */
    public static String checkEnglishName(String params) {
        if (StringUtils.isNotBlank(params) && StringUtils.isNotBlank(params.trim())) {
            Pattern CHINESENAME_PATTERN = Pattern.compile("^[a-zA-Z .-]{0,}$");
            String s = params.replaceAll(" +", " ");
            String trim = s.trim();
            Matcher m = CHINESENAME_PATTERN.matcher(trim);//params.trim()   去除字符串两端的空格
            if (!m.matches()) {
                return "英文姓名仅限：半角英文、半角点号“.”、半角空格号、半角点号“-”";
            }
            String regex = "[a-zA-Z]";
            if (trim.length() - trim.replaceAll(regex, "").length() < 4) {
                return "英文姓名长度至少4个字母";
            }
            return checkFirstEnglish(trim);
        }
        return "";
    }

    /**
     * 外籍校验姓名
     */
    public static String checkForeignName(String params) {
        if (StringUtils.isNotBlank(params) && StringUtils.isNotBlank(params.trim())) {
            Pattern english = Pattern.compile("^[a-zA-Z .-]{0,}$");
            String s = params.replaceAll(" +", " ");
            String trim = s.trim();
            boolean e = english.matcher(trim).matches();
            Pattern chinese = Pattern.compile("^[\u2E80-\uFE4F．·]{1,}+$");
            boolean c = chinese.matcher(params.trim()).matches();
            if (e) {
                return checkEnglishName(params);
            }
            if (c) {
                return checkChineseName(params);
            }
        }
        return "姓名不能同时有中英文";
    }

    /**
     * 校验固定电话
     * <p>
     * 只能录入“-”、“（”、“）”、“+”、数字，如果录入其他字符，需阻断提示   有传值才校验，没传值无需校验
     *
     * @param params
     * @return
     */
    public static String checkTel(String params) {
        if (StringUtils.isNotBlank(params) && StringUtils.isNotBlank(params.trim())) {
            //String pattern = "[0-9-()+]{7,18}";
            //String pattern = "[0-9-（）+]{1,18}";
            String pattern = "^\\d{3}-\\d{7,8}|\\d{4}-\\d{7,8}$";
            Pattern r = Pattern.compile(pattern);
            Matcher m = r.matcher(params.trim());//params.trim()   去除字符串两端的空格
            if (!m.matches()) {
                return "固定电话格式应为：3位或4位区号-7位或8位电话号码";
            }
            return checkRepeat(params);
        }
        return "";
    }


    public static String checkRepeat(String params) {
        //            	“-”、“（”、“）”、“+”不可连续。
        String U = "";//“-”
        String E = "";//“（”
        String F = "";//“）”
        String J = "";//“+”
        char[] chars = params.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if ((chars[i] + "").equals("-")) {
                U = U + i + "U";
            } else if ((chars[i] + "").equals("（")) {
                E = E + i + "E";
            } else if ((chars[i] + "").equals("）")) {
                F = F + i + "F";
            } else if ((chars[i] + "").equals("+")) {
                J = J + i + "J";
            }
        }
        if (U.length() > 0) {
            String[] flags = U.split("U");
            if (flags.length > 1) {
                for (int i = 1; i < flags.length; i++) {
                    if (Integer.parseInt(flags[i]) - Integer.parseInt(flags[i - 1]) == 1) {
                        //索引相差 1 ，说明相邻。
                        return "“-”、“（”、“）”、“+”不可连续。";
                    }
                }
            }
        }
        if (E.length() > 0) {
            String[] EE = E.split("E");
            if (EE.length > 1) {
                for (int i = 1; i < EE.length; i++) {
                    if (Integer.parseInt(EE[i]) - Integer.parseInt(EE[i - 1]) == 1) {
                        //索引相差 1 ，说明相邻。
                        return "“-”、“（”、“）”、“+”不可连续。";
                    }
                }
            }
        }
        if (F.length() > 0) {
            String[] FF = F.split("F");
            if (FF.length > 1) {
                for (int i = 1; i < FF.length; i++) {
                    if (Integer.parseInt(FF[i]) - Integer.parseInt(FF[i - 1]) == 1) {
                        //索引相差 1 ，说明相邻。
                        return "“-”、“（”、“）”、“+”不可连续。";
                    }
                }
            }
        }
        if (J.length() > 0) {
            String[] JJ = J.split("J");
            if (JJ.length > 1) {
                for (int i = 1; i < JJ.length; i++) {
                    if (Integer.parseInt(JJ[i]) - Integer.parseInt(JJ[i - 1]) == 1) {
                        //索引相差 1 ，说明相邻。
                        return "“-”、“（”、“）”、“+”不可连续。";
                    }
                }
            }
        }
        return "";
    }

    public static String checkFirstChinese(String params) {
        char[] chars = params.toCharArray();
        int length = chars.length;
        if ((chars[0] + "").equals("·") || (chars[0] + "").equals("．") || (chars[length - 1] + "").equals("·") || (chars[length - 1] + "").equals("．")) {
            return "全角点号“·”、全角点号“．”不能出现在首尾、不能连续。";
        }
        String F = "";//“·”
        String J = "";//“．”
        String Z = "";//“．”“·”
        for (int i = 0; i < chars.length; i++) {
            if ((chars[i] + "").equals("·")) {
                F = F + i + "F";
            } else if ((chars[i] + "").equals("．")) {
                J = J + i + "J";
            }
            if ((chars[i] + "").equals("．") || (chars[i] + "").equals("·")) {
                Z = Z + i + "Z";
            }
        }
        if (F.length() > 0) {
            String[] FF = F.split("F");
            if (FF.length > 1) {
                for (int i = 1; i < FF.length; i++) {
                    if (Integer.parseInt(FF[i]) - Integer.parseInt(FF[i - 1]) == 1) {
                        //索引相差 1 ，说明相邻。
                        return "全角点号“·”、全角点号“．”不能出现在首尾、不能连续。";
                    }
                }
            }
        }
        if (J.length() > 0) {
            String[] JJ = J.split("J");
            if (JJ.length > 1) {
                for (int i = 1; i < JJ.length; i++) {
                    if (Integer.parseInt(JJ[i]) - Integer.parseInt(JJ[i - 1]) == 1) {
                        //索引相差 1 ，说明相邻。
                        return "全角点号“·”、全角点号“．”不能出现在首尾、不能连续。";
                    }
                }
            }
        }
        if (Z.length() > 0) {
            String[] ZZ = Z.split("Z");
            if (ZZ.length > 1) {
                for (int i = 1; i < ZZ.length; i++) {
                    if (Integer.parseInt(ZZ[i]) - Integer.parseInt(ZZ[i - 1]) == 1) {
                        //索引相差 1 ，说明相邻。
                        return "全角点号“·”、全角点号“．”不能出现在首尾、不能连续。";
                    }
                }
            }
        }
        if (params.trim().lastIndexOf(" ") != -1) {
            return "中文姓名中不能含有空格。";
        }
        return "";
    }

    public static String checkFirstEnglish(String params) {
        char[] chars = params.toCharArray();
        int length = chars.length;
        if ((chars[0] + "").equals(".") || (chars[0] + "").equals(" ") || (chars[0] + "").equals("-") ||
                (chars[length - 1] + "").equals(".") || (chars[length - 1] + "").equals(" ") || (chars[length - 1] + "").equals("-")) {
            return "半角点号“.”、半角空格号、半角点号“-”不能出现在首尾、不能连续。";
        }
        String E = "";//“.”
        String F = "";//“ ”
        String J = "";//“-”
        String Z = "";//“-”“ ”“.”
        for (int i = 0; i < chars.length; i++) {
            if ((chars[i] + "").equals(".")) {
                E = E + i + "E";
                Z = Z + i + "Z";
            } else if ((chars[i] + "").equals(" ")) {
                F = F + i + "F";
                Z = Z + i + "Z";
            } else if ((chars[i] + "").equals("-")) {
                J = J + i + "J";
                Z = Z + i + "Z";
            }
        }
        if (E.length() > 0) {
            String[] EE = E.split("E");
            if (EE.length > 1) {
                for (int i = 1; i < EE.length; i++) {
                    if (Integer.parseInt(EE[i]) - Integer.parseInt(EE[i - 1]) == 1) {
                        //索引相差 1 ，说明相邻。
                        return "半角点号“.”、半角空格号、半角点号“-”不能出现在首尾、不能连续。";
                    }
                }
            }
        }
      /*  if (F.length() > 0) {
            String[] FF = F.split("F");
            if (FF.length > 1) {
                for (int i = 1; i < FF.length; i++) {
                    if (Integer.parseInt(FF[i]) - Integer.parseInt(FF[i - 1]) == 1) {
                        //索引相差 1 ，说明相邻。
                        return "半角点号“.”、半角空格号、半角点号“-”不能出现在首尾、不能连续。";
                    }
                }
            }
        }*/
        if (J.length() > 0) {
            String[] JJ = J.split("J");
            if (JJ.length > 1) {
                for (int i = 1; i < JJ.length; i++) {
                    if (Integer.parseInt(JJ[i]) - Integer.parseInt(JJ[i - 1]) == 1) {
                        //索引相差 1 ，说明相邻。
                        return "半角点号“.”、半角空格号、半角点号“-”不能出现在首尾、不能连续。";
                    }
                }
            }
        }
        if (Z.length() > 0) {
            String[] ZZ = Z.split("Z");
            if (ZZ.length > 1) {
                for (int i = 1; i < ZZ.length; i++) {
                    if (Integer.parseInt(ZZ[i]) - Integer.parseInt(ZZ[i - 1]) == 1) {
                        //索引相差 1 ，说明相邻。
                        return "半角点号“.”、半角空格号、半角点号“-”不能出现在首尾、不能连续。";
                    }
                }
            }
        }
        return "";
    }

    /**
     * 判断是否存在汉字
     *
     * @param countname
     * @return
     */
    public static boolean checkcountname(String countname) {
        Pattern p = Pattern.compile("[\u2E80-\uFE4F]");
        Matcher m = p.matcher(countname);
        if (m.find()) {
            return true;
        }
        return false;
    }

    /**
     * 校验电子邮箱
     *
     * @param email
     * @return
     */
    public static boolean checkEmail(String email) {
        return email.matches("^([a-zA-Z0-9]+[_|\\_|\\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\\_|\\.]?)*[a-zA-Z0-9]+\\.[a-zA-Z]{2,3}$");
    }

    /**
     * 校验企业证件类型和证件号
     *
     * @param grpIdType
     * @param grpIdNo
     */
    public static String checkGrpTypeAndGrpIdNo(String grpIdType, String grpIdNo) {
        // 证件号
        if (grpIdNo.length() < 3) {
            return "企业证件号有误!";
        }
        switch (grpIdType) {
            // 统一社会信用代码
            case "1":
                // 1、统一社会信用代码长度应为18位
                if (grpIdNo.length() != 18) {
                    return "统一社会信用代码长度应为18位！";
                }
                // 2、增加核心的校验规则
                if (!grpIdNo.matches("^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$")) {
                    return "统一社会信用代码有误，请检查！";
                }
                // 3、统一社会信用代码的前两位应为"11,12,13,19,21,29,31,32,33,34,35,39,41,49,51,52,53,59,61,62,69,71,72,79,81,89,91,92,93,A1,A9,N1,N2,N3,N9,Y1,54,55,37,G1"中的一个
                String substring = grpIdNo.substring(0, 2);
                List<String> stringList = Arrays.asList("11", "12", "13", "19", "21", "29", "31", "32", "33",
                        "34", "35", "39", "41", "49", "51", "52", "53", "59", "61", "62", "69", "71", "72", "79", "81",
                        "89", "91", "92", "93", "A1", "A9", "N1", "N2", "N3", "N9", "Y1", "54", "55", "37", "G1");
                if (!stringList.contains(substring)) {
                    return "统一社会信用代码格式有误！";
                }
                // 4、统一社会信用代码的第3-8位对应《业务代码表》中的"县及县以上行政区划代码"
                String substring1 = grpIdNo.substring(2, 8);
                AddressCheckService addressCheckService = EflexApplication.ac.getBean(AddressCheckService.class);
                boolean b = addressCheckService.checkAddressCode(substring1);
                if (!b) {
                    return "统一社会信用代码格式有误！";
                }

                break;
            // 组织结构代码证,由8位数字加上一位校验码(数字或大写字母)组成
            case "2":
                if (!grpIdNo.matches("^d{8}-[0-9A-Y]$")) {
                    return "组织机构代码证号码有误，请检查！";
                }
                break;
            // 税务登记证,由6位行政区划代码和9位组织机构代码组成
            case "3":
                if (!grpIdNo.matches("^[0-9]{15}$")) {
                    return "税务登记证号码有误，请检查！";
                }
                break;
            // 营业执照
            case "4":
                if (grpIdNo.length() == 15) {
                    if (!grpIdNo.matches("^[0-9]{15}$")) {
                        return "营业执照证件号码有误，请检查！";
                    }
                } else if (grpIdNo.length() == 18) {
                    if (!grpIdNo.matches("^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$")) {
                        return "营业执照证件号码有误，请检查！";
                    }
                    List<String> stringList1 = Arrays
                            .asList("11", "12", "13", "19", "21", "29", "31", "32", "33", "34", "35", "39",
                                    "41", "49", "51", "52", "53", "59", "61", "62", "69", "71", "72", "79", "81", "89",
                                    "91", "92", "93", "A1", "A9", "N1", "N2", "N3", "N9", "Y1", "54", "55", "37", "G1");
                    if (!stringList1.contains(grpIdNo.substring(0, 2))) {
                        return "营业执照证件号码有误，请检查！";
                    }
                    String substring2 = grpIdNo.substring(2, 8);
                    AddressCheckService addressCheckService1 = EflexApplication.ac.getBean(AddressCheckService.class);
                    boolean b1 = addressCheckService1.checkAddressCode(substring2);
                    if (!b1) {
                        return "营业执照证件号码有误，请检查！";
                    }
                } else {
                    return "营销执照证件号格式有误！";
                }
                break;
            // 事业单位法人证书,由任意16位字符组成
            case "5":
                if (!grpIdNo.matches("^[\\d\\w\\W]{16}$")) {
                    return "事业单位法人证书号码有误，请检查！";
                }
                break;
            // 社会团体法人证书,由任意16位字符组成
            case "6":
                if (!grpIdNo.matches("^[\\d\\w\\W]{16}$")) {
                    return "社会团体法人证书号码有误，请检查！";
                }
                break;
            // 民办非企业单位登记证书,由16位字母数字组成
            case "7":
                if (!grpIdNo.matches("^[A-Za-z0-9]{16}$")) {
                    return "民办非企业单位登记证书号码有误，请检查！";
                }
                break;
            // 基金会法人登记证书,由16位字母数字组成
            case "8":
                if (!grpIdNo.matches("^[A-Za-z0-9]{16}$")) {
                    return "基金会法人登记证书号码有误，请检查！";
                }
                break;
            // 工商注册号码,由6位首次登记机关码，8位顺序码，1位校验码组成
            case "9":
                if (!grpIdNo.matches("^[0-9]{15}$")) {
                    return "工商注册号码有误，请检查！";
                }
                break;
            // 其他证件
            case "10":
                if (!grpIdNo.matches("^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{3,50}$")) {
                    return "其他证件号码有误，请检查！";
                }
                break;
            default:
                return "企业证件类型不存在！";
        }
        return null;
    }

    /**
     * 校验企业证件类型和证件号，新的校验规则
     *
     * @param grpIdType
     * @param grpIdNo
     */
    public static String checkGrpTypeAndGrpIdNo1(String grpIdType, String grpIdNo) {
        // 证件号
        if (grpIdNo.length() < 3) {
            return "企业证件号有误!";
        }
        switch (grpIdType) {
            // 统一社会信用代码
            case "1":
                // 1、统一社会信用代码长度应为18位
                if (grpIdNo.length() != 18) {
                    return "统一社会信用代码长度应为18位！";
                }
                // 2、增加核心的校验规则
                if (!grpIdNo.matches("^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$")) {
                    return "统一社会信用代码有误，请检查！";
                }
                // 3、统一社会信用代码的前两位应为"11,12,13,19,21,29,31,32,33,34,35,39,41,49,51,52,53,59,61,62,69,71,72,79,81,89,91,92,93,A1,A9,N1,N2,N3,N9,Y1,54,55,37,G1"中的一个
                String substring = grpIdNo.substring(0, 2);
                List<String> stringList = Arrays.asList(new String[]{"11", "12", "13", "19", "21", "29", "31", "32", "33",
                        "34", "35", "39", "41", "49", "51", "52", "53", "59", "61", "62", "69", "71", "72", "79", "81",
                        "89", "91", "92", "93", "A1", "A9", "N1", "N2", "N3", "N9", "Y1", "54", "55", "37", "G1"});
                if (!stringList.contains(substring)) {
                    return "统一社会信用代码格式有误，前两位不符合要求！";
                }
                // 4、统一社会信用代码的第3-8位对应《业务代码表》中的"县及县以上行政区划代码"
                String substring1 = grpIdNo.substring(2, 8);
                AddressCheckService addressCheckService = EflexApplication.ac.getBean(AddressCheckService.class);
                boolean b = addressCheckService.checkAddressCode(substring1);
                if (!b) {
                    return "统一社会信用代码格式有误，3-8位不符合要求！";
                }

                break;
            // 组织结构代码证,由8位数字加上一位校验码(数字或大写字母)组成
            case "2":
                if (!grpIdNo.matches("^[0-9A-Za-z]{9}$")) {
                    return "组织机构代码证号码有误，请检查！";
                }
                break;
            // 税务登记证
            case "3":
                if (!grpIdNo.matches("^[0-9A-Za-z]{3,50}$")) {
                    return "税务登记证号码有误，请检查！";
                }
                break;
            // 营业执照
            case "4":
                if (grpIdNo.length() == 15) {
                    return "营业执照证件号码有误，请检查！";
                } else if (grpIdNo.length() == 18) {
                    if (!grpIdNo.matches("^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$")) {
                        return "营业执照证件号码有误，请检查！";
                    }
                    List<String> stringList1 = Arrays
                            .asList(new String[]{"11", "12", "13", "19", "21", "29", "31", "32", "33", "34", "35", "39",
                                    "41", "49", "51", "52", "53", "59", "61", "62", "69", "71", "72", "79", "81", "89",
                                    "91", "92", "93", "A1", "A9", "N1", "N2", "N3", "N9", "Y1", "54", "55", "37", "G1"});
                    if (!stringList1.contains(grpIdNo.substring(0, 2))) {
                        return "营业执照证件号码有误，请检查！";
                    }
                    String substring2 = grpIdNo.substring(2, 8);
                    AddressCheckService addressCheckService1 = EflexApplication.ac.getBean(AddressCheckService.class);
                    boolean b1 = addressCheckService1.checkAddressCode(substring2);
                    if (!b1) {
                        return "营业执照证件号码有误，请检查！";
                    }
                } else {
                    return "营销执照证件号格式有误！";
                }
                break;
            // 事业单位法人证书,由任意16位字符组成
            case "5":
                if (!grpIdNo.matches("^[0-9A-Za-z]{3,50}$")) {
                    return "事业单位法人证书号码有误，请检查！";
                }
                break;
            // 社会团体法人证书,由任意16位字符组成
            case "6":
                if (!grpIdNo.matches("^[0-9A-Za-z]{3,50}$")) {
                    return "社会团体法人证书号码有误，请检查！";
                }
                break;
            // 民办非企业单位登记证书,由16位字母数字组成
            case "7":
                if (!grpIdNo.matches("^[0-9A-Za-z]{3,50}$")) {
                    return "民办非企业单位登记证书号码有误，请检查！";
                }
                break;
            // 基金会法人登记证书,由16位字母数字组成
            case "8":
                if (!grpIdNo.matches("^[0-9A-Za-z]{3,50}$")) {
                    return "基金会法人登记证书号码有误，请检查！";
                }
                break;
            // 工商注册号码,由6位首次登记机关码，8位顺序码，1位校验码组成
            case "9":
                if (!grpIdNo.matches("^[0-9A-Za-z]{3,50}$")) {
                    return "工商注册号码有误，请检查！";
                }
                break;
            // 其他证件
            case "10":
                if (!grpIdNo.matches("^[0-9A-Za-z]{3,50}$")) {
                    return "其他证件号码有误，请检查！";
                }
                break;
            default:
                return "企业证件类型不存在！";
        }
        return null;
    }

    /**
     * REQ-2757的的需求，把所有的证件号改为统一信用代码的校验
     *
     * @param grpIdType
     * @param grpIdNo
     * @return
     */
    public static String checkGrpTypeAndGrpIdNo2(String grpIdType, String grpIdNo) {
        // 证件号
        if (grpIdNo.length() < 3) {
            return "企业证件号码有误!";
        }
        // 1、统一社会信用代码长度应为18位
        if (grpIdNo.length() != 18) {
            return "企业证件号码不符合统一社会信用代码规则，长度应为18位！";
        }
        // 2、增加核心的校验规则
        if (!grpIdNo.matches("^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$")) {
            return "企业证件号码不符合统一社会信用代码规则，请检查！";
        }
        // 3、统一社会信用代码的前两位应为"11,12,13,19,21,29,31,32,33,34,35,39,41,49,51,52,53,59,61,62,69,71,72,79,81,89,91,92,93,A1,A9,N1,N2,N3,N9,Y1,54,55,37,G1"中的一个
        String substring = grpIdNo.substring(0, 2);
        List<String> stringList = Arrays.asList(new String[]{"11", "12", "13", "19", "21", "29", "31", "32", "33",
                "34", "35", "39", "41", "49", "51", "52", "53", "59", "61", "62", "69", "71", "72", "79", "81",
                "89", "91", "92", "93", "A1", "A9", "N1", "N2", "N3", "N9", "Y1", "54", "55", "37", "G1"});
        if (!stringList.contains(substring)) {
            return "企业证件号码不符合统一社会信用代码规则，前两位不符合要求！";
        }
        // 4、统一社会信用代码的第3-8位对应《业务代码表》中的"县及县以上行政区划代码"
        String substring1 = grpIdNo.substring(2, 8);
        AddressCheckService addressCheckService = EflexApplication.ac.getBean(AddressCheckService.class);
        boolean b = addressCheckService.checkAddressCode(substring1);
        if (!b) {
            return "企业证件号码不符合统一社会信用代码规则，3-8位不符合要求！";
        }
        return null;
    }

    /**
     * 校验邮政编码
     * 如有填写邮政编码信息，但填写信息不为6位数字时，系统阻断性提示
     *
     * @param postalCode
     * @return
     */
    public static String checkPostalCode(String postalCode) {
        if (postalCode.length() != 6) {
            return "通讯地址的邮政编码填写有误，请重新确认";
        }
        return "";
    }

    /**
     * 校验年收入
     * 当投保人或被保险人年收入录入金额超过10000万元时，则系统非阻断性提
     *
     * @param annualIncome
     * @return
     */
    public static String checkAnnualIncome(String annualIncome) {
        if (!annualIncome.matches("(^[1-9]\\d{0,3}$)|(^0\\.\\d{2}$)|(^[1-9]\\d{0,3}\\.\\d{2}$)")) {
            return "本次投保提交的投保人（或被保险人）年收入为" + annualIncome + "万元，请与客户再次核实并确认信息是否正确";
        }
        return "";
    }

    /**
     * 校验银行账户
     * 当用户银行账号信息不符合录入规则时（银行账号需为12-19位数字），则系统提示“银行账号信息有误，请重新确认”
     *
     * @param bankAccount
     * @return
     */
    public static String checkBankAccount(String bankAccount) {
        if (!bankAccount.matches("^[1-9]\\d{9,29}$")) {
            return "银行账号信息有误，请重新确认";
        }
        return "";

    }

    /**
     * 校验投保人与被保险人关系
     * <p>
     * 需要 性别 sex  生日birthday 关系relation
     * <p>
     * 00-本人时，系统需判断投保人身份信息(五要素)与被保险人身份信息（五要素）是否一致。如否，则系统阻断性提示“投/被保险人关系信息异常，请重新确认”
     * 01-父母时，系统需判断投保人年龄是否大于被保险人年龄。如否，“投/被保险人关系信息存疑，需要进一步核实（注：检测异常点为关系与年龄不匹配，有录入错误等风险）
     * 02-配偶时，系统需判断投保人与被保险人性别是否相同。如相同，则系统阻断性提示“投/被保险人关系信息异常，请重新确认”
     * 02-配偶时，系统需判断投保人或被保险人年龄是否小于18周岁。如是，则系统阻断性提示“投/被保险人关系信息异常，请重新确认”
     * 03-子女时，系统需判断投保人年龄是否小于被保险人年龄。如否，则系统阻断性提示“投/被保险人关系信息异常，请重新确认”
     *
     * @param relationship
     * @return
     */
    public static String checkRelationship(Map<String, String> relationship) {
        log.info("校验投保人与被保险人关系:{}", relationship);
        String user = relationship.get("user");
        String family = relationship.get("family");
        String userSex = relationship.get("userSex");
        String familySex = relationship.get("familySex");
        String userID = relationship.get("userID");
        String familyID = relationship.get("familyID");
        String userBirthday = relationship.get("userBirthday");
        String familyBirthday = relationship.get("familyBirthday");
        String relation = relationship.get("relation");

        //获取年龄
        int userAge = checkStaffAge(userBirthday);
        int familyAge = checkStaffAge(familyBirthday);

        switch (relation) {
            //本人
            case "0":
                /*if (!user.equals(family) && !userID.equals(familyID)  && !userSex.equals(familySex) && userBirthday.equals(familyBirthday) && userAge !=familyAge){
                    return "投&被保险人关系信息异常，请重新确认";
                }*/
                break;
            //父母
            case "1":
                if (familyAge < userAge || familyAge < 18) {
                    return "投&被保险人关系信息异常，请重新确认";
                }
                if ((familyAge - userAge) <= 0 || (familyAge - userAge) >= 60 || (familyAge - userAge) < 18) {
                    return "投&被保险人关系信息存疑，需要进一步核实（注：检测异常点为关系与年龄不匹配，有录入错误等风险）";
                }
                break;
            //配偶
            case "2":
                if (userSex.equals(familySex) || familyAge < 18) {
                    return "投&被保险人关系信息异常，请重新确认";
                }
                break;
            //子女
            case "3":
                if (familyAge > userAge) {
                    return "投&被保险人关系信息异常，请重新确认";
                }
                if ((userAge - familyAge) < 18) {
                    return "投&被保险人关系信息存疑，需要进一步核实（注：检测异常点为关系与年龄不匹配，有录入错误等风险）";
                }
                break;
            default:
                return "关系类型不存在！";
        }

        return "";
    }

    /**
     * 校验身故受益人与被保险人关系
     *
     * @param deathBeneficiary
     * @return
     */
    public static String checkDeathBeneficiary(Map<String, String> deathBeneficiary) {
        String user = deathBeneficiary.get("user");
        String family = deathBeneficiary.get("family");
        String userSex = deathBeneficiary.get("userSex");
        String familySex = deathBeneficiary.get("familySex");
        String userID = deathBeneficiary.get("userID");
        String familyID = deathBeneficiary.get("familyID");
        String userBirthday = deathBeneficiary.get("userBirthday");
        String familyBirthday = deathBeneficiary.get("familyBirthday");
        String relation = deathBeneficiary.get("relation");

        //获取年龄
        int userAge = checkStaffAge(userBirthday);
        int familyAge = checkStaffAge(familyBirthday);

        switch (relation) {
            //本人
            case "0":
                if (!user.equals(family) && !userID.equals(familyID) && !userSex.equals(familySex) && userBirthday.equals(familyBirthday) && userAge != familyAge) {
                    return "投&被保险人关系信息异常，请重新确认";
                }
                break;
            //父母
            case "1":
                if (familyAge < userAge) {
                    return "投&被保险人关系信息异常，请重新确认";
                }
                if ((familyAge - userAge) <= 0 || (familyAge - userAge) >= 60) {
                    return "“投&被保险人关系信息存疑，需要进一步核实（注：检测异常点为关系与年龄不匹配，有录入错误等风险）";
                }
                break;
            //配偶
            case "2":
                if (familyAge <= 18 || (familyAge - userAge) >= 60) {
                    return "投&被保险人关系信息存疑，需要进一步核实（注：检测异常点为关系与年龄不匹配，有录入错误等风险）";
                }
                if (userSex.equals(familySex)) {
                    return "投&被保险人关系信息异常，请重新确认";
                }
                break;
            //子女
            case "3":
                if ((familyAge - userAge) < 18 || (familyAge - userAge) >= 60) {
                    return "投&被保险人关系信息存疑，需要进一步核实（注：检测异常点为关系与年龄不匹配，有录入错误等风险）";
                }
                break;
            default:
                return "关系类型不存在！";
        }

        return "";
    }

    private static int calculateAge(String dateOfBirth) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate birthDate = LocalDate.parse(dateOfBirth, formatter);
        LocalDate currentDate = LocalDate.now();
        Period period = Period.between(birthDate, currentDate);
        return period.getYears();
    }

    public static String newCheck(String sign, String nativeplace, String idType, String idno, String birthDay, String gender) {
        int age = calculateAge(birthDay);
        return checkNativeplaceByIdTypeandIdNoandAge(sign, nativeplace, idType, idno, age, birthDay, gender);
    }

}
