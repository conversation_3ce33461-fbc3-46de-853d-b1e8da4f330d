package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcGrpContact;
import com.sinosoft.eflex.model.HrRegist;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FcGrpContactMapper {

    int deleteByPrimaryKey(String contactNo);

    int insert(FcGrpContact record);

    int insertSelective(FcGrpContact record);

    FcGrpContact selectByPrimaryKey(String contactNo);

    int updateByPrimaryKeySelective(FcGrpContact record);

    int updateByPrimaryKey(FcGrpContact record);


    int checkIdNoIsExists(Map<String, String> params);

    int isExist(Map<String, String> map);

    int selectIDFCHrRegistTemp(HrRegist hrRegist);

    int selectIDFCHrRegistTemp1(HrRegist hrRegist);

    List<HrRegist> selectFCHrRegistTempOldInfo(HrRegist hrRegist);

    List<HrRegist> selectFCHrRegistTempOldInfo3(HrRegist hrRegist);

    int selectGrpFCHrRegistTemp(HrRegist hrRegist);

    HrRegist selectAllFCHrRegistTemp(String registSN);

    int selectCountFCHrRegistTemp(String idNo);

    FcGrpContact selectGrpInfo(String grpNo);

    FcGrpContact selectGrpContact(String idNo);

    List<FcGrpContact> selectContacts(Map<String, Object> params);

    int selectContactCount(Map<String, Object> params);

    List<FcGrpContact> selectContactsInfo(Map<String, Object> params);

    List<HrRegist> selectContactsTemp(Map<String, Object> params);


    int updateByshareholders(HrRegist hrRegist);

    int updateHRInfoByIdno(HrRegist hrRegist);

    int updateByRegistSN(HrRegist hrRegist);

    int updateHRinfo(HrRegist hrRegist);

    //查询联系人的数量
    int getGrpContactCount(String IDNo);

    String getmobileByUnifiedsociCode(String unifiedsociCode);

    Map<String, String> selectContactByNo(@Param("grpNo") String grpNo, @Param("contactNo") String contactNo);

    /**
     * 查询企业联系人信息，多条则代表垃圾数据，错误逻辑  2020.11.18
     *
     * @param fcGrpContact
     * @return
     */
    FcGrpContact selectGrpContactInfo(FcGrpContact fcGrpContact);

    FcGrpContact selectGrpContactInfo1(String contactNo);

    void updateByIdNo(Map<String, Object> params);

    FcGrpContact selectGrpContactByIdNoAndGrpNo(@Param("idNo") String idNo, @Param("grpNo") String grpNo);

    List<FcGrpContact> selectGrpContactList(@Param("idNo") String idNo);

    void deleteByContactNo(@Param("idNo") String idNo, @Param("contactNo") String contactNo);

    List<FcGrpContact> selectFcGrpContactImageNull();
}