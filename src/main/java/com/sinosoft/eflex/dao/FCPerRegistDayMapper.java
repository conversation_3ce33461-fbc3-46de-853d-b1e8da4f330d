package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCPerRegistDay;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FCPerRegistDayMapper {
    int deleteByPrimaryKey(String registDayNo);

    int insert(FCPerRegistDay record);

    int insertSelective(FCPerRegistDay record);

    FCPerRegistDay selectByPrimaryKey(String registDayNo);

    int updateByPrimaryKeySelective(FCPerRegistDay record);

    int updateByPrimaryKey(FCPerRegistDay record);

    List<FCPerRegistDay> selectFCPerRegistDayList(Map<String, Object> params);

    FCPerRegistDay selectFCPerRegistDayByKey(Map<String, Object> params);

    FCPerRegistDay selectFCPerRegistDayByKey1(FCPerRegistDay fcPerRegistDay);

    Integer selectStaCount(String ensureCode);

    // 逻辑主键删除
    //int deleteByParams(Map<String, String> param);
    int deleteByParams(@Param("ensureCode")String ensureCode, @Param("perNo")String perNo);

    //查询开放期内最新的一条的数据
    String findNewensureCode(Map<String, Object> map);

    List<Map<String,String>> getEnsureByIdNo(String IDNo);

    List<FCPerRegistDay> getPerRegistDayByPersonID(String personID);

    int updateCloseDayByEnsureCode(FCPerRegistDay record);

    /**
     * 根据个人客户号查询员工职级
     * @param perNo
     * @return
     */
    String selectLevelCodeByPerNo(@Param("perNo") String perNo,@Param("ensureCode") String ensureCode);

    String selectEnsureCodeByPerNo(String PerNo);

    void updateByPerNo(Map<String, Object> paraMap);

    FCPerRegistDay selectByEnsureCode(@Param("ensureCode")String ensureCode, @Param("perNo")String customNo);
}