package com.sinosoft.eflex.dao;

import java.util.List;
import java.util.Map;

import com.sinosoft.eflex.model.insurePlanPage.PersonInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCPerInfo;
import com.sinosoft.eflex.model.FCPerson;
import com.sinosoft.eflex.model.FCStaffFamilyRela;

@Repository
public interface EmpAndFamilyMapper {
    /**
     * <AUTHOR>
     * @description 查询员工信息及家属信息
     * @date 15:57 15:57
     * @modified
     */
    List<FCStaffFamilyRela> empAndFamilySelect(String perNo);

    List<FCStaffFamilyRela> findFamilyName(@Param("personId") String pesonId);

    List<Map<String, Object>> selectOrderInsuredInfo(@Param("personId") String peronId);

    List<FCPerson> selectFamily(@Param("perNo") String perNo, @Param("personId") String peronId);

    List<FCPerson> selectFamilyByIsManual(Map<String, String> map);

    /**
     * 查询需要投保的学生人员信息 --add by wudezhong 2021.4.12 移动端优化
     * 
     * @param map
     * @return
     */
    List<FCPerson> selectFamilyByIsManual1(Map<String, Object> map);

    List<Map<String, Object>> selectIsPlan(@Param("ensureCode") String ensureCode, @Param("personId") String personId);

    List<Map<String, Object>> selectIsEflexPlan(@Param("ensureCode") String ensureCode, @Param("perNo") String perNo, @Param("personId") String personId);

    /**
     * 根据员工perNo查询不同企业下所有人的IDNO
     *
     * @param perNo
     * @return
     */
    List<String> selectFamilyIDNO(String perNo);

    /**
     * 根据员工perNo查询不同企业下所有人的idno,不包括本人
     *
     * @param perNo
     * @return
     */
    List<String> selectFamilyIdNo(String perNo);

    /**
     * 根据IDNO查询员工下所属最新家属的personid
     *
     * @param map
     * @return
     */
    String selectNewFamilyPersonid(Map<String, String> map);
    
    String selectNewFamilyPersonidEflex(Map<String, String> map);

    /**
     * 查询最新的员工以及家属数据
     *
     * @param map
     * @return
     */
    List<FCPerson> selectAllFamilyInfo(Map<String,Object> map);

    /**
     * 查询单个最新的员工或者家属数据
     *
     * @param map
     * @return
     */
    FCPerson selectSingleFamilyInfo(Map<String, Object> map);

    /**
     * 修改\删除
     * 查询同一员工下的证件号相同的Personid
     *
     * @param map
     * @return
     */
    List<String> selectSameStaffPersonid(Map<String, String> map);

    List<Map<String,String>> selectSameStaffPersonidPerNo(Map<String, String> map);


    /**
     * 查询唯一一个员工下的家属对应的personid
     *
     * @param map
     * @return
     */
    String selectSingleSameStaffPersonid(Map<String, String> map);

    /**
     * 查询是否为监护人:0是监护人
     *
     * @param perNo
     * @return
     */
    Integer checkPerType(String perNo);

    Integer checkPerTypeByIDNO(String IdNo);

    /**
     * 查询监护人的信息
     *
     * @param perNo
     * @return
     */
    FCPerInfo selectGuardianInfo(String perNo);

    /**
     * 根据perNo信息查询除了员工身份的personid
     *
     * @param perNo
     * @return
     */
    List<String> selectStaffPersonId(String perNo);

    //校验投保个人信息,成功则返回true，失败返回个人信息要求客户修改
    @Select("select name,sex,birthDate,mobilePhone,nativeplace," +
//            "(select codename from fdcode where codetype = 'nativeplace' and codekey = nativeplace) as nativeplaceName," +
            "iDType,iDNo,idTypeEndDate,occupationCode,JoinMedProtect,openbank,openaccount,email,codename AS occupationName," +
            "(select relation from fcstafffamilyrela where fcperson.personid = personid) as relation" +
            " from fcperson,fdcode where personid = #{personid} AND occupationCode = codekey AND codetype = 'OccupationDetail'")
    Map getByPersonid(String personid);

    /**
     * 获取人员信息
     */
    PersonInfo selectPersonInfo(String personId);

}
