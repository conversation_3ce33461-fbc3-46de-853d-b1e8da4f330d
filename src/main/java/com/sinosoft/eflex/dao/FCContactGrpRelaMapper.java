package com.sinosoft.eflex.dao;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCContactGrpRela;
import com.sinosoft.eflex.model.FCContactGrpRelaKey;

@Repository
public interface FCContactGrpRelaMapper {
    int deleteByPrimaryKey(FCContactGrpRelaKey key);

    int deleteByGrpNo(String grpNo);

    int insert(FCContactGrpRela record);

    int insertSelective(FCContactGrpRela record);

    FCContactGrpRela selectByPrimaryKey(FCContactGrpRelaKey key);

    int updateByPrimaryKeySelective(FCContactGrpRela record);

    int updateByPrimaryKey(FCContactGrpRela record);

    int checkHrLockState(String idNo);

    String selectContactNoByGrpNoandcontactType(String grpNo);

    List<FCContactGrpRela> selectByGrpNo(String grpNo);

    List<FCContactGrpRela> seletcContactListByMap(Map<String, String> map);
}