package com.sinosoft.eflex.dao;


import com.sinosoft.eflex.model.FCPerInfo;
import com.sinosoft.eflex.model.FdUser;
import com.sinosoft.eflex.model.GlobalInput;
import com.sinosoft.eflex.model.configmanage.AuditUserInfo;
import com.sinosoft.eflex.model.configmanage.SelectAuditUserIsExistReq;
import com.sinosoft.eflex.model.configmanage.SelectAuditUserPhoneIsExistReq;
import com.sinosoft.eflex.model.configmanage.SelectAuditUserReq;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FdUserMapper {
    int deleteByPrimaryKey(String userNo);

    int insert(FdUser record);

    int insertSelective(FdUser record);

    FdUser selectByPrimaryKey(String userNo);

    int updateByPrimaryKeySelective(FdUser record);

    int updateByPrimaryKey(FdUser record);
    
    List<FdUser> findUsersByUsername(String username);
    
    FdUser findUserForLogin(@Param("username") String username, @Param("userType") String userType);

    int updateUserState(FdUser fdUser);
    
    //查询员工个人用户信息
    GlobalInput findGlobalInfoByUserNo(String userNo);
    
    //查询企业HR用户信息
    GlobalInput findGlobalInfoForGrp(String userNo);
    
    //查询管理员用户信息
    GlobalInput findGlobalInfoForAdmin(String userNo);

    List<FdUser> findUserByCustNo(String custNo);
    
    //根据五要素查询用户信息
    FdUser findUserByFiveElement(Map<String, Object> userMap);

    FdUser isExistByUser(@Param("idNo") String idNo,@Param("phone") String phone);
    
    /**
     * 找回密码
     * @param idNo
     * @param phone
     * @param customType
     * @return
     */
    FdUser findUserInfo(@Param("idNo") String idNo,@Param("phone") String phone, @Param("customType") String customType);
    
    List<FdUser> findUserInfoByPhone(@Param("phone") String phone, @Param("customType") String customType);

    FdUser selectCustomNoByType(Map<String,Object> map);
    
    FdUser findUserByThreeElement(FCPerInfo fcperinfo);
    
    FdUser findUserByIdno(String idNo);

    FdUser selectByPwd(String IDNo);

    int updataPwd(FdUser fdUser);

    //手机短信验证码登录用
    @Select("select * from fduser where phone = #{phone} and customtype = #{customtype} and idno != #{idno}")
    List<FdUser> selectByPhone(@Param("phone") String phone,@Param("customtype") String customtype,
                         @Param("idno") String idno);

    List<FdUser> selectAuditUserPhone(@Param("phone") String phone,@Param("userType") String userType);

    //手机短信验证码登录用
    @Select("select * from fduser where phone = #{phone} and customtype = #{customtype} order by CustomNo desc")
    List<FdUser> checkPhone(@Param("phone") String phone,@Param("customtype") String customtype);

    @Select("select count(1) from fduser where phone = #{phone} and idno != #{idno} and customtype = #{customtype}")
    int checkByPhone(@Param("phone") String phone,@Param("idno") String idno,@Param("customtype") String customtype);

    /**
     * 根据证件号查询所有登录用户
     * @param idNo
     * @return
     */
    List<FdUser> selectByIdNo(String idNo);

    /**
     * 根据客户号和证件号查询登录用户
     * @param perNo
     * @param idNo
     * @return
     */
    FdUser selectByPerNoAndIdNo(@Param("perNo") String perNo, @Param("idNo") String idNo);

    /**
     * 查询审核岗的用户账号的是否已经存在
     * 
     * @param selectAuditUserIsExistReq
     * @return
     */
    int selectAuditUserIsExist(SelectAuditUserIsExistReq selectAuditUserIsExistReq);

    /**
     * 查询审核岗的用户手机号的是否已经注册
     *
     * @param selectAuditUserPhoneIsExistReq
     * @return
     */
    int selectAuditUserPhoneIsExist(SelectAuditUserPhoneIsExistReq selectAuditUserPhoneIsExistReq);

    /**
     * 查询审核岗用户列表
     * @param selectAuditUserReq
     * @return
     */
    List<AuditUserInfo> selectAuditUserList(SelectAuditUserReq selectAuditUserReq);

    /**
     * 查询单个审核用户信息
     * @param userNo
     * @return
     */
    AuditUserInfo selectSingleAuditUserInfo(String userNo);


    void updateByIdNo(Map<String, Object> params);

    Integer selectCountByIdNo(@Param("idNo")String idNo);

    void updateBatchById(@Param("list")List<FdUser> fdUserList);

    FdUser selectByIdNoAndType(@Param("idNo")String idNo,@Param("type") String type);
}