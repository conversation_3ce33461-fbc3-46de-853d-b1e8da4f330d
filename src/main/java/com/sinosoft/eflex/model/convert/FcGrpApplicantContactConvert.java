package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.model.FCGrpApplicant;
import com.sinosoft.eflex.model.FCGrpApplicantContact;
import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.FcGrpContact;
import com.sinosoft.eflex.util.CommonUtil;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 */
public class FcGrpApplicantContactConvert {

    public static FCGrpApplicantContact convert(FcGrpContact fcGrpContact, String serialNo, String grpAppNo, String grpNo, String userNo) {
        FCGrpApplicantContact fcGrpApplicantContact = new FCGrpApplicantContact();
        fcGrpApplicantContact.setSerialNo(serialNo);
        fcGrpApplicantContact.setGrpAppNo(grpAppNo);
        fcGrpApplicantContact.setGrpNo(grpNo);
        fcGrpApplicantContact.setName(fcGrpContact.getName());
        fcGrpApplicantContact.setNativeplace(fcGrpContact.getNativeplace());
        fcGrpApplicantContact.setIdType(fcGrpContact.getIdType());
        fcGrpApplicantContact.setIdNo(fcGrpContact.getIdNo());
        fcGrpApplicantContact.setIdTypeStartDate(fcGrpContact.getIdTypeStartDate());
        fcGrpApplicantContact.setIdTypeEndDate(fcGrpContact.getIdTypeEndDate());
        fcGrpApplicantContact.setSex(fcGrpContact.getSex());
        fcGrpApplicantContact.setBirthDay(fcGrpContact.getBirthDay());
        fcGrpApplicantContact.setMobilePhone(fcGrpContact.getMobilePhone());
        fcGrpApplicantContact.setEmail(fcGrpContact.getEmail());
        fcGrpApplicantContact.setDepartment(fcGrpContact.getDepartment());
        fcGrpApplicantContact.setIdImage1(fcGrpContact.getIdImage1());
        fcGrpApplicantContact.setIdImage2(fcGrpContact.getIdImage2());
        fcGrpApplicantContact.setOperator(userNo);
        CommonUtil.initObject(fcGrpApplicantContact, "INSERT");
        return fcGrpApplicantContact;
    }
}
