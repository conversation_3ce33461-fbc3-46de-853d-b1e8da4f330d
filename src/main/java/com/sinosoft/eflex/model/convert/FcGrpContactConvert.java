package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.model.FcGrpContact;
import com.sinosoft.eflex.model.request.GrpContactInsertReq;
import com.sinosoft.eflex.util.CommonUtil;

/**
 * 转换*
 *
 * <AUTHOR> *
 */
public class FcGrpContactConvert {

    public static FcGrpContact convert(GrpContactInsertReq req, String userNo, String contactNo) {
        FcGrpContact fcGrpContact = new FcGrpContact();
        fcGrpContact.setContactNo(contactNo);
        fcGrpContact.setContactType("01");
        fcGrpContact.setGrpNo(req.getGrpNo());
        fcGrpContact.setName(req.getName());
        fcGrpContact.setSex(req.getSex());
        fcGrpContact.setNativeplace(req.getNativeplace());
        fcGrpContact.setIdType(req.getIdType());
        fcGrpContact.setIdNo(req.getIdNo());
        fcGrpContact.setIdTypeStartDate(req.getIdTypeStartDate());
        fcGrpContact.setIdTypeEndDate(req.getIdTypeEndDate());
        fcGrpContact.setMobilePhone(req.getMobilePhone());
        fcGrpContact.setBirthDay(req.getBirthDay());
        fcGrpContact.setEmail(req.getEmail());
        fcGrpContact.setIdImage1(req.getIdImage1());
        fcGrpContact.setIdImage2(req.getIdImage2());
        fcGrpContact.setOperator(userNo);
        return CommonUtil.initObject(fcGrpContact, "INSERT");
    }
}
