package com.sinosoft.eflex.model.dailyplan.convert;

import com.sinosoft.eflex.model.FcEnsureContact;
import com.sinosoft.eflex.model.dailyplan.DailyContacts;

/**
 * 转换*
 *
 * <AUTHOR>
 */
public class DailyContactsConvert {

    public static DailyContacts convert(FcEnsureContact fcEnsureContact) {
        DailyContacts dailyContacts = new DailyContacts();
//        dailyContacts.setPhone();
        dailyContacts.setLinkMan(fcEnsureContact.getName());
        dailyContacts.setGender1(fcEnsureContact.getSex());
        dailyContacts.setBirthday1(fcEnsureContact.getBirthDay());
        dailyContacts.setNationality1(fcEnsureContact.getNativeplace());
        dailyContacts.setInsContIDType(fcEnsureContact.getIdType());
        dailyContacts.setInsContIDNo(fcEnsureContact.getIdNo());
        dailyContacts.setInsContIDPeriodOfValidityType(fcEnsureContact.getIdTypeEndDate());
        dailyContacts.setInsContIDStartPeriod(fcEnsureContact.getIdTypeStartDate());
        dailyContacts.setEmail(fcEnsureContact.getEmail());
        dailyContacts.setMobile(fcEnsureContact.getMobilePhone());
        dailyContacts.setDepartment(fcEnsureContact.getDepartment());
        return dailyContacts;
    }
}
