package com.sinosoft.eflex.model.dailyplan.convert;

import com.sinosoft.eflex.model.FCOrderInsured;
import com.sinosoft.eflex.model.FCOrderItem;
import com.sinosoft.eflex.model.FCPerson;
import com.sinosoft.eflex.model.FDCode;
import com.sinosoft.eflex.model.dailyplan.DailyInsuredInfo;
import com.sinosoft.eflex.model.dailyplan.DailyRiskInfo;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 转换*
 *
 * <AUTHOR> *
 */
public class DailyInsuredInfoConvert {

    public static DailyInsuredInfo convert(FCOrderInsured fcOrderInsured, List<DailyRiskInfo> riskList, Map<String, String> signBankInfoMap, FCPerson fcPerson,
                                           String address, FDCode fdCode, FCOrderItem fcOrderItem, String payType) {
        DailyInsuredInfo dailyInsuredInfo = new DailyInsuredInfo();
        dailyInsuredInfo.setName(fcOrderInsured.getName());
        dailyInsuredInfo.setSex(fcOrderInsured.getSex());
        dailyInsuredInfo.setBirthday(fcOrderInsured.getBirthday());
        dailyInsuredInfo.setIdType(fcOrderInsured.getIDType());
        dailyInsuredInfo.setIdNo(fcOrderInsured.getIDNo());
        dailyInsuredInfo.setContNo(fcOrderItem.getContNo());
        dailyInsuredInfo.setMainRelation(fdCode.getCoreCode());
        dailyInsuredInfo.setOccupationCode(fcOrderInsured.getOccupationCode());
        dailyInsuredInfo.setOccupationType(fcOrderInsured.getOccupationType());
        dailyInsuredInfo.setNationality(fcPerson.getNativeplace());
        dailyInsuredInfo.setAddress(address);
        dailyInsuredInfo.setZipCode(fcOrderInsured.getZipCode());
        dailyInsuredInfo.setEmail(fcOrderInsured.getEMail());
        dailyInsuredInfo.setMobile(fcOrderInsured.getMobilePhone());
        // 默认否 险种计算保费涉及社保标志时必传 0-无 1-有
        if (StringUtils.isEmpty(fcOrderInsured.getJoinMedProtect())) {
            dailyInsuredInfo.setSocialInsuFlag("0");
        } else {
            dailyInsuredInfo.setSocialInsuFlag(fcOrderInsured.getJoinMedProtect());
        }
        // 传当前计划编码
        dailyInsuredInfo.setNewPayMode(payType);
        dailyInsuredInfo.setRiskList(riskList);
        dailyInsuredInfo.setIdExpDate(fcPerson.getIdTypeEndDate());
        if (signBankInfoMap != null) {
            dailyInsuredInfo.setAccBankCode(signBankInfoMap.get("PayBankCode"));
            dailyInsuredInfo.setAccName(signBankInfoMap.get("Name"));
            dailyInsuredInfo.setAccNo(signBankInfoMap.get("BankAccount"));
            dailyInsuredInfo.setAccBankProvince("");
        }
//        dailyInsuredInfo.setBnfList();
//        dailyInsuredInfo.setInsuredImpartList();
//        dailyInsuredInfo.setStature();
//        dailyInsuredInfo.setWeight();
//        dailyInsuredInfo.setJoinCompanyDate();
//        dailyInsuredInfo.setSalary();
//        dailyInsuredInfo.setContPlanCode();
        return dailyInsuredInfo;
    }


}
