package com.sinosoft.eflex.model.AddressEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IdCardVerifyRequest implements Serializable {

    private String transSource;
    private String transCode;
    private String transTime;
    private String transNo;
    private String transSN;
    private List<Verify> verifies;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Verify implements Serializable{
        private String transCode;
        private String channelSource;
        private String actionType;
        private String name;
        private String idCard;
        private String idNo;
        private String idType;

    }
}