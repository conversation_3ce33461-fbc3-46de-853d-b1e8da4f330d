package com.sinosoft.eflex.model.AddressEntity.convert;

import com.google.common.collect.Lists;
import com.sinosoft.eflex.enums.CoreIdType;
import com.sinosoft.eflex.model.AddressEntity.IdCardVerifyRequest;
import com.sinosoft.eflex.util.DateTimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 转换*
 *
 * <AUTHOR>
 */
public class IdCardVerifyConvert {

    public static List<IdCardVerifyRequest.Verify> convert(List<Map<String, String>> maps) {
        if (CollectionUtils.isEmpty(maps)) {
            return Lists.newArrayList();
        }
        List<IdCardVerifyRequest.Verify> verifyList = new ArrayList<>();
        for (Map<String, String> map : maps) {
            IdCardVerifyRequest.Verify verify = new IdCardVerifyRequest.Verify();
            verify.setName(map.get("name"));
            verify.setIdNo(map.get("iDNo"));
            verify.setIdType(CoreIdType.getNameByCoreId(map.get("iDType")).name());
            verifyList.add(verify);
        }
        return verifyList;
    }


    public static List<IdCardVerifyRequest.Verify> convert(String name, String idNo, String idType) {
        List<IdCardVerifyRequest.Verify> verifyList = new ArrayList<>();
        IdCardVerifyRequest.Verify verify = new IdCardVerifyRequest.Verify();
        verify.setName(name);
        verify.setIdNo(idNo);
        verify.setIdType(CoreIdType.getNameByCoreId(idType).name());
        verifyList.add(verify);
        return verifyList;
    }


    public static IdCardVerifyRequest convertReq(List<IdCardVerifyRequest.Verify> verifyList) {
        if (CollectionUtils.isEmpty(verifyList)) {
            return new IdCardVerifyRequest();
        }
        for (IdCardVerifyRequest.Verify verify : verifyList) {
            verify.setChannelSource("7");
            verify.setActionType("IDCARD2");
            verify.setTransCode("IS_FACEID_001");
            if (StringUtils.isEmpty(verify.getIdCard())) {
                verify.setIdCard(verify.getIdNo());
            }
            verify.setIdNo(null);
            verify.setIdType(null);
        }
        IdCardVerifyRequest request = new IdCardVerifyRequest();
        request.setTransCode("ISHARE-INTELLIGENCE-VERIFY-IDCARD-BATCH");
        request.setTransNo(String.valueOf(System.currentTimeMillis()));
        request.setTransSN(String.valueOf(System.currentTimeMillis()));
        request.setTransSource("YF");
        request.setTransTime(DateTimeUtil.getCurrentDateTime());
        request.setVerifies(verifyList);
        return request;
    }


}
