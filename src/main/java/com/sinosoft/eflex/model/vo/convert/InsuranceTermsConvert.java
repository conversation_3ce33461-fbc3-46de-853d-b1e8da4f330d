package com.sinosoft.eflex.model.vo.convert;

import com.google.common.collect.Lists;
import com.sinosoft.eflex.model.FCPlanRisk;
import com.sinosoft.eflex.model.FDRiskInfo;
import com.sinosoft.eflex.model.vo.InsuranceTermsVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 转换
 *
 * <AUTHOR> * *
 */
public class InsuranceTermsConvert {

    public static List<InsuranceTermsVO> convert(List<FCPlanRisk> fcPlanRiskList, List<FDRiskInfo> fdRiskInfos) {
        if (CollectionUtils.isEmpty(fcPlanRiskList)) {
            return Lists.newArrayList();
        }

        // 去重
        List<FCPlanRisk> collect = fcPlanRiskList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(FCPlanRisk::getRiskCode))), ArrayList::new));

        Map<String, FDRiskInfo> riskMap = fdRiskInfos.stream().collect(Collectors.toMap(FDRiskInfo::getRiskCode, Function.identity()));
        List<InsuranceTermsVO> insuranceTermsList = Lists.newArrayList();
        for (FCPlanRisk fcPlanRisk : collect) {
            FDRiskInfo fdRiskInfo = riskMap.get(fcPlanRisk.getRiskCode());
            InsuranceTermsVO insuranceTermsVO = new InsuranceTermsVO();
            insuranceTermsVO.setRiskCode(fcPlanRisk.getRiskCode());
            insuranceTermsVO.setRiskName(fdRiskInfo.getRiskName());
            insuranceTermsVO.setProductDescription(fdRiskInfo.getProductDescription());
            insuranceTermsVO.setInsuranceTerms(fdRiskInfo.getInsuranceTerms());
            insuranceTermsVO.setProductDescriptionName(fdRiskInfo.getProductDescriptionName());
            insuranceTermsVO.setInsuranceTermsName(fdRiskInfo.getInsuranceTermsName());
            insuranceTermsVO.setPdfUrl(fdRiskInfo.getPdfUrl());
            insuranceTermsVO.setProtocolReading(fcPlanRisk.getProtocolReading());
            insuranceTermsList.add(insuranceTermsVO);
        }
        return insuranceTermsList;
    }
}
