package com.sinosoft.eflex.model.HomePageInfo.convert;

import com.google.common.collect.Lists;
import com.sinosoft.eflex.dao.FCEnsureMapper;
import com.sinosoft.eflex.model.FCPerInfo;
import com.sinosoft.eflex.model.FCPerson;
import com.sinosoft.eflex.model.HomePageInfo.PerFamilyInfo;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.IDCardUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 公司信息*
 *
 * <AUTHOR>
 */
@Data
public class PerFamilyInfoConvert {


    public static List<PerFamilyInfo> convert(FCPerInfo fcperinfo, List<FCPerson> familyList) {
        if (CollectionUtils.isEmpty(familyList)) {
            return Lists.newArrayList();
        }
        List<PerFamilyInfo> perFamilyInfoList = new ArrayList<>();
        for (FCPerson fcPerson : familyList) {
            if (fcPerson.getRelation().equals("0") && !fcPerson.getIDNo().equals(fcperinfo.getIDNo())) {
                continue;
            }
            if (!fcPerson.getRelation().equals("0") && fcPerson.getIDNo().equals(fcperinfo.getIDNo())) {
                continue;
            }
            if (!fcperinfo.getIDNo().equals(fcPerson.getPerIDNo())) {
                continue;
            }
            PerFamilyInfo perFamilyInfo = new PerFamilyInfo();
            perFamilyInfo.setPersonId(fcPerson.getPersonID());
            perFamilyInfo.setName(fcPerson.getName());
            perFamilyInfo.setRelation(fcPerson.getRelation());
            perFamilyInfo.setRelationProve(fcPerson.getRelationProve());
            perFamilyInfo.setRelationName(fcPerson.getRelationName());
            perFamilyInfo.setBirthDate(fcPerson.getBirthDate());
            int age = IDCardUtil.calculateAge(fcPerson.getBirthDate(), "yyyy-MM-dd");
            perFamilyInfo.setAge(String.valueOf(age));
            perFamilyInfo.setSex(fcPerson.getSex());
            perFamilyInfo.setIdNo(fcPerson.getIDNo());
            perFamilyInfo.setIdType(fcPerson.getIDType());
            perFamilyInfo.setNativePlace(fcPerson.getNativeplace());
            perFamilyInfo.setNativePlaceName(fcPerson.getNativeplaceName());
            perFamilyInfo.setIdTypeEndDate(fcPerson.getIdTypeEndDate());
            perFamilyInfo.setEmail(fcPerson.getEMail());
            perFamilyInfo.setMobilePhone(fcPerson.getMobilePhone());
            perFamilyInfo.setOccupationCode(fcPerson.getOccupationCode());
            perFamilyInfo.setOccupationName(fcPerson.getOccupationName());
            perFamilyInfo.setOccupationType(fcPerson.getOccupationType());
            perFamilyInfo.setOccupationTypeName(fcPerson.getOccupationTypeName());
            perFamilyInfo.setOpenBank(fcPerson.getOpenBank());
            perFamilyInfo.setOpenAccount(fcPerson.getOpenAccount());
            perFamilyInfo.setJoinMedProtect(fcPerson.getJoinMedProtect());
            perFamilyInfoList.add(perFamilyInfo);

        }
        // 关系 0优先
        perFamilyInfoList = perFamilyInfoList.stream().sorted(Comparator.comparing(PerFamilyInfo::getRelation)).collect(Collectors.toList());

        return perFamilyInfoList;
    }


}
