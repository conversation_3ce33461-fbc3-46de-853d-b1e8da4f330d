package com.sinosoft.eflex.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 接收application.yml中的myProps下面的属性
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "myProps")
public class MyProps {

    private Map<String, String> serviceInfo = new HashMap<>();
    private Map<String, String> coreServiceInfo = new HashMap<>();
    private Map<String, String> DSserviceInfo = new HashMap<>();
    private Map<String, String> Etsign = new HashMap<>();
    private Map<String, String> CASystem = new HashMap<>();

    // ocr识别------------ocrUrl
    @Value("${myProps.ocrMsg.ocrUrl}")
    private String ocrUrl;
    // ocr识别------------ocrTransCode
    @Value("${myProps.ocrMsg.ocrTransCode}")
    private String ocrTransCode;
    // ocr识别------------ocrChannelSource
    @Value("${myProps.ocrMsg.ocrChannelSource}")
    private String ocrChannelSource;
    // ocr识别------------ocrActionType
    @Value("${myProps.ocrMsg.ocrActionType}")
    private String ocrActionType;
    // ocr识别------------ocrAction
    @Value("${myProps.ocrMsg.ocrAction}")
    private String ocrAction;
    // ocr识别------------ocrApp_id
    @Value("${myProps.ocrMsg.ocrApp_id}")
    private String ocrApp_id;
    // ocr识别------------ocrApp_secret
    @Value("${myProps.ocrMsg.ocrApp_secret}")
    private String ocrApp_secret;
    //登陆有效期
    @Value("${myProps.TermValidity.loginValidity}")
    private String termValidityLogin;
    //验证码有效期
    @Value("${myProps.TermValidity.codeValidity}")
    private String termValidityCode;
    //签约申请接口地址
    @Value("${myProps.SignSererUrl.SignApplySererUrl}")
    private String signApplySererUrl;
    //签约确认接口地址
    @Value("${myProps.SignSererUrl.SignConfirmSererUrl}")
    private String signConfirmSererUrl;
    //签约接口系统代码
    @Value("${myProps.SignSererUrl.Sourse}")
    private String sourse;
    // 中台签约申请接口地址
    @Value("${myProps.BankSign.MiddlegroundUrl.SignApplyUrl}")
    private String signApplyUrl;
    // 中台签约申请确认接口地址
    @Value("${myProps.BankSign.MiddlegroundUrl.SignConfirmUrl}")
    private String signConfirmUrl;
    // 中台签约app_id
    @Value("${myProps.BankSign.MiddlegroundUrl.appId}")
    private String appId;
    // 中台签约app_secret
    @Value("${myProps.BankSign.MiddlegroundUrl.appSecret}")
    private String appSecret;
    //支付中心--系统来源
    @Value("${myProps.PayCenter.transSource}")
    private String transSource;
    //支付中心--获取支付页面接口交易编码
    @Value("${myProps.PayCenter.getPayUrltransCode}")
    private String getPayUrltransCode;
    //支付中心--支付接入类型
    @Value("${myProps.PayCenter.payKind}")
    private String payKind;
    //支付中心--渠道
    @Value("${myProps.PayCenter.channelCode}")
    private String channelCode;
    //支付中心--页面回调地址
    @Value("${myProps.PayCenter.pageBackUrl}")
    private String pageBackUrl;
    //支付中心--支付后台通知地址
    @Value("${myProps.PayCenter.dataBackUrl}")
    private String dataBackUrl;
    //支付中心--支付状态查询接口交易编码
    @Value("${myProps.PayCenter.payStatusQuery}")
    private String payStatusQuery;
    //支付中心--获取支付页面的接口地址
    @Value("${myProps.PayCenter.payUrl}")
    private String payUrl;
    //支付中心--app_id
    @Value("${myProps.PayCenter.app_id}")
    private String app_id;
    //支付中心--app_secret
    @Value("${myProps.PayCenter.app_secret}")
    private String app_secret;
    //支付中心--支付状态查询接口地址payQueryUrl
    @Value("${myProps.PayCenter.payQueryUrl}")
    private String payQueryUrl;
    //人脸识别--face_appId
    @Value("${myProps.faceDiscern.face_appId}")
    private String face_appId;
    //人脸识别--face_appSecret
    @Value("${myProps.faceDiscern.face_appSecret}")
    private String face_appSecret;
    //人脸识别--face_Url
    @Value("${myProps.faceDiscern.face_Url}")
    private String face_Url;
    //人脸识别--face_QueryUrl
    @Value("${myProps.faceDiscern.face_QueryUrl}")
    private String face_QueryUrl;
    //人脸识别--face_transCode
    @Value("${myProps.faceDiscern.face_transCode}")
    private String face_transCode;
    //人脸识别--face_channelSource
    @Value("${myProps.faceDiscern.face_channelSource}")
    private String face_channelSource;
    //人脸识别--face_actionType
    @Value("${myProps.faceDiscern.face_actionType}")
    private String face_actionType;
    //人脸识别--face_ruleId
    @Value("${myProps.faceDiscern.face_ruleId}")
    private String face_ruleId;
    //人脸识别--face_redirectUrl
    @Value("${myProps.faceDiscern.face_redirectUrl}")
    private String face_redirectUrl;
    //分享界面的人脸识别--face_redirectUrl
    @Value("${myProps.faceDiscern.face_redirectUrlShare}")
    private String face_redirectUrlShare;
    //微信分享--公众号appid
    @Value("${myProps.WeChatShareConfig.Share_AppId}")
    private String Share_AppId;
    //微信分享--公众号开发者秘钥
    @Value("${myProps.WeChatShareConfig.Share_AppSecret}")
    private String Share_AppSecret;
    //微信分享--
    @Value("${myProps.WeChatShareConfig.GetAccessToken_URL}")
    private String GetAccessToken_URL;
    //微信分享--
    @Value("${myProps.WeChatShareConfig.GetTicket_URL}")
    private String GetTicket_URL;

    //基础单预核保接口地址
    @Value("${myProps.coreServiceInfo.basicsPreCheckUrl}")
    private String basicsPreCheckUrl;
    //基础单预签单接口地址
    @Value("${myProps.coreServiceInfo.basicsSignUrl}")
    private String basicsSignUrl;
    //自然人预核保接口地址
    @Value("${myProps.coreServiceInfo.naturalPreCheckUrl}")
    private String naturalPreCheckUrl;
    //自然人核保接口地址
    @Value("${myProps.coreServiceInfo.naturalCheckUrl}")
    private String naturalCheckUrl;
    //自然人签单接口地址
    @Value("${myProps.coreServiceInfo.naturalSignUrl}")
    private String naturalSignUrl;
    //核心地址coreAppId
    @Value("${myProps.coreServiceInfo.coreAppId}")
    private String coreAppId;
    //核心地址coreAppSecret
    @Value("${myProps.coreServiceInfo.coreAppSecret}")
    private String coreAppSecret;
    
    
    //保单信息查询接口地址
    @Value("${myProps.PrintPlatform.guaranteeUrl}")
    private String guaranteeUrl;
    //电子投保单生成
    @Value("${myProps.GenerateImage.policyGenerateImageUrl}")
    private String policyGenerateImageUrl;
    //团体合同及个人凭证下载
    @Value("${myProps.GenerateImage.policyDataSearchUrl}")
    private String policyDataSearchUrl;
    //电子投保单签名地址
    @Value("${myProps.policySign.policySignAddress}")
    private String policySignAddress;
    @Value("${myProps.policySign.policySignBaseAddress}")
    private String policySignBaseAddress;

    //获取ftp文件访问路径
    @Value("${fileDisplay.path}")
    private String fileDisplayPath;
    //获取地址码校验
    @Value("${myProps.addressCheck}")
    private String addressCheck;
    //国籍校验
    @Value("${myProps.nationalityCheck}")
    private String nationalityCheck;

    //客户风险评估
    @Value("${myProps.customerAssessmentCheck}")
    private String customerAssessmentCheck;
    //客户风险等级
    @Value("${myProps.customerLevelCheck}")
    private String customerLevelCheck;

    //疑似重客校验
    @Value("${myProps.checkSameCustomer}")
    private String checkSameCustomer;

    //疑似重客校验
    @Value("${myProps.checkSameCustomerBatch}")
    private String checkSameCustomerBatch;

    //二要素校验
    @Value("${myProps.checkIdCard}")
    private String checkIdCard;

    @Value("${myProps.syncPolicyUpdate}")
    private String syncPolicyUpdate;

    //获取org地址
    @Value("${myProps.organization}")
    private String organization;
    //官微留资
    @Value("${myProps.lifeSaveData}")
    private String lifeSaveData;

    //ocr开关 open 开 off 关  open是放开的意思
    @Value("${myProps.ocrOff}")
    private String ocrOff;

    /**
     * 满天星短信平台设置
     */
    @Value("${myProps.sendMessage.url}")
    private String sendMessageUrl;
    @Value("${myProps.sendMessage.product}")
    private String sendMessageProduct;
    @Value("${myProps.sendMessage.application}")
    private String sendMessageApplication;
    @Value("${myProps.sendMessage.appKey}")
    private String sendMessageAppKey;

    /**
     * 阿里云
     *
     */
    @Value("${myProps.aliyun.oss.endpointOut}")
    private  String ALIYUN_OSS_ENDPOINT_OUT;
    @Value("${myProps.aliyun.oss.endpointIn}")
    private  String ALIYUN_OSS_ENDPOINT_IN;
    @Value("${myProps.aliyun.oss.accessKeyId}")
    private  String ALIYUN_OSS_ACCESSKEYID;
    @Value("${myProps.aliyun.oss.accessKeySecret}")
    private  String ALIYUN_OSS_ACCESSKEYSECRET;
    @Value("${myProps.aliyun.oss.bucketName}")
    private  String ALIYUN_OSS_BUCKETNAME;
    @Value("${myProps.aliyun.oss.catalogue}")
    private  String ALIYUN_OSS_CATALOGUE;

    public String getFace_redirectUrlShare() {
        return face_redirectUrlShare;
    }

    public void setFace_redirectUrlShare(String face_redirectUrlShare) {
        this.face_redirectUrlShare = face_redirectUrlShare;
    }

    public Map<String, String> getCASystem() {
        return CASystem;
    }

    public void setCASystem(Map<String, String> CASystem) {
        this.CASystem = CASystem;
    }

    public String getOcrUrl() {
        return ocrUrl;
    }

    public void setOcrUrl(String ocrUrl) {
        this.ocrUrl = ocrUrl;
    }

    public Map<String, String> getEtsign() {
        return Etsign;
    }

    public void setEtsign(Map<String, String> etsign) {
        Etsign = etsign;
    }

    public Map<String, String> getServiceInfo() {
        return serviceInfo;
    }

    public void setServiceInfo(Map<String, String> serviceInfo) {
        this.serviceInfo = serviceInfo;
    }

    public Map<String, String> getDSserviceInfo() {
        return DSserviceInfo;
    }

    public void setDSserviceInfo(Map<String, String> dSserviceInfo) {
        DSserviceInfo = dSserviceInfo;
    }

    public String getTermValidityLogin() {
        return termValidityLogin;
    }

    public void setTermValidityLogin(String termValidityLogin) {
        this.termValidityLogin = termValidityLogin;
    }

    public String getTermValidityCode() {
        return termValidityCode;
    }

    public void setTermValidityCode(String termValidityCode) {
        this.termValidityCode = termValidityCode;
    }

    public String getSignApplySererUrl() {
        return signApplySererUrl;
    }

    public void setSignApplySererUrl(String signApplySererUrl) {
        this.signApplySererUrl = signApplySererUrl;
    }

    public String getSignConfirmSererUrl() {
        return signConfirmSererUrl;
    }

    public void setSignConfirmSererUrl(String signConfirmSererUrl) {
        this.signConfirmSererUrl = signConfirmSererUrl;
    }

    public String getSourse() {
        return sourse;
    }

    public void setSourse(String sourse) {
        this.sourse = sourse;
    }
    public String getTransSource() {
        return transSource;
    }

    public void setTransSource(String transSource) {
        this.transSource = transSource;
    }

    public String getGetPayUrltransCode() {
        return getPayUrltransCode;
    }

    public void setGetPayUrltransCode(String getPayUrltransCode) {
        this.getPayUrltransCode = getPayUrltransCode;
    }

    public String getPayKind() {
        return payKind;
    }

    public void setPayKind(String payKind) {
        this.payKind = payKind;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getPageBackUrl() {
        return pageBackUrl;
    }

    public void setPageBackUrl(String pageBackUrl) {
        this.pageBackUrl = pageBackUrl;
    }

    public String getDataBackUrl() {
        return dataBackUrl;
    }

    public void setDataBackUrl(String dataBackUrl) {
        this.dataBackUrl = dataBackUrl;
    }

    public String getPayStatusQuery() {
        return payStatusQuery;
    }

    public void setPayStatusQuery(String payStatusQuery) {
        this.payStatusQuery = payStatusQuery;
    }

    public String getPayUrl() {
        return payUrl;
    }

    public void setPayUrl(String payUrl) {
        this.payUrl = payUrl;
    }

    public String getApp_id() {
        return app_id;
    }

    public void setApp_id(String app_id) {
        this.app_id = app_id;
    }

    public String getApp_secret() {
        return app_secret;
    }

    public void setApp_secret(String app_secret) {
        this.app_secret = app_secret;
    }

    public String getPayQueryUrl() {
        return payQueryUrl;
    }

    public void setPayQueryUrl(String payQueryUrl) {
        this.payQueryUrl = payQueryUrl;
    }

    public String getFace_appId() {
        return face_appId;
    }

    public void setFace_appId(String face_appId) {
        this.face_appId = face_appId;
    }

    public String getFace_appSecret() {
        return face_appSecret;
    }

    public void setFace_appSecret(String face_appSecret) {
        this.face_appSecret = face_appSecret;
    }

    public String getFace_QueryUrl() {
        return face_QueryUrl;
    }

    public void setFace_QueryUrl(String face_QueryUrl) {
        this.face_QueryUrl = face_QueryUrl;
    }

    public String getFace_transCode() {
        return face_transCode;
    }

    public void setFace_transCode(String face_transCode) {
        this.face_transCode = face_transCode;
    }

    public String getFace_channelSource() {
        return face_channelSource;
    }

    public void setFace_channelSource(String face_channelSource) {
        this.face_channelSource = face_channelSource;
    }

    public String getFace_actionType() {
        return face_actionType;
    }

    public void setFace_actionType(String face_actionType) {
        this.face_actionType = face_actionType;
    }

    public String getFace_ruleId() {
        return face_ruleId;
    }

    public void setFace_ruleId(String face_ruleId) {
        this.face_ruleId = face_ruleId;
    }

    public String getFace_redirectUrl() {
        return face_redirectUrl;
    }

    public void setFace_redirectUrl(String face_redirectUrl) {
        this.face_redirectUrl = face_redirectUrl;
    }

    public String getFace_Url() {
        return face_Url;
    }

    public void setFace_Url(String face_Url) {
        this.face_Url = face_Url;
    }
    public String getOcrTransCode() {
        return ocrTransCode;
    }

    public void setOcrTransCode(String ocrTransCode) {
        this.ocrTransCode = ocrTransCode;
    }

    public String getOcrChannelSource() {
        return ocrChannelSource;
    }

    public void setOcrChannelSource(String ocrChannelSource) {
        this.ocrChannelSource = ocrChannelSource;
    }

    public String getOcrActionType() {
        return ocrActionType;
    }

    public void setOcrActionType(String ocrActionType) {
        this.ocrActionType = ocrActionType;
    }

    public String getOcrAction() {
        return ocrAction;
    }

    public void setOcrAction(String ocrAction) {
        this.ocrAction = ocrAction;
    }

    public String getOcrApp_id() {
        return ocrApp_id;
    }

    public void setOcrApp_id(String ocrApp_id) {
        this.ocrApp_id = ocrApp_id;
    }

    public String getOcrApp_secret() {
        return ocrApp_secret;
    }

    public void setOcrApp_secret(String ocrApp_secret) {
        this.ocrApp_secret = ocrApp_secret;
    }

    public Map<String, String> getCoreServiceInfo() {
        return coreServiceInfo;
    }

    public void setCoreServiceInfo(Map<String, String> coreServiceInfo) {
        this.coreServiceInfo = coreServiceInfo;
    }

    public String getBasicsPreCheckUrl() {
        return basicsPreCheckUrl;
    }

    public void setBasicsPreCheckUrl(String basicsPreCheckUrl) {
        this.basicsPreCheckUrl = basicsPreCheckUrl;
    }

    public String getBasicsSignUrl() {
        return basicsSignUrl;
    }

    public void setBasicsSignUrl(String basicsSignUrl) {
        this.basicsSignUrl = basicsSignUrl;
    }

    public String getNaturalPreCheckUrl() {
        return naturalPreCheckUrl;
    }

    public void setNaturalPreCheckUrl(String naturalPreCheckUrl) {
        this.naturalPreCheckUrl = naturalPreCheckUrl;
    }

    public String getNaturalCheckUrl() {
        return naturalCheckUrl;
    }

    public void setNaturalCheckUrl(String naturalCheckUrl) {
        this.naturalCheckUrl = naturalCheckUrl;
    }

    public String getNaturalSignUrl() {
        return naturalSignUrl;
    }

    public void setNaturalSignUrl(String naturalSignUrl) {
        this.naturalSignUrl = naturalSignUrl;
    }

    public String getPolicyGenerateImageUrl() {
        return policyGenerateImageUrl;
    }

    public void setPolicyGenerateImageUrl(String policyGenerateImageUrl) {
        this.policyGenerateImageUrl = policyGenerateImageUrl;
    }

    /**
     * 保单查询 接口地址
     * @return
     */
    public String getGuaranteeUrl() {
        return guaranteeUrl;
    }

    public void setGuaranteeUrl(String guaranteeUrl) {
        this.guaranteeUrl = guaranteeUrl;
    }

	public String getPolicyDataSearchUrl() {
		return policyDataSearchUrl;
	}

	public void setPolicyDataSearchUrl(String policyDataSearchUrl) {
		this.policyDataSearchUrl = policyDataSearchUrl;
	}

	public String getPolicySignAddress() {
		return policySignAddress;
	}

	public void setPolicySignAddress(String policySignAddress) {
		this.policySignAddress = policySignAddress;
	}

	public String getPolicySignBaseAddress() {
		return policySignBaseAddress;
	}

	public void setPolicySignBaseAddress(String policySignBaseAddress) {
		this.policySignBaseAddress = policySignBaseAddress;
	}

    public String getShare_AppId() {
        return Share_AppId;
    }

    public void setShare_AppId(String share_AppId) {
        Share_AppId = share_AppId;
    }

    public String getShare_AppSecret() {
        return Share_AppSecret;
    }

    public void setShare_AppSecret(String share_AppSecret) {
        Share_AppSecret = share_AppSecret;
    }

    public String getGetAccessToken_URL() {
        return GetAccessToken_URL;
    }

    public void setGetAccessToken_URL(String getAccessToken_URL) {
        GetAccessToken_URL = getAccessToken_URL;
    }

    public String getGetTicket_URL() {
        return GetTicket_URL;
    }

    public void setGetTicket_URL(String getTicket_URL) {
        GetTicket_URL = getTicket_URL;
    }

    public String getFileDisplayPath() {
        return fileDisplayPath;
    }

    public void setFileDisplayPath(String fileDisplayPath) {
        this.fileDisplayPath = fileDisplayPath;
    }

    public String getAddressCheck() {
        return addressCheck;
    }

    public void setAddressCheck(String addressCheck) {
        this.addressCheck = addressCheck;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getCustomerAssessmentCheck() {
        return customerAssessmentCheck;
    }

    public void setCustomerAssessmentCheck(String customerAssessmentCheck) {
        this.customerAssessmentCheck = customerAssessmentCheck;
    }

    public String getCustomerLevelCheck() {
        return customerLevelCheck;
    }

    public void setCustomerLevelCheck(String customerLevelCheck) {
        this.customerLevelCheck = customerLevelCheck;
    }
}
