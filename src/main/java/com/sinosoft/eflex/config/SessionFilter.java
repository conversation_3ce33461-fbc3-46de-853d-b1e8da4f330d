package com.sinosoft.eflex.config;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.util.IpUtil;
import com.sinosoft.eflex.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;

import javax.servlet.FilterConfig;
import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class SessionFilter implements Filter {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private MyProps myProps;

    @Autowired
    private Environment environment;
    /**
     * 不需要过滤的list列表
     */
    protected static List<Pattern> patterns = new ArrayList<Pattern>();

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        List<String> env = Arrays.asList(environment.getActiveProfiles());
        if (env.contains("uat")) {
            patterns.add(Pattern.compile("swagger"));
            patterns.add(Pattern.compile("api-docs"));
        }
        patterns.add(Pattern.compile("info"));
        patterns.add(Pattern.compile("configuration"));
        patterns.add(Pattern.compile("favicon"));
        patterns.add(Pattern.compile("User/regist"));//注册
        patterns.add(Pattern.compile("User/reciveCreateToken"));//第三方回调创建token
        patterns.add(Pattern.compile("User/login"));//登录
        patterns.add(Pattern.compile("User/firstLogin"));//首次登录
        patterns.add(Pattern.compile("User/secondLogin"));//二次登录
        patterns.add(Pattern.compile("User/getVaildCode"));//获取图片验证码
        patterns.add(Pattern.compile("User/backPassword"));//找回密码
        patterns.add(Pattern.compile("User/spanLogin"));//第三方登录
        patterns.add(Pattern.compile("User//endMsg"));//发送验证码
        patterns.add(Pattern.compile("MaxNo/createMaxNo"));//创建最大号
        patterns.add(Pattern.compile("Person/"));
//		patterns.add(Pattern.compile("sys/insertRegInfo"));//注册审核
        patterns.add(Pattern.compile("hqeflex/services"));//第三方接口
        patterns.add(Pattern.compile("share/getShareData"));//微信分享
        patterns.add(Pattern.compile("User/getClientoInfo"));//校验业务员信息
        patterns.add(Pattern.compile("User/findCode"));//查询码表信息
        patterns.add(Pattern.compile("User/findComCode"));// 查询机构码表信息
        patterns.add(Pattern.compile("TaskController/"));// 需要删除
        patterns.add(Pattern.compile("TestController/"));// 需要删除
        patterns.add(Pattern.compile("File/"));// 文件相关
        patterns.add(Pattern.compile("User/sendMsg"));// 发送找回密码激活码
        patterns.add(Pattern.compile("User/sendMessageCode"));// 手机号登陆发送短信验证码
        patterns.add(Pattern.compile("User/sendAuditUserMessageCode"));// 审核岗发送验证码
        patterns.add(Pattern.compile("User/implicitlyLogin"));//官微隐式登录
        patterns.add(Pattern.compile("User/auditUserBackPassword"));// 审核岗找回密码
        patterns.add(Pattern.compile("User/loginByPhoneCode"));// 手机号登陆
        patterns.add(Pattern.compile("User/checkSinglePeople"));// 校验人员
        patterns.add(Pattern.compile("ensure/exportEmployPlan"));// 导出员工选择计划EXCEL
        patterns.add(Pattern.compile("ensure/exportInsureList"));// 导出投保清单EXCEL(企事业单位投保)
        patterns.add(Pattern.compile("ensure/exportStuInsureList"));// 导出投保清单EXCEL(学生投保)
        patterns.add(Pattern.compile("ensure/exportRelationPlan"));// 导出家属选择计划EXCEL
        patterns.add(Pattern.compile("ensure/exportStudentPlan"));// 导出学生选择计划EXCEL
        patterns.add(Pattern.compile("User/GrpOCRInfo"));// 导出家属选择计划EXCEL
        patterns.add(Pattern.compile("hqeflex/services/getCustomerInfo"));// 电商获取五要素接口
        patterns.add(Pattern.compile("hqeflex/services/base"));// base
        patterns.add(Pattern.compile("User/msgVerificationCode"));// 短信验证码
        patterns.add(Pattern.compile("ensure/downloadContractOrVoucherFile"));
        patterns.add(Pattern.compile("DailyPlanController/getGuaranteeDetail"));
        patterns.add(Pattern.compile("StaffInsureController/getConfirmInfo"));
        patterns.add(Pattern.compile("DailyPlanController/faceStatusQuery"));
        patterns.add(Pattern.compile("StaffInsureController/getInsuredInfo"));
        patterns.add(Pattern.compile("ClaimCount/exportGrpClaimCount"));//导出企业理赔记录
        patterns.add(Pattern.compile("ClaimCount/exportClaimCount"));//导出个人理赔记录
        patterns.add(Pattern.compile("DailyPlanController/getAreaInfo"));//导出个人理赔记录
        patterns.add(Pattern.compile("DailyMakeProposalFormController/makeProposalForm"));//导出个人理赔记录
        patterns.add(Pattern.compile("EFlexEnsureMake/eflexExportPerInfoExcel"));//弹性计划导出人员信息
        patterns.add(Pattern.compile("EnsureAuditController/eflexExportInsureInfoExcel"));//弹性计划导出人员信息---整单确认
        patterns.add(Pattern.compile("ensure/eflexExportPerInfoExcel"));//Hr保障信息查询投保清单导出---弹性计划
        patterns.add(Pattern.compile("DailyPlanPhoneController/faceStatusQuery"));//自然人人脸识别
        patterns.add(Pattern.compile("StaffInsureController/getConfirmInfoShare"));//自然人查询确认签名页面
        patterns.add(Pattern.compile("DailyPlanPhoneController/makeToken"));//自然人更新token
        patterns.add(Pattern.compile("DailyPlanPhoneController/checkLock"));//自然人分享查看是否锁定
        patterns.add(Pattern.compile("EnsureAuditController/exportEmployUnInsurdPerInfo"));//未投保人员清单导出
        patterns.add(Pattern.compile("DailyPlanController/queryRiskList"));//日常计划定制查询险种编码
        patterns.add(Pattern.compile("DailyPlanController/queryPlanListByRiskCode"));//日常计划定制根据险种编码查询计划
        patterns.add(Pattern.compile("AmountTrailController/dailyAmountTrail"));//日常计划年金产品保额试算
        // 固定计划新增计划时查询码表
        patterns.add(Pattern.compile("PlanMake/planFindCode"));
        patterns.add(Pattern.compile("BusInessAdminController/image-change"));
        //初始化页面
        patterns.add(Pattern.compile("InsureController/insurePlanPage"));
        // 用户配置管理
        patterns.add(Pattern.compile("ConfigManage/addAuditUser"));
        // 数据管理平台
        patterns.add(Pattern.compile("dataManage/getEnsureInsureData"));
        //ihomePro查询计划书详情接口
        patterns.add(Pattern.compile("InsurePlan/getInsurePlan"));
        //ihomePro查询上架计划书列表接口
        patterns.add(Pattern.compile("InsurePlan/getInsurePlanList"));
        //ihomePro创建计划书订单接口
        patterns.add(Pattern.compile("InsurePlan/saveInsurePlanOrder"));

        //计划书员工信息查询
        patterns.add(Pattern.compile("InsurePlan/getStaffInfo"));
        //计划书员工信息保存
        patterns.add(Pattern.compile("InsurePlan/saveStaffInfo"));
        //投保清单导出EXCEL
        patterns.add(Pattern.compile("ensure/personsInsureList"));
        //导出计划书员工EXCEL
        patterns.add(Pattern.compile("InsurePlan/staffExportInfoExcel"));
        patterns.add(Pattern.compile("/home-page/info"));
        patterns.add(Pattern.compile("InsureController/getRiskInfos"));
        patterns.add(Pattern.compile("InsureController/getPhoneEnsureList"));
        patterns.add(Pattern.compile("/hr-ensure/grp-ensure"));
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;

        Cookie[] cookies = httpRequest.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                cookie.setSecure(true);
                httpResponse.addCookie(cookie);
            }
            Cookie cookie = cookies[0];
            if (cookie != null) {
                String value = cookie.getValue();
                StringBuilder builder = new StringBuilder();
                builder.append("JSESSIONID=" + value + "; ");
                builder.append("Secure=true; ");
                builder.append("HttpOnly; ");
                httpResponse.setHeader("Set-Cookie", builder.toString());
            }
        }
        httpResponse.setHeader("Content-Security-Policy", "default-src 'self' assets.giocdn.com xflow.zhongan.io wxapi.growingio.com api-xflow.zhongan.io 'unsafe-inline' 'unsafe-eval';font-src *;img-src * data:");
        httpResponse.setHeader("Strict-Transport-Security", "max-age=3600; includeSubdomains");
        httpResponse.setHeader("X-Content-Type-Options", "nosniff");
        httpResponse.setHeader("X-Frame-Options", "sameorign");
        httpResponse.setHeader("X-XSS-Protection", "1; mode=block");
        httpResponse.setHeader("X-Powered-By", "false");

        String url = httpRequest.getRequestURI().substring(httpRequest.getContextPath().length());
        String requestParam = httpRequest.getQueryString();

        // 请求输出
        if (log.isInfoEnabled()) {
            log.info(" eflex 过滤Rest请求获取url:{}", url);
            String clientIp = IpUtil.getIpAddr(httpRequest);
            log.info(" eflex 过滤Rest请求客户端IP:{}", clientIp);
            log.info(" eflex过滤Rest请求,设置response响应头content-type:application/json 请求ur：{}, 参数：{}", httpRequest.getRequestURI(), requestParam);
        }
        if (!StringUtils.isEmpty(requestParam) && (requestParam.contains("..") || url.contains(".."))) {
            Map<String, String> responseMap = new HashMap<>(2);
            responseMap.put("code", "-1");
            responseMap.put("message", "请勿输入非法参数！");
            httpResponse.getWriter().write(JSON.toJSONString(responseMap));
            return;
        }
        if (url.startsWith("/") && url.length() > 1) {
            url = url.substring(1);
        }
        // 不需要过滤
        if (isInclude(url)) {
            chain.doFilter(httpRequest, httpResponse);
        } else {
            String token = httpRequest.getHeader("Authorization");
            if (token != null && redisUtil.get(token) != null) {
                // 重置token有效期  60 * 10
                Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
                redisUtil.expire(token, loginValidity);
                chain.doFilter(httpRequest, httpResponse);
                return;
            }
            log.info("请求链接：" + httpRequest.getRequestURI() + " 被拦截。。。");
            Map<String, String> responseMap = new HashMap<>();
            responseMap.put("code", "-1");
            responseMap.put("message", "token非法或已失效，请重新登录！");
            httpResponse.getWriter().write(JSON.toJSONString(responseMap));
        }
    }

    @Override
    public void destroy() {

    }

    /**
     * 是否需要过滤
     *
     * @param url
     * @return
     */
    private boolean isInclude(String url) {
        for (Pattern pattern : patterns) {
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return true;
            }
        }
        return false;
    }


}