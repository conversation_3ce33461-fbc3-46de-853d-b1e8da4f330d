package com.sinosoft.eflex.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hqins.common.helper.BeanCopier;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.*;
import com.sinosoft.eflex.enums.status.EnsureStateEnum;
import com.sinosoft.eflex.model.AddressEntity.EvaluationCustomer;
import com.sinosoft.eflex.model.AddressEntity.IdCardVerifyRequest;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.encrypt.LisIDEA;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2022/1/5
 * @desc
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class GrpInfoService {

    private final UserService userService;
    private final FdUserMapper fdUserMapper;
    private final FcGrpContactMapper fcGrpContactMapper;
    private final FCContactGrpRelaMapper fcContactGrpRelaMapper;
    private final MaxNoService maxNoService;
    private final RegistRegService registRegService;
    private final FDusertomenugrpMapper fDusertomenugrpMapper;
    private final FDPwdHistMapper fdPwdHistMapper;
    private final FDUserRoleMapper fdUserRoleMapper;
    private final FCGrpInfoMapper fcGrpInfoMapper;
    private final FDCodeMapper fdCodeMapper;
    private final FcHrRegistTempMapper fcHrRegistTempMapper;
    private final FCGrpApplicantMapper fcGrpApplicantMapper;
    private final FCGrpApplicantContactMapper fcGrpApplicantContactMapper;
    private final FCGrpOrderMapper fcGrpOrderMapper;
    private final FcEnsureContactMapper fcEnsureContactMapper;
    private final FCEnsureMapper fcEnsureMapper;
    private final RedisUtil redisUtil;
    private final MyProps myProps;
    private final AddressCheckService addressCheckService;
    private final ImageConvertService imageConvertService;


    /**
     * 查询企业相关信息（企业信息+经办人信息）
     *
     * @param token
     * @return
     */
    public String selectGrpInfo(String token) {
        SelectGrpInfoResp selectGrpInfoResp = new SelectGrpInfoResp();
        GlobalInput globalInput = userService.getSession(token);
        // 查询企业信息
        FCGrpInfo fcGrpInfo1 = fcGrpInfoMapper.selectGrpInfo1(globalInput.getGrpNo());
        // 图片处理
        FCGrpInfo convert = imageConvertService.convert(fcGrpInfo1);
        selectGrpInfoResp.setFcGrpInfo(convert);

        // 查询企业经办人信息
        FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpContactInfo1(globalInput.getCustomNo());
        // 图片处理
        FcGrpContact convertFcGrpContact = imageConvertService.convertFcGrpContact(fcGrpContact);
        selectGrpInfoResp.setFcGrpContact(convertFcGrpContact);

        return JSONObject.toJSONString(ResponseResultUtil.success(selectGrpInfoResp));

    }


    /**
     * 经办人信息变更，（经办人=联系人=企业HR）
     *
     * @param token
     * @param fcGrpContact
     * @return
     */
    @Transactional
    public String saveGrpHrInfo(String token, FcGrpContact fcGrpContact) {
        SaveGrpHrInfoResp saveGrpHrInfoResp = new SaveGrpHrInfoResp();

        GlobalInput globalInput = userService.getSession(token);

        fcGrpContact = checkSaveGrpHrInfoParam(token, fcGrpContact);
        fcGrpContact = imageConvertService.convertFcGrpContactSftp(fcGrpContact);

        // 企业联系人编号
        String oldGrpContactNo = fcGrpContact.getContactNo();
        // 企业客户号
        String grpNo = fcGrpContact.getGrpNo();
        // 企业信息
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(grpNo);
        // 联系人类型，01-第一联系人；02-第二联系人
        String contactType = "02";
        // 是否更换了当前经办人信息（是否改为了新的经办人信息）
        boolean isNewGrpHrInfo = Boolean.TRUE;
        FcGrpContact oldFcGrpContact = fcGrpContactMapper.selectByPrimaryKey(oldGrpContactNo);
        if (fcGrpContact.getIdNo().equals(oldFcGrpContact.getIdNo())) {
            isNewGrpHrInfo = Boolean.FALSE;
        }
        log.info("是否更换了当前经办人信息：isNewGrpHrInfo-" + isNewGrpHrInfo);
        FdUser fdUser;
        // 一、添加新的经办人信息
        if (isNewGrpHrInfo) {
            // 1、禁用之前的企业联系人信息
            FCContactGrpRela oldFcContactGrpRela = new FCContactGrpRela();
            oldFcContactGrpRela.setContactNo(oldGrpContactNo);
            oldFcContactGrpRela.setGrpNo(grpNo);
            oldFcContactGrpRela.setLockState("1");
            //禁用 -- 默认改为第二联系人 方便下次启用
            oldFcContactGrpRela.setContactType("02");
            oldFcContactGrpRela = CommonUtil.initObject(oldFcContactGrpRela, "UPDATE");
            int i = fcContactGrpRelaMapper.updateByPrimaryKeySelective(oldFcContactGrpRela);
            log.info("禁用之前的企业联系人信息: {}", i);
            /**
             * 2、判断当前平台是否已存在该联系人
             */
            Map<String, String> paramInfo = new HashMap<>();
            paramInfo.put("idType", fcGrpContact.getIdType());
            paramInfo.put("idNo", fcGrpContact.getIdNo());
            String contactIsExist = "";
            if (fcGrpContactMapper.isExist(paramInfo) != 0) {
                contactIsExist = "1";
                //存在则获取用户FdUser的信息
                fdUser = fdUserMapper.findUserByIdno(fcGrpContact.getIdNo());
            } else {
                contactIsExist = "0";
                //不存在则插入用户信息表
                fdUser = insertFdUser(fcGrpContact);
            }
            log.info("更换当前经办人后的经办人信息是否存在：contactIsExist-" + contactIsExist);
            if ("0".equals(contactIsExist)) {
                // 变更后的企业联系人不存在
                // 查询该企业下是否有第一联系人
                Map<String, String> mapInfo = new HashMap<>();
                mapInfo.put("grpNo", grpNo);
                mapInfo.put("contactType", "01");//第一联系人顺序
                mapInfo.put("lockState", "0");//0-启用状态  1-禁用状态
                List<FCContactGrpRela> fcContactGrpRelaList = fcContactGrpRelaMapper.seletcContactListByMap(mapInfo);
                if (fcContactGrpRelaList.size() == 0) {
                    contactType = "01";
                }
                //存储企业联系人表
                String contactNo = maxNoService.createMaxNo("ContactNo", null, 20);
                fcGrpContact.setContactNo(contactNo);
                fcGrpContact.setContactType(contactType);
                fcGrpContact.setOperator(globalInput.getUserName());
                fcGrpContact = CommonUtil.initObject(fcGrpContact, "INSERT");
                int insertFcGrpContactResult = fcGrpContactMapper.insertSelective(fcGrpContact);
                log.info("存储企业联系人表: {}", insertFcGrpContactResult);
                /*用户与菜单表*/
                FDusertomenugrpKey fDusertomenugrpKey = registRegService.initFDusertomenugrpKey(fdUser);
                int insertFDusertomenugrpResult = fDusertomenugrpMapper.insert(fDusertomenugrpKey);
                log.info("存储用户与菜单表: {}", insertFDusertomenugrpResult);
                /*密码历史表*/
                FDPwdHist fdPwdHist = new FDPwdHist();
                //登录密码流水号
                fdPwdHist.setPassWordSN(maxNoService.createMaxNo("PassWordSN", null, 20));
                //密码历史表用户编码
                fdPwdHist.setUserNo(fdUser.getUserNo());
                //角色表用户密码
                fdPwdHist.setPassWord(fdUser.getPassWord());
                //密码历史表操作员
                fdPwdHist.setOperator(fdUser.getUserNo());
                fdPwdHist = CommonUtil.initObject(fdPwdHist, "INSERT");
                int insertFdPwdHistResult = fdPwdHistMapper.insert(fdPwdHist);
                log.info("存储密码历史表: {}", insertFdPwdHistResult);
                /*用户角色表*/
                FDUserRole fdUserRole = registRegService.insertFDUserRole(fdUser);
                int insertFdUserRoleResult = fdUserRoleMapper.insert(fdUserRole);
                log.info("存储用户角色表: {}", insertFdUserRoleResult);
                /*用户表*/
                //用户的客户号存储的联系人表的序号
                fdUser.setCustomNo(fcGrpContact.getContactNo());
                int insertFdUserResult = fdUserMapper.insert(fdUser);
                log.info("存储用户信息表: {}", insertFdUserResult);
                saveGrpHrInfoResp.setIsNeedLoginMsg("由于您更改了经办人信息，需重新登录，初始密码为证件号后六位。");
            } else {
                /**
                 * 变更后的企业联系人存在
                 */
                //1、更新用户信息表
                fdUser.setEmail(fcGrpContact.getEmail());
                fdUser.setNickName(fcGrpContact.getName());
                fdUser.setPhone(fcGrpContact.getMobilePhone());
                int updateFdUserResult = fdUserMapper.updateByPrimaryKeySelective(fdUser);
                log.info("更新用户信息表: {}", updateFdUserResult);
                //2、更新企业联系人表
                // 查询已存在的Hr企业联系人信息
                FcGrpContact existFcGrpContact = fcGrpContactMapper.selectGrpContactInfo(fcGrpContact);
                fcGrpContact.setContactNo(existFcGrpContact.getContactNo());
                fcGrpContact = CommonUtil.initObject(fcGrpContact, "UPDATE");
                int updateFcGrpContactResult = fcGrpContactMapper.updateByPrimaryKeySelective(fcGrpContact);
                log.info("更新企业联系人表: {}", updateFcGrpContactResult);
                saveGrpHrInfoResp.setIsNeedLoginMsg("由于您更改了经办人信息，需重新登录。");
            }
            // 存储企业与企业联系人关联表
            FCContactGrpRela fcContactGrpRela = new FCContactGrpRela();
            fcContactGrpRela.setContactNo(fcGrpContact.getContactNo());
            fcContactGrpRela.setGrpNo(grpNo);
            fcContactGrpRela.setContactType(contactType);
            fcContactGrpRela.setLockState("0");// 默认启用
            fcContactGrpRela.setOperator(globalInput.getUserNo());
            fcContactGrpRela = CommonUtil.initObject(fcContactGrpRela, "INSERT");
            int insertFcContactGrpRelaResult = fcContactGrpRelaMapper.insertSelective(fcContactGrpRela);
            log.info("存储企业与企业联系人关联表: {}", insertFcContactGrpRelaResult);
            /**
             * 返回报文
             */
            saveGrpHrInfoResp.setIsNeedLogin(StateEnum.VALID.getCode());
        } else {
            /**
             * 变更之前的联系人信息
             */
            //1、更新用户信息表
            fdUser = fdUserMapper.findUserByIdno(fcGrpContact.getIdNo());
            fdUser.setNickName(fcGrpContact.getName());
            fdUser.setPhone(fcGrpContact.getMobilePhone());
            fdUser = CommonUtil.initObject(fdUser, "update");
            int updateUserResult = fdUserMapper.updateByPrimaryKeySelective(fdUser);
            log.info("更新用户信息表: {}", updateUserResult);
            //2、更新企业联系人表
            // 查询已存在的Hr企业联系人信息
            FcGrpContact existFcGrpContact = fcGrpContactMapper.selectGrpContactInfo(fcGrpContact);
            fcGrpContact.setContactNo(existFcGrpContact.getContactNo());
            fcGrpContact = CommonUtil.initObject(fcGrpContact, "update");
            int updateFcGrpContactResult = fcGrpContactMapper.updateByPrimaryKeySelective(fcGrpContact);
            log.info("更新企业联系人表: {}", updateFcGrpContactResult);
            /**
             * 返回报文
             */
            saveGrpHrInfoResp.setIsNeedLogin(StateEnum.INVALID.getCode());
        }
        /**
         * 更新HR注册信息表
         */
        FcHrRegistTemp fcHrRegistTemp = new FcHrRegistTemp();
        fcHrRegistTemp.setName(fcGrpContact.getName());
        fcHrRegistTemp.setNativeplace(fcGrpContact.getNativeplace());
        fcHrRegistTemp.setIdType(fcGrpContact.getIdType());
        fcHrRegistTemp.setIdNo(fcGrpContact.getIdNo());
        fcHrRegistTemp.setIdTypeStartDate(fcGrpContact.getIdTypeStartDate());
        fcHrRegistTemp.setIdTypeEndDate(fcGrpContact.getIdTypeEndDate());
        fcHrRegistTemp.setSex(fcGrpContact.getSex());
        fcHrRegistTemp.setBirthday(fcGrpContact.getBirthDay());
        fcHrRegistTemp.setMobilePhone(fcGrpContact.getMobilePhone());
        fcHrRegistTemp.setEmail(fcGrpContact.getEmail());
        fcHrRegistTemp.setIdImage1(fcGrpContact.getIdImage1());
        fcHrRegistTemp.setIdImage2(fcGrpContact.getIdImage2());
        fcHrRegistTemp.setDepartment(fcGrpContact.getDepartment());
        fcHrRegistTemp.setOldGrpIdType(fcGrpInfo.getGrpIdType());
        fcHrRegistTemp.setOldGrpIdNo(fcGrpInfo.getGrpIdNo());
        fcHrRegistTemp.setOldIdType(oldFcGrpContact.getIdType());
        fcHrRegistTemp.setOldIdNo(oldFcGrpContact.getIdNo());
        fcHrRegistTemp = CommonUtil.initObject(fcHrRegistTemp, "update");
        int updateFcHrRegistTempInfoResult = fcHrRegistTempMapper.updateFcHrRegistTempInfo(fcHrRegistTemp);
        log.info("更新注册信息表: {}", updateFcHrRegistTempInfoResult);
        /**
         * 更新福利联系人信息表（复审通过的福利不更新）
         */
        //查询企业对应的福利信息
        List<FCEnsure> fcEnsures = fcEnsureMapper.selectEnsureByGrpNo(grpNo, globalInput.getUserName());
        log.info("当前企业与经办人下存在" + fcEnsures.size() + "个福利信息！");
        for (FCEnsure fcEnsure : fcEnsures) {
            if ((fcEnsure.getPlanType().equals(PlanTypeEnum.IMMOBILIZATIONPLAN.getCode()) || fcEnsure.getPlanType().equals(PlanTypeEnum.EFLEXPLAN.getCode())) && (!fcEnsure.getEnsureState().equals(EnsureStateEnum.CHECK_COMPLETED.getCode()) && !fcEnsure.getEnsureState().equals(EnsureStateEnum.ALREADYCANCELLATION.getCode()) && !fcEnsure.getEnsureState().equals(EnsureStateEnum.TOBECHARGED.getCode()))) {
                FcEnsureContact fcEnsureContact = new FcEnsureContact();
                fcEnsureContact.setName(fcGrpContact.getName());
                fcEnsureContact.setNativeplace(fcGrpContact.getNativeplace());
                fcEnsureContact.setIdType(fcGrpContact.getIdType());
                fcEnsureContact.setIdNo(fcGrpContact.getIdNo());
                fcEnsureContact.setIdTypeStartDate(fcGrpContact.getIdTypeStartDate());
                fcEnsureContact.setIdTypeEndDate(fcGrpContact.getIdTypeEndDate());
                fcEnsureContact.setSex(fcGrpContact.getSex());
                fcEnsureContact.setBirthDay(fcGrpContact.getBirthDay());
                fcEnsureContact.setMobilePhone(fcGrpContact.getMobilePhone());
                fcEnsureContact.setEmail(fcGrpContact.getEmail().trim());
                fcEnsureContact.setDepartment(fcGrpContact.getDepartment());
                fcEnsureContact.setIdImage1(fcGrpContact.getIdImage1());
                fcEnsureContact.setIdImage2(fcGrpContact.getIdImage2());
                fcEnsureContact.setEnsureCode(fcEnsure.getEnsureCode());
                fcEnsureContact = CommonUtil.initObject(fcEnsureContact, "update");
                int updateFcEnsureContactCount = fcEnsureContactMapper.updateByPrimaryKeySelective(fcEnsureContact);
                log.info("更新当前福利（" + fcEnsure.getEnsureCode() + "）下投保人信息表-updateFcEnsureContactCount：" + updateFcEnsureContactCount);
                //更新一下投保联系人表 fcgrpapplicantcontact，理论上是不用的，只有复核通过才会保存这张表吗，然而条件中已经去除掉了复审通过
                FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(fcEnsure.getEnsureCode());
                if (!ObjectUtils.isEmpty(fcGrpOrder)) {
                    FCGrpApplicantContact fcGrpApplicantContact = new FCGrpApplicantContact();
                    fcGrpApplicantContact.setGrpAppNo(fcGrpOrder.getGrpAppNo());
                    BeanUtils.copyProperties(fcGrpContact, fcGrpApplicantContact);
                    fcGrpApplicantContact = CommonUtil.initObject(fcGrpApplicantContact, OperationTypeEnum.update.getCode());
                    int updateGrpApplicantContactCount = fcGrpApplicantContactMapper.updateByPrimaryKeySelective(fcGrpApplicantContact);
                    log.info("更新当前团体订单（" + fcGrpOrder.getGrpOrderNo() + "）投保人联系人信息表-updateGrpApplicantCount：" + updateGrpApplicantContactCount);
                }
            }
        }
        /**
         * 更新token信息
         */
        globalInput.setName(fcGrpContact.getName());
        globalInput.setNickName(fcGrpContact.getName());
        globalInput.setUserName(fcGrpContact.getIdNo());
        globalInput.setCustomNo(fcGrpContact.getContactNo());
        globalInput.setSex(fcGrpContact.getSex());
        globalInput.setUserNo(fdUser.getUserNo());
        int loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
        redisUtil.put(token, JSON.toJSONString(globalInput), loginValidity);

        return JSONObject.toJSONString(ResponseResultUtil.success(saveGrpHrInfoResp));
    }

    /**
     * 校验经办人信息变更请求参数
     */
    private FcGrpContact checkSaveGrpHrInfoParam(String token, FcGrpContact fcGrpContact) {
        // 姓名
        String name = fcGrpContact.getName();
        // 证件类型
        String idType = fcGrpContact.getIdType();
        // 年龄
        int age = 0;

        if (StringUtils.isEmpty(fcGrpContact.getGrpNo())) {
            throw new SystemException("企业客户号不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpContact.getName())) {
            throw new SystemException("姓名不能为空！");
        } else {
            if (StringUtils.isEmpty(idType)) {
                throw new SystemException("证件类型不能为空！");
            }
            // 校验姓名结果
            String checkNameResult = "";
            switch (idType) {
                case "1":
                    checkNameResult = CheckUtils.checkChineseName(name);
                    break;
                case "0":
                case "4":
                case "7":
                case "B":
                case "E":
                case "G":
                case "H":
                case "I":
                    checkNameResult = CheckUtils.checkChineseName(name);
                    break;
                default:
                    throw new SystemException("证件类型不存在！");
            }
            if (!StringUtils.isEmpty(checkNameResult)) {
                throw new SystemException(checkNameResult);
            }
        }
        if (StringUtils.isEmpty(fcGrpContact.getMobilePhone())) {
            throw new SystemException("手机号不能为空！");
        } else if (!CheckUtils.checkMobilePhone(fcGrpContact.getMobilePhone())) {
            throw new SystemException("手机号格式有误！");
        }
        if (StringUtils.isEmpty(fcGrpContact.getSex())) {
            throw new SystemException("性别不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpContact.getBirthDay())) {
            throw new SystemException("出生日期不能为空！");
        } else {
            if (!DateTimeUtil.isDate(fcGrpContact.getBirthDay())) {
                throw new SystemException("出生日期不合法！");
            }
            // 年龄
            age = CheckUtils.checkStaffAge(fcGrpContact.getBirthDay());
            if (age < 16) {
                throw new SystemException("年龄需要在16周岁以上！");
            }
        }
        if (StringUtils.isEmpty(fcGrpContact.getIdNo())) {
            throw new SystemException("证件号不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpContact.getIdTypeStartDate())) {
            throw new SystemException("证件有效起期不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpContact.getIdTypeEndDate())) {
            throw new SystemException("证件有效止期不能为空！");
        }
        /**
         * 证件类型和国籍
         */
        String checkData = CheckUtils.newCheck("1", fcGrpContact.getNativeplace(), fcGrpContact.getIdType(), fcGrpContact.getIdNo(), fcGrpContact.getBirthDay(), fcGrpContact.getSex());
        if (!StringUtils.isEmpty(checkData)) {
            throw new SystemException("经办人 " + checkData);
        }
        //校验证件有效期
        String errMsg = CheckUtils.checkIdtypeEndDate(age, idType, fcGrpContact.getIdTypeEndDate(), fcGrpContact.getBirthDay());
        if (!StringUtils.isEmpty(errMsg)) {
            throw new SystemException(errMsg);
        }
        // 校验身份证号是否和出生日期和性别一致
        if (idType.equals(IDTypeEnum.IDCARD.getCode())) {
            String checkIDCardResult = IDCardUtil.checkIDCard(fcGrpContact.getIdNo(), fcGrpContact.getSex(), fcGrpContact.getBirthDay());
            if (!StringUtils.isEmpty(checkIDCardResult)) {
                throw new SystemException(checkIDCardResult);
            }
            //身份证号x改为大写
            fcGrpContact.setIdNo(fcGrpContact.getIdNo().toUpperCase());
        }
        //校验国籍与证件类型，证件号码以及年龄的关系
        String errorMsg = CheckUtils.checkNativeplaceByIdTypeandIdNoandAge("3", fcGrpContact.getNativeplace(), idType, fcGrpContact.getIdNo(), age, fcGrpContact.getBirthDay(), fcGrpContact.getSex());
        if (!StringUtils.isEmpty(errorMsg)) {
            throw new SystemException(errorMsg);
        }
        if (StringUtils.isEmpty(fcGrpContact.getIdImage1()) || StringUtils.isEmpty(fcGrpContact.getIdImage2())) {
            throw new SystemException("证件影像件不能为空！");
        } else {
            if (!fcGrpContact.getIdImage1().startsWith("http") || !fcGrpContact.getIdImage2().startsWith("http")) {
                throw new SystemException("证件影像件信息有误！");
            }
        }
        // 判断手机号是否已存在
        int checkint = fdUserMapper.checkByPhone(fcGrpContact.getMobilePhone(), fcGrpContact.getIdNo(), "2");
        if (checkint > 0) {
            throw new SystemException("该手机号已注册！");
        }

        //判断当前企业下是否存在该Hr，表示企业与HR关联关系已存在。  存在--阻断
        Map<String, String> paramInfo = new HashMap<>();
        paramInfo.put("idType", idType);
        paramInfo.put("idNo", fcGrpContact.getIdNo());
        paramInfo.put("grpNo", fcGrpContact.getGrpNo());
        paramInfo.put("contactNo", fcGrpContact.getContactNo());
        int sum = fcGrpContactMapper.checkIdNoIsExists(paramInfo);
        if (sum > 0) {
            throw new SystemException("当前企业与经办人的绑定关系已存在！");
        }
        List<EvaluationCustomer> customerList = Arrays.asList(EvaluationCustomer.builder()
                .name(fcGrpContact.getName())
                .idType(CoreIdType.getNameByCoreId(fcGrpContact.getIdType()).name())
                .idNo(fcGrpContact.getIdNo())
                .gender(GenderType.getGenderByCoreId(fcGrpContact.getSex()).name())
                .birthday(fcGrpContact.getBirthDay())
                .nationality(fcGrpContact.getNativeplace())
                .build());
        List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
        //校验二要素
        String failVerifies = addressCheckService.checkIdCard(verifies);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(failVerifies)) {
            throw new SystemException(failVerifies);
        }
        return fcGrpContact;
    }

    /**
     * 封装FdUser对象
     *
     * @param fcGrpContact
     * @return
     */
    public FdUser insertFdUser(FcGrpContact fcGrpContact) {
        //封装对象
        FdUser fdUser = new FdUser();
        //用户证件号
        String idNo = fcGrpContact.getIdNo();
        //用户编号
        fdUser.setUserNo(maxNoService.createMaxNo("UserNo", null, 20));
        //用户类型
        fdUser.setCustomType("2");
        //登陆姓名为证件号
        fdUser.setUserName(idNo);
        //昵称
        fdUser.setNickName(fcGrpContact.getName());
        //hr证件号
        fdUser.setIDNo(idNo);
        //用户密码为证件号后6位
        LisIDEA encryPassword = new LisIDEA();
        fdUser.setPassWord(encryPassword.encryptString(idNo.length() >= 6 ? idNo.substring(idNo.length() - 6) : idNo + "000000".substring(0, 6 - idNo.length())));
        //手机号
        fdUser.setPhone(fcGrpContact.getMobilePhone());
        //是否锁定
        fdUser.setIsLock("0");
        //是否是VIP
        fdUser.setIsVIP("N");
        //用户状态
        fdUser.setUserState("1");
        //登录失败次数
        fdUser.setLoginFailTimes(0);
        //操作员
        fdUser.setOperator(fdUser.getUserNo());
        fdUser = CommonUtil.initObject(fdUser, "INSERT");
        return fdUser;
    }

    /**
     * 保存企业信息
     *
     * @param token
     * @param fcGrpInfo
     */
    @Transactional
    public String saveGrpInfo(String token, FCGrpInfo fcGrpInfo) {
        // 校验企业相关信息
        checkSaveGrpInfoParam(fcGrpInfo);

        // 图片处理
        fcGrpInfo = imageConvertService.convertFcGrpInfoSftp(fcGrpInfo);

        FcHrRegistTemp fcHrRegistTemp = new FcHrRegistTemp();
        BeanUtils.copyProperties(fcGrpInfo, fcHrRegistTemp);
        fcHrRegistTemp.setGrpAddress(fcGrpInfo.getGrpAddRess());
        fcHrRegistTemp.setRegAddress(fcGrpInfo.getRegaddress());
        int updateHrRegistTempCount = fcHrRegistTempMapper.updateHrRegistTemp(CommonUtil.initObject(fcHrRegistTemp, OperationTypeEnum.update.getCode()));
        log.info("更新企业注册表信息-updateHrRegisterTempCount：" + updateHrRegistTempCount);
        // 2、更新企业信息表
        int updateFcGrpInfoCount = fcGrpInfoMapper.updateByPrimaryKeySelective(CommonUtil.initObject(fcGrpInfo, OperationTypeEnum.update.getCode()));
        log.info("更新企业信息表-updateFcGrpInfoCount：" + updateFcGrpInfoCount);
        //  3、更新福利投保人信息表，只有复核通过才会插入该表。
        //查询企业对应的投保人信息
        List<FCGrpOrder> fcGrpOrders = fcGrpOrderMapper.selectGrpOrderByGrpNo(fcGrpInfo.getGrpNo());
        log.info("当前企业下存在" + fcGrpOrders.size() + "个团体订单！");
        for (FCGrpOrder fcGrpOrder : fcGrpOrders) {
            //根据福利信息判断
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(fcGrpOrder.getEnsureCode());
            if ((fcEnsure.getPlanType().equals(PlanTypeEnum.IMMOBILIZATIONPLAN.getCode()) ||
                    fcEnsure.getPlanType().equals(PlanTypeEnum.EFLEXPLAN.getCode())) && (!fcEnsure.getEnsureState().equals(EnsureStateEnum.CHECK_COMPLETED.getCode())
                    && !fcEnsure.getEnsureState().equals(EnsureStateEnum.ALREADYCANCELLATION.getCode())
                    && !fcEnsure.getEnsureState().equals(EnsureStateEnum.TOBECHARGED.getCode()))) {
                FCGrpApplicant fcGrpApplicant = new FCGrpApplicant();
                fcGrpApplicant.setGrpAppNo(fcGrpOrder.getGrpAppNo());
                BeanUtils.copyProperties(fcGrpInfo, fcGrpApplicant);
                fcGrpApplicant = CommonUtil.initObject(fcGrpApplicant, OperationTypeEnum.update.getCode());
                int updateGrpApplicantCount = fcGrpApplicantMapper.updateByPrimaryKeySelective(fcGrpApplicant);
                log.info("更新当前团体订单（" + fcGrpOrder.getGrpOrderNo() + "）投保人信息表-updateGrpApplicantCount：" + updateGrpApplicantCount);
            }
        }

        return JSONObject.toJSONString(ResponseResultUtil.success("成功！"));
    }


    /**
     * 校验企业相关信息
     * <p>
     * 】     * @param fcGrpInfo
     *
     * @return
     */
    private void checkSaveGrpInfoParam(FCGrpInfo fcGrpInfo) {

        if (StringUtils.isEmpty(fcGrpInfo.getGrpNo())) {
            throw new SystemException("企业客户号不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getGrpName())) {
            throw new SystemException("企业名称不能为空！");
        }

        if (StringUtils.isEmpty(fcGrpInfo.getCorporationMan())) {
            throw new SystemException("法人代表/负责人信息不能为空！");
        } else {
            String result;
            if (!StringUtil.isEmpty(fcGrpInfo.getCorporationMan())) {
                if (CheckUtils.checkcountname(fcGrpInfo.getCorporationMan())) {
                    result = CheckUtils.checkChineseName(fcGrpInfo.getCorporationMan());
                } else {
                    result = CheckUtils.checkEnglishName(fcGrpInfo.getCorporationMan());
                    String trim = fcGrpInfo.getCorporationMan().trim();
                    fcGrpInfo.setCorporationMan(trim.replaceAll(" +", " "));
                }
                if (!StringUtils.isEmpty(result)) {
                    throw new SystemException(result);
                }
            }
        }

        if (StringUtils.isEmpty(fcGrpInfo.getGrpIdType())) {
            throw new SystemException("企业证件类型不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getGrpIdNo())) {
            throw new SystemException("企业证件号码不能为空！");
        }
        /**
         * 校验企业名称
         */
        if (!StringUtils.isEmpty(fcGrpInfo.getGrpNo())) {
            FCGrpInfo oldFcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(fcGrpInfo.getGrpNo());
            if (!oldFcGrpInfo.getGrpIdNo().equals(fcGrpInfo.getGrpIdNo())) {
                throw new SystemException("企业证件号码不能修改！");
            }
        }

        if (StringUtils.isEmpty(fcGrpInfo.getLegID())) {
            throw new SystemException("企业法定代表人证件号不能为空");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getLegSex())) {
            throw new SystemException("企业法定代表人性别不能为空");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getLegIDType())) {
            throw new SystemException("企业法定代表人证件类型不能为空");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getLegNationality())) {
            throw new SystemException("企业法定代表人国籍不能为空");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getLegIDStartDate())) {
            throw new SystemException("企业法定代表人证件有效期起期不能为空");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getLegBirthday())) {
            throw new SystemException("企业法定代表人出生日期不能为空");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getLegIDEndDate())) {
            throw new SystemException("企业法定代表人证件有效止期不能为空");
        }
        if (LocalDate.parse(fcGrpInfo.getLegIDStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).isAfter(LocalDate.parse(fcGrpInfo.getLegIDEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")))) {
            throw new SystemException("企业法定代表人证件有效期起期不能晚于止期！");
        }
        if (fcGrpInfo.getLegIDType().equals(IDTypeEnum.IDCARD.getCode())) {
            String checkIDCard = IDCardUtil.checkIDCard(fcGrpInfo.getLegID(), fcGrpInfo.getLegSex(), fcGrpInfo.getLegBirthday());
            if (!StringUtils.isEmpty(checkIDCard)) {
                throw new SystemException(checkIDCard);
            }
        }
        /**
         * 证件类型和国籍
         */
        String checkData = CheckUtils.newCheck("1", fcGrpInfo.getLegNationality(), fcGrpInfo.getLegIDType(), fcGrpInfo.getLegID(), fcGrpInfo.getLegBirthday(), fcGrpInfo.getLegSex());
        if (!StringUtils.isEmpty(checkData)) {
            throw new SystemException("法定代表人负责人 " + checkData);
        }

        /**
         * 企业验真
         */
        if (fcGrpInfo.getGrpIdType().equals(GrpIdTypeEnum.UNIFIEDSOCICODE.getCode())) {
            grpTrueCheck(fcGrpInfo.getGrpName(), fcGrpInfo.getGrpIdNo(), fcGrpInfo.getCorporationMan());
        }
        /**
         * 校验企业证件类型与证件号
         */
        String checkGrpTypeAndGrpIdNoResult = CheckUtils.checkGrpTypeAndGrpIdNo2(fcGrpInfo.getGrpIdType(), fcGrpInfo.getGrpIdNo());
        if (!StringUtils.isEmpty(checkGrpTypeAndGrpIdNoResult)) {
            throw new SystemException(checkGrpTypeAndGrpIdNoResult);
        }
        if (StringUtils.isEmpty(fcGrpInfo.getGrpTypeStartDate())) {
            throw new SystemException("企业证件有效起期不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getGrpTypeEndDate())) {
            throw new SystemException("企业证件有效止期不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getGrpIDImage1())) {
            throw new SystemException("企业证件影像件不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getGrpCategory())) {
            throw new SystemException("企业客户类别不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getGrpType())) {
            throw new SystemException("企业性质（投保人性质）不能为空！");
        } else {
            String grpNature1 = fdCodeMapper.selectOtherSign("GrpNature", fcGrpInfo.getGrpType());
            if ("300".equals(grpNature1) && StringUtils.isEmpty(fcGrpInfo.getGrpNatureType())) {
                throw new SystemException("投保单位性质为300，请录企业组织形式！");
            }
            // 校验单位性质
            String grpNature = fdCodeMapper.selectNameByCode("GrpNature", fcGrpInfo.getGrpType());
            if (StringUtils.isEmpty(grpNature)) {
                throw new SystemException("请重新选择投保人性质！");
            }
        }
        if (StringUtils.isEmpty(fcGrpInfo.getTrade())) {
            throw new SystemException("所属行业不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getPeoples())) {
            throw new SystemException("员工总数不能为空！");
        }

        /**
         * 注册电话，之前是以固话的形式去校验的
         */
        if (!StringUtils.isEmpty(fcGrpInfo.getTelphone())) {
            String checkTelResult = CheckUtils.checkTel(fcGrpInfo.getTelphone());
            if (!StringUtils.isEmpty(checkTelResult)) {
                throw new SystemException(checkTelResult);
            }
        }
        //校验是否变更了证件类型和证件号
        FCGrpInfo fcGrpInfo1 = fcGrpInfoMapper.selectByPrimaryKey(fcGrpInfo.getGrpNo());
        if (!fcGrpInfo1.getGrpIdNo().equals(fcGrpInfo.getGrpIdNo())) {
            throw new SystemException("企业证件信息不允许变更！");
        }
        List<EvaluationCustomer> customerList = Collections.singletonList(EvaluationCustomer.builder()
                .name(fcGrpInfo.getCorporationMan())
                .idType(CoreIdType.getNameByCoreId(fcGrpInfo.getLegIDType()).name())
                .idNo(fcGrpInfo.getLegID())
                .gender(GenderType.getGenderByCoreId(fcGrpInfo.getLegSex()).name())
                .birthday(fcGrpInfo.getLegBirthday())
                .nationality(fcGrpInfo.getLegNationality())
                .build());
        List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
        //校验二要素
        String failVerifies = addressCheckService.checkIdCard(verifies);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(failVerifies)) {
            throw new SystemException(failVerifies);
        }

    }


    /**
     * 企业验真
     *
     * @param grpName        企业名称
     * @param grpIdNo        企业证件号
     * @param corporationMan 法人姓名
     */
    public void grpTrueCheck(String grpName, String grpIdNo, String corporationMan) {
        //第三方ocr地址
        String url = myProps.getEtsign().get("url");
        //封装成JSON格式数据
        Map<String, String> map = new HashMap<>();
        map.put("name", grpName);
        map.put("codeUSC", grpIdNo);
        map.put("legalName", corporationMan);
        String json = JSON.toJSONString(map);
        //验证加缓存
        String grpNo = redisUtil.get(grpIdNo);
        if (StringUtils.isEmpty(grpNo)) {
            checkGrp(grpName, grpIdNo, corporationMan, url, json);
        } else {
            HrRegist cache = JSON.parseObject(grpNo, HrRegist.class);
            boolean flag = Boolean.FALSE;
            if (!cache.getGrpName().equals(grpName)) {
                flag = Boolean.TRUE;
            }
            if (!cache.getGrpIdNo().equals(grpIdNo)) {
                flag = Boolean.TRUE;
            }
            if (!cache.getCorporationMan().equals(corporationMan)) {
                flag = Boolean.TRUE;
            }
            if (flag) {
                log.info("企业验真失败：缓存验证结果不一致调用E签宝企业验证请求!!!");
                checkGrp(grpName, grpIdNo, corporationMan, url, json);
            }
        }
        log.info("企业验真成功：缓存验证结果!!!");
    }

    private void checkGrp(String grpName, String grpIdNo, String corporationMan, String url, String json) {
        //将字符串数据转为byte数组
        byte[] byteJson = new byte[0];
        try {
            byteJson = json.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //将请求数据进行hmac256Encode加密
        String result = Base64AndMD5Util.hmac256Encode(myProps.getEtsign().get("ProjectSecret"), json);
        //发送请求及获取相应信息;
        log.info("E签宝企业验证请求 :urlPath={},jsonStr={}", url, json);
        Map<String, Object> src = null;
        try {
            src = HttpUrlUtil.postJson(url, byteJson, result, myProps);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (src.get("errCode").equals("0") || src.get("errCode") == "0") {
            HrRegist hrRegist = new HrRegist();
            hrRegist.setGrpName(grpName);
            hrRegist.setGrpIdNo(grpIdNo);
            hrRegist.setCorporationMan(corporationMan);
            String jsonString = JSON.toJSONString(hrRegist);
            redisUtil.putDay(grpIdNo, jsonString, 7);
            log.info("企业验真成功: {}", src.get("msg"));
        } else {
            log.info("企业验真失败: {}", src.get("msg"));
            throw new SystemException("企业验真失败，" + src.get("msg"));
        }
    }


}