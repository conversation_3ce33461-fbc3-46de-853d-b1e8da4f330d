package com.sinosoft.eflex.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.hqins.common.helper.BeanCopier;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.CoreIdType;
import com.sinosoft.eflex.enums.GenderType;
import com.sinosoft.eflex.enums.GrpIdTypeEnum;
import com.sinosoft.eflex.enums.SMSTemplateNoEnum;
import com.sinosoft.eflex.model.AddressEntity.EvaluationCustomer;
import com.sinosoft.eflex.model.AddressEntity.IdCardVerifyRequest;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.configmanage.AuditUserBackPasswordReq;
import com.sinosoft.eflex.model.configmanage.FindMenuInfoReq;
import com.sinosoft.eflex.model.configmanage.SendAuditUserMessageCodeReq;
import com.sinosoft.eflex.model.sendmessage.SendMessageResp;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.encrypt.LisIDEA;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import com.sinosoft.eflex.util.rest.util.ResponseResult;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;
import com.thoughtworks.xstream.core.util.Base64Encoder;
import com.zhongan.gw.bean.RequestBase;
import com.zhongan.gw.bean.ResponseBase;
import com.zhongan.gw.util.SecurityHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;

import static com.sinosoft.eflex.service.EnsureMakeService.getGenderByIdCard;

/**
 * <AUTHOR>
 */
@Service("UserService")
@RequiredArgsConstructor
@Slf4j
public class UserService {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FcGrpContactMapper fcGrpContactMapper;
    @Autowired
    private FDPwdHistMapper fdPwdHistMapper;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FCUserLoginMapper fcUserLoginMapper;
    @Autowired
    private FDValidateCodeMapper fdValidateCodeMapper;
    @Autowired
    private FCUserTokenMapper fcUserTokenMapper;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private FDmenuMapper fDmenuMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FdComMapper fdComMapper;
    @Autowired
    private FDSysVarMapper fdSysVarMapper;
    @Autowired
    private MyProps myProps;
    @Autowired
    private FDAgentInfoMapper fdAgentInfoMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCContactGrpRelaMapper fcContactGrpRelaMapper;
    @Autowired
    private SendMessageService sendMessageService;

    @Autowired
    private FamilyQueryService familyQueryService;
    @Autowired
    private FCPerRegistDayMapper fcPerRegistDayMapper;
    @Autowired
    private FcHrRegistTempMapper fcHrRegistTempMapper;
    @Autowired
    private AddressCheckService addressCheckService;
    private final ImageConvertService imageConvertService;

    /**
     * 创建图片验证码
     *
     * @param
     * @return map
     */
    public String createVaildCode() {
        Map<String, Object> resultMap = new HashMap<>();

        int len = 4, width = 70, height = 30;
        String randomStr = null;

        //取消0o1i2zZ等容易混淆的字符
        String VERIFY_CODES = "3456789abcdefghjkmnpqrxtuvwxyABCDEFGHJKLMNPQRSTUVWXY";
        //randomStr = RandomStringUtils.random(len, true, true);
        randomStr = RandomStringUtils.random(len, VERIFY_CODES);
        log.info("vaildcode=" + randomStr);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics gra = image.getGraphics();
        gra.setColor(new Color(228, 230, 229));
        gra.fillRect(0, 0, width, height);
        gra.setColor(Color.red);
        gra.setFont(new Font("Lucida Sans", Font.BOLD, 20));
        gra.drawString(randomStr, 10, 20);

        Base64Encoder encoder = new Base64Encoder();
        try {
            ImageIO.write(image, "jpg", outputStream);
            //生成token
            String token = UUID.randomUUID().toString().replaceAll("-", "");
            //验证码放入redis，token作为key，有效期五分钟
            Integer codeValidity = Integer.parseInt(myProps.getTermValidityCode());
            redisUtil.put(token, randomStr.toLowerCase(), codeValidity);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("vToken", token);
            dataMap.put("img", encoder.encode(outputStream.toByteArray()));
            resultMap.put("code", "200");
            resultMap.put("msg", "创建验证码成功");
            resultMap.put("data", dataMap);
        } catch (IOException e) {
            log.info("创建验证码失败：", e);
            resultMap.put("code", "0");
            resultMap.put("msg", "创建验证码失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 用户登录
     *
     * @param user
     * @return map
     */
    @Transactional
    public String login(User user, HttpServletRequest request) {
        // 定义返回结果
        Map<String, Object> resultMap = new HashMap<>();

        // 记录登录IP
        String loginIp = IpUtil.getIpAddr(request);
        log.info("login-登录IP：" + loginIp);

        // 校验参数和验证码
        String checkStr = checkLogin(user, true);
        if (checkStr != null) {
            resultMap.put("code", "1");
            resultMap.put("msg", checkStr);
            return JSON.toJSONString(resultMap);
        }

        try {
            // 校验密码是否正确以及用户是否锁定
            Map<String, Object> loginStatus = checkpwd(user);
            String errorMsg = (String) loginStatus.get("errorMsg");
            if (errorMsg != null) {
                resultMap.put("code", "1");
                resultMap.put("msg", errorMsg);
                return JSON.toJSONString(resultMap);
            }

            // 获取用户信息
            FdUser userInfo = (FdUser) loginStatus.get("userinfo");
            Map<String, Object> dataMap = new HashMap<>();

            //判断登录用户为Hr时 该Hr是否被禁用 禁用 阻断提示
            if ("2".equals(userInfo.getCustomType())) {
                if (fcContactGrpRelaMapper.checkHrLockState(userInfo.getUserName()) <= 0) {
                    resultMap.put("code", "1");
                    resultMap.put("msg", "该Hr用户已禁用，请联系初审管理员进行启用。");
                    resultMap.put("data", dataMap);
                    return JSON.toJSONString(resultMap);
                }
            }
            //校验初始密码修改状态是否为1，为1说明是非首次登录，直接进入登录操作，否则需要返回前台发送短信验证码
            // 排除
            if ("1".equals(userInfo.getPWDState()) || user.getUserType().matches("^3|4|4,5$")) {
                // if ("1".equals(userInfo.getPWDState())) {
                FdUser fdUser = fdUserMapper.selectByPrimaryKey(userInfo.getUserNo());
                if ("Y".equals(fdUser.getIsNeedCaptcha())) {
                    resultMap.put("code", "3");
                    resultMap.put("msg", "");
                    resultMap.put("userNo", userInfo.getUserNo());
                    return JSON.toJSONString(resultMap);
                }
                // 封装token信息
                String token = dealLogin(userInfo.getUserNo(), userInfo.getCustomType(), false, loginIp);
                dataMap.put("token", token);
                dataMap.put("userNo", userInfo.getUserNo());
                // 返回前台数据
                resultMap.put("code", "0");
                resultMap.put("msg", "登录成功！");
                resultMap.put("data", dataMap);
                // 初审岗和复审岗首次登录需要展示提示
                if ("0".equals(userInfo.getPWDState())) {
                    resultMap.put("auditUserFirstLogin", true);
                }
                return JSON.toJSONString(resultMap);

            } else {
                // 首次登录，返回前台发送动态密码
                dataMap.put("isFirst", true);
                dataMap.put("userNo", userInfo.getUserNo());
            }

            resultMap.put("code", "2");
            resultMap.put("msg", "用户首次登录，需要发送短信验证码！");
            resultMap.put("data", dataMap);
        } catch (Exception e) {
            log.info("登录失败！：", e);
            resultMap.put("code", "1");
            resultMap.put("msg", "登录失败！");
            throw new RuntimeException();
        }

        return JSON.toJSONString(resultMap);
    }

    /**
     * 用户首次登录，发送短信验证码之后的登录操作
     *
     * @param user
     * @return map
     */
    @Transactional
    public String firstLogin(User user, HttpServletRequest request) {
        String loginIp = IpUtil.getIpAddr(request);
        log.info("firstLogin-登录IP：" + loginIp);
        Map<String, Object> resultMap = new HashMap<>();
        String checkStr = checkLogin(user, false);
        if (checkStr != null) {
            resultMap.put("code", "1");
            resultMap.put("msg", checkStr);
            return JSON.toJSONString(resultMap);
        }

        try {

            // 密码校验
            Map<String, Object> loginStatus = checkpwd(user);
            String errorMsg = (String) loginStatus.get("errorMsg");
            if (errorMsg != null) {
                resultMap.put("code", "1");
                resultMap.put("msg", loginStatus);
                return JSON.toJSONString(resultMap);
            }

            FdUser userInfo = (FdUser) loginStatus.get("userinfo");
            String userNo = userInfo.getUserNo();

            // 短信验证码校验
            String result = checkPhoneCode(userNo, user.getPhoneCode(), "02");
            if (result != null) {
                resultMap.put("code", "1");
                resultMap.put("msg", result);
                return JSON.toJSONString(resultMap);
            }

            // 登录逻辑
            Map<String, Object> dataMap = new HashMap<>();
            String token = dealLogin(userNo, userInfo.getCustomType(), false, loginIp);
            dataMap.put("token", token);
            dataMap.put("phoneIsFirst", "2".equals(userInfo.getPWDState()));
            resultMap.put("code", "0");
            resultMap.put("msg", "登录成功！");
            resultMap.put("data", dataMap);
        } catch (Exception e) {
            log.info("登录失败", e);
            resultMap.put("code", "1");
            resultMap.put("msg", "登录失败！");
        }

        // 返回前台数据
        return JSON.toJSONString(resultMap);
    }

    /**
     * 因互联网安全需求发送短信验证码之后的登录操作
     *
     * @param user
     * @return map
     */
    @Transactional
    public String secondLogin(User user, HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<>();
        String loginIp = IpUtil.getIpAddr(request);
        log.info("secondLogin-登录IP：" + loginIp);
        String checkStr = checkLogin(user, false);
        if (checkStr != null) {
            resultMap.put("code", "1");
            resultMap.put("msg", checkStr);
            return JSON.toJSONString(resultMap);
        }
        try {
            // 密码校验
            Map<String, Object> loginStatus = checkpwd(user);
            String errorMsg = (String) loginStatus.get("errorMsg");
            if (errorMsg != null) {
                resultMap.put("code", "1");
                resultMap.put("msg", loginStatus);
                return JSON.toJSONString(resultMap);
            }

            FdUser userInfo = (FdUser) loginStatus.get("userinfo");
            String userNo = userInfo.getUserNo();

            // 短信验证码校验
            String result = checkPhoneCode(userNo, user.getPhoneCode(), "02");
            if (result != null) {
                resultMap.put("code", "1");
                resultMap.put("msg", result);
                return JSON.toJSONString(resultMap);
            }

            // 登录逻辑
            Map<String, Object> dataMap = new HashMap<>();
            String token = dealLogin(userNo, userInfo.getCustomType(), false, loginIp);
            dataMap.put("token", token);
            dataMap.put("phoneIsFirst", "true");
            dataMap.put("userNo", userNo);
            resultMap.put("code", "0");
            resultMap.put("msg", "登录成功！");
            resultMap.put("data", dataMap);
        } catch (Exception e) {
            log.info("登录失败", e);
            resultMap.put("code", "1");
            resultMap.put("msg", "登录失败！");
        }

        // 返回前台数据
        return JSON.toJSONString(resultMap);
    }

    /**
     * 第三方单点登录操作
     *
     * @param spanToken
     * @return map
     */
    @Transactional
    public String spanLogin(String spanToken, HttpServletRequest request) {
        //定义返回结果
        Map<String, Object> resultMap = new HashMap<>();
        String loginIp = IpUtil.getIpAddr(request);
        log.info("spanLogin-登录IP：" + loginIp);
        try {
            //定义调用电商获取五要素接口请求参数
            String url = myProps.getDSserviceInfo().get("url");
            String serviceName = "getCustomerInfo-WelfarePlatform";
            String version = myProps.getDSserviceInfo().get("version");
            String appkey = myProps.getDSserviceInfo().get("appkey");
            //不加密 a:rsa加密签名  b:3des加密，c
            String securityType = myProps.getDSserviceInfo().get("securityType");
            String publicKey = myProps.getDSserviceInfo().get("publicKey");
            //业务参数
            Map<String, Object> bizContent = new HashMap<String, Object>();
            bizContent.put("token", spanToken);
            bizContent.put("systemCode", "fulipingtai");
            RequestBase requestBase = new RequestBase();
            requestBase.setServiceName(serviceName);
            requestBase.setVersion(version);
            requestBase.setAppKey(appkey);
            Long timestamp = System.currentTimeMillis();
            requestBase.setTimestamp(timestamp.toString());
            requestBase.setBizContent(bizContent);
            //定义调用电商获取五要素接口返回结果
            String result = "";
            try {
                //加密加签
                SecurityHelper.encryptAndSign(requestBase, publicKey, securityType, "");
                log.info("调用电商五要素接口请求报文: {}", JSON.toJSONString(requestBase));
                result = HttpClientUtil.doPost(url, JSON.toJSONString(requestBase));
                log.info("调用电商五要素接口返回报文: {}", result);
                ResponseBase responseBase = JSON.parseObject(result, ResponseBase.class);
                if (!"200".equals(responseBase.getCode())) {
                    resultMap.put("code", "1");
                    resultMap.put("msg", "跳转失败，错误信息：" + responseBase.getMessage());
                    return JSON.toJSONString(resultMap);
                }
                //解密返回报文
                result = SecurityHelper.checkSignAndDecrypt(responseBase, publicKey, securityType, "");
                log.info("调用电商五要素接口返回报文解密结果: {}", result);
            } catch (Exception e) {
                log.info("调用电商五要素接口异常：", e);
                resultMap.put("code", "1");
                resultMap.put("msg", "跳转失败！");
                return JSON.toJSONString(resultMap);
            }
            //解析返回结果
            JSONObject resultObj = JSONObject.parseObject(result);
            if (!resultObj.getBoolean("success") || resultObj.getJSONObject("data") == null) {
                resultMap.put("code", "1");
                resultMap.put("msg", "跳转失败，错误信息：" + resultObj.getString("message"));
                return JSON.toJSONString(resultMap);
            }
            Map<String, Object> userMap = resultObj.getJSONObject("data");
            // 根据用户五要素查询用户信息
            FdUser userInfo = fdUserMapper.findUserByFiveElement(userMap);
            if (userInfo == null) {
                resultMap.put("code", "1");
                String agentPhone = "400-69-12345";
                resultMap.put("msg", "未查询到您的员工福利信息，具体请与您所在企业确认。如对我司员工福利相关产品及服务感兴趣，欢迎致电" + agentPhone + "咨询。");
                return JSON.toJSONString(resultMap);
            }
            // 登录逻辑
            Map<String, Object> dataMap = new HashMap<>();
            String token = dealLogin(userInfo.getUserNo(), userInfo.getCustomType(), false, loginIp);
            // 校验用户是否是第一次登录
            if ("1".equals(userInfo.getPWDState())) {
                resultMap.put("isFirst", false);
            } else {
                resultMap.put("isFirst", true);
            }

            dataMap.put("token", token);
            resultMap.put("code", "0");
            resultMap.put("msg", "登录成功！");
            resultMap.put("data", dataMap);
        } catch (Exception e) {
            log.info("第三方跳转登录失败：", e);
            resultMap.put("code", "1");
            resultMap.put("msg", "登录失败！");
        }
        //反馈到员福前台https://uflex.e-hqins.com/phone/#/loginJump页面请求的spanLogin接口。
        return JSON.toJSONString(resultMap);
    }

    /**
     * 官网官微五要素登录
     *
     * @param user
     * @return map
     */
    @Transactional
    public String spanLoginApp(String user, HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<>();
        String loginIp = IpUtil.getIpAddr(request);
        log.info("firstLogin-登录IP：" + loginIp);
        try {
            Map<String, Object> userMap = JSON.parseObject(user);
            // 根据用户五要素查询用户信息
            FdUser userInfo = fdUserMapper.findUserByFiveElement(userMap);
            if (userInfo == null) {
                resultMap.put("code", "1");
                String agentPhone = "400-69-12345";
                resultMap.put("msg", "未查询到您的员工福利信息，具体请与您所在企业确认。如对我司员工福利相关产品及服务感兴趣，欢迎致电" + agentPhone + "咨询。");
                return JSON.toJSONString(resultMap);
            }

            // 登录逻辑
            Map<String, Object> dataMap = new HashMap<>();
            String token = dealLogin(userInfo.getUserNo(), userInfo.getCustomType(), false, loginIp);

            // 校验用户是否是第一次登录
            if ("1".equals(userInfo.getPWDState())) {
                resultMap.put("isFirst", false);
            } else {
                resultMap.put("isFirst", true);
            }
            dataMap.put("token", token);
            resultMap.put("code", "0");
            resultMap.put("msg", "登录成功！");
            resultMap.put("data", dataMap);
        } catch (Exception e) {
            log.info("第三方跳转登录失败：", e);
            resultMap.put("code", "1");
            resultMap.put("msg", "登录失败！");
        }

        // 返回前台数据
        return JSON.toJSONString(resultMap);
    }

    /**
     * 用户登录参数校验
     *
     * @param user 登录参数
     * @param flag 判断是否校验图片验证码和短信验证码
     * @return map
     */
    private String checkLogin(User user, Boolean flag) {
        if (user == null) {
            return "登录参数不能为空！";
        }
        if (user.getUserName() == null) {
            return "用户名不能为空！";
        }

        if (user.getPassword() == null) {
            return "密码不能为空！";
        }
        if (user.getUserType() == null) {
            return "用户类型不能为空！";
        }
        //登录时传true校验图片验证码;  首次登录发送短信验证码后传false校验短信验证码。
        if (flag) {
            String vaildcode = user.getVaildCode();
            if (user.getVaildCode() == null) {
                return "验证码不能为空！";
            }
            String vToken = user.getvToken();
            if (vToken == null) {
                return "验证码token不能为空！";
            }
            //校验验证码是否正确
            String vCode = redisUtil.get(vToken);
            if (vCode == null || !vCode.equals(vaildcode.toLowerCase())) {
                return "验证码输入错误或已失效，请重新输入！";
            } else {
                //验证通过清除验证码
                redisUtil.remove(vToken);
            }
        } else {
            if (user.getPhoneCode() == null) {
                return "短信验证码不能为空！";
            }
        }
        return null;
    }

    /**
     * 登录用户名密码校验
     *
     * @param
     * @return map
     */
    private Map<String, Object> checkpwd(User user) {
        // 定义返回参数
        Map<String, Object> resultMap = new HashMap<>();
        // 用户名进行了DES加密，需要解密获取用户名
        String key1 = "a1b2c3d4e5";
        String key2 = "p1o2i3u4y5";
        String key3 = "abcdefg";
        log.info(user.getUserName());
        String username = DesUtilTwo.strDec(user.getUserName(), key1, key2, key3);
        // 清理插件对Sql的影响
        PageHelper.clearPage();
        FdUser userInfo = fdUserMapper.findUserForLogin(username, user.getUserType());
        if (userInfo == null) {
            if (user.getUserType().equals("3") || user.getUserType().equals("4,5")) {
                resultMap.put("errorMsg", "当前账号不存在或录入错误，请确认!");
            } else {
                resultMap.put("errorMsg", "用户名或密码输入错误，请您重新输入!");
            }
            return resultMap;
        }
        //校验用户是否锁定
        if ("1".equals(userInfo.getIsLock())) {
            resultMap.put("errorMsg", "对不起，您的用户已被锁定，您可以通过忘记密码功能重置密码！");
            return resultMap;
        }
        //校验用户密码是否正确
        LisIDEA lisIDEA = new LisIDEA();
        String dbPwd = lisIDEA.decryptString(userInfo.getPassWord());
        if (!user.getPassword().equals(Base64AndMD5Util.md5Encode(dbPwd))) {
            FdUser updateUser = new FdUser();
            updateUser.setUserNo(userInfo.getUserNo());
            updateUser.setModifyDate(DateTimeUtil.getCurrentDate());
            updateUser.setModifyTime(DateTimeUtil.getCurrentTime());

            // 密码输入错误，更新fduser表的登录失败次数字段
            int loginFailTimes = userInfo.getLoginFailTimes();
            updateUser.setLoginFailTimes(loginFailTimes + 1);

            if (loginFailTimes == 4) {
                updateUser.setIsLock("1");//锁定
                fdUserMapper.updateByPrimaryKeySelective(updateUser);
                resultMap.put("errorMsg", "登录失败次数达到5次,您的用户已被锁定，您可以通过忘记密码功能重置密码！");
                return resultMap;
            } else {
//				int chance = 4 - loginFailTimes;
//				errorMsg = "您输入的用户名或者密码错误，您还可以尝试" + chance + "次";
                fdUserMapper.updateByPrimaryKeySelective(updateUser);
                resultMap.put("errorMsg", "您输入的用户名或者密码错误！");
                return resultMap;
            }
        }
        // 校验密码是否过期 add by 2021.11.17
        FDPwdHist fdPwdHist = fdPwdHistMapper.selectNewPwdHist(userInfo.getUserNo());
        if (!ObjectUtils.isEmpty(fdPwdHist) && !StringUtil.isEmpty(fdPwdHist.getPassWordInvalidTime())) {
            String passWordInvalidTime = fdPwdHist.getPassWordInvalidTime();
            if (!DateTimeUtil.checkDate(DateTimeUtil.getCurrentDateTime(), passWordInvalidTime)) {
                resultMap.put("errorMsg", "您的用户密码已过期，可以通过忘记密码功能重置密码！");
                return resultMap;
            }
        }
        resultMap.put("userinfo", userInfo);
        return resultMap;
    }

    /**
     * 短信验证码校验
     *
     * @param userNo, password
     * @return map
     */
    private String checkPhoneCode(String userNo, String phoneCode, String codeType) {
        String code = "";
        switch (codeType) {
            case "01":
                code = "激活码";
                break;
            case "02":
                code = "动态密码";
            case "03":
                code = "短信验证码";
                break;
        }
        // 查询手机验证码
        FDValidateCode trendCode = fdValidateCodeMapper.selectLastCodeByUserNo(userNo, codeType);
        if (trendCode == null) {
            return "对不起，您输入的" + code + "错误，请您重新输入!";
        }

        if ("0".equals(trendCode.getIsvalid())) {
            return "对不起，您的" + code + "已失效，请重新发送!";
        }

        SimpleDateFormat FORMAT = new SimpleDateFormat("yyyy-MM-dd,HH:mm:ss");
        long time = 0;

        try {
            // 动态码有效期
            Date date = FORMAT.parse(trendCode.getEnddate() + "," + trendCode.getEndtime());

            // 当前时间
            Date currentDate = FORMAT.parse(DateTimeUtil.getCurrentDate() + "," + DateTimeUtil.getCurrentTime());

            // 当前时间-动态密码有效期
            time = currentDate.getTime() - date.getTime();
        } catch (Exception e) {
            log.info("校验异常", e);
            return code + "校验异常！";
        }

        // 如果 当前时间-有效期最后时间>0,则动态码失效
        if (time > 0) {
            // 更新动态密码为失效
            FDValidateCode fdValidateCode = new FDValidateCode();
            fdValidateCode.setValidatecodesn(trendCode.getValidatecodesn());
            fdValidateCode.setIsvalid("0");
            fdValidateCodeMapper.updateByPrimaryKeySelective(fdValidateCode);
            return "动态密码已失效，请重新发送!";
        }

        if (!Base64AndMD5Util.md5Encode(trendCode.getValidatecode()).equals(phoneCode)) {
            return "动态密码输入错误，请重新输入!";
        }

        // 校验完毕更新动态密码为失效
        FDValidateCode fdValidateCode = new FDValidateCode();
        fdValidateCode.setValidatecodesn(trendCode.getValidatecodesn());
        fdValidateCode.setIsvalid("0");
        fdValidateCodeMapper.updateByPrimaryKeySelective(fdValidateCode);

        return null;
    }


    /**
     * 处理登录返回token
     *
     * @param userNo
     * @param userType
     * @param isphone
     * @param loginIp  登录IP
     * @return
     */
    private String dealLogin(String userNo, String userType, boolean isphone, String loginIp) {

        //查询上次登录时间
        FCUserLogin lastLogin = fcUserLoginMapper.selectLastLoginByUserNo(userNo);
        String oldLoginDate = null;
        String oldLoginTime = null;
        if (lastLogin != null) {
            oldLoginDate = lastLogin.getLogindate();
            oldLoginTime = lastLogin.getLogintime();
        }

        //插入用户登录记录表 -- add by wudezhong 记录用户信息
        FCUserLogin fcUserLogin = new FCUserLogin();
        String loginSerialNo = maxNoService.createMaxNo("LoginSerialNo", "", 20);
        fcUserLogin.setLoginserialno(loginSerialNo);
        fcUserLogin.setUserno(userNo);
        fcUserLogin.setLogindate(DateTimeUtil.getCurrentDate());
        fcUserLogin.setLogintime(DateTimeUtil.getCurrentTime());
        fcUserLogin.setLoginip(StringUtils.isNotBlank(loginIp) ? loginIp : "127.0.1");
        fcUserLogin.setLogintype("0");
        fcUserLogin.setLoginsource("0");
        fcUserLoginMapper.insert(fcUserLogin);

        if (!isphone) {
            //重置登录失败次数为0
            FdUser fdUser = new FdUser();
            fdUser.setUserNo(userNo);
            fdUser.setLoginFailTimes(0);
            fdUser.setModifyDate(DateTimeUtil.getCurrentDate());
            fdUser.setModifyTime(DateTimeUtil.getCurrentTime());
            fdUserMapper.updateByPrimaryKeySelective(fdUser);
        }

        // 查询用户信息
        GlobalInput userInfo = new GlobalInput();
        Map<String, Object> params = new HashMap<String, Object>();
        switch (userType) {
            case "1"://个人用户
                userInfo = fdUserMapper.findGlobalInfoByUserNo(userNo);
                // 获取ensureCode唯一有效的福利编号
                params.put("perNo", userInfo.customNo);
                params.put("sysDate", DateTimeUtil.getCurrentDate());
                String FindEnsureCode = fcPerRegistDayMapper.findNewensureCode(params);
                if (FindEnsureCode != null) {
                    userInfo.setEnsureCode(FindEnsureCode);
                }
                break;
            case "2"://企业HR用户
                userInfo = fdUserMapper.findGlobalInfoForGrp(userNo);
                // 获取ensureCode唯一有效的福利编号
                params.clear();
                params.put("grpNo", userInfo.getGrpNo());
                params.put("ensureState", "0");
                List<FCEnsure> fcEnsureList2 = fcEnsureMapper.findEnsureList(params);
                if (fcEnsureList2 != null && fcEnsureList2.size() > 0) {
                    userInfo.setEnsureCode(fcEnsureList2.get(0).getEnsureCode());
                }
                break;
            case "3":// 初审
            case "4":// 复审（总公司）
            case "5":// 复审（分公司）
            case "3,5": //初审+复审
                userInfo = fdUserMapper.findGlobalInfoForAdmin(userNo);
                break;
            default:
                break;
        }
        userInfo.setLoginSerialNo(loginSerialNo);
        userInfo.setOldLoginDate(oldLoginDate);
        userInfo.setOldLoginTime(oldLoginTime);

        // 生成token，将用户信息放入redis，token作为key，token返回前台
        String token = UUID.randomUUID().toString().replaceAll("-", "");
        //TODO 这里先写成-1 上线时需要改成10分钟
        Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
        redisUtil.put(token, JSON.toJSONString(userInfo), loginValidity);

        //异步调用员工家属同步方法
        if ("1".equals(userType)) {
            familyQueryService.FamilySynation(userNo);
        }
        return token;

    }

    /**
     * 根据token获取登录用户信息
     *
     * @param token
     * @return GlobalInput
     */
    public GlobalInput getSession(String token) {
        String userinfo = redisUtil.get(token);
        if (StringUtils.isEmpty(userinfo)) {
            return new GlobalInput();
        }
        return JSON.parseObject(userinfo, GlobalInput.class);
    }

    /**
     * 用户退出/注销
     *
     * @param token
     * @return
     */
    @Transactional
    public String logout(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 获取用户信息
            String userinfo = redisUtil.get(token);
            String loginSerialNo = JSON.parseObject(userinfo).getString("loginSerialNo");

            // 记录用户退出时间
            FCUserLogin fcUserLogin = new FCUserLogin();
            fcUserLogin.setLoginserialno(loginSerialNo);
            fcUserLogin.setLogoutdate(DateTimeUtil.getCurrentDate());
            fcUserLogin.setLogouttime(DateTimeUtil.getCurrentTime());
            fcUserLoginMapper.updateByPrimaryKeySelective(fcUserLogin);

            // 清除redis用户信息
            redisUtil.remove(token);

            resultMap.put("code", "200");
            resultMap.put("message", "退出成功");

        } catch (Exception e) {
            log.info("用户退出异常：", e);
            resultMap.put("code", "1");
            resultMap.put("message", "用户退出异常！");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 注册用户的校验
     */
    public String checkRegistInfo(HrRegist hrRegist) {
        String isRightState = hrRegist.getIsRightState();
        Map<String, Object> resultMap = new HashMap<>();
        // 校验法人代表和姓名 和注册电话
        String s = checkNameAndTel(hrRegist);
        if (StringUtils.isNotBlank(s)) {
            resultMap.put("message", s);
            resultMap.put("code", "500");
            return JSON.toJSONString(resultMap);
        }
        // 校验企业信息是否存在
        String result = checkRegist(hrRegist);
        if (StringUtils.isNotBlank(result)) {
            resultMap.put("message", result);
            resultMap.put("code", "500");
            return JSON.toJSONString(resultMap);
        }
        // 校验单位性质
        String grpNature = fdCodeMapper.selectNameByCode("GrpNature", hrRegist.getGrpType());
        if (StringUtils.isBlank(grpNature)) {
            throw new SystemException("请重新选择投保人性质！");
        }
        /**
         * 校验统一社会信用代码，除企业性质为事业单位外，其他都会调用企业验真功能
         */
        // if (hrRegist.getGrpIdType().equals(GrpIdTypeEnum.UNIFIEDSOCICODE.getCode()))
        // {
        // String unifiedsociCode = hrRegist.getGrpIdNo();
        // // 1、统一社会信用代码长度应为18位
        // if (unifiedsociCode.length() != 18) {
        // throw new SystemException("统一社会信用代码长度应为18位！");
        // }
        // //
        // 2、统一社会信用代码的前两位应为"11,12,13,19,21,29,31,32,33,34,35,39,41,49,51,52,53,59,61,62,69,71,72,79,81,89,91,92,93,A1,A9,N1,N2,N3,N9,Y1,54,55,37,G1"中的一个
        // String substring = unifiedsociCode.substring(0, 2);
        // List<String> stringList = Arrays.asList(new String[] { "11", "12", "13",
        // "19", "21", "29", "31", "32", "33",
        // "34", "35", "39", "41", "49", "51", "52", "53", "59", "61", "62", "69", "71",
        // "72", "79", "81",
        // "89", "91", "92", "93", "A1", "A9", "N1", "N2", "N3", "N9", "Y1", "54", "55",
        // "37", "G1" });
        // if (!stringList.contains(substring)) {
        // throw new SystemException("统一社会信用代码格式有误！");
        // }
        // // 3、统一社会信用代码的第3-8位对应《业务代码表》中的"县及县以上行政区划代码"
        // String substring1 = unifiedsociCode.substring(2, 8);
        // int count = fdCodeMapper.selectSingleInfoByCountyCode(substring1);
        // if (count == 0) {
        // throw new SystemException("统一社会信用代码格式有误！");
        // }
        // }
        // 校验证件类型与证件号
        String checkGrpTypeAndGrpIdNoResult = CheckUtils.checkGrpTypeAndGrpIdNo2(hrRegist.getGrpIdType(),
                hrRegist.getGrpIdNo());
        if (StringUtils.isNotBlank(checkGrpTypeAndGrpIdNoResult)) {
            throw new SystemException(checkGrpTypeAndGrpIdNoResult);
        }
        // 校验证件号
        if ("0".equals(hrRegist.getIdType())) {
            if (!IDCardUtil.isIDCard(hrRegist.getIdNo())) {
                resultMap.put("message", "经办人身份证号格式录入错误。");
                resultMap.put("code", "500");
                return JSON.toJSONString(resultMap);
            }
            if (hrRegist.getIdNo().length() != 18) {
                resultMap.put("message", "经办人身份证号应为18位。");
                resultMap.put("code", "500");
                return JSON.toJSONString(resultMap);
            }
            String idBir = IDCardUtil.dateOfBirth(hrRegist.getIdNo());
            if (!hrRegist.getBirthday().equals(idBir)) {
                resultMap.put("message", "经办人出生日期与身份证不符。");
                resultMap.put("code", "500");
                return JSON.toJSONString(resultMap);
            }
            if (!getGenderByIdCard(hrRegist.getIdNo()).equals("0".equals(hrRegist.getSex()) ? "男" : "女")) {
                resultMap.put("message", "经办人性别与身份证不符。");
                resultMap.put("code", "500");
                return JSON.toJSONString(resultMap);
            }
        }
        //校验生效日期
        if (StringUtil.isEmpty(hrRegist.getIdTypeEndDate())) {
            return JSON.toJSONString(ResultUtil.error("经办人证件有效期不能为空！"));
        }
        //邮箱
        if (StringUtil.isEmpty(hrRegist.getEmail())) {
            return JSON.toJSONString(ResultUtil.error("经办人邮箱不能为空！"));
        }
        //所属部门
        if (StringUtil.isEmpty(hrRegist.getDepartment())) {
            return JSON.toJSONString(ResultUtil.error("经办人所属部门不能为空！"));
        }
        //校验企业证件的证件有效起期和证件有效止期
        if (StringUtils.isBlank(hrRegist.getGrpTypeStartDate()) || StringUtils.isBlank(hrRegist.getGrpTypeEndDate())) {
            return JSON.toJSONString(ResultUtil.error("企业证件有效起期和止期不能为空！"));
        }
        if (org.springframework.util.StringUtils.isEmpty(hrRegist.getLegID())) {
            return JSON.toJSONString(ResultUtil.error("企业法定代表人证件号不能为空！"));
        }
        if (org.springframework.util.StringUtils.isEmpty(hrRegist.getLegSex())) {
            return JSON.toJSONString(ResultUtil.error("企业法定代表人性别不能为空！"));
        }
        if (org.springframework.util.StringUtils.isEmpty(hrRegist.getLegIDType())) {
            return JSON.toJSONString(ResultUtil.error("企业法定代表人证件类型不能为空！"));
        }
        if (org.springframework.util.StringUtils.isEmpty(hrRegist.getLegNationality())) {
            return JSON.toJSONString(ResultUtil.error("企业法定代表人国籍不能为空！"));
        }
        if (org.springframework.util.StringUtils.isEmpty(hrRegist.getLegIDStartDate())) {
            return JSON.toJSONString(ResultUtil.error("企业法定代表人证件有效期起期不能为空！"));
        }
        if (org.springframework.util.StringUtils.isEmpty(hrRegist.getLegBirthday())) {
            return JSON.toJSONString(ResultUtil.error("企业法定代表人出生日期不能为空！"));
        }
        if (org.springframework.util.StringUtils.isEmpty(hrRegist.getLegIDEndDate())) {
            return JSON.toJSONString(ResultUtil.error("企业法定代表人证件有效止期不能为空！"));
        }

        String checkIDCard = IDCardUtil.checkIDCard(hrRegist.getLegID(), hrRegist.getLegSex(), hrRegist.getLegBirthday());
        if (!org.springframework.util.StringUtils.isEmpty(checkIDCard)) {
            return JSON.toJSONString(ResultUtil.error(checkIDCard));
        }
        //校验企业注册地
        String checkNationality = addressCheckService.checkNationalityCode(hrRegist.getGrpRegisterAddress());
        if (!org.springframework.util.StringUtils.isEmpty(checkNationality)) {
            return JSON.toJSONString(ResultUtil.error(checkNationality));
        }
        //校验HR信息
        Map<String, String> map1 = new HashMap<>();
        map1.put("sign", "4");//1：员工 2：家属  3：HR 4:法人
        map1.put("idType", hrRegist.getLegIDType());//证件类型
        map1.put("idNo", hrRegist.getLegID());//证件号
        map1.put("birthDay", hrRegist.getLegBirthday());//出生日期
        map1.put("sex", hrRegist.getLegSex());//性别
        map1.put("nativeplace", hrRegist.getLegNationality());//国籍
        map1.put("idTypeEndDate", hrRegist.getLegIDEndDate());//证件有效期
        String resultMsg = CheckUtils.checkSinglePeople(map1);
        if (StringUtils.isNotBlank(resultMsg)) {
            result += "法定代表人负责人 " + resultMsg;
        }
        List<EvaluationCustomer> customerList = new ArrayList<>();
        customerList.add(EvaluationCustomer.builder()
                .name(hrRegist.getName())
                .idType(CoreIdType.getNameByCoreId(hrRegist.getIdType()).name())
                .idNo(hrRegist.getIdNo())
                .gender(GenderType.getGenderByCoreId(hrRegist.getSex()).name())
                .birthday(hrRegist.getBirthday())
                .nationality(hrRegist.getNativeplace())
                .build());
        customerList.add(EvaluationCustomer.builder()
                .name(hrRegist.getCorporationMan())
                .idType(CoreIdType.getNameByCoreId(hrRegist.getLegIDType()).name())
                .idNo(hrRegist.getLegID())
                .gender(GenderType.getGenderByCoreId(hrRegist.getLegSex()).name())
                .birthday(hrRegist.getLegBirthday())
                .nationality(hrRegist.getLegNationality())
                .build());
        List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
        //校验二要素
        String failVerifies = addressCheckService.checkIdCard(verifies);
        if (StringUtils.isNotEmpty(failVerifies)) {
            return JSON.toJSONString(ResultUtil.error(failVerifies));
        }
        // 查询注册证字段是否存在
        if (result == null || "".equals(result)) {
            if (hrRegist.getIdType().equals("0")) {
                hrRegist.setIdNo(hrRegist.getIdNo().toUpperCase());
            }
            // 校验企业联系人是否已经存在，存在后应该核实后提交
            int countId = fcGrpContactMapper.selectIDFCHrRegistTemp1(hrRegist);
            if (countId > 1) {
                // 用户是否确认信息无误后
                if (!isRightState.equals("1")) {
                    List<HrRegist> hrRegistolist = fcGrpContactMapper.selectFCHrRegistTempOldInfo(hrRegist);
                    HrRegist hrRegistold = hrRegistolist.get(0);// 取一条即可
                    // 判断每个元素都可以相等
                    if (!hrRegistold.getName().equals(hrRegist.getName())) {
                        resultMap.put("message", "存在证件号相同，但是姓名不同的联系人，请核对联系人信息是否录入正确。");
                        resultMap.put("code", "500");
                        return JSON.toJSONString(resultMap);
                    }
                    if (!hrRegistold.getIdType().equals(hrRegist.getIdType())) {
                        resultMap.put("message", "存在证件号相同，但是证件类型不同的联系人，请核对联系人信息是否录入正确。");
                        resultMap.put("code", "500");
                        return JSON.toJSONString(resultMap);
                    }
                    String msg = "";
                    if (StringUtils.isNotBlank(hrRegistold.getNativeplace())) {
                        if (!hrRegistold.getNativeplace().equals(hrRegist.getNativeplace())) {
                            msg += "国籍";
                        }
                    }
                    if (StringUtils.isNotBlank(hrRegistold.getIdTypeEndDate())) {
                        if (!hrRegistold.getIdTypeEndDate().equals(hrRegist.getIdTypeEndDate())) {
                            if (StringUtils.isNotBlank(msg)) {
                                msg += ",";
                            }
                            msg += "证件有效期";
                        }
                    }
                    if (StringUtils.isNotBlank(hrRegistold.getSex())) {
                        if (!hrRegistold.getSex().equals(hrRegist.getSex())) {
                            if (StringUtils.isNotBlank(msg)) {
                                msg += ",";
                            }
                            msg += "性别";
                        }
                    }
                    if (StringUtils.isNotBlank(hrRegistold.getBirthday())) {
                        if (!hrRegistold.getBirthday().equals(hrRegist.getBirthday())) {
                            if (StringUtils.isNotBlank(msg)) {
                                msg += ",";
                            }
                            msg += "出生日期";
                        }
                    }
                    if (StringUtils.isNotBlank(hrRegistold.getMobilePhone())) {
                        if (!hrRegistold.getMobilePhone().equals(hrRegist.getMobilePhone())) {
                            if (StringUtils.isNotBlank(msg)) {
                                msg += ",";
                            }
                            msg += "手机号";
                        }
                    }
                    if (StringUtils.isNotBlank(msg)) {
                        resultMap.put("message", "该企业联系人已维护，但由于" + msg + "与之前录入的信息不相符，请您核实确认后提交。");
                        resultMap.put("code", "203");
                        return JSON.toJSONString(resultMap);
                    }
                }
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "校验成功");
        } else {
            resultMap.put("message", result);
            resultMap.put("code", "500");
        }
        return JSON.toJSONString(resultMap);
    }


    public String checkNameAndTel(HrRegist hrRegist) {
        String name = "";
        String nameErr = "";
        String name1 = hrRegist.getName();
        String idType = hrRegist.getIdType();
        if (!StringUtil.isEmpty(idType) && !StringUtil.isEmpty(name1)) {
            if (idType.equals("1")) {
                nameErr = CheckUtils.checkForeignName(name1);
            } else {
                nameErr = CheckUtils.checkChineseName(name1);
            }
            if (!StringUtil.isEmpty(nameErr)) {
                return nameErr;
            }
        }

        String corporationMan = hrRegist.getCorporationMan();
        String telphone = hrRegist.getTelphone();//固定电话
        if (!StringUtil.isEmpty(telphone)) {
            String s = CheckUtils.checkTel(telphone);
            if (!s.equals("")) {
                return s;
            }
        }
        if (!StringUtil.isEmpty(corporationMan)) {
            if (CheckUtils.checkcountname(corporationMan)) {
                name = CheckUtils.checkChineseName(corporationMan);
            } else {
                name = CheckUtils.checkEnglishName(corporationMan);
                String trim = corporationMan.trim();
                corporationMan = trim.replaceAll(" +", " ");
            }
            if (!name.equals("")) {
                return name;
            }
        } else {
            return "法人代表不能为空。";
        }
        return "";

    }

    /**
     * <AUTHOR>
     * @description 注册用户
     * @date 19:24 19:24
     * @modified
     */
    @Transactional
    public String addRegistInfo(HrRegist hrRegist) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (hrRegist.getIdType().equals("0")) {
                hrRegist.setIdNo(hrRegist.getIdNo().toUpperCase());
            }
            insertFCHrRegistTemp(hrRegist);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "注册成功");
        } catch (Exception e) {
            log.info("注册失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "注册失败");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    //将数据插入到临时表FCHrRegistTemp
    public void insertFCHrRegistTemp(HrRegist hrRegist) {
        String idType = hrRegist.getIdType();
        String name = hrRegist.getName();
        String grpName = hrRegist.getGrpName();
        hrRegist.setName(name.replaceAll("\\s*|\\r|\\n|\\t", ""));
        hrRegist.setGrpName(grpName.replaceAll("\\s*|\\r|\\n|\\t", ""));
        hrRegist.setCorporationMan(hrRegist.getCorporationMan().trim());
        if (!StringUtil.isEmpty(idType) && !StringUtil.isEmpty(name)) {
            if (idType.equals("1")) {
                String trim = name.trim();
                name = trim.replaceAll(" +", " ");
                hrRegist.setName(name);
            }
        }
        if (hrRegist.getGrpIdType().equals(GrpIdTypeEnum.UNIFIEDSOCICODE.getCode())) {
            hrRegist.setUnifiedsociCode(hrRegist.getGrpIdNo());
        }
        //审核状态（0-待审核；1-审核通过；2-审核不通过）
        hrRegist.setCheckStatus("0");
        //统一更新相同的HR信息
        int countId = fcGrpContactMapper.selectIDFCHrRegistTemp(hrRegist);
        hrRegist = CommonUtil.initObject(hrRegist, "INSERT");
        if (countId > 0) {
            HrRegist newhrRegist = hrRegist;
            List<HrRegist> hrRegistoldlist = fcGrpContactMapper.selectFCHrRegistTempOldInfo3(hrRegist);
            for (int i = 0; i < hrRegistoldlist.size(); i++) {
                newhrRegist.setRegistSN(hrRegistoldlist.get(i).getRegistSN());
                // 更新HR信息表
                newhrRegist = imageConvertService.convertHrRegisterSftp(newhrRegist);
                fcGrpContactMapper.updateHRinfo(newhrRegist);
            }
        } else {
            //流水号
            hrRegist.setRegistSN(maxNoService.createMaxNo("RegistSN", null, 20));
            //插入HR信息表
            long startTime = System.currentTimeMillis();
            // 插入转换
            hrRegist = imageConvertService.convertHrRegisterSftp(hrRegist);
            //之前的插入方法
            fcHrRegistTempMapper.insertSelectiveByHrRegist(hrRegist);
            long endTime = System.currentTimeMillis();
            log.info("插入所用时间" + ((endTime - startTime) / 1000.0) + "秒");
        }

    }

    /**
     * <AUTHOR>
     * @description校验注册信息
     * @date 16:03 16:03
     * @modified
     */
    public String checkRegist(HrRegist hrRegist) {
        //判断传来的数据是否为空
        if (hrRegist == null) {
            return "你输入的数据为空";
        }
        //管理员修改企业信息时校验
        if (!hrRegist.getGrpIdNo().equals(hrRegist.getOldGrpIdNo()) && StringUtils.isNotBlank(hrRegist.getOldGrpName())
                && StringUtils.isNotBlank(hrRegist.getOldCorporationMan())
                && StringUtils.isNotBlank(hrRegist.getOldGrpIdType())
                && StringUtils.isNotBlank(hrRegist.getOldGrpIdNo())) {
            int count = fcGrpContactMapper.selectGrpFCHrRegistTemp(hrRegist);
            if (count > 0) {
                return "该企业已存在";
            }
        }
        //申请注册HR时进行校验
        if (StringUtils.isBlank(hrRegist.getOldGrpIdType()) && StringUtils.isBlank(hrRegist.getOldGrpIdNo())
                && StringUtils.isBlank(hrRegist.getOldCorporationMan())
                && StringUtils.isBlank(hrRegist.getOldGrpName())) {
            int count = fcGrpContactMapper.selectGrpFCHrRegistTemp(hrRegist);
            if (count > 0) {
                return "该企业已存在";
            }
        }
        int checkint = fdUserMapper.checkByPhone(hrRegist.getMobilePhone(), hrRegist.getIdNo(), "2");
        if (checkint > 0) {
            return "该手机号已注册！";
        }
        return "";
    }

    /**
     * <AUTHOR>
     * @description将注册信息存入企业信息表
     * @date 16:03 16:03
     * @modified
     */
    public FCGrpInfo insertcGrpInfo(HrRegist hrRegist) {
        FCGrpInfo fcGrpInfo = new FCGrpInfo();
        //企业客户号
        fcGrpInfo.setGrpNo(maxNoService.createMaxNo("GrpNo", null, 20));
        //公司名称
        fcGrpInfo.setGrpName(hrRegist.getGrpName());
        //邮编
        fcGrpInfo.setZipCode(hrRegist.getZipCode());
        //企业证件图片1
        fcGrpInfo.setGrpIDImage1(hrRegist.getGrpIDImage1());
        //企业证件图片2
        fcGrpInfo.setGrpIDImage2(hrRegist.getGrpIDImage2());
        //公司性质
        fcGrpInfo.setGrpType(hrRegist.getGrpType());
        //开户账户名名称
        fcGrpInfo.setAccName(hrRegist.getAccName());
        //开户账号
        fcGrpInfo.setGrpBankAccNo(hrRegist.getGrpBankaccno());
        //统一社会信用代码
        fcGrpInfo.setUnifiedsociCode(hrRegist.getUnifiedsociCode());
        //注册地址
        fcGrpInfo.setRegaddress(hrRegist.getRegAddress());
        //开户编码*
        fcGrpInfo.setGrpBankCode(hrRegist.getGrpBankcode());
        //企业注册电话号
        fcGrpInfo.setTelphone(hrRegist.getTelphone());
        //企业地址
        fcGrpInfo.setGrpAddRess(hrRegist.getGrpAddress());
        //业务员工号
        fcGrpInfo.setClientno(hrRegist.getClientNo());
        //企业法人代表
        fcGrpInfo.setCorporationMan(hrRegist.getCorporationMan());
        fcGrpInfo = (FCGrpInfo) CommonUtil.initObject(fcGrpInfo, "INSERT");
        return fcGrpInfo;
    }

    /**
     * <AUTHOR>
     * @description将注册信息存入用户表
     * @date 16:03 16:03
     * @modified
     */
    public FdUser insertFdUser(HrRegist hrRegist) {
        FdUser fdUser = new FdUser();
        LisIDEA encryPassword = new LisIDEA();
        //用户编号
        fdUser.setUserNo(maxNoService.createMaxNo("UserNo", null, 20));
        //用户类型
        fdUser.setCustomType("2");
        //登陆姓名为证件号
        fdUser.setUserName(hrRegist.getIdNo());
        //hr证件号
        fdUser.setIDNo(hrRegist.getIdNo());
        //用户密码为证件号后6位
        fdUser.setPassWord(encryPassword.encryptString(hrRegist.getIdNo().substring(hrRegist.getIdNo().length() - 6)));
        //手机号
        fdUser.setPhone(hrRegist.getMobilePhone());
        //是否锁定 0：没锁定 1：锁定
        fdUser.setIsLock("0");
        //是否是VIP
        fdUser.setIsVIP("N");
        //用户状态
        fdUser.setUserState("0");
        //登录失败次数
        fdUser.setLoginFailTimes(0);
        //操作员
        fdUser.setOperator(fdUser.getUserNo());
        fdUser = (FdUser) CommonUtil.initObject(fdUser, "INSERT");
        return fdUser;
    }

    /**
     * <AUTHOR>
     * @description将注册信息存入联系人表
     * @date 16:03 16:03
     * @modified
     */
    public FcGrpContact insertFcGrpContact(HrRegist hrRegist) {
        FcGrpContact fcGrpContact = new FcGrpContact();
        //联系人编号
        fcGrpContact.setContactNo(maxNoService.createMaxNo("ContactNo", null, 20));
        //联系人类型
        fcGrpContact.setContactType("01");
        //联系人姓名
        fcGrpContact.setName(hrRegist.getName());
        //性别
        fcGrpContact.setSex(hrRegist.getSex());
        //证件号
        fcGrpContact.setIdNo(hrRegist.getIdNo());
        //证件图片1(正面)
        fcGrpContact.setIdImage1(hrRegist.getIdImage1());
        //证件图片2(反面)
        fcGrpContact.setIdImage2(hrRegist.getIdImage2());
        //手机号码
        fcGrpContact.setMobilePhone(hrRegist.getMobilePhone());
        //证件类型
        fcGrpContact.setIdType(hrRegist.getIdType());
        //出生日期
        fcGrpContact.setBirthDay(hrRegist.getBirthday());
        //操作员
        fcGrpContact.setOperator(hrRegist.getName());
        fcGrpContact = (FcGrpContact) CommonUtil.initObject(fcGrpContact, "INSERT");
        return fcGrpContact;
    }

    /**
     * <AUTHOR>
     * @description将注册日期及密码存入历史密码表将注册日期及密码存入历史密码表
     * @date 16:02 16:02
     * @modified
     */
    public FDPwdHist insertFdPwdHist(HrRegist hrRegist) {
        FDPwdHist fdPwdHist = new FDPwdHist();
        //登录密码流水号
        fdPwdHist.setPassWordSN(maxNoService.createMaxNo("PassWordSN", null, 20));
        fdPwdHist = (FDPwdHist) CommonUtil.initObject(fdPwdHist, "INSERT");
        return fdPwdHist;
    }

    /**
     * <AUTHOR>
     * @description 将注册信息存入注册审核表中
     * @date 19:25 19:25
     * @modified
     */
    public FcGrpRegAudit insertFcGrpRegAudit() {
        FcGrpRegAudit fcGrpRegAudit = new FcGrpRegAudit();
        //企业注册流水号
        fcGrpRegAudit.setGrpRegNo(maxNoService.createMaxNo("grpRegNo", null, 20));
        //审核结论
        fcGrpRegAudit.setAuditResult("0");
        fcGrpRegAudit = (FcGrpRegAudit) CommonUtil.initObject(fcGrpRegAudit, "INSERT");
        return fcGrpRegAudit;
    }


    /**
     * <AUTHOR>
     * @description 将注册信息存入用户角色表
     * @date 19:27 19:27
     * @modified
     */
    public FDUserRole insertFDUserRole() {
        FDUserRole fdUserRole = new FDUserRole();
        //用户角色流水号
        fdUserRole.setUserRoleSN(maxNoService.createMaxNo("UserRoleSN", null, 20));
        //角色类型
        fdUserRole.setRoleType("2");
        fdUserRole = (FDUserRole) CommonUtil.initObject(fdUserRole, "INSERT");
        return fdUserRole;
    }


    /**
     * <AUTHOR>
     * @description根据用户名获取原密码
     * @date 16:04 16:04
     * @modified
     */
    public String getPasswordByUserNo(String userNo) {
        //获取用户的密码
        String password = fdUserMapper.selectByPrimaryKey(userNo).getPassWord();
        //密码解密
        LisIDEA encryPassword = new LisIDEA();
        String originPassword = encryPassword.decryptString(password);
        return originPassword;
    }

    /**
     * <AUTHOR>
     * @description 修改密码
     * @date 19:24 19:24
     * @modified
     */
    @Transactional
    public String modifyPasswordByUsername(String password, String newpassword, String Authorization) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            /*获取用户编号*/
            String userNo = getSession(Authorization).getUserNo();
            String checkModifyPwd = checkModifyPwd(password, userNo);
            if ("TRUE".equals(checkModifyPwd)) {
                String key1 = "a1b2c3d4e5";
                String key2 = "p1o2i3u4y5";
                String key3 = "abcdefg";
                DesUtilTwo desObj = new DesUtilTwo();
                String mPassWord = desObj.strDec(newpassword, key1, key2, key3);

                //add by zch 2020/11/12 提示新密码不满足数字加字母的以及大于8位的要求
                if (mPassWord.matches("^[A-Za-z0-9]+$")) {
                    if ((mPassWord.matches("^[A-Za-z]+$") ||
                            (mPassWord.matches("^[0-9]*[1-9][0-9]*$"))) || mPassWord.length() < 8) {
                        resultMap.put("code", "500");
                        resultMap.put("message", "新密码不满足数字加字母的以及大于8位的要求");
                        return JSON.toJSONString(resultMap);
                    }
                }

                LisIDEA encryPassword = new LisIDEA();
                String newpasswordStr = encryPassword.encryptString(mPassWord);
                /*查询到当前用户*/
                FdUser fdUser = fdUserMapper.selectByPrimaryKey(userNo);
                /*修改用户表密码*/
                fdUser.setPassWord(newpasswordStr);
                //更新初始密码修改状态为1
                fdUser.setPWDState("1");
                //更新密码变更日期 add by zch 2020/11/12
                fdUser.setPWDChangeDate(DateTimeUtil.getCurrentDate());
                fdUser = (FdUser) CommonUtil.initObject(fdUser, "UPDATE");
                fdUserMapper.updateByPrimaryKeySelective(fdUser);
                /*修改历史密码表*/
                FDPwdHist fdPwdHist = new FDPwdHist();
                fdPwdHist.setPassWordSN(maxNoService.createMaxNo("PassWordSN", null, 20));
                fdPwdHist.setPassWord(newpasswordStr);
                // 默认为6个月的有效期
                String appointDateReturnDateTime = DateTimeUtil.getAppointDateReturnDateTime(new Date(), 6, "M");
                fdPwdHist.setPassWordInvalidTime(appointDateReturnDateTime);
                fdPwdHist.setUserNo(fdUser.getUserNo());
                fdPwdHist.setOperator(fdUser.getUserName());
                fdPwdHist = (FDPwdHist) CommonUtil.initObject(fdPwdHist, "INSERT");
                fdPwdHistMapper.insert(fdPwdHist);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "修改密码成功");
            } else {
                resultMap.put("code", "500");
                resultMap.put("message", checkModifyPwd);
            }
        } catch (Exception e) {
            log.info("修改失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "修改失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description修改密码信息校验
     * @date 11:59 11:59
     * @modified
     */
    public String checkModifyPwd(String password, String userNo) {
        /*比较密码是否相同*/
        String originPassword = getPasswordByUserNo(userNo);
        String md5Password = Base64AndMD5Util.md5Encode(originPassword);
        if (!(password.equals(md5Password))) {
            return "原密码错误";
        }
        return "TRUE";
    }

    /**
     * <AUTHOR>
     * @description 创建token
     * @date 17:08 17:08
     * @modified
     */
    public String makeToken(String Authorization, String urlType) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = getSession(Authorization);
        FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
        // 将五要素数据放到redis中
        Map<String, Object> map = new HashMap<>();
        map.put("name", fcPerInfo.getName());
        map.put("certificateNo", fcPerInfo.getIDNo());
        map.put("certificateType", fcPerInfo.getIDType());
        map.put("birthday", fcPerInfo.getBirthDay());
        map.put("gender", fcPerInfo.getSex());
        // 生成token
        String token = UUID.randomUUID().toString().replaceAll("-", "");
        // 将token放入redis缓存中
        Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
        redisUtil.put(token, JSON.toJSONString(map), loginValidity);
        try {
            // 添加token表中的数据
            FCUserToken fcUserToken = new FCUserToken();
            fcUserToken.setUserNo(globalInput.getUserNo());
            fcUserToken.setIsValid("1");
            fcUserToken.setToken(token);
            fcUserToken = (FCUserToken) CommonUtil.initObject(fcUserToken, "INSERT");
            // 将token的数据导入数据库中
            fcUserTokenMapper.insert(fcUserToken);
            //查询跳转url
            FDSysVar fdSysVar = fdSysVarMapper.selectByPrimaryKey(urlType);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("token", token);
            dataMap.put("url", fdSysVar.getSysvarvalue());
            // 将token返回给前台
            resultMap.put("message", "token创建成功");
            resultMap.put("code", "200");
            resultMap.put("data", dataMap);
        } catch (Exception e) {
            log.info("创建token失败", e);
            resultMap.put("message", "token创建失败");
            resultMap.put("code", "500");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description发送找回密码激活码
     * @date 16:49 16:49
     * @modified
     */
    public String findUserInfo(String idNo, String phone, String customType) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            String checkUserInfoNull = checkUserInfoNull(idNo, phone);
            // 判断证件号和手机号是否为空
            if (checkUserInfoNull == null || "".equals(checkUserInfoNull)) {
                FdUser fdUser = fdUserMapper.findUserInfo(idNo, phone, customType);
                //判断获取得用户是否存在
                if (fdUser == null) {
                    resultMap.put("message", "该用户不存在");
                    resultMap.put("code", "500");
                    return JSON.toJSONString(resultMap);
                }
                //获取随机的6位激活码
                String validateCode = randNum();
                //短信发送
                SendSMSReq sendSMSReq = new SendSMSReq();
                sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_007.getCode());
                sendSMSReq.setPhones(phone);
                Map<String, Object> map = new HashMap<>();
                map.put("validate_code", validateCode);
                sendSMSReq.setParam(map);
                SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
                //将激活码发送短信到用户手机
                if (!sendMessageResp.getSuccess()) {
                    resultMap.put("message", "发送激活码失败");
                    resultMap.put("code", "500");
                    return JSON.toJSONString(resultMap);
                }
                log.info("激活码：" + validateCode);
                //将第一次动态信息添加到动态密码表
                FDValidateCode fdValidateCode = new FDValidateCode();
                SimpleDateFormat FORMAT2 = new SimpleDateFormat("yyyy-MM-dd");
                SimpleDateFormat FORMAT1 = new SimpleDateFormat("HH:mm:ss");
                Calendar can = Calendar.getInstance();
                //开始日期
                String date1 = FORMAT2.format(can.getTime());
                //开始时间
                String time1 = FORMAT1.format(can.getTime());

                can.add(Calendar.MINUTE, +5);
                //结束日期
                String date2 = FORMAT2.format(can.getTime());
                //结束时间
                String time2 = FORMAT1.format(can.getTime());
                fdValidateCode.setValidatecodesn(maxNoService.createMaxNo("ValidateCodeSN", null, 20));
                fdValidateCode.setUserno(fdUser.getUserNo());
                fdValidateCode.setValidatecode(validateCode);
                fdValidateCode.setCodetype("01");
                fdValidateCode.setIsvalid("1");
                fdValidateCode.setMakedate(date1);
                fdValidateCode.setMaketime(time1);
                fdValidateCode.setEnddate(date2);
                fdValidateCode.setEndtime(time2);
                fdValidateCodeMapper.insert(fdValidateCode);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "发送激活码成功！");
            }
        } catch (Exception e) {
            log.info("获取动态密码失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "获取动态密码失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description校验证件号和手机号是否为空
     * @date 16:52 16:52
     * @modified
     */
    public String checkUserInfoNull(String idNo, String phone) {
        Map<String, Object> map = new HashMap<>();
        if (idNo == null || "".equals(idNo)) {
            map.put("message", "证件号不能为空");
            return JSON.toJSONString(map);
        }
        if (phone == null || "".equals(phone)) {
            map.put("message", "手机号不能为空");
            return JSON.toJSONString(map);
        }
        return "";
    }

    /**
     * <AUTHOR>
     * @description随机生成6为动态密码
     * @date 17:26 17:26
     * @modified
     */
    public String randNum() {
        String code = "";
        Random random = new Random();
        for (int i = 0; i < 6; i++) {
            code += random.nextInt(10);
        }
        System.out.println(code);
        return code;
    }

    /**
     * <AUTHOR>
     * @description 找回密码及修改动态密码状态
     * @date 20:22 20:22
     * @modified
     */
    public String backPasswordInfo(String idNo, String phone, String customType, String phoneCode) {
        Map<String, Object> resultMap = new HashMap<>();
        if (idNo == null || "".equals(idNo)) {
            resultMap.put("message", "证件号不能为空");
            return JSON.toJSONString(resultMap);
        }
        if (phone == null || "".equals(phone)) {
            resultMap.put("message", "电话号不能为空");
            return JSON.toJSONString(resultMap);
        }
        if (phoneCode == null || "".equals(phoneCode)) {
            resultMap.put("message", "验证码不能为空");
            return JSON.toJSONString(resultMap);
        }
        try {
            FdUser fdUser = fdUserMapper.findUserInfo(idNo, phone, customType);
            if (fdUser == null) {
                resultMap.put("code", "500");
                resultMap.put("message", "用户不存在");
                return JSON.toJSONString(resultMap);
            }
            //短信校验
            String checkResult = checkPhoneCode(fdUser.getUserNo(), phoneCode, "01");
            if (checkResult != null) {
                resultMap.put("code", "500");
                resultMap.put("message", checkResult);
                return JSON.toJSONString(resultMap);
            }
            //生成六位随机新密码
            String password = randNum();
            //短信发送
            SendSMSReq sendSMSReq = new SendSMSReq();
            sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_009.getCode());
            sendSMSReq.setPhones(phone);
            Map<String, Object> map = new HashMap<>();
            map.put("password", password);
            sendSMSReq.setParam(map);
            SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
            if (!sendMessageResp.getSuccess()) {
                resultMap.put("message", "发送新密码失败");
                resultMap.put("code", "500");
                return JSON.toJSONString(resultMap);
            }
            //新密码加密，保存数据库
            LisIDEA encryPassword = new LisIDEA();
            password = encryPassword.encryptString(password);

            //更新用户表
            fdUser.setPassWord(password);
            fdUser.setIsLock("0");
            fdUser.setLoginFailTimes(0);
            fdUser = (FdUser) CommonUtil.initObject(fdUser, "UPDATE");
            fdUserMapper.updateByPrimaryKey(fdUser);
            //导入密码历史表
            FDPwdHist fdPwdHist = new FDPwdHist();
            fdPwdHist.setPassWordSN(maxNoService.createMaxNo("PassWordSN", null, 20));
            fdPwdHist.setUserNo(fdUser.getUserNo());
            fdPwdHist.setPassWord(password);
            // 默认为6个月的有效期
            String appointDateReturnDateTime = DateTimeUtil.getAppointDateReturnDateTime(new Date(), 6, "M");
            fdPwdHist.setPassWordInvalidTime(appointDateReturnDateTime);
            fdPwdHist.setOperator(fdUser.getUserName());
            fdPwdHist = (FDPwdHist) CommonUtil.initObject(fdPwdHist, "INSERT");
            fdPwdHistMapper.insert(fdPwdHist);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "找回密码成功");
        } catch (Exception e) {
            log.info("找回密码失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "找回密码失败");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * <AUTHOR>
     * @description查询菜单
     * @date 17:25 17:25
     * @modified
     */
    public String FindMenuInfo(String token, FindMenuInfoReq findMenuInfoReq) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = getSession(token);
            Map<String, Object> data = new HashMap<>();
            //获取该用户所有的菜单
            findMenuInfoReq.setUserNo(globalInput.getUserNo());
            List<HashMap<String, Object>> fDmenuList = fDmenuMapper.findMenuInfo1(findMenuInfoReq);
            //封装用户菜单层级
            List<HashMap<String, Object>> menuInfos = new ArrayList<HashMap<String, Object>>();
            for (int i = 0; i < fDmenuList.size(); i++) {
                List<HashMap<String, Object>> itemMenus = new ArrayList<HashMap<String, Object>>();
                for (int j = 0; j < fDmenuList.size(); j++) {
                    if (2 == (Integer) fDmenuList.get(j).get("NodeLevel")
                            && fDmenuList.get(j).get("ParentNodeCode").equals(fDmenuList.get(i).get("NodeCode"))) {
                        itemMenus.add(fDmenuList.get(j));
                    }
                }
                if (1 == (Integer) fDmenuList.get(i).get("NodeLevel")) {
                    if (itemMenus.size() > 0) {
                        fDmenuList.get(i).put("itemMenus", itemMenus);
                    }
                    menuInfos.add(fDmenuList.get(i));
                }
            }
            data.put("name", globalInput.getName());
            data.put("userType", globalInput.getCustomType());
            data.put("nickName", globalInput.getNickName());
            data.put("sex", globalInput.getSex());
            data.put("oldLoginDate", globalInput.getOldLoginDate());
            data.put("oldLoginTime", globalInput.getOldLoginTime());
            // 主要用于区分审核管理员、复核管理员 3--审核 4--复核（总公司） 5--复核（分公司）
            data.put("menugrpcode", menuInfos.get(0).get("menugrpcode"));
            data.put("menuInfos", menuInfos);
            String grpNo = globalInput.getGrpNo();
            if (!"".equals(grpNo) && grpNo != null) {
                FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(grpNo);
                data.put("grpName", fcGrpInfo.getGrpName());
            }
            resultMap.put("data", data);
            resultMap.put("code", "200");
            resultMap.put("message", "查询菜单成功");
        } catch (Exception e) {
            log.info("查询菜单失败：", e);
            resultMap.put("code", "500");
            resultMap.put("message", "查询菜单失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description查询码表
     * @date 16:04 16:04
     * @modified
     */
    public String findCodeInfo(String codeTypeStr) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (!codeTypeStr.matches("[0-9A-Za-z_]*")) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "查询失败");
            } else {
                // 将获的字符串进行分割
                String[] codeArray = codeTypeStr.split("_");
                //遍历数组
                HashMap<String, Object> map = new HashMap<>();
                for (String s : codeArray) {
                    //查询数据库中codeType字段下的数据
                    List<HashMap<String, Object>> code = fdCodeMapper.findCodeInfo(s);
                    //将数据以codeType为键的
                    map.put(s, code);
                }
                resultMap.put("codeList", map);
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "查询成功");
            }
        } catch (Exception e) {
            log.info("查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询机构编码
     *
     * @return
     */
    public String findComCode() {
        List<SelectAllManageComResp> selectAllManageComResps = fdComMapper.selectAllManageCom(new FdCom());
        return JSONObject.toJSONString(ResponseResultUtil.success(selectAllManageComResps));
    }

    /**
     * <AUTHOR>
     * @description校验业务员工号 * @date 14:46 14:46
     * @modified
     */
    public String getClientoInfo(String cliento) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //判断入参是否为空
            if (cliento == null || "".equals(cliento)) {
                resultMap.put("message", "业务员工号不能为空");
                return JSON.toJSONString(resultMap);
            }
            //查询业务员信息
            List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(cliento);
            if (fdAgentInfoList.size() < 1) {
                resultMap.put("message", "该工号的数据不存在");
                return JSON.toJSONString(resultMap);
            }
            Map<String, String> map = new HashMap<>();
            map.put("name", fdAgentInfoList.get(0).getName());
            map.put("mobile", fdAgentInfoList.get(0).getMobile());
            map.put("cliento", fdAgentInfoList.get(0).getAgentCode());
            resultMap.put("clientoInfo", map);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询业务员工号信息成功");
        } catch (Exception e) {
            log.info("查询失败", e);
            resultMap.put("success", true);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description 企业OCR
     * @date 13:31 13:31
     * @modified
     */
    public String grpOCRInfo(HrRegist hrRegist) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //第三方ocr地址
            String url = myProps.getEtsign().get("url");
            //封装成JSON格式数据
            Map<String, String> map = new HashMap<>();
            map.put("name", hrRegist.getGrpName());
            map.put("codeUSC", hrRegist.getGrpIdNo());
            map.put("legalName", hrRegist.getCorporationMan());
            String json = JSON.toJSONString(map);
            //验证加缓存
            String grpNo = redisUtil.get(hrRegist.getGrpIdNo());

            if (StringUtils.isEmpty(grpNo)) {

                return checkGrp(hrRegist, resultMap, url, json);
            } else {
                HrRegist cache = JSON.parseObject(grpNo, HrRegist.class);
                boolean flag = Boolean.FALSE;
                if (!cache.getGrpName().equals(hrRegist.getGrpName())) {
                    flag = Boolean.TRUE;
                }
                if (!cache.getGrpIdNo().equals(hrRegist.getGrpIdNo())) {
                    flag = Boolean.TRUE;
                }
                if (!cache.getCorporationMan().equals(hrRegist.getCorporationMan())) {
                    flag = Boolean.TRUE;
                }
                if (flag) {
                    log.info("企业验真失败：缓存验证结果不一致调用E签宝企业验证请求!!!");
                    return checkGrp(hrRegist, resultMap, url, json);
                }
            }
            log.info("企业验真成功：缓存验证结果!!!");
            resultMap.put("code", "200");
            resultMap.put("message", "成功");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("OCR注册失败：", e);
            resultMap.put("code", "500");
            resultMap.put("message", "OCR注册失败");
        }
        return JSON.toJSONString(resultMap);
    }

    private String checkGrp(HrRegist hrRegist, Map<String, Object> resultMap, String url, String json) throws Exception {
        //将字符串数据转为byte数组
        byte[] byteJson = json.getBytes(StandardCharsets.UTF_8);

        //将请求数据进行hmac256Encode加密
        String result = Base64AndMD5Util.hmac256Encode(myProps.getEtsign().get("ProjectSecret"), json);
        //发送请求及获取相应信息;
        log.info("E签宝企业验证请求 :urlPath={},jsonStr={}", url, json);
        Map<String, Object> src = HttpUrlUtil.postJson(url, byteJson, result, myProps);
        if (src.get("errCode").equals("0") || src.get("errCode") == "0") {
            redisUtil.putDay(hrRegist.getGrpIdNo(), JSON.toJSONString(hrRegist), 7);
            resultMap.put("code", "200");
            resultMap.put("message", src.get("msg"));
            return JSON.toJSONString(resultMap);
        } else {
            resultMap.put("code", "500");
            resultMap.put("message", "企业验真失败，" + src.get("msg"));
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 短信验证码
     *
     * @param userNo
     * @return
     * <AUTHOR>
     */
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED)
    public String msgVerificationCode(String userNo) {
        ResponseMsg<String> responseMsg = new ResponseMsg<>();
        FdUser fdUser = fdUserMapper.selectByPrimaryKey(userNo);

        // 用户和手机号非空验证
        if (fdUser == null) {
            responseMsg.errorStatus().message("用户不存在!");
            return JSON.toJSONString(responseMsg);
        }
        String phoneNo = fdUser.getPhone();
        if (phoneNo == null) {
            responseMsg.errorStatus().message("手机号不存在!");
            return JSON.toJSONString(responseMsg);
        }

        // 获取随机的6位验证码
        String validateCode = randNum();
        log.info("短信验证码：" + validateCode);
        //短信发送
        SendSMSReq sendSMSReq = new SendSMSReq();
        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_007.getCode());
        sendSMSReq.setPhones(phoneNo);
        Map<String, Object> map = new HashMap<>();
        map.put("validate_code", validateCode);
        sendSMSReq.setParam(map);
        SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
        if (!sendMessageResp.getSuccess()) {
            responseMsg.errorStatus().message(sendMessageResp.getMsg());
            return JSON.toJSONString(responseMsg);
        }
        // 将第一次动态信息添加到动态密码表
        FDValidateCode fdValidateCode = new FDValidateCode();
        SimpleDateFormat FORMAT2 = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat FORMAT1 = new SimpleDateFormat("HH:mm:ss");
        Calendar can = Calendar.getInstance();
        // 开始日期
        String date1 = FORMAT2.format(can.getTime());
        // 开始时间
        String time1 = FORMAT1.format(can.getTime());
        can.add(Calendar.MINUTE, +5);
        // 结束日期
        String date2 = FORMAT2.format(can.getTime());
        // 结束时间
        String time2 = FORMAT1.format(can.getTime());
        String maxNo = maxNoService.createMaxNo("ValidateCodeSN", null, 20);
        fdValidateCode.setValidatecodesn(maxNo);
        fdValidateCode.setUserno(fdUser.getUserNo());
        fdValidateCode.setValidatecode(validateCode);
        fdValidateCode.setCodetype("02");
        fdValidateCode.setIsvalid("1");
        fdValidateCode.setMakedate(date1);
        fdValidateCode.setMaketime(time1);
        fdValidateCode.setEnddate(date2);
        fdValidateCode.setEndtime(time2);
        fdValidateCodeMapper.insert(fdValidateCode);
        log.info("短信发送成功,手机号:" + phoneNo + ", 验证码:" + validateCode);
        responseMsg.okStatus().message("短信验证码成功");
        return JSON.toJSONString(responseMsg);
    }

    public String getImageInfo(String imageInfo) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "200");
        return JSON.toJSONString(resultMap);
    }


    public String getOccupationCode(String codeType, String codeDesc) {
        Map<String, Object> resultMap = new HashMap<>();
        List<HashMap<String, Object>> code = fdCodeMapper.getOccupationCode(codeType, codeDesc);
        resultMap.put("codelist", code);
        resultMap.put("code", "200");
        resultMap.put("message", "查询成功");
        return JSON.toJSONString(resultMap);
    }

    public String checkPeople(Map<String, String> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("message", "校验通过！");
        resultMap.put("code", "200");
        //校验姓名
        String nameError = "";
        String idType = map.get("idType");
        String name = map.get("name");
        if (!StringUtil.isEmpty(idType) && !StringUtil.isEmpty(name)) {
            if (idType.equals("1")) {
                nameError = CheckUtils.checkForeignName(name);
                String trim = name.trim();
                name = trim.replaceAll(" +", " ");
            } else {
                nameError = CheckUtils.checkChineseName(name);
            }
            if (!StringUtil.isEmpty(nameError)) {
                return JSON.toJSONString(ResultUtil.error(nameError));
            }
        }
        //校验国籍
        String checkNationalityCode = addressCheckService.checkNationalityCode(map.get("nativeplace"));
        if (StringUtils.isNotBlank(checkNationalityCode)) {
            checkNationalityCode = checkNationalityCode.replace("***", name);
            resultMap.put("message", checkNationalityCode);
            resultMap.put("code", "500");
            return JSON.toJSONString(resultMap);
        }

        // 公用的校验个人信息的接口
        String resultMsg = CheckUtils.checkSinglePeople(map);
        if (StringUtils.isNotBlank(resultMsg)) {
            if (map.get("sign").equals("3")) {
                resultMsg = "经办人 " + resultMsg;
            }
            resultMap.put("message", resultMsg);
            resultMap.put("code", "500");
        }
        List<IdCardVerifyRequest.Verify> list = new ArrayList<>();
        list.add(IdCardVerifyRequest.Verify.builder()
                .idCard(map.get("idNo"))
                .name(name)
                .build());
        //校验二要素
        String failVerifies = addressCheckService.checkIdCard(list);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(failVerifies)) {
            return JSON.toJSONString(ResultUtil.error(failVerifies));
        }
        return JSON.toJSONString(resultMap);
    }

    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED)
    public String sendMessageCode(String phone, String customType) {
        ResponseMsg<String> responseMsg = new ResponseMsg<>();
        List<FdUser> userinfoList = fdUserMapper.findUserInfoByPhone(phone, customType);
        // 用户和手机号非空验证
        if (userinfoList == null || userinfoList.size() == 0) {
            responseMsg.errorStatus().message("手机号不存在!");
            return JSON.toJSONString(responseMsg);
        } else if (userinfoList != null && userinfoList.size() > 1) {
            List<String> idnoList = new ArrayList<String>();
            for (FdUser fdUser : userinfoList) {
                String idNo = fdUser.getIDNo();
                if (!idnoList.contains(idNo)) {
                    idnoList.add(idNo);
                }
            }
            if (idnoList != null && idnoList.size() > 1) {
                responseMsg.errorStatus().message("当前手机号存在不同用户!");
                return JSON.toJSONString(responseMsg);
            }
        }
        // 获取随机的6位验证码
        String validateCode = randNum();
        log.info("短信验证码：" + validateCode);

        //短信发送
        SendSMSReq sendSMSReq = new SendSMSReq();
        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_007.getCode());
        sendSMSReq.setPhones(phone);
        Map<String, Object> map = new HashMap<>();
        map.put("validate_code", validateCode);
        sendSMSReq.setParam(map);
        SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
        if (sendMessageResp.getSuccess()) {
            responseMsg.errorStatus().message(sendMessageResp.getMsg());
            return JSON.toJSONString(responseMsg);
        }

        // 将第一次动态信息添加到动态密码表
        FDValidateCode fdValidateCode = new FDValidateCode();
        SimpleDateFormat FORMAT2 = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat FORMAT1 = new SimpleDateFormat("HH:mm:ss");
        Calendar can = Calendar.getInstance();
        // 开始日期
        String date1 = FORMAT2.format(can.getTime());
        // 开始时间
        String time1 = FORMAT1.format(can.getTime());
        can.add(Calendar.MINUTE, +5);
        // 结束日期
        String date2 = FORMAT2.format(can.getTime());
        // 结束时间
        String time2 = FORMAT1.format(can.getTime());
        String maxNo = maxNoService.createMaxNo("ValidateCodeSN", null, 20);
        fdValidateCode.setValidatecodesn(maxNo);
        fdValidateCode.setUserno(userinfoList.get(0).getUserNo());
        fdValidateCode.setValidatecode(validateCode);
        fdValidateCode.setCodetype("03");
        fdValidateCode.setIsvalid("1");
        fdValidateCode.setMakedate(date1);
        fdValidateCode.setMaketime(time1);
        fdValidateCode.setEnddate(date2);
        fdValidateCode.setEndtime(time2);
        fdValidateCodeMapper.insert(fdValidateCode);
        log.info("短信发送成功,手机号:" + phone + ", 验证码:" + validateCode);
        responseMsg.okStatus().message("短信验证码发送成功");
        return JSON.toJSONString(responseMsg);
    }

    @Transactional
    public String loginByPhoneCode(String phone, String customType, String phoneCode, HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<>();
        String loginIp = IpUtil.getIpAddr(request);
        log.info("登录IP：" + loginIp);
        //参数校验
        List<FdUser> userinfoList = fdUserMapper.findUserInfoByPhone(phone, customType);
        // 用户和手机号非空验证
        if (userinfoList == null || userinfoList.size() == 0) {
            resultMap.put("code", "1");
            resultMap.put("msg", "手机号不存在!");
            return JSON.toJSONString(resultMap);
        } else if (userinfoList != null && userinfoList.size() > 1) {
            List<String> idnoList = new ArrayList<String>();
            for (FdUser fdUser : userinfoList) {
                String idNo = fdUser.getIDNo();
                if (!idnoList.contains(idNo)) {
                    idnoList.add(idNo);
                }
            }
            if (idnoList != null && idnoList.size() > 1) {
                resultMap.put("code", "1");
                resultMap.put("msg", "当前手机号存在不同用户");
                return JSON.toJSONString(resultMap);
            }
        }
        try {
            FdUser userInfo = userinfoList.get(0);
            // 验证码校验
            String checkResult = checkPhoneCode(userInfo.getUserNo(), phoneCode, "03");
            if (checkResult != null) {
                resultMap.put("code", "1");
                resultMap.put("msg", checkResult);
                return JSON.toJSONString(resultMap);
            }
            Map<String, Object> dataMap = new HashMap<>();
            //校验初始密码修改状态是否为1，为1说明是非首次登录，直接进入登录操作，否则需要返回前台发送短信验证码
            String token = dealLogin(userInfo.getUserNo(), userInfo.getCustomType(), false, loginIp);
            dataMap.put("token", token);
            dataMap.put("userNo", userInfo.getUserNo());
            dataMap.put("PWDState", userInfo.getPWDState());
            // 返回前台数据
            resultMap.put("code", "0");
            resultMap.put("msg", "登录成功！");
            resultMap.put("data", dataMap);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("登录失败！：", e);
            resultMap.put("code", "1");
            resultMap.put("msg", "登录失败！");
        }

        return JSON.toJSONString(resultMap);
    }

    /**
     * 短信验证码登录，发送验证码方法
     *
     * @param phone 前台传的 手机号
     * @param type  前台传的 用户类型
     * @return 返回发送结果
     */
    @Transactional
    public Map getPhoneLoginCode(String phone, String type) {
        //-------------------------------------数据校验-------------------------------------//

        List list;
        if (phone == null || (phone = phone.trim()).equals("")) return ResultUtil.error("手机号不能为空！");
        if ((list = fdUserMapper.checkPhone(phone, type)) == null || list.size() == 0) {
            return ResultUtil.error("2".equals(type) ? "仅支持HR登录" : "该手机号未注册！");
        }

        //-------------------------------------数据初始化-------------------------------------//

        String VERIFY_CODES = "0123456789"; //常量码值

        String randomStr;                   //生成code
        int codeValidity;                   //有效期五分钟

        {//数据初始化
            randomStr = RandomStringUtils.random(6, VERIFY_CODES);
            codeValidity = Integer.parseInt(myProps.getTermValidityCode());
        }

        //-------------------------------------数据操作-------------------------------------//

        redisUtil.put(phone + type, randomStr, codeValidity);//存redis

        {//todo 生产环境把这块删掉
            FDValidateCode fdValidateCode = new FDValidateCode();
            fdValidateCode.setValidatecodesn(maxNoService.createMaxNo("ValidateCodeSN", null, 20));
            fdValidateCode.setUserno(phone);
            fdValidateCode.setValidatecode(randomStr);
            fdValidateCode.setCodetype("1");
            fdValidateCode.setIsvalid("0");
            fdValidateCode.setMakedate(DateTimeUtil.getCurrentDate());
            fdValidateCode.setMaketime(DateTimeUtil.getCurrentTime());
            fdValidateCode.setEnddate(DateTimeUtil.getCurrentDate());
            fdValidateCode.setEndtime(DateTimeUtil.getCurrentTime());
            fdValidateCodeMapper.insert(fdValidateCode);
        }

        //短信发送
        SendSMSReq sendSMSReq = new SendSMSReq();
        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_007.getCode());
        sendSMSReq.setPhones(phone);
        Map<String, Object> map = new HashMap<>();
        map.put("validate_code", randomStr);
        sendSMSReq.setParam(map);
        SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
        if (!sendMessageResp.getSuccess()) {
            return ResultUtil.error("短信发送失败！");
        }
        log.info("短信发送成功！验证码：" + randomStr);
        return ResultUtil.success("短信发送成功！", "");
    }

    /**
     * 短信验证码登录
     *
     * @param phone 前台传的 手机号
     * @param code  前台传的 验证码
     * @param type  前台传的 用户类型
     * @return 返回登录结果
     */
    @Transactional
    public Map phoneLogin(String phone, String code, String type, HttpServletRequest request) {
        String loginIp = IpUtil.getIpAddr(request);
        log.info("phoneLogin-登录IP：" + loginIp);

        //-------------------------------------数据初始化-------------------------------------//

        PageHelper.clearPage(); //这个是为了去掉这个插件在某个地方未关闭导致登录失败的bug
        String newtoken;        //反给前台的身份令牌
        FdUser userInfo;        //用户信息，校验用

        //-------------------------------------校验-----------------------------------//

        List<FdUser> list;
        if (phone == null || (phone = phone.trim()).equals("")) return ResultUtil.error("手机号不能为空！");
        if (code == null || (code = code.trim()).equals("")) return ResultUtil.error("验证码不能为空！");
        if (type == null || type.equals("")) return ResultUtil.error("账号类型不能为空！");
        if ((list = fdUserMapper.checkPhone(phone, type)) == null || list.size() == 0)
            return ResultUtil.error("该手机号未注册！");
        if (redisUtil.get(phone + type) == null) return ResultUtil.error("您输入的验证码错误或已过期！");
        if (!code.equals(Base64AndMD5Util.md5Encode(redisUtil.get(phone + type))))
            return ResultUtil.error("您输入的验证码错误或已过期！");
        userInfo = list.get(0);

        //-------------------------------------登录表操作-----------------------------------//

        redisUtil.remove(phone + type);//清空redis，按理说这个东西跟表没关系，但是放哪都不合适

        newtoken = dealLogin(userInfo.getUserNo(), userInfo.getCustomType(), true, loginIp);
        Map<String, Object> map = new HashMap<>();
        map.put("token", newtoken);
        map.put("userNo", userInfo.getUserNo());
        map.put("phoneIsFirst", !("1".equals(userInfo.getPWDState()) || "2".equals(userInfo.getPWDState())));//两个状态1非首次，2手机首次
        return ResultUtil.success("登录成功！", map, "0");
    }

    /**
     * 手机短信验证登录后修改第一次登录状态
     *
     * @param Authorization 身份令牌
     * @return 修改状态
     */
    @Transactional
    public Map agreement(String Authorization) {
        FdUser fdUser = new FdUser();
        fdUser.setUserNo(getSession(Authorization).getUserNo());
        fdUser.setPWDState("2");
        fdUserMapper.updateByPrimaryKeySelective(fdUser);
        return ResultUtil.success("状态修改成功！", "");
    }

    public String reciveCreateToken(Map<String, String> requestMap) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            String userNo = requestMap.get("userNo");
            String perNo = requestMap.get("perNo");
            String ensureCode = requestMap.get("ensureCode");
            GlobalInput userInfo = new GlobalInput();
            Map<String, Object> dataMap = new HashMap<>();
            userInfo = fdUserMapper.findGlobalInfoByUserNo(userNo);
            userInfo.setCustomNo(perNo);
            userInfo.setEnsureCode(ensureCode);
            // 生成token，将用户信息放入redis，token作为key，token返回前台
            String token = UUID.randomUUID().toString().replaceAll("-", "");
            //TODO 这里先写成-1 上线时需要改成10分钟
            Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
            redisUtil.put(token, JSON.toJSONString(userInfo), loginValidity);
            dataMap.put("token", token);
            dataMap.put("userNo", userInfo.getUserNo());
            // 返回前台数据
            resultMap.put("code", "0");
            resultMap.put("msg", "创建token成功！");
            resultMap.put("data", dataMap);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("创建token失败！：", e);
            resultMap.put("code", "1");
            resultMap.put("msg", "创建token失败！");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 审核岗用户忘记密码-手机号验证
     *
     * @param sendAuditUserMessageCodeReq
     */
    public void sendAuditUserMessageCode(SendAuditUserMessageCodeReq sendAuditUserMessageCodeReq) {

        /**
         * 获取请求数据
         */
        String phone = sendAuditUserMessageCodeReq.getPhone();
        String userType = sendAuditUserMessageCodeReq.getUserType();
        /**
         * 校验数据
         */
        if (org.springframework.util.StringUtils.isEmpty(phone)) {
            throw new SystemException("手机号不能为空！");
        } else if (!phone.matches("[1]{1}[0-9]{10}")) {
            throw new SystemException("手机号填写有误！");
        }
        List<FdUser> fdUserList = fdUserMapper.selectAuditUserPhone(phone, userType);
        if (fdUserList.size() > 1) {
            throw new SystemException("操作失败：该手机号绑定了多个账号！");
        } else if (fdUserList.size() == 0) {
            throw new SystemException("手机号对应绑定的用户不存在！");
        }
        FdUser fdUser = fdUserList.get(0);
        /**
         * 发送验证码
         */
        //获取随机的6位激活码
        String validateCode = randNum();
        //短信发送
        SendSMSReq sendSMSReq = new SendSMSReq();
        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_007.getCode());
        sendSMSReq.setPhones(phone);
        Map<String, Object> map = new HashMap<>();
        map.put("validate_code", validateCode);
        sendSMSReq.setParam(map);
        SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
        //将激活码发送短信到用户手机
        if (!sendMessageResp.getSuccess()) {
            throw new SystemException("短信验证码发送失败！");
        }
        log.info("短信验证码：" + validateCode);
        //将第一次动态信息添加到动态密码表
        SimpleDateFormat FORMAT2 = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat FORMAT1 = new SimpleDateFormat("HH:mm:ss");
        Calendar can = Calendar.getInstance();
        // 开始日期
        String date1 = FORMAT2.format(can.getTime());
        // 开始时间
        String time1 = FORMAT1.format(can.getTime());
        can.add(Calendar.MINUTE, +5);
        // 结束日期
        String date2 = FORMAT2.format(can.getTime());
        // 结束时间
        String time2 = FORMAT1.format(can.getTime());
        FDValidateCode fdValidateCode = new FDValidateCode();
        fdValidateCode.setValidatecodesn(maxNoService.createMaxNo("ValidateCodeSN", null, 20));
        fdValidateCode.setUserno(fdUser.getUserNo());
        fdValidateCode.setValidatecode(validateCode);
        fdValidateCode.setCodetype("01");
        fdValidateCode.setIsvalid("1");
        fdValidateCode.setMakedate(date1);
        fdValidateCode.setMaketime(time1);
        fdValidateCode.setEnddate(date2);
        fdValidateCode.setEndtime(time2);
        fdValidateCodeMapper.insert(fdValidateCode);

    }

    /**
     * 找回密码
     *
     * @param auditUserBackPasswordReq
     */
    public void auditUserBackPassword(AuditUserBackPasswordReq auditUserBackPasswordReq) {

        /**
         * 校验数据
         */
        if (StringUtils.isBlank(auditUserBackPasswordReq.getPhone())) {
            throw new SystemException("手机号不能为空！");
        }
        if (StringUtils.isBlank(auditUserBackPasswordReq.getPhoneCode())) {
            throw new SystemException("短信验证码不能为空！");
        }
        List<FdUser> fdUserList = fdUserMapper.selectAuditUserPhone(auditUserBackPasswordReq.getPhone(), auditUserBackPasswordReq.getUserType());
        if (fdUserList.size() > 1) {
            throw new SystemException("操作失败：该手机号绑定了多个账号！");
        } else if (fdUserList.size() == 0) {
            throw new SystemException("手机号对应绑定的用户不存在！");
        }
        FdUser fdUser = fdUserList.get(0);
        //短信校验
        String checkResult = checkPhoneCode(fdUser.getUserNo(), auditUserBackPasswordReq.getPhoneCode(), "01");
        if (checkResult != null) {
            throw new SystemException(checkResult);
        }

        //生成六位随机新密码
        String password = randNum();
        //短信发送
        SendSMSReq sendSMSReq = new SendSMSReq();
        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_009.getCode());
        sendSMSReq.setPhones(auditUserBackPasswordReq.getPhone());
        Map<String, Object> map = new HashMap<>();
        map.put("password", password);
        sendSMSReq.setParam(map);
        SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
        if (!sendMessageResp.getSuccess()) {
            throw new SystemException("发送新密码失败！");
        }
        //新密码加密，保存数据库
        LisIDEA encryPassword = new LisIDEA();
        password = encryPassword.encryptString(password);

        //更新用户表
        fdUser.setPassWord(password);
        fdUser.setIsLock("0");
        fdUser.setLoginFailTimes(0);
        fdUser = (FdUser) CommonUtil.initObject(fdUser, "UPDATE");
        fdUserMapper.updateByPrimaryKey(fdUser);
        //导入密码历史表
        FDPwdHist fdPwdHist = new FDPwdHist();
        fdPwdHist.setPassWordSN(maxNoService.createMaxNo("PassWordSN", null, 20));
        fdPwdHist.setUserNo(fdUser.getUserNo());
        fdPwdHist.setPassWord(password);
        fdPwdHist.setOperator(fdUser.getUserName());
        fdPwdHist = (FDPwdHist) CommonUtil.initObject(fdPwdHist, "INSERT");
        fdPwdHistMapper.insert(fdPwdHist);

    }

    public Map implicitlyLogin(String phone, HttpServletRequest request) {
        String loginIp = IpUtil.getIpAddr(request);
        Map<String, Object> map = new HashMap<>();
        log.info("phoneLogin-登录IP：" + loginIp);
        List<FdUser> list;
        if ((list = fdUserMapper.checkPhone(phone, "1")) == null || list.isEmpty()) {
            return ResultUtil.error("该手机号未注册！");
        }
        FdUser userInfo = list.get(0);
        String token = redisUtil.get(phone);
        if (StringUtils.isEmpty(token)) {
            String newtoken = dealLogin(userInfo.getUserNo(), userInfo.getCustomType(), true, loginIp);
            map.put("token", newtoken);
            map.put("userNo", userInfo.getUserNo());
            map.put("phoneIsFirst", !("1".equals(userInfo.getPWDState()) || "2".equals(userInfo.getPWDState())));//两个状态1非首次，2手机首次
            redisUtil.put(phone, newtoken, 480);
            return ResultUtil.success("登录成功！", map, "0");
        }
        map.put("token", token);
        map.put("userNo", userInfo.getUserNo());
        map.put("phoneIsFirst", !("1".equals(userInfo.getPWDState()) || "2".equals(userInfo.getPWDState())));//两个状态1非首次，2手机首次
        return ResultUtil.success("登录成功！", map, "0");
    }

    public ResponseResult<String> saveData(PolicyAnalysisCapitalField request) {
        String response = HttpUtil.postHttpRequestJson22(myProps.getLifeSaveData(),
                JSONObject.toJSONString(SaveDataRequest.builder()
                        .activityCapitalName("保单解析留资线索留资")
                        .activityCapitalCode("baodanjiexiliuzi")
                        .activityCapitalType(4)
                        .policyAnalysisCapitalField(request).build()));
        JSONObject parse = JSONObject.parseObject(response);
        String code = parse.getString("code");
        if (code.equals("200")) {
            return ResponseResultUtil.success("请求成功");
        } else {
            return ResponseResultUtil.success("400", "请求失败", "");
        }
    }


}
