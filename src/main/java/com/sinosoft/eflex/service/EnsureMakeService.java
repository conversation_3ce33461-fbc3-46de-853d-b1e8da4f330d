package com.sinosoft.eflex.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.dao.InsurePlan.FcInsurePlanMapper;
import com.sinosoft.eflex.dao.InsurePlan.FcInsurePlanOrderMapper;
import com.sinosoft.eflex.dao.InsurePlan.FcInsurePlanOrderPersonrelMapper;
import com.sinosoft.eflex.enums.*;
import com.sinosoft.eflex.enums.status.EnsureStateEnum;
import com.sinosoft.eflex.model.AddressEntity.CheckSameCustomerRequest;
import com.sinosoft.eflex.model.AddressEntity.EvaluationCustomer;
import com.sinosoft.eflex.model.AddressEntity.IdCardVerifyRequest;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.insurePlan.InsurePlanOrder;
import com.sinosoft.eflex.model.insurePlan.InsurePlanOrderPersonrel;
import com.sinosoft.eflex.model.insurePlan.InsurePlanVo;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @DESCRIPTION
 * @create 2018-08-03 17:41
 **/
@Service("EnsureMakeService")
@Slf4j
@RequiredArgsConstructor
public class EnsureMakeService {

    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCEnsurePlanMapper fcEnsurePlanMapper;
    @Autowired
    private FCPlanRiskMapper fcPlanRiskMapper;
    @Autowired
    private FcPlanRiskInfoMapper fcPlanRiskInfoMapper;
    @Autowired
    private FcDailyInsureRiskInfoMapper fcDailyInsureRiskInfoMapper;
    @Autowired
    private FCPlanRiskDutyMapper fcPlanRiskDutyMapper;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private FCPerInfoTempMapper fcPerInfoTempMapper;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private PremTrailService premTrailService;
    @Autowired
    private UserService userService;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FCDefaultPlanMapper fcDefaultPlanMapper;
    @Autowired
    private FileService fileService;
    @Autowired
    private FCPerRegistDayMapper fcPerRegistDayMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FCGrpApplicantMapper fcGrpApplicantMapper;
    @Autowired
    private FcGrpContactMapper fcGrpContactMapper;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FDRiskDutyInfoMapper fdRiskDutyInfoMapper;
    @Autowired
    private FcEnsureContactMapper fcEnsureContactMapper;
    @Autowired
    private FcPlanConfigMapper fcPlanConfigMapper;
    @Autowired
    private FCAppntImpartInfoMapper fcAppntImpartInfoMapper;
    @Autowired
    private FCOrderMapper fcOrderMapper;
    @Autowired
    private FDAgentInfoMapper fdAgentInfoMapper;
    @Autowired
    private FCPerinfoFamilyTempMapper fcPerinfoFamilyTempMapper;
    @Autowired
    private FcDutyAmountGradeMapper fcDutyAmountGradeMapper;
    @Autowired
    private FcDutyGradeOptionalAmountInfoMapper fcDutyGradeOptionalAmountInfoMapper;
    @Autowired
    private FcDutyGroupDeductibleMapper fcDutyGroupDeductibleMapper;
    @Autowired
    private FcDutGradeCompensationRatioMapper fcDutGradeCompensationRatioMapper;
    @Autowired
    private FCBusPersonTypeMapper fcBusPersonTypeMapper;
    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;
    @Autowired
    private FcBusinessProDutyGrpObjectMapper fcBusinessProDutyGrpObjectMapper;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private MyProps myProps;
    @Autowired
    private GrpInfoService grpInfoService;
    @Autowired
    private FcInsurePlanMapper fcInsurePlanMapper;
    @Autowired
    private FcInsurePlanOrderMapper fcInsurePlanOrderMapper;
    @Autowired
    private FcInsurePlanOrderPersonrelMapper fcInsurePlanOrderPersonrelMapper;
    @Autowired
    private FCOrderItemMapper fcOrderItemMapper;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private AddressCheckService addressCheckService;
    private final ImageConvertService imageConvertService;
    @Autowired
    private final RedisUtil redisUtil;


    boolean bool = true;
    // 计划保费
    private double planPrem = 0.0;
    // 险种保费
    private double riskPrem = 0.0;
    // 计划导入提示信息
    private String planMessage = "";
    private List<FCEnsurePlan> fcEnsurePlanList = new ArrayList<FCEnsurePlan>();
    private List<FCPlanRisk> fcPlanRiskList = new ArrayList<FCPlanRisk>();
    private List<FCPlanRiskDuty> fcPlanRiskDutyList = new ArrayList<FCPlanRiskDuty>();

    /**
     * 分页查询保障信息列表
     *
     * @param ensureName
     * @param ensureCode
     * @param ensureState
     * @param page
     * @param rows
     * @return
     */
    public String selectEnsureListByPage(String token, String ensureName,
                                         String ensureCode, String ensureState, int page, int rows) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            PageHelper.startPage(page, rows);
            Map<String, Object> params = new HashMap<>();
            params.put("ensureName", ensureName);
            params.put("ensureCode", ensureCode);
            params.put("ensureState", "null".equals(ensureState) ? "" : ensureState);
            params.put("grpNo", globalInput.getGrpNo());
            List<HashMap<String, Object>> fCEnsureList = fcEnsureMapper.findEnsureListByPage(params);
            PageHelperUtil<HashMap<String, Object>> teamPageInfo = new PageHelperUtil<>(fCEnsureList);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("ensureList", teamPageInfo.getList());
            resultMap.put("data", dataMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询保障信息成功");
        } catch (Exception e) {
            log.info("查询保障信息失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询保障信息失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 获取第二联系人
     *
     * @param token
     * @return
     */
    public String getSecondContactPerson(String token) {
        log.info("获取第二联系人Start....");
        Map<String, Object> resultMap = new HashMap<String, Object>();
        GlobalInput globalInput = userService.getSession(token);
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("grpNo", globalInput.getGrpNo());
        params.put("contactType", "02");
        List<FcGrpContact> fcGrpContactList = fcGrpContactMapper.selectContactsInfo(params);
        resultMap.put("success", true);
        resultMap.put("code", "200");
        resultMap.put("message", "查询第二联系人成功");
        if (fcGrpContactList.size() > 0) {
            resultMap.put("data", fcGrpContactList.get(0));
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 初始化保障信息
     *
     * @param token
     * @param ensureCode
     * @return
     */
    public String initEnsureInfo(String token, String ensureCode) {
        GlobalInput globalInput = userService.getSession(token);
        //弹性计划
        if (!StringUtil.isEmpty(ensureCode)) {
            // 查询福利保障信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if (fcEnsure.getPlanType().equals(PlanTypeEnum.EFLEXPLAN.getCode())) {
                return initEnsureInfoAdmin(token, ensureCode, globalInput.getGrpNo(), globalInput.getCustomNo(), "");
            }
        }
        //固定计划
        return initEnsureInfoAdmin1(token, ensureCode, globalInput.getGrpNo(), globalInput.getCustomNo(), "");
    }

    /**
     * 管理员福利保障页面初始化
     *
     * @param token
     * @param ensureCode
     * @param grpNo
     * @param customNo
     * @param isOperation 权限标识符  用于区分管理员以及复核管理员，改变福利状态时使用。管理员传null即可  复核管理员传 1
     * @return
     */
    public String initEnsureInfoAdmin(String token, String ensureCode, String grpNo, String customNo, String isOperation) {

        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);
        // 定义返回报文
        Map<String, Object> resultMap = new HashMap<>();

        Map<String, Object> dataMap = new HashMap<>();
        // 查询企业信息
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectTranscodingGrpInfo(grpNo);
        // 图片转换
        fcGrpInfo = imageConvertService.convert(fcGrpInfo);

        // 查询企业当前保障信息
        if (!StringUtil.isEmpty(ensureCode)) {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            fcEnsure.setClientName(fcGrpInfo.getClientName());
            FcEnsureContact fcEnsureContactInfo = fcEnsureContactMapper.selectByPrimaryKey(ensureCode);
            String lastEnsureCode = "";
            if (fcEnsureContactInfo == null) {
                if ("2".equals(globalInput.getCustomType())) {
                    FCEnsure lastFcensure = fcEnsureMapper.getEnsureByGrpNo(grpNo, globalInput.getUserName());
                    if (lastFcensure != null) {
                        lastEnsureCode = lastFcensure.getEnsureCode();
                    }
                } else {
                    lastEnsureCode = ensureCode;
                }
            } else {
                lastEnsureCode = ensureCode;
            }
            Map<String, Object> configNoMap = new HashMap<>();
            configNoMap.put("grpNo", grpNo);
            configNoMap.put("ensureCode", ensureCode);
            configNoMap.put("configNo", "008");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(configNoMap);
            if (fcEnsure != null) {
                dataMap.put("FcEnsure", fcEnsure);
                String policyEndDate = fcEnsure.getPolicyEndDate();
                String cvaliDate = fcEnsure.getCvaliDate();
                try {
                    Long days = DateTimeUtil.getDistanceDays(policyEndDate, cvaliDate);
                    if (days == 364 || days == 365) {
                        dataMap.put("codeName", "一年期");
                        dataMap.put("insuredPeriod", 0);
                    } else {
                        dataMap.put("codeName", "极短期");
                        dataMap.put("insuredPeriod", days + 1);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                // 改为只取企业联系人 update by 2022.2.14
                FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(lastEnsureCode);
                if (fcEnsureContact == null) {
                    FcGrpContact fcGrpContact = fcGrpContactMapper.selectByPrimaryKey(customNo);
                    // 图片转换
                    fcGrpContact = imageConvertService.convertFcGrpContact(fcGrpContact);
                    dataMap.put("contact", fcGrpContact);
                } else {
                    //查询对应的企业联系人信息
                    FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpContact(fcEnsureContact.getIdNo());
                    // 图片转换
                    fcGrpContact = imageConvertService.convertFcGrpContact(fcGrpContact);
                    dataMap.put("FcEnsure", fcEnsure);
                    dataMap.put("contact", fcGrpContact);
                }
                if (fcEnsure.getPlanType().equals(PlanTypeEnum.EFLEXPLAN.getCode())) {
                    //业务员工号展示为企业关联业务员（弹性计划）
                    fcGrpInfo.setClientno(fcGrpInfo.getClientno());
                } else {
                    //业务员工号展示为福利定制的企业关联业务员
                    fcGrpInfo.setClientno(fcEnsure.getClientNo());
                }
            }
            dataMap.put("ensureConfig", fcEnsureConfig);
        } else {
            // 获取联系人 update by 改为只获取企业联系人信息
            FCEnsure lastFcensure = fcEnsureMapper.getEnsureByGrpNo(grpNo, globalInput.getUserName());
            FCEnsure fcEnsure = new FCEnsure();
            if (lastFcensure != null) {
                fcEnsure.setClientNo(lastFcensure.getClientNo());
                fcEnsure.setClientName(fcGrpInfo.getClientName());
                FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(lastFcensure.getEnsureCode());
                //查询对应的企业联系人信息
                FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpContact(fcEnsureContact.getIdNo());
                // 图片转换
                fcGrpContact = imageConvertService.convertFcGrpContact(fcGrpContact);
                dataMap.put("FcEnsure", fcEnsure);
                dataMap.put("contact", fcGrpContact);
            } else {
                fcEnsure.setClientNo(fcGrpInfo.getClientno());
                fcEnsure.setClientName(fcGrpInfo.getClientName());
                FcGrpContact fcGrpContact = fcGrpContactMapper.selectByPrimaryKey(customNo);
                // 图片转换
                fcGrpContact = imageConvertService.convertFcGrpContact(fcGrpContact);
                dataMap.put("FcEnsure", fcEnsure);
                dataMap.put("contact", fcGrpContact);
            }
        }
        // todo 可以换成fcgrpappnt的内容，与此同时在保存信息的时候则需要
        dataMap.put("FCGrpInfo", fcGrpInfo);
        resultMap.put("success", true);
        resultMap.put("code", "200");
        resultMap.put("message", "查询保障信息成功");
        resultMap.put("data", dataMap);
        if (ensureCode != null && !"".equals(ensureCode)) {
            //不同角色都调用了该接口，
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if ("02".equals(fcEnsure.getEnsureState()) && globalInput.getCustomType().matches("^(3|4|5|3,5)")) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_03);
                log.info("更新福利表fcEnsure投保状态完成。。。后台审核中");
            } else if ("05".equals(fcEnsure.getEnsureState()) && StringUtils.isNotBlank(isOperation)) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_06);
                log.info("更新福利表fcEnsure投保状态完成。。。审核确认中");
            } else if ("08".equals(fcEnsure.getEnsureState())) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_09);
                log.info("更新福利表fcEnsure投保状态完成。。。弹性计划 Hr定制中");
            } else if ("12".equals(fcEnsure.getEnsureState())) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_012);
                log.info("更新福利表fcEnsure投保状态完成。。。日常计划计划 初审定制中");
            }
            fcEnsure.setOperator(globalInput.getUserNo());
            fcEnsure = CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKey(fcEnsure);
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 新的福利信息加载逻辑
     *
     * @param token
     * @param ensureCode
     * @param grpNo
     * @param customNo
     * @param isOperation
     * @return
     */
    @Transactional
    public String initEnsureInfoAdmin1(String token, String ensureCode, String grpNo, String customNo, String isOperation) {
        InitEnsureInfoResp initEnsureInfoResp = new InitEnsureInfoResp();

        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);

        // 查询企业信息
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectGrpInfo1(grpNo);
        // 图片转换
        fcGrpInfo = imageConvertService.convert(fcGrpInfo);

        if (!StringUtil.isEmpty(ensureCode)) {
            // 查询福利保障信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            // 查询福利联系人信息
            FcEnsureContact fcEnsureContactInfo = fcEnsureContactMapper.selectByPrimaryKey(ensureCode);
            // 获取福利编码
            String lastEnsureCode = "";
            if (fcEnsureContactInfo == null && "2".equals(globalInput.getCustomType())) {
                // todo 理解这种情况应该是不存在的才对
                log.info("福利联系人信息表不存在！");
                FCEnsure lastFcensure = fcEnsureMapper.getEnsureByGrpNo(grpNo, globalInput.getUserName());
                if (lastFcensure != null) {
                    lastEnsureCode = lastFcensure.getEnsureCode();
                }
            } else {
                lastEnsureCode = ensureCode;
            }

            /**
             * 查询福利保障信息
             */
            Map<String, Object> configNoMap = new HashMap<>();
            configNoMap.put("grpNo", grpNo);
            configNoMap.put("ensureCode", ensureCode);
            configNoMap.put("configNo", "008");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(configNoMap);
            // 设置保障期间字段
            String policyEndDate = fcEnsure.getPolicyEndDate();
            String cvaliDate = fcEnsure.getCvaliDate();
            Long days = DateTimeUtil.getDistanceDays(policyEndDate, cvaliDate);
            if (days == 364 || days == 365) {
                fcEnsure.setInsuredPeriodType(InsuredPeriodTypeEnum.YEARINSURED.getCode());
                fcEnsure.setInsuredPeriod("1");
            } else {
                fcEnsure.setInsuredPeriodType(InsuredPeriodTypeEnum.SHORTINSURED.getCode());
                days = days + 1;
                fcEnsure.setInsuredPeriod(String.valueOf(days));

            }
            fcEnsure.setClientName(fcGrpInfo.getClientName());
            initEnsureInfoResp.setFcEnsure(fcEnsure);
            /**
             * 企业经办人信息
             */
            FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(lastEnsureCode);
            if (fcEnsureContact == null) {
                FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpContactInfo1(customNo);
                //脱敏处理
                fcGrpContact.setIdNo(Base64AndMD5Util.base64SaltEncode(fcGrpContact.getIdNo(), "130"));
                fcGrpContact.setMobilePhone(Base64AndMD5Util.base64SaltEncode(fcGrpContact.getMobilePhone(), "130"));
                // 图片转换
                fcGrpContact = imageConvertService.convertFcGrpContact(fcGrpContact);
                initEnsureInfoResp.setFcGrpContact(fcGrpContact);
            } else {
                // 福利联系人信息，福利不是复核通过的状态会被更新，复核通过则不会更新福利联系人信息
                FcGrpContact fcGrpContact = new FcGrpContact();
                BeanUtils.copyProperties(fcEnsureContact, fcGrpContact);
                fcGrpContact.setGrpNo(grpNo);
                FcGrpContact fcGrpContact1 = fcGrpContactMapper.selectGrpContact(fcEnsureContact.getIdNo());
                // 图片转换
                fcGrpContact = imageConvertService.convertFcGrpContact(fcGrpContact1);
                fcGrpContact.setContactNo(fcGrpContact1.getContactNo());

                //脱敏处理
                fcGrpContact.setIdNo(Base64AndMD5Util.base64SaltEncode(fcGrpContact.getIdNo(), "130"));
                fcGrpContact.setMobilePhone(Base64AndMD5Util.base64SaltEncode(fcGrpContact.getMobilePhone(), "130"));

                initEnsureInfoResp.setFcGrpContact(fcGrpContact);
            }
            if (fcEnsure.getPlanType().equals(PlanTypeEnum.EFLEXPLAN.getCode())) {
                //业务员工号展示为企业关联业务员（弹性计划）
                fcGrpInfo.setClientno(fcGrpInfo.getClientno());
            } else {
                //业务员工号展示为福利定制的企业关联业务员
                fcGrpInfo.setClientno(fcEnsure.getClientNo());
            }
            initEnsureInfoResp.setEnsureConfigList(Collections.singletonList(fcEnsureConfig));
            if (fcEnsure.getEnsureState().equals(EnsureStateEnum.CHECK_COMPLETED.getCode())) {
                FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
                FCGrpApplicant fcGrpApplicant = fcGrpApplicantMapper.selectByPrimaryKey(fcGrpOrder.getGrpAppNo());
                BeanUtils.copyProperties(fcGrpApplicant, fcGrpInfo);
                initEnsureInfoResp.setFcGrpInfo(fcGrpInfo);
            } else {
                initEnsureInfoResp.setFcGrpInfo(fcGrpInfo);
            }
        } else {
            // 获取联系人 update by 改为只获取企业联系人信息
            FCEnsure lastFcensure = fcEnsureMapper.getEnsureByGrpNo(grpNo, globalInput.getUserName());
            FCEnsure fcEnsure = new FCEnsure();
            if (lastFcensure != null) {
                fcEnsure.setClientNo(lastFcensure.getClientNo());
                FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(lastFcensure.getEnsureCode());
                //查询对应的企业联系人信息
                FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpContact(fcEnsureContact.getIdNo());
                // 图片转换
                fcGrpContact = imageConvertService.convertFcGrpContact(fcGrpContact);
                initEnsureInfoResp.setFcEnsure(fcEnsure);
                initEnsureInfoResp.setFcGrpContact(fcGrpContact);
            } else {
                fcEnsure.setClientNo(fcGrpInfo.getClientno());
                fcEnsure.setClientName(fcGrpInfo.getClientName());
                FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpContactInfo1(customNo);
                // 图片转换
                fcGrpContact = imageConvertService.convertFcGrpContact(fcGrpContact);
                initEnsureInfoResp.setFcEnsure(fcEnsure);
                initEnsureInfoResp.setFcGrpContact(fcGrpContact);
            }
            initEnsureInfoResp.setFcGrpInfo(fcGrpInfo);
        }

        if (ensureCode != null && !"".equals(ensureCode)) {
            //不同角色都调用了该接口，
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if ("02".equals(fcEnsure.getEnsureState()) && globalInput.getCustomType().matches("^(3|4|5|3,5)")) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_03);
                log.info("更新福利表fcEnsure投保状态完成。。。后台审核中");
            } else if ("05".equals(fcEnsure.getEnsureState()) && StringUtils.isNotBlank(isOperation)) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_06);
                log.info("更新福利表fcEnsure投保状态完成。。。审核确认中");
            } else if ("08".equals(fcEnsure.getEnsureState())) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_09);
                log.info("更新福利表fcEnsure投保状态完成。。。弹性计划 Hr定制中");
            } else if ("12".equals(fcEnsure.getEnsureState())) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_012);
                log.info("更新福利表fcEnsure投保状态完成。。。日常计划计划 初审定制中");
            }
            fcEnsure.setOperator(globalInput.getUserNo());
            fcEnsure = CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKey(fcEnsure);
        }
        return JSONObject.toJSONString(ResponseResultUtil.success(initEnsureInfoResp));
    }


    /**
     * 新增/修改 保障信息
     * 新的福利保障信息存储逻辑
     *
     * @param
     * @return
     */
    @Transactional
    public String maintainEnsureInfo1(String token, MaintainEnsureInfoReq maintainEnsureInfoReq) {
        MaintainEnsureInfoResp maintainEnsureInfoResp = new MaintainEnsureInfoResp();

        FCEnsure fcEnsure = maintainEnsureInfoReq.getFcEnsure();
        if (ObjectUtils.isEmpty(fcEnsure)) {
            throw new SystemException("福利定制信息不能为空！");
        }
        FCGrpInfo fcGrpInfo = maintainEnsureInfoReq.getFcGrpInfo();
        if (ObjectUtils.isEmpty(fcGrpInfo)) {
            throw new SystemException("企业信息不能为空！");
        }
        FcGrpContact fcGrpContact = maintainEnsureInfoReq.getFcGrpContact();
        if (ObjectUtils.isEmpty(fcGrpContact)) {
            throw new SystemException("企业经办人信息不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getLegIDStartDate())) {
            throw new SystemException("企业法人证件有效期不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpInfo.getLegIDEndDate())) {
            throw new SystemException("企业法人证件有效期不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpContact.getIdTypeStartDate())) {
            throw new SystemException("企业经办人证件有效期不能为空！");
        }
        if (StringUtils.isEmpty(fcGrpContact.getIdTypeEndDate())) {
            throw new SystemException("企业经办人证件有效期不能为空！");
        }
        if (LocalDate.parse(fcGrpInfo.getLegIDStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).isAfter(LocalDate.parse(fcGrpInfo.getLegIDEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")))) {
            throw new SystemException("企业法定代表人证件有效期起期不能晚于止期！");
        }
        if (LocalDate.parse(fcGrpContact.getIdTypeStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).isAfter(LocalDate.parse(fcGrpContact.getIdTypeEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")))) {
            throw new SystemException("经办人证件有效期起期不能晚于止期！");
        }
        /**
         * 存储企业信息
         */
        fcGrpInfo.setPeoples(maintainEnsureInfoReq.getFcEnsure().getInsuredNumber());
        grpInfoService.saveGrpInfo(token, fcGrpInfo);

        /**
         * 存储经办人信息
         */
        String saveGrpHrInfoResult = grpInfoService.saveGrpHrInfo(token, fcGrpContact);
        JSONObject jsonObject = JSONObject.parseObject(saveGrpHrInfoResult);
        SaveGrpHrInfoResp saveGrpHrInfoResp = JSONObject.parseObject(JSONObject.toJSONString(jsonObject.get("data")), SaveGrpHrInfoResp.class);
        maintainEnsureInfoResp.setIsNeedLogin(saveGrpHrInfoResp.getIsNeedLogin());
        maintainEnsureInfoResp.setIsNeedLoginMsg(saveGrpHrInfoResp.getIsNeedLoginMsg());
        /**
         * 存储福利信息
         */
        //业务员工号去除
        fcEnsure.setClientNo(null);
        String ensureCode = saveEnsureInfo(token, fcEnsure, fcGrpInfo, fcGrpContact);
        maintainEnsureInfoResp.setEnsureCode(ensureCode);

        return JSONObject.toJSONString(ResponseResultUtil.success(maintainEnsureInfoResp));
    }


    public boolean maintainEnsureContact(FcEnsureContact fcEnsureContact) {
        //fcEnsureContact
        if (fcEnsureContact == null || fcEnsureContact.getEnsureCode() == null || "".equals(fcEnsureContact.getEnsureCode())) {
            log.info("维护FcEnsureContact时,福利编号不能为空");
            return false;
        }
        FcEnsureContact tfContact = fcEnsureContactMapper.selectByPrimaryKey(fcEnsureContact.getEnsureCode());
        if (tfContact == null) {
            fcEnsureContact = (FcEnsureContact) CommonUtil.initObject(fcEnsureContact, "INSERT");
            fcEnsureContactMapper.insertSelective(fcEnsureContact);
        } else {
            fcEnsureContact = (FcEnsureContact) CommonUtil.initObject(fcEnsureContact, "UPDATE");
            fcEnsureContactMapper.updateByPrimaryKeySelective(fcEnsureContact);
        }
        log.info("维护FcEnsureContact成功！！");
        return true;
    }


    /**
     * 新的福利存储逻辑 add by wudezhong 2022.2.15
     *
     * @param token
     * @param fcEnsure
     * @param fcGrpContact
     * @return
     * @throws Exception
     */
    public String addEnsureInfo1(String token, FCEnsure fcEnsure, FCGrpInfo fcGrpInfo, FcGrpContact fcGrpContact) {
        GlobalInput globalInput = userService.getSession(token);

        /**
         * 存储福利信息
         */
        String ensureCode = "FL" + maxNoService.createMaxNo("EnsureCode", "", 18);
        fcEnsure.setEnsureCode(ensureCode);
        fcEnsure.setPlanType("0");
        fcEnsure.setAppntYear(DateTimeUtil.getCurrentYear());
        fcEnsure.setGrpNo(globalInput.getGrpNo());
        fcEnsure.setEnsureState(EnsureStateEnum.NOTCUSTOM.getCode());
        fcEnsure.setInsuredNumber(fcEnsure.getInsuredNumber());
        fcEnsure.setTotalPrem(0.0);
        fcEnsure.setGrpNo(globalInput.getGrpNo());
        fcEnsure.setOperator(globalInput.getUserNo());

        switch (InsuredPeriodTypeEnum.getTypeByCode(fcEnsure.getInsuredPeriodType())) {
            case SHORTINSURED:
                if (StringUtils.isNotBlank(fcEnsure.getCvaliDate())) {
                    //保单生效日期
                    String cvaliDate = fcEnsure.getCvaliDate();
                    //加用户录入的天数 减一天
                    String endDate = null;
                    try {
                        endDate = DateTimeUtil.plusDay(Integer.parseInt(fcEnsure.getInsuredPeriod()), cvaliDate);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    endDate = DateTimeUtil.dateSubOneDay(endDate);
                    fcEnsure.setPolicyEndDate(endDate);
                }
                break;
            case YEARINSURED:
                if (StringUtils.isNotBlank(fcEnsure.getCvaliDate())) {
                    //保单生效日期
                    String cvaliDate = fcEnsure.getCvaliDate();
                    // 加一年 减一天  生效日期2018-10-30的终止日期为 2019-10-29
                    String endDate = DateTimeUtil.dateAddOneYear(cvaliDate);
                    endDate = DateTimeUtil.dateSubOneDay(endDate);
                    fcEnsure.setPolicyEndDate(endDate);
                }
                break;
        }

        fcEnsure = CommonUtil.initObject(fcEnsure, "INSERT");
        fcEnsureMapper.insert(fcEnsure);

        // 记录福利付款方式
        List<String> configNoList = new ArrayList<>();
        configNoList.add("008");
        maintainFcEnsureConfig(fcEnsure.getEnsureCode(), fcEnsure.getGrpNo(), configNoList, fcEnsure.getPayType(), globalInput);

        // 记录福利联系人信息
        FcEnsureContact fcEnsureContact = new FcEnsureContact();
        fcEnsureContact.setEnsureCode(ensureCode);
        fcEnsureContact.setName(fcGrpContact.getName());
        fcEnsureContact.setNativeplace(fcGrpContact.getNativeplace());
        fcEnsureContact.setIdType(fcGrpContact.getIdType());
        fcEnsureContact.setIdNo(fcGrpContact.getIdNo());
        fcEnsureContact.setDepartment(fcGrpContact.getDepartment());
        fcEnsureContact.setEmail(fcGrpContact.getEmail());
        fcEnsureContact.setMobilePhone(fcGrpContact.getMobilePhone());
        fcEnsureContact.setSex(fcGrpContact.getSex());
        fcEnsureContact.setBirthDay(fcGrpContact.getBirthDay());
        fcEnsureContact.setIdTypeStartDate(fcGrpContact.getIdTypeStartDate());
        fcEnsureContact.setIdTypeEndDate(fcGrpContact.getIdTypeEndDate());
        fcEnsureContact.setIdImage1(fcGrpContact.getIdImage1());
        fcEnsureContact.setIdImage2(fcGrpContact.getIdImage2());
        fcEnsureContact.setOperator(globalInput.getUserNo());
        maintainEnsureContact(fcEnsureContact);
        return ensureCode;
    }

    public void maintainFcEnsureConfig(String ensureCode, String grpNo, List<String> configNoList, String configValue, GlobalInput globalInput) {
        for (int i = 0; i < configNoList.size(); i++) {
            String configNo = configNoList.get(i);
            Map<String, Object> params = new HashMap<>();
            params.put("ensureCode", ensureCode);
            params.put("grpNo", grpNo);
            params.put("configNo", configNo);
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(params);
            if (fcEnsureConfig == null) {
                fcEnsureConfig = new FCEnsureConfig();
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig.setGrpNo(grpNo);
                fcEnsureConfig.setEnsureCode(ensureCode);
                fcEnsureConfig.setConfigNo(configNo);
                fcEnsureConfig.setConfigValue(configValue);
                fcEnsureConfig.setOperator(globalInput.getUserNo());
                fcEnsureConfig = CommonUtil.initObject(fcEnsureConfig, "INSERT");
                fcEnsureConfigMapper.insert(fcEnsureConfig);
                log.info("福利配置表新增成功");
            } else {
                fcEnsureConfig.setConfigValue(configValue);
                fcEnsureConfig.setEnsureCode(ensureCode);
                fcEnsureConfig = CommonUtil.initObject(fcEnsureConfig, "UPDATE");
                fcEnsureConfigMapper.updateByPrimaryKeySelective(fcEnsureConfig);
                log.info("福利配置表更新成功");
            }
        }
    }

    /**
     * 新的福利修改逻辑 add by wudezhong 2022.2.15
     *
     * @param token
     * @param fcEnsure
     * @param fcGrpInfo
     * @param fcGrpContact
     * @return
     */
    public String modifyEnsureInfo1(String token, FCEnsure fcEnsure, FCGrpInfo fcGrpInfo, FcGrpContact fcGrpContact) {
        GlobalInput globalInput = userService.getSession(token);

        /**
         * 删除福利变更后影响的数据
         */
        FCEnsure oldFcEnsure = fcEnsureMapper.selectByPrimaryKey(fcEnsure.getEnsureCode());
        if (oldFcEnsure == null) {
            log.info("福利编号" + fcEnsure.getEnsureCode() + "不存在！");
        }
        // 一年期
        if (InsuredPeriodTypeEnum.YEARINSURED.getCode().equals(fcEnsure.getInsuredPeriodType())) {
            long days = DateTimeUtil.getDistanceDays(oldFcEnsure.getPolicyEndDate(), oldFcEnsure.getCvaliDate());
            if (364 != days) {
                fcEnsurePlanMapper.deleteByEnsureCode(oldFcEnsure.getEnsureCode());
                fcPerInfoTempMapper.deleteAllFcPerInfoTemp(oldFcEnsure.getEnsureCode());
            }
        }
        // 极短期，判断是否更改了保险期间
        if (InsuredPeriodTypeEnum.SHORTINSURED.getCode().equals(fcEnsure.getInsuredPeriodType()) && Integer.parseInt(fcEnsure.getInsuredPeriod()) != DateTimeUtil.getDistanceDays(oldFcEnsure.getPolicyEndDate(), oldFcEnsure.getCvaliDate()) + 1) {
            fcEnsurePlanMapper.deleteByEnsureCode(oldFcEnsure.getEnsureCode());
            fcPlanRiskMapper.deleteByEnsureCode(oldFcEnsure.getEnsureCode());
            fcPlanRiskDutyMapper.deleteByEnsureCode(oldFcEnsure.getEnsureCode());
            fcPerInfoTempMapper.deleteAllFcPerInfoTemp(oldFcEnsure.getEnsureCode());
        }
        //如果福利投保类型发生变化，原本录入的计划和人员全部删除
        if (!oldFcEnsure.getEnsureType().equals(fcEnsure.getEnsureType())) {
            fcEnsurePlanMapper.deleteByEnsureCode(oldFcEnsure.getEnsureCode());
            fcPlanRiskMapper.deleteByEnsureCode(oldFcEnsure.getEnsureCode());
            fcPlanRiskDutyMapper.deleteByEnsureCode(oldFcEnsure.getEnsureCode());
            fcPerInfoTempMapper.deleteAllFcPerInfoTemp(oldFcEnsure.getEnsureCode());
        }

        /**
         * 修改福利信息
         */
        fcEnsure.setPlanType("0");
        fcEnsure.setGrpNo(globalInput.getGrpNo());
        fcEnsure.setAppntYear(DateTimeUtil.getCurrentYear());
        // 0-定制中 1-定制完成
        fcEnsure.setEnsureState("0");
        fcEnsure.setInsuredNumber(fcEnsure.getInsuredNumber());
        fcEnsure.setTotalPrem(0.0);
        fcEnsure.setGrpNo(globalInput.getGrpNo());
        fcEnsure.setOperator(globalInput.getUserNo());
        //新需求   保单终止日期为：如果是insurePeriod传递的codeKey为1，则表示极短期；若为2则表示一年期
        if ("2".equals(fcEnsure.getInsuredPeriodType())) {
            if (StringUtils.isNotBlank(fcEnsure.getCvaliDate())) {
                String endDate = fcEnsure.getCvaliDate();
                // 加一年 减一天  生效日期2018-10-30的终止日期为 2019-10-29
                endDate = DateTimeUtil.dateAddOneYear(endDate);
                endDate = DateTimeUtil.dateSubOneDay(endDate);
                fcEnsure.setPolicyEndDate(endDate);
            }
        }
        if ("1".equals(fcEnsure.getInsuredPeriodType())) {
            if (StringUtils.isNotBlank(fcEnsure.getCvaliDate())) {
                String endDate = fcEnsure.getCvaliDate();
                try {
                    endDate = DateTimeUtil.plusDay(Integer.parseInt(fcEnsure.getInsuredPeriod()), endDate);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                endDate = DateTimeUtil.dateSubOneDay(endDate);
                fcEnsure.setPolicyEndDate(endDate);
            }
        }
        fcEnsure = CommonUtil.initObject(fcEnsure, "UPDATE");
        fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);

        /**
         * 记录企业付款信息
         */
        List<String> configNoList = new ArrayList<>();
        configNoList.add("008");
        maintainFcEnsureConfig(fcEnsure.getEnsureCode(), fcEnsure.getGrpNo(), configNoList, fcEnsure.getPayMode(), globalInput);

        /**
         * 记录福利联系人信息
         */
        FcEnsureContact fcEnsureContact = new FcEnsureContact();
        fcEnsureContact.setEnsureCode(fcEnsure.getEnsureCode());
        fcEnsureContact.setName(fcGrpContact.getName());
        fcEnsureContact.setNativeplace(fcGrpContact.getNativeplace());
        fcEnsureContact.setIdType(fcGrpContact.getIdType());
        fcEnsureContact.setIdNo(fcGrpContact.getIdNo());
        fcEnsureContact.setDepartment(fcGrpContact.getDepartment());
        fcEnsureContact.setEmail(fcGrpContact.getEmail());
        fcEnsureContact.setMobilePhone(fcGrpContact.getMobilePhone());
        fcEnsureContact.setSex(fcGrpContact.getSex());
        fcEnsureContact.setBirthDay(fcGrpContact.getBirthDay());
        fcEnsureContact.setIdTypeStartDate(fcGrpContact.getIdTypeStartDate());
        fcEnsureContact.setIdTypeEndDate(fcGrpContact.getIdTypeEndDate());
        fcEnsureContact.setIdImage1(fcGrpContact.getIdImage1());
        fcEnsureContact.setIdImage2(fcGrpContact.getIdImage2());
        fcEnsureContact.setOperator(globalInput.getUserNo());
        maintainEnsureContact(fcEnsureContact);
        int staffNum = Integer.valueOf(fcGrpInfo.getPeoples());
        if (fcGrpInfo.getPeoples() == null) {
            staffNum = 0;
        }
        return fcEnsure.getEnsureCode();
    }

    /**
     * 删除福利信息
     * 只删除未制定和审核回退状态的福利
     *
     * @param token
     * @param ensureCode
     * @return
     */
    @Transactional
    public String deleteEnsureInfo(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if (ensureCode == null || "".equals(ensureCode)) {
                log.info("删除福利信息失败：ensureCode不能为空！");
                resultMap.put("message", "删除福利信息失败：ensureCode不能为空！");
                return JSON.toJSONString(resultMap);
            }
            if (fcEnsure == null) {
                log.info("删除福利信息失败：该福利不存在！" + ensureCode);
                resultMap.put("message", "删除福利信息失败：该福利不存在！");
                return JSON.toJSONString(resultMap);
            }
            if ("1".equals(fcEnsure.getPlanType())) {
                if (!ConstantUtil.EnsureState_0.equals(fcEnsure.getEnsureState())
                        && !ConstantUtil.EnsureState_07.equals(fcEnsure.getEnsureState())
                        && !ConstantUtil.EnsureState_010.equals(fcEnsure.getEnsureState())
                        && !ConstantUtil.EnsureState_011.equals(fcEnsure.getEnsureState())) {
                    log.info("删除福利信息失败：该福利已定制完成或正在审核中，不可删除！" + ensureCode + "==" + fcEnsure.getEnsureName());
                    resultMap.put("message", "删除福利信息失败：该福利已定制完成或正在审核中，不可删除！");
                    return JSON.toJSONString(resultMap);
                }
            } else {
                if (!ConstantUtil.EnsureState_0.equals(fcEnsure.getEnsureState()) && !ConstantUtil.EnsureState_04.equals(fcEnsure.getEnsureState())) {
                    log.info("删除福利信息失败：该福利已定制完成或正在审核中，不可删除！" + ensureCode + "==" + fcEnsure.getEnsureName());
                    resultMap.put("message", "删除福利信息失败：该福利已定制完成或正在审核中，不可删除！");
                    return JSON.toJSONString(resultMap);
                }
            }

            if (delEnsureInfo(ensureCode)) {
                log.info("删除福利信息成功：" + ensureCode + "==" + fcEnsure.getEnsureName());
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "删除福利信息成功!");
            } else {
                log.info("删除福利信息失败：系统异常。" + ensureCode + "==" + fcEnsure.getEnsureName());
                resultMap.put("message", "删除福利信息失败：系统异常!");
            }
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("删除福利信息失败：系统异常。" + ensureCode, e);
            throw new RuntimeException();
        }
    }

    /**
     * 删除福利接口
     *
     * @param ensureCode
     * @return
     */
    public boolean delEnsureInfo(String ensureCode) {
        log.info("删除福利" + ensureCode + " start.........");
        HashMap<String, String> mapInfo = new HashMap<>();
        mapInfo.put("ensureCode", ensureCode);
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        log.info("删除的福利信息:{}", JSON.toJSONString(fcEnsure));
        fcEnsureMapper.deleteByPrimaryKey(ensureCode);
        fcEnsureConfigMapper.deleteByEnsureCode(ensureCode);
        fcEnsureContactMapper.deleteByPrimaryKey(ensureCode);
        if ("0".equals(fcEnsure.getPlanType())) {
            fcEnsurePlanMapper.deleteByEnsureCode(ensureCode);
            fcPlanRiskMapper.deleteByEnsureCode(ensureCode);
            fcPlanRiskDutyMapper.deleteByEnsureCode(ensureCode);
        } else if ("1".equals(fcEnsure.getPlanType())) {
            //FcPlanRiskInfo 弹性计划险种信息表
            fcPlanRiskInfoMapper.deleteByPrimaryKey(ensureCode, "", "");
            //FcDutyAmountGrade  必选责任保额档次表
            fcDutyAmountGradeMapper.deleteByEnsureCode(mapInfo);
            //FcDutyGradeOptionalAmountInfo 责任档次可选责任保额信息表
            fcDutyGradeOptionalAmountInfoMapper.deleteByEnsureCode(mapInfo);
            //FcDutyGroupDeductible  责任保额档次免赔额表
            fcDutyGroupDeductibleMapper.deleteByEnsureCode(mapInfo);
            //FcDutGradeCompensationRatio 责任保额档次赔付比例表
            fcDutGradeCompensationRatioMapper.deleteByEnsureCode(mapInfo);
            //FCBusPersonType 企业员工职级分类表
            fcBusPersonTypeMapper.deleteByEnsureCode(ensureCode);
            //FcBusinessProDutyGrpObject 企业责任组合配置投保对象表
            fcBusinessProDutyGrpObjectMapper.deleteByEnsureCode(ensureCode, "");
        }
        fcPerInfoTempMapper.deleteAllFcPerInfoTemp(ensureCode);
        fcGrpOrderMapper.deleteByEnsureCode(ensureCode);
        log.info("删除福利" + ensureCode + " 成功.........END");
        return true;
    }


    /**
     * 删除福利接口
     *
     * @param ensureCode
     * @return
     */
    public boolean delEnsure(String ensureCode) {
        log.info("删除计划书福利" + ensureCode + " start.........");
        HashMap<String, String> mapInfo = new HashMap<>();
        mapInfo.put("ensureCode", ensureCode);
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        log.info("删除的福利信息:{}", JSON.toJSONString(fcEnsure));
        fcEnsureMapper.deleteByPrimaryKey(ensureCode);
        fcEnsureConfigMapper.deleteByEnsureCode(ensureCode);
        fcEnsureContactMapper.deleteByPrimaryKey(ensureCode);
        fcPerInfoTempMapper.deleteAllFcPerInfoTemp(ensureCode);
        fcGrpOrderMapper.deleteByEnsureCode(ensureCode);
        log.info("删除计划书福利" + ensureCode + " 成功.........END");
        return true;
    }


    /**
     * 解析保障计划Excel
     *
     * @param token
     * @param file
     * @return
     */
    public String readPlanExcel(String token, MultipartFile file, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        if (ensureCode.isEmpty()) {
            log.info("计划导入失败：session缺失ensureCode！");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "计划导入失败");
            return JSON.toJSONString(resultMap);
        }
        String path = FileUtil.getLocalPath("0201");
        String fileName = ensureCode + file.getOriginalFilename();
        String filePath = path + fileName;
        boolean uploadSuccess = fileService.fileUpload(file, filePath, "0201",
                ensureCode);
        if (!uploadSuccess) {
            log.info("计划导入失败：文件上传失败");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "计划导入失败");
            return JSON.toJSONString(resultMap);
        }
        Workbook wb = null;
        try {
            wb = ExcelUtil.initWorkbook(filePath);
            // 处理业务数据
            if (dealPlanExcel(token, wb, ensureCode)) {
                log.info("计划导入成功");
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "计划导入成功");
            } else {
                List<String> errorMsgList;
                Splitter split = Splitter.on('/').trimResults().omitEmptyStrings(); // 去前后空格&&去空string
                errorMsgList = split.splitToList(planMessage);
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", errorMsgList);
            }
        } catch (IOException e) {
            log.info("计划导入失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "计划导入失败");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 导入计划列表查询
     *
     * @param token
     * @return
     */
    public String queryPlan(String token, int pageNo, int pageSize, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("ensureCode", ensureCode);
            PageHelper.startPage(pageNo, pageSize);
            List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectFCEnsurePlans(params);
            PageHelperUtil<FCEnsurePlan> teamPageInfo = new PageHelperUtil<>(fcEnsurePlanList);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("list", teamPageInfo.getList());
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "计划列表查询成功");
            resultMap.put("data", dataMap);
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("系统异常：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "计划列表查询失败");
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 导入员工清单解析
     *
     * @param token
     * @param file
     * @return
     */
    public String readStaffExcel(String token, String status,
                                 MultipartFile file, String ensureCode, String newFlag) {
        Map<String, Object> resultMap = new HashMap<>();
        String path = FileUtil.getLocalPath("0202");
        String fileName = ensureCode + file.getOriginalFilename();
        String filePath = path + fileName;
        boolean uploadSuccess = fileService.fileUpload(file, filePath, "0202", ensureCode);

        if (!uploadSuccess) {
            log.info("清单导入失败：文件上传失败！");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", Collections.singletonList("清单导入失败！"));
            return JSON.toJSONString(resultMap);
        }
        Workbook wb = null;
        try {
            wb = ExcelUtil.initWorkbook(filePath);

            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);

            if (!"1".equals(fcEnsure.getPlanType())) {
                // 导入人员清单之前校验是否已导入计划
                Map<String, Object> params = new HashMap<>();
                params.put("ensureCode", ensureCode);
                List<FCEnsurePlan> planList = fcEnsurePlanMapper.selectFCEnsurePlans(params);
                if (planList.size() < 1) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", Arrays.asList("请您至少导入一个计划！"));
                    return JSON.toJSONString(resultMap);
                }

                //导入人员清单之前校验是否已导入职级
                List<FCBusPersonType> fcBusPersonTypelist = fcBusPersonTypeMapper.selectByGrpNo(fcEnsureMapper.selectByPrimaryKey(ensureCode).getGrpNo());

                if (fcBusPersonTypelist.size() < 1) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", Arrays.asList("请您至少设置一个职级！"));
                    return JSON.toJSONString(resultMap);
                }
            }

            // 处理业务数据
            resultMap = dealStaffExcel(token, status, wb, ensureCode, newFlag);

        } catch (IOException e) {
            log.info("清单导入失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", Collections.singletonList("清单导入失败！"));
        }
        return JSON.toJSONString(resultMap);
    }

    public int sheetCount(Workbook wb) {
        int count = wb.getNumberOfSheets();
        return count;
    }

    //获取数字有效部分 2.20300→2.203
    public String getStrToPlanString(String str) {
        if (StringUtils.isNotBlank(str)) {
            try {
                return new BigDecimal(str).stripTrailingZeros().toPlainString();
            } catch (Exception e) {
                log.error("Error", e);
                return str;
            }
        } else {
            return str;
        }
    }


    /**
     * 处理员工清单
     *
     * @param wb
     * @return
     */
    @SuppressWarnings("finally")
    @Transactional
    public Map<String, Object> dealStaffExcel(String token, String status, Workbook wb, String ensureCode, String newFlag) {
        String errorMsg = "";
        String errorInfo = "";
        int count = 0;
        int familyCount = 0;
        GlobalInput globalInput = userService.getSession(token);
        List<FCPerInfoTemp> perInfoTemplist = new ArrayList<>();
        List<FCPerinfoFamilyTemp> familyMapList = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();
        //存储错误信息
        List<String> errorMsgList = new ArrayList<>();
        //存储员工基本信息
        Map<String, Map<String, String>> personalMap = new HashMap<>();
        //获取性别Value-Key
        List<HashMap<String, Object>> sexCodeList = fdCodeMapper.CodeInfo("Sex");
        //获取与被保人关系Value-Key
        List<HashMap<String, Object>> relationshipCodeList = fdCodeMapper.CodeInfo("PolicyholderRelationship");
        //获取证件类型和证件号码的Value-Key
        List<HashMap<String, Object>> idTypeCodeList = fdCodeMapper.CodeInfo("IDType");
        // 获取职业类别集合
        List<String> occupationTypeList = fcPerInfoTempMapper.getOccupationTypeList("01");
        // 获取职业集合
        List<String> occupationCodeList = fcPerInfoTempMapper.getOccupationCodeList("01");
        // 获取职业类别与职业代码对应关系
        Map<String, List<String>> occupationTypeCodeMap = getOccupationTypeCode();
        //获取员工关系
        List<HashMap<String, Object>> relationMap = fdCodeMapper.CodeInfo("Relation");
        //获取国籍码值与对应名称
        List<HashMap<String, Object>> nativeplaceMapList = fdCodeMapper.CodeInfo("Nativeplace");
        // 获取企业福利对应的支付方式 1-企业全缴 2-企业代缴 3-混合缴费
        Map<String, Object> params1 = new HashMap<>();
        params1.put("ensureCode", ensureCode);
        params1.put("configNo", "008");
        String payMethod = fcEnsureConfigMapper.selectOnlyValue(params1);
        Map<String, String> nativeplaceMap = new HashMap<>();
        for (HashMap<String, Object> nativeplace : nativeplaceMapList) {
            nativeplaceMap.put(nativeplace.get("CodeName").toString(), nativeplace.get("CodeKey").toString());
        }
        // 获取证件类型集合
        List<String> idTypeList = fcPerInfoTempMapper.getIdTypeList("01");
        // 获取银行代码集合
        List<String> openBankList = fcPerInfoTempMapper.getOpenBankList("01");
        //福利职级
        List<String> levelList = new ArrayList<>();
        List<FCBusPersonType> fcBusPersonTypelist = fcBusPersonTypeMapper.selectByGrpNo(fcEnsureMapper.selectByPrimaryKey(ensureCode).getGrpNo());
        if (fcBusPersonTypelist != null && fcBusPersonTypelist.size() > 0) {
            for (FCBusPersonType fcBusPersonType : fcBusPersonTypelist) {
                levelList.add(fcBusPersonType.getGradeLevelCode());
            }
        }
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        log.info("dealStaffExcel fcEnsure response:{}", JsonUtil.toJSON(fcEnsure));
        String grpNo = globalInput.getGrpNo();
        String operator = globalInput.getUserNo();

        // 人员反洗钱校验list
        List<EvaluationCustomer> checkCustomer = new ArrayList<>();
        try {
            // 判断是添加导入还是覆盖导入 // "01"-添加导入;"02"-覆盖导入
            if ("02".equals(status)) {
                fcPerInfoTempMapper.deleteAllFcPerInfoTemp(ensureCode);
                HashMap<String, String> deleMap = new HashMap<>();
                deleMap.put("ensureCode", ensureCode);
                fcPerinfoFamilyTempMapper.deleteByPrimaryKey(deleMap);
            }

            // 处理导入人员信息 EnsureType：0-企事业单位投保，1-在校学生投保，2-场地险投保
            if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                //PlanType：0-固定计划，1-弹性计划
                if ("0".equals(fcEnsure.getPlanType())) {   //员工解析
                    //用于存储员工三要素 判断家属所录员工是否存在
                    List<Map<String, String>> perInList = new ArrayList<>();
                    for (int j = 0; j < 2; j++) {
                        // 记录错误行号
                        int rowNum = 0;
                        Sheet sheet = wb.getSheetAt(j);
                        Row row = null;
                        if (j == 0) {
                            List<HashMap<String, String>> listPerIfo = new ArrayList<>();
                            List<String> listIdNo = new ArrayList<>();
                            List<String> listdefaultPlanCode = new ArrayList<>();
                            List<String> listbusPersonType = new ArrayList<>();
                            log.info(sheet.getLastRowNum() + "");
                            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                                log.info("正在解析第" + (i + 1) + "行！");
                                rowNum = i - 1;
                                //错误信息超过10条，不再继续解析，仅在员工处添加，其他涉及的情况很少，建议使用List根据JVM的内存情况来存储。
                                // 去前后空格&&去空string
                                Splitter split = Splitter.on('/').trimResults().omitEmptyStrings();
                                List<String> list = new ArrayList<>(split.splitToList(errorMsg));
                                if (list.size() == 10) {
                                    break;
                                }
                                row = sheet.getRow(i);
                                if (ExcelUtil.isRowEmpty(row, 2)) {
                                    log.info("员工清单第" + (i + 1) + "行是无效数据。");
                                } else {
                                    Map<String, String> personal = new HashMap<>();
                                    count++;
                                    // 校验字段非空
                                    errorMsg += checkIsEmpTy(i + 1, row);
                                    if (!checkScientifiNotation(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(8)))) || !CheckUtils.checkMobilePhone(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(8))))) {
                                        errorMsg += "员工清单第" + (i + 1) + "行,手机号填写格式有误!/";
                                    }
                                    if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(6)))) {
                                        errorMsg += "员工清单第" + (i + 1) + "行,证件号填写格式有误，建议将格式调整为文本格式!/";
                                    }
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(12))) || !checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(12)))) {
                                        errorInfo += "员工清单第" + (i + 1) + "行,员工职级填写格式有误!/";
                                    }
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(17)))) {
                                        String checkBankAccount = CheckUtils.checkBankAccount(ExcelUtil.getCellValue(row.getCell(17)));
                                        if (StringUtils.isNotEmpty(checkBankAccount)) {
                                            errorMsg += "员工清单第" + (i + 1) + "行" + checkBankAccount + "/";
                                        }
                                    }
                                    //场地险投保只有一种个人批扣的支付方式，并且场地险要求福利额度为空，开户行账号不能为空 add by wudezhong 2021.12.7
                                    if ("3".equals(payMethod) && fcEnsure.getEnsureType().equals(EnsureTypeEnum.SITEGRPENSURE.getCode())) {
                                        if (StringUtils.isBlank(ExcelUtil.getCellValue(row.getCell(16))) || StringUtils.isBlank(ExcelUtil.getCellValue(row.getCell(17)))) {
                                            errorMsg += "员工清单第" + (i + 1) + "行,开户行、开户账号不允许为空!/";
                                        }
                                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(18))) || StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(19)))) {
                                            errorMsg += "员工清单第" + (i + 1) + "行,员工福利额度、家属福利额度必须为空!/";
                                        }
                                        String checkBankAccount = CheckUtils.checkBankAccount(ExcelUtil.getCellValue(row.getCell(17)));
                                        if (StringUtils.isNotEmpty(checkBankAccount)) {
                                            errorMsg += "员工清单第" + (i + 1) + "行" + checkBankAccount + "/";
                                        }
                                    }
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(16)))) {
                                        if (!openBankList.contains(ExcelUtil.getCellValue(row.getCell(16)))) {
                                            errorMsg += "员工清单第" + (i + 1) + "行开户行录入错误/";
                                        }
                                    }
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(17)))) {
                                        if (!checkScientifiNotation(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(17))))) {
                                            errorMsg += "第" + (i + 1) + "行,开户账号填写格式有误!/";
                                        }
                                        if (getStrToPlanString(ExcelUtil.getCellValue(row.getCell(17))).length() < 12 || getStrToPlanString(ExcelUtil.getCellValue(row.getCell(17))).length() > 30) {
                                            errorMsg += "第" + (i + 1) + "行,开户账号填写格式有误!/";
                                        }
                                    }
                                    //校验国籍
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(4)))) {
                                        String nativeplacecode = nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(4)));
                                        if (StringUtils.isBlank(nativeplacecode)) {
                                            errorMsg += "员工清单第" + (i + 1) + "行,国籍填写有误!/";
                                        }
                                    }
                                    HashMap<String, String> hashMap = new HashMap<>();
                                    hashMap.put("name", ExcelUtil.getCellValue(row.getCell(1)));
                                    //性别的公式值转换
                                    String sex = ExcelUtil.getCellFormula(row.getCell(9));
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellFormula(row.getCell(9)))) {
                                        if (!"男".equals(sex) && !"女".equals(sex)) {
                                            errorMsg += "员工清单第" + (i + 1) + "行,性別不符合录入规则（男|女）!/";
                                        }
                                    }
                                    //性别码值转换
                                    for (HashMap<String, Object> hashMap2 : sexCodeList) {
                                        if (sex.equals(hashMap2.get("CodeName").toString())) {
                                            sex = hashMap2.get("CodeKey").toString();
                                            break;
                                        }
                                    }
                                    //与投保人关系转换
                                    hashMap.put("sex", sex);
                                    //出生日期的公式值转换
                                    hashMap.put("birthday", ExcelUtil.getCellFormula(row.getCell(10)));
                                    //证件类型码值转换
                                    String idTypeExcel = ExcelUtil.getCellValue(row.getCell(5));
                                    if (idTypeExcel != null && !idTypeExcel.equals("")) {
                                        String idType = "";
                                        for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
                                            if (idTypeExcel.equals(hashMap1.get("CodeName").toString())) {
                                                idType = hashMap1.get("CodeKey").toString();
                                                break;
                                            }
                                        }
                                        hashMap.put("idtype", idType);
                                    } else {
                                        hashMap.put("idtype", "");
                                    }
                                    if (!StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(6))) && !StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(8)))) {
                                        if (fcPerInfoTempMapper.selectByPhone(ExcelUtil.getCellValue(row.getCell(8)), "0", ExcelUtil.getCellValue(row.getCell(6)), hashMap.get("idtype")) > 0) {
                                            errorMsg += "员工清单第" + (i + 1) + "行该客户手机号与既往其他客户的手机号重复，请确认！/";
                                        }
                                        int checkint = fdUserMapper.checkByPhone(ExcelUtil.getCellValue(row.getCell(8)), ExcelUtil.getCellValue(row.getCell(6)), "1");
                                        if (checkint > 0) {
                                            errorMsg += "员工清单第" + (i + 1) + "行,手机号已注册!/";
                                        }
                                    }
                                    hashMap.put("busPersonType", ExcelUtil.getCellValue(row.getCell(12)));
                                    hashMap.put("idno", ExcelUtil.getCellValue(row.getCell(6)));
                                    hashMap.put("ensureCode", ensureCode);
                                    hashMap.put("serialNo", (i - 1) + "");
                                    // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确
                                    if ("0".equals(hashMap.get("idtype"))) {
                                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(1)))) {
                                            String s = CheckUtils
                                                    .checkChineseName(ExcelUtil.getCellValue(row.getCell(1)));
                                            if (!StringUtil.isEmpty(s)) {
                                                errorMsg += "员工清单第" + (i + 1) + "行,姓名" + s + "/";
                                            }
                                        }
                                        FCPerInfoTemp fcPerInfoTemp = new FCPerInfoTemp();
                                        fcPerInfoTemp.setIDNo(hashMap.get("idno"));
                                        fcPerInfoTemp.setBirthDay(ExcelUtil.getCellFormula(row.getCell(10)));
                                        fcPerInfoTemp.setSex(sex);
                                        // 校验身份证与出生日期、性别的是否一致
                                        String errMsg = checkOneIDCard(fcPerInfoTemp);
                                        if (StringUtils.isNotBlank(errMsg)) {
                                            errorMsg += "员工清单第" + (i + 1) + "行," + errMsg + "/";
                                        }
                                    }
                                    if ("1".equals(hashMap.get("idtype"))) {
                                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(1)))) {
                                            String s = CheckUtils.checkForeignName(ExcelUtil.getCellValue(row.getCell(1)));
                                            if (!StringUtil.isEmpty(s)) {
                                                errorMsg += "员工清单第" + (i + 1) + "行,姓名" + s + "/";
                                            }
                                        }
                                    }

                                    personal.put("sex", sex);
                                    personal.put("name", CheckUtils.checkForeignName(ExcelUtil.getCellValue(row.getCell(1))));
                                    personal.put("ID", hashMap.get("idno"));
                                    personal.put("birthDay", ExcelUtil.getCellFormula(row.getCell(10)));
                                    personal.put("phone", ExcelUtil.getCellFormula(row.getCell(8)));
                                    personalMap.put(hashMap.get("idno"), personal);
                                    Map<String, String> map = new HashMap<>();
                                    //1：员工 2：家属
                                    map.put("sign", "1");
                                    map.put("idType", hashMap.get("idtype"));
                                    map.put("idNo", hashMap.get("idno"));
                                    map.put("birthDay", ExcelUtil.getCellFormula(row.getCell(10)));
                                    map.put("sex", sex);
                                    map.put("nativeplace", nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(4))));
                                    // 人员信息
                                    checkCustomer.add(EvaluationCustomer.builder()
                                            .name(ExcelUtil.getCellValue(row.getCell(1)))
                                            .idType(CoreIdType.getNameByCoreId(hashMap.get("idtype")).name())
                                            .idNo(hashMap.get("idno"))
                                            .gender(GenderType.getGenderByCoreId(sex).name())
                                            .businessNo(ensureCode)
                                            .birthday(ExcelUtil.getCellFormula(row.getCell(10)))
                                            .nationality(nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(4))))
                                            .build());

                                    String sameCustomer = addressCheckService.checkSameCustomer(CheckSameCustomerRequest.builder()
                                            .customerBirthday(ExcelUtil.getCellFormula(row.getCell(10)))
                                            .customerIDNo(hashMap.get("idno"))
                                            .customerIDType(hashMap.get("idtype"))
                                            .customerName(ExcelUtil.getCellValue(row.getCell(1)))
                                            .customerSex(sex)
                                            .build());
                                    if (StringUtils.isNotBlank(sameCustomer)) {
                                        errorMsg += "员工清单第" + (i + 1) + "行 " + hashMap.get("name") + sameCustomer + "/";
                                    }
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(7)))) {
                                        map.put("idTypeEndDate", ExcelUtil.getCellValue(row.getCell(7)));//证件有效期
                                    }
                                    map.put("busPersonType", ExcelUtil.getCellValue(row.getCell(12)));// 员工职级
                                    map.put("occupationCode", ExcelUtil.getCellValue(row.getCell(14)));//职业代码
                                    map.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
                                    /**
                                     * 证件类型和国籍
                                     */
                                    String resultMsg = CheckUtils.checkSinglePeople(map);
                                    if (StringUtils.isNotBlank(resultMsg)) {
                                        errorMsg += "员工清单第" + (i + 1) + "行 " + hashMap.get("name") + resultMsg + "/";
                                    }
                                    listPerIfo.add(hashMap);
                                    listIdNo.add(ExcelUtil.getCellValue(row.getCell(6)));
                                    if (ExcelUtil.isCellEmpty(row.getCell(11))) {
                                        errorMsg += "员工清单第" + (i + 1) + "行，默认计划编码不能为空/";
                                    } else {
                                        String seque = ExcelUtil.getCellValue(row.getCell(11));
                                        // 判断序号是否为数字
                                        if (!CommonUtil.isNumeric(seque)) {
                                            errorMsg += "员工清单第" + (i + 1) + "行，默认计划编码不存在，请详见Excel中提供的默认编码！/";
                                        } else {
                                            Double mainWastage = Double.parseDouble(seque);
                                            if (mainWastage.intValue() - mainWastage != 0) {// 判断是否符合取整条件
                                                errorMsg += "员工清单第" + (i + 1) + "行，默认计划编码不存在，请详见Excel中提供的默认编码！/";
                                            } else {
                                                listdefaultPlanCode.add(mainWastage.intValue() + "");
                                            }
                                        }
                                    }
                                    listbusPersonType.add(ExcelUtil.getCellValue(row.getCell(12)));
                                }
                            }
                            Map<String, String> palnMap = new HashMap<>();
                            palnMap.put("ensureCode", ensureCode);
                            palnMap.put("planObject", "1");

                            // 批量查询 默认计划编码 是否存在
                            List<String> defaultPlanCodeCollection = fcEnsurePlanMapper.existListdefultPlanCode(palnMap);
                            for (int i = 0; i < listdefaultPlanCode.size(); i++) {
                                if (!defaultPlanCodeCollection.contains(listdefaultPlanCode.get(i))) {
                                    errorMsg += "员工清单第" + (i + 3) + "行的数据，默认计划编码不存在，请详见Excel中提供的默认编码！/";
                                }
                            }
                            // 批量查询  员工职级    是否存在
                            for (int i = 0; i < listbusPersonType.size(); i++) {
                                if (!levelList.contains(listbusPersonType.get(i))) {
                                    errorMsg += "员工清单第" + (i + 3) + "行的数据，员工职级不存在，请详见Excel中提供的员工职级！/";
                                }
                            }

                            // 判断模板中是否存在相同的证件号
                            HashSet<String> set = new HashSet<>(listIdNo);
                            if (listIdNo.size() != set.size()) {
                                // 获得list与set的差集
                                Collection rs = CollectionUtils.disjunction(listIdNo, set);
                                // 将collection转换为list
                                List<String> list1 = new ArrayList<>(rs);
                                for (String str : list1) {
                                    String serialNo = "";
                                    for (HashMap<String, String> hashMap : listPerIfo) {
                                        if (hashMap.get("idno").equals(str)) {
                                            serialNo += hashMap.get("serialNo") + ",";
                                        }
                                    }
                                    errorMsg += "员工清单第" + serialNo.substring(0, serialNo.length() - 1) + "行证件号重复/";
                                }
                            }
                            // 根据证件号判断正式表是否已存在
                            if (listPerIfo != null && listPerIfo.size() > 0) {

                                // 根据证件号、福利编号判断临时表是否已存在
                                List<FCPerInfoTemp> checkTempIdNoIsExists = checkTempIdNoIsExists(grpNo, listPerIfo);
                                if (checkTempIdNoIsExists.size() > 0) {// 当前福利编号下已存在相同的证件号
                                    for (FCPerInfoTemp fcPerInfoTemp : checkTempIdNoIsExists) {
                                        for (HashMap<String, String> hashMap : listPerIfo) {
                                            if (hashMap.get("idno").equals(fcPerInfoTemp.getIDNo())) {
                                                errorMsg += "员工清单第" + (Integer.parseInt(hashMap.get("serialNo")) + 2) + "行当前福利下已存在相同的证件号，请检查/";
                                            }
                                        }
                                    }
                                }
                                // 存在相同的证件号
                                if (!checkIdNoIsExists(listPerIfo)) {
                                    boolean br = false;
                                    // 判断是否存在证件号存在，但是其他四要素不同的数据
                                    List<FCPerInfoTemp> checkOtherIsEsists = checkOtherIsEsists(listPerIfo);
                                    if (checkOtherIsEsists.size() > 0) {// 存在证件号相同，其他四要素不同的数据
                                        for (FCPerInfoTemp fcPerInfoTemp : checkOtherIsEsists) {
                                            for (HashMap<String, String> hashMap : listPerIfo) {
                                                if (hashMap.get("idno").equals(
                                                        fcPerInfoTemp.getIDNo())) {
                                                    errorMsg += "员工清单第" + (Integer.parseInt(hashMap.get("serialNo")) + 2) + "行员工证件号，当前系统下存在相同的证件号，但是姓名、性别、出生日期、证件类型不同的数据/";
                                                    br = true;
                                                }
                                            }
                                            if (br) {
                                                break;
                                            }
                                        }
                                    }
                                }
                            }

                            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                                rowNum = i - 1;
                                row = sheet.getRow(i);
                                if (ExcelUtil.isRowEmpty(row, 2)) {
                                    log.info("员工清单第" + (i - 1) + "行是无效数据。");
                                } else {
                                    // 校验证件类型代码、职业类别代码、职业代码、银行码表是否录入符合规则
                                    errorMsg += checkCodeKey2(i + 1, row, idTypeList, occupationTypeList, occupationCodeList, openBankList, fcEnsure.getPlanType());
                                    // 校验当前职业代码是否符合当前职业类别
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(13))) && StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(14)))) {
                                        if (!occupationTypeList.contains(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(13))))) {
                                            errorMsg += "员工清单第" + (i + 1) + "行职业类别不存在/";
                                        }
                                        if (!occupationCodeList.contains(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(14))))) {
                                            errorMsg += "员工清单第" + (i + 1) + "行所录职业不存在/";
                                        }
                                        if (!occupationTypeCodeMap.get(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(13)))).contains(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(14))))) {
                                            errorMsg += "员工清单第" + (i + 1) + "行职业类别不包含所录职业/";
                                        }
                                    }
                                    String relationship = ExcelUtil.getCellFormula(row.getCell(3));
                                    for (HashMap<String, Object> map : relationshipCodeList) {
                                        if (relationship.equals(map.get("CodeName").toString())) {
                                            relationship = map.get("CodeKey").toString();
                                            break;
                                        }
                                    }
                                    // 员工信息临时表
                                    FCPerInfoTemp fcPerTempInfo = new FCPerInfoTemp();
                                    fcPerTempInfo.setGrpNo(grpNo);// 企业号
                                    fcPerTempInfo.setName(ExcelUtil.getCellValue(row.getCell(1)));// 姓名
                                    fcPerTempInfo.setDepartment("");
                                    fcPerTempInfo.setRelationship(relationship);
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(2)))) {
                                        fcPerTempInfo.setDepartment(ExcelUtil.getCellValue(row.getCell(2)));// 部门
                                    }
                                    //国籍
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(4)))) {
                                        fcPerTempInfo.setNativeplace(nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(4))));
                                    }
                                    //性别码值转换
                                    String sex = "";
                                    int index = 0;
                                    for (HashMap<String, Object> hashMap : sexCodeList) {
                                        if (ExcelUtil.getCellFormula(row.getCell(9)).equals(hashMap.get("CodeName").toString())) {
                                            sex = hashMap.get("CodeKey").toString();
                                            break;
                                        }
                                        index++;
                                        if (index == sexCodeList.size()) {
                                            errorMsg += "员工清单第" + (i + 1) + "行性别录入错误/";
                                        }
                                    }
                                    fcPerTempInfo.setSex(sex);
                                    fcPerTempInfo.setBirthDay(ExcelUtil.getCellFormula(row.getCell(10)));// 出生日期--公式值
                                    fcPerTempInfo.setMobilePhone(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(8))));// 手机号
                                    //证件类型码值转换
                                    String idType = "";
                                    for (HashMap<String, Object> hashMap : idTypeCodeList) {
                                        if (ExcelUtil.getCellValue(row.getCell(5)).equals(hashMap.get("CodeName").toString())) {
                                            idType = hashMap.get("CodeKey").toString();
                                            break;
                                        }
                                    }
                                    fcPerTempInfo.setIDType(idType);// 证件类型
                                    fcPerTempInfo.setIDNo(ExcelUtil.getCellValue(row.getCell(6)).toUpperCase());// 证件号
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(7)))) {
                                        fcPerTempInfo.setIdTypeEndDate(ExcelUtil.getCellValue(row.getCell(7)));//证件有效期
                                    }
                                    fcPerTempInfo.setLevelCode(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(12))));// 员工职级
                                    fcPerTempInfo.setOccupationType(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(13))));// 职业类别
                                    fcPerTempInfo.setOccupationCode(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(14))));// 职业编码
                                    String JoinMedProtect = "";
                                    if ("有".equals(ExcelUtil.getCellValue(row.getCell(15)))) {
                                        JoinMedProtect = "1";
                                    } else if ("无".equals(ExcelUtil.getCellValue(row.getCell(15)))) {
                                        JoinMedProtect = "0";
                                    } else {
                                        errorMsg += "员工清单第" + (i + 1) + "行有无医保只可录入（有、无），请重新录入/";
                                    }
                                    fcPerTempInfo.setJoinMedProtect(JoinMedProtect);// 有无医保
                                    fcPerTempInfo.setOpenBank(ExcelUtil.getCellValue(row.getCell(16)));// 开户行
                                    fcPerTempInfo.setOpenAccount(ExcelUtil.getCellValue(row.getCell(17)));// 开户账号
                                    String planCode = ExcelUtil.getCellValue(row.getCell(11));
                                    Map<String, String> param = new HashMap<>();
                                    param.put("ensureCode", ensureCode);
                                    param.put("planCode", planCode);
                                    FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                                    //场地险投保只有一种个人批扣的支付方式，并且场地险要求福利额度为空，开户行账号不能为空 add by wudezhong 2021.12.7
                                    // 校验员工福利额度
                                    Double totalPrem = 0.00;
                                    if (fcEnsurePlan != null && fcEnsurePlan.getTotalPrem() != null) {
                                        totalPrem = fcEnsurePlan.getTotalPrem();
                                    }
                                    // 获取表格中的员工额度和家属额度
                                    String strGrpPrem = getStrToPlanString(ExcelUtil.getCellValue(row.getCell(18)));
                                    boolean str = strGrpPrem.contains(".");
                                    if (str) {
                                        errorMsg += "员工清单第" + (i + 1) + "行员工福利额度不能为小数/";
                                    }
                                    String famGrpPrem = getStrToPlanString(ExcelUtil.getCellValue(row.getCell(19)));
                                    boolean fam = famGrpPrem.contains(".");
                                    if (fam) {
                                        errorMsg += "员工清单第" + (i + 1) + "行家属福利额度不能为小数/";
                                    }

                                    if ("".equals(strGrpPrem) || null == strGrpPrem) {
                                        // 员工福利额度
                                        errorMsg += "员工清单第" + (i + 1) + "行员工福利额度不能为空！/";
                                    } else {
                                        Double staffGrpPrem = Double.parseDouble(strGrpPrem);
                                        if (staffGrpPrem < totalPrem) {
                                            errorMsg += "员工清单第" + (i + 1) + "行员工福利额度要大于等于默认计划的保费/";
                                        }
                                        fcPerTempInfo.setStaffGrpPrem(staffGrpPrem);
                                    }
                                    if (!"".equals(famGrpPrem)) {
                                        // 家属福利额度
                                        Double familyGrpPrem = Double.parseDouble(famGrpPrem);
                                        if (familyGrpPrem < 0) {
                                            errorMsg += "员工清单第" + (i + 1) + "行家属福利额度应大于0/";
                                        }
                                        fcPerTempInfo.setFamilyGrpPrem(familyGrpPrem);
                                    }
                                    fcPerTempInfo.setOperator(operator);// 操作员
                                    String perTempNo = maxNoService.createMaxNo("PerTempNo", "", 20);
                                    fcPerTempInfo.setPerTempNo(perTempNo);// 流水号
                                    fcPerTempInfo.setEnsureCode(ensureCode);// 福利编号

                                    String dPlanCode = "";
                                    if (ExcelUtil.isCellEmpty(row.getCell(11))) {
                                        fcPerTempInfo.setDefaultPlan(dPlanCode);
                                    } else {
                                        String cellValue = ExcelUtil.getCellValue(row.getCell(11));
                                        // 判断序号是否为数字
                                        if (!CommonUtil.isNumeric(cellValue)) {
                                            fcPerTempInfo.setDefaultPlan(dPlanCode);
                                        } else {
                                            int value = new Double(Double.valueOf(cellValue)).intValue();
                                            dPlanCode = String.valueOf(value);
                                            fcPerTempInfo.setDefaultPlan(dPlanCode);
                                        }
                                    }
                                    fcPerTempInfo.setErrorMsg("");// 错误原因
                                    fcPerTempInfo.setImpotStatus("01");// 导入状态（01-导入成功；02-导入失败）
                                    fcPerTempInfo.setSubStaus("01");// 提交状态（01-未提交；02-已提交）
                                    fcPerTempInfo = CommonUtil.initObject(fcPerTempInfo, "INSERT");
                                    Map<String, String> perInfoMap = new HashMap<>();
                                    perInfoMap.put("name", fcPerTempInfo.getName());
                                    perInfoMap.put("iDType", fcPerTempInfo.getIDType());
                                    perInfoMap.put("iDNo", fcPerTempInfo.getIDNo());
                                    perInfoMap.put("perTempNo", perTempNo);
                                    perInList.add(perInfoMap);
                                    perInfoTemplist.add(fcPerTempInfo);
                                }
                            }
                        } else if (j == 1) {
                            log.info(sheet.getLastRowNum() + "");
                            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                                row = sheet.getRow(i);
                                FCPerinfoFamilyTemp fcPerinfoFamilyTemp = new FCPerinfoFamilyTemp();
                                if (ExcelUtil.isRowEmpty(row, 6)) {
                                    log.info("第" + i + "行数据无效");
                                } else {
                                    if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(4)))) {
                                        errorMsg += "家属清单第" + (i + 1) + "行,证件号填写格式有误，建议将格式调整为文本格式!/";
                                    }
                                    if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(15)))) {
                                        errorMsg += "家属清单第" + (i + 1) + "行,员工证件号填写格式有误，建议将格式调整为文本格式!/";
                                    }
                                    fcPerinfoFamilyTemp.setName(ExcelUtil.getCellValue(row.getCell(1)));
                                    //校验国籍
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(2)))) {
                                        String nativeplacecode = nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(2)));
                                        if (StringUtils.isBlank(nativeplacecode)) {
                                            errorMsg += "家属清单第" + (i + 1) + "行,国籍填写有误!/";
                                        }
                                    }
                                    String nativePlace = ExcelUtil.getCellValue(row.getCell(2));
                                    fcPerinfoFamilyTemp.setNativeplace(nativeplaceMap.get(nativePlace));
                                    String IDType = "";
                                    String PerIDType = ExcelUtil.getCellValue(row.getCell(14));
                                    //家属及员工证件类型码值转化
                                    for (HashMap idType : idTypeCodeList) {
                                        if (idType.get("CodeName").toString().equals(ExcelUtil.getCellValue(row.getCell(3)))) {
                                            IDType = idType.get("CodeKey").toString();
                                        }
                                        if (idType.get("CodeName").toString().equals(ExcelUtil.getCellValue(row.getCell(14)))) {
                                            PerIDType = idType.get("CodeKey").toString();
                                        }
                                    }
                                    String idno = ExcelUtil.getCellValue(row.getCell(4));
                                    if ("0".equals(IDType)) {
                                        if (!IDCardUtil.isIDCard(idno)) {
                                            errorMsg += "家属清单第" + (i + 1) + "行身份证号格式错误：" + idno + "！/";
                                        } else if (idno.length() != 18) {
                                            errorMsg += "家属清单第" + (i + 1) + "行身份证号应为18位！/";
                                        } else {
                                            errorMsg += isCheckFamilyEmpTy(i + 1, row);
                                            if (StringUtils.isNotEmpty(errorMsg)) {
                                                Splitter split = Splitter.on('/').trimResults().omitEmptyStrings(); // 去前后空格&&去空string
                                                errorMsgList = new ArrayList<>(split.splitToList(errorMsg));
                                                resultMap.put("code", "500");
                                                resultMap.put("success", false);
                                                resultMap.put("message", errorMsgList);
                                            }
                                            DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
                                            Date date = format1.parse(ExcelUtil.getCellValue(row.getCell(8)));
                                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                                            String dateString = formatter.format(date);
                                            String idBir = IDCardUtil.dateOfBirth(idno);
                                            if (!dateString.equals(idBir)) {
                                                errorMsg += "家属清单第" + (i + 1) + "行出生日期与身份证不符/";
                                            }
                                            if (!getGenderByIdCard(idno)
                                                    .equals(ExcelUtil.getCellValue(row.getCell(7)))) {
                                                errorMsg += "家属清单第" + (i + 1) + "行性别与身份证不符/";
                                            }
                                        }
                                    }

                                    if ("1".equals(IDType)) {
                                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(1)))) {
                                            String s = CheckUtils.checkForeignName(ExcelUtil.getCellValue(row.getCell(1)));
                                            if (!StringUtil.isEmpty(s)) {
                                                errorMsg += "员工清单第" + (i + 1) + "行,姓名" + s;
                                            }
                                        }
                                    } else {
                                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(1)))) {
                                            String s = CheckUtils.checkChineseName(ExcelUtil.getCellValue(row.getCell(1)));
                                            if (!StringUtil.isEmpty(s)) {
                                                errorMsg += "员工清单第" + (i + 1) + "行,姓名" + s;
                                            }
                                        }
                                    }

                                    fcPerinfoFamilyTemp.setIDType(IDType);
                                    fcPerinfoFamilyTemp.setPerIDType(PerIDType);
                                    fcPerinfoFamilyTemp.setIDNo(ExcelUtil.getCellValue(row.getCell(4)));
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(5)))) {
                                        fcPerinfoFamilyTemp.setIdTypeEndDate(ExcelUtil.getCellValue(row.getCell(5)));
                                    }
                                    Map<String, String> personMap = personalMap.get(ExcelUtil.getCellValue(row.getCell(15)));
                                    if (StringUtils.isNotEmpty(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(6))))) {
                                        if (!checkScientifiNotation(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(6))))
                                                || !CheckUtils.checkMobilePhone(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(6))))) {
                                            errorMsg += "家属清单第" + (i + 1) + "行,手机号填写格式有误!/";
                                        }
                                    }
                                    fcPerinfoFamilyTemp.setPhone(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(6))));
                                    // 判断家属手机号是否与投保人员工一致
                                    if (StringUtils.isNotEmpty(fcPerinfoFamilyTemp.getPhone()) && null != personMap && fcPerinfoFamilyTemp.getPhone().equals(personMap.get("phone"))) {
                                        errorMsg += "家属清单第" + (i + 1) + "行,手机号与投保员工手机号一致!/";
                                    }

                                    String Sex = ExcelUtil.getCellValue(row.getCell(7));
                                    //家属性别码值转化
                                    if (StringUtils.isNotBlank(Sex)) {
                                        if (!"男".equals(ExcelUtil.getCellValue(row.getCell(7))) && !"女".equals(ExcelUtil.getCellValue(row.getCell(7)))) {
                                            errorMsg += "家属清单第" + (i + 1) + "行,性別不符合录入规则（男|女）!/";
                                        }
                                    }
                                    for (HashMap sex : sexCodeList) {
                                        if (sex.get("CodeName").toString().equals(ExcelUtil.getCellValue(row.getCell(7)))) {
                                            Sex = sex.get("CodeKey").toString();
                                            break;
                                        }
                                    }

                                    fcPerinfoFamilyTemp.setSex(Sex);
                                    fcPerinfoFamilyTemp.setBirthDay(ExcelUtil.getCellValue(row.getCell(8)));
                                    String occType = getStrToPlanString(ExcelUtil.getCellValue(row.getCell(9)));
                                    String occCode = getStrToPlanString(ExcelUtil.getCellValue(row.getCell(10)));
                                    //职业类别校验
                                    if (StringUtils.isNotBlank(occType)) {
                                        if (!occupationTypeList.contains(occType)) {
                                            errorMsg += "家属清单第" + (i + 1) + "行职业类别录入错误，请重新录入！/";
                                        }
                                    }
                                    fcPerinfoFamilyTemp.setOccupationType(occType);
                                    //职业代码校验
                                    if (StringUtils.isNotBlank(occCode)) {
                                        if (!occupationCodeList.contains(occCode)) {
                                            errorMsg += "家属清单第" + (i + 1) + "行职业代码录入错误，请重新录入！/";
                                        }
                                    }
                                    fcPerinfoFamilyTemp.setOccupationCode(occCode);
                                    if (StringUtils.isNotBlank(occType) && StringUtils.isNotBlank(occCode) && occupationTypeList.contains(occType) && occupationCodeList.contains(occCode)) {
                                        List<String> OccupationCodeList = occupationTypeCodeMap.get(occType);
                                        HashSet hashSet = new HashSet(OccupationCodeList);
                                        if (!hashSet.contains(occCode)) {
                                            errorMsg += "家属清单第" + (i + 1) + "行职业类别不包含该职业代码，请重新选择！/";
                                        }
                                    }

                                    if ("无".equals(ExcelUtil.getCellValue(row.getCell(11)))) {
                                        fcPerinfoFamilyTemp.setJoinMedProtect("0");
                                    } else if ("有".equals(ExcelUtil.getCellValue(row.getCell(11)))) {
                                        fcPerinfoFamilyTemp.setJoinMedProtect("1");
                                    } else {
                                        errorMsg += "家属清单第" + (i + 1) + "行有无医保只可录入（有、无），请重新录入/";
                                    }
                                    String Realtion = ExcelUtil.getCellValue(row.getCell(12));
                                    for (HashMap relation : relationMap) {
                                        if (relation.get("CodeName").equals(Realtion)) {
                                            Realtion = relation.get("CodeKey").toString();
                                            break;
                                        }
                                    }

                                    fcPerinfoFamilyTemp.setRelation(Realtion);
                                    fcPerinfoFamilyTemp.setPerName(ExcelUtil.getCellValue(row.getCell(13)));
                                    fcPerinfoFamilyTemp.setPerIDNo(ExcelUtil.getCellValue(row.getCell(15)));
                                    String name = fcPerinfoFamilyTemp.getPerName();
                                    String idType = fcPerinfoFamilyTemp.getPerIDType();
                                    String idNo = fcPerinfoFamilyTemp.getPerIDNo();
                                    int sum = 0;
                                    Map<String, Object> param = new HashMap<>();
                                    param.put("grpNo", grpNo);
                                    param.put("ensureCode", ensureCode);
                                    List<Map<String, String>> fcPerInfoList = fcPerInfoMapper.selectByParamList(param);
                                    perInList.addAll(fcPerInfoList);
                                    if (perInList.size() == 0) {
                                        errorMsg += "该福利未录入相关员工信息，请录入！/";
                                    } else if (perInList.size() > 0) {
                                        for (int k = 0; k < perInList.size(); k++) {
                                            if (idType.equals(perInList.get(k).get("iDType")) && idNo.equalsIgnoreCase(perInList.get(k).get("iDNo")) && name.equals(perInList.get(k).get("name"))) {
                                                fcPerinfoFamilyTemp.setPerTempNo(perInList.get(k).get("perTempNo"));
                                                break;
                                            } else {
                                                sum++;
                                            }
                                            if (sum == perInList.size()) {
                                                errorMsg += "家属清单第" + (i + 1) + "行员工信息不存在,请重新录入！/";
                                            }
                                        }
                                    }
                                    // 根据证件号、福利编号判断临时表是否已存在
                                    int checkFamilyCount = fcPerinfoFamilyTempMapper.getFamilyCountByEnsureCode(fcPerinfoFamilyTemp);
                                    if (checkFamilyCount > 0) {
                                        errorMsg += "家属清单第" + (i + 1) + "行，当前福利下已存在所录员工以及家属相同的证件号，请重新录入!/";  //当前福利下员工已经存在与该家属的数据
                                    }

                                    if (fcPerinfoFamilyTemp.getIDNo().equals(idNo)) {
                                        errorMsg += "家属清单第" + (i + 1) + "行家属所录证件号不可与所录员工相同!/";
                                    }

                                    Map<String, String> checkMap = new HashMap<>();
                                    checkMap.put("sex", fcPerinfoFamilyTemp.getSex());
                                    checkMap.put("idNo", fcPerinfoFamilyTemp.getIDNo());
                                    checkMap.put("sign", "2");
                                    checkMap.put("idType", fcPerinfoFamilyTemp.getIDType());
                                    checkMap.put("birthDay", fcPerinfoFamilyTemp.getBirthDay());
                                    checkMap.put("nativeplace", fcPerinfoFamilyTemp.getNativeplace());
                                    checkMap.put("idTypeEndDate", fcPerinfoFamilyTemp.getIdTypeEndDate());
                                    checkMap.put("occupationCode", fcPerinfoFamilyTemp.getOccupationCode());
                                    checkMap.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
                                    /**
                                     * 证件类型和国籍
                                     */
                                    String resultMsg = CheckUtils.checkSinglePeople(checkMap);
                                    checkCustomer.add(EvaluationCustomer.builder()
                                            .name(fcPerinfoFamilyTemp.getName())
                                            .idType(CoreIdType.getNameByCoreId(fcPerinfoFamilyTemp.getIDType()).name())
                                            .idNo(fcPerinfoFamilyTemp.getIDNo())
                                            .gender(GenderType.getGenderByCoreId(fcPerinfoFamilyTemp.getSex()).name())
                                            .birthday(fcPerinfoFamilyTemp.getBirthDay())
                                            .nationality(fcPerinfoFamilyTemp.getNativeplace())
                                            .build());
                                    if (StringUtils.isNotBlank(resultMsg)) {
                                        errorMsg += "家属清单第" + (i + 1) + "行 " + ExcelUtil.getCellValue(row.getCell(1)) + resultMsg + "/";
                                    }
                                    fcPerinfoFamilyTemp.setEnsureCode(ensureCode);
                                    fcPerinfoFamilyTemp.setSubStaus("01");
                                    fcPerinfoFamilyTemp.setOperator(operator);
                                    fcPerinfoFamilyTemp.setRelationship("06");
                                    fcPerinfoFamilyTemp.setFamilyTempNo(maxNoService.createMaxNo("FamilyTempNo", "", 20));
                                    fcPerinfoFamilyTemp = CommonUtil.initObject(fcPerinfoFamilyTemp, "INSERT");
                                    familyMapList.add(fcPerinfoFamilyTemp);
                                    familyCount++;
                                }
                            }
                            if (familyMapList != null && familyMapList.size() > 0) {

                                // 判断家属清单中家属证件号相同其他四要素是否一致
                                int k = 3;
                                List<String> idList = new ArrayList<>();
                                for (FCPerinfoFamilyTemp family : familyMapList) {
                                    if (!idList.contains(family.getIDNo())) {
                                        int i = 3;
                                        String serialNo = "";
                                        for (FCPerinfoFamilyTemp familys : familyMapList) {
                                            if (familys.getIDNo().equals(family.getIDNo())) {
                                                if (!familys.getIDType().equals(family.getIDType()) || !familys.getName().equals(family.getName()) || !familys.getSex().equals(family.getSex()) || !familys.getBirthDay().equals(family.getBirthDay())) {
                                                    serialNo += i + ",";
                                                }
                                            }
                                            i++;
                                        }
                                        if (StringUtils.isNotBlank(serialNo)) {
                                            errorMsg += "家属清单第" + serialNo.substring(0, serialNo.length() - 1) + "行与清单第" + k + "行家属证件号：" + (family.getIDNo()) + "重复,但姓名、性别、出生日期以及证件类型存在不同，请统一所录监护人信息！/";
                                        }
                                    }
                                    k++;
                                    idList.add(family.getIDNo());
                                }

                                //判断家属证件号相同时，员工证件号是否相同
                                for (int i = 0; i < familyMapList.size(); i++) {
                                    for (int l = i; l < familyMapList.size(); l++) {
                                        if (familyMapList.get(i).getIDNo().equals(familyMapList.get(l).getIDNo())) {
                                            if (i != l) {
                                                if (familyMapList.get(i).getPerIDNo().equals(familyMapList.get(l).getPerIDNo())) {
                                                    errorMsg += "家属清单第" + (i + 3) + "行与第" + (l + 3) + "行家属证件号及员工证件号均相同，请重新录入!/";
                                                }
                                            }
                                        }
                                    }
                                }

                                for (int i = 0; i < familyMapList.size(); i++) {
                                    if (!checkStuIdNoIsExists(familyMapList.get(i).getIDNo())) {   //校验正式表 当前企业下是否存在同一证件号  存在 进行五要素比对 fcperson
                                        Map<String, String> checkMap = new HashMap<>();
                                        checkMap.put("idNo", familyMapList.get(i).getIDNo());
                                        checkMap.put("idType", familyMapList.get(i).getIDType());
                                        checkMap.put("name", familyMapList.get(i).getName());
                                        checkMap.put("sex", familyMapList.get(i).getSex());
                                        checkMap.put("birthday", familyMapList.get(i).getBirthDay());
                                        if (!checkStuIdNoIsExistsTwo(checkMap)) {        //五要素不同
                                            errorMsg += "家属清单第" + (i + 3) + "行家属证件号，当前系统存在相同的证件号，但是姓名、性别、出生日期、证件类型不同，请检查/";

                                        }
                                    }
                                }
                            }
                        }
                    }
                } else if ("1".equals(fcEnsure.getPlanType())) {          //弹性计划员工清单解析
                    if (levelList.size() < 0) {
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        resultMap.put("message", "由于该弹性计划未定制职级，不能进行人员导入！");
                        return resultMap;
                    }
                    //获取员工服务年限 是否退休
                    List<FcBusinessProDutyGrpObject> fcBusinessProDutyGrpObjectList = fcBusinessProDutyGrpObjectMapper.selectByEnsureCode(ensureCode, "01");
                    int sheetSum = sheetCount(wb);
                    List<Map<String, String>> perInList = new ArrayList<>();
                    for (int j = 0; j < 2; j++) {
                        int rowNum = 0;
                        Sheet sheet = wb.getSheetAt(j);
                        Row row = null;
                        if (j == 0) {
                            List<HashMap<String, String>> listPerIfo = new ArrayList<>();
                            List<String> listIdNo = new ArrayList<>();
                            List<String> listDefaultPlanCode = new ArrayList<>();
                            log.info(sheet.getLastRowNum() + "");
                            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                                rowNum = i - 1;
                                row = sheet.getRow(i);
                                if (ExcelUtil.isRowEmpty(row, 2)) {
                                    log.info("员工清单第" + (i + 1) + "行是无效数据。");
                                } else {
                                    count++;
                                    Map<String, String> personal = new HashMap<>();
                                    // 校验字段非空
                                    errorMsg += checkEflexIsEmpTy(i + 1, row);
                                    if (!checkScientifiNotation(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(7))))
                                            || !CheckUtils.checkMobilePhone(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(7))))) {
                                        errorMsg += "员工清单第" + (i + 1) + "行,手机号填写格式有误!/";
                                    }
                                    if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(5)))) {
                                        errorMsg += "员工清单第" + (i + 1) + "行,证件号填写格式有误，建议将格式调整为文本格式!/";
                                    }
                                    //校验国籍
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(3)))) {
                                        String nativeplacecode = nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(3)));
                                        if (StringUtils.isBlank(nativeplacecode)) {
                                            errorMsg += "员工清单第" + (i + 1) + "行,国籍填写有误!/";
                                        }
                                    }
                                    HashMap<String, String> hashMap = new HashMap<>();
                                    hashMap.put("name", ExcelUtil.getCellValue(row.getCell(1)));
                                    //性别的公式值转换
                                    String sex = ExcelUtil.getCellFormula(row.getCell(8));
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellFormula(row.getCell(8)))) {
                                        if (!"男".equals(sex) && !"女".equals(sex)) {
                                            errorMsg += "员工清单第" + (i + 1) + "行,性別不符合录入规则（男|女）!/";
                                        }
                                    }
                                    //性别码值转换
                                    for (HashMap<String, Object> hashMap2 : sexCodeList) {
                                        if (sex.equals(hashMap2.get("CodeName").toString())) {
                                            sex = hashMap2.get("CodeKey").toString();
                                            break;
                                        }
                                    }
                                    hashMap.put("sex", sex);
                                    //出生日期的公式值转换
                                    hashMap.put("birthday", ExcelUtil.getCellFormula(row.getCell(9)));
                                    //证件类型码值转换
                                    String idTypeExcel = ExcelUtil.getCellValue(row.getCell(4));
                                    if (idTypeExcel != null && !idTypeExcel.equals("")) {
                                        String idType = "";
                                        for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
                                            if (idTypeExcel.equals(hashMap1.get("CodeName").toString())) {
                                                idType = hashMap1.get("CodeKey").toString();
                                                break;
                                            }
                                        }
                                        hashMap.put("idtype", idType);
                                    } else {
                                        hashMap.put("idtype", "");
                                    }
                                    if (!StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(5))) && !StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(7)))) {
                                        if (fcPerInfoTempMapper.selectByPhone(ExcelUtil.getCellValue(row.getCell(7)),
                                                "0", ExcelUtil.getCellValue(row.getCell(5)),
                                                hashMap.get("idtype")) > 0) {
                                            errorMsg += "员工清单第" + (i + 1) + "行该客户手机号与既往其他客户的手机号重复，请确认！/";
                                        }
                                        int checkint = fdUserMapper.checkByPhone(ExcelUtil.getCellValue(row.getCell(7)), ExcelUtil.getCellValue(row.getCell(5)), "1");
                                        if (checkint > 0) {
                                            errorMsg += "员工清单第" + (i + 1) + "行,手机号已注册!/";
                                        }
                                    }
                                    hashMap.put("idno", ExcelUtil.getCellValue(row.getCell(5)));
                                    hashMap.put("ensureCode", ensureCode);
                                    hashMap.put("serialNo", (i - 1) + "");
                                    // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确
                                    if (hashMap.get("idtype").equals("0")) {
                                        FCPerInfoTemp fcPerInfoTemp = new FCPerInfoTemp();
                                        fcPerInfoTemp.setIDNo(hashMap.get("idno"));
                                        fcPerInfoTemp.setBirthDay(ExcelUtil.getCellFormula(row.getCell(9)));
                                        fcPerInfoTemp.setSex(sex);
                                        String errMsg = checkOneIDCard(fcPerInfoTemp);
                                        if (StringUtils.isNotBlank(errMsg)) {
                                            errorMsg += "员工清单第" + (i + 1) + "行," + errMsg + "!/";
                                        }
                                    }
                                    if (hashMap.get("idtype").equals("1")) {
                                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(1)))) {
                                            String s = CheckUtils.checkForeignName(ExcelUtil.getCellValue(row.getCell(1)));
                                            if (!StringUtil.isEmpty(s)) {
                                                errorMsg += "员工清单第" + (i + 1) + "行,姓名" + s;
                                            }
                                        }
                                    } else {
                                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(1)))) {
                                            String s = CheckUtils.checkChineseName(ExcelUtil.getCellValue(row.getCell(1)));
                                            if (!StringUtil.isEmpty(s)) {
                                                errorMsg += "员工清单第" + (i + 1) + "行,姓名" + s;
                                            }
                                        }
                                    }

                                    personal.put("sex", sex);
                                    personal.put("ID", hashMap.get("idno"));
                                    personal.put("birthDay", ExcelUtil.getCellFormula(row.getCell(9)));
                                    personal.put("phone", ExcelUtil.getCellFormula(row.getCell(7)));
                                    personalMap.put(hashMap.get("idno"), personal);
                                    Map<String, String> map = new HashMap<>();
                                    map.put("sign", "1");//1：员工 2：家属
                                    map.put("idType", hashMap.get("idtype"));//证件类型
                                    map.put("idNo", hashMap.get("idno"));//证件号
                                    map.put("birthDay", ExcelUtil.getCellFormula(row.getCell(9)));//出生日期
                                    map.put("sex", sex);//性别
                                    map.put("nativeplace", nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(3))));//国籍
                                    //人员信息
                                    checkCustomer.add(EvaluationCustomer.builder()
                                            .name(ExcelUtil.getCellValue(row.getCell(1)))
                                            .idType(CoreIdType.getNameByCoreId(hashMap.get("idtype")).name())
                                            .idNo(hashMap.get("idno"))
                                            .gender(GenderType.getGenderByCoreId(sex).name())
                                            .birthday(ExcelUtil.getCellFormula(row.getCell(9)))
                                            .nationality(nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(3))))
                                            .build());
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(6)))) {
                                        map.put("idTypeEndDate", ExcelUtil.getCellValue(row.getCell(6)));//证件有效期
                                    }
                                    map.put("occupationCode", getStrToPlanString(ExcelUtil.getCellValue(row.getCell(12))));//职业代码
                                    map.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
                                    /**
                                     * 证件类型和国籍
                                     */
                                    String resultMsg = CheckUtils.checkSinglePeople(map);
                                    if (StringUtils.isNotBlank(resultMsg)) {
                                        errorMsg += "员工清单第" + (i + 1) + "行 " + hashMap.get("name") + resultMsg + "/";
                                    }
                                    listPerIfo.add(hashMap);
                                    listIdNo.add(ExcelUtil.getCellValue(row.getCell(5)));
                                    listDefaultPlanCode.add(ExcelUtil.getCellValue(row.getCell(10)));
                                }
                            }
                            // 判断模板中是否存在相同的证件号
                            HashSet<String> set = new HashSet<>(listIdNo);
                            if (listIdNo.size() != set.size()) {
                                // 获得list与set的差集
                                Collection rs = CollectionUtils.disjunction(listIdNo, set);
                                // 将collection转换为list
                                List<String> list1 = new ArrayList<>(rs);
                                for (String str : list1) {
                                    String serialNo = "";
                                    for (HashMap<String, String> hashMap : listPerIfo) {
                                        if (hashMap.get("idno").equals(str)) {
                                            serialNo += hashMap.get("serialNo") + ",";
                                        }
                                    }
                                    errorMsg += "员工清单第" + serialNo.substring(0, serialNo.length() - 1) + "行证件号重复/";
                                }
                            }
                            // 根据证件号判断正式表是否已存在
                            if (listPerIfo != null && listPerIfo.size() > 0) {

                                // 根据证件号、福利编号判断临时表是否已存在
                                List<FCPerInfoTemp> checkTempIdNoIsExists = checkTempIdNoIsExists(grpNo, listPerIfo);
                                if (checkTempIdNoIsExists.size() > 0) {// 当前福利编号下已存在相同的证件号
                                    for (FCPerInfoTemp fcPerInfoTemp : checkTempIdNoIsExists) {
                                        for (HashMap<String, String> hashMap : listPerIfo) {
                                            if (hashMap.get("idno").equals(fcPerInfoTemp.getIDNo())) {
                                                errorMsg += "员工清单第" + (Integer.parseInt(hashMap.get("serialNo")) + 2) + "行当前福利下已存在相同的证件号，请检查/";
                                            }
                                        }
                                    }
                                }

                                if (!checkIdNoIsExistsTemp(grpNo, listPerIfo)) {// 存在相同的证件号
                                    // 判断是否存在证件号存在，但是其他四要素不同的数据
                                    List<FCPerInfoTemp> checkOtherIsEsists = checkOtherIsEsistsTemp(grpNo, listPerIfo);
                                    if (checkOtherIsEsists.size() > 0) {// 存在证件号相同，其他四要素不同的数据
                                        for (FCPerInfoTemp fcPerInfoTemp : checkOtherIsEsists) {
                                            for (HashMap<String, String> hashMap : listPerIfo) {
                                                if (hashMap.get("idno").equals(
                                                        fcPerInfoTemp.getIDNo())) {
                                                    errorMsg += "员工清单第" + (Integer.parseInt(hashMap.get("serialNo")) + 2) + "行,当前企业定制中的福利存在相同的证件号，但是姓名、性别、出生日期、证件类型不同，请核对后再进行录入/";
                                                }
                                            }
                                        }
                                    }
                                }


                                if (!checkIdNoIsExists(listPerIfo)) {// 存在相同的证件号
                                    boolean br = false;
                                    // 判断是否存在证件号存在，但是其他四要素不同的数据
                                    List<FCPerInfoTemp> checkOtherIsEsists = checkOtherIsEsists(listPerIfo);
                                    if (checkOtherIsEsists.size() > 0) {// 存在证件号相同，其他四要素不同的数据
                                        for (FCPerInfoTemp fcPerInfoTemp : checkOtherIsEsists) {
                                            for (HashMap<String, String> hashMap : listPerIfo) {
                                                if (hashMap.get("idno").equals(
                                                        fcPerInfoTemp.getIDNo())) {
                                                    errorMsg += "员工清单第" + (Integer.parseInt(hashMap.get("serialNo")) + 2) + "行员工证件号，当前系统下存在相同的证件号，但是姓名、性别、出生日期、证件类型不同的数据/";
                                                    br = true;
                                                }
                                            }
                                            if (br) {
                                                break;
                                            }
                                        }
                                    }
                                }
                            }

                            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                                rowNum = i - 1;
                                row = sheet.getRow(i);
                                if (ExcelUtil.isRowEmpty(row, 2)) {
                                    log.info("员工清单第" + (i - 1) + "行是无效数据。");
                                } else {
                                    // 校验证件类型代码、职业类别代码、职业代码、银行码表是否录入符合规则
                                    errorMsg += checkCodeKey(i + 1, row, idTypeList,
                                            occupationTypeList, occupationCodeList,
                                            openBankList, fcEnsure.getPlanType());
                                    // 校验当前职业代码是否符合当前职业类别
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(11))) && StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(12)))) {
                                        if (!occupationTypeList.contains(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(11))))) {
                                            errorMsg += "员工清单第" + (i + 1) + "行职业类别不存在/";
                                        }
                                        if (!occupationCodeList.contains(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(12))))) {
                                            errorMsg += "员工清单第" + (i + 1) + "行职业代码不存在/";
                                        }
                                        if (!occupationTypeCodeMap.get(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(11)))).contains(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(12))))) {
                                            errorMsg += "员工清单第" + (i + 1) + "行职业类别不包含所录职业/";
                                        }
                                    }

                                    // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确
                                    if (ExcelUtil.getCellValue(row.getCell(4)).equals("身份证")) {
                                        errorMsg += checkIDCard(i + 1, row);
                                    }
                                    // 员工信息临时表
                                    FCPerInfoTemp fcPerTempInfo = new FCPerInfoTemp();
                                    fcPerTempInfo.setGrpNo(grpNo);// 企业号
                                    fcPerTempInfo.setName(ExcelUtil.getCellValue(row.getCell(1)));// 姓名
                                    fcPerTempInfo.setDepartment("");
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(2)))) {
                                        fcPerTempInfo.setDepartment(ExcelUtil.getCellValue(row.getCell(2)));// 部门
                                    }
                                    //国籍
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(3)))) {
                                        fcPerTempInfo.setNativeplace(nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(3))));
                                    }
                                    //性别码值转换
                                    String sex = "";
                                    int index = 0;
                                    for (HashMap<String, Object> hashMap : sexCodeList) {
                                        if (ExcelUtil.getCellFormula(row.getCell(8)).equals(hashMap.get("CodeName").toString())) {
                                            sex = hashMap.get("CodeKey").toString();
                                            break;
                                        }
                                        index++;
                                        if (index == sexCodeList.size()) {
                                            errorMsg += "员工清单第" + (i + 1) + "行性别录入错误/";
                                        }
                                    }
                                    fcPerTempInfo.setSex(sex);
                                    fcPerTempInfo.setBirthDay(ExcelUtil.getCellFormula(row.getCell(9)));// 出生日期--公式值
                                    fcPerTempInfo.setMobilePhone(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(7))));// 手机号
                                    //证件类型码值转换
                                    String idType = "";
                                    for (HashMap<String, Object> hashMap : idTypeCodeList) {
                                        if (ExcelUtil.getCellValue(row.getCell(4)).equals(hashMap.get("CodeName").toString())) {
                                            idType = hashMap.get("CodeKey").toString();
                                            break;
                                        }
                                    }
                                    fcPerTempInfo.setIDType(idType);// 证件类型
                                    fcPerTempInfo.setIDNo(ExcelUtil.getCellValue(row.getCell(5)).toUpperCase());// 证件号
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(6)))) {
                                        fcPerTempInfo.setIdTypeEndDate(ExcelUtil.getCellValue(row.getCell(6)));//证件有效期
                                    }
                                    fcPerTempInfo.setOccupationType(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(11))));// 职业类别
                                    fcPerTempInfo.setOccupationCode(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(12))));// 职业编码
                                    String JoinMedProtect = "";
                                    if (ExcelUtil.getCellValue(row.getCell(13)).equals("有")) {
                                        JoinMedProtect = "1";
                                    } else if (ExcelUtil.getCellValue(row.getCell(13)).equals("无")) {
                                        JoinMedProtect = "0";
                                    } else {
                                        errorMsg += "员工清单第" + (i + 1) + "行有无医保只可录入（有、无），请重新录入/";
                                    }
                                    fcPerTempInfo.setJoinMedProtect(JoinMedProtect);// 有无医保
                                    //服务年限
                                    String ServiceTerm = getStrToPlanString(ExcelUtil.getCellValue(row.getCell(14)));
                                    //是否退休
                                    String Retirement = ExcelUtil.getCellValue(row.getCell(15));
                                    if (StringUtils.isNotBlank(Retirement)) {
                                        if (Retirement.equals("是")) {
                                            Retirement = "0";
                                        } else if (Retirement.equals("否")) {
                                            Retirement = "1";
                                        } else {
                                            errorMsg += "员工清单第" + (i + 1) + "行是否退休只可录入（是、否），请重新录入/";
                                        }
                                    }
                                    if (fcBusinessProDutyGrpObjectList.size() > 0) {
                                        if ((StringUtils.isNotBlank(fcBusinessProDutyGrpObjectList.get(0).getComeAgeLowLimit())
                                                || StringUtils.isNotBlank(fcBusinessProDutyGrpObjectList.get(0).getComeAgeLowLimit()))
                                                && StringUtils.isBlank(ServiceTerm)) {
                                            errorMsg += "员工清单第" + (i + 1) + "行未录入服务年限/";
                                        }
                                        if (StringUtils.isNotBlank(fcBusinessProDutyGrpObjectList.get(0).getRetirement()) && StringUtils.isBlank(Retirement)) {
                                            errorMsg += "员工清单第" + (i + 1) + "行未录入是否退休/";
                                        }
                                    }
                                    if (StringUtils.isNotBlank(ServiceTerm)) {
                                        if (!ServiceTerm.matches("^\\d+$")) {
                                            errorMsg += "员工清单第" + (i + 1) + "行服务年限仅可录入大于等于0的整数，请重新录入/";
                                        }
                                    }
                                    fcPerTempInfo.setServiceTerm(ServiceTerm);
                                    fcPerTempInfo.setRetirement(Retirement);
                                    //校验员工职级
                                    String levelCode = ExcelUtil.getCellValue(row.getCell(10));
                                    if (levelList.contains(levelCode)) {
                                        Map<String, String> mapInfo = new HashMap<>();
                                        mapInfo.put("grpNo", grpNo);
                                        mapInfo.put("gradeLevelCode", levelCode);
                                        System.out.println("mapInfo为：" + mapInfo);
                                        FCBusPersonType fcBusPersonType = fcBusPersonTypeMapper.selectByPrimaryKey(mapInfo);
                                        System.out.println("fcBusPersonType" + fcBusPersonType);
                                        fcPerTempInfo.setLevelCode(fcBusPersonType.getGradeLevelCode());
                                    } else {
                                        errorMsg += "员工清单第" + (i + 1) + "行职级不存在，请重新录入/";
                                    }
                                    /**获取个人的默认档次的保费之和进行比较----未测试,部分错误未纠正，只是写个流程，需修改。    已测试---20190802*/
                                    Map<String, Object> map = new HashMap<>();
                                    map.put("EnsureCode", ensureCode);
                                    map.put("BirthDay", fcPerTempInfo.getBirthDay());
                                    map.put("CvaliDate", fcEnsure.getCvaliDate());
                                    map.put("InsureCount", fcEnsure.getInsuredNumber());
                                    map.put("Sex", fcPerTempInfo.getSex());
                                    map.put("JoinMedProtect", fcPerTempInfo.getJoinMedProtect());
                                    map.put("OccupationType", fcPerTempInfo.getOccupationType());
                                    map.put("LevelCod", fcPerTempInfo.getLevelCode());
                                    map.put("ServiceTerm", fcPerTempInfo.getServiceTerm());
                                    map.put("Retirement", fcPerTempInfo.getRetirement());
                                    log.info("是否退休" + fcPerTempInfo.getRetirement());
                                    map.put("Relation", "0");
                                    log.info(JSON.toJSONString(map));
                                    List<Map<String, Object>> list = premTrailService.generateRequest(map);
                                    Double totalPrem = 0.00;
                                    for (Map<String, Object> map2 : list) {
                                        Map<String, Object> resultmap = premTrailService.premTrail(map2);
                                        if (Boolean.valueOf(resultmap.get("success").toString())) {
                                            if ("17020".equals(map2.get("RiskCode").toString()) || "15060".equals(map2.get("RiskCode").toString()) || "15070".equals(map2.get("RiskCode").toString())) {
                                                Map<String, Object> premMap = (Map<String, Object>) resultmap.get("Prem");
                                                totalPrem = CommonUtil.add(totalPrem, Double.valueOf(premMap.get("Prem").toString()));
                                            } else {
                                                totalPrem = CommonUtil.add(totalPrem, Double.valueOf(resultmap.get("Prem").toString()));
                                            }
                                        }
                                    }

                                    //获取表格中的员工额度和家属额度
                                    String strGrpPrem = getStrToPlanString(ExcelUtil.getCellValue(row.getCell(16)));
                                    log.info("员工福利额度=" + strGrpPrem + "&&默认计划总保费=" + totalPrem);
                                    boolean str = strGrpPrem.contains(".");
                                    if (str) {
                                        errorMsg += "员工清单第" + (i + 1) + "行员工福利额度不能为小数/";
                                    }
                                    String famGrpPrem = getStrToPlanString(ExcelUtil.getCellValue(row.getCell(17)));
                                    boolean fam = famGrpPrem.contains(".");
                                    if (fam) {
                                        errorMsg += "员工清单第" + (i + 1) + "行家属福利额度不能为小数/";
                                    }
                                    if (list.size() > 0 && StringUtils.isBlank(strGrpPrem)) {
                                        errorMsg += "员工清单第" + (i + 1) + "行员工存在默认保额档次，员工福利额度不可为空/";
                                    }
                                    if (!"".equals(strGrpPrem)) {
                                        //员工福利额度
                                        Double staffGrpPrem = Double.parseDouble(strGrpPrem);
                                        if (staffGrpPrem < 0) {
                                            errorMsg += "员工清单第" + (i + 1) + "行员工福利额度应大于0/";
                                        }
                                        if (staffGrpPrem < totalPrem) {
                                            errorMsg += "员工清单第" + (i + 1) + "行员工福利额度要大于等于默认计划的保费，默认计划保费为" + totalPrem + "元/";
                                        }
                                        fcPerTempInfo.setStaffGrpPrem(staffGrpPrem);
                                    }
                                    if (!"".equals(famGrpPrem)) {
                                        //家属福利额度
                                        Double familyGrpPrem = Double.parseDouble(famGrpPrem);
                                        if (familyGrpPrem < 0) {
                                            errorMsg += "员工清单第" + (i + 1) + "行家属福利额度应大于0/";
                                        }
                                        fcPerTempInfo.setFamilyGrpPrem(familyGrpPrem);
                                    }


                                    fcPerTempInfo.setOperator(operator);// 操作员
                                    String perTempNo = maxNoService.createMaxNo("PerTempNo", "", 20);
                                    fcPerTempInfo.setPerTempNo(perTempNo);// 流水号
                                    fcPerTempInfo.setEnsureCode(ensureCode);// 福利编号
                                    fcPerTempInfo.setErrorMsg("");// 错误原因
                                    fcPerTempInfo.setImpotStatus("01");// 导入状态（01-导入成功；02-导入失败）
                                    fcPerTempInfo.setSubStaus("01");// 提交状态（01-未提交；02-已提交）
                                    fcPerTempInfo = CommonUtil.initObject(fcPerTempInfo, "INSERT");
                                    Map<String, String> perInfoMap = new HashMap<>();
                                    perInfoMap.put("name", fcPerTempInfo.getName());
                                    perInfoMap.put("iDType", fcPerTempInfo.getIDType());
                                    perInfoMap.put("iDNo", fcPerTempInfo.getIDNo());
                                    perInfoMap.put("perTempNo", perTempNo);
                                    perInList.add(perInfoMap);
                                    perInfoTemplist.add(fcPerTempInfo);
                                }
                            }
                        } else if (j == 1) {
                            log.info(sheet.getLastRowNum() + "");
                            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                                row = sheet.getRow(i);
                                FCPerinfoFamilyTemp fcPerinfoFamilyTemp = new FCPerinfoFamilyTemp();
                                if (ExcelUtil.isRowEmpty(row, 6)) {
                                    log.info("第" + i + "行数据无效");
                                } else {
                                    if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(4)))) {
                                        errorMsg += "家属清单第" + (i + 1) + "行,证件号填写格式有误，建议将格式调整为文本格式!/";
                                    }
                                    if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(15)))) {
                                        errorMsg += "家属清单第" + (i + 1) + "行,员工证件号填写格式有误，建议将格式调整为文本格式!/";
                                    }
                                    fcPerinfoFamilyTemp.setName(ExcelUtil.getCellValue(row.getCell(1)));
                                    //校验国籍
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(2)))) {
                                        String nativeplaceCode = nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(2)));
                                        if (StringUtils.isBlank(nativeplaceCode)) {
                                            errorMsg += "家属清单第" + (i + 1) + "行,国籍填写有误!/";
                                        }
                                    }
                                    fcPerinfoFamilyTemp.setNativeplace(nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(2))));
                                    String IDType = "";
                                    String PerIDType = ExcelUtil.getCellValue(row.getCell(14));
                                    //家属及员工证件类型码值转化
                                    for (HashMap idType : idTypeCodeList) {
                                        if (idType.get("CodeName").toString().equals(ExcelUtil.getCellValue(row.getCell(3)))) {
                                            IDType = idType.get("CodeKey").toString();
                                        }
                                        if (idType.get("CodeName").toString().equals(ExcelUtil.getCellValue(row.getCell(14)))) {
                                            PerIDType = idType.get("CodeKey").toString();
                                        }
                                    }
                                    String idno = ExcelUtil.getCellValue(row.getCell(4));
                                    if ("0".equals(IDType)) {
                                        if (!IDCardUtil.isIDCard(idno)) {
                                            errorMsg += "家属清单第" + (i + 1) + "行身份证号格式错误：" + idno + "！/";
                                        }
                                        DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
                                        Date date = format1.parse(ExcelUtil.getCellValue(row.getCell(8)));
                                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                                        String dateString = formatter.format(date);
                                        String idBir = IDCardUtil.dateOfBirth(idno);
                                        if (!dateString.equals(idBir)) {
                                            errorMsg += "家属清单第" + (i + 1) + "行出生日期与身份证不符/";
                                        }
                                        if (!getGenderByIdCard(idno).equals(ExcelUtil.getCellValue(row.getCell(7)))) {
                                            errorMsg += "家属清单第" + (i + 1) + "行性别与身份证不符/";
                                        }
                                    }
                                    if ("1".equals(IDType)) {
                                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(1)))) {
                                            String s = CheckUtils.checkForeignName(ExcelUtil.getCellValue(row.getCell(1)));
                                            if (!StringUtil.isEmpty(s)) {
                                                errorMsg += "员工清单第" + (i + 1) + "行,姓名" + s;
                                            }
                                        }
                                    } else {
                                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(1)))) {
                                            String s = CheckUtils.checkChineseName(ExcelUtil.getCellValue(row.getCell(1)));
                                            if (!StringUtil.isEmpty(s)) {
                                                errorMsg += "员工清单第" + (i + 1) + "行,姓名" + s;
                                            }
                                        }
                                    }
                                    fcPerinfoFamilyTemp.setIDType(IDType);
                                    fcPerinfoFamilyTemp.setPerIDType(PerIDType);
                                    fcPerinfoFamilyTemp.setIDNo(ExcelUtil.getCellValue(row.getCell(4)));
                                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(5)))) {
                                        fcPerinfoFamilyTemp.setIdTypeEndDate(ExcelUtil.getCellValue(row.getCell(5)));
                                    }
                                    fcPerinfoFamilyTemp.setPhone(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(6))));
                                    // 员工投保信息
                                    Map<String, String> personMap = personalMap.get(ExcelUtil.getCellValue(row.getCell(15)));
                                    if (StringUtils.isNotEmpty(fcPerinfoFamilyTemp.getPhone())) {
                                        if (!checkScientifiNotation(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(6)))) || !CheckUtils.checkMobilePhone(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(6))))) {
                                            errorMsg += "家属清单第" + (i + 1) + "行,手机号填写格式有误!/";
                                        }
                                    }
                                    // 判断家属手机号是否与投保人员工一致
                                    if (StringUtils.isNotEmpty(fcPerinfoFamilyTemp.getPhone()) && null != personMap && fcPerinfoFamilyTemp.getPhone().equals(personMap.get("phone"))) {
                                        errorMsg += "家属清单第" + (i + 1) + "行,手机号与投保员工手机号一致!/";
                                    }


                                    String Sex = ExcelUtil.getCellValue(row.getCell(7));
                                    //家属性别码值转化
                                    if (StringUtils.isNotBlank(Sex)) {
                                        if (!"男".equals(ExcelUtil.getCellValue(row.getCell(7))) && !"女".equals(ExcelUtil.getCellValue(row.getCell(7)))) {
                                            errorMsg += "家属清单第" + (i + 1) + "行,性別不符合录入规则（男|女）!/";
                                        }
                                    }
                                    for (HashMap sex : sexCodeList) {
                                        if (sex.get("CodeName").toString().equals(ExcelUtil.getCellValue(row.getCell(7)))) {
                                            Sex = sex.get("CodeKey").toString();
                                            break;
                                        }
                                    }

                                    fcPerinfoFamilyTemp.setSex(Sex);
                                    fcPerinfoFamilyTemp.setBirthDay(ExcelUtil.getCellValue(row.getCell(8)));
                                    String occType = getStrToPlanString(ExcelUtil.getCellValue(row.getCell(9)));
                                    String occCode = getStrToPlanString(ExcelUtil.getCellValue(row.getCell(10)));
                                    //职业类别校验
                                    if (StringUtils.isNotBlank(occType)) {
                                        if (!occupationTypeList.contains(occType)) {
                                            errorMsg += "家属清单第" + (i + 1) + "行职业类别录入错误，请重新录入！/";
                                        }
                                    }
                                    fcPerinfoFamilyTemp.setOccupationType(occType);

                                    //职业代码校验
                                    if (StringUtils.isNotBlank(occCode)) {
                                        if (!occupationCodeList.contains(occCode)) {
                                            errorMsg += "家属清单第" + (i + 1) + "行职业代码录入错误，请重新录入！/";
                                        }
                                    }
                                    fcPerinfoFamilyTemp.setOccupationCode(occCode);
                                    if (StringUtils.isNotBlank(occType) && StringUtils.isNotBlank(occCode)
                                            && occupationTypeList.contains(occType) && occupationCodeList.contains(occCode)) {
                                        List<String> OccupationCodeList = occupationTypeCodeMap.get(occType);
                                        HashSet hashSet = new HashSet(OccupationCodeList);
                                        if (!hashSet.contains(occCode)) {
                                            errorMsg += "家属清单第" + (i + 1) + "行职业类别不包含该职业代码，请重新选择！/";
                                        }
                                    }

                                    if ("无".equals(ExcelUtil.getCellValue(row.getCell(11)))) {
                                        fcPerinfoFamilyTemp.setJoinMedProtect("0");
                                    } else if ("有".equals(ExcelUtil.getCellValue(row.getCell(11)))) {
                                        fcPerinfoFamilyTemp.setJoinMedProtect("1");
                                    } else {
                                        errorMsg += "家属清单第" + (i + 1) + "行有无医保只可录入（有、无），请重新录入/";
                                    }
                                    String Realtion = ExcelUtil.getCellValue(row.getCell(12));
                                    boolean flag = false;
                                    for (HashMap relation : relationMap) {
                                        String relationName = relation.get("CodeName").toString();
                                        String relationKey = relation.get("CodeKey").toString();
                                        if (relationName.equals(Realtion) && !relationKey.equals("6")) {
                                            Realtion = relationKey;
                                            flag = true;
                                            break;
                                        }
                                    }
                                    if (!flag) {
                                        errorMsg += "家属清单第" + (i + 1) + "行与员工关系不存在，请重新选择！/";
                                    }
                                    fcPerinfoFamilyTemp.setRelation(Realtion);
                                    fcPerinfoFamilyTemp.setPerName(ExcelUtil.getCellValue(row.getCell(13)));
                                    fcPerinfoFamilyTemp.setPerIDNo(ExcelUtil.getCellValue(row.getCell(15)));
                                    String name = fcPerinfoFamilyTemp.getPerName();
                                    String idType = fcPerinfoFamilyTemp.getPerIDType();
                                    String idNo = fcPerinfoFamilyTemp.getPerIDNo();
                                    int sum = 0;
                                    Map<String, Object> param = new HashMap<>();
                                    param.put("grpNo", grpNo);
                                    param.put("ensureCode", ensureCode);
                                    List<Map<String, String>> fcPerInfoList = fcPerInfoMapper.selectByParamList(param);
                                    perInList.addAll(fcPerInfoList);
                                    if (perInList.size() == 0) {
                                        errorMsg += "该福利未录入相关员工信息，请录入！/";
                                    } else if (perInList.size() > 0) {
                                        for (int k = 0; k < perInList.size(); k++) {
                                            if (idType.equals(perInList.get(k).get("iDType")) && idNo.equalsIgnoreCase(perInList.get(k).get("iDNo")) && name.equals(perInList.get(k).get("name"))) {
                                                fcPerinfoFamilyTemp.setPerTempNo(perInList.get(k).get("perTempNo"));
                                                break;
                                            } else {
                                                sum++;
                                            }
                                            if (sum == perInList.size()) {
                                                errorMsg += "家属清单第" + (i + 1) + "行员工信息不存在,请重新录入！/";
                                            }
                                        }
                                    }
                                    // 根据证件号、福利编号判断临时表是否已存在
                                    int checkFamilyCount = fcPerinfoFamilyTempMapper.getFamilyCountByEnsureCode(fcPerinfoFamilyTemp);
                                    if (checkFamilyCount > 0) {
                                        errorMsg += "家属清单第" + (i + 1) + "行，当前福利下已存在所录员工以及家属相同的证件号，请重新录入!/";  //当前福利下员工已经存在与该家属的数据
                                    }

                                    if (fcPerinfoFamilyTemp.getIDNo().equals(idNo)) {
                                        errorMsg += "家属清单第" + (i + 1) + "行家属所录证件号不可与所录员工相同!/";
                                    }

                                    Map<String, String> checkMap = new HashMap<>();
                                    checkMap.put("sex", fcPerinfoFamilyTemp.getSex());
                                    checkMap.put("idNo", fcPerinfoFamilyTemp.getIDNo());
                                    checkMap.put("sign", "2");
                                    checkMap.put("idType", fcPerinfoFamilyTemp.getIDType());
                                    checkMap.put("birthDay", fcPerinfoFamilyTemp.getBirthDay());
                                    checkMap.put("nativeplace", fcPerinfoFamilyTemp.getNativeplace());
                                    checkMap.put("idTypeEndDate", fcPerinfoFamilyTemp.getIdTypeEndDate());
                                    checkMap.put("occupationCode", fcPerinfoFamilyTemp.getOccupationCode());
                                    checkMap.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
                                    /**
                                     * 证件类型和国籍
                                     */
                                    String resultMsg = CheckUtils.checkSinglePeople(checkMap);
                                    checkCustomer.add(EvaluationCustomer.builder()
                                            .name(fcPerinfoFamilyTemp.getName())
                                            .idType(CoreIdType.getNameByCoreId(fcPerinfoFamilyTemp.getIDType()).name())
                                            .idNo(fcPerinfoFamilyTemp.getIDNo())
                                            .gender(GenderType.getGenderByCoreId(fcPerinfoFamilyTemp.getSex()).name())
                                            .birthday(fcPerinfoFamilyTemp.getBirthDay())
                                            .nationality(fcPerinfoFamilyTemp.getNativeplace())
                                            .build());
                                    if (StringUtils.isNotBlank(resultMsg)) {
                                        errorMsg += "家属清单第" + (i + 1) + "行 " + fcPerinfoFamilyTemp.getName() + resultMsg + "/";
                                    }
                                    fcPerinfoFamilyTemp.setEnsureCode(ensureCode);
                                    fcPerinfoFamilyTemp.setSubStaus("01");
                                    fcPerinfoFamilyTemp.setOperator(operator);
                                    fcPerinfoFamilyTemp.setFamilyTempNo(maxNoService.createMaxNo("FamilyTempNo", "", 20));
                                    fcPerinfoFamilyTemp = CommonUtil.initObject(fcPerinfoFamilyTemp, "INSERT");
                                    familyMapList.add(fcPerinfoFamilyTemp);
                                    familyCount++;
                                }
                            }
                            if (familyMapList != null && familyMapList.size() > 0) {

                                // 判断家属清单中家属证件号相同其他四要素是否一致
                                int k = 3;
                                List<String> idList = new ArrayList<>();
                                for (FCPerinfoFamilyTemp family : familyMapList) {
                                    if (!idList.contains(family.getIDNo())) {
                                        int i = 3;
                                        String serialNo = "";
                                        for (FCPerinfoFamilyTemp familys : familyMapList) {
                                            if (familys.getIDNo().equals(family.getIDNo())) {
                                                if (!familys.getIDType().equals(family.getIDType()) || !familys.getName().equals(family.getName()) || !familys.getSex().equals(family.getSex()) || !familys.getBirthDay().equals(family.getBirthDay())) {
                                                    serialNo += i + ",";
                                                }
                                            }
                                            i++;
                                        }
                                        if (StringUtils.isNotBlank(serialNo)) {
                                            errorMsg += "家属清单第" + serialNo.substring(0, serialNo.length() - 1) + "行与清单第" + k + "行家属证件号：" + (family.getIDNo()) + "重复,但姓名、性别、出生日期以及证件类型存在不同，请统一所录监护人信息！/";
                                        }
                                    }
                                    k++;
                                    idList.add(family.getIDNo());
                                }

                                //判断家属证件号相同时，员工证件号是否相同
                                for (int i = 0; i < familyMapList.size(); i++) {
                                    for (int l = i; l < familyMapList.size(); l++) {
                                        if (familyMapList.get(i).getIDNo().equals(familyMapList.get(l).getIDNo())) {
                                            if (i != l) {
                                                if (familyMapList.get(i).getPerIDNo().equals(familyMapList.get(l).getPerIDNo())) {
                                                    errorMsg += "家属清单第" + (i + 3) + "行与第" + (l + 3) + "行家属证件号相同，请重新录入!/";
                                                }
                                            }
                                        }
                                    }
                                }

                                for (int i = 0; i < familyMapList.size(); i++) {
                                    if (!checkStuIdNoIsExists(familyMapList.get(i).getIDNo())) {   //校验正式表 当前企业下是否存在同一证件号  存在 进行五要素比对 fcperson
                                        Map<String, String> checkMap = new HashMap<>();
                                        checkMap.put("idNo", familyMapList.get(i).getIDNo());
                                        checkMap.put("idType", familyMapList.get(i).getIDType());
                                        checkMap.put("name", familyMapList.get(i).getName());
                                        checkMap.put("sex", familyMapList.get(i).getSex());
                                        checkMap.put("birthday", familyMapList.get(i).getBirthDay());
                                        if (!checkStuIdNoIsExistsTwo(checkMap)) {        //五要素不同
                                            errorMsg += "家属清单第" + (i + 3) + "行家属证件号，当前系统存在相同的证件号，但是姓名、性别、出生日期、证件类型不同，请检查/";

                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } else if ("1".equals(fcEnsure.getEnsureType())) {          //学生解析
                Sheet sheet = wb.getSheetAt(0);
                Row row = null;
                List<HashMap<String, String>> studentPerIfo = new ArrayList<>();
                List<HashMap<String, String>> garPerInfo = new ArrayList<>();
                List<String> listIdNo = new ArrayList<>();
                for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                    row = sheet.getRow(i);
                    FCPerinfoFamilyTemp fcPerinfoFamilyTemp = new FCPerinfoFamilyTemp();
                    if (ExcelUtil.isRowEmpty(row, 7)) {
                        log.info("学生清单第" + (i + 1) + "行是无效数据。");
                    } else {
                        count++;
                        errorMsg += checkStudentInfo(i + 1, row);
                        //校验国籍
                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(2)))) {
                            String nativeplacecode = nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(2)));
                            if (StringUtils.isBlank(nativeplacecode)) {
                                errorMsg += "学生清单第" + (i + 1) + "行,学生国籍填写有误!/";
                            } else {
                                fcPerinfoFamilyTemp.setNativeplace(nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(2))));
                            }
                        }
                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(15)))) {
                            String nativeplacecode = nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(15)));
                            if (StringUtils.isBlank(nativeplacecode)) {
                                errorMsg += "学生清单第" + (i + 1) + "行,监护人国籍填写有误!/";
                            }
                        }
                        HashMap<String, String> studentHashMap = new HashMap<>();
                        HashMap<String, String> garHashMap = new HashMap<>();
                        studentHashMap.put("name", ExcelUtil.getCellValue(row.getCell(1)));
                        garHashMap.put("name", ExcelUtil.getCellValue(row.getCell(14)));
                        fcPerinfoFamilyTemp.setName(ExcelUtil.getCellValue(row.getCell(1)));
                        fcPerinfoFamilyTemp.setPerName(ExcelUtil.getCellValue(row.getCell(14)));
                        if (!checkScientifiNotation(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(21))))
                                || !CheckUtils.checkMobilePhone(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(21))))) {
                            errorMsg += "学生清单第" + (i + 1) + "行,监护人手机号填写格式有误!/";
                        }
                        if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(4)))) {
                            errorMsg += "学生清单第" + (i + 1) + "行,学生证件号填写格式有误，建议将格式调整为文本格式!/";
                        }
                        fcPerinfoFamilyTemp.setIDNo(ExcelUtil.getCellValue(row.getCell(4)));
                        studentHashMap.put("idno", ExcelUtil.getCellValue(row.getCell(4)));
                        garHashMap.put("idno", ExcelUtil.getCellValue(row.getCell(17)));
                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(5)))) {
                            fcPerinfoFamilyTemp.setIdTypeEndDate(ExcelUtil.getCellValue(row.getCell(5)));
                        }
                        if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(17)))) {
                            errorMsg += "学生清单第" + (i + 1) + "行,监护人证件号填写格式有误，建议将格式调整为文本格式!/";
                        }
                        fcPerinfoFamilyTemp.setPerIDNo(ExcelUtil.getCellValue(row.getCell(17)));
                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(23)))) {
                            if (!checkScientifiNotation(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(23))))) {
                                errorMsg += "第" + (i + 1) + "行,开户账号填写格式有误!/";
                            }
                            if (getStrToPlanString(ExcelUtil.getCellValue(row.getCell(23))).length() < 12 || getStrToPlanString(ExcelUtil.getCellValue(row.getCell(23))).length() > 30) {
                                errorMsg += "第" + (i + 1) + "行,开户账号填写格式有误!/";
                            }
                            String checkBankAccount = CheckUtils.checkBankAccount(ExcelUtil.getCellValue(row.getCell(16)));
                            if (StringUtils.isNotEmpty(checkBankAccount)) {
                                errorMsg += "第" + (i + 1) + checkBankAccount;
                            }
                        }
                        //性别的公式值转换
                        String studentSex = ExcelUtil.getCellFormula(row.getCell(6));
                        String guardianSex = ExcelUtil.getCellFormula(row.getCell(19));
                        if (StringUtils.isNotBlank(ExcelUtil.getCellFormula(row.getCell(6)))) {
                            if (!"男".equals(studentSex) && !"女".equals(studentSex)) {
                                errorMsg += "学生清单第" + (i + 1) + "行,学生性別不符合录入规则（男|女）!/";
                            }
                            if (!"男".equals(guardianSex) && !"女".equals(guardianSex)) {
                                errorMsg += "学生清单第" + (i + 1) + "行,监护人性別不符合录入规则（男|女）!/";
                            }
                        }
                        //性别码值转换
                        for (HashMap<String, Object> hashMap2 : sexCodeList) {
                            if (studentSex.equals(hashMap2.get("CodeName").toString())) {
                                studentSex = hashMap2.get("CodeKey").toString();
                            }
                            if (guardianSex.equals(hashMap2.get("CodeName").toString())) {
                                guardianSex = hashMap2.get("CodeKey").toString();
                            }
                        }
                        studentHashMap.put("sex", studentSex);
                        garHashMap.put("sex", guardianSex);
                        fcPerinfoFamilyTemp.setSex(studentSex);
                        //出生日期的公式值转换
                        studentHashMap.put("birthday", ExcelUtil.getCellFormula(row.getCell(7)));
                        garHashMap.put("birthday", ExcelUtil.getCellValue(row.getCell(20)));
                        fcPerinfoFamilyTemp.setBirthDay(ExcelUtil.getCellFormula(row.getCell(7)));
                        // 批量查询 默认计划编码 是否存在

                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(8)))) {
                            Map<String, String> palnMap = new HashMap<>();
                            palnMap.put("ensureCode", ensureCode);
                            palnMap.put("planObject", "3");
                            List<String> defaultPlanCodeCollection = fcEnsurePlanMapper.existListdefultPlanCode(palnMap);

                            String seque = ExcelUtil.getCellValue(row.getCell(8));
                            // 判断序号是否为数字
                            if (!CommonUtil.isNumeric(seque)) {
                                errorMsg += "学生清单第" + (i + 1) + "行的数据，默认计划编码不存在，请详见Excel中提供的默认编码！/";
                            } else {
                                Double mainWastage = Double.parseDouble(seque);
                                if (mainWastage.intValue() - mainWastage != 0) {// 判断是否符合取整条件
                                    errorMsg += "学生清单第" + (i + 1) + "行的数据，默认计划编码不存在，请详见Excel中提供的默认编码！/";
                                } else {
                                    if (!defaultPlanCodeCollection.contains(mainWastage.intValue() + "")) {
                                        errorMsg += "学生清单第" + (i + 1) + "行的数据，默认计划编码不存在，请详见Excel中提供的默认编码！/";
                                    }
                                }
                            }
                        }
                        //证件类型码值转换
                        String studentIdType = ExcelUtil.getCellValue(row.getCell(3));
                        String guardianIdType = ExcelUtil.getCellValue(row.getCell(16));
                        if (StringUtils.isNotBlank(studentIdType)) {
                            if (!idTypeList.contains(studentIdType)) {
                                errorMsg += "学生清单第" + i + "行学生证件类型录入错误/";
                            }
                        }
                        if (studentIdType != null && !studentIdType.equals("")) {
                            String idType = "";
                            for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
                                if (studentIdType.equals(hashMap1.get("CodeName").toString())) {
                                    idType = hashMap1.get("CodeKey").toString();
                                    break;
                                }
                            }
                            fcPerinfoFamilyTemp.setIDType(idType);
                            studentHashMap.put("idtype", idType);
                        } else {
                            studentHashMap.put("idtype", "");
                        }

                        if (StringUtils.isNotBlank(guardianIdType)) {
                            if (!idTypeList.contains(guardianIdType)) {
                                errorMsg += "学生清单第" + i + "行监护人证件类型录入错误/";
                            }
                        }
                        if (guardianIdType != null && !guardianIdType.equals("")) {
                            String idType = "";
                            for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
                                if (guardianIdType.equals(hashMap1.get("CodeName").toString())) {
                                    idType = hashMap1.get("CodeKey").toString();
                                    break;
                                }
                            }
                            fcPerinfoFamilyTemp.setPerIDType(idType);
                            garHashMap.put("idtype", idType);
                        }

                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(9))) && StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(10)))) {
                            String occupationType = getStrToPlanString(ExcelUtil.getCellValue(row.getCell(9)));
                            String occupationCode = getStrToPlanString(ExcelUtil.getCellValue(row.getCell(10)));
                            if (!occupationTypeList.contains(occupationType)) {
                                errorMsg += "学生清单第" + (i + 1) + "行职业类别录入错误/";
                            }
                            if (!occupationCodeList.contains(occupationCode)) {
                                errorMsg += "学生清单第" + (i + 1) + "行职业代码录入错误/";
                            }
                            fcPerinfoFamilyTemp.setOccupationType(occupationType);
                            fcPerinfoFamilyTemp.setOccupationCode(occupationCode);
                        }
                        String openBank = ExcelUtil.getCellValue(row.getCell(22));
                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(23)))) {
                            if (!openBankList.contains(openBank)) {
                                errorMsg += "学生清单第" + (i + 1) + "行开户行录入错误/";
                            }
                        }
                        // 校验当前职业代码是否符合当前职业类别
                        if (StringUtils.isNotBlank(fcPerinfoFamilyTemp.getOccupationCode()) && StringUtils.isNotBlank(fcPerinfoFamilyTemp.getOccupationType())
                                && occupationTypeList.contains(fcPerinfoFamilyTemp.getOccupationType()) && occupationCodeList.contains(fcPerinfoFamilyTemp.getOccupationCode())) {
                            if (!occupationTypeCodeMap.get(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(9)))).contains(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(10))))) {
                                errorMsg += "学生清单第" + (i + 1) + "行职业类别不包含所录职业/";
                            }
                        }

                        // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确
                        if (ExcelUtil.getCellValue(row.getCell(3)).equals("身份证")) {
                            Date date = null;
                            DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
                            String idno = ExcelUtil.getCellValue(row.getCell(4));
                            String sex = ExcelUtil.getCellFormula(row.getCell(6));
                            String birthday = ExcelUtil.getCellFormula(row.getCell(7));
                            if (!IDCardUtil.isIDCard(idno)) {
                                errorMsg += "第" + (i + 1) + "行学生身份证号格式错误：" + idno + "/";
                            }
                            date = format1.parse(birthday);
                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                            String dateString = formatter.format(date);
                            String idBir = IDCardUtil.dateOfBirth(idno);
                            if (!dateString.equals(idBir)) {
                                errorMsg += "第" + (i + 1) + "行学生出生日期与身份证不符/";
                            }
                            if (!getGenderByIdCard(idno).equals(sex)) {
                                errorMsg += "第" + (i + 1) + "行学生性别与身份证不符/";
                            }
                        }

                        if (!studentHashMap.get("idtype").equals("1")) {
                            if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(1)))) {
                                String s = CheckUtils.checkChineseName(ExcelUtil.getCellValue(row.getCell(1)));
                                if (!StringUtil.isEmpty(s)) {
                                    errorMsg += "学生清单第" + (i + 1) + "行,学生姓名/" + s;
                                }
                            }
                        }

                        if (("0").equals(fcPerinfoFamilyTemp.getPerIDType())) {
                            FCPerInfoTemp fcPerInfoTemp = new FCPerInfoTemp();
                            fcPerInfoTemp.setIDNo(fcPerinfoFamilyTemp.getPerIDNo());
                            fcPerInfoTemp.setBirthDay(ExcelUtil.getCellFormula(row.getCell(20)));
                            fcPerInfoTemp.setSex(guardianSex);
                            String errMsg = checkOneIDCard(fcPerInfoTemp);
                            if (StringUtils.isNotBlank(errMsg)) {
                                errorMsg += "学生清单第" + (i + 1) + "行,监护人" + errMsg + "!/";
                            }
                        }

                        if (!("1").equals(fcPerinfoFamilyTemp.getPerIDType())) {
                            if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(14)))) {
                                String s = CheckUtils.checkChineseName(ExcelUtil.getCellValue(row.getCell(14)));
                                if (!StringUtil.isEmpty(s)) {
                                    errorMsg += "学生清单第" + (i + 1) + "行,监护人姓名" + s + "/";
                                }
                            }
                        }

                        Map<String, String> checkMap = new HashMap<>();
                        checkMap.put("sex", fcPerinfoFamilyTemp.getSex());
                        checkMap.put("idNo", fcPerinfoFamilyTemp.getIDNo());
                        checkMap.put("sign", "2");
                        checkMap.put("idType", fcPerinfoFamilyTemp.getIDType());
                        checkMap.put("birthDay", fcPerinfoFamilyTemp.getBirthDay());
                        checkMap.put("nativeplace", fcPerinfoFamilyTemp.getNativeplace());
                        checkMap.put("idTypeEndDate", fcPerinfoFamilyTemp.getIdTypeEndDate());
                        checkMap.put("occupationCode", fcPerinfoFamilyTemp.getOccupationCode());
                        checkMap.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
                        /**
                         * 证件类型和国籍
                         */
                        String resultMsg = CheckUtils.checkSinglePeople(checkMap);
                        if (StringUtils.isNotBlank(resultMsg)) {
                            errorMsg += "学生清单第" + (i + 1) + "行" + resultMsg + "/";
                        }
                        if ("无".equals(ExcelUtil.getCellValue(row.getCell(11)))) {
                            fcPerinfoFamilyTemp.setJoinMedProtect("0");
                        } else if ("有".equals(ExcelUtil.getCellValue(row.getCell(11)))) {
                            fcPerinfoFamilyTemp.setJoinMedProtect("1");
                        } else {
                            errorMsg += "学生清单第" + (i + 1) + "行有无医保只可录入（有、无），请重新录入/";
                        }
                        String Realtion = ExcelUtil.getCellValue(row.getCell(13));
                        for (HashMap relation : relationMap) {
                            if (relation.get("CodeName").equals(Realtion)) {
                                Realtion = relation.get("CodeKey").toString();
                                break;
                            }
                        }
                        studentHashMap.put("ensureCode", ensureCode);
                        garHashMap.put("ensureCode", ensureCode);
                        studentHashMap.put("serialNo", (i + 1) + "");
                        garHashMap.put("serialNo", (i + 1) + "");
                        studentPerIfo.add(studentHashMap);
                        garPerInfo.add(garHashMap);
                        fcPerinfoFamilyTemp.setPhone(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(21))));
                        fcPerinfoFamilyTemp.setRelation(Realtion);
                        fcPerinfoFamilyTemp.setEnsureCode(ensureCode);
                        fcPerinfoFamilyTemp.setSubStaus("01");
                        fcPerinfoFamilyTemp.setOperator(operator);
                        fcPerinfoFamilyTemp.setFamilyTempNo(maxNoService.createMaxNo("FamilyTempNo", "", 20));
                        fcPerinfoFamilyTemp = CommonUtil.initObject(fcPerinfoFamilyTemp, "INSERT");
                        listIdNo.add(ExcelUtil.getCellValue(row.getCell(4)));
                        familyMapList.add(fcPerinfoFamilyTemp);
                    }
                }

                // 判断模板中是否存在相同的证件号
                HashSet<String> set = new HashSet<>(listIdNo);
                if (listIdNo.size() != set.size()) {
                    // 获得list与set的差集
                    Collection rs = CollectionUtils.disjunction(listIdNo, set);
                    // 将collection转换为list
                    List<String> list1 = new ArrayList<>(rs);
                    for (String str : list1) {
                        String serialNo = "";
                        for (HashMap<String, String> hashMap : studentPerIfo) {
                            if (hashMap.get("idno").equals(str)) {
                                serialNo += hashMap.get("serialNo") + ",";
                            }
                        }
                        errorMsg += "学生清单第" + serialNo.substring(0, serialNo.length() - 1) + "行学生证件号重复/";
                    }
                }

                //校验监护人信息
                if (garPerInfo != null && garPerInfo.size() > 0) {
                    // 判断模版中相同证件号其他四要素是否一致
                    List<String> idList = new ArrayList<>();
                    for (Map<String, String> garMap : garPerInfo) {
                        if (!idList.contains(garMap.get("idno"))) {
                            String serialNo = "";
                            for (Map<String, String> garMaps : garPerInfo) {
                                if (garMap.get("idno").equals(garMaps.get("idno"))) {
                                    if (!garMap.get("idtype").equals(garMaps.get("idtype")) || !garMap.get("name").equals(garMaps.get("name")) || !garMap.get("sex").equals(garMaps.get("sex")) || !garMap.get("birthday").equals(garMaps.get("birthday"))) {
                                        serialNo += garMaps.get("serialNo") + ",";
                                    }
                                }
                            }
                            if (StringUtils.isNotBlank(serialNo)) {
                                errorMsg += "学生清单第" + serialNo.substring(0, serialNo.length() - 1) + "行与清单第" + garMap.get("serialNo") + "行监护人证件号：" + (garMap.get("idno")) + "重复,但姓名、性别、出生日期以及证件类型存在不同，请统一所录监护人信息！/";
                            }
                            idList.add(garMap.get("idno"));
                        }
                    }

                    if (!checkIdNoIsExistsTemp(grpNo, garPerInfo)) {// 存在相同的证件号
                        // 判断是否存在证件号存在，但是其他四要素不同的数据
                        List<FCPerInfoTemp> checkOtherIsEsists = checkOtherIsEsistsTemp(grpNo, garPerInfo);
                        if (checkOtherIsEsists.size() > 0) {// 存在证件号相同，其他四要素不同的数据
                            for (FCPerInfoTemp fcPerInfoTemp : checkOtherIsEsists) {
                                for (HashMap<String, String> hashMap : garPerInfo) {
                                    if (hashMap.get("idno").equals(
                                            fcPerInfoTemp.getIDNo())) {
                                        errorMsg += "学生清单第" + Integer.parseInt(hashMap.get("serialNo")) + "行,当前企业定制中的福利存在相同的监护人证件号，但是姓名、性别、出生日期、证件类型不同，请核对后再次进行录入/";
                                    }
                                }
                            }
                        }
                    }

                    if (!checkIdNoIsExists(garPerInfo)) {// 存在相同的证件号
                        // 判断是否存在证件号存在，但是其他四要素不同的数据
                        List<FCPerInfoTemp> checkOtherIsEsists = checkOtherIsEsists(garPerInfo);
                        if (checkOtherIsEsists.size() > 0) {// 存在证件号相同，其他四要素不同的数据
                            for (FCPerInfoTemp fcPerInfoTemp : checkOtherIsEsists) {
                                for (HashMap<String, String> hashMap : garPerInfo) {
                                    if (hashMap.get("idno").equals(
                                            fcPerInfoTemp.getIDNo())) {
                                        errorMsg += "学生清单第" + Integer.parseInt(hashMap.get("serialNo")) + "行监护人证件号,当前系统存在相同证件号，但是姓名、性别、出生日期、证件类型不同的数据/";
                                    }
                                }
                            }
                        }
                    }
                }

                //校验学生信息
                if (studentPerIfo != null && studentPerIfo.size() > 0) {
                    // 根据证件号、福利编号判断临时表是否已存在  fcperinfofamilytemp
                    for (int i = 0; i < studentPerIfo.size(); i++) {
                        FCPerinfoFamilyTemp fcPerinfoFamilyTemp = new FCPerinfoFamilyTemp();
                        fcPerinfoFamilyTemp.setEnsureCode(studentPerIfo.get(i).get("ensureCode"));
                        fcPerinfoFamilyTemp.setIDNo(studentPerIfo.get(i).get("idno"));
                        if (!checkStuIdNoIsExistsTemp(fcPerinfoFamilyTemp)) {
                            errorMsg += "学生清单" + Integer.parseInt(studentPerIfo.get(i).get("serialNo")) + "行当前福利下已存在相同的学生证件号，请检查/";
                        }
                    }

                    for (int i = 0; i < studentPerIfo.size(); i++) {
                        if (!checkStuIdNoIsExists(studentPerIfo.get(i).get("idno"))) {   //校验正式表 当前企业下是否存在同一证件号  存在 进行五要素比对 fcperson
                            Map<String, String> checkMap = new HashMap<>();
                            checkMap.put("idNo", studentPerIfo.get(i).get("idno"));
                            checkMap.put("idType", studentPerIfo.get(i).get("idtype"));
                            checkMap.put("name", studentPerIfo.get(i).get("name"));
                            checkMap.put("sex", studentPerIfo.get(i).get("sex"));
                            checkMap.put("birthday", studentPerIfo.get(i).get("birthday"));
                            if (!checkStuIdNoIsExistsTwo(checkMap)) {        //五要素不同
                                errorMsg += "学生清单第" + (Integer.parseInt(studentPerIfo.get(i).get("serialNo"))) + "行学生证件号，当前系统存在相同证件号，但是姓名、性别、出生日期、证件类型不同，请检查/";
                            }
                        }
                    }
                }


                for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                    row = sheet.getRow(i);
                    if (ExcelUtil.isRowEmpty(row, 7)) {
                        log.info("学生清单第" + (i - 1) + "行是无效数据。");
                    } else {
                        // 员工信息临时表
                        FCPerInfoTemp fcPerTempInfo = new FCPerInfoTemp();
                        fcPerTempInfo.setGrpNo(grpNo);// 企业号
                        fcPerTempInfo.setName(ExcelUtil.getCellValue(row.getCell(14)));// 姓名
                        //国籍
                        String nativeplaceCode = nativeplaceMap.get(ExcelUtil.getCellValue(row.getCell(15)));
                        if (StringUtils.isNotBlank(nativeplaceCode)) {
                            fcPerTempInfo.setNativeplace(nativeplaceCode);
                        }
                        //性别码值转换
                        String sex = "";
                        int index = 0;
                        for (HashMap<String, Object> hashMap : sexCodeList) {
                            if (ExcelUtil.getCellFormula(row.getCell(19)).equals(hashMap.get("CodeName").toString())) {
                                sex = hashMap.get("CodeKey").toString();
                                break;
                            }
                            index++;
                            if (index == sexCodeList.size()) {
                                errorMsg += "学生清单第" + (i + 1) + "行监护人性别录入错误/";
                            }
                        }
                        fcPerTempInfo.setSex(sex);
                        fcPerTempInfo.setBirthDay(ExcelUtil.getCellFormula(row.getCell(20)));// 出生日期--公式值
                        fcPerTempInfo.setMobilePhone(getStrToPlanString(ExcelUtil.getCellValue(row.getCell(21))));// 手机号
                        //证件类型码值转换
                        String gardianIdType = "";
                        for (HashMap<String, Object> hashMap : idTypeCodeList) {
                            if (ExcelUtil.getCellValue(row.getCell(16)).equals(hashMap.get("CodeName").toString())) {
                                gardianIdType = hashMap.get("CodeKey").toString();
                                break;
                            }
                        }
                        fcPerTempInfo.setIDType(gardianIdType);// 证件类型
                        fcPerTempInfo.setIDNo(ExcelUtil.getCellValue(row.getCell(17)).toUpperCase());// 证件号
                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(18)))) {
                            fcPerTempInfo.setIdTypeEndDate(ExcelUtil.getCellValue(row.getCell(18)));//证件有效期
                        }
                        fcPerTempInfo.setOpenBank(ExcelUtil.getCellValue(row.getCell(22)));// 开户行
                        fcPerTempInfo.setOpenAccount(ExcelUtil.getCellValue(row.getCell(23)));// 开户账号
                        String planCode = "";
                        FCEnsurePlan fcEnsurePlan = new FCEnsurePlan();
                        String sPlanCode = "";
                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(8)))) {
                            String cellValue = ExcelUtil.getCellValue(row.getCell(8));
                            // 判断序号是否为数字
                            if (!CommonUtil.isNumeric(cellValue)) {
                                fcPerTempInfo.setDefaultPlan(sPlanCode);
                            } else {
                                int value = new Double(Double.valueOf(cellValue)).intValue();
                                sPlanCode = String.valueOf(value);
                                fcPerTempInfo.setDefaultPlan(sPlanCode);
                            }
                            Map<String, String> param = new HashMap<String, String>();
                            param.put("ensureCode", ensureCode);
                            param.put("planCode", sPlanCode);
                            fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                        }
                        //校验员工福利额度
                        Double totalPrem = 0.00;
                        if (fcEnsurePlan != null && fcEnsurePlan.getTotalPrem() != null) {
                            totalPrem = fcEnsurePlan.getTotalPrem();
                        }
                        //获取表格中的学生福利额度
                        String stuGrpPrem = getStrToPlanString(ExcelUtil.getCellValue(row.getCell(12)));
                        boolean str = stuGrpPrem.contains(".");
                        if (str) {
                            errorMsg += "学生清单第" + (i + 1) + "行学生福利额度不能为小数/";
                        }
                        if (!"".equals(stuGrpPrem)) {
                            //员工福利额度
                            Double stuPrem = Double.parseDouble(stuGrpPrem);
                            if (stuPrem < 0) {
                                errorMsg += "学生清单第" + (i + 1) + "行学生福利额度应大于0/";
                            }
                            if (stuPrem < totalPrem) {
                                errorMsg += "学生清单第" + (i + 1) + "行学生福利额度要大于等于默认计划的保费/";
                            }
                            fcPerTempInfo.setStudentGrpPrem(stuPrem);
                        }
                        fcPerTempInfo.setOperator(operator);// 操作员
                        String perTempNo = maxNoService.createMaxNo("PerTempNo", "", 20);
                        fcPerTempInfo.setPerTempNo(perTempNo);// 流水号
                        fcPerTempInfo.setEnsureCode(ensureCode);// 福利编号
                        fcPerTempInfo.setErrorMsg("");// 错误原因
                        fcPerTempInfo.setImpotStatus("01");// 导入状态（01-导入成功；02-导入失败）
                        fcPerTempInfo.setSubStaus("01");// 提交状态（01-未提交；02-已提交）
                        fcPerTempInfo = CommonUtil.initObject(fcPerTempInfo, "INSERT");
                        familyMapList.get(i - 2).setPerTempNo(perTempNo); //监护人临时编号存于学生临时表
                        perInfoTemplist.add(fcPerTempInfo);
                    }
                }

            }
        } catch (Exception e) {
            log.info("", e);
            log.info("人员清单导入失败：" + errorMsg + errorInfo);
            if (!errorMsg.equals("")) {
                Splitter split = Splitter.on('/').trimResults().omitEmptyStrings(); // 去前后空格&&去空string
                errorMsgList = new ArrayList<>(split.splitToList(errorMsg));
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", errorMsgList);
                return resultMap;
            } else {
                throw new RuntimeException();
            }
        }

        // 手机号重复校验
        errorMsg = checkPhone(perInfoTemplist, familyMapList, errorMsg);
        try {
            if (!"".equals(errorMsg)) {
                Splitter split = Splitter.on('/').trimResults().omitEmptyStrings(); // 去前后空格&&去空string
                errorMsgList = new ArrayList<>(split.splitToList(errorMsg));
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", errorMsgList);
                return resultMap;
            }
            //校验黑名单
            int start = 0;
            /**
             * 有福利号
             */
            while (start < checkCustomer.size()) {
                int end = Math.min(start + 10, checkCustomer.size());
                List<EvaluationCustomer> list = checkCustomer.subList(start, end);
                List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(list, IdCardVerifyRequest.Verify.class);
                //校验二要素
                String failVerifies = addressCheckService.checkIdCard(verifies);
                if (StringUtils.isNotEmpty(failVerifies)) {
                    errorMsgList.add(failVerifies);
                }
                if (CollectionUtils.isNotEmpty(errorMsgList)) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", errorMsgList);
                    return resultMap;
                }
                start += 10;
            }

            //新增重复手机号校验
            String errorMess = "";
            String errorMesss = "";
            List<String> errorList = new ArrayList<>();
            for (int i = 0; i < perInfoTemplist.size(); i++) {
                List<FdUser> phonecount = fdUserMapper.selectByPhone(perInfoTemplist.get(i).getMobilePhone(), "1", perInfoTemplist.get(i).getIDNo());

                if (phonecount != null && phonecount.size() > 0) {
                    errorMesss += perInfoTemplist.get(i).getMobilePhone() + ",";
                }
                int ck = 0;
                for (int j = 0; j < perInfoTemplist.size(); j++) {
                    if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                        if (perInfoTemplist.get(i).getMobilePhone().equals(perInfoTemplist.get(j).getMobilePhone())) {
                            ck++;
                        }
                    } else if ("1".equals(fcEnsure.getEnsureType())) {
                        if (perInfoTemplist.get(i).getMobilePhone().equals(perInfoTemplist.get(j).getMobilePhone()) && !perInfoTemplist.get(i).getIDNo().equals(perInfoTemplist.get(j).getIDNo())) {
                            ck++;
                        }
                    }
                }
                if (ck > 1 && !errorList.contains(perInfoTemplist.get(i).getMobilePhone())) {
                    errorList.add(perInfoTemplist.get(i).getMobilePhone());
                    errorMess += perInfoTemplist.get(i).getMobilePhone() + ",";
                }
                int pcou = fcPerInfoTempMapper.selectByPhone(perInfoTemplist.get(i).getMobilePhone(), "",
                        perInfoTemplist.get(i).getIDNo(), perInfoTemplist.get(i).getIDType());
                if (ck > 1 && !errorList.contains(perInfoTemplist.get(i).getMobilePhone()) && pcou > 0) {
                    errorList.add(perInfoTemplist.get(i).getMobilePhone());
                    errorMess += perInfoTemplist.get(i).getMobilePhone() + ",";
                }
            }
            Splitter split = Splitter.on('/').trimResults().omitEmptyStrings(); // 去前后空格&&去空string
            if (!errorMess.equals("")) {
                String reMess = "导入失败，清单中有以下重复手机号," + errorMess + "请核对后重新导入/";
                errorMsgList = new ArrayList<>(split.splitToList(reMess));
                return ResultUtil.error(errorMsgList, "500");
            }
            if (!errorMesss.equals("")) {
                String reMess = "导入失败，以下手机号已被注册," + errorMesss + "请核对后重新导入/";
                errorMsgList = new ArrayList<>(split.splitToList(reMess));
                return ResultUtil.error(errorMsgList, "500");
            }

            // 重复客户校验
            //TODOs
            if (ConstantUtil.EnsureState_1.equals(fcEnsure.getEnsureState())) {
                List<String> fcOrderList = fcOrderMapper.selectByEnsureCode(ensureCode);
                if (fcOrderList.contains("02")) {
                    errorMsgList.add("该福利下存在已提交至核心订单，不能导入");
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", errorMsgList);
                } else {

                    if (perInfoTemplist.size() > 0) {
                        fcPerInfoTempMapper.insert(perInfoTemplist);
                    }
                    if (familyMapList.size() > 0) {
                        fcPerinfoFamilyTempMapper.insertList(familyMapList);
                    }
                    // 同步人员信息
                    if (count > 0 || familyCount > 0) {
                        fcEnsure.setOperator(globalInput.getUserNo());
                        fcEnsure = (FCEnsure) CommonUtil.initObject(fcEnsure, "UPDATE");
                        fcEnsureMapper.updateByPrimaryKey(fcEnsure);
                        log.info("福利状态更新完成。。。");
                    }
                    if (StringUtils.isNotBlank(newFlag) && "1".equals(newFlag)) {
                        Map<String, String> map = new HashMap<>();
                        map.put("grpNo", grpNo);
                        map.put("ensureCode", ensureCode);
                        asyncService.insertNewUser(globalInput, map);
                    }
                    log.info("人员清单导入成功");
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                        resultMap.put("message", "人员清单导入成功,本次成功导入" + count + "条员工数据，" + familyCount + "条家属数据。");
                    } else {
                        resultMap.put("message", "人员清单导入成功,本次成功导入" + count + "条数据");
                    }

                }
            } else {
                if (perInfoTemplist.size() > 0) {
                    fcPerInfoTempMapper.insert(perInfoTemplist);
                }
                if (familyMapList.size() > 0) {
                    fcPerinfoFamilyTempMapper.insertList(familyMapList);
                }
                if (StringUtils.isNotBlank(newFlag) && "1".equals(newFlag)) {
                    Map<String, String> map = new HashMap<>();
                    map.put("grpNo", grpNo);
                    map.put("ensureCode", ensureCode);
                    asyncService.insertNewUser(globalInput, map);
                }
                log.info("人员清单导入成功");
                resultMap.put("success", true);
                resultMap.put("code", "200");
                if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                    resultMap.put("message", "人员清单导入成功,本次成功导入" + count + "条员工数据，" + familyCount + "条家属数据。");
                } else {
                    resultMap.put("message", "人员清单导入成功,本次成功导入" + count + "条数据");
                }
            }
        } catch (Exception e) {
            log.info("人员清单导入失败：", e);
            errorMsgList.add("人员清单导入失败");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", errorMsgList);
            return resultMap;
        }
        return resultMap;
    }

    private String checkPhone(List<FCPerInfoTemp> perInfoTempList, List<FCPerinfoFamilyTemp> familyMapList, String errorMsg) {
        List<String> mobilePhoneList = perInfoTempList.stream().map(FCPerInfoTemp::getMobilePhone).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        List<String> phoneList = familyMapList.stream().map(FCPerinfoFamilyTemp::getPhone).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        Set<String> repeatPhone = mobilePhoneList.stream().filter(phoneList::contains).collect(Collectors.toSet());
        log.info("checkPhone repeatPhone:{}", JsonUtil.toJSON(repeatPhone));

        Map<String, List<FCPerInfoTemp>> perInfoTempMap = perInfoTempList.stream().collect(Collectors.groupingBy(FCPerInfoTemp::getMobilePhone));
        Map<String, List<FCPerinfoFamilyTemp>> familyMap = familyMapList.stream().collect(Collectors.groupingBy(FCPerinfoFamilyTemp::getPhone));

        for (String phone : repeatPhone) {
            List<String> names = Lists.newArrayList();
            List<FCPerInfoTemp> perInfList = perInfoTempMap.get(phone);
            List<FCPerinfoFamilyTemp> familyList = familyMap.get(phone);
            if (CollectionUtils.isNotEmpty(perInfList)) {
                names.addAll(perInfList.stream().map(FCPerInfoTemp::getName).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(familyList)) {
                names.addAll(familyList.stream().map(FCPerinfoFamilyTemp::getName).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(names)) {
                errorMsg += "手机号" + phone + "和" + names + "重复，请录入家属本人号码。或家属手机号可为空/";
            }
        }
        return errorMsg;
    }


    public String isCheckFamilyEmpTy(int i, Row row) {
        String messsage = "";
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(1)))) {
            messsage += "家属第" + i + "行姓名不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(2)))) {
            messsage += "家属第" + i + "行国籍不能为空/";
        }

        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(3)))) {
            messsage += "家属第" + i + "行证件类型不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(4)))) {
            messsage += "家属第" + i + "行证件号不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(7)))) {
            messsage += "家属第" + i + "行性别不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(8)))) {
            messsage += "家属第" + i + "行出生日期不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(9)))) {
            messsage += "家属第" + i + "行职业类别不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(10)))) {
            messsage += "家属第" + i + "行职业代码不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(11)))) {
            messsage += "家属第" + i + "行有无医保不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(12)))) {
            messsage += "家属第" + i + "行与员工关系不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(13)))) {
            messsage += "家属第" + i + "行员工姓名不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(14)))) {
            messsage += "家属第" + i + "行员工证件类型不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(15)))) {
            messsage += "家属第" + i + "行员工证件号不能为空/";
        }
        return messsage;
    }

    public Map<String, List<String>> getOccupationTypeCode() {
        Map<String, List<String>> hashMap = new HashMap<>();
        List<Map<String, String>> list = fcPerInfoTempMapper.selectOccupationList("01");
        if (list != null && list.size() > 0) {
            for (Map map : list) {
                List<String> codeList = new ArrayList<String>();
                String codeKey = map.get("occupationType").toString().trim();
                List<Map<String, String>> codelist = fcPerInfoTempMapper
                        .selectOccupationCode(codeKey);
                if (codelist != null && codelist.size() > 0) {
                    for (Map<String, String> stringStringMap : codelist) {
                        codeList.add(stringStringMap.get("occupationCode").trim());
                    }
                }
                hashMap.put(map.get("occupationType").toString().trim(), codeList);
            }
        }
        return hashMap;
    }

    public String checkCodeKey(int i, Row row, List<String> idTypeList,
                               List<String> occupationTypeList, List<String> occupationCodeList,
                               List<String> openBankList, String planType) {
        String errorMsg = "";
        String idType = ExcelUtil.getCellValue(row.getCell(4));
        if (StringUtils.isNotBlank(idType)) {
            if (!idTypeList.contains(idType)) {
                errorMsg += "第" + i + "行证件类型录入错误/";
            }
        }
        if ("0".equals(planType)) { // 固定计划处理表数据时
            if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(12))) && StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(13)))) {
                String occupationType = ExcelUtil.getCellValue(row.getCell(12));
                String occupationCode = String.valueOf((int) Double.parseDouble(ExcelUtil.getCellValue(row.getCell(13))));
                if (!occupationTypeList.contains(occupationType)) {
                    errorMsg += "第" + i + "行职业类别录入错误/";
                }
                if (!occupationCodeList.contains(occupationCode)) {
                    errorMsg += "第" + i + "行职业代码录入错误/";
                }
            }
        }
        if ("1".equals(planType)) { // 弹性计划处理表数据时
            if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(11))) && StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(12)))) {
                String occupationType = ExcelUtil.getCellValue(row.getCell(11));
                String occupationCode = String.valueOf((int) Double.parseDouble(ExcelUtil.getCellValue(row.getCell(12))));
                if (!occupationTypeList.contains(occupationType)) {
                    errorMsg += "第" + i + "行职业类别录入错误/";
                }
                if (!occupationCodeList.contains(occupationCode)) {
                    errorMsg += "第" + i + "行职业代码录入错误/";
                }
            }
        }
        return errorMsg;
    }

    public String checkCodeKey2(int i, Row row, List<String> idTypeList,
                                List<String> occupationTypeList, List<String> occupationCodeList,
                                List<String> openBankList, String planType) {
        String errorMsg = "";
        String idType = ExcelUtil.getCellValue(row.getCell(5));
        if (StringUtils.isNotBlank(idType)) {
            if (!idTypeList.contains(idType)) {
                errorMsg += "第" + i + "行证件类型录入错误/";
            }
        }
        if ("0".equals(planType)) { // 固定计划处理表数据时
            if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(13))) && StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(14)))) {
                String occupationType = ExcelUtil.getCellValue(row.getCell(13));
                String occupationCode = String.valueOf((int) Double.parseDouble(ExcelUtil.getCellValue(row.getCell(14))));
                if (!occupationTypeList.contains(occupationType)) {
                    errorMsg += "第" + i + "行职业类别录入错误/";
                }
                if (!occupationCodeList.contains(occupationCode)) {
                    errorMsg += "第" + i + "行职业代码录入错误/";
                }
            }
        }
        if ("1".equals(planType)) { // 弹性计划处理表数据时
            if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(12))) && StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(13)))) {
                String occupationType = ExcelUtil.getCellValue(row.getCell(12));
                String occupationCode = String.valueOf((int) Double.parseDouble(ExcelUtil.getCellValue(row.getCell(13))));
                if (!occupationTypeList.contains(occupationType)) {
                    errorMsg += "第" + i + "行职业类别录入错误/";
                }
                if (!occupationCodeList.contains(occupationCode)) {
                    errorMsg += "第" + i + "行职业代码录入错误/";
                }
            }
        }
        return errorMsg;
    }

    public String checkOneCodeKey(FCPerInfoTemp fcperinfo,
                                  List<String> idTypeList, List<String> occupationTypeList,
                                  List<String> occupationCodeList, List<String> openBankList) {
        //证件类型码值转换
        String idTypeValue = fcperinfo.getIDType();
        String idType = "";
        List<HashMap<String, Object>> idTypeCodeList = fdCodeMapper.CodeInfo("IDType");
        for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
            if (idTypeValue.equals(hashMap1.get("CodeKey").toString())) {
                idType = hashMap1.get("CodeName").toString();
                break;
            }
        }
        String occupationType = fcperinfo.getOccupationType();
        String occupationCode = fcperinfo.getOccupationCode();
        if (!idTypeList.contains(idType)) {
            return "证件类型录入错误";
        }
        if (!occupationTypeList.contains(occupationType)) {
            return "职业类别录入错误";
        }
        if (!occupationCodeList.contains(occupationCode)) {
            return "职业代码录入错误";
        }
        return "";
    }

    public String checkStudentInfo(int i, Row row) {
        String errorMsg = "";
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(1)))) {
            errorMsg += "第" + i + "行学生姓名不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(3)))) {
            errorMsg += "第" + i + "行学生证件类型不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(4)))) {
            errorMsg += "第" + i + "行学生证件号不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(6)))) {
            errorMsg += "第" + i + "行学生性别不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(7)))) {
            errorMsg += "第" + i + "行学生出生日期不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(9)))) {
            errorMsg += "第" + i + "行职业类别不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(10)))) {
            errorMsg += "第" + i + "行职业代码不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(11)))) {
            errorMsg += "第" + i + "行有无医保不能为空/";
        }
        if (!StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(8)))) {
            if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(12)))) {
                errorMsg += "第" + i + "行福利额度不能为空/";
            }
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(8)))) {
            if (!StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(12)))) {
                errorMsg += "第" + i + "行学生默认计划没有录入，福利额度不可录入/";
            }
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(13)))) {
            errorMsg += "第" + i + "行与监护人关系不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(14)))) {
            errorMsg += "第" + i + "行监护人姓名不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(16)))) {
            errorMsg += "第" + i + "行监护人证件类型不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(17)))) {
            errorMsg += "第" + i + "行监护人证件号不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(19)))) {
            errorMsg += "第" + i + "行监护人性别不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(20)))) {
            errorMsg += "第" + i + "行监护人出生日期不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(21)))) {
            errorMsg += "第" + i + "行监护人手机号不能为空/";
        }
        return errorMsg;
    }

    public String checkIsEmpTy(int i, Row row) {
        String errorMsg = "";
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(1)))) {
            errorMsg += "第" + i + "行姓名不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(3)))) {
            errorMsg += "第" + i + "行与投保人关系不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(4)))) {
            errorMsg += "第" + i + "行国籍不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(5)))) {
            errorMsg += "第" + i + "行证件类型不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(6)))) {
            errorMsg += "第" + i + "行证件号不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(8)))) {
            errorMsg += "第" + i + "行手机号不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellFormula(row.getCell(9)))) {
            errorMsg += "第" + i + "行性别不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellFormula(row.getCell(10)))) {
            errorMsg += "第" + i + "行出生日期不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(11)))) {
            errorMsg += "第" + i + "行默认计划编码不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(12)))) {
            errorMsg += "第" + i + "行员工职级不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(13)))) {
            errorMsg += "第" + i + "行职业类别不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(14)))) {
            errorMsg += "第" + i + "行职业编码不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(15)))) {
            errorMsg += "第" + i + "行有无医保不能为空/";
        }
        return errorMsg;
    }

    public String checkEflexIsEmpTy(int i, Row row) {
        String errorMsg = "";
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(1)))) {
            errorMsg += "第" + i + "行姓名不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(3)))) {
            errorMsg += "第" + i + "行国籍不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(4)))) {
            errorMsg += "第" + i + "行证件类型不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(5)))) {
            errorMsg += "第" + i + "行证件号不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(7)))) {
            errorMsg += "第" + i + "行手机号不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellFormula(row.getCell(8)))) {
            errorMsg += "第" + i + "行性别不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellFormula(row.getCell(9)))) {
            errorMsg += "第" + i + "行出生日期不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(10)))) {
            errorMsg += "第" + i + "行员工职级不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(11)))) {
            errorMsg += "第" + i + "行职业类别不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(12)))) {
            errorMsg += "第" + i + "行职业编码不能为空/";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(13)))) {
            errorMsg += "第" + i + "行有无医保不能为空/";
        }
        return errorMsg;
    }

    public String checkOneIsEmpTy(FCPerInfoTemp fcperinfo, String planType) {
        if (StringUtil.isEmpty(fcperinfo.getName())) {
            return "姓名不能为空,";
        }
        if (StringUtil.isEmpty(fcperinfo.getSex())) {
            return "性别不能为空,";
        }
        if (StringUtil.isEmpty(fcperinfo.getBirthDay())) {
            return "出生日期不能为空,";
        }
        if (StringUtil.isEmpty(fcperinfo.getMobilePhone())) {
            return "手机号不能为空,";
        }
        if (StringUtil.isEmpty(fcperinfo.getNativeplace())) {
            return "国籍不能为空,";
        }
        if (StringUtil.isEmpty(fcperinfo.getIDType())) {
            return "证件类型不能为空,";
        }
        if (StringUtil.isEmpty(fcperinfo.getIDNo())) {
            return "证件号不能为空,";
        }
        if (planType.equals("0")) {
            if (StringUtil.isEmpty(fcperinfo.getDefaultPlan())) {
                return "默认计划编码不能为空,";
            }
        } else {
            //获取员工服务年限 是否退休
            List<FcBusinessProDutyGrpObject> fcBusinessProDutyGrpObjectList = fcBusinessProDutyGrpObjectMapper.selectByEnsureCode(fcperinfo.getEnsureCode(), "01");
            if (StringUtil.isEmpty(fcperinfo.getLevelCode())) {
                return "职级不能为空,";
            }
            if (fcBusinessProDutyGrpObjectList.size() > 0) {
                if ((StringUtils.isNotBlank(fcBusinessProDutyGrpObjectList.get(0).getComeAgeLowLimit())
                        || StringUtils.isNotBlank(fcBusinessProDutyGrpObjectList.get(0).getComeAgeLowLimit()))
                        && StringUtils.isBlank(fcperinfo.getServiceTerm())) {
                    return "服务年限不能为空,";
                }
                if (StringUtils.isNotBlank(fcBusinessProDutyGrpObjectList.get(0).getRetirement()) && StringUtils.isBlank(fcperinfo.getRetirement())) {
                    return "是否退休不能为空,";
                }
            }
            if (StringUtils.isNotBlank(fcperinfo.getServiceTerm())) {
                if (!fcperinfo.getServiceTerm().matches("^\\d+$")) {
                    return "服务年限应为大于等于0的整数,";
                }
            }
        }
        if (StringUtil.isEmpty(fcperinfo.getOccupationType())) {
            return "职业类别不能为空,";
        }
        if (StringUtil.isEmpty(fcperinfo.getOccupationCode())) {
            return "职业编码不能为空,";
        }
        if (StringUtil.isEmpty(fcperinfo.getJoinMedProtect())) {
            return "有无医保不能为空,";
        }
        return "";
    }

    public String checkIDCard(int i, Row row) {
        DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        String errorMsg = "";
        try {
            String idno = ExcelUtil.getCellValue(row.getCell(5));
            String sex = ExcelUtil.getCellFormula(row.getCell(8));
            String birthday = ExcelUtil.getCellFormula(row.getCell(9));
            if (!IDCardUtil.isIDCard(idno)) {
                errorMsg += "第" + i + "行身份证号格式错误：" + idno + "/";
            } else if (idno.length() != 18) {
                // 经由核心、运营确认，身份证号仅能为18位 add by wudezhong 2021.5.27
                errorMsg += "清单第" + i + "行证件号码长度应为18位！";
            } else {
                date = format1.parse(birthday);
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String dateString = formatter.format(date);
                String idBir = IDCardUtil.dateOfBirth(idno);
                if (!dateString.equals(idBir)) {
                    errorMsg += "第" + i + "行出生日期与身份证不符/";
                }
                if (!getGenderByIdCard(idno).equals(sex)) {
                    errorMsg += "第" + i + "行性别与身份证不符/";
                }
            }
        } catch (ParseException e) {
            errorMsg = "第" + i + "行数据异常/";
            return errorMsg;
        }
        return errorMsg;
    }


    public String checkOneIDCard(FCPerInfoTemp fcperinfo) {
        try {
            String idno = fcperinfo.getIDNo();
            String sex = fcperinfo.getSex();
            String birthday = DateTimeUtil.dateFormat(fcperinfo.getBirthDay());
            if (!IDCardUtil.isIDCard(idno)) {
                return "身份证号格式错误！";
            }
            String checkIDCardResult = IDCardUtil.checkIDCard(idno, sex, birthday);
            if (StringUtils.isNotBlank(checkIDCardResult)) {
                return checkIDCardResult;
            }
        } catch (Exception e) {
            return "数据异常！";
        }
        return "";
    }

    public static String getGenderByIdCard(String idCard) {
        String sGender = "未知";
        String sCardNum = IDCardUtil.sex(idCard);
        if (Integer.parseInt(sCardNum) == 0) {
            sGender = "男";
        } else {
            sGender = "女";
        }
        return sGender;
    }


    // 校验科学计数法
    public boolean checkScientifiNotation(String arr) {
        if (arr.contains("E") && arr.contains(".")) {
            return false;
        }
        return true;
    }

    public boolean checkIdNoIsExistsTemp(String grpNo, List<HashMap<String, String>> listPerIfo) {
        int val = fcPerInfoTempMapper.checkIdNoIsExistsTemp(grpNo, listPerIfo);
        if (val > 0)
            return false;
        return true;
    }

    public boolean updateCheckIdNoIsExistsTemp(HashMap<String, String> perIfoMap) {
        int val = fcPerInfoTempMapper.updateCheckIdNoIsExistsTemp(perIfoMap);
        if (val > 0)
            return false;
        return true;
    }

    public List<FCPerInfoTemp> updateCheckOtherIsEsistsTemp(HashMap<String, String> perIfoMap) {
        List<FCPerInfoTemp> list = fcPerInfoTempMapper.updateCheckOtherIsEsistsTemp(perIfoMap);
        return list;
    }

    public boolean checkIdNoIsExists(List<HashMap<String, String>> listPerIfo) {
        int val = fcPerInfoTempMapper.checkIdNoIsExists(listPerIfo);
        if (val > 0)
            return false;
        return true;
    }

    public List<FCPerInfoTemp> checkOtherIsEsistsTemp(String grpNo, List<HashMap<String, String>> listPerIfo) {
        List<FCPerInfoTemp> list = fcPerInfoTempMapper.checkOtherIsEsistsTemp(grpNo, listPerIfo);
        return list;
    }

    public List<FCPerInfoTemp> checkOtherIsEsists(List<HashMap<String, String>> listPerIfo) {
        List<FCPerInfoTemp> list = fcPerInfoTempMapper.checkOtherIsEsists(listPerIfo);
        return list;
    }

    public List<FCPerInfoTemp> checkTempIdNoIsExists(String grpNo, List<HashMap<String, String>> listPerIfo) {
        List<FCPerInfoTemp> list = fcPerInfoTempMapper.checkTempIdNoIsExists(grpNo, listPerIfo);
        return list;
    }

    public List<FCPerInfoTemp> getNeedSyncNum(String ensureCode) {
        List<FCPerInfoTemp> needSyncNum = fcPerInfoTempMapper.getNeedSyncNum(ensureCode);
        return needSyncNum;
    }

    public List<Map<String, String>> getNeedSyncGarNum(String ensureCode) {
        List<Map<String, String>> needSyncNum = fcPerInfoTempMapper.getNeedSyncGarNum(ensureCode);
        return needSyncNum;
    }

    /**
     * 员工清单查询
     *
     * @param token
     * @return
     */
    public String queryStaff(String token, String defaultPlan, String name, String sex, String IDType, String IDNo, String ensureCode, String levelCode, String nativePlace, int pageNo, int pageSize) {
        GlobalInput globalInput = userService.getSession(token);
        return queryStaffAdmin(token, defaultPlan, globalInput.getGrpNo(), name, sex, IDType, IDNo, ensureCode, nativePlace, pageNo, pageSize);
    }

    public String queryStaffAdmin(String token, String defaultPlan, String grpNo, String name, String sex, String IDType, String IDNo, String ensureCode, String nativeplace, int pageNo, int pageSize) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("grpNo", grpNo);
            param.put("ensureCode", ensureCode);
            param.put("defaultPlan", defaultPlan);
            param.put("name", name);
            param.put("sex", sex);
            param.put("IDType", IDType);
            param.put("IDNo", IDNo);
            param.put("nativeplace", nativeplace);
            List<Map<String, String>> fcPerInfoList = fcPerInfoMapper.selectByParamList(param);
            //******************
            for (Map<String, String> map : fcPerInfoList) {
                String nativeplace1 = map.get("nativeplace");
                String relationship = map.get("relationship");
                String s = fdCodeMapper.selectNameByCode("nativeplace", nativeplace1);
                String d = fdCodeMapper.selectNameByCode("PolicyholderRelationship", relationship);
                map.put("nativeplaceName", s);
                map.put("relationshipName", d);
            }
            //******************
            List<FCPerinfoFamilyTemp> fcPerinfoFamilyTempList = fcPerinfoFamilyTempMapper.getFamilyByEnsureCode(param);
            PageHelper.startPage(pageNo, pageSize);
            List<Map<String, String>> fcInfoList = new LinkedList<>();
            fcInfoList.addAll(fcPerInfoList);
            for (Map<String, String> map : fcPerInfoList) {
                for (FCPerinfoFamilyTemp fcPerinfoFamilyTemp : fcPerinfoFamilyTempList) {
                    Map<String, String> perinfoFamilyMap = new HashMap<>();
                    if (map.get("perTempNo").equals(fcPerinfoFamilyTemp.getPerTempNo())) {
                        perinfoFamilyMap.put("FamilyTempNo", fcPerinfoFamilyTemp.getFamilyTempNo());
                        perinfoFamilyMap.put("perTempNo", fcPerinfoFamilyTemp.getPerTempNo());
                        perinfoFamilyMap.put("ensureCode", fcPerinfoFamilyTemp.getEnsureCode());
                        perinfoFamilyMap.put("name", fcPerinfoFamilyTemp.getName());
                        perinfoFamilyMap.put("sex", fcPerinfoFamilyTemp.getSex());
                        perinfoFamilyMap.put("iDNo", Base64AndMD5Util.base64SaltEncode(fcPerinfoFamilyTemp.getIDNo(), "130"));
                        perinfoFamilyMap.put("iDType", fcPerinfoFamilyTemp.getIDType());
                        perinfoFamilyMap.put("birthDay", fcPerinfoFamilyTemp.getBirthDay());
                        perinfoFamilyMap.put("mobilePhone", Base64AndMD5Util.base64SaltEncode(fcPerinfoFamilyTemp.getPhone(), "130"));
                        perinfoFamilyMap.put("relationship", fcPerinfoFamilyTemp.getRelationship());
                        perinfoFamilyMap.put("joinMedProtect", fcPerinfoFamilyTemp.getJoinMedProtect());
                        perinfoFamilyMap.put("occupationCode", fcPerinfoFamilyTemp.getOccupationCode());
                        perinfoFamilyMap.put("occupationType", fcPerinfoFamilyTemp.getOccupationType());
                        perinfoFamilyMap.put("relation", fcPerinfoFamilyTemp.getRelation());
                        perinfoFamilyMap.put("perName", fcPerinfoFamilyTemp.getPerName());
                        perinfoFamilyMap.put("perIDType", fcPerinfoFamilyTemp.getPerIDType());
                        perinfoFamilyMap.put("perIDNo", fcPerinfoFamilyTemp.getPerIDNo());
                        perinfoFamilyMap.put("nativeplace", fcPerinfoFamilyTemp.getNativeplace());
                        perinfoFamilyMap.put("nativeplaceName", fcPerinfoFamilyTemp.getNativeplaceName());
                        perinfoFamilyMap.put("idTypeEndDate", fcPerinfoFamilyTemp.getIdTypeEndDate());
                        fcInfoList.add(perinfoFamilyMap);
                    }


                }
                //脱敏处理
                map.put("iDNo", Base64AndMD5Util.base64SaltEncode(map.get("iDNo"), "130"));
                map.put("mobilePhone", Base64AndMD5Util.base64SaltEncode(map.get("mobilePhone"), "130"));
            }
            if (fcInfoList.size() > 0) {
                // 如果有数据则进行码值转换
                for (int i = 0; i < fcInfoList.size(); i++) {
                    FDCodeKey key = new FDCodeKey();
                    key.setCodeType("Sex");
                    key.setCodeKey(fcInfoList.get(i).get("sex"));
                    fcInfoList.get(i).put("sexName", fdCodeMapper.selectByPrimaryKey(key).getCodeName());
                    key.setCodeType("IDType");
                    key.setCodeKey(fcInfoList.get(i).get("iDType"));
                    fcInfoList.get(i).put("iDTypeName", fdCodeMapper.selectByPrimaryKey(key).getCodeName());
                    key.setCodeType("OccupationDetail");
                    key.setCodeKey(String.valueOf((int) Double.parseDouble(fcInfoList.get(i).get("occupationCode"))));
                    fcInfoList.get(i).put("occupationCodeName", fdCodeMapper.selectByPrimaryKey(key).getCodeName().trim());
                    if (!"".equals(fcInfoList.get(i).get("nativePlace")) && fcInfoList.get(i).get("nativePlace") != null) {
                        key.setCodeType("Nativeplace");
                        key.setCodeKey(fcInfoList.get(i).get("nativeplace"));
                        fcInfoList.get(i).put("nativeplace", fdCodeMapper.selectByPrimaryKey(key).getCodeName().trim());
                    }
                    if (StringUtils.isNotBlank(fcInfoList.get(i).get("retirement"))) {
                        key.setCodeType("isRetire");
                        key.setCodeKey(fcInfoList.get(i).get("retirement"));
                        fcInfoList.get(i).put("retirement", fdCodeMapper.selectByPrimaryKey(key).getCodeName().trim());
                    }
                }
            }
            log.info("员工清单查询成功");
            Map<String, Object> dataMap = new HashMap<>();
            PageHelperUtil<Map<String, String>> teamPageInfo = new PageHelperUtil<>(fcInfoList);
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("list", teamPageInfo.getList());
            resultMap.put("data", dataMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "员工清单查询成功");
            // 这里会有转成json字符串后首字母小写问题
            ObjectMapper mapper = new ObjectMapper();
            String mapJakcson = mapper.writeValueAsString(resultMap);
            return mapJakcson;
        } catch (Exception e) {
            log.info("员工清单查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "员工清单查询失败");
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 员工删除
     *
     * @param perNo
     * @return
     */
    @Transactional
    public String deleteStaff(String token, String perNo, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            HashMap<String, String> map = new HashMap<>();
            map.put("perTempNo", perNo);
            fcPerinfoFamilyTempMapper.deleteByPrimaryKey(map);
            fcPerInfoTempMapper.deleteFcPerInfoTemp(perNo);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "员工删除成功。");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("员工删除失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "员工删除失败");
            return JSON.toJSONString(resultMap);
        }
    }

    public String editStaff(String token, FCPerInfoTemp fcPerInfo, String ensureCode) {
        // 解密
        fcPerInfo.decode();
        String errorMsg = "";
        GlobalInput globalInput = userService.getSession(token);
        Map<String, Object> resultMap = new HashMap<>();
        log.info("个人信息+++++++++=" + JSON.toJSONString(fcPerInfo));
        try {
            fcPerInfo.setEnsureCode(ensureCode);
            if (!CheckUtils.checkMobilePhone(fcPerInfo.getMobilePhone())) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "手机号格式错误，请检查");
                return JSON.toJSONString(resultMap);
            }
            //校验姓名
            String name = fcPerInfo.getName();
            fcPerInfo.setName(name.trim().replaceAll(" +", " "));
            String idType = fcPerInfo.getIDType();
            if (!StringUtil.isEmpty(idType) && !"1".equals(idType)) {
                if (!StringUtil.isEmpty(name)) {
                    String s = CheckUtils.checkChineseName(name);
                    if (!StringUtil.isEmpty(s)) {
                        return JSON.toJSONString(ResultUtil.error(s));
                    }
                }
            }

            //校验手机号是否注册
            List<FdUser> list1;
            if (fcPerInfoTempMapper.selectByPhone(fcPerInfo.getMobilePhone(), fcPerInfo.getPerTempNo(), fcPerInfo.getIDNo(), fcPerInfo.getIDType()) > 0) {
                return JSON.toJSONString(ResultUtil.error("该手机号已存在！"));
            }
            if ((list1 = fdUserMapper.selectByPhone(fcPerInfo.getMobilePhone(), "1", fcPerInfo.getIDNo())) != null && list1.size() > 0) {
                return JSON.toJSONString(ResultUtil.error("该手机号已注册！"));
            }

            List<FCPerinfoFamilyTemp> fcPerInfoFamilyTemps = fcPerinfoFamilyTempMapper.selectByFamilyTemp(new FCPerinfoFamilyTemp(ensureCode, fcPerInfo.getMobilePhone()));
            if (CollectionUtils.isNotEmpty(fcPerInfoFamilyTemps)) {
                List<String> names = fcPerInfoFamilyTemps.stream().map(FCPerinfoFamilyTemp::getName).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(names)) {
                    errorMsg = fcPerInfo.getMobilePhone() + "手机号和" + names + "重复，请确保手机号唯一，无法提供时可忽略家属号码录入！";
                    throw new RuntimeException();
                }
            }

            if (fcPerInfo.getPerTempNo() == null || fcPerInfo.getIDNo() == null
                    || fcPerInfo.getPerTempNo().isEmpty()
                    || fcPerInfo.getIDNo().isEmpty()) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "证件号和员工号不能为空。");
                return JSON.toJSONString(resultMap);
            }
            // 根据证件号、福利编号判断临时表是否已存在 // 当前福利编号下已存在相同的证件号
            if (!checkTempOneNIdNoIsExists(fcPerInfo.getIDNo(), ensureCode, fcPerInfo.getPerTempNo())) {
                errorMsg = "当前福利编号下已存在相同的证件号，请检查";
                throw new RuntimeException();
            }

            // 查询福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            //根据证件号判断临时表是否存在该员工（除自身以外）
            HashMap<String, String> perHashMap = new HashMap<>();
            perHashMap.put("name", fcPerInfo.getName());
            perHashMap.put("idno", fcPerInfo.getIDNo());
            perHashMap.put("sex", fcPerInfo.getSex());
            perHashMap.put("birthday", fcPerInfo.getBirthDay());
            perHashMap.put("idtype", fcPerInfo.getIDType());
            perHashMap.put("grpNo", globalInput.getGrpNo());
            perHashMap.put("perTempNo", fcPerInfo.getPerTempNo());
            if (!updateCheckIdNoIsExistsTemp(perHashMap)) {
                if (updateCheckOtherIsEsistsTemp(perHashMap).size() > 0) {
                    errorMsg = "当前企业定制中福利存在相同证件号，但是姓名、性别、出生日期、证件类型不同，请核对后进行修改";
                    throw new RuntimeException();
                }
            }
            // 根据证件号判断正式表是否已存在
            if (!checkOneIdNoIsExists(fcPerInfo.getIDNo())) {
                // 存在相同的证件号
                // 判断是否存在证件号存在，但是其他四要素不同的数据  同一企业下  不同企业互不影响
                if (!checkOneOtherIsEsists(fcPerInfo.getIDNo(), fcPerInfo.getName(), fcPerInfo.getBirthDay(),
                        fcPerInfo.getSex(), fcPerInfo.getIDType())) {// 存在证件号相同，其他四要素不同的数据
                    errorMsg = "当前系统存在相同的员工证件号，但是姓名、性别、出生日期、证件类型不同，请核对后进行修改";
                    throw new RuntimeException();
                }
            }
            List<Map<String, Object>> list = new ArrayList<>();
            if ("1".equals(fcEnsure.getPlanType())) {
                //计算员工默认计划保费和福利额度的关系
                Map<String, Object> map = new HashMap<>();
                map.put("EnsureCode", ensureCode);
                map.put("BirthDay", fcPerInfo.getBirthDay());
                map.put("CvaliDate", fcEnsure.getCvaliDate());
                map.put("Sex", fcPerInfo.getSex());
                map.put("JoinMedProtect", fcPerInfo.getJoinMedProtect());
                map.put("OccupationType", fcPerInfo.getOccupationType());
                map.put("LevelCod", fcPerInfo.getLevelCode());
                map.put("ServiceTerm", fcPerInfo.getServiceTerm());
                map.put("Retirement", fcPerInfo.getRetirement());
                map.put("Relation", "0");
                list = premTrailService.generateRequest(map);
            }
            if (StringUtils.isNotBlank(fcPerInfo.getStaffGrpPrem() == null ? "" : String.valueOf(fcPerInfo.getStaffGrpPrem()))) {
                double remainder = fcPerInfo.getStaffGrpPrem() % 1;
                if (remainder != 0 || fcPerInfo.getStaffGrpPrem() < 0) {
                    errorMsg = "员工福利额度只能为大于等于0的整数";
                    throw new RuntimeException();
                }
                if (fcEnsure.getPlanType().equals("0")) {
                    Map<String, String> param = new HashMap<>();
                    param.put("planCode", fcPerInfo.getDefaultPlan());
                    param.put("ensureCode", ensureCode);
                    FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                    if (fcPerInfo.getStaffGrpPrem() < fcEnsurePlan.getTotalPrem()) {
                        errorMsg += "员工福利额度要大于等于默认计划的保费";
                        throw new RuntimeException();
                    }
                } else {
                    Double totalPrem = 0.00;
                    for (Map<String, Object> map2 : list) {
                        Map<String, Object> resultmap = premTrailService.premTrail(map2);
                        if (Boolean.valueOf(resultmap.get("success").toString())) {
                            if ("17020".equals(map2.get("RiskCode").toString()) || "15060".equals(map2.get("RiskCode").toString()) || "15070".equals(map2.get("RiskCode").toString())) {
                                Map<String, Object> premMap = (Map<String, Object>) resultmap.get("Prem");
                                totalPrem = CommonUtil.add(totalPrem, Double.valueOf(premMap.get("Prem").toString()));
                            } else {
                                totalPrem = CommonUtil.add(totalPrem, Double.valueOf(resultmap.get("Prem").toString()));
                            }
                        }
                    }
                    log.info("员工福利额度=" + fcPerInfo.getStaffGrpPrem() + "&&默认计划总保费=" + totalPrem);
                    if (fcPerInfo.getStaffGrpPrem() < totalPrem) {
                        errorMsg += "员工福利额度要大于等于默认计划的保费，默认计划保费为" + totalPrem + "元";
                        throw new RuntimeException();
                    }
                }
            } else if (null == fcPerInfo.getFamilyGrpPrem() && "3".equals(fcEnsure.getPayType())) {
                //个人批扣福利  额度不能为空
                errorMsg += "员工福利额度要大于等于默认计划的保费且额度不能为空";
                throw new RuntimeException();
            }
            if (StringUtils.isNotBlank(fcPerInfo.getFamilyGrpPrem() == null ? "" : String.valueOf(fcPerInfo.getFamilyGrpPrem()))) {
                double remainder = fcPerInfo.getFamilyGrpPrem() % 1;
                if (remainder != 0 || fcPerInfo.getFamilyGrpPrem() < 0) {
                    errorMsg = "家属福利额度只能为大于等于0的整数";
                    throw new RuntimeException();
                }
            }
            // 校验字段非空
            errorMsg = checkOneIsEmpTy(fcPerInfo, fcEnsure.getPlanType());
            if (!"".equals(errorMsg)) {
                throw new RuntimeException();
            }
            // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确
            if (fcPerInfo.getIDType().equals("0")) {
                errorMsg = checkOneIDCard(fcPerInfo);
                if (!errorMsg.equals("")) {
                    throw new RuntimeException();
                }
            }
            // 获取证件类型集合
            List<String> idTypeList = fcPerInfoTempMapper.getIdTypeList("01");
            // 获取职业类别集合
            List<String> occupationTypeList = fcPerInfoTempMapper.getOccupationTypeList("01");
            // 获取职业集合
            List<String> occupationCodeList = fcPerInfoTempMapper.getOccupationCodeList("01");
            // 获取职业类别与职业代码对应关系
            Map<String, List<String>> occupationTypeCodeMap = getOccupationTypeCode();
            // 获取银行代码集合
            List<String> openBankList = fcPerInfoTempMapper.getOpenBankList("01");
            // 校验证件类型代码、职业类别代码、职业代码、银行码表是否录入符合规则
            errorMsg = checkOneCodeKey(fcPerInfo, idTypeList, occupationTypeList, occupationCodeList, openBankList);
            if (!"".equals(errorMsg)) {
                throw new RuntimeException();
            }
            // 校验当前职业代码是否符合当前职业类别
            if (!occupationTypeCodeMap.get(fcPerInfo.getOccupationType()).contains(fcPerInfo.getOccupationCode())) {
                errorMsg = "职业类别不包含所录职业";
                throw new RuntimeException();
            }
            // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确
            if ("0".equals(fcPerInfo.getIDType())) {
                errorMsg = checkOneIDCard(fcPerInfo);
                if (!"".equals(errorMsg)) {
                    throw new RuntimeException();
                }
            }
            //校验人员--add by wudezhong
            Map<String, String> map = new HashMap<>();
            map.put("sign", "1");//1：员工 2：家属
            map.put("idType", fcPerInfo.getIDType());//证件类型
            map.put("idNo", fcPerInfo.getIDNo());//证件号
            map.put("birthDay", fcPerInfo.getBirthDay());//出生日期
            map.put("sex", fcPerInfo.getSex());//性别
            map.put("nativeplace", fcPerInfo.getNativeplace());//国籍
            map.put("idTypeEndDate", fcPerInfo.getIdTypeEndDate());//证件有效期
            map.put("occupationCode", fcPerInfo.getOccupationCode());//职业代码
            map.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
            /**
             * 证件类型和国籍
             */
            String resultMsg = CheckUtils.checkSinglePeople(map);
            if (StringUtils.isNotBlank(resultMsg)) {
                errorMsg = fcPerInfo.getName() + resultMsg;
                throw new RuntimeException();
            }
            List<EvaluationCustomer> customerList = Arrays.asList(EvaluationCustomer.builder()
                    .name(fcPerInfo.getName())
                    .idType(CoreIdType.getNameByCoreId(fcPerInfo.getIDType()).name())
                    .idNo(fcPerInfo.getIDNo())
                    .gender(GenderType.getGenderByCoreId(fcPerInfo.getSex()).name())
                    .birthday(fcPerInfo.getBirthDay())
                    .nationality(fcPerInfo.getNativeplace())
                    .businessNo(ensureCode)
                    .build());
            //校验黑名单
            List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
            //校验二要素
            String failVerifies = addressCheckService.checkIdCard(verifies);
            if (StringUtils.isNotEmpty(failVerifies)) {
                errorMsg = failVerifies;
                throw new RuntimeException();
            }
            if (StringUtils.isBlank(fcPerInfo.getIdTypeEndDate())) {
                fcPerInfo.setIdTypeEndDate(null);
            }
            if (ConstantUtil.EnsureState_1.equals(fcEnsure.getEnsureState())) {
                List<String> fcOrderList = fcOrderMapper.selectByEnsureCode(ensureCode);
                if (fcOrderList.contains("02")) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "该福利下存在已提交至核心订单，不可修改");
                } else {
                    updateStaff(globalInput, fcPerInfo, ensureCode);
                    //用于区分员工或家属 0-员工  1-家属  修改fcperinfo、fcperson表数据
                    Map<String, String> updateMap = new HashMap<>();
                    updateMap.put("IDNo", fcPerInfo.getIDNo());
                    updateMap.put("mobilePhone", fcPerInfo.getMobilePhone());
                    updatePerAndPerson(globalInput, updateMap, ensureCode, "0");
                    log.info("员工编辑成功");
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", "员工编辑成功");
                }
            } else {
                updateStaff(globalInput, fcPerInfo, ensureCode);
                log.info("员工编辑成功");
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "员工编辑成功");
            }
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("员工编辑失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "员工编辑失败。" + errorMsg);
            return JSON.toJSONString(resultMap);
        }
    }

    public boolean checkOneIdNoIsExists(String idNo) {
        int val = fcPerInfoTempMapper.checkOneIdNoIsExists(idNo);
        if (val > 0) {
            return false;
        }
        return true;
    }


    public boolean checkTempOneIdNoIsExists(String idNo, String idType, String ensureCode, String grpNo) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("idNo", idNo);
        map.put("ensureCode", ensureCode);
        map.put("grpNo", grpNo);
        int val = fcPerInfoTempMapper.checkTempOneIdNoIsExists(map);
        if (val > 0) {
            return false;
        }
        return true;
    }

    public boolean checkTempOneNIdNoIsExists(String idNo, String ensureCode, String perNo) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("idNo", idNo);
        map.put("ensureCode", ensureCode);
        map.put("perNo", perNo);
        int val = fcPerInfoTempMapper.checkTempOneNIdNoIsExists(map);
        if (val > 0) {
            return false;
        }
        return true;
    }

    public boolean checkOneOtherIsEsists(String idNo, String name, String birthday, String sex, String idType) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("idNo", idNo);
        map.put("name", name);
        map.put("birthday", birthday);
        map.put("sex", sex);
        map.put("idType", idType);
        int val = fcPerInfoTempMapper.checkOneOtherIsEsists(map);
        if (val > 0) {
            return false;
        }
        return true;
    }

    public String insertStaff(String token, FCPerInfoTemp fcPerInfo, String ensureCode) {
        String errorMsg = "";
        String newFlag = fcPerInfo.getNewFlag();
        GlobalInput globalInput = userService.getSession(token);
        List<FCPerInfoTemp> perInfoTempList = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();
        String grpNo = globalInput.getGrpNo();
        String operator = globalInput.getUserNo();
        try {
            // 查询福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            fcPerInfo.setEnsureCode(ensureCode);
            // 根据证件号、福利编号判断临时表是否已存在
            if (!checkTempOneIdNoIsExists(fcPerInfo.getIDNo(), fcPerInfo.getIDType(), ensureCode, grpNo)) {// 当前福利编号下已存在相同的证件号
                errorMsg = "当前福利编号下已存在相同的证件号，请检查";
                throw new RuntimeException();
            }
            if (!CheckUtils.checkMobilePhone(fcPerInfo.getMobilePhone())) {
                errorMsg = "手机号格式错误，请检查";
                throw new RuntimeException();
            }
            //校验姓名
            String idType = fcPerInfo.getIDType();
            String name = fcPerInfo.getName();
            if (!StringUtil.isEmpty(idType) && !idType.equals("1")) {
                String s = CheckUtils.checkChineseName(name);
                if (!StringUtil.isEmpty(s)) {
                    return JSON.toJSONString(ResultUtil.error(s));
                }
            }
            fcPerInfo.setName(name.trim().replaceAll(" +", " "));
            //校验手机号是否注册
            List list1;
            if (fcPerInfoTempMapper.selectByPhone(fcPerInfo.getMobilePhone(), fcPerInfo.getPerTempNo(), fcPerInfo.getIDNo(), fcPerInfo.getIDType()) > 0) {
                return JSON.toJSONString(ResultUtil.error("该手机号已存在！"));
            }
            if ((list1 = fdUserMapper.selectByPhone(fcPerInfo.getMobilePhone(), "1", fcPerInfo.getIDNo())) != null && list1.size() > 0) {
                return JSON.toJSONString(ResultUtil.error("该手机号已注册！"));
            }

            List<FCPerinfoFamilyTemp> fcPerInfoFamilyTemps = fcPerinfoFamilyTempMapper.selectByFamilyTemp(new FCPerinfoFamilyTemp(ensureCode, fcPerInfo.getMobilePhone()));
            if (CollectionUtils.isNotEmpty(fcPerInfoFamilyTemps)) {
                List<String> names = fcPerInfoFamilyTemps.stream().map(FCPerinfoFamilyTemp::getName).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(names)) {
                    errorMsg = fcPerInfo.getMobilePhone() + "手机号和" + names + "重复 请确保手机号唯一，无法提供时可忽略家属号码录入!";
                    throw new RuntimeException();
                }
            }

            List<HashMap<String, String>> listPerIfo = new ArrayList<>();
            HashMap<String, String> garHashMap = new HashMap<>();
            garHashMap.put("name", name);
            garHashMap.put("idno", fcPerInfo.getIDNo());
            garHashMap.put("sex", fcPerInfo.getSex());
            garHashMap.put("birthday", fcPerInfo.getBirthDay());
            garHashMap.put("idtype", fcPerInfo.getIDType());
            listPerIfo.add(garHashMap);
            if (!checkIdNoIsExistsTemp(grpNo, listPerIfo)) {// 存在相同的证件号
                // 判断是否存在证件号存在，但是其他四要素不同的数据
                List<FCPerInfoTemp> checkOtherIsEsists = checkOtherIsEsistsTemp(grpNo, listPerIfo);
                if (checkOtherIsEsists.size() > 0) {// 存在证件号相同，其他四要素不同的数据
                    errorMsg += "当前企业定制中的福利存在相同的证件号，但是姓名、性别、出生日期、证件类型不同的数据,请核对后从新进行添加/";
                    throw new RuntimeException();
                }
            }

            // 根据证件号判断正式表是否已存在
            if (!checkOneIdNoIsExists(fcPerInfo.getIDNo())) {// 存在相同的证件号
                // 判断是否存在证件号存在，但是其他四要素不同的数据   同一企业下
                if (!checkOneOtherIsEsists(fcPerInfo.getIDNo(),
                        fcPerInfo.getName(), fcPerInfo.getBirthDay(),
                        fcPerInfo.getSex(), fcPerInfo.getIDType())) {// 存在证件号相同，其他四要素不同的数据
                    errorMsg = "当前系统存在相同员工证件号，但是姓名、性别、出生日期、证件类型不同的数据";
                    throw new RuntimeException();
                }
            }
            String sameCustomer = addressCheckService.checkSameCustomer(CheckSameCustomerRequest.builder()
                    .customerBirthday(fcPerInfo.getBirthDay())
                    .customerIDNo(fcPerInfo.getIDNo())
                    .customerIDType(fcPerInfo.getIDType())
                    .customerName(name)
                    .customerSex(fcPerInfo.getSex())
                    .build());
            if (StringUtils.isNotBlank(sameCustomer)) {
                errorMsg = sameCustomer;
                throw new RuntimeException();
            }
            // 判断计划是否真实存在
            if (fcEnsure.getPlanType().equals("0")) {
                Map<String, Object> sparams = new HashMap<>();
                sparams.put("ensureCode", ensureCode);
                sparams.put("planCode", fcPerInfo.getDefaultPlan());
                List<FCEnsurePlan> list = fcEnsurePlanMapper
                        .selectFCEnsurePlans(sparams);
                if (list.size() < 1) {
                    errorMsg = "该默认计划不存在，请检查默认计划编号";
                    throw new RuntimeException();
                }
            }
            // 校验字段非空
            errorMsg = checkOneIsEmpTy(fcPerInfo, fcEnsure.getPlanType());
            if (!"".equals(errorMsg)) {
                throw new RuntimeException();
            }
            List<Map<String, Object>> list = new ArrayList<>();
            if ("1".equals(fcEnsure.getPlanType())) {
                //计算员工默认计划保费和福利额度的关系
                Map<String, Object> map = new HashMap<>();
                map.put("EnsureCode", ensureCode);
                map.put("BirthDay", fcPerInfo.getBirthDay());
                map.put("CvaliDate", fcEnsure.getCvaliDate());
                map.put("Sex", fcPerInfo.getSex());
                map.put("JoinMedProtect", fcPerInfo.getJoinMedProtect());
                map.put("OccupationType", fcPerInfo.getOccupationType());
                map.put("LevelCod", fcPerInfo.getLevelCode());
                map.put("ServiceTerm", fcPerInfo.getServiceTerm());
                map.put("Retirement", fcPerInfo.getRetirement());
                map.put("Relation", "0");
                list = premTrailService.generateRequest(map);
            }
            if (StringUtils.isNotBlank(fcPerInfo.getStaffGrpPrem() == null ? "" : String.valueOf(fcPerInfo.getStaffGrpPrem()))) {
                double remainder = fcPerInfo.getStaffGrpPrem() % 1;
                if (remainder != 0 || fcPerInfo.getStaffGrpPrem() < 0) {
                    errorMsg = "员工福利额度只能为大于等于0的整数";
                    throw new RuntimeException();
                }
                if (fcEnsure.getPlanType().equals("0")) {
                    Map<String, String> param = new HashMap<>();
                    param.put("planCode", fcPerInfo.getDefaultPlan());
                    param.put("ensureCode", ensureCode);
                    FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                    if (fcPerInfo.getStaffGrpPrem() < fcEnsurePlan.getTotalPrem()) {
                        errorMsg += "员工福利额度要大于等于默认计划的保费";
                        throw new RuntimeException();
                    }
                } else {
                    Double totalPrem = 0.00;
                    for (Map<String, Object> map2 : list) {
                        Map<String, Object> resultmap = premTrailService.premTrail(map2);
                        if (Boolean.parseBoolean(resultmap.get("success").toString())) {
                            if ("17020".equals(map2.get("RiskCode").toString()) || "15060".equals(map2.get("RiskCode").toString()) || "15070".equals(map2.get("RiskCode").toString())) {
                                Map<String, Object> premMap = (Map<String, Object>) resultmap.get("Prem");
                                totalPrem = CommonUtil.add(totalPrem, Double.valueOf(premMap.get("Prem").toString()));
                            } else {
                                totalPrem = CommonUtil.add(totalPrem, Double.valueOf(resultmap.get("Prem").toString()));
                            }
                        }
                    }
                    log.info("员工福利额度=" + fcPerInfo.getStaffGrpPrem() + "&&默认计划总保费=" + totalPrem);
                    if (fcPerInfo.getStaffGrpPrem() < totalPrem) {
                        errorMsg += "员工福利额度要大于等于默认计划的保费，默认计划保费为" + totalPrem + "元";
                        throw new RuntimeException();
                    }
                }
            } else if (null == fcPerInfo.getFamilyGrpPrem() && "3".equals(fcEnsure.getPayType())) {
                //个人批扣福利  额度不能为空
                errorMsg += "员工福利额度要大于等于默认计划的保费且额度不能为空";
                throw new RuntimeException();
            }
            if (StringUtils.isNotBlank(fcPerInfo.getFamilyGrpPrem() == null ? "" : String.valueOf(fcPerInfo.getFamilyGrpPrem()))) {
                double remainder = fcPerInfo.getFamilyGrpPrem() % 1;
                if (remainder != 0 || fcPerInfo.getFamilyGrpPrem() < 0) {
                    errorMsg = "家属福利额度只能为大于等于0的整数";
                    throw new RuntimeException();
                }
            }
            // 获取证件类型集合
            List<String> idTypeList = fcPerInfoTempMapper.getIdTypeList("01");
            // 获取职业类别集合
            List<String> occupationTypeList = fcPerInfoTempMapper.getOccupationTypeList();
            // 获取职业集合
            List<String> occupationCodeList = fcPerInfoTempMapper.getOccupationCodeList("01");
            // 获取职业类别与职业代码对应关系
            Map<String, List<String>> occupationTypeCodeMap = getOccupationTypeCode();
            // 获取银行代码集合
            List<String> openBankList = fcPerInfoTempMapper.getOpenBankList("01");
            // 校验证件类型代码、职业类别代码、职业代码、银行码表是否录入符合规则
            errorMsg = checkOneCodeKey(fcPerInfo, idTypeList, occupationTypeList, occupationCodeList, openBankList);
            if (!"".equals(errorMsg)) {
                throw new RuntimeException();
            }
            // 校验当前职业代码是否符合当前职业类别
            if (!occupationTypeCodeMap.get(fcPerInfo.getOccupationType()).contains(fcPerInfo.getOccupationCode())) {
                errorMsg = "职业类别不包含所录职业";
                throw new RuntimeException();
            }
            // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确
            if ("0".equals(fcPerInfo.getIDType())) {
                errorMsg = checkOneIDCard(fcPerInfo);
                if (!"".equals(errorMsg)) {
                    throw new RuntimeException();
                }
            }
            //校验人员--add by wudezhonga
            Map<String, String> map = new HashMap<>();
            map.put("sign", "1");
            map.put("idType", fcPerInfo.getIDType());//证件类型
            map.put("idNo", fcPerInfo.getIDNo());//证件号
            map.put("birthDay", fcPerInfo.getBirthDay());//出生日期
            map.put("sex", fcPerInfo.getSex());//性别
            map.put("nativeplace", fcPerInfo.getNativeplace());//国籍
            map.put("idTypeEndDate", fcPerInfo.getIdTypeEndDate());//证件有效期
            map.put("occupationCode", fcPerInfo.getOccupationCode());//职业代码
            map.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
            map.put("levelCode", fcPerInfo.getLevelCode());// 职级
            /**
             * 证件类型和国籍
             */
            String resultMsg = CheckUtils.checkSinglePeople(map);
            if (StringUtils.isNotBlank(resultMsg)) {
                errorMsg = fcPerInfo.getName() + resultMsg;
                throw new RuntimeException();
            }
            List<EvaluationCustomer> customerList = Arrays.asList(EvaluationCustomer.builder()
                    .name(fcPerInfo.getName())
                    .idType(CoreIdType.getNameByCoreId(fcPerInfo.getIDType()).name())
                    .idNo(fcPerInfo.getIDNo())
                    .gender(GenderType.getGenderByCoreId(fcPerInfo.getSex()).name())
                    .birthday(fcPerInfo.getBirthDay())
                    .nationality(fcPerInfo.getNativeplace())
                    .businessNo(ensureCode)
                    .build());
            List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
            //校验二要素
            String failVerifies = addressCheckService.checkIdCard(verifies);
            if (StringUtils.isNotEmpty(failVerifies)) {
                errorMsg = failVerifies;
                throw new RuntimeException();
            }


            if (StringUtils.isBlank(fcPerInfo.getIdTypeEndDate())) {
                fcPerInfo.setIdTypeEndDate(null);
            }
            // 员工信息临时表
            FCPerInfoTemp fcPerTempInfo = new FCPerInfoTemp();
            fcPerTempInfo.setGrpNo(grpNo);// 企业号
            fcPerTempInfo.setName(name);// 姓名
            fcPerTempInfo.setDepartment(fcPerInfo.getDepartment());// 部门
            fcPerTempInfo.setSex(fcPerInfo.getSex());
            fcPerTempInfo.setBirthDay(fcPerInfo.getBirthDay());// 出生日期
            fcPerTempInfo.setMobilePhone(fcPerInfo.getMobilePhone());// 手机号
            fcPerTempInfo.setNativeplace(fcPerInfo.getNativeplace());//国籍
            fcPerTempInfo.setIdTypeEndDate(fcPerInfo.getIdTypeEndDate());//证件有效期
            fcPerTempInfo.setIDType(fcPerInfo.getIDType());// 证件类型
            fcPerTempInfo.setIDNo(fcPerInfo.getIDNo().toUpperCase());// 证件号
            fcPerTempInfo.setOccupationType(fcPerInfo.getOccupationType());// 职业类别
            fcPerTempInfo.setOccupationCode(fcPerInfo.getOccupationCode());// 职业编码
            fcPerTempInfo.setJoinMedProtect(fcPerInfo.getJoinMedProtect());// 有无医保
            fcPerTempInfo.setServiceTerm(fcPerInfo.getServiceTerm());//服务年限
            fcPerTempInfo.setRetirement(fcPerInfo.getRetirement());//是否退休
            fcPerTempInfo.setLevelCode(fcPerInfo.getLevelCode());//职级
            fcPerTempInfo.setOpenBank(fcPerInfo.getOpenBank());// 开户行
            fcPerTempInfo.setOpenAccount(fcPerInfo.getOpenAccount());// 开户账号
            fcPerTempInfo.setOperator(operator);// 操作员
            String perTempNo = maxNoService.createMaxNo("PerTempNo", "", 20);
            fcPerTempInfo.setPerTempNo(perTempNo);// 流水号
            fcPerTempInfo.setEnsureCode(ensureCode);// 福利编号
            fcPerTempInfo.setDefaultPlan(fcPerInfo.getDefaultPlan());// 默认计划编码
            fcPerTempInfo.setErrorMsg("");// 错误原因
            fcPerTempInfo.setImpotStatus("01");// 导入状态（01-导入成功；02-导入失败）
            fcPerTempInfo.setSubStaus("01");// 提交状态（01-未提交；02-已提交）
            fcPerTempInfo.setRelationship(fcPerInfo.getRelationship());
            //加两个字段
            //企业为员工缴费
            fcPerTempInfo.setStaffGrpPrem(fcPerInfo.getStaffGrpPrem());
            //企业为家属缴费
            fcPerTempInfo.setFamilyGrpPrem(fcPerInfo.getFamilyGrpPrem());
            fcPerTempInfo = (FCPerInfoTemp) CommonUtil.initObject(
                    fcPerTempInfo, "INSERT");
            perInfoTempList.add(fcPerTempInfo);
        } catch (Exception e) {
            log.info("添加员工失败：" + errorMsg, e);
        }
        if (!errorMsg.equals("")) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", errorMsg);
            return JSON.toJSONString(resultMap);
        }
        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if (ConstantUtil.EnsureState_1.equals(fcEnsure.getEnsureState())) {
                List<String> fcOrderList = fcOrderMapper.selectByEnsureCode(ensureCode);
                if (fcOrderList.contains("02")) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "该福利下存在已提交至核心订单，不能添加");
                } else {
                    fcPerInfoTempMapper.insert(perInfoTempList);
                    // 同步人员信息
                    // 获取需要同步的人数
                    List<FCPerInfoTemp> needSyncNum = getNeedSyncNum(ensureCode);
                    if (needSyncNum.size() > 0) {
                        fcEnsure.setOperator(globalInput.getUserNo());
                        fcEnsure = (FCEnsure) CommonUtil.initObject(fcEnsure, "UPDATE");
                        fcEnsureMapper.updateByPrimaryKey(fcEnsure);
                        log.info("福利状态更新完成。。。");
                    }
                    if (StringUtils.isNotBlank(newFlag) && "1".equals(newFlag)) {
                        Map<String, String> map = new HashMap<>();
                        map.put("grpNo", grpNo);
                        map.put("ensureCode", ensureCode);
                        asyncService.insertNewUser(globalInput, map);
                    }
                    log.info("员工添加成功");
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", "员工添加成功");
                }
            } else {
                fcPerInfoTempMapper.insert(perInfoTempList);
                if (StringUtils.isNotBlank(newFlag) && "1".equals(newFlag)) {
                    Map<String, String> map = new HashMap<>();
                    map.put("grpNo", grpNo);
                    map.put("ensureCode", ensureCode);
                    asyncService.insertNewUser(globalInput, map);
                }
                log.info("员工添加成功");
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "员工添加成功");
            }
        } catch (Exception e) {
            log.info("员工添加失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "员工添加失败");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }


    public List<FCPerInfo> isExitPerInfo(String idNo) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("IDNo", idNo);
        List<FCPerInfo> list = fcPerInfoMapper.isExitPerInfo(params);
        if (list.size() > 0) {
            return list;
        } else {
            return null;
        }
    }

    /**
     * 处理excel业务数据
     *
     * @param wb
     */
    @Transactional
    public boolean dealPlanExcel(String token, Workbook wb, String ensureCode) {
        planMessage = "";
        bool = true;
        try {
            GlobalInput globalInput = userService.getSession(token);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            String operator = globalInput.getUserNo();
            Sheet sheet = wb.getSheetAt(0);
            // 获取最后一行的列数 add by wudezhong
            int physicalNumberOfCells = sheet.getRow(sheet.getLastRowNum())
                    .getPhysicalNumberOfCells();
            if (physicalNumberOfCells != 67) {
                planMessage += "模板有误，请您重新下载！/";
                return false;
            }
            Row row = null;
            int errorRow = 0;
            fcEnsurePlanList.clear();
            fcPlanRiskList.clear();
            fcPlanRiskDutyList.clear();
            //声明存放计划编码的集合
            List<Object> PlanCodelist = new ArrayList<>();
            //声明存放计划名称的集合
            List<Object> PlanNamelist = new ArrayList<>();
            //声明一个计划对象的集合
            List<String> PlanObjectlist = new ArrayList<>();
            //声明一个的所录险种编号的集合
            List<String> riskCodeList = new ArrayList<>();
            //获取停售的险种集合
            List<String> stopSaleRiskList = fdRiskInfoMapper.selectStopSaleRiskCode();
            // 第4行为数据的起始行
            for (int i = 4; i < sheet.getLastRowNum(); i++) {
                errorRow = i + 1;
                row = sheet.getRow(i);
                if (ExcelUtil.isRowEmpty(row, 1)) {
                    log.info("第" + (i + 1) + "行是无效数据。"
                            + ExcelUtil.getCellValue(row.getCell(0)));
                    continue;
                }
                if (ExcelUtil.isCellEmpty(row.getCell(0))) {
                    planMessage += "第" + (i + 1) + "行数据异常,序号不能为空！/";
                } else {
                    String seque = ExcelUtil.getCellValue(row.getCell(0));
                    //判断序号是否为数字
                    if (!CommonUtil.isNumeric(seque)) {
                        planMessage += "第" + (i + 1) + "行数据异常,序号只能为正整数！/";
                    } else {
                        Double mainWastage = Double.parseDouble(ExcelUtil.getCellValue(row.getCell(0)));
                        if (mainWastage.intValue() - mainWastage != 0) {// 判断是否符合取整条件
                            planMessage += "第" + (i + 1) + "行数据异常,序号只能为正整数！/";
                        }
                    }
                }
                if (PlanCodelist.contains(ExcelUtil.getCellValue(row.getCell(0)))) {
                    int index = PlanCodelist.indexOf(ExcelUtil.getCellValue(row.getCell(0)));
                    planMessage += "第" + (i + 1) + "行 ，与第" + (index + 5) + "行数据 序号重复！/";
                }
                if (ExcelUtil.isCellEmpty(row.getCell(1))) {
                    planMessage += "第" + (i + 1) + "行数据异常,保险计划名称不能为空！/";
                } else {
                    String planName = ExcelUtil.getCellValue(row.getCell(1));
                    if (planName.length() > 30) {
                        planMessage += "第" + (i + 1) + "行数据异常,保险计划名称长度不能超过30个字符！/";
                    }
                }
                if (PlanNamelist.contains(ExcelUtil.getCellValue(row.getCell(1)))) {
                    int index = PlanNamelist.indexOf(ExcelUtil.getCellValue(row.getCell(1)));
                    planMessage += "第" + (i + 1) + "行 ，与第" + (index + 5) + "行数据 计划名称重复！/";
                }
                if (ExcelUtil.isCellEmpty(row.getCell(2))) {
                    planMessage += "第" + (i + 1) + "行数据异常,计划对象不能为空！/";
                }
                if (ExcelUtil.isCellEmpty(row.getCell(3))) {
                    planMessage += "第" + (i + 1) + "行数据异常,计划重点不能为空！/";
                }
                if (!ExcelUtil.isCellEmpty(row.getCell(1))) {
                    PlanNamelist.add(ExcelUtil.getCellValue(row.getCell(1)));
                }
                FCEnsurePlan plan = new FCEnsurePlan();
                String planCode = "";
                if (ExcelUtil.isCellEmpty(row.getCell(0))) {
                    plan.setPlanCode(planCode);
                } else {
                    String cellValue = ExcelUtil.getCellValue(row.getCell(0));
                    //判断序号是否为数字
                    if (!CommonUtil.isNumeric(cellValue)) {
                        plan.setPlanCode(planCode);
                    } else {
                        int value = new Double(Double.valueOf(cellValue)).intValue();
                        planCode = String.valueOf(value);
                        plan.setPlanCode(planCode);
                    }
                }
                // 保障编码
                plan.setEnsureCode(ensureCode);
                plan.setPlanName(ExcelUtil.getCellValue(row.getCell(1)));
                plan.setPlanObject(CommonUtil.mappingPlanObject(ExcelUtil.getCellValue(row.getCell(2))));
                // 计划重点
                plan.setPlanKey(ExcelUtil.getCellValue(row.getCell(3)));
                plan.setInsuredNumber(0);
                plan.setOperator(operator);
                plan = CommonUtil.initObject(plan, "INSERT");
                // 存放变量
                int count = 0;
                // 处理险种
                planPrem = 0.0;
                int startColumn = 0;
                String riskCode = "";
                // 1、团体定期寿险（12020）
                startColumn = 4;
                riskCode = "12020";
                if (!ExcelUtil.isCellEmpty(row.getCell(startColumn)) || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
                    bool = true;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber(ExcelUtil.getCellValue(row.getCell(startColumn)))
                            || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 1)) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，团体定期寿险保额不能为空，保额最低为1万，且保留到小数点后两位。（只能为数字）/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，团体定期寿险保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                    }
                    if (bool && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，团体定期寿险已下架，暂不支持计划定制。/";
                    }
                    if (bool) {
                        dealRow(row, ensureCode, planCode, riskCode, "GD0017",
                                operator, 4, "N");
                        riskCodeList.add(riskCode);
                    }
                } else {
                    count++;
                }
                // 2、门诊急诊团体医疗保险（17030）
                startColumn = 6;
                riskCode = "17030";
                if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 3))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 4))) {
                    bool = true;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 2)) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，门诊急诊团体医疗保险保额不能为空，必须为0.2～3之间的数字，且保留到小数点后两位。/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，门诊急诊团体医疗保险保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，门诊急诊团体医疗保险免赔额不能为空。/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !EnsureMakeService.isGetLimitType(ExcelUtil.getCellValue(row.getCell(startColumn)), 1)) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，门诊急诊团体医疗保险免赔额属性不能为空，且只能为模板下拉框指定的值。/";
                    } else {
                        //门诊急诊团体医疗保险（17030）险种的免赔额属性判断，免赔额的数值
                        if (ExcelUtil.getCellValue(row.getCell(startColumn)).equals("按次免赔")
                                && !ExcelUtil.isCellEmpty(row.getCell(startColumn - 1))
                                && isNumber0(ExcelUtil.getCellValue(row.getCell(startColumn - 1)))
                                && !EnsureMakeService.isDeductibles(ExcelUtil.getCellValue(row.getCell(startColumn - 1)), 1)) {
                            planMessage += "第" + errorRow + "行，免赔额输入错误，请重新输入。/";
                        }
                        if (ExcelUtil.getCellValue(row.getCell(startColumn)).equals("按年免赔")
                                && !ExcelUtil.getCellValue(row.getCell(startColumn - 1)).matches("^\\d+$")) {
                            planMessage += "第" + errorRow + "行，免赔额输入错误，请重新输入。/";
                        }
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber1(ExcelUtil.getCellValue(row.getCell(startColumn)))
                            || !isReimbursementRatio(ExcelUtil.getCellValue(row.getCell(startColumn)), 1)) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，门诊急诊团体医疗保险赔付比例不能为空,且只能为模板下拉框指定的值。/";
                    }
                    if (bool && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，门诊急诊团体医疗保险已下架，暂不支持计划定制。/";
                    }
                    if (bool) {
                        dealRow(row, ensureCode, planCode, riskCode, "GD0031",
                                operator, 6, "Y");
                        riskCodeList.add(riskCode);
                    }
                } else {
                    count++;
                }
                // 3、团体意外伤害保险（15030）
                startColumn = 11;
                riskCode = "15030";
                if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 1))) {
                    bool = true;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 3)) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，团体意外伤害保险保额不能为空，最低为1万且只能以千元递增，保留到小数点后两位。（只能为数字）/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，团体意外伤害保险保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                    }
                    if (bool && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，团体意外伤害保险保险已下架，暂不支持计划定制。/";
                    }
                    if (bool) {
                        dealRow(row, ensureCode, planCode, riskCode, "GD0037",
                                operator, 11, "N");
                        riskCodeList.add(riskCode);
                    }
                } else {
                    count++;
                }

                // 4、意外伤害团体医疗保险（15040）
                startColumn = 13;
                riskCode = "15040";
                if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 3))) {
                    bool = true;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 4)) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，意外伤害团体医疗保险保额不能为空，最低为0.5万且只能以千元递增，保留到小数点后两位。（只能为数字）/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，意外伤害团体医疗保险保费不能为空，且必须是大于等于0的数字以及仅支持小数点后两位。/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn)) || !isNumber0(ExcelUtil.getCellValue(row.getCell(startColumn))) || !isDeductibles(ExcelUtil.getCellValue(row.getCell(startColumn)), 2)) {
                        bool = false;
                        System.out.println("=====================================");
                        System.out.println(errorRow + "====" + startColumn + "==" + ExcelUtil.getCellValue(row.getCell(startColumn)));
                        System.out.println(ExcelUtil.isCellEmpty(row.getCell(startColumn)));
                        System.out.println(isNumber0(ExcelUtil.getCellValue(row.getCell(startColumn))));
                        System.out.println(isDeductibles(ExcelUtil.getCellValue(row.getCell(startColumn)), 2));
                        planMessage += "第" + errorRow
                                + "行，意外伤害团体医疗保险免赔额不能为空,且只能为模板下拉框指定的值。/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber1(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isReimbursementRatio(ExcelUtil.getCellValue(row.getCell(startColumn)), 1)) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，意外伤害团体医疗保险赔付比例不能为空,且只能为模板下拉框指定的值。/";
                    }
                    if (bool && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，意外伤害团体医疗保险已下架，暂不支持计划定制。/";
                    }
                    if (bool) {
                        dealRow(row, ensureCode, planCode, riskCode, "GD0028", operator, 13, "Y");
                        riskCodeList.add(riskCode);
                    }
                } else {
                    count++;
                }

                // 5、 意外伤害住院津贴团体医疗保险（15060）
                startColumn = 17;
                riskCode = "15060";
                if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 3))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 4))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 5))) {
                    bool = true;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 6)) {
                        bool = false;
                        planMessage += "第"
                                + errorRow
                                + "行，意外伤害住院津贴团体医疗保险，意外伤害住院津贴保险责任为必录责任保额不能为空，最低为10且只能以10元递增，保留到小数点后两位。（只能为数字）/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第"
                                + errorRow
                                + "行，意外伤害住院津贴团体医疗保险，意外伤害住院津贴保险责任为必录责任保费不能为空，且必须是大于等于0的数字以及仅支持小数点后两位。/";
                    }
                    startColumn++;
                    //add by zch 2020/11/17
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isMaxPayDay(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第"
                                + errorRow
                                + "行，意外伤害住院津贴团体医疗保险，意外伤害住院津贴保险责任为必录责任最大赔付天数不能为空，且必须是1-366之间的整数。/";
                    }
                    startColumn++;
                    // 可选责任
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(startColumn + 1))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(startColumn + 2))) {
                        if (!"".equals(ExcelUtil.getCellValue(row.getCell(startColumn))) && !"".equals(ExcelUtil.getCellValue(row.getCell(17)))) {
                            if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                    || !isNumber(ExcelUtil.getCellValue(row
                                    .getCell(startColumn)))
                                    //重症监护日额津贴保险金不得超过意外伤害住院日额津贴保险金的5倍
                                    || (Double.valueOf(ExcelUtil.getCellValue(row.getCell(startColumn))) > Double.valueOf(ExcelUtil.getCellValue(row.getCell(17))) * 5)) {
                                bool = false;
                                planMessage += "第"
                                        + errorRow
                                        + "行，意外伤害住院津贴团体医疗保险，意外伤害重症住院津贴保险责任保额不能为空，且必须是大于0的数字且不得超过意外伤害住院日额津贴保险金的5倍以及仅支持小数点后两位。/";
                            }
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，意外伤害住院津贴团体医疗保险，意外伤害重症住院津贴保险责任保费不能为空，且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                        //add by zch 2020/11/17
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isMaxPayDay(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，意外伤害住院津贴团体医疗保险，意外伤害重症住院津贴保险责任最大赔付天数不能为空，且必须是1-366之间的整数。/";
                        }
                    }
                    if (bool && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，意外伤害住院津贴团体医疗保险已下架，暂不支持计划定制。/";
                    }
                    if (bool) {
                        dealRow_15060(row, ensureCode, planCode, riskCode,
                                operator, 17);
                        riskCodeList.add(riskCode);
                    }
                } else {
                    count++;
                }

                // 6、团体重大疾病保险（16040）
                startColumn = 23;
                riskCode = "16040";
                if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 1))) {
                    bool = true;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 5)) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，团体重大疾病保险保额不能为空,且必须是1~100的数字以及仅支持小数点后两位。/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，团体重大疾病保险保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                    }
                    if (bool && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，团体重大疾病保险已下架，暂不支持计划定制。/";
                    }
                    if (bool) {
                        dealRow(row, ensureCode, planCode, riskCode, "GD0060",
                                operator, 23, "N");
                        riskCodeList.add(riskCode);
                    }
                } else {
                    count++;
                }
                // 7、琴逸团体重大疾病保险
                startColumn = 25;
                riskCode = "16490";
                if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 1))) {
                    bool = true;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 5)) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，琴逸团体重大疾病保险保额不能为空,且必须是1~100的数字以及仅支持小数点后两位。/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，琴逸团体重大疾病保险保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                    }
                    if (bool == true && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，琴逸团体重大疾病保险已下架，暂不支持计划定制。/";
                    }
                    if (bool == true) {
                        dealRow(row, ensureCode, planCode, riskCode, "ID6490",
                                operator, 25, "N");
                        riskCodeList.add(riskCode);
                    }
                } else {
                    count++;
                }
                // 7、住院津贴团体医疗保险（17020）
                startColumn = 27;
                riskCode = "17020";
                if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 3))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 4))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 5))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 6))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 7))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 8))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 9))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 10))) {
                    bool = true;
                    // 必录责任
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 6)) {
                        bool = false;
                        planMessage += "第"
                                + errorRow
                                + "行，住院津贴团体医疗保险，一般住院日额津贴保险责任为必录责任保额不能为空，最低为10且只能以10元递增，保留到小数点后两位。（只能是数字）/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第"
                                + errorRow
                                + "行，住院津贴团体医疗保险，一般住院日额津贴保险责任为必录责任保费不能为空且必须是大于等于0的数字以及仅支持小数点后两位。/";
                    }
                    //add by zch 2020/11/17
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isMaxPayDay(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第"
                                + errorRow
                                + "行，住院津贴团体医疗保险，一般住院日额津贴保险责任为必录责任最大赔付天数不能为空且必须是1-366之间的整数。/";
                    }
                    // 可选责任1
                    startColumn = 30;
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(startColumn + 1))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(startColumn + 2))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 6)) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，癌症住院日额津贴保险金责任保额不能为空,最低为10且只能以10元递增，保留到小数点后两位。（只能是数字）/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，癌症住院日额津贴保险金责任保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                        //add by zch 2020/11/17
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isMaxPayDay(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，癌症住院日额津贴保险金责任最大赔付天数不能为空且必须是1-366之间的整数。/";
                        }
                    }
                    // 可选责任2
                    startColumn = 33;
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(startColumn + 1))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(startColumn + 2))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 6)) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，重症监护日额津贴保险金责任保额不能为空,最低为10且只能以10元递增，保留到小数点后两位。（只能是数字）/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，重症监护日额津贴保险金责任保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                        //add by zch 2020/11/17
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isMaxPayDay(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，重症监护日额津贴保险金责任最大赔付天数不能为空且必须是1-366之间的整数。/";
                        }
                    }
                    // 可选责任3
                    startColumn = 36;
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(startColumn + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 4)) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，手术医疗津贴保险金责任保额不能为空,且必须是最低为0.5万的数字且以千元递增以及仅支持小数点后两位。/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，手术医疗津贴保险金责任保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                        //add bu zch 2020/11/17
                       /* startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isMaxPayDay(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，手术医疗津贴保险金责任最大赔付天数不能为空且必须是1-366之间的整数。/";
                        }*/
                    }
                    if (bool == true && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，住院津贴团体医疗保险已下架，暂不支持计划定制。/";
                    }
                    if (bool == true) {
                        dealRow_17020(row, ensureCode, planCode, riskCode,
                                operator, 27);
                        riskCodeList.add(riskCode);
                    }
                } else {
                    count++;
                }

                // 8、住院团体医疗保险（17010）
                startColumn = 38;
                riskCode = "17010";
                if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 3))) {
                    bool = true;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 2)) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，住院团体医疗保险保额不能为空,必须为0.2～3之间的数字，且保留到小数点后两位。/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，住院团体医疗保险保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isDeductibles(ExcelUtil.getCellValue(row.getCell(startColumn)), 3)) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，住院团体医疗保险免赔额不能为空,且只能为模板下拉框指定的值。/";
                    }
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber1(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isReimbursementRatio(ExcelUtil.getCellValue(row.getCell(startColumn)), 1)) {
                        bool = false;
                        planMessage += "第" + errorRow
                                + "行，住院团体医疗保险赔付比例不能为空,且只能为模板下拉框指定的值。/";
                    }
                    if (bool == true && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，住院团体医疗保险已下架，暂不支持计划定制。/";
                    }
                    if (bool == true) {
                        dealRow(row, ensureCode, planCode, riskCode, "GD0018",
                                operator, 38, "Y");
                        riskCodeList.add(riskCode);
                    }
                } else {
                    count++;
                }

                // 9、尊享团体补充医疗保险（17050）
                startColumn = 42;
                riskCode = "17050";
                if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 3))) {
                    bool = true;
                    // 一般医疗保险金GD0070

                    // 保额（元）
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 11)) {
                        bool = false;
                        planMessage += "第"
                                + errorRow
                                + "行，尊享团体补充医疗保险，一般医疗保险金责任为必录责任保额不能为空，且必须是模板下拉框中指定的数字。/";
                    }
                    // 保费（元）
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第"
                                + errorRow
                                + "行，尊享团体补充医疗保险，一般医疗保险金责任为必录责任保费不能为空且必须是大于等于0的数字以及仅支持小数点后两位。/";
                    }
                    // 免赔额
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))) {
                        bool = false;
                        planMessage += "第"
                                + errorRow
                                + "行，尊享团体补充医疗保险，一般医疗保险金责任为必录责任免赔额不能为空且必须是大于等于0的数字以及仅支持小数点后两位。/";
                    }
                    // 赔付比例(%)
                    startColumn++;
                    if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !isNumber1(ExcelUtil.getCellValue(row
                            .getCell(startColumn)))
                            || !isReimbursementRatio(ExcelUtil.getCellValue(row.getCell(startColumn)), 2)) {
                        bool = false;
                        planMessage += "第"
                                + errorRow
                                + "行，尊享团体补充医疗保险，一般医疗保险金责任为必录责任赔付比例不能为空，且只能为模板下拉框指定的值。/";
                    }
                    // 恶性肿瘤医疗保险金GD0071---应横琴核心要求去除免赔额和赔付比例---->应横琴要求保额只录一次，保费为0
//						startColumn = 38;
//						if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
//								|| !isNumber(ExcelUtil.getCellValue(row
//										.getCell(startColumn)))) {
//							planMessage = "第"
//									+ errorRow
//									+ "行，尊享团体补充医疗保险，恶性肿瘤医疗保险金责任为必录责任保费不能为空且必须是大于0的数字以及仅支持小数点后两位。";
//							return false;
//						}
//						startColumn++;
//						if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
//								|| !isNumber(ExcelUtil.getCellValue(row
//										.getCell(startColumn)))) {
//							planMessage = "第"
//									+ errorRow
//									+ "行，尊享团体补充医疗保险，恶性肿瘤医疗保险金责任为必录责任保费不能为空且必须是大于0的数字以及仅支持小数点后两位。";
//							return false;
//						}
//						 // 免赔额
//						 startColumn++;
//						 if(ExcelUtil.isCellEmpty(row.getCell(startColumn))||!isNumber(ExcelUtil.getCellValue(row.getCell(startColumn)))){
//						 planMessage =
//						 "第"+errorRow+"行，尊享团体补充医疗保险，恶性肿瘤医疗保险金责任为必录责任免赔额不能为空且必须是大于等于0的数字以及仅支持小数点后两位。";
//						 return false;
//						 }
//						 // 赔付比例(%)
//						 startColumn++;
//						 if(ExcelUtil.isCellEmpty(row.getCell(startColumn))||!isNumber(ExcelUtil.getCellValue(row.getCell(startColumn)))){
//						 planMessage =
//						 "第"+errorRow+"行，尊享团体补充医疗保险，恶性肿瘤医疗保险金责任为必录责任赔付比例不能为空且必须是大于0的数字以及仅支持小数点后两位。";
//						 return false;
//						 }
                    if (bool == true && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，尊享团体补充医疗保险已下架，暂不支持计划定制。/";
                    }
                    if (bool == true) {
                        dealRow_17050(row, ensureCode, planCode, riskCode,
                                operator, 42);
                        riskCodeList.add(riskCode);
                    }
                } else {
                    count++;
                }

                // 10、综合交通团体意外伤害保险（15070）
                startColumn = 46;///
                riskCode = "15070";
                if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 3))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 4))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 5))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 6))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 7))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 8))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 9))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 10))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 11))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 12))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 13))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 14))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 15))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 16))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 17))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 18))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(startColumn + 19))) {
                    bool = true;
                    // 1、公路公共交通工具保险金GD0050
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn)) || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 7)) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，公路公共交通工具保险金责任为必录责任保额不能为空，且必须是1万~200万的数字以及仅支持小数点后两位。/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，公路公共交通工具保险金责任为必录责任保费不能为空且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    startColumn = 48;
                    // 2、轨道交通工具保险金GD0051
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn)) || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 8)) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，轨道交通工具保险金责任为必录责任保额不能为空，且必须是1万~300万的数字以及仅支持小数点后两位。/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，轨道交通工具保险金责任为必录责任保费不能为空且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    startColumn = 50;
                    // 3、水路公共交通工具保险金GD0052
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn)) || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 7)) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，水路公共交通工具保险金责任为必录责任保额不能为空，且必须是1万~200万的数字以及仅支持小数点后两位。/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，水路公共交通工具保险金责任为必录责任保费不能为空且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    startColumn = 52;
                    // 4、民航班机保险金
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn)) || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 9)) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，民航班机保险金责任为必录责任保额不能为空，且必须是1万~500万的数字以及仅支持小数点后两位。/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，民航班机保险金责任为必录责任保费不能为空且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    startColumn = 54;
                    // 5、私家车或公务车保险金
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn)) || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || !isAmnt(ExcelUtil.getCellValue(row.getCell(startColumn)), 5)) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，私家车或公务车保险金责任为必录责任保额不能为空，且必须是1万~100万的数字以及仅支持小数点后两位。/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，私家车或公务车保险金责任为必录责任保费不能为空且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    /************************ 可选责任 ***********************************************/
                    // 6、公路公共意外伤害医疗保险金GD0055
                    startColumn = 56;
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))
                    ) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || ExcelUtil.isCellEmpty(row.getCell(46))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，公路公共意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字以及仅支持小数点后两位,选择该责任请先录入公路公共交通工具保险金GD0050责任。/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，公路公共意外伤害医疗保险金责任保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    // 7、轨道交通意外伤害医疗保险金GD0056
                    startColumn = 58;
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(startColumn + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || ExcelUtil.isCellEmpty(row.getCell(48))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，轨道交通意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字以及仅支持小数点后两位,选择该责任请先录入轨道交通工具保险金GD0051责任。/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，轨道交通意外伤害医疗保险金责任保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    // 8、水路公共交通意外伤害医疗保险金GD0057
                    startColumn = 60;
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(startColumn + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || ExcelUtil.isCellEmpty(row.getCell(50))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，水路公共交通意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字以及仅支持小数点后两位,选择该责任请先录入水路公共交通工具保险金GD0052责任。/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，水路公共交通意外伤害医疗保险金责任保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    // 9、民航班机意外伤害医疗保险金GD0058
                    startColumn = 62;
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(startColumn + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || ExcelUtil.isCellEmpty(row.getCell(52))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，民航班机意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字以及仅支持小数点后两位,选择该责任请先录入民航班机保险金GD0053责任。/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，民航班机意外伤害医疗保险金责任保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    // 10、私家车或公务车意外伤害医疗保险金GD0059
                    startColumn = 64;
                    if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(startColumn + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))
                                || ExcelUtil.isCellEmpty(row.getCell(54))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，私家车或公务车意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字以及仅支持小数点后两位,选择该责任请先录入私家车或公务车保险金GD0054责任。/";
                        }
                        startColumn++;
                        if (ExcelUtil.isCellEmpty(row.getCell(startColumn))
                                || !isNumber0(ExcelUtil.getCellValue(row
                                .getCell(startColumn)))) {
                            bool = false;
                            planMessage += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，私家车或公务车意外伤害医疗保险金责任保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    if (bool == true && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "第" + errorRow + "行，综合交通团体意外伤害保险已下架，暂不支持计划定制。/";
                    }
                    if (bool == true) {
                        deal_15070(row, ensureCode, planCode, riskCode,
                                operator, 46);
                        riskCodeList.add(riskCode);
                    }
                } else {
                    count++;
                }
                // 是否录入险种
                if (count == 11) {
                    planMessage += "第" + (i + 1) + "行,请至少录入一个险种!/";
                }
                // 保留两位小数
                BigDecimal b = new BigDecimal(planPrem);
                double totalplanPrem = b.setScale(2,
                        BigDecimal.ROUND_HALF_UP).doubleValue();
                plan.setTotalPrem(totalplanPrem);
                plan.setPlanState(PlanStateEnum.MAKEDONE.getCode());
                fcEnsurePlanList.add(plan);
                PlanObjectlist.add(plan.getPlanObject());
                //将此计划的序号加入到计划编码的集合中,以此判断是否有重复的计划编码
                PlanCodelist.add(ExcelUtil.getCellValue(row.getCell(0)));
            }
            //判断是一年期还是极短期
            String policyEndDate = fcEnsure.getPolicyEndDate();
            String cvaliDate = fcEnsure.getCvaliDate();
            try {
                Long days = DateTimeUtil.getDistanceDays(policyEndDate, cvaliDate);
                if (days != 364 && days != 365) {
                    if (riskCodeList.contains("12020") || riskCodeList.contains("17030")
                            || riskCodeList.contains("16040") || riskCodeList.contains("16490")
                            || riskCodeList.contains("17020") || riskCodeList.contains("17010")
                            || riskCodeList.contains("17050")) {
                        planMessage += "极短期险种导入有误";
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (!PlanObjectlist.contains("1") && !PlanObjectlist.contains("3")) {
                planMessage += "请至少录入一个计划!/";
            } else {
                if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                    if (PlanObjectlist.contains("3")) {
                        planMessage += "企事业单位投保，计划对象只可为员工以及家属。";
                    }
                } else if ("1".equals(fcEnsure.getEnsureType())) {
                    if (PlanObjectlist.contains("1")) {
                        planMessage += "在校学生投保，计划对象只可为学生。";
                    }
                }
            }
            try {
                if (!"".equals(planMessage)) {
                    return false;
                } else {
                    // 先删除已有数据 再 插入新数据
                    fcEnsurePlanMapper.deleteByEnsureCode(ensureCode);
                    fcPlanRiskMapper.deleteByEnsureCode(ensureCode);
                    fcPlanRiskDutyMapper.deleteByEnsureCode(ensureCode);
                    fcEnsurePlanMapper.insertList(fcEnsurePlanList);
                    fcPlanRiskMapper.insertList(fcPlanRiskList);
                    fcPlanRiskDutyMapper.insertList(fcPlanRiskDutyList);
                }
            } catch (Exception e) {
                log.info("导入计划失败：", e);
                throw new RuntimeException();
            }
            /*
             * resultMap.put("success", true); resultMap.put("code", "200");
             * resultMap.put("message", "计划导入成功");
             */
            planMessage = "计划导入成功";
            return true;
        } catch (Exception e) {
            /*
             * log.info("计划导入失败：" + e.getMessage()); resultMap.put("success",
             * false); resultMap.put("code", "500"); resultMap.put("message",
             * "计划导入失败");
             */
            log.info("计划导入失败：", e);
            planMessage = "计划导入失败";
            return false;
        }
        // return resultMap;
    }

    /**
     * 以险种为单位处理数据
     *
     * @param row
     * @param ensureCode
     * @param planCode
     * @param riskCode
     * @param operator
     * @param startCell
     * @param isMedical
     * @return
     */
    private boolean dealRow(Row row, String ensureCode, String planCode,
                            String riskCode, String dutyCode, String operator, int startCell,
                            String isMedical) throws Exception {
        FCPlanRisk risk = new FCPlanRisk();
        risk.setEnsureCode(ensureCode);
        risk.setPlanCode(planCode);
        risk.setRiskCode(riskCode);
        risk.setOperator(operator);
        risk = (FCPlanRisk) CommonUtil.initObject(risk, "INSERT");
        fcPlanRiskList.add(risk);
        FCPlanRiskDuty duty = new FCPlanRiskDuty();
        duty.setDutyCode(dutyCode);
        duty.setEnsureCode(ensureCode);
        duty.setRiskCode(riskCode);
        duty.setPlanCode(planCode);
//      应横琴需求，所有保额皆为万元   update  by  wudezhong
//		if (dutyCode.equals("GD0060")) {// 团体重大疾病保险,团体重大疾病保险责任，保额单位为万元
        duty.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell))), 10000.0));
        duty.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell + 1))));
        planPrem += duty.getPrem();
        if ("Y".equals(isMedical)) {
            duty.setGetLimit(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 2))));
            String GetLimitType;
            if ("17030".equals(riskCode)) {
                String ss = ExcelUtil.getCellValue(row.getCell(startCell + 3));
                GetLimitType = ExcelUtil.getCellValue(row.getCell(startCell + 3)).equals("按次免赔") ? "1" : "2";
                duty.setGetLimitType(GetLimitType);
                duty.setGetRatio(
                        CommonUtil.div(Double.parseDouble(ExcelUtil.getCellValue(row.getCell(startCell + 4))), (double) 100, 2));
            } else if ("15040".equals(riskCode) || "17010".equals(riskCode)) {
                GetLimitType = "1";
                duty.setGetLimitType(GetLimitType);
                duty.setGetRatio(
                        CommonUtil.div(Double.parseDouble(ExcelUtil.getCellValue(row.getCell(startCell + 3))), (double) 100, 2));
            } else {
                duty.setGetRatio(
                        CommonUtil.div(Double.parseDouble(ExcelUtil.getCellValue(row.getCell(startCell + 3))), (double) 100, 2));
            }
        }
        duty.setOperator(operator);
        duty = (FCPlanRiskDuty) CommonUtil.initObject(duty, "INSERT");
        fcPlanRiskDutyList.add(duty);
        return true;
    }

    /**
     * 处理险种-住院津贴团体医疗保险（17020）
     *
     * @param row
     * @param ensureCode
     * @param planCode
     * @param riskCode
     * @param operator
     * @param startCell
     * @return
     */
    private boolean dealRow_17020(Row row, String ensureCode, String planCode,
                                  String riskCode, String operator, int startCell) throws Exception {
        FCPlanRisk risk = new FCPlanRisk();
        risk.setEnsureCode(ensureCode);
        risk.setPlanCode(planCode);
        risk.setRiskCode(riskCode);
        // 不存RiskName
        risk.setOperator(operator);
        risk = (FCPlanRisk) CommonUtil.initObject(risk, "INSERT");
        fcPlanRiskList.add(risk);
        FCPlanRiskDuty duty1 = new FCPlanRiskDuty();
        // 一般住院日额津贴保险责任GD0032
        duty1.setDutyCode("GD0032");
        duty1.setEnsureCode(ensureCode);
        duty1.setRiskCode(riskCode);
        duty1.setPlanCode(planCode);
        duty1.setAmnt(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell))));
        duty1.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell + 1))));
        duty1.setMaxGetDay(new BigDecimal(ExcelUtil.getCellValue(row
                .getCell(startCell + 2))));
        planPrem += duty1.getPrem();
        duty1.setOperator(operator);
        duty1 = (FCPlanRiskDuty) CommonUtil.initObject(duty1, "INSERT");
        fcPlanRiskDutyList.add(duty1);
        int startColumn = 30;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                && !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {// 判断是否选择可选责任
            // 癌症住院日额津贴保险金 （可选责任）GD0033
            FCPlanRiskDuty duty2 = new FCPlanRiskDuty();
            duty2.setDutyCode("GD0033");
            duty2.setEnsureCode(ensureCode);
            duty2.setRiskCode(riskCode);
            duty2.setPlanCode(planCode);
            duty2.setAmnt(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 3))));
            duty2.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 4))));
            duty2.setMaxGetDay(new BigDecimal(ExcelUtil.getCellValue(row
                    .getCell(startCell + 5))));
            planPrem += duty2.getPrem();
            duty2.setOperator(operator);
            duty2 = (FCPlanRiskDuty) CommonUtil.initObject(duty2, "INSERT");
            fcPlanRiskDutyList.add(duty2);
        }
        startColumn = 33;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                && !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {// 判断是否选择可选责任
            // "重症监护日额津贴保险金 （可选责任）GD0034"
            FCPlanRiskDuty duty3 = new FCPlanRiskDuty();
            duty3.setDutyCode("GD0034");
            duty3.setEnsureCode(ensureCode);
            duty3.setRiskCode(riskCode);
            duty3.setPlanCode(planCode);
            duty3.setAmnt(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 6))));
            duty3.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 7))));
            duty3.setMaxGetDay(new BigDecimal(ExcelUtil.getCellValue(row
                    .getCell(startCell + 8))));
            planPrem += duty3.getPrem();
            duty3.setOperator(operator);
            duty3 = (FCPlanRiskDuty) CommonUtil.initObject(duty3, "INSERT");
            fcPlanRiskDutyList.add(duty3);
        }
        // 可选责任3
        startColumn = 36;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {// 判断是否选择可选责任
            // 手术医疗津贴保险金（可选责任）GD0035
            FCPlanRiskDuty duty4 = new FCPlanRiskDuty();
            duty4.setDutyCode("GD0035");
            duty4.setEnsureCode(ensureCode);
            duty4.setRiskCode(riskCode);
            duty4.setPlanCode(planCode);
            duty4.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 9))), 10000.0));
            duty4.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 10))));
            planPrem += duty4.getPrem();
            duty4.setOperator(operator);
            duty4 = (FCPlanRiskDuty) CommonUtil.initObject(duty4, "INSERT");
            fcPlanRiskDutyList.add(duty4);
        }
        return true;

    }

    /**
     * 处理险种-尊享团体补充医疗保险（17050）
     *
     * @param row
     * @param ensureCode
     * @param planCode
     * @param riskCode
     * @param operator
     * @param startCell
     * @return
     */
    private boolean dealRow_17050(Row row, String ensureCode, String planCode,
                                  String riskCode, String operator, int startCell) throws Exception {
        FCPlanRisk risk = new FCPlanRisk();
        risk.setEnsureCode(ensureCode);
        risk.setPlanCode(planCode);
        risk.setRiskCode(riskCode);
        // 不存RiskName
        risk.setOperator(operator);
        risk = CommonUtil.initObject(risk, "INSERT");
        fcPlanRiskList.add(risk);
        FCPlanRiskDuty duty1 = new FCPlanRiskDuty();
        // 一般医疗保险金GD0070
        duty1.setDutyCode("GD0070");
        duty1.setEnsureCode(ensureCode);
        duty1.setRiskCode(riskCode);
        duty1.setPlanCode(planCode);
        duty1.setAmnt(CommonUtil.mul(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell))), 10000.0), 0.5));
        duty1.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell + 1))));
        planPrem += duty1.getPrem();
        duty1.setGetLimit(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell + 2))));
        duty1.setGetLimitType("2");
        duty1.setGetRatio(CommonUtil.div(Double.parseDouble(ExcelUtil.getCellValue(row.getCell(startCell + 3))), (double) 100, 2));
        duty1.setOperator(operator);
        duty1 = CommonUtil.initObject(duty1, "INSERT");
        fcPlanRiskDutyList.add(duty1);
        // 恶性肿瘤医疗保险金GD0071
        FCPlanRiskDuty duty2 = new FCPlanRiskDuty();
        duty2.setEnsureCode(ensureCode);
        duty2.setRiskCode(riskCode);
        duty2.setPlanCode(planCode);
        duty2.setDutyCode("GD0071");
        duty2.setAmnt(CommonUtil.mul(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell))), 10000.0), 0.5));
        // -----应横琴核心要求，只获取第一列的责任的免赔额和赔付比例,保费置为0
        duty2.setPrem(0.0);
        duty2.setGetLimit(0.0);
        duty2.setGetLimitType("");
        duty2.setGetRatio(CommonUtil.div(Double.parseDouble(ExcelUtil.getCellValue(row.getCell(startCell + 3))), (double) 100, 2));
        duty2.setOperator(operator);
        duty2 = CommonUtil.initObject(duty2, "INSERT");
        fcPlanRiskDutyList.add(duty2);
        return true;

    }

    /**
     * 处理险种-意外伤害住院津贴团体医疗保险（15060）
     *
     * @param row
     * @param ensureCode
     * @param planCode
     * @param riskCode
     * @param operator
     * @param startCell
     * @return
     */
    private boolean dealRow_15060(Row row, String ensureCode, String planCode,
                                  String riskCode, String operator, int startCell) throws Exception {
        FCPlanRisk risk = new FCPlanRisk();
        risk.setEnsureCode(ensureCode);
        risk.setPlanCode(planCode);
        risk.setRiskCode(riskCode);
        // 不存RiskName
        risk.setOperator(operator);
        risk = (FCPlanRisk) CommonUtil.initObject(risk, "INSERT");
        fcPlanRiskList.add(risk);
        FCPlanRiskDuty duty1 = new FCPlanRiskDuty();
        // 意外伤害住院津贴保险责任GD0029
        duty1.setDutyCode("GD0029");
        duty1.setEnsureCode(ensureCode);
        duty1.setRiskCode(riskCode);
        duty1.setPlanCode(planCode);
        duty1.setAmnt(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell))));
        duty1.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell + 1))));
        duty1.setMaxGetDay(new BigDecimal(ExcelUtil.getCellValue(row
                .getCell(startCell + 2))));
        planPrem += duty1.getPrem();
        duty1.setOperator(operator);
        duty1 = (FCPlanRiskDuty) CommonUtil.initObject(duty1, "INSERT");
        fcPlanRiskDutyList.add(duty1);
        int startColumn = 20;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                && !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {// 判断是否选择可选责任
            // 意外伤害重症住院津贴保险责任 （可选责任）GD0030
            FCPlanRiskDuty duty2 = new FCPlanRiskDuty();
            duty2.setDutyCode("GD0030");
            duty2.setEnsureCode(ensureCode);
            duty2.setRiskCode(riskCode);
            duty2.setPlanCode(planCode);
            duty2.setAmnt(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 3))));
            duty2.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 4))));
            duty2.setMaxGetDay(new BigDecimal(ExcelUtil.getCellValue(row
                    .getCell(startCell + 5))));
            planPrem += duty2.getPrem();
            duty2.setOperator(operator);
            duty2 = (FCPlanRiskDuty) CommonUtil.initObject(duty2, "INSERT");
            fcPlanRiskDutyList.add(duty2);
        }
        return true;

    }

    /**
     * 处理险种
     *
     * @param row
     * @param ensureCode
     * @param planCode
     * @param riskCode
     * @param operator
     * @param startCell
     * @return
     */
    private boolean deal_15070(Row row, String ensureCode, String planCode, String riskCode, String operator, int startCell) throws Exception {
        FCPlanRisk risk = new FCPlanRisk();
        risk.setEnsureCode(ensureCode);
        risk.setPlanCode(planCode);
        risk.setRiskCode(riskCode);
        // 不存RiskName
        risk.setOperator(operator);
        risk = (FCPlanRisk) CommonUtil.initObject(risk, "INSERT");
        fcPlanRiskList.add(risk);
        FCPlanRiskDuty duty1 = new FCPlanRiskDuty();
        // 公路公共交通工具保险金GD0050
        int startColumn = 46;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn)) || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
            duty1.setDutyCode("GD0050");
            duty1.setEnsureCode(ensureCode);
            duty1.setRiskCode(riskCode);
            duty1.setPlanCode(planCode);
            duty1.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell))), 10000.0));
            duty1.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 1))));
            planPrem += duty1.getPrem();
            duty1.setOperator(operator);
            duty1 = (FCPlanRiskDuty) CommonUtil.initObject(duty1, "INSERT");
            fcPlanRiskDutyList.add(duty1);
        }
        // 轨道交通工具保险金GD0051
        startColumn = 48;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn)) || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
            FCPlanRiskDuty duty2 = new FCPlanRiskDuty();
            duty2.setDutyCode("GD0051");
            duty2.setEnsureCode(ensureCode);
            duty2.setRiskCode(riskCode);
            duty2.setPlanCode(planCode);
            duty2.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 2))), 10000.0));
            duty2.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 3))));
            planPrem += duty2.getPrem();
            duty2.setOperator(operator);
            duty2 = (FCPlanRiskDuty) CommonUtil.initObject(duty2, "INSERT");
            fcPlanRiskDutyList.add(duty2);
        }
        // 水路公共交通工具保险金GD0052
        startColumn = 50;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn)) || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
            FCPlanRiskDuty duty3 = new FCPlanRiskDuty();
            duty3.setDutyCode("GD0052");
            duty3.setEnsureCode(ensureCode);
            duty3.setRiskCode(riskCode);
            duty3.setPlanCode(planCode);
            duty3.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 4))), 10000.0));
            duty3.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 5))));
            planPrem += duty3.getPrem();
            duty3.setOperator(operator);
            duty3 = (FCPlanRiskDuty) CommonUtil.initObject(duty3, "INSERT");
            fcPlanRiskDutyList.add(duty3);
        }
        // 民航班机保险金GD0053
        startColumn = 52;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn)) || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
            FCPlanRiskDuty duty4 = new FCPlanRiskDuty();
            duty4.setDutyCode("GD0053");
            duty4.setEnsureCode(ensureCode);
            duty4.setRiskCode(riskCode);
            duty4.setPlanCode(planCode);
            duty4.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 6))), 10000.0));
            duty4.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 7))));
            planPrem += duty4.getPrem();
            duty4.setOperator(operator);
            duty4 = (FCPlanRiskDuty) CommonUtil.initObject(duty4, "INSERT");
            fcPlanRiskDutyList.add(duty4);
        }
        // 私家车或公务车保险金GD0054
        startColumn = 54;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn)) || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
            FCPlanRiskDuty duty5 = new FCPlanRiskDuty();
            duty5.setDutyCode("GD0054");
            duty5.setEnsureCode(ensureCode);
            duty5.setRiskCode(riskCode);
            duty5.setPlanCode(planCode);
            duty5.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 8))), 10000.0));
            duty5.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 9))));
            planPrem += duty5.getPrem();
            duty5.setOperator(operator);
            duty5 = (FCPlanRiskDuty) CommonUtil.initObject(duty5, "INSERT");
            fcPlanRiskDutyList.add(duty5);
        }
        // 公路公共意外伤害医疗保险金GD0055
        startColumn = 56;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
            FCPlanRiskDuty duty6 = new FCPlanRiskDuty();
            duty6.setDutyCode("GD0055");
            duty6.setEnsureCode(ensureCode);
            duty6.setRiskCode(riskCode);
            duty6.setPlanCode(planCode);
            duty6.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 10))), 10000.0));
            duty6.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 11))));
            duty6.setGetLimit(100.0);
            duty6.setGetLimitType("2");
            planPrem += duty6.getPrem();
            duty6.setOperator(operator);
            duty6 = (FCPlanRiskDuty) CommonUtil.initObject(duty6, "INSERT");
            fcPlanRiskDutyList.add(duty6);
        }
        // 轨道交通意外伤害医疗保险金GD0056
        startColumn = 58;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
            FCPlanRiskDuty duty7 = new FCPlanRiskDuty();
            duty7.setDutyCode("GD0056");
            duty7.setEnsureCode(ensureCode);
            duty7.setRiskCode(riskCode);
            duty7.setPlanCode(planCode);
            duty7.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 12))), 10000.0));
            duty7.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 13))));
            duty7.setGetLimit(100.0);
            duty7.setGetLimitType("2");
            planPrem += duty7.getPrem();
            duty7.setOperator(operator);
            duty7 = (FCPlanRiskDuty) CommonUtil.initObject(duty7, "INSERT");
            fcPlanRiskDutyList.add(duty7);
        }
        // 水路公共交通意外伤害医疗保险金GD0057
        startColumn = 60;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
            FCPlanRiskDuty duty8 = new FCPlanRiskDuty();
            duty8.setDutyCode("GD0057");
            duty8.setEnsureCode(ensureCode);
            duty8.setRiskCode(riskCode);
            duty8.setPlanCode(planCode);
            duty8.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 14))), 10000.0));
            duty8.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 15))));
            duty8.setGetLimit(100.0);
            duty8.setGetLimitType("2");
            planPrem += duty8.getPrem();
            duty8.setOperator(operator);
            duty8 = (FCPlanRiskDuty) CommonUtil.initObject(duty8, "INSERT");
            fcPlanRiskDutyList.add(duty8);
        }
        // 民航班机意外伤害医疗保险金GD0058
        startColumn = 62;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
            FCPlanRiskDuty duty9 = new FCPlanRiskDuty();
            duty9.setDutyCode("GD0058");
            duty9.setEnsureCode(ensureCode);
            duty9.setRiskCode(riskCode);
            duty9.setPlanCode(planCode);
            duty9.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 16))), 10000.0));
            duty9.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 17))));
            duty9.setGetLimit(100.0);
            duty9.setGetLimitType("2");
            planPrem += duty9.getPrem();
            duty9.setOperator(operator);
            duty9 = (FCPlanRiskDuty) CommonUtil.initObject(duty9, "INSERT");
            fcPlanRiskDutyList.add(duty9);
        }
        // 私家车或公务车意外伤害医疗保险金GD0059
        startColumn = 64;
        if (!ExcelUtil.isCellEmpty(row.getCell(startColumn))
                || !ExcelUtil.isCellEmpty(row.getCell(startColumn + 1))) {
            FCPlanRiskDuty duty10 = new FCPlanRiskDuty();
            duty10.setDutyCode("GD0059");
            duty10.setEnsureCode(ensureCode);
            duty10.setRiskCode(riskCode);
            duty10.setPlanCode(planCode);
            duty10.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 18))), 10000.0));
            duty10.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 19))));
            duty10.setGetLimit(100.0);
            duty10.setGetLimitType("2");
            planPrem += duty10.getPrem();
            duty10.setOperator(operator);
            duty10 = (FCPlanRiskDuty) CommonUtil.initObject(duty10, "INSERT");
            fcPlanRiskDutyList.add(duty10);
        }
        return true;

    }

    // 校验金额为数字或者小数点后两位 且 大于0
    public static boolean isNumber(String str) {
        // 判断小数点后2位的数字的正则表达式
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
        Matcher match = pattern.matcher(str);
        if (!match.matches()) {
            return false;
        } else if (Double.parseDouble(str) > 0) {
            return true;
        }
        return false;
    }

    // 校验金额为数字或者小数点后两位 且 大等于0
    public static boolean isNumber0(String str) {
        // 判断小数点后2位的数字的正则表达式
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
        Matcher match = pattern.matcher(str);
        if (!match.matches()) {
            return false;
        } else if (Double.parseDouble(str) >= 0) {
            return true;
        }
        return false;
    }

    // 校验金额为数字或者小数点后两位 且 大等于0
    public static boolean isNumber1(String str) {
        // 判断小数点后2位的数字的正则表达式
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
        Matcher match = pattern.matcher(str);
        if (!match.matches()) {
            return false;
        } else if (Double.valueOf(str) > 0 && Double.valueOf(str) <= 100) {
            return true;
        }
        return false;
    }


    //保额校验规则
    public static boolean isAmnt(String str, int t) {
        switch (t) {
            case 1://最低保额1万元
                if (Double.valueOf(str) >= 1) return true;
                break;
            case 2://最低保额为2000元，最高保额为30000元
                if (Double.valueOf(str) >= 0.2 && Double.valueOf(str) <= 3) return true;
                break;
            case 3://最低保险金额1万元，并以千元为递增单位；
                if (Double.valueOf(str) >= 1 && ((Double.valueOf(str) * 10) % 1 == 0)) return true;
                break;
            case 4://1.最低保险金额为5000元，并以千元为递增单位；
                if (Double.valueOf(str) >= 0.5 && ((Double.valueOf(str) * 10) % 1 == 0)) return true;
                break;
            case 5://最低保险金额不低于1万元；最高保险金额不超过100万元。
                if (Double.valueOf(str) >= 1 && Double.valueOf(str) <= 100) return true;
                break;
            case 6://本险种的最低保额为10元，以每10元为单位递增。；
                if (Double.valueOf(str) >= 10 && (Double.valueOf(str) % 10 == 0)) return true;
                break;
            case 7://最低保险金额不低于1万元；最高保险金额不超过200万元。
                if (Double.valueOf(str) >= 1 && Double.valueOf(str) <= 200) return true;
                break;
            case 8://最低保险金额不低于1万元；最高保险金额不超过300万元。
                if (Double.valueOf(str) >= 1 && Double.valueOf(str) <= 300) return true;
                break;
            case 9://最低保险金额不低于1万元；最高保险金额不超过500万元。
                if (Double.valueOf(str) >= 1 && Double.valueOf(str) <= 500) return true;
                break;
            case 10://最高保险金额不超过30万元。
                if (Double.valueOf(str) <= 30) return true;
                break;
            case 11://保险金额为100,200,400
                List<String> amntList = Arrays.asList("100", "200", "400", "100.0", "200.0", "400.0");
                if (amntList.contains(str)) return true;
                break;

        }
        return false;
    }

    //免赔额属性校验规则
    public static boolean isGetLimitType(String str, int t) {
        //门诊急诊团体医疗保险（17030）免赔额属性集合
        List<String> c1 = Arrays.asList("按年免赔", "按次免赔");
        switch (t) {
            case 1:
                if (c1.contains(str)) return true;
                break;
        }
        return false;
    }

    //免赔额校验规则
    public static boolean isDeductibles(String str, int t) {
        //门诊急诊团体医疗保险（17030）免赔额集合
        List<String> c1 = Arrays.asList
                ("0", "20", "50", "80", "100", "150", "200", "300", "400", "500", "0.0", "20.0", "50.0", "80.0", "100.0", "150.0", "200.0", "300.0", "400.0", "500.0");
        //横琴意外伤害团体医疗保险（15040）免赔额集合
        List<String> c2 = Arrays.asList
                ("0", "50", "100", "0.0", "50.0", "100.0");
        //横琴住院团体医疗保险(17010)  免赔额集合
        List<String> c3 = Arrays.asList("0", "200", "300", "500", "600", "700", "800", "900", "1000", "0.0", "200.0", "300.0", "500.0", "600.0", "700.0", "800.0", "900.0", "1000.0");
        switch (t) {
            case 1:
                if (c1.contains(str)) {
                    return true;
                }
                break;
            case 2:
                if (c2.contains(str)) {
                    return true;
                }
                break;
            case 3:
                if (c3.contains(str)) {
                    return true;
                }
                break;
        }
        return false;
    }

    //赔付比例校验规则
    public static boolean isReimbursementRatio(String str, int t) {
        switch (t) {
            case 1://横琴门诊急诊团体医疗保险(17030);横琴住院团体医疗保险(17010);横琴意外伤害团体医疗(15040) 赔付比例
                if (Double.valueOf(str) >= 50 && Double.valueOf(str) <= 100 && (Double.valueOf(str) % 10 == 0))
                    return true;
                break;
            case 2://横琴尊享团体补充医疗（17050）
                if (Double.valueOf(str) >= 60 && Double.valueOf(str) <= 100 && (Double.valueOf(str) % 10 == 0))
                    return true;
                break;
        }
        return false;
    }


    public boolean checkOneIdNoIsExists1(String idNo) {
        int val = fcPerInfoTempMapper.checkOneIdNoIsExists(idNo);
        if (val > 1) {
            return false;
        }
        return true;
    }

    //add by zch 2020/11/18
    //最大赔付天数校验规则  判断是否为1-366的整数 是返回true 否则返回false
    public static boolean isMaxPayDay(String str) {
        String regEx1 = "[1-9]|[1-9][0-9]|[12][0-9][0-9]|3(?:[0-5][0-9]|6[0-6])";
        Pattern p;
        Matcher m;
        p = Pattern.compile(regEx1);
        int value = new Double(Double.valueOf(str)).intValue();
        String string = String.valueOf(value);
        m = p.matcher(string);
        if (m.matches()) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * @param token
     * @return
     */
    @Transactional
    public String ensureMakeConfirm(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            /******************** 准备数据 *********************/
            // 获取session
            GlobalInput globalInput = userService.getSession(token);
            // 判断是否首次提交。（福利定制完后是可以添加导入员工的。这个时候需要重新点提交按钮）
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);

            List<FCPerInfoTemp> fcPerInfoTemps = fcPerInfoTempMapper.selectByEnsureCode(ensureCode);
            //判断险种是否下架
            String riskStopSale = checkRiskStopSale(ensureCode);
            if (!StringUtil.isEmpty(riskStopSale)) {
                resultMap.put("success", Boolean.FALSE);
                resultMap.put("code", "500");
                resultMap.put("message", riskStopSale);
                return JSON.toJSONString(resultMap);
            }
            if (!"1".equals(fcEnsure.getPlanType())) {
                //系统后台默认配置配置投保计划年龄段限制
                Map<String, Object> map = new HashMap<>();
                map.put("ensureCode", ensureCode);
                List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectFCEnsurePlans(map);
                List<FcPlanConfig> insertPlanConfigList = new ArrayList<>();
                //查询企业下所有的职级
                List<FCBusPersonType> fcBusPersonTypeList = fcBusPersonTypeMapper.selectByGrpNo(globalInput.getGrpNo());
                for (FCEnsurePlan fcEnsurePlan : fcEnsurePlanList) {
                    for (int i = 1; i <= 11; i++) {
                        FcPlanConfig fcPlanConfig = new FcPlanConfig();
                        fcPlanConfig.setEnsureCode(ensureCode);
                        fcPlanConfig.setPlanCode(fcEnsurePlan.getPlanCode());
                        fcPlanConfig.setGrpNo(globalInput.getGrpNo());
                        fcPlanConfig.setOperator("admin001");
                        switch (i) {
                            case 1:
                                fcPlanConfig.setConfigNo("011");
                                fcPlanConfig.setConfigValue("0");
                                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                                break;
                            case 2:
                                fcPlanConfig.setConfigNo("012");
                                fcPlanConfig.setConfigValue("D");
                                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                                break;
                            case 3:
                                fcPlanConfig.setConfigNo("013");
                                fcPlanConfig.setConfigValue("99");
                                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                                break;
                            case 4:
                                fcPlanConfig.setConfigNo("014");
                                fcPlanConfig.setConfigValue("Y");
                                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                                break;
                            case 5:
                                fcPlanConfig.setConfigNo("015");
                                fcPlanConfig.setConfigValue("父母@配偶@子女");
                                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                                break;
                            case 6:
                                fcPlanConfig.setConfigNo("016");
                                fcPlanConfig.setConfigValue("1");
                                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                                break;
                            case 7:
                                fcPlanConfig.setConfigNo("017");
                                fcPlanConfig.setConfigValue("7");
                                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                                break;
                            case 8:
                                fcPlanConfig.setConfigNo("018");
                                fcPlanConfig.setConfigValue("男@女");
                                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                                break;
                            case 9:
                                fcPlanConfig.setConfigNo("019");
                                fcPlanConfig.setConfigValue("有@无");
                                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                                break;
                            case 10:
                                fcPlanConfig.setConfigNo("020");
                                FCBusPersonType low = fcBusPersonTypeList.get(0);
                                String gradeLowLevelCode = low.getGradeLevelCode();
                                fcPlanConfig.setConfigValue(gradeLowLevelCode);
                                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                                break;
                            case 11:
                                fcPlanConfig.setConfigNo("021");
                                FCBusPersonType top = fcBusPersonTypeList.get(fcBusPersonTypeList.size() - 1);
                                String gradeTopLevelCode = top.getGradeLevelCode();
                                fcPlanConfig.setConfigValue(gradeTopLevelCode);
                                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                                break;
                        }
                        insertPlanConfigList.add(fcPlanConfig);
                    }
                }
                fcPlanConfigMapper.insert(insertPlanConfigList);
            }
            fcEnsure.setEnsureState(ConstantUtil.EnsureState_02);
            fcEnsure.setOperator(globalInput.getUserNo());
            fcEnsure.setInsuredNumber(fcPerInfoTemps.size());
            fcEnsure = (FCEnsure) CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKey(fcEnsure);
            log.info("更新福利表fcEnsure福利状态完成。。。");
            //删除退回原因
            fcEnsureConfigMapper.deleteByEnsureCode(ensureCode);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "福利制定提交成功");
            log.info("福利制定提交成功!");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("福利制定提交失败：", e);
            throw new RuntimeException();
        }
    }

    /**
     * 导入计划查看
     *
     * @param planCode
     * @return
     */
    public String queryPlanDetail(String planCode, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 保障计划表
            Map<String, Object> paramOne = new HashMap<>();
            paramOne.put("ensureCode", ensureCode);
            paramOne.put("planCode", planCode);
            FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByPrimaryKey(paramOne);
            if (fcEnsurePlan == null) {
                log.info("该计划不存在");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "该计划不存在");
                return JSON.toJSONString(resultMap);
            }
            Map<String, Object> params = new HashMap<>();
            params.put("planCode", planCode);
            params.put("ensureCode", ensureCode);
            // 计划-险种-责任集合
            List<Map<String, Object>> planRiskDutyInfo = new ArrayList<>();
            // 查询险种
            List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectRiskList(params);
            // 循环险种
            List<Map<String, Object>> risklist = new ArrayList<>();
            Map<String, String> param = new HashMap<>();
            for (FCPlanRisk risk : fcPlanRiskList) {
                // 查询责任
                params.put("riskCode", risk.getRiskCode());
                List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper
                        .selectDutyList(params);
                // 循环责任
                for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                    Map<String, Object> dutyMap = new HashMap<>();
                    dutyMap.put("planCode", risk.getPlanCode());
                    dutyMap.put("planName", fcEnsurePlan.getPlanName());
                    dutyMap.put("riskCode", risk.getRiskCode());
                    dutyMap.put("amnt", fcPlanRiskDuty.getAmnt());
                    dutyMap.put("dutyCode", fcPlanRiskDuty.getDutyCode());
                    //回显添加免赔额、免赔额属性、赔付比例三个字段（若数据库中为空，则返回也为空）
                    //免赔额
                    if (null == fcPlanRiskDuty.getGetLimit()) {
                        dutyMap.put("getLimit", "");
                    } else {
                        dutyMap.put("getLimit", fcPlanRiskDuty.getGetLimit());
                    }
                    //免赔额属性
                    if (null == fcPlanRiskDuty.getGetLimitType()) {
                        dutyMap.put("getLimitType", "");
                    } else {
                        dutyMap.put("getLimitType", fcPlanRiskDuty.getGetLimitType());
                    }
                    //赔付比例
                    if (null == fcPlanRiskDuty.getGetRatio()) {
                        dutyMap.put("getRatio", "");
                    } else {
                        //此数据在数据库中存储的是小数，向前台传输数据得时候要恢复到百分比
                        dutyMap.put("getRatio", fcPlanRiskDuty.getGetRatio() * 100);
                    }
                    //最大赔付天数
                    if (null == fcPlanRiskDuty.getMaxGetDay()) {
                        dutyMap.put("maxGetDay", "");
                    } else {
                        dutyMap.put("maxGetDay", fcPlanRiskDuty.getMaxGetDay());
                    }
                    param.clear();
                    param.put("riskCode", risk.getRiskCode());
                    param.put("dutyCode", fcPlanRiskDuty.getDutyCode());
                    Map<String, Object> dutyinfo = fdRiskDutyInfoMapper
                            .selectByDutyCode(param);
                    dutyMap.put("dutyName", dutyinfo.get("DutyName"));
                    dutyMap.put("riskName", dutyinfo.get("RiskName"));
                    // 保费
                    dutyMap.put("prem", fcPlanRiskDuty.getPrem());
                    planRiskDutyInfo.add(dutyMap);
                }
                Map<String, Object> rMap = new HashMap<>();
                rMap.put("dutyCount", fcPlanRiskDutyList.size());
                risklist.add(rMap);
            }
            // 应前端要求，这里要把fcEnsurePlan放入数组里
            List<FCEnsurePlan> planList = new ArrayList<>();
            planList.add(fcEnsurePlan);
            // 应前台格式要求
            resultMap.put("totalPrem", fcEnsurePlan.getTotalPrem());
            resultMap.put("data", planRiskDutyInfo);
            resultMap.put("planFocus", fcEnsurePlan.getPlanKey());
            resultMap.put("riskInfo", risklist);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "福利计划查看");
            log.info("福利制定提交成功!");
        } catch (Exception e) {
            log.info("查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "福利计划查询失败");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * @param grpNo
     * @param numb
     */
    public void updateGrpPeoples(String grpNo, int numb) {
        // 更新企业人数
        FCGrpInfo fcGrpInfo = new FCGrpInfo();
        fcGrpInfo.setGrpNo(grpNo);
        fcGrpInfo.setPeoples(numb);
        fcGrpInfoMapper.updateByPrimaryKeySelective(fcGrpInfo);
    }


    /**
     * 更新员工信息
     *
     * @param globalInput
     * @param fcPerInfo
     * @param ensureCode
     */
    private void updateStaff(GlobalInput globalInput, FCPerInfoTemp fcPerInfo, String ensureCode) {
        // 员工信息临时表
        FCPerInfoTemp fcPerTempInfo = new FCPerInfoTemp();
        fcPerTempInfo.setPerTempNo(fcPerInfo.getPerTempNo());
        fcPerTempInfo.setGrpNo(globalInput.getGrpNo());// 企业号
        fcPerTempInfo.setName(fcPerInfo.getName());// 姓名
        fcPerTempInfo.setDepartment(fcPerInfo.getDepartment());// 部门
        fcPerTempInfo.setSex(fcPerInfo.getSex());
        fcPerTempInfo.setBirthDay(fcPerInfo.getBirthDay());// 出生日期
        fcPerTempInfo.setMobilePhone(fcPerInfo.getMobilePhone());// 手机号
        fcPerTempInfo.setIDType(fcPerInfo.getIDType());// 证件类型
        fcPerTempInfo.setIDNo(fcPerInfo.getIDNo().toUpperCase());// 证件号
        fcPerTempInfo.setIdTypeEndDate(fcPerInfo.getIdTypeEndDate());//证件有效期
        fcPerTempInfo.setNativeplace(fcPerInfo.getNativeplace());//国籍
        fcPerTempInfo.setOccupationType(fcPerInfo.getOccupationType());// 职业类别
        fcPerTempInfo.setOccupationCode(fcPerInfo.getOccupationCode());// 职业编码
        fcPerTempInfo.setJoinMedProtect(fcPerInfo.getJoinMedProtect());// 有无医保
        fcPerTempInfo.setLevelCode(fcPerInfo.getLevelCode());// 职级
        fcPerTempInfo.setServiceTerm(fcPerInfo.getServiceTerm());// 服务年限
        fcPerTempInfo.setRetirement(fcPerInfo.getRetirement());// 是否退休
        fcPerTempInfo.setOpenBank(fcPerInfo.getOpenBank());// 开户行
        fcPerTempInfo.setOpenAccount(fcPerInfo.getOpenAccount());// 开户账号
        fcPerTempInfo.setStaffGrpPrem(fcPerInfo.getStaffGrpPrem());//员工福利额度
        fcPerTempInfo.setFamilyGrpPrem(fcPerInfo.getFamilyGrpPrem());//家属福利额度
        fcPerTempInfo.setOperator(globalInput.getUserNo());// 操作员
        fcPerTempInfo.setEnsureCode(ensureCode);// 福利编号
        fcPerTempInfo.setDefaultPlan(fcPerInfo.getDefaultPlan());// 默认计划编码
        fcPerTempInfo.setErrorMsg("");// 错误原因
        fcPerTempInfo.setImpotStatus("01");// 导入状态（01-导入成功；02-导入失败）
        fcPerTempInfo.setSubStaus("01");// 提交状态（01-未提交；02-已提交）
        fcPerTempInfo = (FCPerInfoTemp) CommonUtil.initObject(fcPerTempInfo, "UPDATE");
        fcPerInfoTempMapper.updateOneFcPerInfoTemp(fcPerTempInfo);

        FCPerinfoFamilyTemp fcPerinfoFamilyTemp = new FCPerinfoFamilyTemp();
        fcPerinfoFamilyTemp.setEnsureCode(ensureCode);
        fcPerinfoFamilyTemp.setPerTempNo(fcPerInfo.getPerTempNo());
        fcPerinfoFamilyTemp.setOldPerTempNo(fcPerInfo.getPerTempNo());
        fcPerinfoFamilyTemp.setPerName(fcPerInfo.getName());
        fcPerinfoFamilyTemp.setPerIDType(fcPerInfo.getIDType());
        fcPerinfoFamilyTemp.setPerIDNo(fcPerInfo.getIDNo());
        fcPerinfoFamilyTemp.setOperator(globalInput.getUserNo());
        fcPerinfoFamilyTemp = CommonUtil.initObject(fcPerinfoFamilyTemp, "UPDATE");
        int i = fcPerinfoFamilyTempMapper.updateByPrimaryKeySelective(fcPerinfoFamilyTemp);
    }

    public void updatePerAndPerson(GlobalInput globalInput, Map<String, String> updateMap, String ensureCode, String isflag) {
        if ("0".equals(isflag)) {
            FCPerInfo fcPerInfo = new FCPerInfo();
            fcPerInfo.setIDNo(updateMap.get("IDNo"));
            fcPerInfo.setMobilePhone(updateMap.get("mobilePhone"));
            fcPerInfo.setOperator(globalInput.getUserNo());
            fcPerInfo = CommonUtil.initObject(fcPerInfo, "UPDATE");
            int i = fcPerInfoMapper.updateByPrimaryKeySelective(fcPerInfo);
            FdUser fdUser = new FdUser();
            fdUser.setUserName(updateMap.get("IDNo"));
            fdUser.setCustomType("1");
            fdUser.setPhone(updateMap.get("mobilePhone"));
            fdUser.setOperator(globalInput.getUserNo());
            fdUser = CommonUtil.initObject(fdUser, "UPDATE");
            fdUserMapper.updataPwd(fdUser);
        }
        FCPerson fcPerson = new FCPerson();
        fcPerson.setIDNo(updateMap.get("IDNo"));
        fcPerson.setMobilePhone(updateMap.get("mobilePhone"));
        fcPerson.setOperator(globalInput.getUserNo());
        fcPerson = CommonUtil.initObject(fcPerson, "UPDATE");
        fcPersonMapper.updateByPrimaryKeySelective(fcPerson);
        FCOrderInsured fcOrderInsured = new FCOrderInsured();
        fcOrderInsured.setIDNo(updateMap.get("IDNo"));
        fcOrderInsured.setMobilePhone(updateMap.get("mobilePhone"));
        fcOrderInsured.setOperator(globalInput.getUserNo());
        fcOrderInsured = CommonUtil.initObject(fcOrderInsured, "UPDATE");
        fcOrderInsuredMapper.updateInPhoneByNoCommitCore(fcOrderInsured);
    }


    /**
     * hhw
     *
     * @param token
     * @param ensureCode
     * @param planObject 使用人: 1-员工 2-家属
     * @return
     */
    public String getPlanNameList(String token, String ensureCode, String planObject) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("ensureCode", ensureCode);
            params.put("planObject", planObject);
            List<FCEnsurePlan> planList = fcEnsurePlanMapper.selectFCEnsurePlans(params);
            if (planList != null && planList.size() > 0) {
                resultMap.put("data", planList);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "获取员工计划成功！");
            } else {
                log.info("获取员工计划失败: 当前福利下没有员工计划");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "获取员工计划失败，当福利下没有员工计划！");
            }
        } catch (Exception e) {
            log.info("获取员工计划失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "获取员工计划失败！");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 投保人告知接口
     *
     * @param token
     * @param ensureCode
     * @param map
     * @return
     */
    @Transactional
    public String appntImpartInfo(String token, String ensureCode, Map<String, String> map) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String grpNo = globalInput.getGrpNo();
            if (StringUtils.isEmpty(grpNo)) {
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
                grpNo = fcEnsure.getGrpNo();
            }
            Map<String, String> params = new HashMap<String, String>();
            params.put("ensureCode", ensureCode);
            if (StringUtils.isBlank(ensureCode)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "参数缺失，福利编号为空！");
                return JSON.toJSONString(resultMap);
            }
            int i = fcAppntImpartInfoMapper.deleteImpartInfo(params);
            FCAppntImpartInfo fcAppntImpartInfo = new FCAppntImpartInfo();
            fcAppntImpartInfo.setEnsureCode(ensureCode);
            fcAppntImpartInfo.setGrpNo(grpNo);
            for (String key : map.keySet()) {
                fcAppntImpartInfo.setImpartCode(key);
                fcAppntImpartInfo.setImpartParamModle(map.get(key));
                fcAppntImpartInfo.setOperator(globalInput.getUserNo());
                fcAppntImpartInfo = CommonUtil.initObject(fcAppntImpartInfo, "INSERT");
                fcAppntImpartInfoMapper.insert(fcAppntImpartInfo);
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "投保人告知保存成功！");
            log.info("投保人告知保存成功。");
        } catch (Exception e) {
            log.info("系统异常：投保人告知保存失败。");
            throw new RuntimeException("系统异常：投保人告知保存失败。");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 投保人告知查询接口
     *
     * @param token
     * @param ensureCode
     * @return
     */
    public String queryAppntImpartInfo(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, String> params = new HashMap<>();
            params.put("ensureCode", ensureCode);
            List<FCAppntImpartInfo> fcAppntImpartInfoList = fcAppntImpartInfoMapper.selectList(params);
            if (fcAppntImpartInfoList != null && fcAppntImpartInfoList.size() > 0 && !StringUtil.isEmpty(ensureCode)) {
                Map<String, String> map = new HashMap<>();
                for (int i = 0; i < fcAppntImpartInfoList.size(); i++) {
                    map.put(fcAppntImpartInfoList.get(i).getImpartCode(), fcAppntImpartInfoList.get(i).getImpartParamModle());
                }
                resultMap.put("data", map);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "投保人告知查询成功！");
                log.info("投保人告知查询成功。");
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "没有投保告知信息！");
                log.info("没有投保告知信息！");
            }

        } catch (Exception e) {
            log.info("系统异常：投保人告知查询失败。");
            throw new RuntimeException("系统异常：投保人告知查询失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 获取业务员信息接口
     *
     * @param token
     * @param ensureCode
     * @return
     */
    public String querySalesmanInfo(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FDAgentInfo agentInfo = fdAgentInfoMapper.getAgentByEnsureCode(ensureCode);
            if (!StringUtils.isNotBlank(agentInfo.getPhone())) {
                if (StringUtils.isNotBlank(agentInfo.getMobile())) {
                    agentInfo.setPhone(agentInfo.getMobile());
                }
            }
            resultMap.put("data", agentInfo);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "获取业务员信息成功");
        } catch (Exception e) {
            log.info("获取失败原因：" + e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "获取业务员信息失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询企业信息列表
     */
    public String gertGrplist(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        String grpNo = globalInput.getGrpNo();
        String contactNo = globalInput.getCustomNo();
        String customType = globalInput.getCustomType();
        if (StringUtils.isBlank(contactNo) || StringUtils.isBlank(customType) || !customType.equals("2")) {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "获取企业列表信息，参数有误！");
            return JSON.toJSONString(resultMap);
        }
        try {
            List<FCgrplistInfo> fcGrpInfoLists = fcGrpInfoMapper.getGrpInfoByContactNo(contactNo);
            resultMap.put("data", fcGrpInfoLists);
            //获取当前企业
            resultMap.put("curretGrpNo", grpNo);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "获取企业列表信息成功！");
        } catch (Exception e) {
            log.info("获取失败原因：" + e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "获取企业列表信息失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 更换登录企业
     */
    public String selectionGrp(String token, String grpNo) {
        Map<String, Object> resultMap = new HashMap<>();
        //重置globalInput信息
        GlobalInput globalInput = new GlobalInput();
        String userinfo = redisUtil.get(token);
        globalInput = JSON.parseObject(userinfo, GlobalInput.class);
        globalInput.setGrpNo(grpNo);
        //获取福利编号
        Map<String, Object> params = new HashMap<>();
        params.put("grpNo", grpNo);
        params.put("ensureState", "0");
        List<FCEnsure> fcEnsureList = fcEnsureMapper.findEnsureList(params);
        if (fcEnsureList != null && fcEnsureList.size() > 0) {
            globalInput.setEnsureCode(fcEnsureList.get(0).getEnsureCode());
        } else {
            globalInput.setEnsureCode("");
        }
        Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
        redisUtil.put(token, JSON.toJSONString(globalInput), loginValidity);
        if (JSON.parseObject(redisUtil.get(token), GlobalInput.class).getGrpNo().equals(grpNo)) {
            resultMap.put("globalInput", globalInput);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "更换登录企业成功");
        } else {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "更换登录企业失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 修改员工家属信息
     *
     * @param token
     * @param fcPerinfoFamilyTemp
     * @return
     */
    public String updateByFamilyTempNo(String token, FCPerinfoFamilyTemp fcPerinfoFamilyTemp, String iDType, String iDNo) {
        Map<String, Object> resultMap = new HashMap<>();
        String errorMsg = "";
        try {
            GlobalInput globalInput = userService.getSession(token);
            int i = 0;
            fcPerinfoFamilyTemp.setIDType(iDType);
            fcPerinfoFamilyTemp.setIDNo(iDNo);
            // 校验字段非空
            errorMsg = checkFamilyInfoIsEmpty(fcPerinfoFamilyTemp);

            if (!"".equals(errorMsg)) {
                throw new RuntimeException();
            }

            // 手机号校验
            if (StringUtils.isNotEmpty(fcPerinfoFamilyTemp.getPhone())) {
                List<FCPerInfoTemp> perInfoTempList = fcPerInfoTempMapper.selectByEnsureCode(fcPerinfoFamilyTemp.getEnsureCode());
                List<FCPerinfoFamilyTemp> perInfoFamilyTemps = fcPerinfoFamilyTempMapper.selectByFamilyTemp(new FCPerinfoFamilyTemp(fcPerinfoFamilyTemp.getEnsureCode(), fcPerinfoFamilyTemp.getPhone()));
                if (CollectionUtils.isNotEmpty(perInfoTempList) || CollectionUtils.isNotEmpty(perInfoFamilyTemps)) {
                    List<String> names = perInfoTempList.stream().filter(s -> s.getMobilePhone().equals(fcPerinfoFamilyTemp.getPhone())).map(FCPerInfoTemp::getName).collect(Collectors.toList());
                    List<String> nameList = perInfoFamilyTemps.stream().filter(s -> !s.getFamilyTempNo().equals(fcPerinfoFamilyTemp.getFamilyTempNo())).map(FCPerinfoFamilyTemp::getName).collect(Collectors.toList());
                    names.addAll(nameList);
                    if (CollectionUtils.isNotEmpty(names)) {
                        errorMsg = fcPerinfoFamilyTemp.getPhone() + "手机号和" + names + "重复，请录入家属本人号码。或家属手机号可为空！";
                        throw new RuntimeException();
                    }
                }

                // 校验 家属成年手机号是否与员工投保手机号一致
                FCPerInfoTemp fcPerInfoTemp = fcPerInfoTempMapper.selectFcPerInfoTemp(fcPerinfoFamilyTemp.getPerTempNo());
                if (!Objects.isNull(fcPerInfoTemp) && StringUtils.isNotEmpty(fcPerinfoFamilyTemp.getPhone()) && fcPerinfoFamilyTemp.getPhone().equals(fcPerInfoTemp.getMobilePhone())) {
                    errorMsg = fcPerinfoFamilyTemp.getPhone() + "手机号和" + fcPerInfoTemp.getName() + "," + fcPerinfoFamilyTemp.getName() + "重复，请录入家属本人号码。或家属手机号可为空！";
                    throw new RuntimeException();
                }
            }

            if ("0".equals(fcPerinfoFamilyTemp.getRelation())) {
                errorMsg = "与员工关系不可修改为本人。";
                throw new RuntimeException();
            }
            List<HashMap<String, String>> listPerIfo = new ArrayList<>();
            HashMap<String, String> perHashMap = new HashMap<>();
            perHashMap.put("ensureCode", fcPerinfoFamilyTemp.getEnsureCode());
            perHashMap.put("idno", fcPerinfoFamilyTemp.getPerIDNo());
            perHashMap.put("IDType", fcPerinfoFamilyTemp.getPerIDType());
            perHashMap.put("Name", fcPerinfoFamilyTemp.getPerName());
            listPerIfo.add(perHashMap);
            // 根据证件号、福利编号判断临时表是否已存在
            List<FCPerInfoTemp> checkTempIdNoIsExists = checkTempIdNoIsExists(globalInput.getGrpNo(), listPerIfo);
            if (checkTempIdNoIsExists.size() == 0) {// 当前福利编号下已存在相同的证件号,
                errorMsg = "当前福利下不存在该员工，请检查录入信息是否正确。";
                throw new RuntimeException();
            }

            // 根据证件号、福利编号判断临时表是否已存在  ensureCode下perTempNo下面IDNo,IDType已经存在
            int checkFamilyCount = fcPerinfoFamilyTempMapper.getFamilyCountByEnsureCode(fcPerinfoFamilyTemp);
            if (checkFamilyCount > 0) {
                errorMsg = "当前福利下所录员工已存在与该家属相同的证件号，请重新录入。";
                throw new RuntimeException();
            }

            //校验正式表 当前企业下是否存在同一证件号  存在 进行五要素比对 fcperson
            if (!checkStuIdNoIsExists(iDNo)) {
                Map<String, String> checkMap = new HashMap<>();
                checkMap.put("idNo", fcPerinfoFamilyTemp.getIDNo());
                checkMap.put("idType", fcPerinfoFamilyTemp.getIDType());
                checkMap.put("name", fcPerinfoFamilyTemp.getName());
                checkMap.put("sex", fcPerinfoFamilyTemp.getSex());
                checkMap.put("birthday", fcPerinfoFamilyTemp.getBirthDay());
                if (!checkStuIdNoIsExistsTwo(checkMap)) {        //五要素不同
                    errorMsg += "当前系统存在相同的家属证件号，但是姓名、性别、出生日期、证件类型不同，请核对后修改。/";

                }
            }
            // 获取证件类型集合
            List<String> idTypeList = fcPerInfoTempMapper.getIdTypeList("01");
            // 获取职业类别集合
            List<String> occupationTypeList = fcPerInfoTempMapper.getOccupationTypeList("01");
            // 获取职业集合
            List<String> occupationCodeList = fcPerInfoTempMapper.getOccupationCodeList("01");
            // 获取职业类别与职业代码对应关系
            Map<String, List<String>> occupationTypeCodeMap = getOccupationTypeCode();
            // 校验证件类型代码、职业类别代码、职业代码是否录入符合规则
            //证件类型码值转换
            String idTypeValue = fcPerinfoFamilyTemp.getIDType();
            String idType = "";
            List<HashMap<String, Object>> idTypeCodeList = fdCodeMapper.CodeInfo("IDType");
            for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
                if (idTypeValue.equals(hashMap1.get("CodeKey").toString())) {
                    idType = hashMap1.get("CodeName").toString();
                    break;
                }
            }
            String occupationType = fcPerinfoFamilyTemp.getOccupationType();
            String occupationCode = fcPerinfoFamilyTemp.getOccupationCode();
            if (!idTypeList.contains(idType)) {
                errorMsg = "家属证件类型录入错误";
                throw new RuntimeException();
            }
            if (!occupationTypeList.contains(occupationType)) {
                errorMsg = "家属职业类别录入错误";
                throw new RuntimeException();
            }
            if (!occupationCodeList.contains(occupationCode)) {
                errorMsg = "家属职业代码录入错误";
                throw new RuntimeException();
            }
            // 校验当前职业代码是否符合当前职业类别
            if (!occupationTypeCodeMap.get(fcPerinfoFamilyTemp.getOccupationType()).contains(fcPerinfoFamilyTemp.getOccupationCode())) {
                errorMsg = "职业类别不包含所录职业";
                throw new RuntimeException();
            }

            if ("0".equals(fcPerinfoFamilyTemp.getIDType())) {
                if (!IDCardUtil.isIDCard(fcPerinfoFamilyTemp.getIDNo())) {
                    errorMsg += "家属身份证号格式错误：" + fcPerinfoFamilyTemp.getIDNo() + "！";
                    throw new RuntimeException();
                }
                DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
                Date date = format1.parse(fcPerinfoFamilyTemp.getBirthDay());
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String dateString = formatter.format(date);
                String idBir = IDCardUtil.dateOfBirth(fcPerinfoFamilyTemp.getIDNo());
                if (!dateString.equals(idBir)) {
                    errorMsg += "家属出生日期与身份证不符";
                    throw new RuntimeException();
                }
                if (!getGenderByIdCard(fcPerinfoFamilyTemp.getIDNo()).equals("0".equals(fcPerinfoFamilyTemp.getSex()) ? "男" : "女")) {
                    errorMsg += "家属性别与身份证不符";
                    throw new RuntimeException();
                }
            }
            fcPerinfoFamilyTemp.setSubStaus("01");
            fcPerinfoFamilyTemp.setOperator(globalInput.getUserNo());
            fcPerinfoFamilyTemp.setOperator(globalInput.getUserName());
            CommonUtil.initObject(fcPerinfoFamilyTemp, "UPDATE");
            fcPerinfoFamilyTemp.setOldPerTempNo(checkTempIdNoIsExists.get(0).getPerTempNo());
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(fcPerinfoFamilyTemp.getEnsureCode());
            //校验家属-add by wudezhong
            Map<String, String> map = new HashMap<>();
            map.put("sign", "2");//1：员工 2：家属
            map.put("idType", fcPerinfoFamilyTemp.getIDType());//证件类型
            map.put("idNo", fcPerinfoFamilyTemp.getIDNo());//证件号
            map.put("birthDay", fcPerinfoFamilyTemp.getBirthDay());//出生日期
            map.put("sex", fcPerinfoFamilyTemp.getSex());//性别
            map.put("nativeplace", fcPerinfoFamilyTemp.getNativeplace());//国籍
            map.put("idTypeEndDate", fcPerinfoFamilyTemp.getIdTypeEndDate());//证件有效期
            map.put("occupationCode", fcPerinfoFamilyTemp.getOccupationCode());//职业代码
            map.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
            /**
             * 证件类型和国籍
             */
            String resultMsg = CheckUtils.checkSinglePeople(map);
            if (StringUtils.isNotBlank(resultMsg)) {
                errorMsg = fcPerinfoFamilyTemp.getName() + resultMsg;
                throw new RuntimeException();
            }
            if (ConstantUtil.EnsureState_1.equals(fcEnsure.getEnsureState())) {
                List<String> fcOrderList = fcOrderMapper.selectByEnsureCode(fcPerinfoFamilyTemp.getEnsureCode());
                if (fcOrderList.contains("02")) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "该福利下存在已提交至核心订单，不可修改");
                } else {
                    i = fcPerinfoFamilyTempMapper.updateByPrimaryKeySelective(fcPerinfoFamilyTemp);
                    Map<String, String> updateMap = new HashMap<>();
                    updateMap.put("IDNo", fcPerinfoFamilyTemp.getIDNo());
                    updateMap.put("mobilePhone", fcPerinfoFamilyTemp.getPhone());
                    updatePerAndPerson(globalInput, updateMap, fcPerinfoFamilyTemp.getEnsureCode(), "1");
                }
            } else {
                i = fcPerinfoFamilyTempMapper.updateByPrimaryKeySelective(fcPerinfoFamilyTemp);
            }
            if (i > 0) {
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "员工家属编辑成功");
            }
        } catch (Exception e) {
            log.info("修改家属信息失败:{}", e, errorMsg);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", errorMsg);
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 家属信息非空校验
     *
     * @return
     */
    public String checkFamilyInfoIsEmpty(FCPerinfoFamilyTemp fcPerinfoFamilyTemp) {
        if (StringUtil.isEmpty(fcPerinfoFamilyTemp.getName())) {
            return "家属姓名不可为空";
        }
        if (StringUtil.isEmpty(fcPerinfoFamilyTemp.getIDType())) {
            return "家属证件类型不可为空";
        }
        if (StringUtil.isEmpty(fcPerinfoFamilyTemp.getIDNo())) {
            return "家属证件号不可为空";
        }
        if (StringUtil.isEmpty(fcPerinfoFamilyTemp.getSex())) {
            return "家属性别不可为空";
        }
        if (StringUtil.isEmpty(fcPerinfoFamilyTemp.getBirthDay())) {
            return "家属出生日期不可为空";
        }
        if (StringUtil.isEmpty(fcPerinfoFamilyTemp.getJoinMedProtect())) {
            return "家属有无医保不可为空";
        }
        if (StringUtil.isEmpty(fcPerinfoFamilyTemp.getOccupationCode())) {
            return "家属职业不可为空";
        }
        if (StringUtil.isEmpty(fcPerinfoFamilyTemp.getRelation())) {
            return "家属与员工关系不可为空";
        }
        if (StringUtil.isEmpty(fcPerinfoFamilyTemp.getPerName())) {
            return "员工姓名不可为空";
        }
        if (StringUtil.isEmpty(fcPerinfoFamilyTemp.getPerIDType())) {
            return "员工证件类型不可为空";
        }
        if (StringUtil.isEmpty(fcPerinfoFamilyTemp.getPerIDNo())) {
            return "员工证件号不可为空";
        }
        return "";
    }

    @Transactional
    public String deleteByFamilyTempNo(String token, String familyTempNo) {
        Map<String, Object> resultMap = new HashMap<>();
        HashMap<String, String> familyInfoMap = new HashMap<>();
        if (StringUtils.isBlank(familyTempNo)) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "家属信息删除失败。");
            return JSON.toJSONString(resultMap);
        }
        familyInfoMap.put("familyTempNo", familyTempNo);
        int i = fcPerinfoFamilyTempMapper.deleteByPrimaryKey(familyInfoMap);
        if (i > 0) {
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "家属信息删除成功。");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 学生清单查询接口
     *
     * @param token
     * @param map
     * @return
     */
    public String queryStudent(String token, Map<String, String> map) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(map.get("ensureCode"));
            map.put("grpNo", fcEnsure.getGrpNo());
            PageHelper.startPage(Integer.parseInt(map.get("pageNo")), Integer.parseInt(map.get("pageSize")));
            List<Map<String, String>> studentList = fcPerinfoFamilyTempMapper.queryStudent(map);
            if (studentList.size() > 0) {
                // 如果有数据则进行码值转换
                for (int i = 0; i < studentList.size(); i++) {
                    FDCodeKey key = new FDCodeKey();
                    key.setCodeType("Sex");
                    key.setCodeKey(studentList.get(i).get("sex"));
                    studentList.get(i).put("sexName", fdCodeMapper.selectByPrimaryKey(key).getCodeName());
                    key.setCodeType("IDType");
                    key.setCodeKey(studentList.get(i).get("iDType"));
                    studentList.get(i).put("iDTypeName", fdCodeMapper.selectByPrimaryKey(key).getCodeName());
                    key.setCodeType("OccupationDetail");
                    key.setCodeKey(String.valueOf((int) Double.parseDouble(studentList.get(i).get("occupationCode"))));
                    studentList.get(i).put("occupationCodeName", fdCodeMapper.selectByPrimaryKey(key).getCodeName().trim());
                    if (!"".equals(studentList.get(i).get("nativeplace")) && studentList.get(i).get("nativeplace") != null) {
                        key.setCodeType("Nativeplace");
                        key.setCodeKey(studentList.get(i).get("nativeplace"));
                        studentList.get(i).put("nativeplaceName", fdCodeMapper.selectByPrimaryKey(key).getCodeName().trim());
                    }
                }
            }
            PageHelperUtil<Map<String, String>> teamPageInfo = new PageHelperUtil<Map<String, String>>(studentList);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("studentList", teamPageInfo.getList());
            dataMap.put("total", teamPageInfo.getTotal());
            resultMap.put("data", dataMap);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "学生清单查询成功");
        } catch (Exception e) {
            log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "学生清单查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 新增学生
     *
     * @param token
     * @param map
     * @return
     */
    @Transactional
    public String insertStudent(String token, Map<String, String> map) {
        Map<String, Object> resultMap = new HashMap<>();
        String errorMsg = "";
        GlobalInput globalInput = userService.getSession(token);
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(map.get("ensureCode"));
        try {
            String grpNo = globalInput.getGrpNo();
            // 查询福利信息
            errorMsg = checkStudent(map);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new RuntimeException();
            }
            if (!CheckUtils.checkMobilePhone(map.get("garMobilePhone"))) {
                errorMsg = "监护人手机号格式错误，请核对后重新录入！";
                throw new RuntimeException();
            }
            // 根据证件号判断正式表  当前企业是否已存在该账户 fcperinfo  监护人、员工
            if (!checkOneIdNoIsExists(map.get("garIDNo"))) {// 存在相同的证件号
                // 判断是否存在证件号存在，但是其他四要素不同的数据
                if (!checkOneOtherIsEsists(map.get("garIDNo"),
                        map.get("garName"), map.get("garBirthDay"),
                        map.get("garSex"), map.get("garIDType"))) {// 存在证件号相同，其他四要素不同的数据
                    errorMsg = "当前系统存在相同的监护人证件号，但是姓名、性别、出生日期、证件类型不同的数据";
                    throw new RuntimeException();
                }
            }
            // 根据证件号、企业客户号判断临时表该企业下是否已存在
            List<HashMap<String, String>> garPerInfo = new ArrayList<>();
            HashMap<String, String> garHashMap = new HashMap<>();
            garHashMap.put("name", map.get("garName"));
            garHashMap.put("idno", map.get("garIDNo"));
            garHashMap.put("sex", map.get("garSex"));
            garHashMap.put("birthday", map.get("garBirthDay"));
            garHashMap.put("idtype", map.get("garIDType"));
            garPerInfo.add(garHashMap);
            if (!checkIdNoIsExistsTemp(grpNo, garPerInfo)) {// 存在相同的证件号
                // 判断是否存在证件号存在，但是其他四要素不同的数据
                List<FCPerInfoTemp> checkOtherIsEsists = checkOtherIsEsistsTemp(grpNo, garPerInfo);
                if (checkOtherIsEsists.size() > 0) {// 存在证件号相同，其他四要素不同的数据
                    errorMsg += "当前企业其他福利存在相同的监护人证件号，但是姓名、性别、出生日期、证件类型不同，请重新录入监护人信息！";
                    throw new RuntimeException();
                }
            }

            //同监护人 判断当前企业下是否存在相同证件号  学生  正式表 fcpersono
            if (!checkStuIdNoIsExists(map.get("idNo"))) {
                //同监护人 判断是否存在证件号相同但其他四要素不同的数据
                Map<String, String> checkMap = new HashMap<>();
                checkMap.put("idNo", map.get("idNo"));
                checkMap.put("idType", map.get("iDType"));
                checkMap.put("name", map.get("name"));
                checkMap.put("sex", map.get("sex"));
                checkMap.put("birthday", map.get("birthDay"));
                if (!checkStuIdNoIsExistsTwo(checkMap)) {
                    errorMsg = "当前系统存在相同的学生证件号，但是姓名、性别、出生日期、证件类型不同的数据";
                    throw new RuntimeException();
                }
            }

            //同监护人 判断当前福利下是否已存在该学生
            FCPerinfoFamilyTemp fcPerinfoFamilyTemp = new FCPerinfoFamilyTemp();
            fcPerinfoFamilyTemp.setEnsureCode(map.get("ensureCode"));
            fcPerinfoFamilyTemp.setIDNo(map.get("idNo"));
            if (!checkStuIdNoIsExistsTemp(fcPerinfoFamilyTemp)) {
                errorMsg = "当前福利下已存在相同的学生证件号，请检查";
                throw new RuntimeException();
            }

            if (StringUtils.isNotBlank(map.get("planCode"))) {
                if (StringUtils.isBlank(map.get("studentGrpPrem"))) {
                    errorMsg = "学生默认计划录入，福利额度同样需要录入";
                    throw new RuntimeException();
                }
            }
            if (StringUtils.isBlank(map.get("planCode"))) {
                if (StringUtils.isNotBlank(map.get("studentGrpPrem"))) {
                    errorMsg = "学生默认计划未录入，福利额度不可录入";
                    throw new RuntimeException();
                }
            }

            if (StringUtils.isNotBlank(map.get("studentGrpPrem") == null ? "" : String.valueOf(map.get("studentGrpPrem")))) {
                String studentGrpPrem = String.valueOf(map.get("studentGrpPrem"));
                if (!studentGrpPrem.matches("^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*|0?\\.0+|0$") && !studentGrpPrem.matches("^\\d+$")) {
                    errorMsg = "学生福利额度只可录入数字（大于零）";
                    throw new RuntimeException();
                }
            }
            if (StringUtils.isNotBlank(map.get("planCode")) && StringUtils.isNotBlank(map.get("studentGrpPrem")) && StringUtils.isNotBlank(map.get("ensureCode"))) {
                Map<String, String> param = new HashMap<>();
                param.put("planCode", map.get("planCode"));
                param.put("ensureCode", map.get("ensureCode"));
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                String studentGrpPrem = String.valueOf(map.get("studentGrpPrem"));
                Double totalPrem = fcEnsurePlan.getTotalPrem();
                Double stuPrem = Double.parseDouble(studentGrpPrem);
                if (stuPrem < totalPrem) {
                    errorMsg = "学生福利额度应大于等于默认计划的保费";
                    throw new RuntimeException();
                }
            }
            // 获取证件类型集合
            List<String> idTypeList = fcPerInfoTempMapper.getIdTypeList("01");
            // 获取职业类别集合
            List<String> occupationTypeList = fcPerInfoTempMapper.getOccupationTypeList();
            // 获取职业集合
            List<String> occupationCodeList = fcPerInfoTempMapper.getOccupationCodeList("01");
            // 获取职业类别与职业代码对应关系
            Map<String, List<String>> occupationTypeCodeMap = getOccupationTypeCode();
            // 获取银行代码集合
            List<String> openBankList = fcPerInfoTempMapper.getOpenBankList("01");
            // 校验证件类型代码、职业类别代码、职业代码、银行码表是否录入符合规则
            FCPerInfoTemp fcPerInfo = new FCPerInfoTemp();
            fcPerInfo.setIDType(map.get("garIDType"));
            fcPerInfo.setIDNo(map.get("garIDNo"));
            fcPerInfo.setSex(map.get("garSex"));
            fcPerInfo.setBirthDay(map.get("garBirthDay"));
            fcPerInfo.setOccupationType(map.get("occupationType"));
            fcPerInfo.setOccupationCode(map.get("occupationCode"));
            errorMsg = checkOneCodeKey(fcPerInfo, idTypeList,
                    occupationTypeList, occupationCodeList, openBankList);
            if (!errorMsg.equals("")) {
                throw new RuntimeException();
            }
            // 校验当前职业代码是否符合当前职业类别
            if (!occupationTypeCodeMap.get(fcPerInfo.getOccupationType()).contains(fcPerInfo.getOccupationCode())) {
                errorMsg = "职业类别不包含所录职业";
                throw new RuntimeException();
            }
            // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确  监护人
            if (fcPerInfo.getIDType().equals("0")) {
                errorMsg = checkOneIDCard(fcPerInfo);
                if (!errorMsg.equals("")) {
                    throw new RuntimeException();
                }
            }

            Map<String, String> checkMap = new HashMap<>();
            checkMap.put("sex", map.get("sex"));
            checkMap.put("idNo", map.get("idNo"));
            checkMap.put("sign", "2");
            checkMap.put("idType", map.get("iDType"));
            checkMap.put("birthDay", map.get("birthDay"));
            checkMap.put("nativeplace", map.get("nativeplace"));
            checkMap.put("idTypeEndDate", map.get("idTypeEndDate"));
            checkMap.put("occupationCode", map.get("occupationCode"));
            checkMap.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
            /**
             * 证件类型和国籍
             */
            String resultMsg = CheckUtils.checkSinglePeople(checkMap);
            if (StringUtils.isNotBlank(resultMsg)) {
                errorMsg = "学生" + resultMsg;
                throw new RuntimeException();
            }

            FCPerInfoTemp fcPerInfoStudent = new FCPerInfoTemp();
            fcPerInfoStudent.setIDType(map.get("iDType"));
            fcPerInfoStudent.setIDNo(map.get("idNo"));
            fcPerInfoStudent.setSex(map.get("sex"));
            fcPerInfoStudent.setBirthDay(map.get("birthDay"));
            // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确  学生
            if (fcPerInfoStudent.getIDType().equals("0")) {
                errorMsg = checkOneIDCard(fcPerInfoStudent);
                if (!errorMsg.equals("")) {
                    throw new RuntimeException();
                }
            }
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "学生信息添加失败:" + errorMsg);
            return JSON.toJSONString(resultMap);
        }
        try {
            if (ConstantUtil.EnsureState_1.equals(fcEnsure.getEnsureState())) {
                List<String> fcOrderList = fcOrderMapper.selectByEnsureCode(map.get("ensureCode"));
                if (fcOrderList.contains("02")) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "该福利下存在已提交至核心订单，不能添加");
                } else {
                    resultMap = insertStudentInfo(globalInput, map);
                    List<FCPerInfoTemp> needSyncNum = getNeedSyncNum(map.get("ensureCode"));
                    if (needSyncNum.size() > 0) {
                        fcEnsure.setEnsureState(ConstantUtil.EnsureState_02);
                        fcEnsure.setOperator(globalInput.getUserNo());
                        fcEnsure = (FCEnsure) CommonUtil.initObject(fcEnsure, "UPDATE");
                        fcEnsureMapper.updateByPrimaryKey(fcEnsure);
                        log.info("福利状态更新完成。。。");
                    }
                }
            } else {
                resultMap = insertStudentInfo(globalInput, map);
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    public Map<String, Object> insertStudentInfo(GlobalInput globalInput, Map<String, String> map) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //监护人存员工临时表
            List<FCPerInfoTemp> perInfoTemplist = new ArrayList<FCPerInfoTemp>();
            FCPerInfoTemp fcPerInfoTemp = new FCPerInfoTemp();
            fcPerInfoTemp.setName(map.get("garName"));
            fcPerInfoTemp.setSex(map.get("garSex"));
            fcPerInfoTemp.setBirthDay(map.get("garBirthDay"));
            fcPerInfoTemp.setNativeplace(map.get("garNativeplace"));
            fcPerInfoTemp.setIDType(map.get("garIDType"));
            fcPerInfoTemp.setIDNo(map.get("garIDNo"));
            fcPerInfoTemp.setIdTypeEndDate(map.get("garIdTypeEndDate"));
            fcPerInfoTemp.setMobilePhone(map.get("garMobilePhone"));
            fcPerInfoTemp.setEnsureCode(map.get("ensureCode"));
            fcPerInfoTemp.setDefaultPlan(map.get("planCode"));
            if (StringUtils.isNotBlank(map.get("studentGrpPrem"))) {
                fcPerInfoTemp.setStudentGrpPrem(Double.parseDouble(map.get("studentGrpPrem")));
            } else {
                fcPerInfoTemp.setStudentGrpPrem(null);
            }
            fcPerInfoTemp.setOpenBank(map.get("garOpenbank"));
            fcPerInfoTemp.setOpenAccount(map.get("garOpenAccount"));
            fcPerInfoTemp.setGrpNo(globalInput.getGrpNo());
            fcPerInfoTemp.setErrorMsg("");// 错误原因
            fcPerInfoTemp.setImpotStatus("01");// 导入状态（01-导入成功；02-导入失败）
            fcPerInfoTemp.setSubStaus("01");// 提交状态（01-未提交；02-已提交）
            fcPerInfoTemp.setOperator(globalInput.getUserNo());
            fcPerInfoTemp.setPerTempNo(maxNoService.createMaxNo("PerTempNo", "", 20));
            fcPerInfoTemp = CommonUtil.initObject(fcPerInfoTemp, "INSERT");
            perInfoTemplist.add(fcPerInfoTemp);
            fcPerInfoTempMapper.insert(perInfoTemplist);
            //学生存家属临时表
            FCPerinfoFamilyTemp fcPerinfoFamilyTemp = new FCPerinfoFamilyTemp();
            fcPerinfoFamilyTemp.setPerTempNo(fcPerInfoTemp.getPerTempNo());
            fcPerinfoFamilyTemp.setEnsureCode(map.get("ensureCode"));
            fcPerinfoFamilyTemp.setName(map.get("name"));
            fcPerinfoFamilyTemp.setIDType(map.get("iDType"));
            fcPerinfoFamilyTemp.setIDNo(map.get("idNo"));
            fcPerinfoFamilyTemp.setSex(map.get("sex"));
            fcPerinfoFamilyTemp.setBirthDay(map.get("birthDay"));
            fcPerinfoFamilyTemp.setIdTypeEndDate(map.get("idTypeEndDate"));
            fcPerinfoFamilyTemp.setPhone(map.get("garMobilePhone"));
            fcPerinfoFamilyTemp.setOccupationType(map.get("occupationType"));
            fcPerinfoFamilyTemp.setOccupationCode(map.get("occupationCode"));
            fcPerinfoFamilyTemp.setJoinMedProtect(map.get("joinMedProtect"));
            fcPerinfoFamilyTemp.setNativeplace(map.get("nativeplace"));
            fcPerinfoFamilyTemp.setRelation(map.get("relation"));
            fcPerinfoFamilyTemp.setPerName(map.get("garName"));
            fcPerinfoFamilyTemp.setPerIDType(map.get("garIDType"));
            fcPerinfoFamilyTemp.setPerIDNo(map.get("garIDNo"));
            fcPerinfoFamilyTemp.setSubStaus("01");
            fcPerinfoFamilyTemp.setFamilyTempNo(maxNoService.createMaxNo("FamilyTempNo", "", 20));
            fcPerinfoFamilyTemp.setOperator(globalInput.getUserNo());
            fcPerinfoFamilyTemp = CommonUtil.initObject(fcPerinfoFamilyTemp, "INSERT");
            fcPerinfoFamilyTempMapper.insertSelective(fcPerinfoFamilyTemp);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "学生信息添加成功！");
        } catch (Exception e) {
            log.info(e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "学生信息添加失败！");
        }
        return resultMap;
    }

    /**
     * 学生信息非空校验
     *
     * @param map
     * @return
     */
    public String checkStudent(Map<String, String> map) {
        if (StringUtils.isBlank(map.get("name"))) {
            return "学生姓名不可为空!/";
        }
        if (StringUtils.isBlank(map.get("sex"))) {
            return "学生性别不可为空!/";
        }
        if (StringUtils.isBlank(map.get("birthDay"))) {
            return "学生出生日期不可为空!/";
        }
        if (StringUtils.isBlank(map.get("iDType"))) {
            return "学生证件类型不可为空!/";
        }
        if (StringUtils.isBlank(map.get("idNo"))) {
            return "学生证件号不可为空!/";
        }
        if (StringUtils.isBlank(map.get("occupationType"))) {
            return "学生职业类别不可为空!/";
        }
        if (StringUtils.isBlank(map.get("occupationCode"))) {
            return "学生职业不可为空!/";
        }
        if (StringUtils.isBlank(map.get("joinMedProtect"))) {
            return "学生有无医保不可为空!/";
        }
        if (StringUtils.isBlank(map.get("relation"))) {
            return "学生与监护人关系不可为空!/";
        }
        if (StringUtils.isBlank(map.get("garName"))) {
            return "监护人姓名不可为空!/";
        }
        if (StringUtils.isBlank(map.get("garSex"))) {
            return "监护人性别不可为空!/";
        }
        if (StringUtils.isBlank(map.get("garBirthDay"))) {
            return "监护人出生日期不可为空!/";
        }
        if (StringUtils.isBlank(map.get("garIDType"))) {
            return "监护人证件类型不可为空!/";
        }
        if (StringUtils.isBlank(map.get("garIDNo"))) {
            return "监护人证件号不可为空!/";
        }
        if (StringUtils.isBlank(map.get("garMobilePhone"))) {
            return "监护人联系方式不可为空!/";
        }
        return "";
    }

    /**
     * 校验正式表学生信息 判断该企业下是否已经存在相同证件号 家属可共用
     *
     * @param idNo
     * @return
     */
    public boolean checkStuIdNoIsExists(String idNo) {
        Map<String, String> map = new HashMap<>();
        map.put("idNo", idNo);
        int val = fcPersonMapper.checkStuIdNoIsExists(map);
        return val <= 0;
    }

    /**
     * 校验正式表学生信息 同一企业下已存在相同证件号，判断其他四要素是否相同  家属可共用
     *
     * @param map
     * @return
     */
    public boolean checkStuIdNoIsExistsTwo(Map<String, String> map) {
        int val = fcPersonMapper.checkStuIdNoIsExistsTwo(map);
        return val <= 0;
    }

    public List<String> checkNewPersonIsExists(Map<String, String> map) {
        return fcPersonMapper.checkNewPersonIsExists(map);
    }

    public boolean checkPerIdNoIsExists(Map<String, String> map) {
        int val = fcPersonMapper.checkPerIdNoIsExists(map);
        return val <= 0;
    }

    /**
     * 校验临时表信息  同一福利下是否存在相同证件号  家属可共用
     *
     * @return
     */
    public boolean checkStuIdNoIsExistsTemp(FCPerinfoFamilyTemp fcPerinfoFamilyTemp) {
        Integer val = fcPerinfoFamilyTempMapper.getFamilyCountByEnsureCode(fcPerinfoFamilyTemp);
        return val <= 0;
    }

    public boolean checkOneOtherIsEsistsTemp(Map<String, String> map) {
        Integer val = fcPerInfoTempMapper.checkOneOtherIsEsistsTemp(map);
        return val <= 0;
    }

    /**
     * 修改学生及监护人信息
     *
     * @param token
     * @param map
     * @return
     */
    @Transactional
    public String updateStudent(String token, Map<String, String> map) {
        String error = "";
        Map<String, Object> resultMap = new HashMap<>();
        //同监护人 判断当前福利下是否已存在该学生
        FCPerinfoFamilyTemp fcPerinfoFamilyTemp = new FCPerinfoFamilyTemp();
        GlobalInput globalInput = userService.getSession(token);
        // 查询福利信息
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(map.get("ensureCode"));
        try {
            String grpNo = globalInput.getGrpNo();
            error = checkStudent(map);
            if (StringUtils.isNotBlank(error)) {
                throw new RuntimeException();
            }
            String garMobilePhone = map.get("garMobilePhone");
            if (!CheckUtils.checkMobilePhone(garMobilePhone)) {
                error = "监护人手机号格式错误，请核对后重新录入！";
                throw new RuntimeException();
            }
            // 根据证件号判断正式表  当前企业是否已存在该账户 fcperinfo  监护人、员工
            if (!checkOneIdNoIsExists(map.get("garIDNo"))) {// 存在相同的证件号
                // 判断是否存在证件号存在，但是其他四要素不同的数据
                if (!checkOneOtherIsEsists(map.get("garIDNo"),
                        map.get("garName"), map.get("garBirthDay"),
                        map.get("garSex"), map.get("garIDType"))) {// 存在证件号相同，其他四要素不同的数据
                    error = "当前系统存在相同的监护人证件号，但是姓名、性别、出生日期、证件类型不同，请核对后重新录入！";
                    throw new RuntimeException();
                }
            }
            // 根据证件号、福利编号判断临时表是否已存在
            if (!checkTempOneIdNoIsExists(map.get("garIDNo"), map.get("garIDType"), map.get("ensureCode"), grpNo)) {// 当前福利编号下已存在相同的证件号
                //根据证件号及福利编号校验该福利下相同证件号五要素是否一致
                Map<String, String> checkMap = new HashMap<>();
                checkMap.put("idNo", map.get("garIDNo"));
                checkMap.put("idType", map.get("garIDType"));
                checkMap.put("name", map.get("garName"));
                checkMap.put("sex", map.get("garSex"));
                checkMap.put("birthday", map.get("garBirthDay"));
                checkMap.put("ensureCode", map.get("ensureCode"));
                if (!checkOneOtherIsEsistsTemp(checkMap)) {
                    error = "该福利下存在相同的监护人证件号，但是姓名、性别、出生日期、证件类型不同的数据，请重新录入！";
                    throw new RuntimeException();
                }
            }

            //判断当前企业下是否存在相同证件号  学生  正式表 fcpersono
            if (!checkStuIdNoIsExists(map.get("idNo"))) {
                //判断是否存在证件号相同但其他四要素不同的数据
                Map<String, String> checkMap = new HashMap<>();
                checkMap.put("idNo", map.get("idNo"));
                checkMap.put("idType", map.get("iDType"));
                checkMap.put("name", map.get("name"));
                checkMap.put("sex", map.get("sex"));
                checkMap.put("birthday", map.get("birthDay"));
                if (!checkStuIdNoIsExistsTwo(checkMap)) {
                    error = "当前系统存在与该学生相同的证件号，但是姓名、性别、出生日期、证件类型不同的数据";
                    throw new RuntimeException();
                }
            }
            fcPerinfoFamilyTemp.setEnsureCode(map.get("ensureCode"));
            fcPerinfoFamilyTemp.setIDNo(map.get("idNo"));
            fcPerinfoFamilyTemp.setFamilyTempNo(map.get("familyTempNo"));
            if (!checkStuIdNoIsExistsTemp(fcPerinfoFamilyTemp)) {
                error = "当前福利下已存在相同的学生证件号，请重新录入";
                throw new RuntimeException();
            }

            if (StringUtils.isNotBlank(map.get("planCode"))) {
                if (StringUtils.isBlank(map.get("studentGrpPrem"))) {
                    error = "学生默认计划录入，福利额度同样需要录入";
                    throw new RuntimeException();
                }
            }
            if (StringUtils.isBlank(map.get("planCode"))) {
                if (StringUtils.isNotBlank(map.get("studentGrpPrem"))) {
                    error = "学生默认计划未录入，福利额度不可录入";
                    throw new RuntimeException();
                }
            }
            if (StringUtils.isNotBlank(map.get("studentGrpPrem") == null ? "" : String.valueOf(map.get("studentGrpPrem")))) {
                String studentGrpPrem = String.valueOf(map.get("studentGrpPrem"));
                if (!studentGrpPrem.matches("^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*|0?\\.0+|0$") && !studentGrpPrem.matches("^\\d+$")) {
                    error = "学生福利额度只可录入数字（大于零）";
                    throw new RuntimeException();
                }
            }

            //校验计划编码是否存在
            if (StringUtils.isNotBlank(map.get("planCode")) && StringUtils.isNotBlank(map.get("ensureCode"))) {
                Map<String, String> param = new HashMap<>();
                param.put("planCode", map.get("planCode"));
                param.put("ensureCode", map.get("ensureCode"));
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                if (fcEnsurePlan == null) {
                    error = "学生默认计划不存在，请重新选择！";
                    throw new RuntimeException();
                }
            }

            if (StringUtils.isNotBlank(map.get("planCode")) && StringUtils.isNotBlank(map.get("studentGrpPrem")) && StringUtils.isNotBlank("ensureCode")) {
                Map<String, String> param = new HashMap<>();
                param.put("planCode", map.get("planCode"));
                param.put("ensureCode", map.get("ensureCode"));
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                String studentGrpPrem = String.valueOf(map.get("studentGrpPrem"));
                Double totalPrem = fcEnsurePlan.getTotalPrem();
                Double stuPrem = Double.parseDouble(studentGrpPrem);
                if (stuPrem < totalPrem) {
                    error = "学生福利额度应大于等于默认计划的保费";
                    throw new RuntimeException();
                }
            }

            // 获取证件类型集合
            List<String> idTypeList = fcPerInfoTempMapper.getIdTypeList("01");
            // 获取职业类别集合
            List<String> occupationTypeList = fcPerInfoTempMapper.getOccupationTypeList();
            // 获取职业集合
            List<String> occupationCodeList = fcPerInfoTempMapper.getOccupationCodeList("01");
            // 获取职业类别与职业代码对应关系
            Map<String, List<String>> occupationTypeCodeMap = getOccupationTypeCode();
            // 获取银行代码集合
            List<String> openBankList = fcPerInfoTempMapper.getOpenBankList("01");
            // 校验证件类型代码、职业类别代码、职业代码、银行码表是否录入符合规则
            FCPerInfoTemp fcPerInfo = new FCPerInfoTemp();
            fcPerInfo.setIDType(map.get("garIDType"));
            fcPerInfo.setIDNo(map.get("garIDNo"));
            fcPerInfo.setSex(map.get("garSex"));
            fcPerInfo.setBirthDay(map.get("garBirthDay"));
            fcPerInfo.setOccupationType(map.get("occupationType"));
            fcPerInfo.setOccupationCode(map.get("occupationCode"));
            error = checkOneCodeKey(fcPerInfo, idTypeList,
                    occupationTypeList, occupationCodeList, openBankList);
            if (!error.equals("")) {
                throw new RuntimeException();
            }
            // 校验当前职业代码是否符合当前职业类别
            if (!occupationTypeCodeMap.get(fcPerInfo.getOccupationType()).contains(fcPerInfo.getOccupationCode())) {
                error = "职业类别不包含所录职业";
                throw new RuntimeException();
            }
            // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确  监护人
            if (fcPerInfo.getIDType().equals("0")) {
                error = checkOneIDCard(fcPerInfo);
                if (!error.equals("")) {
                    error = "监护人" + error;
                    throw new RuntimeException();
                }
            }
            FCPerInfoTemp fcPerInfoStudent = new FCPerInfoTemp();
            fcPerInfoStudent.setIDType(map.get("iDType"));
            fcPerInfoStudent.setIDNo(map.get("idNo"));
            fcPerInfoStudent.setSex(map.get("sex"));
            fcPerInfoStudent.setBirthDay(map.get("birthDay"));
            // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确  学生
            if (fcPerInfoStudent.getIDType().equals("0")) {
                error = checkOneIDCard(fcPerInfoStudent);
                if (!error.equals("")) {
                    error = "学生" + error;
                    throw new RuntimeException();
                }
            }

            Map<String, String> checkMap = new HashMap<>();
            checkMap.put("sex", map.get("sex"));
            checkMap.put("idNo", map.get("idNo"));
            checkMap.put("sign", "2");
            checkMap.put("idType", map.get("iDType"));
            checkMap.put("birthDay", map.get("birthDay"));
            checkMap.put("nativeplace", map.get("nativeplace"));
            checkMap.put("idTypeEndDate", map.get("idTypeEndDate"));
            checkMap.put("occupationCode", map.get("occupationCode"));
            checkMap.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
            /**
             * 证件类型和国籍
             */
            String resultMsg = CheckUtils.checkSinglePeople(checkMap);
            if (StringUtils.isNotBlank(resultMsg)) {
                error = "学生" + resultMsg;
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "学生信息修改失败:" + error);
            return JSON.toJSONString(resultMap);
        }
        try {
            if (ConstantUtil.EnsureState_1.equals(fcEnsure.getEnsureState())) {
                List<String> fcOrderList = fcOrderMapper.selectByEnsureCode(fcPerinfoFamilyTemp.getEnsureCode());
                if (fcOrderList.contains("02")) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "该福利下存在已提交至核心订单，不可修改");
                } else {
                    updateStudentInfo(globalInput, map);
                    Map<String, String> updateMap = new HashMap<>();
                    updateMap.put("IDNo", fcPerinfoFamilyTemp.getIDNo());
                    updateMap.put("mobilePhone", fcPerinfoFamilyTemp.getPhone());
                    updatePerAndPerson(globalInput, updateMap, fcPerinfoFamilyTemp.getEnsureCode(), "1");
                }
            } else {
                updateStudentInfo(globalInput, map);
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "学生信息修改成功！");
        } catch (Exception e) {
            log.info(e.getMessage());
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    public String updateStudentInfo(GlobalInput globalInput, Map<String, String> map) {
        //监护人存员工临时表
        FCPerInfoTemp fcPerInfoTemp = new FCPerInfoTemp();
        fcPerInfoTemp.setName(map.get("garName"));
        fcPerInfoTemp.setSex(map.get("garSex"));
        fcPerInfoTemp.setBirthDay(map.get("garBirthDay"));
        fcPerInfoTemp.setNativeplace(map.get("garNativeplace"));
        fcPerInfoTemp.setIDType(map.get("garIDType"));
        fcPerInfoTemp.setIDNo(map.get("garIDNo"));
        fcPerInfoTemp.setIdTypeEndDate(map.get("garIdTypeEndDate"));
        fcPerInfoTemp.setMobilePhone(map.get("garMobilePhone"));
        fcPerInfoTemp.setEnsureCode(map.get("ensureCode"));
        fcPerInfoTemp.setDefaultPlan(map.get("planCode"));
        if (StringUtils.isNotBlank(map.get("studentGrpPrem"))) {
            fcPerInfoTemp.setStudentGrpPrem(Double.parseDouble(map.get("studentGrpPrem")));
        } else {
            fcPerInfoTemp.setStudentGrpPrem(null);
        }
        fcPerInfoTemp.setOpenBank(map.get("garOpenbank"));
        fcPerInfoTemp.setOpenAccount(map.get("garOpenAccount"));
        fcPerInfoTemp.setGrpNo(globalInput.getGrpNo());
        fcPerInfoTemp.setOperator(globalInput.getUserNo());
        fcPerInfoTemp.setPerTempNo(map.get("perTempNo"));
        fcPerInfoTemp = CommonUtil.initObject(fcPerInfoTemp, "UPDATE");
        fcPerInfoTempMapper.updateOneFcPerInfoTemp(fcPerInfoTemp);
        //学生存家属临时表
        FCPerinfoFamilyTemp fcPerinfoFamilyTemp = new FCPerinfoFamilyTemp();
        fcPerinfoFamilyTemp.setPerTempNo(map.get("perTempNo"));
        fcPerinfoFamilyTemp.setOldPerTempNo(map.get("perTempNo"));
        fcPerinfoFamilyTemp.setEnsureCode(map.get("ensureCode"));
        fcPerinfoFamilyTemp.setName(map.get("name"));
        fcPerinfoFamilyTemp.setIDType(map.get("iDType"));
        fcPerinfoFamilyTemp.setIDNo(map.get("idNo"));
        fcPerinfoFamilyTemp.setSex(map.get("sex"));
        fcPerinfoFamilyTemp.setBirthDay(map.get("birthDay"));
        fcPerinfoFamilyTemp.setIdTypeEndDate(map.get("idTypeEndDate"));
        fcPerinfoFamilyTemp.setPhone(map.get("garMobilePhone"));
        fcPerinfoFamilyTemp.setOccupationType(map.get("occupationType"));
        fcPerinfoFamilyTemp.setOccupationCode(map.get("occupationCode"));
        fcPerinfoFamilyTemp.setJoinMedProtect(map.get("joinMedProtect"));
        fcPerinfoFamilyTemp.setNativeplace(map.get("nativeplace"));
        fcPerinfoFamilyTemp.setRelation(map.get("relation"));
        fcPerinfoFamilyTemp.setPerName(map.get("garName"));
        fcPerinfoFamilyTemp.setPerIDType(map.get("garIDType"));
        fcPerinfoFamilyTemp.setPerIDNo(map.get("garIDNo"));
        fcPerinfoFamilyTemp.setFamilyTempNo(map.get("familyTempNo"));
        fcPerinfoFamilyTemp.setOperator(globalInput.getUserNo());
        fcPerinfoFamilyTemp = CommonUtil.initObject(fcPerinfoFamilyTemp, "UPDATE");
        int i = fcPerinfoFamilyTempMapper.updateByPrimaryKeySelective(fcPerinfoFamilyTemp);
        return "";
    }

    /**
     * 学生删除接口
     *
     * @param token
     * @param familyTempNo
     * @param perTempNo
     * @return
     */
    public String deleteStudent(String token, String familyTempNo, String perTempNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtils.isBlank(familyTempNo) || StringUtils.isBlank(perTempNo)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "学生删除失败：familyTempNo is NULL！");
                return JSON.toJSONString(resultMap);
            }
            HashMap<String, String> familyInfoMap = new HashMap<>();

            familyInfoMap.put("familyTempNo", familyTempNo);
            int i = fcPerinfoFamilyTempMapper.deleteByPrimaryKey(familyInfoMap);
            if (i > 0) {
                fcPerInfoTempMapper.deleteFcPerInfoTemp(perTempNo);
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "学生删除完成！");
        } catch (Exception e) {
            log.info(e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "学生删除失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 弹性计划--Hr退回后台定制
     *
     * @param
     * @param
     * @param
     * @return
     */
    @Transactional
    public String returnEnsure(String Authorization, Map<String, String> map) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            //福利编号
            String ensureCode = map.get("ensureCode");
            //退回节点
            String returnNode = map.get("returnNode");
            //退回原因
            String returnReason = map.get("returnReason");
            //执行退回人身份
            String operatorIdentity = map.get("OperatorIdentity");
            if (StringUtils.isBlank(ensureCode)
                    || StringUtils.isBlank(returnNode)
                    || StringUtils.isBlank(returnReason)
                    || StringUtils.isBlank(operatorIdentity)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "退回失败：请求参数有误！");
                return JSON.toJSONString(resultMap);
            }
            GlobalInput globalInput = userService.getSession(Authorization);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            /** 退回处理 */
            //删除已经存在的退回原因
            fcEnsureConfigMapper.deleteByEnsureCode(ensureCode);
            List<FCEnsureConfig> fcEnsureConfigList = new ArrayList<>();
            FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
            fcEnsureConfig.setEnsureCode(ensureCode);
            fcEnsureConfig.setGrpNo(fcEnsure.getGrpNo());
            fcEnsureConfig.setConfigNo("010");
            fcEnsureConfig
                    .setConfigValue(format.format(new Date()) + "@@" + returnReason + "@@" + globalInput.getNickName());
            fcEnsureConfig.setOperator(globalInput.getUserNo());
            fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
            CommonUtil.initObject(fcEnsureConfig, "INSERT");
            fcEnsureConfigList.add(fcEnsureConfig);
            fcEnsureConfigMapper.inserts(fcEnsureConfigList);
            //查询企业号
            fcEnsure.setEnsureState(ConstantUtil.EnsureState_010);
            fcEnsure.setOperator(globalInput.getUserNo());
            CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKey(fcEnsure);
            log.info("更新福利表FcEnsure投保状态完成。。。");
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "退回成功！");
        } catch (Exception e) {
            log.info("退回失败" + e.getMessage());
            throw new RuntimeException();

        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 福利退回原因查询
     *
     * @param Authorization
     * @param ensureCode
     * @return
     */
    public String selectEnsureReturnReason(String Authorization, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        try {
            if (StringUtils.isBlank(ensureCode)) {
                resultMap.put("message", "退回原因查询失败：请求参数有误！");
                return JSON.toJSONString(resultMap);
            }
            Map<String, Object> params = new HashMap<>();
            params.put("ensureCode", ensureCode);
            params.put("configNo", "010");
            String configValue = fcEnsureConfigMapper.selectOnlyValue(params);
            Map<String, Object> ensureMap = new HashMap<>();
            ensureMap.put("ensureCode", ensureCode);
            ensureMap.put("configNo", "010");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(ensureMap);
            if (StringUtils.isBlank(configValue)) {
                resultMap.put("message", "当前福利下不存在退回原因。");
            } else {
                List<String> returnInfoList = new ArrayList<>();
                if (configValue.contains("@@")) {
                    returnInfoList = Arrays.asList(configValue.split("\\@@"));
                    resultMap.put("returnDate", returnInfoList.get(0));     //退回时间
                    if (returnInfoList.size() > 1) {
                        resultMap.put("returnReason", returnInfoList.get(1));   //退回原因
                    }
                    if (returnInfoList.size() > 2) {
                        resultMap.put("returnHrName", returnInfoList.get(2));   //退回人员
                    }
                } else {
                    resultMap.put("returnReason", configValue);
                }
                resultMap.put("message", "退回原因查询完成！");
                resultMap.put("operator", fcEnsureConfig.getOperator());
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("data", configValue);
        } catch (Exception e) {
            log.info("退回原因查询失败" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "退回失败！");
        }
        return JSON.toJSONString(resultMap);
    }


    //校验险种是否停售
    public String checkRiskStopSale(String ensureCode) {
        //定义停售话术
        String message = "";
        //获取福利信息
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        String planType = fcEnsure.getPlanType();
        List<String> riskCodelist = new ArrayList<>();
        //固定计划
        if (planType.equals("0")) {
            riskCodelist = fcPlanRiskMapper.selectStopSaleRiskCodeByEnsureCode(ensureCode);
        } else if (planType.equals("1")) {
            //弹性计划
            riskCodelist = fcPlanRiskInfoMapper.selectStopSaleRiskCodeByEnsureCode(ensureCode);
        } else if (planType.equals("2")) {
            //日常计划
            riskCodelist = fcDailyInsureRiskInfoMapper.selectStopSaleRiskCodeByEnsureCode(ensureCode);
        }
        for (int i = 0; i < riskCodelist.size(); i++) {
            if (i == 0) {
                message += "该福利下";
            }
            if (i != 0) {
                message += "、";
            }
            message += riskCodelist.get(i);
        }
        if (!StringUtil.isEmpty(message)) {
            message += "产品已下架，请重新定制福利。";
        }
        return message;

    }

    /**
     * 存储福利信息
     */
    @Transactional
    public String saveEnsureInfo(String token, FCEnsure fcEnsure, FCGrpInfo fcGrpInfo, FcGrpContact fcGrpContact) {
        //校验福利名称
        Map<String, String> checkMap = new HashMap<>();
        checkMap.put("ensureName", fcEnsure.getEnsureName());
        checkMap.put("ensureCode", fcEnsure.getEnsureCode());
        int isExists = fcEnsureMapper.checkEnsureNameIsExists(checkMap);
        if (isExists != 0) {
            throw new SystemException("福利名称已存在，请检查");
        }
        //若为极短期，判断天数是否大于0小于365
        if ("1".equals(fcEnsure.getInsuredPeriodType())) {
            int num = Integer.parseInt(fcEnsure.getInsuredPeriod());
            if (num <= 0 || num >= 365) {
                throw new SystemException("保险期间天数只能是大于0小于365的正整数");
            }
        }

        String ensureCode;
        if (StringUtils.isBlank(fcEnsure.getEnsureCode())) {
            // 新增
            ensureCode = addEnsureInfo1(token, fcEnsure, fcGrpInfo, fcGrpContact);
        } else {
            // 修改
            ensureCode = modifyEnsureInfo1(token, fcEnsure, fcGrpInfo, fcGrpContact);
        }

        return ensureCode;
    }

    /**
     * 关联订单号接口操作
     *
     * @param authorization
     * @param orderNo
     * @return
     */
    public String relationOrderNoCheck(String authorization, String orderNo) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            //根据订单号查询
            InsurePlanOrder insurePlanOrder = fcInsurePlanOrderMapper.selectByOrderNo(orderNo);
            if (ObjectUtils.isEmpty(insurePlanOrder)) {
                response.put("message", "未查询到该订单号,请确认后输入正确的订单号！");
                return JSON.toJSONString(response);
            }
            // 订单关联状态（1：是，0：否）'
            if (insurePlanOrder.getStatus() == 1) {
                response.put("message", "订单号已被关联！");
                return JSON.toJSONString(response);
            }
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "关联成功！");
        } catch (Exception e) {
            log.info("系统异常:关联订单校验操作异常:" + e.getMessage());
        }
        return JSON.toJSONString(response);
    }

    /**
     * 订单号关联接口
     *
     * @param authorization
     * @param orderNo
     * @return
     */
    @Transactional
    public String relationOrderNo(String authorization, String orderNo, FCEnsure fcEnsure) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            GlobalInput globalInput = userService.getSession(authorization);

            // 查询企业信息
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectGrpInfo1(globalInput.getGrpNo());

            //企业联系人信息
            FcGrpContact fcGrpContact = new FcGrpContact();
            FCEnsure lastFcensure = fcEnsureMapper.getEnsureByGrpNo(globalInput.getGrpNo(), globalInput.getUserName());
            //TODO 可以通过 fcGrpContactMapper.selectGrpContactInfo1(customNo) 方式查询 ？是否需要
            if (lastFcensure != null) {
                fcEnsure.setClientNo(lastFcensure.getClientNo());
                FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(lastFcensure.getEnsureCode());
                //查询对应的企业联系人信息
                fcGrpContact = fcGrpContactMapper.selectGrpContact(fcEnsureContact.getIdNo());
            } else {
                fcGrpContact = fcGrpContactMapper.selectGrpContactInfo1(globalInput.getCustomNo());
            }
            //todo 从注册等方面完善录入
            if (null == fcGrpContact.getEmail()) {
                fcGrpContact.setEmail("-");
            }
            if (null == fcGrpContact.getDepartment()) {
                fcGrpContact.setDepartment("-");
            }
            //订单创建时间
            InsurePlanOrder insurePlanOrder = fcInsurePlanOrderMapper.selectByOrderNo(orderNo);
            log.info("订单详细信息:{}", JSON.toJSONString(insurePlanOrder));
            InsurePlanVo insurePlanVo = fcInsurePlanMapper.selectByInsurePlanCode(insurePlanOrder.getInsureplanCode());
            log.info("计划书详细信息:{}", JSON.toJSONString(insurePlanVo));

            //统计投保人数
            int count = fcInsurePlanOrderPersonrelMapper.selectCountPersonrel(orderNo);
            //基本参数填充
            fcEnsure.setClientNo(fcGrpInfo.getClientno());
            //默认值
            fcEnsure.setEndAppntDate("9999-12-31");

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //投保日期
            fcEnsure.setStartAppntDate(simpleDateFormat.format(new Date()));
            if (!ObjectUtils.isEmpty(insurePlanOrder)) {
                String format = simpleDateFormat.format(insurePlanOrder.getCreateTime() == null ? new Date() : insurePlanOrder.getCreateTime());
                fcEnsure.setStartAppntDate(format);
            }
            //生效时间
            fcEnsure.setCvaliDate(simpleDateFormat.format(new Date()));
            //投保人数
            fcEnsure.setInsuredNumber(count);
            //投保类型 默认
            fcEnsure.setEnsureType("0");
            //保险期间默认一年期
            fcEnsure.setInsuredPeriodType(insurePlanVo.getInsuredPeriodType());
            //保险期间
            fcEnsure.setInsuredPeriod(insurePlanVo.getInsuredPeriod());
            //付款方式
            fcEnsure.setPayType("1");
            //保存基本信息
            String ensureCode = saveEnsureInfo(authorization, fcEnsure, fcGrpInfo, fcGrpContact);
            log.info("福利编号:{}", ensureCode);
            fcEnsure.setEnsureCode(ensureCode);
            //险种责任信息保存
            relationRiskConfig(authorization, orderNo, globalInput, fcEnsure);
            //人员信息保存
            savePersonal(authorization, orderNo, globalInput, ensureCode, fcEnsure);

            //修改订单关联完成
            FCEnsure fe = new FCEnsure();
            fe.setEnsureCode(ensureCode);
            fe.setOrderNo(orderNo);
            fcEnsureMapper.updateByPrimaryKeySelective(fe);
            //订单号关联
            InsurePlanOrder order = new InsurePlanOrder();
            order.setId(insurePlanOrder.getId());
            order.setStatus(1);
            order.setUpdateTime(new Date());
            order.setModifier(globalInput.getUserName());
            //关联订单号
            fcInsurePlanOrderMapper.updateByPrimaryKeySelective(order);
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "关联成功！");
            return JSON.toJSONString(response);
        } catch (Exception e) {
            log.info("系统异常:关联订单操作异常:" + e.getMessage());
            throw new RuntimeException();

        }

    }


    /**
     * 关联计划书险种信息
     *
     * @param authorization
     * @param orderNo
     * @param globalInput
     * @param fcEnsure
     */
    public void relationRiskConfig(String authorization, String orderNo, GlobalInput globalInput, FCEnsure fcEnsure) {
        log.info("当前关联保单号:{},fcEnsure:{}", orderNo, JSON.toJSONString(fcEnsure));
        //计划书关联信息
        InsurePlanOrder insurePlanOrder = fcInsurePlanOrderMapper.selectByOrderNo(orderNo);
        log.info("计划书与订单信息:{}", JSON.toJSONString(insurePlanOrder));

        //查询计划书获取ID（ID作为福利ID保存险种信息）
        InsurePlanVo insurePlanVo = fcInsurePlanMapper.selectByInsurePlanCode(insurePlanOrder.getInsureplanCode());
        log.info("计划书信息:{}", JSON.toJSONString(insurePlanVo));
        String jEnsureCode = "JHS" + insurePlanVo.getId();
        //修改对应计划书配置到福利中
        fcEnsurePlanMapper.updateByEnsureCode(jEnsureCode, fcEnsure.getEnsureCode());
        fcPlanRiskMapper.updateByEnsureCode(jEnsureCode, fcEnsure.getEnsureCode());
        fcPlanRiskDutyMapper.updateByEnsureCode(jEnsureCode, fcEnsure.getEnsureCode());
        log.info("险种关联完成");
    }

    /**
     * 保存福利关联员工信息
     */
    public void savePersonal(String authorization, String orderNo, GlobalInput globalInput, String ensureCode, FCEnsure fcEnsure) {
        log.info("当前福利编号:{}", orderNo);
        try {
            List<FCPerInfoTemp> fcPerInfoTemps = fcPerInfoTempMapper.selectByEnsureCode(ensureCode);
            //查询当前订单下的人员信息
            List<InsurePlanOrderPersonrel> insurePlanOrderPersonrels = fcInsurePlanOrderPersonrelMapper.selectByOrderNo(orderNo);
            insurePlanOrderPersonrels = insurePlanOrderPersonrels.stream().filter(item -> fcPerInfoTemps.stream().noneMatch(per -> per.getIDNo().equals(item.getCredentialNo()))).collect(Collectors.toList());

            //不为空保存
            if (CollectionUtils.isNotEmpty(insurePlanOrderPersonrels)) {
                //员工表
                List<FCPerInfoTemp> perInfoTemplist = new ArrayList<>();

                for (InsurePlanOrderPersonrel insurePlanOrderPersonrel : insurePlanOrderPersonrels) {
                    // 员工信息临时表
                    FCPerInfoTemp fcPerTempInfo = new FCPerInfoTemp();
                    fcPerTempInfo.setGrpNo(globalInput.getGrpNo());// 企业号
                    fcPerTempInfo.setName(insurePlanOrderPersonrel.getName());// 姓名
                    String credentialNo = insurePlanOrderPersonrel.getCredentialNo();
                    fcPerTempInfo.setSex(IDCardUtil.sex(credentialNo));
                    fcPerTempInfo.setBirthDay(IDCardUtil.dateOfBirth(credentialNo));// 出生日期
                    fcPerTempInfo.setMobilePhone(insurePlanOrderPersonrel.getPhone());// 手机号
                    fcPerTempInfo.setNativeplace("CHN");//国籍
                    fcPerTempInfo.setIDType(insurePlanOrderPersonrel.getCredentialType());// 证件类型
                    fcPerTempInfo.setIDNo(credentialNo);// 证件号
                    fcPerTempInfo.setOccupationType("1");// 职业类别
                    fcPerTempInfo.setOccupationCode("2099908");// 职业编码
                    fcPerTempInfo.setJoinMedProtect("1");// 有无医保
                    fcPerTempInfo.setLevelCode("P1");//职级
                    fcPerTempInfo.setOperator(globalInput.getUserNo());// 操作员
                    String perTempNo = maxNoService.createMaxNo("PerTempNo", "", 20);
                    fcPerTempInfo.setPerTempNo(perTempNo);// 流水号
                    fcPerTempInfo.setEnsureCode(ensureCode);// 福利编号
                    fcPerTempInfo.setDefaultPlan("1");// 默认计划编码 --1
                    fcPerTempInfo.setErrorMsg("");// 错误原因
                    fcPerTempInfo.setImpotStatus("01");// 导入状态（01-导入成功；02-导入失败）
                    fcPerTempInfo.setSubStaus("01");// 提交状态（01-未提交；02-已提交）
                    CommonUtil.initObject(fcPerTempInfo, "INSERT");
                    perInfoTemplist.add(fcPerTempInfo);
                }
                fcPerInfoTempMapper.insert(perInfoTemplist);
            }
        } catch (Exception e) {
            log.info("关联订单 保存人员信息异常！+" + e.getMessage());
            throw new RuntimeException();
        }
    }

    /**
     * 解除订单关联
     *
     * @param authorization
     * @param ensureCode
     * @return
     */
    @Transactional
    public String relieveOrderNo(String authorization, String ensureCode) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            log.info("福利详情:{}}", JSON.toJSONString(fcEnsure));
            if (null == fcEnsure.getOrderNo()) {
                response.put("message", "未查询到关联订单号");
                return JSON.toJSONString(response);
            }
            fcEnsureMapper.updateOrderNoNull(fcEnsure);
            InsurePlanOrder insurePlanOrder = fcInsurePlanOrderMapper.selectByOrderNo(fcEnsure.getOrderNo());
            insurePlanOrder.setStatus(0); //解除关联
            fcInsurePlanOrderMapper.updateByPrimaryKeySelective(insurePlanOrder);
            //查询计划书
            InsurePlanVo insurePlanVo = fcInsurePlanMapper.selectByInsurePlanCode(insurePlanOrder.getInsureplanCode());
            //解除险种相关信息 --重新关联为计划书
            String jEnsureCode = "JHS" + insurePlanVo.getId();
            log.info("计划书详情:{}}", JSON.toJSONString(insurePlanVo));

            //修改对应计划书配置到福利中
            fcEnsurePlanMapper.updateByEnsureCode(fcEnsure.getEnsureCode(), jEnsureCode);
            fcPlanRiskMapper.updateByEnsureCode(fcEnsure.getEnsureCode(), jEnsureCode);
            fcPlanRiskDutyMapper.updateByEnsureCode(fcEnsure.getEnsureCode(), jEnsureCode);
            //删除相关内容 保持原逻辑
            if (delEnsure(ensureCode)) {
                response.put("success", true);
                response.put("code", "200");
                response.put("message", "解除成功！");
                return JSON.toJSONString(response);
            }
            response.put("message", "解除异常！");
        } catch (Exception e) {
            log.error("系统异常:解除关联异常！{},{}", e, e.getMessage());
        }
        return JSON.toJSONString(response);
    }

    /**
     * 复合成功后修改人员信息
     *
     * @param perInfo
     * @return
     */
    public String updateUser(FCPerInfoTemp perInfo) {
        String ensureCode = perInfo.getEnsureCode();
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", true);
        resultMap.put("code", "200");
        resultMap.put("message", "更新成功!");

        // 校验 家属成年手机号是否与员工投保手机号一致
        if (StringUtils.isNotEmpty(perInfo.getBirthDay()) && CheckUtils.checkStaffAge(perInfo.getBirthDay()) > 18 && StringUtils.isNotEmpty(perInfo.getPerTempNo())) {
            FCPerInfoTemp fcPerInfoTemp = fcPerInfoTempMapper.selectFcPerInfoTemp(perInfo.getPerTempNo());
            if (null != fcPerInfoTemp && StringUtils.isNotEmpty(fcPerInfoTemp.getMobilePhone()) && perInfo.getMobilePhone().equals(fcPerInfoTemp.getMobilePhone())) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "手机号与投保人手机号一致 请修改");
                return JSON.toJSONString(resultMap);
            }
        }

        if (StringUtils.isNotEmpty(ensureCode)) {
            String errorMsg = "";
            boolean flag = false;

            if (StringUtils.isNotEmpty(perInfo.getMobilePhone())) {
                if (!CheckUtils.checkMobilePhone(perInfo.getMobilePhone())) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "手机号格式错误，请检查");
                    return JSON.toJSONString(resultMap);
                }
            }
            // 查询福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            //校验福利额度
            if (StringUtils.isNotBlank(perInfo.getStaffGrpPrem() == null ? "" : String.valueOf(perInfo.getStaffGrpPrem()))) {
                double remainder = perInfo.getStaffGrpPrem() % 1;
                if (remainder != 0 || perInfo.getStaffGrpPrem() < 0) {
                    errorMsg = "员工福利额度只能为大于等于0的整数";
                }
                if (fcEnsure.getPlanType().equals("0")) {
                    Map<String, String> param = new HashMap<>();
                    param.put("planCode", perInfo.getDefaultPlan());
                    param.put("ensureCode", ensureCode);
                    FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                    if (perInfo.getStaffGrpPrem() < fcEnsurePlan.getTotalPrem()) {
                        errorMsg += "员工福利额度要大于等于默认计划的保费";
                    }
                }
                if (StringUtils.isNotBlank(errorMsg)) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", errorMsg);
                    return JSON.toJSONString(resultMap);
                }
            }

            Map<String, Object> paraMap = new HashMap<>();
            paraMap.put("idNo", perInfo.getIDNo());
            paraMap.put("mobilePhone", perInfo.getMobilePhone());
            paraMap.put("staffGrpPrem", perInfo.getStaffGrpPrem());
            List<FdUser> fdUserList = fdUserMapper.selectByIdNo(perInfo.getIDNo());
            List<FCPerson> fcPeopleList = fcPersonMapper.selectFcPersonByIdNo(perInfo.getIDNo());
            List<FCOrderInsured> fcOrderInsuredList = fcOrderInsuredMapper.selectByIdNo(perInfo.getIDNo());
            List<FCPerInfoTemp> fcPerInfoTempList = fcPerInfoTempMapper.selectByIdNo(perInfo.getIDNo());
            List<FCPerInfo> fcPerInfoList = fcPerInfoMapper.selectByIdNo(perInfo.getIDNo());
            List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectByIdNo(perInfo.getIDNo());
            FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpContact(perInfo.getIDNo());
            List<FCPerinfoFamilyTemp> fcPerinfoFamilyList = fcPerinfoFamilyTempMapper.selectByIdNo(perInfo.getIDNo());
            //判断额度是否修改
            for (FdUser fdUser : fdUserList) {
                paraMap.put("perNo", fdUser.getCustomNo());
                paraMap.put("ensureCode", ensureCode);
                FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(paraMap);
                if (null != fcPerRegistDay) {
                    if (!perInfo.getStaffGrpPrem().equals(fcPerRegistDay.getStaffGrpPrem())) {
                        if (checkUpdateStaffGrpPrem(perInfo.getIDNo(), ensureCode)) {
                            resultMap.put("success", false);
                            resultMap.put("code", "500");
                            resultMap.put("message", "该员工已完成计划确认，无法修改福利额度");
                            return JSON.toJSONString(resultMap);
                        }
                    }
                    break;
                }
            }
            if (CollectionUtils.isNotEmpty(fdUserList)) {
                flag = true;
                log.info("开始更新fdUser表");
                fdUserMapper.updateByIdNo(paraMap);
                //更新员工福利额度  需要用到fdUser的CustomNo
                for (FdUser fdUser : fdUserList) {
                    paraMap.put("perNo", fdUser.getCustomNo());
                    paraMap.put("staffGrpPrem", perInfo.getStaffGrpPrem());
                    paraMap.put("ensureCode", ensureCode);
                    fcPerRegistDayMapper.updateByPerNo(paraMap);
                }
            }
            if (CollectionUtils.isNotEmpty(fcPeopleList)) {
                flag = true;
                log.info("开始更新fcPerson表");
                fcPersonMapper.updateByIdNo(paraMap);
            }
            if (CollectionUtils.isNotEmpty(fcOrderInsuredList)) {
                flag = true;
                log.info("开始更新fcOrderInsured表");
                fcOrderInsuredMapper.updateByIdNo(paraMap);
            }
            if (CollectionUtils.isNotEmpty(fcPerInfoTempList)) {
                flag = true;
                log.info("开始更新fcPerInfoTemp表");
                fcPerInfoTempMapper.updateByIdNo(paraMap);
                //更新福利额度
                fcPerInfoTempMapper.updateByensureCode(paraMap);
            }
            if (CollectionUtils.isNotEmpty(fcPerInfoList)) {
                flag = true;
                log.info("开始更新fcPerInfo表");
                fcPerInfoMapper.updateByIdNo(paraMap);
            }
            if (CollectionUtils.isNotEmpty(fdAgentInfoList)) {
                flag = true;
                log.info("开始更新fdAgentInfo表");
                fdAgentInfoMapper.updateByIdNo(paraMap);
            }
            if (!ObjectUtils.isEmpty(fcGrpContact)) {
                flag = true;
                log.info("开始更新fcGrpContact表");
                fcGrpContactMapper.updateByIdNo(paraMap);
            }
            if (CollectionUtils.isNotEmpty(fcPerinfoFamilyList)) {
                flag = true;
                log.info("开始更新fcPerinfoFamilyTemp表");
                fcPerinfoFamilyTempMapper.updateByIdNo(paraMap);
            }
            if (!flag) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "人员不存在请查看证件号是否正确!");
            }
            return JSON.toJSONString(resultMap);
        } else {
            boolean flag = false;
            FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpContact(Base64AndMD5Util.base64SaltDecode(perInfo.getIDNo(), "130"));
            List<FdUser> fdUserList = fdUserMapper.selectByIdNo(Base64AndMD5Util.base64SaltDecode(perInfo.getIDNo(), "130"));
            List<FCPerson> fcPeopleList = fcPersonMapper.selectFcPersonByIdNo(Base64AndMD5Util.base64SaltDecode(perInfo.getIDNo(), "130"));
            Map<String, Object> paraMap = new HashMap<>();
            paraMap.put("idNo", Base64AndMD5Util.base64SaltDecode(perInfo.getIDNo(), "130"));
            paraMap.put("mobilePhone", Base64AndMD5Util.base64SaltDecode(perInfo.getMobilePhone(), "130"));
            if (!ObjectUtils.isEmpty(fcGrpContact)) {
                flag = true;
                log.info("开始更新fcGrpContact表");
                fcGrpContactMapper.updateByIdNo(paraMap);
            }
            if (CollectionUtils.isNotEmpty(fdUserList)) {
                flag = true;
                log.info("开始更新fdUser表");
                fdUserMapper.updateByIdNo(paraMap);
            }
            if (CollectionUtils.isNotEmpty(fcPeopleList)) {
                flag = true;
                log.info("开始更新fcPerson表");
                fcPersonMapper.updateByIdNo(paraMap);
            }
            if (!flag) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "人员不存在请查看证件号是否正确!");
            }
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 复合完成后的员工修改提示校验
     *
     * @return
     */
    public String checkUpdate(Map<String, String> request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String idNo = request.get("iDNo");
        List<FdUser> fdUserList = fdUserMapper.selectByIdNo(idNo);
        for (FdUser fdUser : fdUserList) {
            List<FCOrder> fcOrders = fcOrderMapper.selectOrder(fdUser.getCustomNo(), request.get("EnsureCode"));
            if (CollectionUtils.isNotEmpty(fcOrders)) {
                //已完成订单 该员工已完成计划确认，如删除员工，员工将无法投保该福利，员工选择的计划信息将一并删除，是否确定删除该员工？
                resultMap.put("orderSuccessCheck", false);
                break;
            } else {
                //未完成订单 该员工未完成计划确认，如删除员工，员工将无法投保该福利，是否确定删除该员工？
                resultMap.put("orderSuccessCheck", true);
            }
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 复合完成后的员工修改额度校验
     *
     * @return
     */
    public boolean checkUpdateStaffGrpPrem(String idNo, String ensureCode) {
        boolean flag = false;
        List<FdUser> fdUserList = fdUserMapper.selectByIdNo(idNo);
        for (FdUser fdUser : fdUserList) {
            List<FCOrder> fcOrders = fcOrderMapper.selectOrder(fdUser.getCustomNo(), ensureCode);
            if (CollectionUtils.isNotEmpty(fcOrders)) {
                //已完成订单 不允许修改额度
                flag = true;
                break;
            }
        }
        log.info("复合完成后的员工修改额度校验{}", flag);
        return flag;
    }

    /**
     * 复合完成后的员工删除接口
     *
     * @return
     */
    @Transactional
    public String deleteUser(Map<String, String> request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        if ("1".equals(request.get("ensureState"))) {
            log.info("复合完成后的员工删除接口:{}", JSON.toJSONString(request));
            try {
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "更新成功!");
                String idNo = request.get("iDNo");
                String ensureCode = request.get("ensureCode");
                List<FdUser> fdUserList = fdUserMapper.selectByIdNo(idNo);
                List<FCPerson> fcPeopleList = fcPersonMapper.selectFcPersonByIdNo(idNo);
                fcPeopleList.forEach(x -> {
                    request.put("personId", x.getPersonID());
                    fcDefaultPlanMapper.deleteByPerId(request);
                });
                for (FdUser fdUser : fdUserList) {
                    List<FCOrder> fcOrders = fcOrderMapper.selectOrder(fdUser.getCustomNo(), ensureCode);
                    if (CollectionUtils.isNotEmpty(fcOrders)) {
                        fcOrders.forEach(x -> {
                            fcOrderMapper.deleteByPrimaryKey(x.getOrderNo());
                            fcOrderItemMapper.deleteByOrderNo(x.getOrderNo());
                            fcOrderInsuredMapper.deleteByOrderNo(x.getOrderNo());
                        });
                    }
                    fcPerRegistDayMapper.deleteByParams(ensureCode, fdUser.getCustomNo());
                }
                HashMap<String, String> map = new HashMap<>();
                map.put("perTempNo", request.get("perTempNo"));
                fcPerinfoFamilyTempMapper.deleteByPrimaryKey(map);
                fcPerInfoTempMapper.deleteFcPerInfoTemp(request.get("perTempNo"));
                return JSON.toJSONString(resultMap);
            } catch (Exception e) {
                log.info("出现异常" + e);
                //手动回滚事务**
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                //组装异常情况下需要返回的数据
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "人员删除错误");
                return JSON.toJSONString(resultMap);
            }
        } else {
            return deleteStaff("", request.get("perTempNo"), request.get("ensureCode"));
        }
    }
}
