package com.sinosoft.eflex.service;

import ch.qos.logback.classic.Logger;
import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.dao.admin.EnsureAuditMapper;
import com.sinosoft.eflex.enums.CoreIdType;
import com.sinosoft.eflex.enums.SMSTemplateNoEnum;
import com.sinosoft.eflex.model.AddressEntity.EvaluationCustomer;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import com.sinosoft.eflex.service.admin.EnsureAuditService;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.util.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.*;

@Service("ElfexEnsureMakeService")
@SuppressWarnings("unused")
public class EflexEnsureMakeService {

    /**
     * 日志
     */
    private static Logger Log = (Logger) LoggerFactory.getLogger(EflexEnsureMakeService.class);


    /**
     * 引用Service层
     */
    @Autowired
    private UserService userService;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private EnsureAuditService ensureAuditService;
    /**
     * 注入Dao层
     */
    @Autowired
    private FCBusPersonTypeMapper fcBusPersonTypeMapper;
    @Autowired
    private EnsureAuditMapper ensureAuditMapper;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;
    @Autowired
    private FcPlanConfigMapper fcPlanConfigMapper;
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private EnsureMakeService ensureMakeService;
    @Autowired
    private FcEnsureContactMapper fcEnsureContactMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FcGrpContactMapper fcGrpContactMapper;
    @Autowired
    private FCGrpApplicantMapper fcGrpApplicantMapper;
    @Autowired
    private FCGrpApplicantContactMapper fcGrpApplicantContactMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FCEnsurePlanMapper fcEnsurePlanMapper;
    @Autowired
    private FCPerInfoTempMapper fcPerInfoTempMapper;
    @Autowired
    private FCPlanInformMapper fcPlanInformMapper;
    @Autowired
    private FCPlanRiskMapper fcPlanRiskMapper;
    @Autowired
    private FcPlanRiskInfoMapper fcPlanRiskInfoMapper;
    @Autowired
    private FcDutyAmountGradeMapper fcDutyAmountGradeMapper;
    @Autowired
    private FcDutyGradeOptionalAmountInfoMapper fcDutyGradeOptionalAmountInfoMapper;
    @Autowired
    private FcDutGradeCompensationRatioMapper fcDutGradeCompensationRatioMapper;
    @Autowired
    private FcDutyGroupDeductibleMapper fcDutyGroupDeductibleMapper;
    @Autowired
    private FcBusinessProDutyGrpObjectMapper fcBusinessProDutyGrpObjectMapper;
    @Autowired
    private FCPlanHealthDesignRelaMapper fcPlanHealthDesignRelaMapper;
    @Autowired
    private FCHealthDesignMapper fcHealthDesignMapper;
    @Autowired
    private FCHealthDesignDetailRelaMapper fcHealthDesignDetailRelaMapper;
    @Autowired
    private FCHealthDesignDetailMapper fcHealthDesignDetailMapper;
    @Autowired
    private FCPerinfoFamilyTempMapper fcPerinfoFamilyTempMapper;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private FDAgentInfoMapper fdAgentInfoMapper;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;
    @Autowired
    private FDRiskDutyInfoMapper fdRiskDutyInfoMapper;
    @Autowired
    private FDRiskDeductibleMapper fdRiskDeductibleMapper;
    @Autowired
    private FDRiskCompensationRatioMapper fdRiskCompensationRatioMapper;
    @Autowired
    private FCFileDocMainMapper fcFileDocMainMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private AddressCheckService addressCheckService;

    /**
     * 新增/修改 弹性计划保障信息
     *
     * @param token
     * @param param
     * @return
     */
    @Transactional
    public String maintainEnsureInfo(String token, Map<String, String> param) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            /**
             * 校验基本参数
             */
            //校验姓名和 电话
            String name = param.get("name");
            String iDType = param.get("iDType");
            if (!StringUtil.isEmpty(iDType) && !iDType.equals("1")) {
                String s = CheckUtils.checkChineseName(name);
                if (!StringUtil.isEmpty(s)) {
                    return JSON.toJSONString(ResultUtil.error(s));
                }
            } else if (!StringUtil.isEmpty(iDType) && iDType.equals("1")) {
                String s = CheckUtils.checkForeignName(name);
                String trim = name.trim();
                name = trim.replaceAll(" +", " ");
                if (!StringUtil.isEmpty(s)) {
                    return JSON.toJSONString(ResultUtil.error(s));
                }
            }
            List<EvaluationCustomer> customerList = Collections.singletonList(EvaluationCustomer.builder()
                    .name(param.get("grpName"))
                    .idType(CoreIdType.getNameByCoreId("9").name())
                    .idNo(param.get("grpIdNo"))
                    .build());

            /**
             * 逻辑处理
             */
            String ensureCode = "";
            // 获取session
            GlobalInput globalInput = userService.getSession(token);
            // 判断福利名称是否 已存在
            Map<String, String> checkMap = new HashMap<String, String>();
            checkMap.put("ensureName", param.get("ensureName"));
            if (param.get("ensureCode") == null || param.get("ensureCode").equals("")) {
                //查询福利名称是否已经存在
                int isExists = fcEnsureMapper.checkEnsureNameIsExists(checkMap);
                if (isExists == 0) {
                    ensureCode = "FL" + maxNoService.createMaxNo("EnsureCode", "", 18);
                    // 获取参数
                    FCEnsure fcEnsure = new FCEnsure();
                    fcEnsure = setFcEnsure(globalInput, fcEnsure, param);
                    fcEnsure.setEnsureCode(ensureCode);
                    fcEnsure.setGrpNo(param.get("grpNo"));
                    fcEnsure.setPremCalType(param.get("PremCalType"));
                    fcEnsure.setGreenInsurance("false".equals(param.get("greenInsurance")) ? Boolean.FALSE : Boolean.TRUE);
                    fcEnsure = CommonUtil.initObject(fcEnsure, "INSERT");
                    fcEnsureMapper.insert(fcEnsure);
                    param.put("ensureCode", ensureCode);
                    param.put("grpPayType", "D");
                    resultMap = ensureAuditService.insertEnsureConfig(globalInput, param);     //配置福利保障
                    if ("303".equals(resultMap.get("code"))) {
                        return JSON.toJSONString(resultMap);
                    } else {
                        Log.info("福利配置完成！");
                    }
                } else {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "福利名称已存在，请检查");
                    return JSON.toJSONString(resultMap);
                }
            } else {
                checkMap.put("ensureCode", param.get("ensureCode"));
                int isExists = fcEnsureMapper.checkEnsureNameIsExists(checkMap);
                if (isExists == 0) {
                    // 获取参数
                    ensureCode = param.get("ensureCode");
                    FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(param.get("ensureCode"));
                    if (fcEnsure == null) {
                        Log.info("福利编号" + param.get("ensureCode") + "不存在！");
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        resultMap.put("message", "福利编号" + param.get("ensureCode") + "不存在，请检查");
                        return JSON.toJSONString(resultMap);
                    }
                    fcEnsure = setFcEnsure(globalInput, fcEnsure, param);
                    fcEnsure.setGrpNo(param.get("grpNo"));
                    fcEnsure.setPremCalType(param.get("premCalType"));
                    fcEnsure.setGreenInsurance("false".equals(param.get("greenInsurance")) ? Boolean.FALSE : Boolean.TRUE);
                    fcEnsure = (FCEnsure) CommonUtil.initObject(fcEnsure, "UPDATE");
                    fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
                    // 弹性计划   配置福利保障 未对008进行更新  单独删除
                    fcEnsureConfigMapper.deleteByConfigNo_008(ensureCode);
                    resultMap = ensureAuditService.insertEnsureConfig(globalInput, param);     //配置福利保障
                    if ("303".equals(resultMap.get("code"))) {
                        return JSON.toJSONString(resultMap);
                    } else {
                        Log.info("福利配置完成！");
                    }
                } else {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "福利名称已存在，请检查");
                    return JSON.toJSONString(resultMap);
                }
            }
            resultMap.put("ensureCode", ensureCode);
            resultMap.put("grpNo", param.get("grpNo"));
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "弹性计划维护保障信息成功");
        } catch (Exception e) {
            Log.info(e.getMessage());
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 封装福利信息
     *
     * @param globalInput
     * @param fcEnsure
     * @param param
     * @return
     */
    public FCEnsure setFcEnsure(GlobalInput globalInput, FCEnsure fcEnsure, Map<String, String> param) {
        fcEnsure.setEnsureName(param.get("ensureName"));
        fcEnsure.setCvaliDate(param.get("cvaliDate"));
        fcEnsure.setStartAppntDate(param.get("startAppntDate"));
        fcEnsure.setEndAppntDate(param.get("endAppntDate"));
        fcEnsure.setClientNo(param.get("clientNo"));
        fcEnsure.setAppntYear(DateTimeUtil.getCurrentYear());
        // 0-订制中 1-定制完成
        if ("0".equals(fcEnsure.getEnsureState()) || StringUtils.isBlank(fcEnsure.getEnsureState())) {
            fcEnsure.setEnsureState("07");
        }
        fcEnsure.setTotalPrem(0.0);
        fcEnsure.setGrpNo(globalInput.getGrpNo());
        fcEnsure.setOperator(globalInput.getUserNo());
        // 保单终止日期为 生效日期加一年 减一天 modify by 20181106
        if (param.get("cvaliDate") != null) {
            String endDate = param.get("cvaliDate");
            // 加一年 减一天  生效日期2018-10-30的终止日期为 2019-10-29
            endDate = DateTimeUtil.dateAddOneYear(endDate);
            endDate = DateTimeUtil.dateSubOneDay(endDate);
            fcEnsure.setPolicyEndDate(endDate);
        }
        //福利投保类型，参考EnsureTypeEnum
        fcEnsure.setEnsureType(param.get("ensureType"));
        // 福利类型也可以称为计划类型，参考PlanTypeEnum
        fcEnsure.setPlanType(param.get("planType"));
        // 付款方式，参考PaymentTypeEnum
        fcEnsure.setPayType(param.get("payType"));

        return fcEnsure;
    }


    /**
     * 单击新增保额档次调用  查询该责任对应的可选责任及相关免赔额、赔付比例的配置数据
     *
     * @param riskCode
     * @param dutyCode
     * @return
     */
    public String getDutyConfig(String riskCode, String dutyCode, String amountGrageCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("riskCode", riskCode);
            mapInfo.put("amountGrageCode", amountGrageCode);
            FDRiskInfo fdRiskInfo = fdRiskInfoMapper.selectByPrimaryKey(riskCode);
            DutyConfigUtil dutyConfigUtil = new DutyConfigUtil();
            //二类险种查询可选责任相关信息
            if (dutyConfigUtil.getTwoRiskList().contains(riskCode)) {
                //可选责任
                List<Map<String, String>> fdRiskDutyInfoList = fdRiskDutyInfoMapper.getOptDutyByRiskCode(riskCode, dutyCode);
                resultMap.put("optDutyInfoList", fdRiskDutyInfoList);
                //可选责任--档次对应可选责任保额
                List<FcDutyGradeOptionalAmountInfo> optDutyInfo = fcDutyGradeOptionalAmountInfoMapper.selectByPrimaryKey(mapInfo);
                resultMap.put("optDutyInfoKey", optDutyInfo);
                //三类险种查询免赔额、赔付比例相关信息
            } else if (dutyConfigUtil.getThreeRiskList().contains(riskCode)) {
                //免赔额
                List<FDRiskDeductible> deductibleList = fdRiskDeductibleMapper.selectByPrimaryKey(mapInfo);
                resultMap.put("deductibleList", deductibleList);
                //赔付比例
                List<FDRiskCompensationRatio> riskRatioList = fdRiskCompensationRatioMapper.selectByPrimaryKey(mapInfo);
                resultMap.put("riskRatioList", riskRatioList);
                //免赔额---保额档次对应免赔额数值
                List<FcDutyGroupDeductible> dutyDeductibleList = fcDutyGroupDeductibleMapper.selectByPrimaryKey(mapInfo);
                resultMap.put("deductibleListKey", dutyDeductibleList);
                //赔付比例----对应赔付比例数值
                List<FcDutGradeCompensationRatio> dutGradeRatio = fcDutGradeCompensationRatioMapper.selectByPrimaryKey(mapInfo);
                resultMap.put("ratioListKey", dutGradeRatio);
                //四类险种查询可选责任相关信息
            } else if (dutyConfigUtil.getFourRiskList().contains(riskCode)) {
                //可选责任
                List<Map<String, String>> fdRiskDutyInfoList = fdRiskDutyInfoMapper.getOptDutyByRiskCode(riskCode, dutyCode);
                resultMap.put("optDutyInfoList", fdRiskDutyInfoList);
                //可选责任--档次对应可选责任保额
                List<FcDutyGradeOptionalAmountInfo> optDutyInfo = fcDutyGradeOptionalAmountInfoMapper.selectByPrimaryKey(mapInfo);
                resultMap.put("optDutyInfoKey", optDutyInfo);
                //属一类险种  除必选责任相关配置外无其他信息可查
            } else {
                resultMap.put("optDutyInfoList", null);
                resultMap.put("deductibleList", null);
                resultMap.put("riskRatioList", null);
            }
            resultMap.put("riskCode", riskCode);
            resultMap.put("riskName", fdRiskInfo.getRiskName());
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "保额档次配置数据查询成功。");
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "保额档次配置数据查询失败。");
        } finally {
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 获取必选责任档次相关配置信息
     *
     * @param riskCode
     * @param amountGrageCode
     * @return
     */
    public String getGradeConfig(String riskCode, String amountGrageCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("amountGrageCode", amountGrageCode);
            DutyConfigUtil dutyConfigUtil = new DutyConfigUtil();
            if (dutyConfigUtil.getTwoRiskList().contains(riskCode)) {                //二类险种查询可选责任相关信息
                //可选责任
                List<FcDutyGradeOptionalAmountInfo> optDutyInfo = fcDutyGradeOptionalAmountInfoMapper.selectByPrimaryKey(mapInfo);
                resultMap.put("optDutyInfo", optDutyInfo);
            } else if (dutyConfigUtil.getThreeRiskList().contains(riskCode)) {        //三类险种查询免赔额、赔付比例相关信息
                //免赔额
                List<FcDutyGroupDeductible> dutyDeductibleList = fcDutyGroupDeductibleMapper.selectByPrimaryKey(mapInfo);
                //赔付比例
                List<FcDutGradeCompensationRatio> dutGradeRatio = fcDutGradeCompensationRatioMapper.selectByPrimaryKey(mapInfo);
                resultMap.put("deductibleList", dutyDeductibleList);
                resultMap.put("ratioList", dutGradeRatio);
            } else if (dutyConfigUtil.getFourRiskList().contains(riskCode)) {            //四类险种查询可选责任信息
                //可选责任
                List<FcDutyGradeOptionalAmountInfo> optDutyInfo = fcDutyGradeOptionalAmountInfoMapper.selectByPrimaryKey(mapInfo);
                resultMap.put("optDutyInfo", optDutyInfo);
            } else {                                                                  //属一类险种  除必选责任相关配置外无其他信息可查
                resultMap.put("optDutyInfo", null);
                resultMap.put("deductibleList", null);
                resultMap.put("ratioList", null);
            }
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "保额档次相关配置信息查询成功。");
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "该保额档次相关配置信息查询失败");
        } finally {
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 新增保额档次
     *
     * @param token
     * @param map
     * @return
     */
    @Transactional
    public String addOrUpdateAmntGrade(String token, String operateType, Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        DutyConfigUtil dutyConfigUtil = new DutyConfigUtil();
        String riskCode = String.valueOf(map.get("riskCode"));
        try {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            //非空校验
            String error = checkRiskIsNull(dutyConfigUtil, map);
            if (!"".equals(error)) {
                resultMap.put("message", error);
                return JSON.toJSONString(resultMap);
            }
            //保额、免赔额、赔付比例等规则录入校验
            error = dutyConfigUtil.RiskCheck(map);
            if (!"".equals(error)) {
                resultMap.put("message", error);
                return JSON.toJSONString(resultMap);
            }
            //进行单位的转化以及非必录后台默认赋固定数值
            map = paramterConversion(map);
            if (StringUtils.isNotBlank(String.valueOf(map.get("waitingPeriod")))) {
                double waitingPeriod = Double.parseDouble(map.get("waitingPeriod").toString());
                if (!String.valueOf(map.get("waitingPeriod")).matches("^\\d+$") || waitingPeriod < 0) {
                    error = "等待期应为大于等于0的整数";
                    resultMap.put("message", error);
                    return JSON.toJSONString(resultMap);
                }
            }
            //TODO	加条件：只有是含有津贴的险种才校验最大赔付天数	含有津贴险种：15060、17020	即DutyConfigUtil中的第四类险种
            //最大赔付天数只能是1-366之间的正整数
            if (dutyConfigUtil.getFourRiskList().contains(map.get("riskCode"))) {
                if (StringUtils.isNotBlank(String.valueOf(map.get("maxGetDay")))) {
                    double maxGetDay = Double.parseDouble(map.get("maxGetDay").toString());
                    if (!String.valueOf(map.get("maxGetDay")).matches("[1-9]|[1-9][0-9]|[12][0-9][0-9]|3(?:[0-5][0-9]|6[0-6])")) {
                        error = "最大赔付天数只能是1-366之间的正整数";
                        resultMap.put("message", error);
                        return JSON.toJSONString(resultMap);
                    }
                }
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保额档次添加失败，请联系维护人员进行维护");
            return JSON.toJSONString(resultMap);

        }
        try {
            if ("0".equals(operateType) || "1".equals(operateType)) {
                // 存储必选责任表
                FcDutyAmountGrade fcDutyAmountGrade = addFcDutyAmountGrade(globalInput, operateType, map);
                // 二类险种
                if (dutyConfigUtil.getTwoRiskList().contains(riskCode)) {
                    //可选责任集合
                    Object object = map.get("optDutyInfoList");
                    List<Map<String, String>> optDutyInfoList = (List<Map<String, String>>) object;
                    if (optDutyInfoList.size() > 0) {
                        addFcDutyGradeOptionalAmountInfo(globalInput, operateType, fcDutyAmountGrade.getAmountGrageCode(), optDutyInfoList, String.valueOf(map.get("ensureCode")));
                    }
                }
                // 四类险种
                if (dutyConfigUtil.getFourRiskList().contains(riskCode)) {
                    //可选责任集合
                    Object object = map.get("optDutyInfoList");
                    List<Map<String, String>> optDutyInfoList = (List<Map<String, String>>) object;
                    if (optDutyInfoList.size() > 0) {
                        addFcDutyGradeOptionalAmountInfo(globalInput, operateType, fcDutyAmountGrade.getAmountGrageCode(), optDutyInfoList, String.valueOf(map.get("ensureCode")));
                    }

                }
                // 三类险种
                if (dutyConfigUtil.getThreeRiskList().contains(riskCode) || "15070".equals(riskCode)) {
                    addDeductibleAndRatio(globalInput, operateType, fcDutyAmountGrade.getAmountGrageCode(), map);
                }
                if ("0".equals(operateType)) {
                    resultMap.put("message", "所选险种保额档次新增成功");
                } else {
                    resultMap.put("message", "所选险种保额档次修改成功");
                }
            } else {
                resultMap.put("message", "操作标识符传输错误，请联系平台维护人员。");
            }
        } catch (Exception e) {
            Log.info(e.getMessage());
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 新增保额档次非空校验
     *
     * @param dutyConfigUtil
     * @param map
     * @return
     */
    public String checkRiskIsNull(DutyConfigUtil dutyConfigUtil, Map<String, Object> map) {
        String error = "";
        if (StringUtils.isBlank(String.valueOf(map.get("amountGrageName")))) {
            error = "自定义名称不可为空。";
        } else if (StringUtils.isBlank(String.valueOf(map.get("amnt")))) {
            error = "必选责任保额不可为空。";
        } else if (StringUtils.isBlank(String.valueOf(map.get("discountRatio")))) {
            error = "折扣比例不可为空。";
        }
        if (StringUtils.isNotBlank(error)) {
            return error;
        }
        //可选责任非空校验
        if (dutyConfigUtil.getTwoRiskList().contains(String.valueOf(map.get("riskCode")))) {
            //可选责任
            Object object = map.get("optDutyInfoList");
            List<Map<String, String>> optDutyInfoList = (List<Map<String, String>>) object;
            if (optDutyInfoList.size() > 0) {
                for (Map optDutyInfo : optDutyInfoList) {
                    FcDutyGradeOptionalAmountInfo fcDutyGradeOptionalAmountInfo = JSON.parseObject(JSON.toJSONString(optDutyInfo), FcDutyGradeOptionalAmountInfo.class);
                    if (StringUtils.isNotBlank(fcDutyGradeOptionalAmountInfo.getOptDutyCode())) {
                        if (StringUtils.isBlank(String.valueOf(fcDutyGradeOptionalAmountInfo.getAmnt()))) {
                            error = "可选责任保额不可为空。";
                            return error;
                        }
                    }
                }
            }
        }
        //四类险种（津贴险种）非空校验
        if (dutyConfigUtil.getFourRiskList().contains(String.valueOf(map.get("riskCode")))) {
            //可选责任
            Object object = map.get("optDutyInfoList");
            List<Map<String, String>> optDutyInfoList = (List<Map<String, String>>) object;
            if (optDutyInfoList.size() > 0) {
                for (Map optDutyInfo : optDutyInfoList) {
                    FcDutyGradeOptionalAmountInfo fcDutyGradeOptionalAmountInfo = JSON.parseObject(JSON.toJSONString(optDutyInfo), FcDutyGradeOptionalAmountInfo.class);
                    if (StringUtils.isNotBlank(fcDutyGradeOptionalAmountInfo.getOptDutyCode())) {
                        if (StringUtils.isBlank(String.valueOf(fcDutyGradeOptionalAmountInfo.getAmnt()))) {
                            error = "可选责任保额不可为空。";
                            return error;
                        }
                    }
                }
            }
        }
        //免赔额、赔付比例非空校验
        if (dutyConfigUtil.getThreeRiskList().contains(String.valueOf(map.get("riskCode")))) {
            //免赔额
            Object object = map.get("dutyDeductibleList");
            List<Map<String, String>> fcDutyGroupDeductibleList = (List<Map<String, String>>) object;
            List<String> deductibleList = new ArrayList<>();
            for (Map mapInfo : fcDutyGroupDeductibleList) {
                FcDutyGroupDeductible fcDutyGroupDeductible = JSON.parseObject(JSON.toJSONString(mapInfo), FcDutyGroupDeductible.class);
                if (StringUtils.isBlank(String.valueOf(fcDutyGroupDeductible.getDeductible()))) {
                    error = "免赔额不可为空，请录入。";
                    return error;
                }
                if (deductibleList.contains(fcDutyGroupDeductible.getDeductible().toString())) {
                    error = "免赔额不可重复，请重新录入。";
                    return error;
                }
                deductibleList.add(fcDutyGroupDeductible.getDeductible().toString());
            }
            //赔付比例
            Object objects = map.get("dutyRatioList");
            List<Map<String, String>> fcDutGradeCompensationRatioList = (List<Map<String, String>>) objects;
            for (Map tatioInfo : fcDutGradeCompensationRatioList) {
                FcDutGradeCompensationRatio fcDutGradeCompensationRatio = JSON.parseObject(JSON.toJSONString(tatioInfo), FcDutGradeCompensationRatio.class);
                if (StringUtils.isBlank(String.valueOf(fcDutGradeCompensationRatio.getCompensationRatio()))) {
                    error = "赔付比例不可为空，请录入。";
                    return error;
                }
            }
        }
        return error;
    }

    /**
     * 对前台传的值进行单位统一  以及 非必录后台进行赋固定值值
     *
     * @param map
     * @return
     */
    public Map<String, Object> paramterConversion(Map<String, Object> map) {
        DutyConfigUtil dutyConfigUtil = new DutyConfigUtil();
        String riskCode = String.valueOf(map.get("riskCode"));
        //等待期
        if (StringUtils.isBlank(String.valueOf(map.get("waitingPeriod")))) {
            //新增琴逸团体重大疾病险	险种编码"16490"
            if ("16040".equals(map.get("riskCode")) || "16490".equals(map.get("riskCode")) || "17020".equals(map.get("riskCode")) || "17010".equals(map.get("riskCode")) || "17030".equals(map.get("riskCode")) || "17050".equals(map.get("riskCode"))) {
                map.put("waitingPeriod", 30);
            } else {
                map.put("waitingPeriod", 0);
            }
        }
        //单位转化  万元转元
        if (dutyConfigUtil.getOneRiskList().contains(riskCode)) {
            double Amnt = Double.parseDouble(String.valueOf(map.get("amnt"))) * 10000;
            map.put("amnt", Amnt);
        } else if (dutyConfigUtil.getTwoRiskList().contains(riskCode)) {
            if ("15070".equals(map.get("riskCode"))) {
                double Amnt = Double.parseDouble(String.valueOf(map.get("amnt"))) * 10000;

                Object object = map.get("optDutyInfoList");
                List<Map<String, String>> optDutyInfoList = (List<Map<String, String>>) object;
                if (optDutyInfoList.size() > 0) {
                    for (int i = 0; i < optDutyInfoList.size(); i++) {
                        FcDutyGradeOptionalAmountInfo fcDutyGradeOptionalAmountInfo = JSON.parseObject(JSON.toJSONString(optDutyInfoList.get(i)), FcDutyGradeOptionalAmountInfo.class);
                        double optAmnt = fcDutyGradeOptionalAmountInfo.getAmnt() * 10000;
                        optDutyInfoList.get(i).put("amnt", String.valueOf(optAmnt));
                    }
                }
                map.put("amnt", Amnt);
                map.put("optDutyInfoList", optDutyInfoList);
            }
        } else if (dutyConfigUtil.getThreeRiskList().contains(riskCode)) {
            double amnt = Double.parseDouble(String.valueOf(map.get("amnt"))) * 10000;
            map.put("amnt", amnt);
        }
        return map;
    }

    /**
     * 存储必选责任
     *
     * @param globalInput
     * @param map
     * @return
     */
    public FcDutyAmountGrade addFcDutyAmountGrade(GlobalInput globalInput, String operateType, Map<String, Object> map) {
        String riskCode = String.valueOf(map.get("riskCode"));
        FcDutyAmountGrade fcDutyAmountGrade = new FcDutyAmountGrade();
        fcDutyAmountGrade.setEnsureCode(String.valueOf(map.get("ensureCode")));
        fcDutyAmountGrade.setRiskCode(String.valueOf(map.get("riskCode")));
        //只针对15070险种（其他险种存01）01-民航班机；02-轨道交通工具；03-水运公共交通工具；04-公路公共交通工具；05-私家车；
        if (!String.valueOf(map.get("riskCode")).equals("15070") || String.valueOf(map.get("dutyCode")).equals("GD0053")) {
            fcDutyAmountGrade.setRiskType("01");
        } else if (String.valueOf(map.get("dutyCode")).equals("GD0050")) {
            fcDutyAmountGrade.setRiskType("04");
        } else if (String.valueOf(map.get("dutyCode")).equals("GD0051")) {
            fcDutyAmountGrade.setRiskType("02");
        } else if (String.valueOf(map.get("dutyCode")).equals("GD0052")) {
            fcDutyAmountGrade.setRiskType("03");
        } else {
            fcDutyAmountGrade.setRiskType("05");        //GD0054  私家车
        }
        if ("15070".equals(riskCode)) {
            fcDutyAmountGrade.setAnnualTimeDeduction("2");
        } else {
            fcDutyAmountGrade.setAnnualTimeDeduction(String.valueOf(map.get("annualTimeDeduction")));
        }
        fcDutyAmountGrade.setDutyCode(String.valueOf(map.get("dutyCode")));
        fcDutyAmountGrade.setDutyName(String.valueOf(map.get("dutyName")));
        fcDutyAmountGrade.setDutyRange(String.valueOf(map.get("dutyRange")));
        fcDutyAmountGrade.setDutyType(String.valueOf(map.get("dutyType")));
        fcDutyAmountGrade.setAmountGrageName(String.valueOf(map.get("amountGrageName")));
        fcDutyAmountGrade.setAmnt(Double.parseDouble(String.valueOf(map.get("amnt"))));
        fcDutyAmountGrade.setDiscountRatio(Double.parseDouble(String.valueOf(map.get("discountRatio"))));
        fcDutyAmountGrade.setWaitingPeriod(Double.parseDouble(String.valueOf(map.get("waitingPeriod"))));
        //这是必选责任列表，针对17020险种中的GD0035没有意义。若是津贴险种才录入最大赔付天数，若不是，则不进入
        if (!("".equals(map.get("maxGetDay")) || "null".equals(map.get("maxGetDay")))) {
            fcDutyAmountGrade.setMaxGetDay(new BigDecimal(String.valueOf(map.get("maxGetDay"))));    //新增最大赔付天数
        }
        fcDutyAmountGrade.setSpecialAgreement(String.valueOf(map.get("specialAgreement")));
        fcDutyAmountGrade.setOperator(globalInput.getUserNo());
        //新增保额档次
        if ("0".equals(operateType)) {
            fcDutyAmountGrade.setAmountGrageCode(maxNoService.createMaxNo("amountGrageCode", "", 20));
            fcDutyAmountGrade = CommonUtil.initObject(fcDutyAmountGrade, "INSERT");
            fcDutyAmountGradeMapper.insertSelective(fcDutyAmountGrade);
        } else {//修改保额档次
            fcDutyAmountGrade.setAmountGrageCode(String.valueOf(map.get("amountGrageCode")));
            fcDutyAmountGrade = CommonUtil.initObject(fcDutyAmountGrade, "UPDATE");
            fcDutyAmountGradeMapper.updateByPrimaryKeySelective(fcDutyAmountGrade);
        }
        return fcDutyAmountGrade;
    }

    /**
     * 存储可选责任
     *
     * @param globalInput
     * @param optDutyInfoList
     */
    public void addFcDutyGradeOptionalAmountInfo(GlobalInput globalInput, String operateType, String amountGrageCode, List<Map<String, String>> optDutyInfoList, String ensureCode) {
        List<FcDutyGradeOptionalAmountInfo> fcDutyGradeOptionalAmountInfoList = new ArrayList<>();
        if (!"0".equals(operateType)) {
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("amountGrageCode", amountGrageCode);
            int o = fcDutyGradeOptionalAmountInfoMapper.deleteByEnsureCode(mapInfo);
        }
        //新增可选责任
        for (Map optDutyInfo : optDutyInfoList) {
            FcDutyGradeOptionalAmountInfo fcDutyGradeOptionalAmountInfo = JSON.parseObject(JSON.toJSONString(optDutyInfo), FcDutyGradeOptionalAmountInfo.class);
            fcDutyGradeOptionalAmountInfo.setAmountGrageCode(amountGrageCode);
            fcDutyGradeOptionalAmountInfo.setOperator(globalInput.getUserNo());
            fcDutyGradeOptionalAmountInfo = CommonUtil.initObject(fcDutyGradeOptionalAmountInfo, "INSERT");
            fcDutyGradeOptionalAmountInfoList.add(fcDutyGradeOptionalAmountInfo);
        }
        fcDutyGradeOptionalAmountInfoMapper.insertOptDutyList(fcDutyGradeOptionalAmountInfoList);
    }

    /**
     * 存储相关免赔额、赔付比例
     *
     * @param globalInput
     * @param map
     */
    public void addDeductibleAndRatio(GlobalInput globalInput, String operateType, String amountGrageCode, Map<String, Object> map) {
        if ("1".equals(operateType)) {
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", String.valueOf(map.get("ensureCode")));
            mapInfo.put("amountGrageCode", amountGrageCode);
            fcDutyGroupDeductibleMapper.deleteByEnsureCode(mapInfo);
            fcDutGradeCompensationRatioMapper.deleteByEnsureCode(mapInfo);
        }
        String riskCode = String.valueOf(map.get("riskCode"));
        //免赔额
        Object object = map.get("dutyDeductibleList");
        List<Map<String, String>> fcDutyGroupDeductibleList = (List<Map<String, String>>) object;
        if (fcDutyGroupDeductibleList.size() > 0) {
            List<FcDutyGroupDeductible> dutyDeductibleList = new ArrayList<>();
            for (Map mapInfo : fcDutyGroupDeductibleList) {
                FcDutyGroupDeductible fcDutyGroupDeductible = JSON.parseObject(JSON.toJSONString(mapInfo), FcDutyGroupDeductible.class);
                fcDutyGroupDeductible.setAmountGrageCode(amountGrageCode);
                fcDutyGroupDeductible.setOperator(globalInput.getUserNo());
                fcDutyGroupDeductible = CommonUtil.initObject(fcDutyGroupDeductible, "INSERT");
                dutyDeductibleList.add(fcDutyGroupDeductible);
            }
            fcDutyGroupDeductibleMapper.insertDutyDeductibleList(dutyDeductibleList);
        } else if ("15070".equals(riskCode)) {
            Object objects = map.get("optDutyInfoList");
            List<Map<String, String>> optDutyInfoList = (List<Map<String, String>>) objects;
            if (optDutyInfoList.size() > 0) {
                FcDutyGroupDeductible fcDutyGroupDeductible = new FcDutyGroupDeductible();
                fcDutyGroupDeductible.setAmountGrageCode(amountGrageCode);
                fcDutyGroupDeductible.setDeductible(100.00);
                fcDutyGroupDeductible.setOperator(globalInput.getUserNo());
                fcDutyGroupDeductible = CommonUtil.initObject(fcDutyGroupDeductible, "INSERT");
                fcDutyGroupDeductibleMapper.insertSelective(fcDutyGroupDeductible);
                FcDutGradeCompensationRatio fcDutGradeCompensationRatio = new FcDutGradeCompensationRatio();
                fcDutGradeCompensationRatio.setAmountGrageCode(amountGrageCode);
                fcDutGradeCompensationRatio.setCompensationRatio(90.00);
                fcDutGradeCompensationRatio.setOperator(globalInput.getUserNo());
                fcDutGradeCompensationRatio = CommonUtil.initObject(fcDutGradeCompensationRatio, "INSRT");
                fcDutGradeCompensationRatioMapper.insertSelective(fcDutGradeCompensationRatio);
            }
        }

        //赔付比例
        Object objects = map.get("dutyRatioList");
        List<Map<String, String>> fcDutGradeCompensationRatioList = (List<Map<String, String>>) objects;
        if (fcDutGradeCompensationRatioList.size() > 0) {
            List<FcDutGradeCompensationRatio> dutyRatioList = new ArrayList<>();
            for (Map tatioInfo : fcDutGradeCompensationRatioList) {
                FcDutGradeCompensationRatio fcDutGradeCompensationRatio = JSON.parseObject(JSON.toJSONString(tatioInfo), FcDutGradeCompensationRatio.class);
                fcDutGradeCompensationRatio.setAmountGrageCode(amountGrageCode);
                fcDutGradeCompensationRatio.setOperator(globalInput.getUserNo());
                fcDutGradeCompensationRatio = CommonUtil.initObject(fcDutGradeCompensationRatio, "UPDATE");
                dutyRatioList.add(fcDutGradeCompensationRatio);
            }
            fcDutGradeCompensationRatioMapper.insertDutyRatioList(dutyRatioList);
        }
    }

    /**
     * 新增险种
     *
     * @param token
     * @param fcPlanRiskInfo
     * @param isCheck        用于15070险种5个责任分保标记、佣金奖励津贴比率、手续费比率字段录入不一致时是否进行修改使用  确认修改时--非空
     * @return
     */
    @Transactional
    public String addOrUpdateRiskInfo(String token, FcPlanRiskInfo fcPlanRiskInfo, String isCheck) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            String ensureCode = fcPlanRiskInfo.getEnsureCode();
            String riskCode = fcPlanRiskInfo.getRiskCode();
            String riskType = fcPlanRiskInfo.getRiskType();
            GlobalInput globalInput = userService.getSession(token);
            int fcDutyAmountGradeCount = fcDutyAmountGradeMapper.selectByEnsureCode(ensureCode, riskCode, riskType);
            if (fcDutyAmountGradeCount < 1) {
                resultMap.put("message", "险种定制完成失败：当前险种保额档次未配置，请至少配置一个保额档次。");
                return JSON.toJSONString(resultMap);
            }
            Double feeRatio = fcPlanRiskInfo.getFeeRatio();
            Double commissionOrAllowanceRatio = fcPlanRiskInfo.getCommissionOrAllowanceRatio();
            if (StringUtils.isBlank(fcPlanRiskInfo.getReinsuranceMark())) {
                resultMap.put("message", "请选择分保标记。");
                return JSON.toJSONString(resultMap);
            }
            //update by zch 2020/12/1
            if (feeRatio != null) {
                if (fcPlanRiskInfo.getFeeRatio() < 0 || fcPlanRiskInfo.getFeeRatio() > 100) {
                    resultMap.put("message", "手续费比率为0-100之间的整数");
                    return JSON.toJSONString(resultMap);
                } else {
                    //转换小数
                    fcPlanRiskInfo.setFeeRatio(feeRatio / 100);
                }
            }
            if (commissionOrAllowanceRatio != null) {
                if (fcPlanRiskInfo.getCommissionOrAllowanceRatio() < 0 || fcPlanRiskInfo.getCommissionOrAllowanceRatio() > 100) {
                    resultMap.put("message", "佣金/服务津贴率为0-100之间的整数");
                    return JSON.toJSONString(resultMap);
                } else {
                    //转换小数
                    fcPlanRiskInfo.setCommissionOrAllowanceRatio(commissionOrAllowanceRatio / 100);
                }
            } else {
                resultMap.put("message", "佣金/服务津贴率不能为空！");
                return JSON.toJSONString(resultMap);
            }
            if ("15070".equals(riskCode) && StringUtils.isBlank(isCheck)) {
                FcPlanRiskInfo fcPlanRiskInfos = fcPlanRiskInfoMapper.getFeeRatioByEnsureCodeAndRiskCode(ensureCode, riskCode);
                if (fcPlanRiskInfos != null) {
                    if (!fcPlanRiskInfo.getReinsuranceMark().equals(fcPlanRiskInfos.getReinsuranceMark())) {
                        resultMap.put("code", "303");
                        resultMap.put("message", "同一险种分保标记需保持一致，您输入的内容与已添加险种列表不一致，点击确认进行修改。");
                        return JSON.toJSONString(resultMap);
                    }
                    if (fcPlanRiskInfo.getFeeRatio() != null && fcPlanRiskInfos.getFeeRatio() != null) {
                        if (fcPlanRiskInfo.getFeeRatio().doubleValue() != fcPlanRiskInfos.getFeeRatio().doubleValue()) {
                            resultMap.put("code", "303");
                            resultMap.put("message", "同一险种手续费比率需保持一致，您输入的内容与已添加险种列表不一致，点击确认进行修改。");
                            return JSON.toJSONString(resultMap);
                        }
                    } else if ((fcPlanRiskInfo.getFeeRatio() == null && fcPlanRiskInfos.getFeeRatio() != null)
                            || (fcPlanRiskInfo.getFeeRatio() != null && fcPlanRiskInfos.getFeeRatio() == null)) {
                        resultMap.put("code", "303");
                        resultMap.put("message", "同一险种手续费比率需保持一致，您输入的内容与已添加险种列表不一致，点击确认进行修改。");
                        return JSON.toJSONString(resultMap);
                    }
                    if (fcPlanRiskInfo.getCommissionOrAllowanceRatio() != null && fcPlanRiskInfos.getCommissionOrAllowanceRatio() != null) {
                        if (!fcPlanRiskInfo.getCommissionOrAllowanceRatio().equals(fcPlanRiskInfos.getCommissionOrAllowanceRatio())) {
                            resultMap.put("code", "303");
                            resultMap.put("message", "同一险种佣金/服务津贴率需保持一致，您输入的内容与已添加险种列表不一致，点击确认进行修改。");
                            return JSON.toJSONString(resultMap);
                        }
                    } else if ((fcPlanRiskInfo.getCommissionOrAllowanceRatio() == null && fcPlanRiskInfos.getCommissionOrAllowanceRatio() != null)
                            || (fcPlanRiskInfo.getCommissionOrAllowanceRatio() != null && fcPlanRiskInfos.getCommissionOrAllowanceRatio() == null)) {
                        resultMap.put("code", "303");
                        resultMap.put("message", "同一险种佣金/服务津贴率需保持一致，您输入的内容与已添加险种列表不一致，点击确认进行修改。");
                        return JSON.toJSONString(resultMap);
                    }
                }
            } else if ("15070".equals(riskCode) && StringUtils.isNotBlank(isCheck)) { //确认修改
                fcPlanRiskInfo.setOperator(globalInput.getUserNo());
                fcPlanRiskInfo = CommonUtil.initObject(fcPlanRiskInfo, "UPDATE");
                int i = fcPlanRiskInfoMapper.updateByRiskCode(fcPlanRiskInfo);
            }
            int i = 0;
            FcPlanRiskInfo planRiskInfo = fcPlanRiskInfoMapper.selectByPrimaryKey(ensureCode, riskCode, riskType);
            if (planRiskInfo == null) {
                fcPlanRiskInfo.setOperator(globalInput.getUserNo());
                fcPlanRiskInfo = CommonUtil.initObject(fcPlanRiskInfo, "INSERT");
                i = fcPlanRiskInfoMapper.insertSelective(fcPlanRiskInfo);
                if (i > 0) {
                    resultMap.put("message", "险种新增成功。");
                } else {
                    resultMap.put("message", "险种新增失败。");
                }
            } else {
                fcPlanRiskInfo.setOperator(globalInput.getUserNo());
                fcPlanRiskInfo = CommonUtil.initObject(fcPlanRiskInfo, "UPDATE");
                i = fcPlanRiskInfoMapper.updateByPrimaryKeySelective(fcPlanRiskInfo);
                if (i > 0) {
                    resultMap.put("message", "险种修改成功。");
                } else {
                    resultMap.put("message", "险种修改失败。");
                }
            }
            if (i > 0) {
                resultMap.put("code", "200");
                resultMap.put("success", true);
            }
        } catch (Exception e) {
            Log.info(e.getMessage());
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 删除所选险种相关信息
     *
     * @param Authorization
     * @param ensureCode
     * @param riskCode
     * @param riskType
     * @return
     */
    @Transactional
    public String delRiskInfo(String Authorization, String ensureCode, String riskCode, String riskType, String dutyCode, String amountGrageCode, String operateType, String deductible) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            String error = "";
            if (StringUtils.isBlank(ensureCode)) {
                error = "福利编码为空，不可进行险种删除，请联系平台维护人员。";
            } else {
                //operateType 操作标识符 区分删除属于按险种删除还是保额档次删除  0--险种  1--保额档次  2--删除免赔额  为1时amountGrageCode字段不可为空
                if ("0".equals(operateType)) {
                    if (StringUtils.isBlank(riskCode)) {
                        error = "险种编码为空，不可进行险种删除，请联系平台维护人员。";
                    } else if (StringUtils.isBlank(riskType)) {
                        error = "险种类别为空，不可进行险种删除，请联系平台维护人员。";
                    }
                } else if (("1".equals(operateType) || "2".equals(operateType)) && StringUtils.isBlank(amountGrageCode)) {
                    error = "保额档次为空，不可进行保额档次删除，请联系平台维护人员。";
                }
                if ("2".equals(operateType) && StringUtils.isBlank(deductible)) {
                    error = "免赔额为空，不可进行免赔额删除，请联系平台维护人员。";
                }
            }
            if (StringUtils.isNotBlank(error)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", error);
                return JSON.toJSONString(resultMap);
            }
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("riskCode", riskCode);
            mapInfo.put("riskType", riskType);
            mapInfo.put("dutyCode", dutyCode);
            mapInfo.put("amountGrageCode", amountGrageCode);
            mapInfo.put("deductible", deductible);
            if ("0".equals(operateType)) {
                fcPlanRiskInfoMapper.deleteByEnsureCodeRiskCode(mapInfo);
            }
            if ("1".equals(operateType)) {
                int fcDutyAmountGradeCount = fcDutyAmountGradeMapper.selectByEnsureCode(ensureCode, riskCode, riskType);
                if (fcDutyAmountGradeCount < 2) {
                    fcPlanRiskInfoMapper.deleteByEnsureCodeRiskCode(mapInfo);
                }
                int i = fcDutyAmountGradeMapper.deleteByEnsureCode(mapInfo);
                int o = fcDutyGradeOptionalAmountInfoMapper.deleteByEnsureCode(mapInfo);
                int p = fcDutGradeCompensationRatioMapper.deleteByEnsureCode(mapInfo);
            }
            int q = fcDutyGroupDeductibleMapper.deleteByEnsureCode(mapInfo);
            //查询删除之后必选保额档次信息
            List<Map<String, String>> dutyAmountGradeInfoMap = fdRiskDutyInfoMapper.getRiskAndDutyInfoByDutyCode(mapInfo);
            resultMap.put("data", dutyAmountGradeInfoMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "删除成功!!!");
        } catch (Exception e) {
            Log.info(e.getMessage());
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 福利职级查询
     *
     * @param token
     * @param ensureCode
     * @return
     */
    public String selectFCBusPersonTypeByEnsureCode(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtils.isBlank(ensureCode)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "职级查询失败：请求参数有误！");
                return JSON.toJSONString(resultMap);
            }
            GlobalInput globalInput = userService.getSession(token);
            List<FCBusPersonType> fcBusPersonTypelist = fcBusPersonTypeMapper.selectByGrpNo(fcEnsureMapper.selectByPrimaryKey(ensureCode).getGrpNo());
            if (fcBusPersonTypelist.size() < 1) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "该福利福利下未设置职级！");
                return JSON.toJSONString(resultMap);
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("data", fcBusPersonTypelist);
            resultMap.put("message", "查询成功！");
        } catch (Exception e) {
            Log.info("职级查询失败：" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 职级定制完成--职级顺序号校验
     *
     * @param orderNumList
     * @return
     */
    public String checkStaffRank(List<Integer> orderNumList) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            int temp = 0;
            for (int i = 0; i < orderNumList.size(); i++) {
                for (int j = 0; j < -i - 1; j++) {
                    if (orderNumList.get(j) > orderNumList.get(j + 1)) {
                        temp = orderNumList.get(j);
                        orderNumList.set(j, orderNumList.get(j + 1));
                        orderNumList.set(j + 1, orderNumList.get(j));
                    }
                }
            }
            if (orderNumList.size() > 0) {
                if (orderNumList.get(0) != 1) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "职级顺序号须从1开始，为连续数字。");
                    return JSON.toJSONString(resultMap);
                }
            }
            for (int i = 0; i < orderNumList.size() - 1; i++) {
                if (!((orderNumList.get(i) + 1) == orderNumList.get(i + 1))) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "职级顺序号须从1开始，为连续数字。");
                    return JSON.toJSONString(resultMap);
                }
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "职级保存成功。");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            Log.info(e.getMessage());
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 新增员工职级
     *
     * @param token
     * @param fcBusPersonTypeInfo
     * @return
     */
    @Transactional
    public String insertStaffRank(String token, FCBusPersonType fcBusPersonTypeInfo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String error = isCheckRank(fcBusPersonTypeInfo);
            if (StringUtils.isNotBlank(error)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "新增员工职级失败：" + error);
                return JSON.toJSONString(resultMap);
            }
            fcBusPersonTypeInfo.setOperator(globalInput.getUserNo());
            fcBusPersonTypeInfo = CommonUtil.initObject(fcBusPersonTypeInfo, "INSERT");
            int i = fcBusPersonTypeMapper.insertSelective(fcBusPersonTypeInfo);
            if (i > 0) {
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "新增员工职级成功。");
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "新增员工职级失败。");
            }
        } catch (Exception e) {
            Log.info(e.getMessage());
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 新增员工职级  校验职级编号是否重复
     *
     * @param fcBusPersonType
     * @return
     */
    public String isCheckRank(FCBusPersonType fcBusPersonType) {
        String error = "";
        String reg = "^[1-9]\\d*$";
        if (StringUtils.isBlank(fcBusPersonType.getGradeLevelCode())) {
            error = "职级编号不可为空，请录入。";
            return error;
        }
        if (StringUtils.isBlank(fcBusPersonType.getGradeDesc())) {
            error = "职级描述不可为空，请录入。";
            return error;
        }
        if (StringUtils.isBlank(fcBusPersonType.getOrderNum())) {
            error = "职级顺序号不可为空，请录入。";
            return error;
        }
        if (!fcBusPersonType.getOrderNum().matches(reg)) {
            error = "职级顺序号只能是正整数，请重新录入。";
            return error;
        }
        if (!fcBusPersonType.getGradeLevelCode().matches("^[a-z0-9A-Z]+$")) {
            error = "职级编号只能是字母或数字，请重新录入。";
            return error;
        }
        List<FCBusPersonType> fcBusPersonTypelist = fcBusPersonTypeMapper.selectByGrpNo(fcBusPersonType.getGrpNo());
        if (fcBusPersonTypelist.size() > 0) {
            List<String> GradeLevelCodeList = new ArrayList<>();
            for (FCBusPersonType fcBusPersonInfo : fcBusPersonTypelist) {
                if (fcBusPersonType.getOrderNum().equals(fcBusPersonInfo.getOrderNum())) {
                    error = "职级顺序号已存在，请重新录入。";
                    return error;
                }
                if (fcBusPersonType.getGradeLevelCode().equals(fcBusPersonInfo.getGradeLevelCode())) {
                    error = "职级编号" + fcBusPersonInfo.getGradeLevelCode() + "已存在，请重新录入。";
                    return error;
                }
            }
        }
        return error;
    }

    /**
     * 删除员工职级
     *
     * @param token
     * @param ensureCode
     * @param gradeLevelCode
     * @return
     */
    @Transactional
    public String deleteStaffRank(String token, String ensureCode, String gradeLevelCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtils.isBlank(ensureCode) || StringUtils.isBlank(gradeLevelCode)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "职级删除失败：请求参数为空！");
                return JSON.toJSONString(resultMap);
            }
            int i = fcBusPersonTypeMapper.deleteByPrimaryKey(ensureCode, gradeLevelCode);
            if (i > 0) {
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "员工职级删除成功。");
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "未查询到该职级，请刷新页面重新进行删除。");
            }
        } catch (Exception e) {
            Log.info(e.getMessage());
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 配置投保对象---新增层级
     *
     * @param token
     * @param map
     * @param isCheck 判断用户是否修改维度 修改 删除已经存在的所有层级及相关险种信息  0--未修改  1--修改
     * @return
     */
    @Transactional
    public String insertHierarchy(String token, Map<String, Object> map, String isCheck) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            GlobalInput globalInput = userService.getSession(token);
            String error = "";
            String Sex = String.valueOf(map.get("sex"));
            String Retirement = String.valueOf(map.get("retirement"));
            String GradeLevelTopLimit = String.valueOf(map.get("gradeLevelTopLimit"));
            String GradeLevelLowLimit = String.valueOf(map.get("gradeLevelLowLimit"));
            String OccupationTypeTopLimit = String.valueOf(map.get("occupationTypeTopLimit"));
            String OccupationTypeLowLimit = String.valueOf(map.get("occupationTypeLowLimit"));
            String AgeTopLimit = String.valueOf(map.get("ageTopLimit"));
            String AgeLowLimit = String.valueOf(map.get("ageLowLimit"));
            String ComeAgeTopLimit = String.valueOf(map.get("comeAgeTopLimit"));
            String ComeAgeLowLimit = String.valueOf(map.get("comeAgeLowLimit"));
            String ensureCode = String.valueOf(map.get("ensureCode"));
            String grpNo = String.valueOf(map.get("grpNo"));
            String insuredType = map.get("insuredType").toString();
            if (StringUtils.isNotBlank(GradeLevelLowLimit)) {
                if (StringUtils.isBlank(GradeLevelTopLimit)) {
                    resultMap.put("message", "职级下限已录，职级上限必录。");
                    return JSON.toJSONString(resultMap);
                }
                if (Double.parseDouble(GradeLevelLowLimit) < Double.parseDouble(GradeLevelTopLimit)) {
                    resultMap.put("message", "职级下限不能大于职级上限。");
                    return JSON.toJSONString(resultMap);
                }
            }
            if (StringUtils.isNotBlank(OccupationTypeLowLimit)) {
                if (Double.parseDouble(OccupationTypeLowLimit) > Double.parseDouble(OccupationTypeTopLimit)) {
                    resultMap.put("message", "职业类别下限不能大于上限。");
                    return JSON.toJSONString(resultMap);
                }

            }
            if (StringUtils.isNotBlank(AgeTopLimit)) {
                if (Double.parseDouble(AgeTopLimit) < Double.parseDouble(AgeLowLimit)) {
                    resultMap.put("message", "投保年龄下限不能大于上限。");
                    return JSON.toJSONString(resultMap);
                }
                if (!AgeTopLimit.matches("^\\d+$")) {
                    resultMap.put("message", "投保年龄上限应为大于等于0的整数。");
                    return JSON.toJSONString(resultMap);
                }
                if (!AgeLowLimit.matches("^\\d+$")) {
                    resultMap.put("message", "投保年龄下限应为大于等于0的整数。");
                    return JSON.toJSONString(resultMap);
                }
            }
            if (StringUtils.isNotBlank(ComeAgeTopLimit)) {
                if (Double.parseDouble(ComeAgeTopLimit) < Double.parseDouble(ComeAgeLowLimit)) {
                    resultMap.put("message", "服务年限下限不能大于上限。");
                    return JSON.toJSONString(resultMap);
                }
                if (!ComeAgeTopLimit.matches("^\\d+$")) {
                    resultMap.put("message", "服务年限上限应为大于等于0的整数。");
                    return JSON.toJSONString(resultMap);
                }
                if (!ComeAgeLowLimit.matches("^\\d+$")) {
                    resultMap.put("message", "服务年限下限应为大于等于0的整数。");
                    return JSON.toJSONString(resultMap);
                }
            }
            String serialNo = "";
            String message = "";
            //根据层级流水号是否为空判断保存是属于新增还是修改   二期需求  修改 删除该层级相关信息，将新的层级信息进行存储
            if (StringUtils.isBlank(String.valueOf(map.get("serialNo")))) {
                serialNo = maxNoService.createMaxNo("HierarchySerialNo", null, 20);
                message = "新增层级成功。";
                if ("1".equals(isCheck)) {
                    fcBusinessProDutyGrpObjectMapper.deleteByEnsureCode(ensureCode, insuredType);
                }
            } else {
                serialNo = String.valueOf(map.get("serialNo"));
                message = "修改层级成功。";
                Map<String, String> delMapInfo = new HashMap<>();
                delMapInfo.put("serialNo", serialNo);
                fcBusinessProDutyGrpObjectMapper.deleteByPrimaryKey(delMapInfo);
            }
            List<FcBusinessProDutyGrpObject> fcBusinessProDutyGrpObjectList = new ArrayList<>();
            Object objects = map.get("proDutyGrpObjectList");
            List<Map<String, String>> proDutyGrpObjectListMapInfo = (List<Map<String, String>>) objects;
            for (Map hierarchyInfo : proDutyGrpObjectListMapInfo) {
                FcBusinessProDutyGrpObject proDutyGrpObjectInfo = JSON.parseObject(JSON.toJSONString(hierarchyInfo), FcBusinessProDutyGrpObject.class);
                proDutyGrpObjectInfo.setSerialNo(serialNo);
                proDutyGrpObjectInfo.setEnsureCode(ensureCode);
                proDutyGrpObjectInfo.setGrpNo(grpNo);
                proDutyGrpObjectInfo.setGradeLevelTopLimit(GradeLevelTopLimit);
                proDutyGrpObjectInfo.setGradeLevelLowLimit(GradeLevelLowLimit);
                proDutyGrpObjectInfo.setOccupationTypeTopLimit(StringUtils.isNotBlank(OccupationTypeTopLimit) ? OccupationTypeTopLimit : "");
                proDutyGrpObjectInfo.setOccupationTypeLowLimit(StringUtils.isNotBlank(OccupationTypeLowLimit) ? OccupationTypeLowLimit : "");
                proDutyGrpObjectInfo.setAgeTopLimit(AgeTopLimit);
                proDutyGrpObjectInfo.setAgeLowLimit(AgeLowLimit);
                proDutyGrpObjectInfo.setComeAgeTopLimit(ComeAgeTopLimit);
                proDutyGrpObjectInfo.setComeAgeLowLimit(ComeAgeLowLimit);
                proDutyGrpObjectInfo.setInsuredType(map.get("insuredType").toString());
                proDutyGrpObjectInfo.setSex(Sex);
                Double amnt = Double.parseDouble(hierarchyInfo.get("amnt").toString());
                proDutyGrpObjectInfo.setAmnt(amnt);
                proDutyGrpObjectInfo.setRetirement(Retirement);
                proDutyGrpObjectInfo.setOperator(globalInput.getUserNo());
                proDutyGrpObjectInfo = CommonUtil.initObject(proDutyGrpObjectInfo, "INSERT");
                fcBusinessProDutyGrpObjectList.add(proDutyGrpObjectInfo);
            }
            if (fcBusinessProDutyGrpObjectList.size() > 0) {
                fcBusinessProDutyGrpObjectMapper.insertList(fcBusinessProDutyGrpObjectList);
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", message);
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            Log.info(e.getMessage());
            throw new RuntimeException();
        }
    }

    /**
     * 查询投保层级
     *
     * @param token
     * @param ensureCode
     * @param isCheck
     * @return
     */
    public String selectHierarchy(String token, String ensureCode, String isCheck) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            String grpNo = fcEnsure.getGrpNo();
            String error = isCheckPlanConfig(ensureCode, "0");
            if (StringUtils.isNotBlank(error)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", error);
                return JSON.toJSONString(resultMap);
            }
            //查询已配置层级相关维度信息
            if ("01".equals(isCheck) || "02".equals(isCheck)) {
                List<FcBusinessProDutyGrpObject> fcBusinessProDutyGrpObjectList = fcBusinessProDutyGrpObjectMapper.selectByGrpNo(grpNo, isCheck, ensureCode);
                resultMap.put("data", fcBusinessProDutyGrpObjectList);
            } else {
                List<FcBusinessProDutyGrpObject> perList = fcBusinessProDutyGrpObjectMapper.selectByGrpNo(grpNo, "01", ensureCode);
                List<FcBusinessProDutyGrpObject> familyList = fcBusinessProDutyGrpObjectMapper.selectByGrpNo(grpNo, "02", ensureCode);
                resultMap.put("perData", perList);
                resultMap.put("familyData", familyList);
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("success", "层级列表查询成功。");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "层级列表查询失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 校验险种 职级  投保对象是否配置完成
     *
     * @param ensureCode
     * @param isCheck    0--配置投保对象时调用  1--弹性计划配置完成时调用
     * @return
     */
    public String isCheckPlanConfig(String ensureCode, String isCheck) {
        String error = "";
        Map<String, String> mapInfo = new HashMap<>();
        mapInfo.put("ensureCode", ensureCode);
        mapInfo.put("isCheck", isCheck);
        List<FcPlanRiskInfo> riskList = fcPlanRiskInfoMapper.getAddedRiskInfo(mapInfo);
        if (riskList.size() < 1) {
            error = "请先新增险种。";
            return error;
        }
        List<FCBusPersonType> fcBusPersonTypeList = fcBusPersonTypeMapper.selectByGrpNo(fcEnsureMapper.selectByPrimaryKey(ensureCode).getGrpNo());
        if (fcBusPersonTypeList.size() < 1) {
            error = "请先定制员工职级。";
            return error;
        }
        if ("1".equals(isCheck)) {
            List<FcBusinessProDutyGrpObject> fcBusinessProDutyGrpObjectList = fcBusinessProDutyGrpObjectMapper.selectByEnsureCode(ensureCode, "");
            if (fcBusinessProDutyGrpObjectList.size() < 1) {
                error = "请先配置投保对象。";
                return error;
            }
        }
        return error;
    }

    /**
     * 新增层级时查询该福利下已配置的险种信息  勾选层级后根据流水号查询该层级下绑定的相关险种及配置信息
     *
     * @param Authorization
     * @param ensureCode
     * @return
     */
    public String getDutyAmountGradeByEnsureCode(String Authorization, int pageNo, int pageSize, String ensureCode, String serialNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("serialNo", serialNo);
            List<FCBusPersonType> fcBusPersonTypelist = fcBusPersonTypeMapper.selectByGrpNo(fcEnsureMapper.selectByPrimaryKey(ensureCode).getGrpNo());
            List<FcDutyAmountGrade> fcDutyAmountGradeList = fcBusinessProDutyGrpObjectMapper.getFcDutyAmountGradeListByEnsureCode(mapInfo);
            List<FcDutyAmountGrade> fcDutyAmountGrades = fcDutyAmountGradeList;
            if (fcDutyAmountGradeList.size() > 0) {
                resultMap.put("BusList", fcBusPersonTypelist);
                resultMap.put("recordsTotal", fcDutyAmountGradeList.size());
                resultMap.put("dutyList", fcDutyAmountGradeList);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "层级险种信息查询成功。");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            Log.info(e.getMessage());
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询所选层级险种信息(修改调用)
     *
     * @param Authorization
     * @param ensureCode
     * @param serialNo
     * @return
     */
    public String getAllDutyAmountGradeBySerialNo(String Authorization, String ensureCode, String serialNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(Authorization);
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("serialNo", serialNo);
            List<FCBusPersonType> fcBusPersonTypelist = fcBusPersonTypeMapper.selectByGrpNo(fcEnsureMapper.selectByPrimaryKey(ensureCode).getGrpNo());
            List<FcDutyAmountGrade> fcBusinessProDutyGrpObjectList = fcBusinessProDutyGrpObjectMapper.getAllDutyAmountGradeBySerialNo(mapInfo);
            if (fcBusinessProDutyGrpObjectList.size() > 0) {
                resultMap.put("BusList", fcBusPersonTypelist);
                resultMap.put("dutyList", fcBusinessProDutyGrpObjectList);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "查询所选层级险种信息查询成功。");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            Log.info(e.getMessage());
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询所选层级险种信息(回显调用)
     *
     * @param Authorization
     * @param ensureCode
     * @param serialNo
     * @return
     */
    public String getDutyAmountGradeBySerialNo(String Authorization, String ensureCode, String serialNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("serialNo", serialNo);
            List<FCBusPersonType> fcBusPersonTypelist = fcBusPersonTypeMapper.selectByGrpNo(fcEnsureMapper.selectByPrimaryKey(ensureCode).getGrpNo());
            List<FcBusinessProDutyGrpObject> fcBusinessProDutyGrpObjectList = fcBusinessProDutyGrpObjectMapper.getDutyAmountGradeBySerialNo(mapInfo);
            if (fcBusinessProDutyGrpObjectList.size() > 0) {
                resultMap.put("BusList", fcBusPersonTypelist);
                resultMap.put("dutyList", fcBusinessProDutyGrpObjectList);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "查询所选层级险种信息查询成功。");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            Log.info(e.getMessage());
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 删除投保对象层级
     *
     * @param Authorization
     * @param serialNo
     * @return
     */
    @Transactional
    public String deleteDutyGrpObjectBySerialNo(String Authorization, String serialNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtils.isBlank(serialNo)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "投保对象层级删除失败：请求参数为空。");
                return JSON.toJSONString(resultMap);
            }
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("serialNo", serialNo);
            int i = fcBusinessProDutyGrpObjectMapper.deleteByPrimaryKey(mapInfo);
            if (i > 0) {
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "投保对象层级删除成功。");
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "投保对象层级删除失败：未查询到该投保层级，请刷新页面后再次进行删除。");
            }
        } catch (Exception e) {
            Log.info(e.getMessage());
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 弹性计划配置完成
     *
     * @param token
     * @param ensureCode
     * @return
     */
    @Transactional
    public String eflexEnsureComplete(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String error = "";
            error = isCheckPlanConfig(ensureCode, "1");
            if (StringUtils.isNotBlank(error)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "弹性计划配置失败：" + error);
                return JSON.toJSONString(resultMap);
            }
            //判断险种是否下架
            String riskStopSale = ensureMakeService.checkRiskStopSale(ensureCode);
            if (!StringUtil.isEmpty(riskStopSale)) {
                resultMap.put("success", Boolean.FALSE);
                resultMap.put("code", "500");
                resultMap.put("message", riskStopSale);
                return JSON.toJSONString(resultMap);
            }
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            //首次提交
            if (ConstantUtil.EnsureState_07.equals(fcEnsure.getEnsureState())) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_08);
            }    //HR退回后台定制
            else if (ConstantUtil.EnsureState_010.equals(fcEnsure.getEnsureState()) || ConstantUtil.EnsureState_011.equals(fcEnsure.getEnsureState())) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_08);
                //删除退回原因  退回来源
                fcEnsureConfigMapper.deleteByEnsureCode(ensureCode);
            } else {
                error = "福利状态有误，请联系平台维护人员进行处理。";
            }
            if (StringUtils.isNotBlank(error)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "弹性计划配置失败：" + error);
                return JSON.toJSONString(resultMap);
            }
            fcEnsure.setOperator(globalInput.getUserNo());
            fcEnsure = (FCEnsure) CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKey(fcEnsure);
            Log.info("弹性计划:" + ensureCode + "福利状态更新完成。。。" + fcEnsure.getEnsureState());
            Map<String, Object> mapInfo = new HashMap<>();
            mapInfo.put("grpNo", fcEnsure.getGrpNo());
            mapInfo.put("contactType", "01");
            List<FcGrpContact> fcGrpContact = fcGrpContactMapper.selectContactsInfo(mapInfo);
            String Phone = fcGrpContact.get(0).getMobilePhone();
            //短信发送
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(fcEnsure.getGrpNo());
            SendSMSReq sendSMSReq = new SendSMSReq();
            sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_011.getCode());
            sendSMSReq.setPhones(Phone);
            Map<String, Object> map = new HashMap<>();
            map.put("grp_name", fcGrpInfo.getGrpName());
            map.put("ensure_name", fcEnsure.getEnsureName());
            sendSMSReq.setParam(map);
            sendMessageService.sendSMS(sendSMSReq);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "福利制定提交成功");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            Log.info(e.getMessage());
            throw new RuntimeException();
        }
    }

    /**
     * 导出投保清单EXCEL
     *
     * @param authorization
     * @param response
     */
    public String eflexExportPerInfoExcel(String authorization, HttpServletResponse response, String ensureCode, String grpNo, String name, String sex, String iDType, String iDNo) {
        ResponseMsg<Object> responseMsg = new ResponseMsg<>();
        try {
            if (StringUtils.isBlank(ensureCode) || StringUtils.isBlank(grpNo)) {
                responseMsg.code("-1").message("请求参数有误，人员清单导出失败！");
                return JSON.toJSONString(responseMsg);
            }
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("name", name);
            mapInfo.put("sex", sex);
            mapInfo.put("iDType", iDType);
            mapInfo.put("iDNo", iDNo);
            FCFileDocMain fcFileDocMain = fcFileDocMainMapper.selectByPrimaryKey("00000016");
            String filePath = fcFileDocMain.getFilePath();
            String filename = fcFileDocMain.getFileSaveName() + fcFileDocMain.getFileSuffix();
            File file = new File(filePath + filename);
            Workbook wb = WorkbookFactory.create(new FileInputStream(file));
            // 调用生成excel生成工具类,返回前台下载
            ServletOutputStream outputStream = response.getOutputStream();
            String filenameF = new String((fcFileDocMain.getFileName() + fcFileDocMain.getFileSuffix()).getBytes("UTF-8"), "iso-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + filenameF);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            // excel表格数据
            wb = perInfoSheet(wb, mapInfo);
            wb.write(outputStream);
            outputStream.flush();
            responseMsg.okStatus().message("导出人员清单EXCEL成功!");
        } catch (Exception e) {
            Log.info("导出人员清单EXCEL失败!", e);
            responseMsg.errorStatus().message("导出人员清单EXCEL失败!");
        }
        return JSON.toJSONString(responseMsg);
    }

	public Workbook perInfoSheet(Workbook wb,Map<String,String> mapInfo){
		try {
		//获取证件类型和证件号码的Value-Key
		List<HashMap<String, Object>> idTypeCodeList = fdCodeMapper.CodeInfo("IDType");
		//获取员工关系
		List<HashMap<String,Object>> relationMap = fdCodeMapper.CodeInfo("Relation");
		//员工清单
		List<FCPerInfoTemp> fcPerInfoTempList = fcPerInfoTempMapper.getPerInfoByEnsureCode(mapInfo);
		//家属清单
		List<Map<String,String>> fcPerinfoFamilyTempList = fcPerinfoFamilyTempMapper.getPerInfoByEnsureCode(mapInfo);
		//职级信息
		List<FCBusPersonType> fcBusPersonTypeList = fcBusPersonTypeMapper.selectByGrpNo(fcEnsureMapper.selectByPrimaryKey(mapInfo.get("ensureCode")).getGrpNo());
		// 员工清单sheet页
		Sheet staSheet = (Sheet) wb.getSheetAt(0);
		if (fcPerInfoTempList.size()>0){
			int rowIndex;
			for (int i = 0;i<fcPerInfoTempList.size();i++){
				rowIndex = i + 1;
				Row row = staSheet.createRow(rowIndex+1);
				row.setHeightInPoints((float) 20);
				CellStyle inCellStyle = this.cellStyleBorder(wb);
				// 创建员工清单页单元格
				for (int rowNum = 0; rowNum < 18; rowNum++) {
					Cell cell = row.createCell(rowNum);
					cell.setCellStyle(inCellStyle);
				}
				row.getCell(0).setCellValue(rowIndex);
				row.getCell(1).setCellValue(fcPerInfoTempList.get(i).getName());
				row.getCell(2).setCellValue(fcPerInfoTempList.get(i).getDepartment());
				if ("0".equals(fcPerInfoTempList.get(i).getSex())){
					row.getCell(3).setCellValue("男");
				}else if ("1".equals(fcPerInfoTempList.get(i).getSex())){
					row.getCell(3).setCellValue("女");
				}
				row.getCell(4).setCellValue(fcPerInfoTempList.get(i).getBirthDay());
				row.getCell(5).setCellValue(MaskUtils.maskPhone(fcPerInfoTempList.get(i).getMobilePhone()));
				String idTypeName = "";
				for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
					if (fcPerInfoTempList.get(i).getIDType().equals(hashMap1.get("CodeKey").toString())) {
						idTypeName  = hashMap1.get("CodeName").toString();
						break;
					}
				}
				row.getCell(6).setCellValue(idTypeName);
				row.getCell(7).setCellValue(MaskUtils.maskIDCard(fcPerInfoTempList.get(i).getIDNo()));
				row.getCell(8).setCellValue(fcPerInfoTempList.get(i).getIdTypeEndDate());
				String nativePlace = fdCodeMapper.selectNameByCode("nativeplace",fcPerInfoTempList.get(i).getNativeplace());
				row.getCell(9).setCellValue(nativePlace);
				Map<String,String> busPersonMap = new HashMap<>();
				//通过福利编码查询出企业
				FCEnsure fcEnsure1 = fcEnsureMapper.selectByPrimaryKey(mapInfo.get("ensureCode"));
				String grpNo = fcEnsure1.getGrpNo();
				busPersonMap.put("grpNo",grpNo);
				busPersonMap.put("orderNum",fcPerInfoTempList.get(i).getLevelCode());
				FCBusPersonType fcBusPersonType = fcBusPersonTypeMapper.selectByPrimaryKey(busPersonMap);
				row.getCell(10).setCellValue(fcBusPersonType.getGradeLevelCode());
				row.getCell(11).setCellValue(fcPerInfoTempList.get(i).getOccupationType());
				row.getCell(12).setCellValue(fcPerInfoTempList.get(i).getOccupationCode());
				if ("0".equals(fcPerInfoTempList.get(i).getJoinMedProtect())){
					row.getCell(13).setCellValue("无");
				} else if ("1".equals(fcPerInfoTempList.get(i).getJoinMedProtect())) {
					row.getCell(13).setCellValue("有");
				}
				row.getCell(14).setCellValue(fcPerInfoTempList.get(i).getServiceTerm());
				if ("0".equals(fcPerInfoTempList.get(i).getRetirement())){
					row.getCell(15).setCellValue("是");
				} else if ("1".equals(fcPerInfoTempList.get(i).getRetirement())) {
					row.getCell(15).setCellValue("否");
				}
				row.getCell(16).setCellValue(fcPerInfoTempList.get(i).getStaffGrpPrem() == null ? "":String.valueOf(fcPerInfoTempList.get(i).getStaffGrpPrem()));
				row.getCell(17).setCellValue(fcPerInfoTempList.get(i).getFamilyGrpPrem() == null ? "":String.valueOf(fcPerInfoTempList.get(i).getFamilyGrpPrem()));
			}
		}
		// 家属sheet页
		Sheet famSheet = (Sheet) wb.getSheetAt(1);
		if (fcPerinfoFamilyTempList.size()>0){
			int rowIndex = 1;
			for (Map<String,String> map:fcPerinfoFamilyTempList){
				Row row = famSheet.createRow(rowIndex+1);
				row.setHeightInPoints((float) 20);
				CellStyle inCellStyle = this.cellStyleBorder(wb);
				// 创建家属清单页单元格
				for (int rowNum = 0; rowNum < 15; rowNum++) {
					Cell cell = row.createCell(rowNum);
					cell.setCellStyle(inCellStyle);
				}
				row.getCell(0).setCellValue(rowIndex);
				row.getCell(1).setCellValue(map.get("name"));
				if ("0".equals(map.get("sex"))){
					row.getCell(2).setCellValue("男");
				}else if ("1".equals(map.get("sex"))){
					row.getCell(2).setCellValue("女");
				}
				row.getCell(3).setCellValue(map.get("birthDay"));
				String idTypeName = "";
				String perIDTypeName = "";
				for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
					if (map.get("idType").equals(hashMap1.get("CodeKey").toString())) {
						idTypeName  = hashMap1.get("CodeName").toString();
					}
					if (map.get("perIDType").equals(hashMap1.get("CodeKey").toString())) {
						perIDTypeName  = hashMap1.get("CodeName").toString();
					}
				}
				row.getCell(4).setCellValue(idTypeName);
				row.getCell(5).setCellValue(map.get("idNo"));
				row.getCell(6).setCellValue(map.get("idTypeEndDate"));
				String nativePlace = fdCodeMapper.selectNameByCode("nativeplace",map.get("nativePlace"));
				row.getCell(7).setCellValue(nativePlace);
				row.getCell(8).setCellValue(map.get("phone"));
				row.getCell(9).setCellValue(map.get("occupationType"));
				row.getCell(10).setCellValue(map.get("occupationCode"));
				if ("0".equals(map.get("JoinMedProtect"))){
					row.getCell(11).setCellValue("无");
				} else if ("1".equals(map.get("JoinMedProtect"))) {
					row.getCell(11).setCellValue("有");
				}
				String relationName = "";
				for (HashMap<String, Object> hashMap1 : relationMap) {
					if (map.get("relation").equals(hashMap1.get("CodeKey").toString())) {
						relationName  = hashMap1.get("CodeName").toString();
						break;
					}
				}
				row.getCell(12).setCellValue(relationName);
				row.getCell(13).setCellValue(perIDTypeName);
				row.getCell(14).setCellValue(map.get("perIDNo"));
				rowIndex++;
			}
		}
			//职级Sheet页
			Sheet rankSheet = (Sheet) wb.getSheetAt(5);
			if (fcBusPersonTypeList.size() > 0){
				int rowIndex;
				for (int i = 0;i<fcBusPersonTypeList.size();i++){
					rowIndex = i + 1;
					Row row = rankSheet.createRow(i+1);
					row.setHeightInPoints((float) 20);
					CellStyle inCellStyle = this.cellStyleBorder(wb);
					// 创建职级清单页单元格
					for (int rowNum = 0; rowNum < 4; rowNum++) {
						Cell cell = row.createCell(rowNum);
						cell.setCellStyle(inCellStyle);
					}
					row.getCell(0).setCellValue(rowIndex);
					row.getCell(1).setCellValue(fcBusPersonTypeList.get(i).getGradeLevelCode());
					row.getCell(2).setCellValue(fcBusPersonTypeList.get(i).getGradeDesc());
					row.getCell(3).setCellValue(fcBusPersonTypeList.get(i).getOrderNum());
				}
			}
		}catch (Exception e){
			Log.info(e.getMessage());
		}
		return wb;
	}

    /**
     * 表格样式设置
     *
     * @param wb
     * @return
     */
    private CellStyle cellStyleBorder(Workbook wb) {
        CellStyle inCellStyle = wb.createCellStyle();
        inCellStyle.setBorderBottom(BorderStyle.THIN);
        inCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderLeft(BorderStyle.THIN);
        inCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderRight(BorderStyle.THIN);
        inCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderTop(BorderStyle.THIN);
        inCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        return inCellStyle;
    }

    /**
     * 维护弹性福利联系人信息
     *
     * @param token
     * @param param
     * @return
     */
    @Transactional
    public String maintainEnsureContactInfo(String token, Map<String, String> param) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            String name = param.get("name");
            String idType = param.get("idType");
            String idNo = param.get("idNo");
            /**
             * 数据校验
             */
            // 校验姓名
            param.put("name", param.get("name").trim().replaceAll(" +", " "));
            if (!StringUtil.isEmpty(idType) && !idType.equals("1")) {
                String s = CheckUtils.checkChineseName(name);
                if (!StringUtil.isEmpty(s)) {
                    return JSON.toJSONString(ResultUtil.error(s));
                }
            } else if (!StringUtil.isEmpty(idType) && idType.equals("1")) {
                String s = CheckUtils.checkForeignName(name);
                String trim = name.trim();
                name = trim.replaceAll(" +", " ");
                param.put("name", name);
                if (!StringUtil.isEmpty(s)) {
                    return JSON.toJSONString(ResultUtil.error(s));
                }
            }
            // 校验联系人证件类型为“身份证”，输入的出生日期、性别和证件号码的不一致问题
            String sex = param.get("sex");
            // 校验证件号码
            if (StringUtils.isEmpty(idNo)) {
                return JSON.toJSONString(ResultUtil.error("证件号码不能为空！"));
            } else {
                // 身份证号+户口簿的校验规则一致
                if ("0".equals(idType) || "4".equals(idType)) {
                    // 经由核心、运营确认，身份证号仅能为18位 add by wudezhong 2021.5.27
                    if (idNo.length() != 18) {
                        return JSON.toJSONString(ResultUtil.error("证件号码长度应为18位！"));
                    }
                    // 校验身份证号
                    if (IDCardUtil.isIDCard(idNo)) {
                        // 判断性别
                        String idcard_sex = IDCardUtil.sex(idNo);
                        if (!idcard_sex.equals(sex)) {
                            return JSON.toJSONString(ResultUtil.error("证件号与性别不匹配！"));
                        }
                    }
                } else {
                    if (idNo.length() < 3) {
                        return JSON.toJSONString(ResultUtil.error("证件号码长度小于3位！"));
                    }
                }
            }
            // 校验手机号
            String mobilePhone = param.get("mobilePhone");
            if (!CheckUtils.checkMobilePhone(mobilePhone)) {
                return JSON.toJSONString(ResultUtil.error("联系人手机格式错误，请检查！"));
            }
            // 校验邮箱
            String email = param.get("email");
            if (!CheckUtils.checkEmail(email)) {
                return JSON.toJSONString(ResultUtil.error("联系人邮箱录入有误，请检查！"));
            }
            //****************
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = param.get("ensureCode");
            int staffCount = fcPerInfoTempMapper.selectHas(ensureCode);
            if (staffCount < 1) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "请导入人员信息。");
                return JSON.toJSONString(resultMap);
            }
            FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpContact(param.get("idNo"));
            FcEnsureContact fcEnsureContact = new FcEnsureContact();
            fcEnsureContact.setEnsureCode(ensureCode);
            fcEnsureContact.setName(param.get("name"));
            fcEnsureContact.setNativeplace(param.get("nativeplace"));
            fcEnsureContact.setDepartment(param.get("department"));
            fcEnsureContact.setEmail(param.get("email"));
            fcEnsureContact.setMobilePhone(param.get("mobilePhone"));
            fcEnsureContact.setIdNo(param.get("idNo"));
            fcEnsureContact.setIdType(param.get("idType"));
            fcEnsureContact.setSex(param.get("sex"));
            fcEnsureContact.setBirthDay(param.get("birthDay"));
            fcEnsureContact.setIdTypeEndDate(fcGrpContact.getIdTypeEndDate());
            fcEnsureContact.setIdTypeStartDate(fcGrpContact.getIdTypeStartDate());
            if (StringUtils.isNotBlank(param.get("povalidity"))) {
                fcEnsureContact.setIdTypeEndDate(param.get("povalidity"));
            }
            fcEnsureContact.setOperator(globalInput.getUserNo());
            ensureMakeService.maintainEnsureContact(fcEnsureContact);
            //更新企业人数
            int staffNum = Integer.valueOf(param.get("peoples"));
            if (param.get("peoples") == null) {
                staffNum = 0;
            }
            ensureMakeService.updateGrpPeoples(globalInput.getGrpNo(), staffNum);
            //更新福利投保人数
            if (!param.get("insuredNumber").matches("^\\d+$")) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "投保人数请录入正整数。");
                return JSON.toJSONString(resultMap);
            } else {
                double insuredNumber = Double.valueOf(param.get("insuredNumber"));
                if (insuredNumber <= 0) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "投保人数请录入正整数。");
                    return JSON.toJSONString(resultMap);
                }
            }
            FCEnsure fcEnsure = new FCEnsure();
            fcEnsure.setEnsureCode(ensureCode);
            fcEnsure.setClientNo(param.get("clientNo"));
            fcEnsure.setInsuredNumber(Integer.parseInt(param.get("insuredNumber")));
            fcEnsure = CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "福利联系人信息维护成功。");
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("福利联系人信息维护失败。");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }
}
