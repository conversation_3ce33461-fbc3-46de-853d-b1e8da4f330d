package com.sinosoft.eflex.service;

import ch.qos.logback.classic.Logger;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hqins.common.helper.BeanCopier;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.PlanStateEnum;
import com.sinosoft.eflex.enums.status.GrpOrderStatusEnum;
import com.sinosoft.eflex.enums.status.InsureStateEnum;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.PolicyUpdate.*;
import com.sinosoft.eflex.model.confirmInsure.PeopleInsurePlanInfo;
import com.sinosoft.eflex.model.pay.PayCenterPayNotify;
import com.sinosoft.eflex.model.policy.DataBody;
import com.sinosoft.eflex.model.policy.DataContentValue;
import com.sinosoft.eflex.model.remove.DataBadyRemove;
import com.sinosoft.eflex.model.remove.DataContentRemove;
import com.sinosoft.eflex.model.remove.DataContentValueRemove;
import com.sinosoft.eflex.model.review.DataBodyReview;
import com.sinosoft.eflex.model.review.DataContentReview;
import com.sinosoft.eflex.model.review.DataContentValueReview;
import com.sinosoft.eflex.model.review.Result;
import com.sinosoft.eflex.service.dailyinsure.DailyIssueService;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.encrypt.LisIDEA;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-08-24 17:56
 */
@Service
@Slf4j
public class SpanService {


    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private FCUserTokenMapper fcUserTokenMapper;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private FDAgentInfoMapper fdAgentInfoMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FCOrderItemMapper fcOrderItemMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCOrderMapper fcOrderMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FcGrpContactMapper fcGrpContactMapper;
    @Autowired
    private FcHrRegistTempMapper fcHrRegistTempMapper;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;
    @Autowired
    private MaxNoService maxNoService;

    @Autowired
    private FCOrderItemDetailMapper fcOrderItemDetailMapper;
    @Autowired
    private FcPrtandCoreRelaMapper fcPrtandCoreRelaMapper;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private FCPerInfoTempMapper fcPerInfoTempMapper;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FCPerinfoFamilyTempMapper fcPerinfoFamilyTempMapper;
    @Autowired
    private FCBusPersonTypeMapper fcBusPersonTypeMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private EnsureMakeService ensureMakeService;
    @Autowired
    private FCEnsurePlanMapper fcEnsurePlanMapper;
    @Autowired
    private InsureService insureService;
    @Autowired
    private FCOrderLocusMapper fcOrderLocusMapper;
    @Autowired
    private FcPerImpartResultMapper fcPerImpartResultMapper;
    @Autowired
    private FPInsurePlanMapper fpInsurePlanMapper;
    @Autowired
    private FCPerAppntMapper fcPerAppntMapper;
    @Autowired
    private FCPerRegistDayMapper fcPerRegistDayMapper;
    @Autowired
    private FCPlanRiskMapper fcPlanRiskMapper;
    @Autowired
    private FCPlanRiskDutyMapper fcPlanRiskDutyMapper;
    @Autowired
    private FcPlanConfigMapper fcPlanConfigMapper;
    @Autowired
    private FcEnsureContactMapper fcEnsureContactMapper;
    @Autowired
    private FCGrpApplicantMapper fcGrpApplicantMapper;

    private DailyIssueService dailyIssueService;

    public SpanService(DailyIssueService dailyIssueService) {
        this.dailyIssueService = dailyIssueService;
    }

    /**
     * <AUTHOR>
     * @description 获取用户的五要素
     * @date 17:42
     * @modified
     */
    public String getToken(String token) {

        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 判断获取的token是否为空
            if (token == null || "".equals(token)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "你输入的token为空");
                return JSON.toJSONString(resultMap);
            }
            String str = redisUtil.get(token);
            // 判断redis中是否存在token的数据
            if (StringUtil.isNotEmpty(str)) {
                // 将redis中获取的token信息转换成map集合类型
                Map<String, Object> map = JSONObject.parseObject(str);
                // 将获取的五要素信息返回给前台
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "请求成功");
                resultMap.put("data", map);
                // 修改token表中有效值及修改时间
                FCUserToken fcUserToken = new FCUserToken();
                fcUserToken.setToken(token);
                fcUserToken.setIsValid("0");
                fcUserToken = (FCUserToken) CommonUtil.initObject(fcUserToken, "UPDATE");
                fcUserTokenMapper.updateByPrimaryKeySelective(fcUserToken);
                // 将缓存数据移除
                redisUtil.remove(token);
                return JSON.toJSONString(resultMap);
            }
            // redis中没有数据，则到数据库中查询
            FCUserToken fcUserToken = fcUserTokenMapper.selectByPrimaryKey(token);
            // 判断token用户在数据表中是否存在
            if (StringUtil.isNotEmpty(fcUserToken)) {
                // 判断有效值是否有效
                if (fcUserToken.getIsValid().equals("0")) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "token不合法或已失效！");
                    return JSON.toJSONString(resultMap);
                }
                // 获取个人信息
                FCPerInfo fcPerInfo = fcPerInfoMapper.findUserInfo(fcUserToken.getUserNo());
                if (StringUtil.isEmpty(fcPerInfo)) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "用户不存在！");
                    return JSON.toJSONString(resultMap);
                }
                // 将获取的五要素信息返回给前台
                Map<String, Object> map = new HashMap<>();
                map.put("certificateNo", fcPerInfo.getIDNo());
                map.put("gender", fcPerInfo.getSex());
                map.put("birthday", fcPerInfo.getBirthDay());
                map.put("name", fcPerInfo.getName());
                map.put("certificateType", fcPerInfo.getIDType());
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "请求成功！");
                resultMap.put("data", map);
                // 修改token表中有效值及修改时间
                fcUserToken.setToken(token);
                fcUserToken.setIsValid("0");
                fcUserToken = (FCUserToken) CommonUtil.initObject(fcUserToken, "UPDATE");
                fcUserTokenMapper.updateByPrimaryKeySelective(fcUserToken);
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "token不合法或已失效！");
            }
        } catch (Exception e) {
            log.info("请求失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "请求失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description添加及修改代理人信息
     * @date 15:15 15:15
     * @modified
     */
    public String addAgentInfo(String requestInfo) {

        if (requestInfo == null || "".equals(requestInfo)) {
            return "请求报文为空！";
        }
        JSONObject obj = JSON.parseObject(requestInfo);
        String transRefGUID = obj.getString("transRefGUID");
        String transType = obj.getString("transType");
        String transExeDate = obj.getString("transExeDate");
        String transExeTime = obj.getString("transExeTime");
        List<FDAgentInfo> agentInfo = JSONArray.parseArray(obj.getJSONArray("AgentInfo").toString(), FDAgentInfo.class);
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        try {
            // 校验获取的交易ID是否为空
            if (transRefGUID == null || "".equals(transRefGUID)) {
                resultMap.put("resultCode", "500");
                resultMap.put("message", "交易ID不能为空");
                return JSON.toJSONString(resultMap);
            }
            // 校验获取的交易类型是否为空
            if (transType == null || "".equals(transType)) {
                resultMap.put("resultCode", "500");
                resultMap.put("message", "交易类型不能为空");
                return JSON.toJSONString(resultMap);
            }
            // 校验获取的交易日期是否为空
            if (StringUtil.isEmpty(transExeDate)) {
                resultMap.put("resultCode", "500");
                resultMap.put("message", "交易日期不能为空");
                return JSON.toJSONString(resultMap);
            }
            // 校验获取的交易时间是否为空
            if (StringUtil.isEmpty(transExeTime)) {
                resultMap.put("resultCode", "500");
                resultMap.put("message", "交易时间不能为空");
                return JSON.toJSONString(resultMap);
            }
            // 校验获取的代理人信息是否为空
            if (StringUtil.isEmpty(agentInfo)) {
                resultMap.put("resultCode", "500");
                resultMap.put("message", "代理人信息不能为空");
                return JSON.toJSONString(resultMap);
            }
            // 将交易ID，交易类型，交易日期，交易时间放入一个map集合
            resultMap.put("交易ID", transRefGUID);
            resultMap.put("交易类型", transType);
            resultMap.put("交易日期", transExeDate);
            resultMap.put("交易时间", transExeTime);
            // 定义一个更新的list对象
            List<FDAgentInfo> insertAgentInfoList = new LinkedList<>();
            // 定义一个插入的list对象
            List<FDAgentInfo> updateAgentInfoList = new ArrayList<>();
            // 遍历参数list对象
            for (FDAgentInfo fdAgentInfoList : agentInfo) {
                // 判断对象中的AgentCode是否在数据库中存在
//				FDAgentInfo fdAgentInfo = fdAgentInfoMapper.selectByPrimaryKey(fdAgentInfoList.getSerialNo());
                FDAgentInfo fdAgentInfo = fdAgentInfoMapper.selectByAgentCode(fdAgentInfoList.getAgentCode());
                if (fdAgentInfo != null) {
                    // 存在则进行批量更新操作
                    updateAgentInfoList.add(fdAgentInfoList);
                } else {
                    // 不存在则进行批量插入操作
                    insertAgentInfoList.add(fdAgentInfoList);
                }
            }
            if (updateAgentInfoList.size() > 0) {
                fdAgentInfoMapper.updateAgentInfo(updateAgentInfoList);
            }
            if (insertAgentInfoList.size() > 0) {
                fdAgentInfoMapper.insertAgentInfo(insertAgentInfoList);
            }
            map.put("resultCode", "200");
            map.put("resultInfo", "导入代理人信息成功");
            resultMap.put("transResult", map);
        } catch (Exception e) {
            log.info("导入数据失败", e);
            map.put("resultCode", "500");
            map.put("resultInfo", "导入代理人信息失败");
            resultMap.put("transResult", map);
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 批量投保（结果）回调接口
     *
     * @param backInfo
     * @return
     */
    @Transactional
    public String insureCallback(String backInfo) {
        if (backInfo == null || "".equals(backInfo)) {
            log.info("投保结果回调接口:请求报文为空！");
            return "投保结果回调接口:请求报文为空！";
        }

        // 解析核心承保返回结果
        JSONObject backObj = JSON.parseObject(backInfo);
        String transRefGUID = backObj.getString("transRefGUID");
        String transType = backObj.getString("transType");
        String PrtNo = backObj.getString("PrtNo");
        JSONObject transResult = backObj.getJSONObject("transResult");
        String resultCode = transResult.getString("resultCode");
        String resultInfo = transResult.getString("resultInfo");

        // 定义返回结果
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> transResultMap = new HashMap<>();

        resultMap.put("transRefGUID", transRefGUID);
        resultMap.put("transType", transType);
        resultMap.put("transExeDate", DateTimeUtil.getCurrentDate());
        resultMap.put("transExeTime", DateTimeUtil.getCurrentTime());
        try {
            // 校验请求参数
            if (PrtNo == null || "".equals(PrtNo)) {
                log.info("核心投保单号PrtNo不能为空！");
                transResultMap.put("resultCode", "500");
                transResultMap.put("resultInfo", "核心传输的投保单号PrtNo不能为空！");
                resultMap.put("transResult", transResultMap);
                return JSON.toJSONString(resultMap);
            }
            // 获取平台对应的投保单号
            FcPrtandCoreRela fcPrtandCoreRela = fcPrtandCoreRelaMapper.selectByTPrtNo(PrtNo);
            if (ObjectUtils.isEmpty(fcPrtandCoreRela)) {
                log.info("平台未查询到对应的保单请求记录，投保单号（" + PrtNo + "）。");
                transResultMap.put("resultCode", "500");
                transResultMap.put("resultInfo", "投保单号对应记录不存在");
                resultMap.put("transResult", transResultMap);
                return JSON.toJSONString(resultMap);
            }
            String prtNo = fcPrtandCoreRela.getPrtNo();
            if (prtNo == null || "".equals(prtNo)) {
                log.info("平台记录的投保单号PrtNo不存在");
                transResultMap.put("resultCode", "500");
                transResultMap.put("resultInfo", "投保单号PrtNo不存在");
                resultMap.put("transResult", transResultMap);
                return JSON.toJSONString(resultMap);
            }
            // 获取团单信息
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectPrtNo(PrtNo);
            String ensureCode = fcGrpOrder.getEnsureCode();
            String grpOrderNo = fcGrpOrder.getGrpOrderNo();

            if ("200".equals(resultCode)) {
                log.info("resultCode::" + resultCode);
                // 01-已发送，02-承保成功，03-承保失败, 04-投保成功, 05-投保失败
                updateFcPrtAndCoreRela(PrtNo, "04", resultInfo);
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
                // 保单状态 1-投保中 2-待承保 3-已承保 4-承保结束 5-承保失败
                fcEnsure.setPolicyState("2");
                fcEnsure = CommonUtil.initObject(fcEnsure, "UPDATE");
                fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
                //fpinsureplan 投保状态InsureState 0-未提交订单表 1-已提交订单表 2-提交核心成功
                String sql1 = "update fpinsureplan set InsureState = '2',ModifyDate = '" + DateTimeUtil.getCurrentDate() + "',ModifyTime = '" + DateTimeUtil.getCurrentTime() + "' where EnsureCode = '" + ensureCode + "'";
                jdbcTemplate.execute(sql1);
                // GrpOrderStatus 团体订单状态01-未提交 02-已提交 03-提交失败 04-投保成功 05-投保失败
                String sql2 = "update fcgrporder set GrpOrderStatus = '02',ModifyDate = '" + DateTimeUtil.getCurrentDate() + "',ModifyTime = '" + DateTimeUtil.getCurrentTime() + "' where GrpOrderNo = '" + grpOrderNo + "'";
                jdbcTemplate.execute(sql2);
                // OrderStatus 订单状态01-待提交核心 02-已提交核心
                String sql3 = "update fcorder set OrderStatus = '02',ModifyDate = '" + DateTimeUtil.getCurrentDate() + "',ModifyTime = '" + DateTimeUtil.getCurrentTime() + "' where GrpOrderNo = '" + grpOrderNo + "'";
                jdbcTemplate.execute(sql3);

                log.info("投保单号" + PrtNo + "投保成功");

            } else {
                // 01-已发送，02-承保成功，03-承保失败, 04-投保成功, 05-投保失败
                updateFcPrtAndCoreRela(PrtNo, "05", resultInfo);
                // GrpOrderStatus 团体订单状态01-未提交 02-已提交 03-提交失败 04-投保成功 05-投保失败
                String sql2 = "update fcgrporder set GrpOrderStatus = '03',ModifyDate = '" + DateTimeUtil.getCurrentDate() + "',ModifyTime = '" + DateTimeUtil.getCurrentTime() + "' where GrpOrderNo = '" + grpOrderNo + "'";
                jdbcTemplate.execute(sql2);
                log.info("投保单号" + PrtNo + "投保交易失败");
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
                // 保单状态 1-投保中 2-待承保 3-已承保 4-承保结束 5-承保失败
                fcEnsure.setPolicyState("5");
                fcEnsure = CommonUtil.initObject(fcEnsure, "UPDATE");
                fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
            }
            transResultMap.put("resultCode", "200");
            transResultMap.put("resultInfo", "批量投保（结果）回调接口回调成功！投保单号" + PrtNo);
            resultMap.put("transResult", transResultMap);
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("投保单号" + PrtNo + "投保交易失败", e);
            throw new RuntimeException();
        }
    }


    /**
     * <AUTHOR>
     * @description承保结果反馈及修改状态
     * @date 15:18 15:18
     * @modified
     */
    public String updatePolicyStatus(String backInfo) {

        if (backInfo == null || "".equals(backInfo)) {
            return "请求报文为空！";
        }
        JSONObject backObj = JSON.parseObject(backInfo);
        JSONObject policyInfo = backObj.getJSONObject("PolicyResult");

        String PrtNo = policyInfo.getString("PrtNo");
        // PolicyStatus = 1
        String policyStatus = policyInfo.getString("PolicyStatus");
        String remark = policyInfo.getString("Remark");
        String grpContNo = policyInfo.getString("GrpContNo");

        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> transResultMap = new HashMap<>();
        try {
            // 校验承保结果是否为空
            String PolicyResult = checkNull(PrtNo, grpContNo, policyStatus, remark, backObj);
            resultMap.put("TransRefGUID", backObj.getString("TransRefGUID"));
            resultMap.put("TransType", backObj.getString("TransType"));
            resultMap.put("TransExeDate", DateTimeUtil.getCurrentDate());
            resultMap.put("TransExeTime", DateTimeUtil.getCurrentTime());
            if (PolicyResult == null || "".equals(PolicyResult)) {
                // 查询投保单号是否存在
                FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectPrtNo(PrtNo);
                // 订单存在
                if (fcGrpOrder != null) {
                    // 核心签单成功
                    if ("1".equals(policyStatus)) {
                        // 记录关联表状态 01-已发送，02-承保成功，03-承保失败, 04-投保成功, 05-投保失败
                        updateFcPrtAndCoreRela(PrtNo, "02", "承保成功");
                        // 修改福利状态
                        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(fcGrpOrder.getEnsureCode());
                        // 保单状态 1-投保中 2-待承保 3-已承保 4-承保结束 5-承保失败
                        fcEnsure.setPolicyState("3");
                        fcEnsureMapper.updateByPrimaryKeySelective(CommonUtil.initObject(fcEnsure, "UPDATE"));
                        // 团体订单状态 01-未提交 02-已提交 03-提交失败 04-投保成功 05-投保失败
                        fcGrpOrder.setGrpOrderStatus(GrpOrderStatusEnum.INSURE_SUCCESS.getCode());
                        fcGrpOrder.setGrpContNo(grpContNo);
                        // 修改对象的状态
                        fcGrpOrderMapper.updateByPrimaryKey(CommonUtil.initObject(fcGrpOrder, "UPDATE"));

                        /**
                         * 补充子订单信息
                         */
                        String sql2 = "update fcorderitem set grpcontno = '" + grpContNo + "' where OrderNo in (select OrderNo from fcorder where GrpOrderNo = '" + fcGrpOrder.getGrpOrderNo() + "')";
                        jdbcTemplate.execute(sql2);
                        fcGrpOrderMapper.updateByPrimaryKey(CommonUtil.initObject(fcGrpOrder, "UPDATE"));
//                        //补充子订单信息
//                        List<FCOrder> fcOrders = fcOrderMapper.selectOrderListByGrpOrderNo(fcGrpOrder.getGrpOrderNo());
//                        List<String> orderNoList = fcOrders.stream().map(FCOrder::getOrderNo).collect(Collectors.toList());
//                        Lists.partition(orderNoList, 500).forEach(s -> fcOrderItemMapper.updateByGrpContNo(grpContNo, s));
                    } else {
                        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(fcGrpOrder.getEnsureCode());
                        // 保单状态 1-投保中 2-待承保 3-已承保 4-承保结束 5-承保失败
                        fcEnsure.setPolicyState("5");
                        fcEnsureMapper.updateByPrimaryKeySelective(CommonUtil.initObject(fcEnsure, "UPDATE"));
                        // 01-已发送，02-承保成功，03-承保失败, 04-投保成功, 05-投保失败
                        updateFcPrtAndCoreRela(PrtNo, "03", "承保失败");
                        // 团体订单状态 01-未提交 02-已提交 03-提交失败 04-投保成功 05-投保失败
                        fcGrpOrder.setGrpOrderStatus("05");
                        // 修改对象的状态
                        fcGrpOrderMapper.updateByPrimaryKey(CommonUtil.initObject(fcGrpOrder, "UPDATE"));
                    }
                } else {
                    //更新平台与核心投保单号对照关系表状态为"发送失败"
                    transResultMap.put("resultCode", "500");
                    transResultMap.put("resultInfo", "此订单不存在");
                    resultMap.put("transResult", transResultMap);
                    return JSON.toJSONString(resultMap);
                }
                transResultMap.put("resultCode", "200");
                transResultMap.put("resultInfo", "承保状态修改成功");
                resultMap.put("transResult", transResultMap);
            } else {
                // 返回校验结果
                return PolicyResult;
            }
        } catch (Exception e) {
            log.info("承保状态修改失败", e);
            transResultMap.put("resultCode", "500");
            transResultMap.put("resultInfo", "承保状态修改失败");
            resultMap.put("transResult", transResultMap);
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 01-已发送，02-承保成功，03-承保失败 , 04 投保成功 05 投保失败
     *
     * @param tprtNo
     * @param status
     */
    public void updateFcPrtAndCoreRela(String tprtNo, String status, String describe) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("tPrtNo", tprtNo);//核心投保单号
        map.put("status", status);//发送状态
        map.put("describe", describe); // 状态描述
        fcGrpOrderMapper.updateFcPrtAndCoreRela(map);
    }

    public String checkNull(String PrtNo, String grpContNo, String policyStatus, String remark, Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        //校验获取的交易ID是否为空
        if (map.get("TransRefGUID") == null || "".equals(map.get("TransRefGUID"))) {
            resultMap.put("resultCode", "500");
            resultMap.put("message", "交易ID不能为空");
            return JSON.toJSONString(resultMap);
        }
        //校验获取的交易类型是否为空
        if (map.get("TransType") == null || "".equals(map.get("TransType"))) {
            resultMap.put("resultCode", "500");
            resultMap.put("message", "交易类型不能为空");
            return JSON.toJSONString(resultMap);
        }
        //校验获取的交易日期是否为空
        if (StringUtil.isEmpty(map.get("TransExeDate"))) {
            resultMap.put("resultCode", "500");
            resultMap.put("message", "交易日期不能为空");
            return JSON.toJSONString(resultMap);
        }
        //校验获取的交易时间是否为空
        if (StringUtil.isEmpty(map.get("TransExeTime"))) {
            resultMap.put("resultCode", "500");
            resultMap.put("message", "交易时间不能为空");
            return JSON.toJSONString(resultMap);
        }
        if (PrtNo == null || "".equals(PrtNo)) {
            resultMap.put("resultCode", "500");
            resultMap.put("message", "投保单号不能为空");
            return JSON.toJSONString(resultMap);
        }
        if (policyStatus == null || "".equals(policyStatus)) {
            resultMap.put("resultCode", "500");
            resultMap.put("message", "承保状态不能为空");
            return JSON.toJSONString(resultMap);
        }
        if (remark == null || "".equals(remark)) {
            resultMap.put("resultCode", "500");
            resultMap.put("message", "描述信息不能为空");
            return JSON.toJSONString(resultMap);
        }
        return "";
    }

    public String payStatusNotice(String requestInfo) {
        try {
            if (requestInfo == null) {
                return "FALSE";
            }
            PayCenterPayNotify payCenterPayNotify = JSONObject.toJavaObject(JSONObject.parseObject(requestInfo), PayCenterPayNotify.class);
            String orderNo = payCenterPayNotify.getBusinessNo();
            String payStatus = payCenterPayNotify.getPayStatus();
            String payType = payCenterPayNotify.getPayType();
            //首先判断订单状态是否为“待支付”、“支付中”、“支付失败”、“待人工核保”
            FCOrder fcOrder = fcOrderMapper.selectByPrimaryKey(orderNo);
            String orderStatus = fcOrder.getOrderStatus();
            log.info("回调开始时的支付状态为》》》》》》》》{}，payStatus>>>>>>>>>{}", orderStatus, payStatus);
            String orderStatu = CheckUtils.changePayStatus(payStatus, orderStatus);
            if (orderStatus.matches("^0(4|5|7)$") || orderStatus.equals("010") || orderStatus.equals("015")) {
                if (payType.equals("1")) {//微信支付
                    Map<String, Object> paraMap = new HashMap<String, Object>();
                    paraMap.put("orderNo", orderNo);
                    paraMap.put("payType", "03");
                    paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                    paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                    fcOrderMapper.updateFcorderpayIsValid(paraMap);
                } else if (payType.equals("2")) {//银行卡实时支付
                    String errMsg = checkBankInfo(payCenterPayNotify);
                    if (!"".equals(errMsg)) return errMsg;
                    Map<String, Object> paraMap = new HashMap<String, Object>();
                    paraMap.put("orderNo", orderNo);
                    paraMap.put("payType", "04");
                    paraMap.put("BankCode", payCenterPayNotify.getBankCode());
                    paraMap.put("BankAccType", payCenterPayNotify.getBankAccType());
                    paraMap.put("BankAccNo", payCenterPayNotify.getBankAccNo());
                    paraMap.put("BankAccName", payCenterPayNotify.getBankAccName());
                    paraMap.put("bankAccPhone", payCenterPayNotify.getBankAccPhone());
                    paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                    paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                    fcOrderMapper.updateFcorderpayIsValid(paraMap);
                } else if (payType.equals("3")) {//银行卡批扣
                    String errMsg = checkBankInfo(payCenterPayNotify);
                    if (!"".equals(errMsg)) return errMsg;
                    Map<String, Object> paraMap = new HashMap<String, Object>();
                    paraMap.put("orderNo", orderNo);
                    paraMap.put("payType", "05");
                    paraMap.put("BankCode", payCenterPayNotify.getBankCode());
                    paraMap.put("BankAccType", payCenterPayNotify.getBankAccType());
                    paraMap.put("BankAccNo", payCenterPayNotify.getBankAccNo());
                    paraMap.put("BankAccName", payCenterPayNotify.getBankAccName());
                    paraMap.put("bankAccPhone", payCenterPayNotify.getBankAccPhone());
                    paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                    paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                    fcOrderMapper.updateFcorderpayIsValid(paraMap);
                }
                fcOrderMapper.updateFcorder(orderStatu, orderNo, DateTimeUtil.getCurrentDate(), DateTimeUtil.getCurrentTime());
                Map<String, String> insureInfo = fcOrderMapper.getInsureInfo(orderNo);
                Map<String, Object> insureResultMap = dailyIssueService.dailyNaturalSignBill(insureInfo.get("ensureCode"), payType.equals("3") ? "YF0004" : "YF0005", insureInfo.get("perNo"), insureInfo.get("orderItemNo"));
                String insureResult = String.valueOf(insureResultMap.get("result"));
                String insureFlag = JSONObject.parseObject(insureResult).getString("flag");
                if (insureFlag.equals("0")) {
                    System.out.println("这个最终的状态:" + fcOrder.getOrderStatus());
                    //成功
                    if (payType.equals("3")) {
                        //自核通过则订单状态为待扣费,自核不通过则订单状态为待人工核保
                        fcOrderMapper.updateFcorder(fcOrder.getOrderStatus().equals("015") ? "010" : "09", orderNo, DateTimeUtil.getCurrentDate(), DateTimeUtil.getCurrentTime());
                    } else {
                        //投保成功
                        fcOrderMapper.updateFcorder("08", orderNo, DateTimeUtil.getCurrentDate(), DateTimeUtil.getCurrentTime());
                    }

                } else {
                    fcOrderMapper.updateFcorder("014", orderNo, DateTimeUtil.getCurrentDate(), DateTimeUtil.getCurrentTime());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "FALSE";
        }
        return "SUCCESS";
    }

    public String checkBankInfo(PayCenterPayNotify payCenterPayNotify) {
        String errMsg = "";
        if (StringUtil.isEmpty(payCenterPayNotify.getBankCode())) {
            errMsg = "银行编码不能为空";
        }
        if (StringUtil.isEmpty(payCenterPayNotify.getBankAccType())) {
            errMsg = "".equals(errMsg) ? errMsg + ",银行账户类型不能为空" : "银行账户类型不能为空";
        }
        if (StringUtil.isEmpty(payCenterPayNotify.getBankAccNo())) {
            errMsg = "".equals(errMsg) ? errMsg + ",银行账号不能为空" : "银行账号不能为空";
        }
        if (StringUtil.isEmpty(payCenterPayNotify.getBankAccName())) {
            errMsg = "".equals(errMsg) ? errMsg + ",银行账户姓名不能为空" : "银行账户姓名不能为空";
        }
        if (StringUtil.isEmpty(payCenterPayNotify.getBankAccPhone())) {
            errMsg = "".equals(errMsg) ? errMsg + ",银行账户预留手机号不能为空" : "银行账户预留手机号不能为空。";
        }
        return errMsg;
    }

    public String insureStatusNotice(Result<String> result) {
        try {
            if (!StringUtil.isEmpty(result)) {
                /**
                 *   2020/6/8    接口信息调整
                 *       dataContent
                 * 	     dataHead
                 * 	     两部分合并到body中
                 */
                //获取接口返回的状态码 0--成功
                int messageCode = result.getCode();
                if (messageCode == 0) {
                    DataBody dataBody = JSONObject.toJavaObject(JSONObject.parseObject(result.getBody()), DataBody.class);
                    com.sinosoft.eflex.model.policy.DataContent dataContent = dataBody.getDataContent();
                    List<DataContentValue> value = dataContent.getValue();
                    if (StringUtil.isEmpty(value) || value.size() < 1) {
                        return "FALSE";
                    } else {
                        for (DataContentValue dataContentValue : value) {
                            try {
                                String getpStateid = dataContentValue.getpStateId();//签单状态，53：成功，其他编码失败
                                String policyCode = dataContentValue.getPolicyCode();//保单号
                                if (getpStateid.equals("53")) {//承保成功
                                    //更新订单状态为“投保成功 ”
                                    Map<String, Object> paraMap = new HashMap<String, Object>();
                                    paraMap.put("contNo", policyCode);
                                    paraMap.put("orderStatus", "08");
                                    paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                                    paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                                    fcOrderMapper.updateFcorderByConNo(paraMap);
                                    //更新子订单保费
                                    paraMap.clear();
                                    paraMap.put("contNo", policyCode);
                                    paraMap.put("configNo", "021");
                                    String payType = fcEnsureConfigMapper.selectOnlyValueByContNo(paraMap);
                                    if (!StringUtil.isEmpty(payType)) {
                                        paraMap.clear();
                                        if (payType.equals("0")) {//企业定期结算
                                            paraMap.put("grpPrem", dataContentValue.getInsFee());
                                        } else if (payType.equals("1")) {//个人支付
                                            paraMap.put("selfPrem", dataContentValue.getInsFee());
                                        }
                                        paraMap.put("contNo", policyCode);
                                        paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                                        paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                                        if (!StringUtil.isEmpty(dataContentValue.getInsAmnt())) {
                                            paraMap.put("insuredAmount", CommonUtil.div(Double.valueOf(dataContentValue.getInsAmnt()), 10000.0, 2) + "");
                                            fcOrderItemDetailMapper.updateInsureAmountContNo(paraMap);
                                        }
                                        fcOrderItemMapper.updateFcorderItemByContNo(paraMap);
                                    } else {
                                        continue;
                                    }
                                } else {//承保失败
                                    //更新订单状态为“投保失败 ”
                                    Map<String, Object> paraMap = new HashMap<String, Object>();
                                    paraMap.put("contNo", policyCode);
                                    paraMap.put("orderStatus", "012");
                                    paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                                    paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                                    fcOrderMapper.updateFcorderByConNo(paraMap);
                                }
                            } catch (Exception e) {
                                continue;
                            }
                        }
                    }
                } else {
                    return "FALSE";
                }
            } else {
                return "FALSE";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "FALSE";
        }
        return "SUCCESS";
    }

    public String reviewStatusNotice(Result<String> result) {

        try {
            if (!StringUtil.isEmpty(result)) {
                /**
                 *   2020/6/8   自然人投保-核保结论通知接口   接口调整
                 *      dataContent
                 * 		dataHead
                 * 	 	两部分合并到body中
                 */
                //获取接口返回的状态码 0--成功
                int messageCode = result.getCode();
                if (messageCode == 0) {
                    DataBodyReview dataBodyReview = JSONObject.toJavaObject(JSONObject.parseObject(result.getBody()), DataBodyReview.class);
                    DataContentReview dataContent = dataBodyReview.getDataContent();
                    if (!StringUtil.isEmpty(dataContent)) {
                        List<DataContentValueReview> value = dataContent.getValue();
                        if (StringUtil.isEmpty(value) || value.size() < 1) {
                            return "FALSE";
                        } else {
                            for (DataContentValueReview dataContentValue : value) {
                                try {
                                    String approved = dataContentValue.getApproved();
                                    String policyCode = dataContentValue.getPolicyCode();//分单号
                                    //更新当前分单之前的核保结论为无效，并插入最新的数据
                                    Map<String, Object> paraMap = new HashMap<String, Object>();
                                    paraMap.put("contNo", policyCode);
                                    paraMap.put("ReviewState", "0");
                                    paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                                    paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                                    fcOrderMapper.updateFCOrderReview(paraMap);
                                    paraMap.clear();
                                    String orderReviewNo = maxNoService.createMaxNo("OrderReviewNo", null, 20);
                                    paraMap.put("OrderReviewNo", orderReviewNo);
                                    paraMap.put("contNo", policyCode);
                                    paraMap.put("Prem", dataContentValue.getCoverage().get(0).getAddPrem());
                                    paraMap.put("ReviewState", "1");
                                    paraMap.put("Approved", approved);
                                    paraMap.put("FailedCause", dataContentValue.getFailedCause());
                                    paraMap.put("Operator", "");
                                    paraMap.put("OperatorCom", "");
                                    paraMap.put("MakeDate", DateTimeUtil.getCurrentDate());
                                    paraMap.put("MakeTime", DateTimeUtil.getCurrentTime());
                                    paraMap.put("ModifyDate", DateTimeUtil.getCurrentDate());
                                    paraMap.put("ModifyTime", DateTimeUtil.getCurrentTime());
                                    fcOrderMapper.insertFCOrderReview(paraMap);

                                    if (approved.equals("1") || approved.equals("a")) {
                                        //更新订单状态为“拒保 ”或“撤保”
                                        paraMap.clear();
                                        paraMap.put("contNo", policyCode);
                                        paraMap.put("orderStatus", approved.equals("1") ? "011" : "013");  //011-拒保 012-延期 013-撤单
                                        paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                                        paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                                        fcOrderMapper.updateFcorderByConNo(paraMap);
                                    }
                                    if (approved.equals("2")) {
                                        //更新订单状态为“延期”
                                        paraMap.clear();
                                        paraMap.put("contNo", policyCode);
                                        paraMap.put("orderStatus", "012");  //011-拒保 012-延期 013-撤单
                                        paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                                        paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                                        fcOrderMapper.updateFcorderByConNo(paraMap);
                                    }
                                } catch (Exception e) {
                                    continue;
                                }
                            }
                        }
                    } else {
                        return "FALSE";
                    }
                } else {
                    return "FALSE";
                }
            } else {
                return "FALSE";
            }
        } catch (Exception e) {
            return "FALSE";
        }
        return "SUCCESS";
    }

    public String removeParts(Result<String> result) {
        try {
            if (!StringUtil.isEmpty(result)) {
                //获取接口返回的状态码 0--成功
                int messageCode = result.getCode();
                if (messageCode == 0) {
                    System.out.println(result);
                    System.out.println(result.getBody());
                    DataBadyRemove dataBadyRemove = JSONObject.toJavaObject(JSONObject.parseObject(result.getBody()), DataBadyRemove.class);
                    DataContentRemove dataContent = dataBadyRemove.getDataContent();
                    List<DataContentValueRemove> value = dataContent.getValue();
                    if (StringUtil.isEmpty(value) || value.size() < 1) {
                        return "FALSE";
                    } else {
                        for (DataContentValueRemove dataContentValueRemove : value) {
                            try {
                                //保单号
                                String contno = dataContentValueRemove.getContno();
                                String prem = dataContentValueRemove.getPrem();
                                String uid = dataContentValueRemove.getUid();
                                String cancleTime = dataContentValueRemove.getCancleTime();
                                //撤件原因
                                String cancleCause = dataContentValueRemove.getCancelCause();
                                log.info("保单号为：ContNo:" + contno + "代理人工号为：" + uid + "撤件日期为:" + cancleTime);
                                if (!StringUtil.isEmpty(contno)) {
                                    //更新订单状态为“撤件 ”
                                    Map<String, Object> paraMap = new HashMap<String, Object>();
                                    paraMap.put("contNo", contno);
                                    paraMap.put("orderStatus", "016");
                                    paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                                    paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                                    fcOrderMapper.updateFcorderByConNo(paraMap);
                                    //更新当前分单之前的核保结论为无效，并插入最新的数据
                                    paraMap.clear();
                                    paraMap.put("contNo", contno);
                                    paraMap.put("ReviewState", "0");
                                    paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                                    paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                                    fcOrderMapper.updateFCOrderReview(paraMap);
                                    paraMap.clear();
                                    String orderReviewNo = maxNoService.createMaxNo("OrderReviewNo", null, 20);
                                    paraMap.put("OrderReviewNo", orderReviewNo);
                                    paraMap.put("contNo", contno);
                                    paraMap.put("Prem", prem);
                                    paraMap.put("ReviewState", "1");
                                    //人核结论 1-拒保;2-延期;3-加费承保;4-特约承保;5-未通过自动核保;9-标准承保;a-撤保;z-核保订正  此处加一个 c-撤件
                                    paraMap.put("Approved", "c");
                                    paraMap.put("FailedCause", cancleCause);
                                    paraMap.put("Operator", "");
                                    paraMap.put("OperatorCom", "");
                                    paraMap.put("MakeDate", DateTimeUtil.getCurrentDate());
                                    paraMap.put("MakeTime", DateTimeUtil.getCurrentTime());
                                    paraMap.put("ModifyDate", DateTimeUtil.getCurrentDate());
                                    paraMap.put("ModifyTime", DateTimeUtil.getCurrentTime());
                                    fcOrderMapper.insertFCOrderReview(paraMap);
                                } else {
                                    continue;
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                                continue;
                            }
                        }
                    }
                } else {
                    return "FALSE";
                }
            } else {
                return "FALSE";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "FALSE";
        }
        return "SUCCESS";
    }

    /**
     * 处理注册临时表
     */
    public String dealRegistTemp(String grpNo) {
        // 处理指定的企业信息
        if (!StringUtils.isEmpty(grpNo)) {
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(grpNo);
            FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpInfo(grpNo);

            FcHrRegistTemp fcHrRegistTemp = fcHrRegistTempMapper.selectHrRegistTemp(fcGrpInfo.getUnifiedsociCode(), fcGrpContact.getIdNo());
        } else {
            // 处理临时表的信息（从企业信息表、企业联系人表同步至临时表）

        }
        return null;
    }


    public String syncPolicyUpdate(UpdateRequest request) {
        if (request != null && request.getDataContent() != null && request.getDataContent().getValue() != null && request.getDataContent().getValue().getBody() != null) {
            //判断Pojo里的三个属性那个不空那个需要更新
            GrpEdorSendResultPojo resultPojo = request.getDataContent().getValue().getBody();
            String grpContNo = resultPojo.getGrpContNo();
            String edorType = resultPojo.getEdorType();
            //投保单位信息
            GrpAppntInfo grpAppntInfo = resultPojo.getGrpAppntInfo();
            //机构信息（客户身份识别）
            GrpInstitutionalInfo grpInstitutionalInfo = resultPojo.getGrpInstitutionalInfo();
            //被保人信息列表
            List<GrpInsuredInfo> grpInsuredInfoList = resultPojo.getGrpInsuredInfoList();
            //保险计划
            List<GrpContPlanInfo> grpContPlanInfoList = resultPojo.getGrpContPlanInfoList();

            //根据grpContNo 去查福利号
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByGrpContNo(grpContNo);

            if (null != fcGrpOrder) {

                if (grpAppntInfo != null) {
                    //更新投保单位信息
                    syncGrpAppntInfo(fcGrpOrder, grpAppntInfo);
                }
                if (grpInstitutionalInfo != null) {
                    //更新机构信息
                    //syncGrpInstitutionalInfo(fcGrpOrder,grpInstitutionalInfo);
                }
                if (CollectionUtils.isNotEmpty(grpInsuredInfoList)) {
                    //更新被保人信息列表
                    CompletableFuture.runAsync(() -> syncGrpInsuredInfo(fcGrpOrder, grpInsuredInfoList, edorType));
                }
                if (CollectionUtils.isNotEmpty(grpContPlanInfoList)) {
                    syncGrpContPlanInfo(fcGrpOrder, grpContPlanInfoList);
                }
                if (edorType.equals("CT")) {
                    fcOrderMapper.updateByGrpOrderNo(fcGrpOrder.getGrpOrderNo());
                    fcEnsureMapper.updateEnsureState(fcGrpOrder.getEnsureCode());
                }
            }

        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("resultCode", "200");
        resultMap.put("resultInfo", "保全信息同步成功!!");
        return JSON.toJSONString(resultMap);
    }


    private void syncGrpContPlanInfo(FCGrpOrder fcGrpOrder, List<GrpContPlanInfo> grpContPlanInfoList) {

        String ensureCode = fcGrpOrder.getEnsureCode();

        List<FCEnsurePlan> fcEnsurePlanList = new ArrayList<>();
        List<FCPlanRisk> fcPlanRiskList = new ArrayList<>();
        List<FCPlanRiskDuty> fcPlanRiskDutyList = new ArrayList<>();
        List<FcPlanConfig> configInstList = new ArrayList<>();
        List<FcPlanConfig> configList = fcPlanConfigMapper.selectFCPlanConfigByEnsureCode(ensureCode);


        for (GrpContPlanInfo grpContPlanInfo : grpContPlanInfoList) {
            String contPlanName = grpContPlanInfo.getContPlanName();
            String contPlanCode = grpContPlanInfo.getContPlanCode();

            //险种分组
            Map<String, List<GrpContPlanDetailInfo>> collect = grpContPlanInfo.getGrpContPlanDetailInfoList().stream().collect(Collectors.groupingBy(GrpContPlanDetailInfo::getRiskCode));

            double totalPrem = 0.0;

            for (Map.Entry<String, List<GrpContPlanDetailInfo>> entry : collect.entrySet()) {
                String riskCode = entry.getKey();
                FCPlanRisk fcPlanRisk = new FCPlanRisk();
                fcPlanRisk.setEnsureCode(ensureCode);
                fcPlanRisk.setPlanCode(contPlanCode);
                fcPlanRisk.setRiskCode(riskCode);
                fcPlanRisk.setReinsuranceMark("0");
                fcPlanRisk.setGiftInsureSign("0");
                fcPlanRisk.setFeeRatio(0.0);
                fcPlanRisk.setCommissionOrAllowanceRatio(0.0);
                fcPlanRisk.setOperator("admin");


                //责任分组
                Map<String, List<GrpContPlanDetailInfo>> dutyMap = entry.getValue().stream().collect(Collectors.groupingBy(GrpContPlanDetailInfo::getDutyCode));
                for (Map.Entry<String, List<GrpContPlanDetailInfo>> dutyEntry : dutyMap.entrySet()) {
                    String dutyCode = dutyEntry.getKey();

                    FCPlanRiskDuty fcPlanRiskDuty = new FCPlanRiskDuty();
                    fcPlanRiskDuty.setEnsureCode(ensureCode);
                    fcPlanRiskDuty.setPlanCode(contPlanCode);
                    fcPlanRiskDuty.setRiskCode(riskCode);
                    fcPlanRiskDuty.setDutyCode(dutyCode);
                    fcPlanRiskDuty.setOperator("admin");

                    //计算
                    List<GrpContPlanDetailInfo> dutyDetailInfoList = dutyEntry.getValue();
                    for (GrpContPlanDetailInfo grpContPlanDetailInfo : dutyDetailInfoList) {
                        switch (grpContPlanDetailInfo.getFactorName()) {
                            case "保费":
                                double prem = Double.parseDouble(grpContPlanDetailInfo.getCalFactorValue());
                                fcPlanRiskDuty.setPrem(prem);
                                totalPrem += prem;
                                break;
                            case "计算规则":
                                break;
                            case "免赔额":
                                fcPlanRiskDuty.setGetLimit(Double.parseDouble(grpContPlanDetailInfo.getCalFactorValue()));
                                break;
                            case "免赔额属性":
                                fcPlanRiskDuty.setGetLimitType(grpContPlanDetailInfo.getCalFactorValue());
                                break;
                            case "赔付比例":
                                fcPlanRiskDuty.setGetRatio(Double.parseDouble(grpContPlanDetailInfo.getCalFactorValue()));
                                break;
                            case "保险期间":
                                break;
                            case "保险期间单位":
                                break;
                            case "最大赔付天数":
                                fcPlanRiskDuty.setMaxGetDay(BigDecimal.valueOf(Double.parseDouble(grpContPlanDetailInfo.getCalFactorValue())));
                                break;
                            case "免赔天数":
                                break;
                            case "保额":
                            case "重症住院津贴日额":
                            case "住院津贴日额":
                                fcPlanRiskDuty.setAmnt(Double.parseDouble(grpContPlanDetailInfo.getCalFactorValue()));
                                break;

                            default:
                                break;
                        }
                    }
                    CommonUtil.initObject(fcPlanRiskDuty, "INSERT");
                    fcPlanRiskDutyList.add(fcPlanRiskDuty);

                }
                CommonUtil.initObject(fcPlanRisk, "INSERT");
                fcPlanRiskList.add(fcPlanRisk);
            }
            //循环configList复制到configInstList里面
            configList.forEach(config -> {
                FcPlanConfig fcPlanConfig = BeanCopier.copyObject(config, FcPlanConfig.class);
                fcPlanConfig.setPlanCode(contPlanCode);
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                configInstList.add(fcPlanConfig);
            });


            FCEnsurePlan fcEnsurePlan = new FCEnsurePlan();
            fcEnsurePlan.setPlanCode(contPlanCode);
            fcEnsurePlan.setPlanName(contPlanName);
            fcEnsurePlan.setEnsureCode(ensureCode);
            fcEnsurePlan.setPlanKey(" ");
            fcEnsurePlan.setPlanState(PlanStateEnum.MAKEDONE.getCode());
            fcEnsurePlan.setPlanObject("1");
            fcEnsurePlan.setTotalPrem(totalPrem);
            fcEnsurePlan.setInsuredNumber(0);
            fcEnsurePlan.setOperator("admin");
            CommonUtil.initObject(fcEnsurePlan, "INSERT");
            fcEnsurePlanList.add(fcEnsurePlan);

        }


        if (CollectionUtils.isNotEmpty(fcEnsurePlanList)) {
            fcEnsurePlanMapper.insertList(fcEnsurePlanList);
        }

        if (CollectionUtils.isNotEmpty(fcPlanRiskList)) {
            fcPlanRiskMapper.insertList2(fcPlanRiskList);
        }
        if (CollectionUtils.isNotEmpty(fcPlanRiskDutyList)) {
            fcPlanRiskDutyMapper.insertList(fcPlanRiskDutyList);
        }
        if (CollectionUtils.isNotEmpty(configInstList)) {
            fcPlanConfigMapper.insert(configInstList);
        }


    }

    private void syncGrpAppntInfo(FCGrpOrder fcGrpOrder, GrpAppntInfo grpAppntInfo) {
        //根据grpNo查询详情
        FCGrpInfo grpInfo = fcGrpInfoMapper.selectGrpInfo2(fcGrpOrder.getGrpNo());

        //法定代表人/负责人信息
        GrpCorporationInfo grpCorporationInfo = grpAppntInfo.getGrpCorporationInfo();

        //授权经办人信息
        GrpLinkManInfo grpLinkManInfo = grpAppntInfo.getGrpLinkManInfo();
        FcGrpContact grpContact = fcGrpContactMapper.selectGrpContactByIdNoAndGrpNo(grpLinkManInfo.getInsContOldIDNo1(), grpInfo.getGrpNo());

        //更新企业信息法人信息
        makeGrpInfo(grpAppntInfo, grpInfo, grpCorporationInfo);
        fcGrpInfoMapper.updateByPrimaryKeySelective(grpInfo);

        List<FCGrpApplicant> fcGrpApplicant = fcGrpApplicantMapper.selectByGrpNo(fcGrpOrder.getGrpNo());
        for (FCGrpApplicant grpApplicant : fcGrpApplicant) {
            BeanUtils.copyProperties(grpInfo, grpApplicant);
            fcGrpApplicantMapper.updateByPrimaryKeySelective(grpApplicant);
        }
        //更新hr信息
        if (grpContact != null) {
            FdUser user = fdUserMapper.selectByIdNoAndType(grpLinkManInfo.getInsContOldIDNo1(), "2");
            makeHrInfo(grpLinkManInfo, grpContact, user);
            //更新user表
            fcGrpContactMapper.updateByPrimaryKeySelective(grpContact);

            FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByEnsureCode(fcGrpOrder.getEnsureCode());
            BeanUtils.copyProperties(grpContact, fcEnsureContact);

            fdUserMapper.updateByPrimaryKey(user);

            fcEnsureContactMapper.updateByPrimaryKeySelective(fcEnsureContact);
        }


    }


    private void syncGrpInstitutionalInfo(FCGrpOrder fcGrpOrder, GrpInstitutionalInfo grpInstitutionalInfo) {
        //根据grpNo查询详情
        FCGrpInfo grpInfo = fcGrpInfoMapper.selectGrpInfo2(fcGrpOrder.getGrpNo());


        //法定代表人信息
        GrpOrgPersonsInfo corporation = grpInstitutionalInfo.getCorporationList().get(grpInstitutionalInfo.getCorporationList().size() - 1);
        //授权经办人信息
        GrpOrgPersonsInfo linkMan = grpInstitutionalInfo.getLinkManList().get(grpInstitutionalInfo.getLinkManList().size() - 1);


        grpInfo.setLegID(corporation.getIdNo());
        grpInfo.setLegIDType(corporation.getIdType());
        grpInfo.setCorporationMan(corporation.getName());

        grpInfo.setGrpName(grpInstitutionalInfo.getOrgName());
        grpInfo.setUnifiedsociCode(grpInstitutionalInfo.getSocialCreditCode());
        grpInfo.setGrpType(grpInstitutionalInfo.getGrpNatureNew());
        grpInfo.setTrade(grpInstitutionalInfo.getBusinessTypeNew());


    }

    /**
     * 1.先去团体订单表根据 团单号查询福利号
     * <p>
     * 2. 然后在人员临时表根据福利号新增人员  区分员工和家属
     * <p>
     * 3.之后就根据福利号 获取需要同步的人数
     * <p>
     * 4.分别更新员工表和家属表以及计划表 订单表等
     * BB-被保人基本资料变更
     * IC-被保人重要资料变更
     * LC-保险计划变更
     * NI-新增被保险人
     * NZ-同质风险加减人
     * ZT-减少被保险人
     */
    @Async
    void syncGrpInsuredInfo(FCGrpOrder fcGrpOrder, List<GrpInsuredInfo> grpInsuredInfoList, String edorType) {


        //再根据保全项区分
        switch (edorType) {
            case "BB":
            case "IC":
                basicDataChange(edorType, grpInsuredInfoList);
                break;
            case "LC":
                planChange(fcGrpOrder, grpInsuredInfoList);
                break;
            case "NI":
                saveInsuranceChange(fcGrpOrder, grpInsuredInfoList);
                break;
            case "NZ":
                addSubPerson(fcGrpOrder, grpInsuredInfoList);
                break;
            case "ZT":
                reducePerson(fcGrpOrder, grpInsuredInfoList);
                break;
            default:
                break;
        }

    }

    private void addSubPerson(FCGrpOrder fcGrpOrder, List<GrpInsuredInfo> grpInsuredInfoList) {
        List<GrpInsuredInfo> addList = grpInsuredInfoList.stream().filter(x -> x.getAppFlag().equals("1")).collect(Collectors.toList());
        //家属
        List<GrpInsuredInfo> familySubList = grpInsuredInfoList.stream().filter(x -> x.getEmployeeNo() != null && x.getAppFlag().equals("4")).collect(Collectors.toList());
        //员工保单状态 1-有效保单  4-终止保单
        List<GrpInsuredInfo> userSubList = grpInsuredInfoList.stream().filter(x -> x.getEmployeeNo() == null && x.getAppFlag().equals("4")).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(addList)) {
            saveInsuranceChange(fcGrpOrder, addList);
        }

        if (CollectionUtils.isNotEmpty(familySubList)) {
            makeSubFanmily(fcGrpOrder, familySubList);
        }
        if (CollectionUtils.isNotEmpty(userSubList)) {
            reducePerson(fcGrpOrder, userSubList);
        }

    }

    private void makeSubFanmily(FCGrpOrder fcGrpOrder, List<GrpInsuredInfo> familySubList) {
        for (GrpInsuredInfo grpInsuredInfo : familySubList) {
            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByGrpOrderNoAndIdNo(fcGrpOrder.getGrpOrderNo(), grpInsuredInfo.getIdNo());
            FCPerinfoFamilyTemp fcPerinfoFamilyTemp = fcPerinfoFamilyTempMapper.selectByIdNoAndEnsureCode(fcGrpOrder.getEnsureCode(), grpInsuredInfo.getIdNo());

            if (null != fcPerinfoFamilyTemp) {
                fcPerinfoFamilyTempMapper.deleteByPerTempNo(fcPerinfoFamilyTemp.getPerTempNo());
            }

            if (fcOrderInsured != null) {
                fcOrderItemMapper.deleteByPrimaryKey(fcOrderInsured.getOrderItemNo());
            }
        }

    }


    /**
     * 更新 五要素  +   手机号  证件起止日期 国籍   职业代码和 职业类别
     *
     * @param grpInsuredInfoList
     */
    private void basicDataChange(String edorType, List<GrpInsuredInfo> grpInsuredInfoList) {


        List<FCOrderInsured> fcOrderInsuredList = grpInsuredInfoList.stream().map(obj ->
                FCOrderInsured.builder()
                        .edorType(edorType)
                        .oldIdNo(obj.getOldIdNo())
                        .name(obj.getName())
                        .IDNo(obj.getIdNo())
                        .IDType(obj.getIdType())
                        .sex(obj.getSex())
                        .birthday(obj.getBirthday())
                        .mobilePhone(obj.getMobile())
                        .Nativeplace(obj.getNativePlace())
                        .occupationType(obj.getOccupationType())
                        .occupationCode(obj.getOccupationCode())
                        .joinMedProtect(obj.getSocialInsuFlag())
                        .EMail(obj.getEmail())
                        .build()
        ).collect(Collectors.toList());

        List<FdUser> fdUserList = grpInsuredInfoList.stream().map(obj ->
                FdUser.builder()
                        .edorType(edorType)
                        .oldIdNo(obj.getOldIdNo())
                        .IDNo(obj.getIdNo())
                        .nickName(obj.getName())
                        .userName(obj.getIdNo())
                        .phone(obj.getMobile())
                        .email(obj.getEmail())
                        .build()
        ).collect(Collectors.toList());

        List<FCPerInfoTemp> fcPerInfoTempList = grpInsuredInfoList.stream().map(obj ->
                FCPerInfoTemp.builder()
                        .edorType(edorType)
                        .oldIdNo(obj.getOldIdNo())
                        .name(obj.getName())
                        .sex(obj.getSex())
                        .IDNo(obj.getIdNo())
                        .IDType(obj.getIdType())
                        .relationship(obj.getRelationToAppnt())
                        .birthDay(obj.getBirthday())
                        .idTypeEndDate(obj.getIdExpDate())
                        .Nativeplace(obj.getNativePlace())
                        .openBank(obj.getBankCode())
                        .openAccount(obj.getBankAccNo())
                        .joinMedProtect(obj.getSocialInsuFlag())
                        .occupationType(obj.getOccupationType())
                        .occupationCode(obj.getOccupationCode())
                        .mobilePhone(obj.getMobile())
                        .build()
        ).collect(Collectors.toList());

        List<FCPerinfoFamilyTemp> fcPerinfoFamilyTempList = grpInsuredInfoList.stream().map(obj ->
                FCPerinfoFamilyTemp.builder()
                        .edorType(edorType)
                        .oldIdNo(obj.getOldIdNo())
                        .iDNo(obj.getIdNo())
                        .name(obj.getName())
                        .sex(obj.getSex())
                        .iDType(obj.getIdType())
                        .birthDay(obj.getBirthday())
                        .relationship(obj.getRelationToAppnt())
                        .idTypeEndDate(obj.getIdExpDate())
                        .nativeplace(obj.getNativePlace())
                        .joinMedProtect(obj.getSocialInsuFlag())
                        .occupationType(obj.getOccupationType())
                        .occupationCode(obj.getOccupationCode())
                        .phone(obj.getMobile())
                        .build()
        ).collect(Collectors.toList());

        List<FCPerInfo> fcPerInfoList = grpInsuredInfoList.stream().map(obj ->
                FCPerInfo.builder()
                        .edorType(edorType)
                        .oldIdNo(obj.getOldIdNo())
                        .name(obj.getName())
                        .sex(obj.getSex())
                        .IDNo(obj.getIdNo())
                        .IDType(obj.getIdType())
                        .birthDay(obj.getBirthday())
                        .idTypeEndDate(obj.getIdExpDate())
                        .nativeplace(obj.getNativePlace())
                        .openBank(obj.getBankCode())
                        .openAccount(obj.getBankAccNo())
                        .joinMedProtect(obj.getSocialInsuFlag())
                        .occupationType(obj.getOccupationType())
                        .occupationCode(obj.getOccupationCode())
                        .phone(obj.getMobile())
                        .relationship(obj.getRelationToAppnt())
                        .email(obj.getEmail())
                        .build()
        ).collect(Collectors.toList());

        List<FCPerson> fcPersonList = grpInsuredInfoList.stream().map(obj ->
                FCPerson.builder()
                        .edorType(edorType)
                        .oldIdNo(obj.getOldIdNo())
                        .IDNo(obj.getIdNo())
                        .name(obj.getName())
                        .sex(obj.getSex())
                        .IDType(obj.getIdType())
                        .birthDate(obj.getBirthday())
                        .phone(obj.getMobile())
                        .mobilePhone(obj.getMobile())
                        .EMail(obj.getEmail())
                        .openBank(obj.getBankCode())
                        .openAccount(obj.getBankAccNo())
                        .joinMedProtect(obj.getSocialInsuFlag())
                        .idTypeEndDate(obj.getIdExpDate())
                        .nativeplace(obj.getNativePlace())
                        .occupationType(obj.getOccupationType())
                        .occupationCode(obj.getOccupationCode())
                        .build()
        ).collect(Collectors.toList());

        //更新

        fdUserMapper.updateBatchById(fdUserList);

        fcOrderInsuredMapper.updateBatchById(fcOrderInsuredList);

        fcPerInfoTempMapper.updateBatchById(fcPerInfoTempList);

        fcPerInfoMapper.updateBatchById(fcPerInfoList);

        fcPersonMapper.updateBatchById(fcPersonList);

        fcPerinfoFamilyTempMapper.updateBatchById(fcPerinfoFamilyTempList);


    }

    private void planChange(FCGrpOrder fcGrpOrder, List<GrpInsuredInfo> grpInsuredInfoList) {

        for (GrpInsuredInfo insuredInfo : grpInsuredInfoList) {
            //根据团单号和证件号查询订单详情表
            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByGrpOrderNoAndIdNo(fcGrpOrder.getGrpOrderNo(), insuredInfo.getIdNo());

            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(fcOrderInsured.getOrderItemNo());

            fcOrderItemDetailMapper.updateByOrderItemDetailNo(insuredInfo.getContPlanCode(), fcOrderItem.getOrderItemDetailNo());

        }

    }

    @Transactional
    public void saveInsuranceChange(FCGrpOrder fcGrpOrder, List<GrpInsuredInfo> grpInsuredInfoList) {
        //家属
        List<GrpInsuredInfo> familyList = grpInsuredInfoList.stream().filter(x -> x.getEmployeeNo() != null).collect(Collectors.toList());
        List<GrpInsuredInfo> userList = grpInsuredInfoList.stream().filter(x -> x.getEmployeeNo() == null).collect(Collectors.toList());

        List<FCPerInfoTemp> perInfoTempList = makeNewUser(fcGrpOrder, userList);


        Map<String, String> idNoToPerTempNoMap = perInfoTempList.stream()
                .collect(Collectors.toMap(FCPerInfoTemp::getIDNo, FCPerInfoTemp::getPerTempNo));

        familyList.forEach(grpInsuredInfo -> {
            String perTempNo = idNoToPerTempNoMap.get(grpInsuredInfo.getEmployeeIdNo());
            if (perTempNo != null) {
                grpInsuredInfo.setPerTempNo(perTempNo);
            }
        });
        if (CollectionUtils.isEmpty(userList)) {
            familyList.forEach(x -> {
                FCPerInfoTemp fcPerInfoTemp = fcPerInfoTempMapper.selectByEnsureCodeAndIdNo(fcGrpOrder.getEnsureCode(), x.getEmployeeIdNo());
                x.setPerTempNo(fcPerInfoTemp.getPerTempNo());
            });
        }

        Map<String, String> map = new HashMap<>();
        map.put("grpNo", fcGrpOrder.getGrpNo());
        map.put("ensureCode", fcGrpOrder.getEnsureCode());
        insertNewUser(map);

        makeNewFanmily(fcGrpOrder, familyList);


        insertOrder(fcGrpOrder, userList, familyList);


        log.info("保全增人同步数据NI结束!!!!!!");
    }

    private void insertOrder(FCGrpOrder fcGrpOrder, List<GrpInsuredInfo> userList, List<GrpInsuredInfo> familyList) {

        if (CollectionUtils.isNotEmpty(familyList)) {
            for (GrpInsuredInfo family : familyList) {
                List<FdUser> fdUserList = fdUserMapper.selectByIdNo(family.getEmployeeIdNo());

                if (CollectionUtils.isNotEmpty(fdUserList)) {
                    FdUser user = fdUserList.get(0);
                    FCPerInfo fcPerInfo = fcPerInfoMapper.selectByIdNoAndGrpNo(family.getEmployeeIdNo(), fcGrpOrder.getGrpNo());


                    userList.removeIf(x -> x.getIdNo().equals(family.getEmployeeIdNo()));

                    GlobalInput globalInput = new GlobalInput();
                    globalInput.setEnsureCode(fcGrpOrder.getEnsureCode());
                    globalInput.setCustomNo(fcPerInfo.getPerNo());
                    globalInput.setUserNo(user.getUserNo());
                    globalInput.setGrpNo(fcPerInfo.getGrpNo());
                    globalInput.setName(fcPerInfo.getName());

                    List<PeopleInsurePlanInfo> peopleInsurePlanInfos = new ArrayList<>();

                    peopleInsurePlanInfos.add(PeopleInsurePlanInfo.builder()
                            .ensureCode(fcGrpOrder.getEnsureCode())
                            .grpNo(fcPerInfo.getGrpNo())
                            .perNo(fcPerInfo.getPerNo())
                            .name(family.getName())
                            .personId(family.getPersonID())
                            .planCode(family.getContPlanCode())
                            .build());


                    //员工
                    List<FCPerson> fcPeople = fcPersonMapper.selectFcPersonByIdNo(family.getEmployeeIdNo());


                    FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByIdNoAndGrpOrderNo(family.getEmployeeIdNo(), fcGrpOrder.getGrpOrderNo());
                    String planCode;
                    if (fcOrderInsured != null) {

                        FCOrderItemDetail fcOrderItemDetail = fcOrderItemMapper.selectPlanCode(fcOrderInsured.getOrderItemNo());
                        planCode = fcOrderItemDetail.getProductCode();

                    } else {
                        FCPerInfoTemp fcPerInfoTemp = fcPerInfoTempMapper.selectLevelCodeByEnsureCodeAndIdNo2(fcGrpOrder.getEnsureCode(), family.getEmployeeIdNo());
                        planCode = fcPerInfoTemp.getDefaultPlan();

                    }

                    peopleInsurePlanInfos.add(PeopleInsurePlanInfo.builder()
                            .ensureCode(fcGrpOrder.getEnsureCode())
                            .grpNo(fcPerInfo.getGrpNo())
                            .perNo(fcPerInfo.getPerNo())
                            .name(fcPeople.get(0).getName())
                            .personId(fcPeople.get(0).getPersonID())
                            .planCode(planCode)
                            .build());
                    FCOrder fcOrder = fcOrderMapper.selectOrderByPerNoAndEnsureCode(fcPerInfo.getPerNo(), fcGrpOrder.getEnsureCode());
                    String orderNo;
                    if (fcOrder == null) {
                        orderNo = maxNoService.createMaxNo("OrderNo", DateTimeUtil.getTimeString().substring(0, 4), 8);
                    } else {
                        orderNo = fcOrder.getOrderNo();
                    }
                    try {
                        confirmInsure(globalInput, peopleInsurePlanInfos, orderNo, fcGrpOrder.getEnsureCode());
                    } catch (Exception e) {
                        log.info("保全增人同步数据添加家属错误:::::: {}", e);
                    }

                }
            }

        }
        if (CollectionUtils.isNotEmpty(userList)) {
            for (GrpInsuredInfo user : userList) {

                FCPerInfo fcPerInfo = fcPerInfoMapper.selectbyIdNoandGrpNo(user.getIdNo(), fcGrpOrder.getGrpNo());
                FdUser fdUser = fdUserMapper.selectByPerNoAndIdNo(fcPerInfo.getPerNo(), user.getIdNo());
                List<FCPerson> fcPeople = fcPersonMapper.selectFcPersonByIdNo(user.getIdNo());


                GlobalInput globalInput = new GlobalInput();
                globalInput.setEnsureCode(fcGrpOrder.getEnsureCode());
                globalInput.setCustomNo(fcPerInfo.getPerNo());
                globalInput.setUserNo(fdUser.getUserNo());
                globalInput.setGrpNo(fcPerInfo.getGrpNo());
                globalInput.setName(fcPerInfo.getName());

                List<PeopleInsurePlanInfo> peopleInsurePlanInfos = new ArrayList<>();

                PeopleInsurePlanInfo peopleInsurePlanInfo = new PeopleInsurePlanInfo();
                peopleInsurePlanInfo.setEnsureCode(fcGrpOrder.getEnsureCode());
                peopleInsurePlanInfo.setGrpNo(fcPerInfo.getGrpNo());
                peopleInsurePlanInfo.setPerNo(fcPerInfo.getPerNo());
                peopleInsurePlanInfo.setName(fcPeople.get(0).getName());
                peopleInsurePlanInfo.setPersonId(fcPeople.get(0).getPersonID());
                peopleInsurePlanInfo.setPlanCode(user.getContPlanCode());

                peopleInsurePlanInfos.add(peopleInsurePlanInfo);


                FCOrder fcOrder = fcOrderMapper.selectOrderByPerNoAndEnsureCode(fcPerInfo.getPerNo(), fcGrpOrder.getEnsureCode());
                String orderNo;
                if (fcOrder == null) {
                    orderNo = maxNoService.createMaxNo("OrderNo", DateTimeUtil.getTimeString().substring(0, 4), 8);
                } else {
                    orderNo = fcOrder.getOrderNo();
                }
                try {
                    confirmInsure(globalInput, peopleInsurePlanInfos, orderNo, fcGrpOrder.getEnsureCode());
                } catch (Exception e) {
                    log.info("保全增人同步数据添加家属错误:::::: {}", e);
                }
            }

        }
    }


    private List<FCPerInfoTemp> makeNewUser(FCGrpOrder fcGrpOrder, List<GrpInsuredInfo> grpInsuredInfoList) {
        FCBusPersonType fcBusPersonType = fcBusPersonTypeMapper.selectByGrpNo(fcGrpOrder.getGrpNo()).get(0);


        List<FCPerInfoTemp> perInfoTempList = grpInsuredInfoList.stream()
                .map(obj -> FCPerInfoTemp.builder()
                        .PerTempNo(maxNoService.createMaxNo("PerTempNo", "", 20))
                        .EnsureCode(fcGrpOrder.getEnsureCode())
                        .grpNo(fcGrpOrder.getGrpNo())
                        .levelCode(fcBusPersonType.getGradeLevelCode())
                        .name(obj.getName())
                        .sex(obj.getSex())
                        .IDType(obj.getIdType())
                        .relationship(obj.getRelationToAppnt())
                        .IDNo(obj.getIdNo())
                        .idTypeEndDate(obj.getIdExpDate())
                        .birthDay(obj.getBirthday())
                        .mobilePhone(obj.getMobile())
                        .relationship(obj.getRelationToAppnt())
                        .occupationType(obj.getOccupationType())
                        .occupationCode(obj.getOccupationCode())
                        .Nativeplace(obj.getNativePlace())
                        .defaultPlan(obj.getContPlanCode())
                        .joinMedProtect(obj.getSocialInsuFlag())
                        .ImpotStatus("01")
                        .SubStaus("01")
                        .operator("SYSTEM")
                        .makeDate(LocalDate.now().toString())
                        .build())
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(perInfoTempList)) {
            fcPerInfoTempMapper.insert(perInfoTempList);
            return perInfoTempList;
        }
        return new ArrayList<>();
    }

    private void makeNewFanmily(FCGrpOrder fcGrpOrder, List<GrpInsuredInfo> familyList) {
        for (GrpInsuredInfo family : familyList) {
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByIdNoAndGrpNo(family.getEmployeeIdNo(), fcGrpOrder.getGrpNo());
            //个人信息表
            if (null != fcPerInfo) {
                FCPerson fcPerson = updatePerson(family);
                fcPerson.setPersonID(maxNoService.createMaxNo("PersonID", null, 20));
                fcPerson = CommonUtil.initObject(fcPerson, "INSERT");

                family.setPersonID(fcPerson.getPersonID());
                //员工家属信息关联表
                FCStaffFamilyRela fcStaffFamilyRela = updateStaffFamilyRela(family);
                fcStaffFamilyRela.setPerNo(fcPerInfo.getPerNo());
                fcStaffFamilyRela.setPersonID(fcPerson.getPersonID());
                CommonUtil.initObject(fcStaffFamilyRela, "INSERT");

                FCPerinfoFamilyTemp fcPerinfoFamilyTemp = FCPerinfoFamilyTemp.builder()
                        .familyTempNo(maxNoService.createMaxNo("FamilyTempNo", "", 20))
                        .perTempNo(family.getPerTempNo())
                        .relationship("06")
                        .name(family.getName())
                        .relation(family.getEmployeeRelation().substring(1, 2))
                        .joinMedProtect(family.getSocialInsuFlag())
                        .nativeplace(family.getNativePlace())
                        .phone(family.getMobile())
                        .iDType(family.getIdType())
                        .ensureCode(fcGrpOrder.getEnsureCode())
                        .birthDay(family.getBirthday())
                        .sex(family.getSex())
                        .iDNo(family.getIdNo())
                        .idTypeEndDate(family.getIdExpDate())
                        .occupationCode(family.getOccupationCode())
                        .occupationType(family.getOccupationType())
                        .perName(family.getLastNamePy())
                        .perIDNo(family.getEmployeeIdNo())
                        .perIDType(fcPerInfo.getIDType())
                        .subStaus("01")
                        .operator("admin").build();

                CommonUtil.initObject(fcPerinfoFamilyTemp, "INSERT");
                try {
                    fcPerinfoFamilyTempMapper.insert(fcPerinfoFamilyTemp);
                    fcPersonMapper.insert(fcPerson);
                    fcStaffFamilyRelaMapper.insert(fcStaffFamilyRela);
                } catch (Exception e) {
                    log.info("核心同步添加家属失败：", e);
                }

            }
        }
    }

    private void reducePerson(FCGrpOrder fcGrpOrder, List<GrpInsuredInfo> grpInsuredInfoList) {
        for (GrpInsuredInfo grpInsuredInfo : grpInsuredInfoList) {
            FCPerInfo perInfo = fcPerInfoMapper.selectByIdNoAndGrpNo(grpInsuredInfo.getIdNo(), fcGrpOrder.getGrpNo());
            FCPerInfoTemp fcPerInfoTemp = fcPerInfoTempMapper.selectByEnsureCodeAndIdNo(fcGrpOrder.getEnsureCode(), grpInsuredInfo.getIdNo());
            if (null != fcPerInfoTemp) {
                fcPerInfoTempMapper.deleteFcPerInfoTemp(fcPerInfoTemp.getPerTempNo());
            }

            if (null != perInfo) {
                fcOrderMapper.deleteByGrpOrderAndPerNo(fcGrpOrder.getGrpNo(), perInfo.getPerNo());

            }

        }
    }


    private FCGrpInfo makeGrpInfo(GrpAppntInfo grpAppntInfo, FCGrpInfo grpInfo, GrpCorporationInfo grpCorporationInfo) {
        grpInfo.setLegID(grpCorporationInfo.getCorporationIDNo());
        grpInfo.setLegIDType(grpCorporationInfo.getCorporationIDType());
        grpInfo.setLegSex(grpCorporationInfo.getCorporationGender());
        grpInfo.setLegBirthday(grpCorporationInfo.getCorporationBirthday());
        grpInfo.setLegNationality(grpCorporationInfo.getCorporationNationality());
        grpInfo.setLegIDStartDate(grpCorporationInfo.getCorporationIDExpStartDate());
        grpInfo.setLegIDEndDate(grpCorporationInfo.getCorporationIDExpDate());
        grpInfo.setGrpName(grpAppntInfo.getGrpName());
        grpInfo.setGrpAddRess(grpAppntInfo.getGrpAddress());
        grpInfo.setUnifiedsociCode(grpAppntInfo.getComNo());
        grpInfo.setGrpIdType(grpAppntInfo.getComType());
        grpInfo.setGrpIdNo(grpAppntInfo.getComNo());
        grpInfo.setGrpType(grpAppntInfo.getGrpNature());
        grpInfo.setCorporationMan(grpCorporationInfo.getCorporation());
        //grpInfo.setRegaddress(grpAppntInfo.getRegestedPlace());
        grpInfo.setGrpBankCode(grpAppntInfo.getBankCode());
        grpInfo.setGrpBankAccNo(grpAppntInfo.getBankAccNo());
        grpInfo.setTrade(grpAppntInfo.getBusinessType());
        grpInfo.setGrpTypeStartDate(grpAppntInfo.getBusliceStartDate());
        grpInfo.setGrpTypeEndDate(grpAppntInfo.getBusliceDate());
        grpInfo.setGrpEstablishDate(grpAppntInfo.getFoundDate());
        grpInfo.setGrpRegisterAddress(grpAppntInfo.getRegestedPlace());

        return grpInfo;
    }

    private FcGrpContact makeHrInfo(GrpLinkManInfo grpLinkManInfo, FcGrpContact grpContact, FdUser user) {
        grpContact.setName(grpLinkManInfo.getLinkMan1());
        grpContact.setSex(grpLinkManInfo.getGender1());
        grpContact.setNativeplace(grpLinkManInfo.getNationality1());
        grpContact.setIdType(grpLinkManInfo.getInsContIDType1());
        grpContact.setIdNo(grpLinkManInfo.getInsContIDNo1());
        grpContact.setIdTypeStartDate(grpLinkManInfo.getInsContIDStartPeriod1());
        grpContact.setIdTypeEndDate(grpLinkManInfo.getInsContIDPeriodOfValidityType1());
        grpContact.setMobilePhone(grpLinkManInfo.getMobilePhone1());
        grpContact.setBirthDay(grpLinkManInfo.getBirthday1());
        grpContact.setEmail(grpLinkManInfo.getEmail1());

        user.setUserName(grpLinkManInfo.getInsContIDNo1());
        user.setNickName(grpLinkManInfo.getLinkMan1());
        user.setPhone(grpLinkManInfo.getMobilePhone1());
        user.setIDNo(grpLinkManInfo.getInsContIDNo1());


        return grpContact;
    }


    public FCPerson updatePerson(GrpInsuredInfo fcEmpAndFamilyInfo) {
        FCPerson fcPerson = new FCPerson();
        fcPerson.setName(fcEmpAndFamilyInfo.getName());
        fcPerson.setBirthDate(fcEmpAndFamilyInfo.getBirthday());
        fcPerson.setIDNo(fcEmpAndFamilyInfo.getIdNo());
        fcPerson.setIDType(fcEmpAndFamilyInfo.getIdType());
        fcPerson.setNativeplace(fcEmpAndFamilyInfo.getNativePlace());
        fcPerson.setIdTypeEndDate(fcEmpAndFamilyInfo.getIdExpDate());
        fcPerson.setOpenBank(fcEmpAndFamilyInfo.getBankCode());
        fcPerson.setMobilePhone(fcEmpAndFamilyInfo.getMobile());
        fcPerson.setOccupationCode(fcEmpAndFamilyInfo.getOccupationCode());
        fcPerson.setOpenAccount(fcEmpAndFamilyInfo.getBankAccNo());
        fcPerson.setOccupationType(fcEmpAndFamilyInfo.getOccupationType());
        fcPerson.setOperator(fcEmpAndFamilyInfo.getName());
        fcPerson.setSex(fcEmpAndFamilyInfo.getSex());
        fcPerson.setEMail(fcEmpAndFamilyInfo.getEmail());
        fcPerson.setRelationship("06");
        fcPerson = (FCPerson) CommonUtil.initObject(fcPerson, "UPDATE");
        return fcPerson;
    }

    public FCStaffFamilyRela updateStaffFamilyRela(GrpInsuredInfo fcEmpAndFamilyInfo) {
        FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
        fcStaffFamilyRela.setRelation(fcEmpAndFamilyInfo.getEmployeeRelation().substring(1, 2));
        fcStaffFamilyRela.setOperator(fcEmpAndFamilyInfo.getName());
        fcStaffFamilyRela.setRelationProve(" ");
        fcStaffFamilyRela = (FCStaffFamilyRela) CommonUtil.initObject(fcStaffFamilyRela, "UPDATE");
        return fcStaffFamilyRela;
    }

    public void insertNewUser(Map<String, String> params) {
        log.info("复合完成后添加人员::::::{}", JSON.toJSONString(params));
        // 获取当前企业信息
        int peoples = 0;
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(params.get("grpNo"));
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(params.get("ensureCode"));
        if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
            List<HashMap<String, String>> listPerIfo = new ArrayList<HashMap<String, String>>();
            Map<String, String> map = new HashMap<String, String>();
            // 获取需要同步的人数
            List<FCPerInfoTemp> needSyncNum = ensureMakeService.getNeedSyncNum(params.get("ensureCode"));
            peoples = peoples + needSyncNum.size();
            if (needSyncNum.size() > 0) {
                LisIDEA encryPassword = new LisIDEA();
                for (int i = 0; i < needSyncNum.size(); i++) {
                    Calendar cal = Calendar.getInstance();
                    int year = cal.get(Calendar.YEAR);
                    HashMap<String, String> hashMap = new HashMap<String, String>();
                    hashMap.put("currentDate", DateTimeUtil.getCurrentDate());
                    hashMap.put("currentTime", DateTimeUtil.getCurrentTime());
                    hashMap.put("Operator", "admin");
                    hashMap.put("year", year + "");
                    FdUser fdUser = fdUserMapper.selectByPwd(needSyncNum.get(i).getIDNo());
                    if (fdUser == null) {
                        hashMap.put("PassWord", encryPassword.encryptString(needSyncNum.get(i).getIDNo().substring(needSyncNum.get(i).getIDNo().length() - 6)));
                    } else if (fdUser != null) {
                        hashMap.put("PassWord", fdUser.getPassWord());
                    }
                    hashMap.put("ensureCode", params.get("ensureCode"));
                    hashMap.put("idNo", needSyncNum.get(i).getIDNo());
                    hashMap.put("nativeplace", needSyncNum.get(i).getNativeplace());
                    hashMap.put("IDType", needSyncNum.get(i).getIDType());
                    hashMap.put("levelCode", needSyncNum.get(i).getLevelCode());
                    hashMap.put("UserNo", maxNoService.createMaxNo("UserNo", "", 20));
                    hashMap.put("UserRole", maxNoService.createMaxNo("UserRole", "SN", 18));
                    hashMap.put("PwdSN", maxNoService.createMaxNo("PwdSN", "PWDSN", 15));
                    Map<String, Object> perInfoMap = new HashMap<>();
                    perInfoMap.put("IDNo", needSyncNum.get(i).getIDNo());
                    perInfoMap.put("grpNo", fcGrpInfo.getGrpNo());
                    List<FCPerInfo> fcPerInfo = fcPerInfoMapper.selectByExample(perInfoMap);
                    if (fcPerInfo.size() < 1) {
                        hashMap.put("PerNo", maxNoService.createMaxNo("PerNo", "", 20));
                    } else {
                        hashMap.put("PerNo", fcPerInfo.get(0).getPerNo());
                    }
                    Map<String, String> infoMap = new HashMap<>();
                    infoMap.put("grpNo", fcGrpInfo.getGrpNo());
                    infoMap.put("idNo", needSyncNum.get(i).getIDNo());
                    FCPerson fcPerson = fcPersonMapper.getPerPersonID(infoMap);
                    if (fcPerson != null) {
                        hashMap.put("PersonId", fcPerson.getPersonID());
                    } else {
                        hashMap.put("PersonId", maxNoService.createMaxNo("PersonId", "", 20));
                    }
                    hashMap.put("seriaNo", maxNoService.createMaxNo("DefaultPlan", "", 20));
                    hashMap.put("RegDay", maxNoService.createMaxNo("RegDay", "", 20));
                    //判断该福利是固定计划还是弹性,固定计划，defaultPlan不为空
                    if ("0".equals(fcEnsure.getPlanType())) {
                        hashMap.put("StaffGrpPrem", String.valueOf(fcEnsurePlanMapper.selectPlanPrem(params.get("ensureCode"), needSyncNum.get(i).getDefaultPlan())));
                    }
                    if ("1".equals(fcEnsure.getPlanType())) {
                        hashMap.put("StaffGrpPrem", "");
                    }
                    hashMap.put("grpNo", fcGrpInfo.getGrpNo());
                    listPerIfo.add(hashMap);
                }
                log.info("同步人员信息集合listPerIfo准备完毕，开始同步人员。。。");
                map.put("ensureCode", params.get("ensureCode"));
                map.put("planType", fcEnsure.getPlanType());
                map.put("year", Calendar.getInstance().get(Calendar.YEAR) + "");
                map.put("currentDate", DateTimeUtil.getCurrentDate());
                map.put("currentTime", DateTimeUtil.getCurrentTime());
                map.put("grpNo", fcGrpInfo.getGrpNo());
                // 同步客户表FcPerInfo（更新）
                //弹性计划 FcPerInfo 表中不存员工职级信息 update-20200417
                log.info("更新FcPerInfo表开始。。。");
                fcPerInfoTempMapper.updateFcPerInfo(map);
                log.info("更新FcPerInfo表完毕。。。");

                // 同步客户表FcPerSon（更新）
                log.info("更新FcPerSon表开始。。。");
                fcPerInfoTempMapper.updateFcPerSon(map);
                log.info("更新FcPerSon表完毕。。。");

                // 同步客户表FdUser（更新）
                log.info("更新FdUser开始。。。");
                fcPerInfoTempMapper.updateFdUser(map);
                log.info("更新FdUser完毕。。。");

                log.info("更新FCPerRegistDay开始。。。");
                //弹性计划 FCPerRegistDay 表中存员工职级信息 update-20200417
                fcPerInfoTempMapper.updateFCPerRegistDay(map);//应关闭
                log.info("更新FCPerRegistDay完毕。。。");
                log.info("更新人员相关表完成，开始插入人员相关表。。。");

                // 同步客户表FcPerSon（插入）
                fcPerInfoTempMapper.insertFcPerSon(listPerIfo);
                // 同步家庭关系表FCStaffFamilyRela
                fcPerInfoTempMapper.insertFCStaffFamilyRela(listPerIfo);
                // 同步员工默认计划表fcDefaultPlan
                if (!"1".equals(fcEnsure.getPlanType())) {
                    fcPerInfoTempMapper.insertFCDefaultPlan(listPerIfo);
                }
                // 注册个人账号 custType : 1-个人账号
                fcPerInfoTempMapper.insertFdUser(listPerIfo);
                fcPerInfoTempMapper.insertFdUserRole(listPerIfo);
                fcPerInfoTempMapper.insertFdPwdHist(listPerIfo);
                fcPerInfoTempMapper.insertFDusertomenugrp(listPerIfo);
                // 同步员工注册期表 FCPerRegistDay
                //弹性计划 FCPerRegistDay 表中存员工职级信息 update-20200417
                fcPerInfoTempMapper.insertFCPerRegistDay(listPerIfo);
                // 同步客户表FcPerInfo（插入）
                //弹性计划 FcPerInfo 表中不存员工职级信息 update-20200417
                fcPerInfoTempMapper.insertFcPerInfo(listPerIfo);
                // 同步完成，更新临时表数据为已提交
                fcPerInfoTempMapper.updateFCPerinfoTemp(map);
                log.info("同步人员信息完成。。。");
            }

            //同步企业人数
            peoples = peoples + fcGrpInfo.getPeoples();
            FCGrpInfo grpPeople = new FCGrpInfo();
            grpPeople.setGrpNo(params.get("grpNo"));
            grpPeople.setPeoples(peoples);
            fcGrpInfoMapper.updateByPrimaryKeySelective(grpPeople);

        }
    }


    public void confirmInsure(GlobalInput globalInput, List<PeopleInsurePlanInfo> peopleInsurePlanInfos, String orderNo, String ensureCode) {

        /**
         * 订单来源
         */
        String orderSource = "02";

        FPInsurePlan fpInsurePlan1 = new FPInsurePlan();
        fpInsurePlan1.setEnsureCode(ensureCode);
        fpInsurePlan1.setPerno(globalInput.getCustomNo());
        fpInsurePlanMapper.deleteInsurePlan(fpInsurePlan1);

        peopleInsurePlanInfos.forEach((PeopleInsurePlanInfo peopleInsurePlanInfo) -> {

            /**
             * 存储投保计划信息表  主表
             */
            FPInsurePlan insurePlan = new FPInsurePlan();
            String insurePlanNo = maxNoService.createMaxNo("InsurePlanNo", "", 20);
            insurePlan.setInsurePlanNo(insurePlanNo);
            insurePlan.setEnsureCode(peopleInsurePlanInfo.getEnsureCode());
            insurePlan.setAppntYear(DateTimeUtil.getCurrentYear());
            insurePlan.setPlanCode(peopleInsurePlanInfo.getPlanCode());
            // 0:订单未提交 1.订单已提交
            insurePlan.setInsureState("1");
            insurePlan.setPerno(peopleInsurePlanInfo.getPerNo());
            insurePlan.setPersonId(peopleInsurePlanInfo.getPersonId());
            insurePlan.setOperator(globalInput.getName());
            insurePlan = (FPInsurePlan) CommonUtil.initObject(insurePlan, "INSERT");
            fpInsurePlanMapper.insertSubtables(insurePlan);

        });
        List<FCOrder> fcOrderList = fcOrderMapper.selectOrder(globalInput.getCustomNo(), ensureCode);
        if (fcOrderList.size() > 0) {
            //删除之前的订单
            String deletConfirmInfo = deleteConfirmInfo(fcOrderList);
            if (!("".equals(deletConfirmInfo))) {
                throw new SystemException("删除订单失败！");
            }
        }
        /******************************************订单存储逻辑 start*******************************************/

        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);

        //获取代理人信息
        List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
        FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);
        // 根据福利编号获取团体保单信息
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(fcEnsure.getEnsureCode());
        /*
         * 1、投保人表FCPerAppnt 从 员工表FCPerInfo取数据
         * 2、一个员工家庭在订单表FCOrder表创建一条数据
         */
        // 1、投保人表FCPerAppnt
        FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
        FCPerAppnt fcPerAppnt = new FCPerAppnt();
        String perAppNo = maxNoService.createMaxNo("PerAppNo", "", 20);
        fcPerAppnt.setPerAppNo(perAppNo);
        fcPerAppnt.setGrpNo(globalInput.getGrpNo());
        fcPerAppnt.setPerNo(globalInput.getCustomNo());
        fcPerAppnt.setName(fcPerInfo.getName());
        fcPerAppnt.setSex(fcPerInfo.getSex());
        fcPerAppnt.setIDType(fcPerInfo.getIDType());
        fcPerAppnt.setIDNo(fcPerInfo.getIDNo());
        fcPerAppnt.setBirthDay(fcPerInfo.getBirthDay());
        fcPerAppnt.setOperator(globalInput.getUserNo());
        fcPerAppnt = CommonUtil.initObject(fcPerAppnt, "INSERT");
        fcPerAppntMapper.insert(fcPerAppnt);
        // 2、订单表FCOrder

        FCOrder fcOrder = new FCOrder();
        FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectByEnsureCode(ensureCode, globalInput.getCustomNo());


        fcOrder.setOrderNo(orderNo);
        fcOrder.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());

        fcOrder.setOrderStatus("02");//02-待支付，03-待确认投保信息
        fcOrder.setOrderType("01");
        fcOrder.setOrderSource(orderSource);
        fcOrder.setOpenDay(fcPerRegistDay.getOpenDay());
        fcOrder.setCloseDay(fcPerRegistDay.getCloseDay());
        fcOrder.setGrpNo(globalInput.getGrpNo());
        fcOrder.setPerNo(globalInput.getCustomNo());
        fcOrder.setPerAppNo(perAppNo);
        fcOrder.setCommitDate(DateTimeUtil.getCurrentDate());
        fcOrder.setClientNo(fcEnsure.getClientNo());
        fcOrder.setOperator(globalInput.getUserNo());
        fcOrder = CommonUtil.initObject(fcOrder, "INSERT");
        fcOrderMapper.insert(fcOrder);

        // 3、订单轨迹表FCOrderLocus
        FCOrderLocus fcOrderLocus = new FCOrderLocus();
        String orderLocusSN = maxNoService.createMaxNo("orderLocusSN", "", 20);
        fcOrderLocus.setOrderLocusSN(orderLocusSN);
        fcOrderLocus.setOrderNo(orderNo);
        fcOrderLocus.setOrderStatus("02");//02-待支付，03-待确认投保信息
        fcOrderLocus.setOperator(globalInput.getUserNo());
        fcOrderLocus = CommonUtil.initObject(fcOrderLocus, "INSERT");
        fcOrderLocusMapper.insert(fcOrderLocus);
        Map param = new HashMap();
        param.put("perNo", globalInput.getCustomNo());
        param.put("ensureCode", globalInput.getEnsureCode());
        //修改成查询个人投保记录临时表（因为只有在最终确认的时候才会在主表表中添加信息）
        List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectFPInsurePlans(param);

        //获取拆分保费：个人缴费+公司缴费
        Map<String, Object> dataMap = insureService.calStaffPolicyPremDetail(globalInput.getEnsureCode(), globalInput.getCustomNo(), fcEnsure.getEnsureType());
        if (dataMap == null) {
            log.info("系统异常，缴费明细计算失败");
            throw new RuntimeException();
        }
        Map<String, Object> staffMap = (HashMap) dataMap.get("staffMap");
        List<Map<String, Object>> familyList = (ArrayList<Map<String, Object>>) dataMap.get("familyMap");
        Map<String, Map<String, Object>> familyMaps = new HashMap<>();
        if (familyList != null && familyList.size() > 0) {
            for (Map<String, Object> map : familyList) {
                familyMaps.put((String) map.get("personId"), map);
            }
        }

        for (FPInsurePlan fpInsurePlan : fpInsurePlanList) {
            // 3、子订单表FCOrderItem
            FCOrderItem fcOrderItem = new FCOrderItem();
            String orderItemNo = maxNoService.createMaxNo("OrderItemNo", "", 20);
            String orderItemDetailNo = maxNoService.createMaxNo("OrderItemDetailNo", "", 20);
            fcOrderItem.setOrderItemNo(orderItemNo);
            fcOrderItem.setOrderItemDetailNo(orderItemDetailNo);
            fcOrderItem.setOrderNo(orderNo);
            /* 个人保单号生成规则：
			   1、前缀99；
			   2、3-4位截取保单管理机构的3、4位，如为总公司则取86（如管理机构为864498取44）；
			   3、5-6位截取年份后两位（如2018取18）；
			   4、7-15位取每个自然年度流水号；
			   5、最后一位固定为8；
			   例如弹性平台今年第一张个人保险凭证号应为：9944180000000018。
			*/
            String rule = "99" + fdAgentInfo.getManageCom().substring(2, 4) + DateTimeUtil.getCurrentYear().substring(2, 4);
            String contNo = maxNoService.createMaxNo("ContNo" + DateTimeUtil.getCurrentYear(), rule, 9);
            contNo = contNo + "8";
            fcOrderItem.setContNo(contNo);
            if (!staffMap.isEmpty()) {
                if (fpInsurePlan.getPersonId().equals(staffMap.get("personId"))) {
                    fcOrderItem.setSelfPrem((double) staffMap.get("staffPrem"));
                    fcOrderItem.setGrpPrem((double) staffMap.get("staffDefaultPrem"));
                }
            }
            if (!familyMaps.isEmpty()) {
                if (familyMaps.get(fpInsurePlan.getPersonId()) != null) {
                    Map<String, Object> familyMap = familyMaps.get(fpInsurePlan.getPersonId());
                    fcOrderItem.setSelfPrem((double) familyMap.get("familyPrem"));
                    fcOrderItem.setGrpPrem((double) familyMap.get("familyDefaultPrem"));
                }
            }
            fcOrderItem.setOperator(globalInput.getUserNo());
            fcOrderItem = CommonUtil.initObject(fcOrderItem, "INSERT");
            fcOrderItemMapper.insert(fcOrderItem);
            // 4、子订单产品要素详情表FCOrderItemDetail
            FCOrderItemDetail fcOrderItemDetail = new FCOrderItemDetail();
            fcOrderItemDetail.setOrderItemDetailNo(orderItemDetailNo);
            fcOrderItemDetail.setProductCode(fpInsurePlan.getPlanCode());
            fcOrderItemDetail.setEnsureCode(ensureCode);
            fcOrderItemDetail.setProductEleCode("001");
            fcOrderItemDetail.setOperator(globalInput.getUserNo());
            fcOrderItemDetail = CommonUtil.initObject(fcOrderItemDetail, "INSERT");
            fcOrderItemDetailMapper.insert(fcOrderItemDetail);
            // 5、被保人表FCOrderInsured
            FCOrderInsured fcOrderInsured = new FCOrderInsured();
            fcOrderInsured.setOrderItemNo(orderItemNo);
            fcOrderInsured.setOrderNo(orderNo);
            fcOrderInsured.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
            FCPerson person = fcPersonMapper.selectByPrimaryKey(fpInsurePlan.getPersonId());
            fcOrderInsured.setPersonID(person.getPersonID());
            fcOrderInsured.setName(person.getName());
            fcOrderInsured.setSex(person.getSex());
            fcOrderInsured.setBirthDay(person.getBirthDate());
            fcOrderInsured.setNativeplace(person.getNativeplace());
            fcOrderInsured.setIDType(person.getIDType());
            fcOrderInsured.setIDNo(person.getIDNo());
            fcOrderInsured.setMobilePhone(person.getMobilePhone());
            fcOrderInsured.setPhone(person.getPhone());
            fcOrderInsured.setDepartment(fcPerInfo.getDepartment());
            fcOrderInsured.setOccupationType(person.getOccupationType());
            fcOrderInsured.setOccupationCode(person.getOccupationCode());
            fcOrderInsured.setJoinMedProtect(person.getJoinMedProtect());
            fcOrderInsured.setMedProtectType(person.getMedProtectType());
            fcOrderInsured.setEMail(person.getEMail());
            fcOrderInsured.setAddress(person.getAddress());
            fcOrderInsured.setOperator(globalInput.getUserNo());
            fcOrderInsured = CommonUtil.initObject(fcOrderInsured, "INSERT");
            fcOrderInsuredMapper.insert(fcOrderInsured);

            // 6、个人健康告知信息表（记录需要健康告知的人员信息）
            long age = Integer.valueOf(DateTimeUtil.getCurrentAge(person.getBirthDate(), fcEnsure.getCvaliDate()));
            if (age > 0) {
                age = age * 365;
            } else {
                age = DateTimeUtil.getCurrentAge(person.getBirthDate(), DateTimeUtil.getCurrentDate());
            }
            if (insureService.ageValidate(age, fpInsurePlan.getPlanCode(), ensureCode)) {
                FcPerImpartResult fcPerImpartResult = new FcPerImpartResult();
                fcPerImpartResult.setOrderItemNo(orderItemNo);
                fcPerImpartResultMapper.insertSameResultImpartList(fcPerImpartResult);
            }

            //修改投保状态，0-未提交订单表 1-已提交订单表 2-核心承保成功
            fpInsurePlan.setInsureState(InsureStateEnum.SUBMITTED.getCode());
            //修改投保临时表订单状态
            fpInsurePlanMapper.updateByPrimaryKey(fpInsurePlan);
        }

        /******************************************订单存储逻辑 end*******************************************/

    }

    public String deleteConfirmInfo(List<FCOrder> fcOrderList) {
        for (int i = 0; i < fcOrderList.size(); i++) {
            //删除子订单详情表
            fcOrderItemDetailMapper.deleteByOrderNo(fcOrderList.get(i).getOrderNo());
            // 删除子订单个人健康告知信息表
            fcPerImpartResultMapper.deleteByOrderNo(fcOrderList.get(i).getOrderNo());
            //删除子订单表
            fcOrderItemMapper.deleteByOrderNo(fcOrderList.get(i).getOrderNo());
            //删除被保人表
            fcOrderInsuredMapper.deleteByOrderNo(fcOrderList.get(i).getOrderNo());
            //删除个人被保人表
            fcPerAppntMapper.deleteByPrimaryKey(fcOrderList.get(i).getPerAppNo());
            //删除订单表
            fcOrderMapper.deleteByPrimaryKey(fcOrderList.get(i).getOrderNo());
        }
        return "";
    }

}
