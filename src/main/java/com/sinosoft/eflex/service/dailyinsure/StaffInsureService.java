package com.sinosoft.eflex.service.dailyinsure;

import ch.qos.logback.classic.Logger;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.dailyplan.ConfirmSignReq;
import com.sinosoft.eflex.model.dailyplan.ImageReq;
import com.sinosoft.eflex.model.dailyplan.PersonInsuredReq;
import com.sinosoft.eflex.service.*;
import com.sinosoft.eflex.util.*;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/12 15:56
 * @desc
 */

@Service
public class StaffInsureService {

    private static Logger Log = (Logger) LoggerFactory.getLogger(FcStaffInfoService.class);

    @Autowired
    private FcDailyInsureRiskDetailInfoMapper fcDailyInsureRiskDetailInfoMapper;

    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FCOrderItemDetailMapper fcOrderItemDetailMapper;
    @Autowired
    private FCOrderBnfMapper fcOrderBnfMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private UserService userService;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FCOrderMapper fcOrderMapper;
    @Autowired
    private FCOrderItemMapper fcOrderItemMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private EnsureMakeService ensureMakeService;
    @Autowired
    private FPInsurePlanMapper fpInsurePlanMapper;
    @Autowired
    private FPInsureEflexPlanMapper fpInsureEflexPlanMapper;
    @Autowired
    private FCOrderBnfRelaMapper fcOrderBnfRelaMapper;
    @Autowired
    private PremTrailService premTrailService;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;
    @Autowired
    private FileService fileService;
    @Autowired
    private FCPersonImageMapper fcPersonImageMapper;
    @Autowired
    private FDAgentInfoMapper fdAgentInfoMapper;
    @Autowired
    private DailyIssueService dailyIssueService;
    @Autowired
    private FCAppntImpartInfoPersonalMapper fcAppntImpartInfoPersonalMapper;
    @Autowired
    private DailyPlanPhoneService dailyPlanPhoneService;
    @Autowired
    private AddressCheckService addressCheckService;

    /**
     * 投保信息查询
     *
     * @param token
     * @param ensureCode
     * @param orderItemNo
     * @return
     */
    public String getInsuredInfo(String token, String ensureCode, String perNo, String personId, String orderItemNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //查询人员信息
            if (StringUtils.isEmpty(perNo)) {
                return JSON.toJSONString(ResultUtil.error("请求参数缺失！"));
            }
            //查询基础单配置的计划信息
            List<Map<String, String>> planInfos = new ArrayList<>();
            //*********************
            List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate(ensureCode);
            for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos) {
                String planC = fcDailyInsureRiskDetailInfo.getPlanCode();
                String codeName = fdCodeMapper.selectNameByCode("planCode", planC);
                Map<String, String> DateM = new HashMap<>();
                DateM.put("planName", codeName);
                DateM.put("planCode", planC);
                planInfos.add(DateM);
            }
            resultMap.put("planInfos", planInfos);
            //默认本人
            String relation = "0";
            Map<String, String> params = new HashMap<>();
            params.put("perNo", perNo);
            params.put("relation", relation);
            if (!StringUtils.isEmpty(orderItemNo)) {
                params.put("orderItemNo", orderItemNo);
                List<Map<String, String>> fcPersonList = fcPersonMapper.selectFcPersonAndSalaryItemNo(params);
                for (Map<String, String> map : fcPersonList) {
                    String city = map.get("city");
                    String county = map.get("county");
                    if (DailyPlanService.checkcountname(city)) {
                        map.put("cityName", city);
                    } else if (DailyPlanService.checkcountname(county)) {
                        map.put("countyName", county);
                    }
                }
                fcPersonList.get(0).put("isChange", "1");
                resultMap.put("staffInfo", fcPersonList.get(0));
                //已投过保
                //查询订单信息
                Map<String, String> orderItemInfo = fcOrderItemDetailMapper.selectOrderItemInfo(orderItemNo);
                //查询被保人信息
                //根据perNo和personId查询被保人信息
                params.clear();
                params.put("perNo", perNo);
                params.put("personId", personId);
                params.put("orderItemNo", orderItemNo);
//                List<Map<String, String>> orderInsuredInfos = fcPersonMapper.selectFcPersonInfoByParams(params);
                List<Map<String, String>> orderInsuredInfos = fcPersonMapper.selectFcPersonInfoByParam(params);
                String birthDate = fcPersonList.get(0).get("birthDate");//员工生日
                String birthday = fcPersonList.get(0).get("birthday");//被保人生日
                String payFrequency = orderItemInfo.get("payFrequency");
                String payPeriod = orderItemInfo.get("payPeriod");
                if (payFrequency.equals("1")) {
                    //趸交
                    if (!payPeriod.equals("01")) {
                        Map<String, Object> error = ResultUtil.error("趸交只能是一次性交清！");
                        return JSON.toJSONString(error);
                    }
                    Map<String, Object> DateMap1 = new HashMap<>();
                    Map<String, Object> DateMap2 = new HashMap<>();
                    List<Map<String, Object>> list = new ArrayList();
                    DateMap1.put("CodeKey", "1");
                    DateMap1.put("CodeName", "一次交清");
                    DateMap1.put("isCheck", true);
                    DateMap2.put("CodeKey", "2");
                    DateMap2.put("CodeName", "年交");
                    DateMap2.put("isCheck", false);
                    list.add(DateMap1);
                    list.add(DateMap2);
                    resultMap.put("payFrequency", list);
                    Map<String, Object> DateMap02 = new HashMap<>();
                    List<Map<String, Object>> listPayPeriod = new ArrayList();
                    DateMap02.put("CodeKey", "01");
                    DateMap02.put("CodeName", "一次交清");
                    DateMap02.put("isCheck", true);
                    listPayPeriod.add(DateMap02);
                    resultMap.put("payPeriod", listPayPeriod);
                } else {
                    if (payPeriod.equals("01")) {
                        Map<String, Object> error = ResultUtil.error("年交不能是一次性交清！");
                        return JSON.toJSONString(error);
                    }
                    Map<String, Object> DateMap1 = new HashMap<>();
                    Map<String, Object> DateMap2 = new HashMap<>();
                    List<Map<String, Object>> list = new ArrayList();
                    DateMap1.put("CodeKey", "1");
                    DateMap1.put("CodeName", "一次交清");
                    DateMap1.put("isCheck", false);
                    DateMap2.put("CodeKey", "2");
                    DateMap2.put("CodeName", "年交");
                    DateMap2.put("isCheck", true);
                    list.add(DateMap1);
                    list.add(DateMap2);
                    resultMap.put("payFrequency", list);
                    String s = dailyPlanPhoneService.autoGetPlanInfoByTwo2(token, birthday, payFrequency, payPeriod);
                    JSONObject jsonObject = JSON.parseObject(s);
                    Object resultAll = jsonObject.get("resultAlla");
                    resultMap.put("payPeriod", resultAll);
                }
                resultMap.put("orderInsuredInfo", "");
                if (orderInsuredInfos.size() > 0) {
                    Map<String, String> orderInsuredInfo = orderInsuredInfos.get(0);
                    if (!"0".equals(orderInsuredInfo.get("relation"))) {
                        birthDate = orderInsuredInfo.get("birthDate");
                        //校验家属五要素是否可以修改  0可修改 1不可修改
                        orderInsuredInfo.put("isChange", "0");
                        Integer fixedCount = fpInsurePlanMapper.selectStaffFamilyInsured(perNo, personId);
                        Integer flexCount = fpInsureEflexPlanMapper.selectStaffFamilyInsured(perNo, personId);
//                        Integer count = fcOrderInsuredMapper.selectStaffFamilyInsured(personId);
                        Integer count = fcOrderInsuredMapper.selectStaffFamilyInsuredAndOrder(personId);
                        FCOrder fcOrder = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
                        String orderStatus = fcOrder.getOrderStatus();
                        if (orderStatus.matches("^0(4|6|5|8|9|10|12|13|15)$")) {
                            orderInsuredInfo.put("isChange", "1");
                        }
                        if (fixedCount > 0 || flexCount > 0 || count > 0) {
                            //家属已投保，不能修改五要素
                            orderInsuredInfo.put("isChange", "1");
                        }
                        resultMap.put("orderInsuredInfo", orderInsuredInfo);
                    }
                }
                List<Map<String, String>> bnfInfos = new ArrayList<>();
                //查询受益人信息
//                FCOrderBnfRela fcOrderBnfRela = fcOrderBnfRelaMapper.selectByPrimaryKey(orderItemNo);
                List<FCOrderBnfRela> fcOrderBnfRelas = fcOrderBnfRelaMapper.selectlistByOrderItemNo(orderItemNo);
                for (FCOrderBnfRela fcOrderBnfRela : fcOrderBnfRelas) {
                    String bnfType = fcOrderBnfRela.getBnfType();
                    if (!StringUtil.isEmpty(bnfType) && bnfType.equals("1")) {
                        //只有受益人类型为自选受益人时，才能查询到受益人信息
                        bnfInfos = fcOrderBnfMapper.selectBnfInfos2(orderItemNo);
                        break;
                    }
                }

                resultMap.put("orderItemInfo", orderItemInfo);
                resultMap.put("bnfInfos", bnfInfos);
            } else {
                List<Map<String, String>> fcPersonList = fcPersonMapper.selectFcPersonAndSalary(params);
                fcPersonList.get(0).put("isChange", "1");
                resultMap.put("staffInfo", fcPersonList.get(0));
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
        } catch (Exception e) {
            Log.info("投保信息查询失败：" + e.getMessage());
            resultMap = ResultUtil.error("投保信息查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 根据关系查询被保人信息
     *
     * @param token
     * @param perNo
     * @param relation
     * @return
     */
    public String getInsuredByRelation(String token, String perNo, String relation, String orderItemNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtil.isEmpty(orderItemNo)) {
                //主被保人信息信息
                Map<String, String> params = new HashMap<>();
                params.put("perNo", perNo);
                params.put("relation", "0");
                List<Map<String, String>> staffInfos = fcPersonMapper.selectFcPersonInfoByParams(params);
                staffInfos = checkIsChange(staffInfos, perNo);
                //如果不是员工本人，查询出对应的被保人信息
                if (!"0".equals(relation)) {
                    params.clear();
                    params.put("perNo", perNo);
                    params.put("relation", relation);
                    List<Map<String, String>> fcPersonList = fcPersonMapper.selectFcPersonInfosByParams(params);
                    List<Map<String, String>> fcPersonName = fcPersonMapper.selectfcPersoninfos(params);//查找员工家属姓名信息
                    resultMap.put("fcPersonName", fcPersonName);
                    fcPersonList = checkIsChange(fcPersonList, perNo);
                    resultMap.put("fcPersonList", fcPersonList);
                }
                resultMap.put("staffInfo", staffInfos.get(0));
                resultMap.put("success", true);
                resultMap.put("code", "200");
            } else {
                //主被保人信息信息
                Map<String, String> params = new HashMap<>();
                params.put("perNo", perNo);
                params.put("relation", "0");
                params.put("orderItemNo", orderItemNo);
//                List<Map<String, String>> staffInfos = fcPersonMapper.selectFcPersonInfoByParams(params);
                List<Map<String, String>> staffInfos = fcPersonMapper.selectMainFcPersonInfoByParams(params);
                staffInfos = checkIsChange(staffInfos, perNo);
                //如果不是员工本人，查询出对应的被保人信息
                if (!"0".equals(relation)) {
                    params.clear();
                    params.put("perNo", perNo);
                    params.put("relation", relation);
//                    List<Map<String, String>> fcPersonList = fcPersonMapper.selectFcPersonInfosByParams(params);
                    List<Map<String, String>> fcPersonList = fcPersonMapper.selectInfoFcPersonInfoByParams(params);
                    List<Map<String, String>> fcPersonName = fcPersonMapper.selectfcPersoninfos(params);//查找员工家属姓名信息
                    resultMap.put("fcPersonName", fcPersonName);
                    fcPersonList = checkIsChange(fcPersonList, perNo);
                    resultMap.put("fcPersonList", fcPersonList);
                }
                resultMap.put("staffInfo", staffInfos.get(0));
                resultMap.put("success", true);
                resultMap.put("code", "200");
            }


        } catch (Exception e) {
            Log.info("查询被保人信息失败：" + e.getMessage());
            resultMap = ResultUtil.error("查询被保人信息失败!");
        }
        return JSON.toJSONString(resultMap);
    }

    private List<Map<String, String>> checkIsChange(List<Map<String, String>> list, String perNo) {
        for (Map<String, String> map : list) {
            map.put("isChange", "0");
            String personId = map.get("personId");
            Integer fixedCount = fpInsurePlanMapper.selectStaffFamilyInsured(perNo, personId);
            Integer flexCount = fpInsureEflexPlanMapper.selectStaffFamilyInsured(perNo, personId);
            Integer count = fcOrderInsuredMapper.selectStaffFamilyInsured(personId);
            if (fixedCount > 0 || flexCount > 0 || count > 0) {
                //家属已投保，不能修改五要素
                map.put("isChange", "1");
            }
        }
        return list;
    }

    /**
     * 通过personID查询被保人信息
     *
     * @param token
     * @param personId
     * @return
     */
    public String getInsuredByPersonId(String token, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, String> params = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String perNo = globalInput.getCustomNo();
            //不是员工本人，查询出对应家属得信息的被保人信息
            if (StringUtil.isEmpty(personId)) {
                Map<String, Object> error = ResultUtil.error("参数为空！");
                return JSON.toJSONString(error);
            }
            params.put("perNo", perNo);
            params.put("personId", personId);
            List<Map<String, String>> fcPersonList = fcPersonMapper.selectFcPersonInfoByParams(params);
            for (Map<String, String> map : fcPersonList) {
                //***************************************************
                //校验被保人得年龄是否符合要求  被保险人年龄是否满足大于等于28天且小于等于65周岁：
                map.get("age");
                int age = Integer.parseInt(map.get("age"));
                int ageDay = Integer.parseInt(map.get("ageDay"));
                String name = map.get("name");//生日
                if (age == 0) {
                    //说明没有到一岁
                    if (ageDay < 28) {
                        Map<String, Object> error = ResultUtil.error(name + "的年龄不满足投保要求！");
                        return JSON.toJSONString(error);
                    }
                } else if (age > 0) {
                    if (age > 65) {
                        Map<String, Object> error = ResultUtil.error(name + "的年龄不满足投保要求！");
                        return JSON.toJSONString(error);
                    }
                }
                //***************************************************
                map.put("isChange", "0");
                Integer fixedCount = fpInsurePlanMapper.selectStaffFamilyInsured(perNo, personId);
                Integer flexCount = fpInsureEflexPlanMapper.selectStaffFamilyInsured(perNo, personId);
//                Integer count = fcOrderInsuredMapper.selectStaffFamilyInsured(personId);
                Integer count = fcOrderInsuredMapper.selectStaffFamilyInsuredAndOrder(personId);
                if (fixedCount > 0 || flexCount > 0 || count > 0) {
                    //家属已投保，不能修改五要素
                    map.put("isChange", "1");
                }
            }
            resultMap.put("fcPerson", fcPersonList.get(0));
            resultMap.put("success", true);
            resultMap.put("code", "200");
        } catch (Exception e) {
            Log.info("查询被保人家属信息失败：" + e.getMessage());
            resultMap = ResultUtil.error("查询被保人家属信息失败!");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 我要投保
     *
     * @param token
     * @param personInsuredReq
     * @return
     */
    @Transactional
    public String personInsured(String token, PersonInsuredReq personInsuredReq) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> result = new HashMap<>();
        try {
            String ensureCode = personInsuredReq.getEnsureCode();
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByEnsureCode(ensureCode);
            String grpNo = fcGrpInfo.getGrpNo();
            String perNo = personInsuredReq.getPerNo();
            String orderItemNo = personInsuredReq.getOrderItemNo();
            Double prem = personInsuredReq.getPrem();
            FCPerson staffInfo = personInsuredReq.getStaffInfo();
            FCPerson staffFamilyInfo = personInsuredReq.getStaffFamilyInfo();
            String relation = personInsuredReq.getRelation();
            FCOrderItemDetail fcOrderItemDetail = personInsuredReq.getFcOrderItemDetail();
            List<Map<String, String>> fcOrderBnfs = personInsuredReq.getFcOrderBnfs();
            String bnfType = personInsuredReq.getBnfType();
            //员工信息校验（五要素不可修改，不用校验）
            resultMap = checkStaffInfo(staffInfo);
            if ("500".equals(resultMap.get("code"))) {
                return JSON.toJSONString(resultMap);
            }
            //校验家属信息
            if (!"0".equals(relation)) {
                resultMap = checkStaffFamilyInfo(perNo, staffFamilyInfo);
                if ("500".equals(resultMap.get("code"))) {
                    return JSON.toJSONString(resultMap);
                }
            }
            FCPerson fcPerson = new FCPerson();
            if ("0".equals(relation)) {
                fcPerson = staffInfo;
            } else {
                fcPerson = staffFamilyInfo;
            }
            //校验受益人信息
            if (fcOrderBnfs != null && fcOrderBnfs.size() > 0) {
                resultMap = checkBnfsInfo(fcOrderBnfs);
                if ("500".equals(resultMap.get("code"))) {
                    return JSON.toJSONString(resultMap);
                }
            }
            //订单信息校验
            resultMap = checkOrderDetail(prem, fcOrderItemDetail, fcPerson);
            if ("500".equals(resultMap.get("code")) || "303".equals(resultMap.get("code"))) {
                return JSON.toJSONString(resultMap);
            }
            //校验年龄问题
            resultMap = checkAgeInfo(staffFamilyInfo, staffInfo, relation, fcOrderBnfs);
            if ("500".equals(resultMap.get("code"))) {
                return JSON.toJSONString(resultMap);
            }
            //校验健康告知是否发生变化
            String panpan = "1";
            result = checkappntinfo(orderItemNo, relation, staffInfo, staffFamilyInfo);
            if ("500".equals(result.get("code"))) {
                return JSON.toJSONString(result);
            } else if ("200".equals(result.get("code"))) {
                panpan = String.valueOf(result.get("panpan"));
            }
            //处理被保人信息
            resultMap = dealFcPersonInfo(token, perNo, relation, staffInfo, staffFamilyInfo);
            if ("500".equals(resultMap.get("code"))) {
                return JSON.toJSONString(resultMap);
            }
            if ("0".equals(relation)) {
                fcPerson = staffInfo;
            } else {
                fcPerson = staffFamilyInfo;
            }
            //处理订单信息订单信息,订单被保人信息
            resultMap = dealOrderInfo(token, grpNo, ensureCode, perNo, orderItemNo, prem, fcOrderItemDetail, fcPerson, staffInfo, relation);
            if ("500".equals(resultMap.get("code"))) {
                return JSON.toJSONString(resultMap);
            }
            if (StringUtils.isEmpty(orderItemNo)) {
                orderItemNo = (String) resultMap.get("orderItemNo");
            }
            //处理受益人信息
            resultMap = dealOrderBnfInfo(token, orderItemNo, fcOrderBnfs, bnfType);
            if ("500".equals(resultMap.get("code"))) {
                return JSON.toJSONString(resultMap);
            }

            resultMap.put("orderItemNo", orderItemNo);
            resultMap.put("perNo", perNo);
            resultMap.put("personId", fcPerson.getPersonID());
            resultMap.put("relation", relation);
            resultMap.put("panpan", panpan);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "我要投保成功");
        } catch (Exception e) {
            Log.info("我要投保失败：" + e.getMessage());
            //事务回滚
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    @Transactional
    public Map<String, Object> checkappntinfo(String orderItemNo, String relation, FCPerson staffInfo, FCPerson staffFamilyInfo) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (!StringUtil.isEmpty(orderItemNo)) {
                FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(orderItemNo);
                String sex = fcOrderInsured.getSex();
                String birthday = fcOrderInsured.getBirthday();
                String oldpersonID = fcOrderInsured.getPersonID();//上一次被保人 personid
                String newpersonID = "";//此次被保人 personid

                if (!StringUtil.isEmpty(relation) && relation.equals("0")) {
                    newpersonID = staffInfo.getPersonID();//本人 personID
                } else {
                    newpersonID = staffFamilyInfo.getPersonID();//j家属 personID
                }
                if (StringUtil.isEmpty(newpersonID)) {
                    //说明 新增 家属，此时家属没有入库呢，即说明肯定不会回显见刊健康告页面，也不会回显 影像页面
                    result.put("panpan", "1");
                    //由于被保人变化了。所以导致 确认签名页面的 影像也需要清空
                    List<FCPersonImage> fcPersonImages = fcPersonImageMapper.selectByOrderItemNo(orderItemNo);
                    if (fcPersonImages != null) {
                        for (FCPersonImage fcPersonImage : fcPersonImages) {
                            String imageNo = fcPersonImage.getImageNo();
                            fileService.dailyFileRemove(imageNo);
                        }
                    }
                } else {
                    if (!StringUtil.isEmpty(oldpersonID) && !StringUtil.isEmpty(newpersonID) && oldpersonID.equals(newpersonID)) {
                        //说明被保人没有变化，即 下个页面的健康告知继续回显
                        result.put("panpan", "0");
                    } else {
                        result.put("panpan", "1");
                        //由于被保人变化了。所以导致 确认签名页面的 影像也需要清空
                        List<FCPersonImage> fcPersonImages = fcPersonImageMapper.selectByOrderItemNo(orderItemNo);
                        if (fcPersonImages != null) {
                            for (FCPersonImage fcPersonImage : fcPersonImages) {
                                String imageNo = fcPersonImage.getImageNo();
                                fileService.dailyFileRemove(imageNo);
                            }
                        }
                    }
                }
            } else {
                result.put("panpan", "1");
            }

            result.put("success", true);
            result.put("code", "200");
            result.put("message", "健康告知判断成功");
        } catch (Exception e) {
            Log.info("健康告知判断失败:" + e.getMessage());
            throw new RuntimeException();
        }
        return result;
    }

    /**
     * 处理受益人信息
     *
     * @param orderItemNo
     * @param fcOrderBnfs
     */
    @Transactional
    public Map<String, Object> dealOrderBnfInfo(String token, String orderItemNo, List<Map<String, String>> fcOrderBnfs, String bnfType) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        FCOrder fcOrder = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
        try {
            //受益顺序
            List<String> bnfOrderList = new ArrayList<>();
            //受益比例
            Double totalBnfRatioFir = 0.0, totalBnfRatioTwo = 0.0;
            //不管之前是否存在，全部删除重新添加。
            List<FCOrderBnfRela> fcOrderBnfRelas = fcOrderBnfRelaMapper.selectlistByOrderItemNo(orderItemNo);
            for (FCOrderBnfRela fcOrderBnfRela1 : fcOrderBnfRelas) {
                String bnfNo1 = fcOrderBnfRela1.getBnfNo();
                fcOrderBnfMapper.deleteByPrimaryKey(bnfNo1);
            }
            fcOrderBnfRelaMapper.deleteByPrimaryKey(orderItemNo);
            System.out.println("受益人信息已经全部删除，现在重新添加");
            if (!StringUtil.isEmpty(bnfType) && bnfType.equals("1")) {
                if (fcOrderBnfs != null) {
                    for (Map<String, String> bnfMap : fcOrderBnfs) {
                        String bnfNo = bnfMap.get("bnfNo");
                        String relation = bnfMap.get("relation");
                        String name = bnfMap.get("name");
                        String sex = bnfMap.get("sex");
                        String birthday = bnfMap.get("birthday");
                        String idType = bnfMap.get("IDType");
                        String idNo = bnfMap.get("IDNo");
                        String idTypeEndDate = bnfMap.get("idTypeEndDate");
                        String mobilePhone = bnfMap.get("mobilePhone");
                        String province = bnfMap.get("province");
                        String city = bnfMap.get("city");
                        String county = bnfMap.get("county");
                        String detaileAddress = bnfMap.get("detaileAddress");
                        String bnfOrder = bnfMap.get("bnfOrder");
                        String nativeplace = bnfMap.get("nativeplace");
                        String zipCode = bnfMap.get("zipCode");
                        if (!StringUtil.isEmpty(idType) && idType.equals("1")) {
                            String s = name.replaceAll(" +", " ");
                            name = s.trim();
                        }
                        double bnfRatio = CommonUtil.div(Double.parseDouble(bnfMap.get("bnfRatio")), 100.0, 2);
                        String bnfKind = bnfMap.get("bnfKind");
                        if (bnfOrder.equals("1")) {
                            totalBnfRatioFir = CommonUtil.add(totalBnfRatioFir, Double.valueOf(bnfRatio));
                        } else if (bnfOrder.equals("2")) {
                            totalBnfRatioTwo = CommonUtil.add(totalBnfRatioTwo, Double.valueOf(bnfRatio));
                        }
                        bnfOrderList.add(bnfOrder);
                        FCOrderBnf fcOrderBnf = new FCOrderBnf();
                        fcOrderBnf.setRelation(relation);
                        fcOrderBnf.setName(name);
                        fcOrderBnf.setSex(sex);
                        fcOrderBnf.setBirthday(birthday);
                        fcOrderBnf.setIDType(idType);
                        fcOrderBnf.setIDNo(idNo);
                        fcOrderBnf.setIdTypeEndDate(idTypeEndDate);
                        fcOrderBnf.setMobilePhone(mobilePhone);
                        fcOrderBnf.setProvince(province);
                        fcOrderBnf.setCity(city);
                        fcOrderBnf.setCounty(county);
                        fcOrderBnf.setDetaileAddress(detaileAddress);
                        fcOrderBnf.setZipCode(zipCode);
                        fcOrderBnf.setOperator(globalInput.getUserNo());
                        fcOrderBnf.setNativePlace(nativeplace);
                        FCOrderBnfRela fcOrderBnfRela = new FCOrderBnfRela();
                        fcOrderBnfRela.setOrderNo(fcOrder.getOrderNo());
                        fcOrderBnfRela.setOrderItemNo(orderItemNo);
                        fcOrderBnfRela.setBnfNo(bnfNo);
                        fcOrderBnfRela.setBnfType("1");
                        fcOrderBnfRela.setRelation(relation);
                        fcOrderBnfRela.setBnfKind(bnfKind);
                        fcOrderBnfRela.setBnfOrder(Integer.valueOf(bnfOrder));
                        fcOrderBnfRela.setBnfRatio(Double.valueOf(bnfRatio));
                        fcOrderBnfRela.setOperator(globalInput.getUserNo());
                        bnfNo = maxNoService.createMaxNo("BnfNo", "", 20);
                        fcOrderBnf.setBnfNo(bnfNo);
                        fcOrderBnf = CommonUtil.initObject(fcOrderBnf, "INSERT");
                        fcOrderBnfMapper.insertSelectiveAll(fcOrderBnf);
                        fcOrderBnfRela.setBnfNo(bnfNo);
                        fcOrderBnfRela = CommonUtil.initObject(fcOrderBnfRela, "INSERT");
                        fcOrderBnfRelaMapper.insertSelective(fcOrderBnfRela);
                    }
                    //校验受益顺序
                    if (!bnfOrderList.contains("1")) {
                        return ResultUtil.error("受请录入指定受益人信息！");
                    }
                    //校验
                    if (bnfOrderList.contains("1") && !"1.0".equals(totalBnfRatioFir.toString())) {
                        return ResultUtil.error("受益顺序为1的受益比例之和不等于100，请核对！");
                    }
                    if (bnfOrderList.contains("2") && !"1.0".equals(totalBnfRatioTwo.toString())) {
                        return ResultUtil.error("受益顺序为2的受益比例之和不等于100，请核对！");
                    }
                }
            } else if (!StringUtil.isEmpty(bnfType) && bnfType.equals("0")) {
                //1. 判断该订单是否已经存在  受益人关系表。如果存在修改 受益人类别，并且删除该订单对应的受益人信息
                FCOrderBnfRela fcOrderBnfRela = fcOrderBnfRelaMapper.selectByPrimaryKey(orderItemNo);
                if (fcOrderBnfRela == null) {
                    //如果 受益人关系表为空，需要新增
                    FCOrderBnfRela fcOrderBnfRela1 = new FCOrderBnfRela();
                    String bnfNo = maxNoService.createMaxNo("BnfNo", "", 20);
                    fcOrderBnfRela1.setOrderNo(fcOrder.getOrderNo());
                    fcOrderBnfRela1.setOrderItemNo(orderItemNo);
                    fcOrderBnfRela1.setBnfNo(bnfNo);
                    fcOrderBnfRela1.setBnfType("0");
                    fcOrderBnfRela1.setRelation("06");
                    fcOrderBnfRela1.setOperator(globalInput.getUserNo());
                    fcOrderBnfRela1 = CommonUtil.initObject(fcOrderBnfRela1, "INSERT");
                    fcOrderBnfRelaMapper.insertSelective(fcOrderBnfRela1);
                } else {
                    //受益人关系表不为空，说明之前 新增了。这时判断是法定受益人还是 指定受益人， 如果是指定受益人，删除对应受益人信息
                    String bnfType1 = fcOrderBnfRela.getBnfType();
                    String bnfNo = fcOrderBnfRela.getBnfNo();
//                    受益人类型 0-法定受益人  1-自选受益人
                    if (bnfType1.equals("1")) {
                        fcOrderBnfMapper.deleteByPrimaryKey(bnfNo);
                        fcOrderBnfRela.setBnfType("0");
                        fcOrderBnfRela = CommonUtil.initObject(fcOrderBnfRela, "UPDATE");
                        fcOrderBnfRelaMapper.updateByPrimaryKeySelective(fcOrderBnfRela);
                    } else {
                        fcOrderBnfRela = CommonUtil.initObject(fcOrderBnfRela, "UPDATE");
                        fcOrderBnfRelaMapper.updateByPrimaryKeySelective(fcOrderBnfRela);
                    }
                }

            }
            resultMap = ResultUtil.success("受益人信息配置成功");
        } catch (Exception e) {
            Log.info("受益人信息配置失败:" + e.getMessage());
            throw new RuntimeException();
        }
        return resultMap;
    }

    /**
     * 员工信息校验
     *
     * @param staffInfo
     * @return
     */
    private Map<String, Object> checkStaffInfo(FCPerson staffInfo) throws Exception {
        Map<String, Object> result = new HashMap<>();
        List<String> errorMsgList = new ArrayList<>();
        //国籍码值
        List<String> nativeplaceList = fdCodeMapper.selectCodeKeyByCodeType("Nativeplace");

        String idTypeEndDate = staffInfo.getIdTypeEndDate();
        String nativeplace = staffInfo.getNativeplace();
        String province = staffInfo.getProvince();
        String city = staffInfo.getCity();
        String county = staffInfo.getCounty();
        String detaileAddress = staffInfo.getDetaileAddress();
        String zipCode = staffInfo.getZipCode();
        String eMail = staffInfo.getEMail();
        String birthDay = staffInfo.getBirthDate();
        String iDNo = staffInfo.getIDNo();
        String iDType = staffInfo.getIDType();
        String sex = staffInfo.getSex();
        String name = staffInfo.getName();
        String personID = staffInfo.getPersonID();

        //校验年龄
//        int age = Integer.parseInt(DateTimeUtil.getAge(birthDay));
        int age = DateTimeUtil.getCurrentAge(birthDay, DateTimeUtil.getCurrentDate());
        long distanceDays = DateTimeUtil.getDistanceDays(birthDay, DateTimeUtil.getCurrentDate());//出生天数
        if (distanceDays < 28 || age > 65) {
            errorMsgList.add("被保人年龄必须在28天到65周岁！");
        }
        //校验证件有效期
        if (StringUtils.isEmpty(idTypeEndDate)) {
            errorMsgList.add("证件有效期不能为空！");
        } else {
            if (!DateTimeUtil.isDate(idTypeEndDate)) {
                errorMsgList.add("证件有效期有误！");
            } else {
                if (!DateTimeUtil.checkDate(DateTimeUtil.getCurrentDate(), idTypeEndDate)) {
                    errorMsgList.add("证件有效期不得小于当前日期！");
                }
            }
            //使用公共方法校验证件有效期
            String msg = CheckUtils.checkDailyIdtypeEndDate(iDType, idTypeEndDate, birthDay);
            if (!StringUtil.isEmpty(msg)) {
                errorMsgList.add(msg);
            }
        }

        //校验证件号码
        if (StringUtils.isEmpty(iDNo)) {
            errorMsgList.add("证件号码不能为空！");
        } else if (errorMsgList.size() == 0) {
            //身份证号+户口本的校验规则一致
            if ("0".equals(iDType) || "4".equals(iDType)) {
                String errMsg = IDCardUtil.checkIDCard(iDNo, sex, birthDay);
                if (StringUtils.isEmpty(errMsg)) {
                    iDNo = iDNo.toUpperCase();
                } else {
                    errorMsgList.add(errMsg);
                }
            } else {
                if (iDNo.length() < 3) {
                    errorMsgList.add("证件号码长度小于3位！");
                }
            }
        }
        //校验国籍
        if (StringUtils.isEmpty(nativeplace)) {
            errorMsgList.add("国籍不能为空！");
        } else {
            if (!nativeplaceList.contains(nativeplace)) {
                errorMsgList.add("国籍填写有误！");
            }
            String checkNationality = addressCheckService.checkNationalityCode(nativeplace);
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(checkNationality)) {
                errorMsgList.add(checkNationality);
            }
        }
        //通讯地址
        if (StringUtils.isEmpty(province) || StringUtils.isEmpty(city) ||
                StringUtils.isEmpty(county) || StringUtils.isEmpty(detaileAddress)) {
            errorMsgList.add("通讯地址不能为空！");
        }
        //邮编
        if (StringUtils.isEmpty(zipCode)) {
            errorMsgList.add("邮编不能为空！");
        } else {
        }
        //邮箱
        if (!StringUtils.isEmpty(eMail)) {
            if (!CheckUtils.checkEmail(eMail)) {
                errorMsgList.add("邮箱格式有误！");
            }
        }
        Map<String, String> checkMap = new HashMap<>();
        checkMap.put("idNo", iDNo);
        checkMap.put("idType", iDType);
        checkMap.put("name", name);
        checkMap.put("sex", sex);
        checkMap.put("birthday", birthDay);
        checkMap.put("personId", personID);
        if (!ensureMakeService.checkPerIdNoIsExists(checkMap)) {//已投保，不允许修改五要素
            errorMsgList.add("当前家属已投保，不允许修改五要素！");
        }
        if (errorMsgList.size() > 0) {
            result = ResultUtil.error(org.apache.commons.lang3.StringUtils.join(errorMsgList, ","));
        }
        return result;
    }

    /**
     * 年龄校验
     *
     * @param staffInfo
     * @return
     */
    private Map<String, Object> checkAgeInfo(FCPerson staffFamilyInfo, FCPerson staffInfo, String relation, List<Map<String, String>> fcOrderBnfs) throws Exception {
        Map<String, Object> result = new HashMap<>();
        List<String> errorMsgList = new ArrayList<>();


        String idTypeEndDate = staffInfo.getIdTypeEndDate();
        String name = staffInfo.getName();
        String nativeplace = staffInfo.getNativeplace();
        String province = staffInfo.getProvince();
        String city = staffInfo.getCity();
        String county = staffInfo.getCounty();
        String detaileAddress = staffInfo.getDetaileAddress();
        String zipCode = staffInfo.getZipCode();
        String eMail = staffInfo.getEMail();
        String birthDay = staffInfo.getBirthDate();
        String iDNo = staffInfo.getIDNo();
        String iDType = staffInfo.getIDType();
        String sex = staffInfo.getSex();
        String yearSalary = staffInfo.getYearSalary();
        String occupationCode = staffInfo.getOccupationCode();
        String mobilePhone = staffInfo.getMobilePhone();
        int age = Integer.parseInt(DateTimeUtil.getAge(birthDay));
        //****************
        //校验姓名和 电话
        if (!StringUtil.isEmpty(iDType) && !iDType.equals("1")) {
            String s = CheckUtils.checkChineseName(name);
            if (!StringUtil.isEmpty(s)) {
                return ResultUtil.error(s);
            }
        } else if (!StringUtil.isEmpty(iDType) && iDType.equals("1")) {
            String s = CheckUtils.checkForeignName(name);
            String trim = name.trim();
            name = trim.replaceAll(" +", " ");
            if (!StringUtil.isEmpty(s)) {
                return ResultUtil.error(s);
            }
        }
        //****************
        //校验国籍与证件类型，证件号码以及年龄的关系
        String msg = CheckUtils.checkNativeplaceByIdTypeandIdNoandAge("1", nativeplace, iDType, iDNo, age, birthDay, sex);
        if (!StringUtil.isEmpty(msg)) {
            return ResultUtil.error("员工:" + msg);
        }
        //校验员工校验证件
        if (!StringUtil.isEmpty(idTypeEndDate)) {
            if (iDType.equals("7") && !idTypeEndDate.equals(DateTimeUtil.getAppointDate(new SimpleDateFormat("yyyy-MM-dd").parse(birthDay), 3, -1))) {
                return ResultUtil.error("员工证件类型为“出生证”时，则证件有效期默认“客户出生日期+3年-1天”且不允许修改；");
            }
            String errmsg = CheckUtils.checkIdtypeEndDate(age, iDType, idTypeEndDate, birthDay);
            if (!StringUtil.isEmpty(errmsg)) {
                return ResultUtil.error("员工:" + errmsg);
            }
        }
        //校验员工手机号
        if (!StringUtil.isEmpty(mobilePhone)) {
            if (!CheckUtils.checkMobilePhone(mobilePhone)) {
                return ResultUtil.error("员工手机号格式错误，请检查");
            }
        }
        //2. 校验 员工及家属 职业 相关
        Map<String, String> maps = new HashMap<>();
        maps.put("sign", "2");//1：员工 2：家属
        maps.put("idType", iDType);//证件类型
        maps.put("idNo", iDNo);//证件号
        maps.put("birthDay", birthDay);//出生日期
        maps.put("sex", sex);//性别
        maps.put("nativeplace", nativeplace);//国籍
        maps.put("idTypeEndDate", idTypeEndDate);//证件有效期
        maps.put("occupationCode", occupationCode);//职业代码
        if (!StringUtil.isEmpty(occupationCode)) {
            String resultMsg = CheckUtils.checkSinglePeople(maps);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(resultMsg)) {
                result.put("success", false);
                result.put("code", "500");
                result.put("message", resultMsg);
                return result;
            }

        }
        if (!StringUtil.isEmpty(relation) && !relation.equals("0")) {
            //校验年收入
            if (StringUtil.isEmpty(yearSalary) || yearSalary.equals("0")) {
                return ResultUtil.error("主被保人年收入信息未填写，请确认");
            }
        }
        //校验校验详细地址：长度大于等于7，包括真实性汉字
        if (detaileAddress.length() < 9) {
            System.out.println("你输入的详细地址为:" + detaileAddress);
            return ResultUtil.error("员工详细地址长度大于等于9！");
        }
        if (!detaileAddress.contains("村") && !detaileAddress.contains("街") && !detaileAddress.contains("路") && !detaileAddress.contains("道") &&
                !detaileAddress.contains("弄") && !detaileAddress.contains("胡同") && !detaileAddress.contains("院") && !detaileAddress.contains("信箱") &&
                !detaileAddress.contains("小区") && !detaileAddress.contains("号") && !detaileAddress.contains("房") && !detaileAddress.contains("室") &&
                !detaileAddress.contains("栋") && !detaileAddress.contains("楼") && !detaileAddress.contains("幢")) {
            return ResultUtil.error("请正确输入员工详细地址！");
        }

        if (!"0".equals(relation)) {
            String staffname = staffFamilyInfo.getName();
            String staffiDType = staffFamilyInfo.getIDType();
            String staffiDNo = staffFamilyInfo.getIDNo();
            String staffbirthDay = staffFamilyInfo.getBirthDate();
            String staffsex = staffFamilyInfo.getSex();
            String staffoccupationCode = staffFamilyInfo.getOccupationCode();
            String staffoccupationType = staffFamilyInfo.getOccupationType();
            String FamilydetaileAddress = staffFamilyInfo.getDetaileAddress();
            String staffnativeplace = staffFamilyInfo.getNativeplace();
            String staffidTypeEndDate = staffFamilyInfo.getIdTypeEndDate();
            String staffmobilePhone = staffFamilyInfo.getMobilePhone();
            int staffage = Integer.parseInt(DateTimeUtil.getAge(staffbirthDay));
            //****************
            //校验姓名和 电话
            if (!StringUtil.isEmpty(staffiDType) && !staffiDType.equals("1")) {
                String s = CheckUtils.checkChineseName(staffname);
                if (!StringUtil.isEmpty(s)) {
                    return ResultUtil.error(s);
                }
            } else if (!StringUtil.isEmpty(staffiDType) && staffiDType.equals("1")) {
                String s = CheckUtils.checkForeignName(staffname);
                String trim = staffname.trim();
                staffname = trim.replaceAll(" +", " ");
                if (!StringUtil.isEmpty(s)) {
                    return ResultUtil.error(s);
                }
            }
            //****************
            //校验国籍与证件类型，证件号码以及年龄的关系
            String mssg = CheckUtils.checkNativeplaceByIdTypeandIdNoandAge("1", staffnativeplace, staffiDType, staffiDNo, staffage, staffbirthDay, staffsex);
            if (!StringUtil.isEmpty(mssg)) {
                return ResultUtil.error("家属:" + mssg);
            }
            //校验家属校验证件
            if (!StringUtil.isEmpty(staffidTypeEndDate)) {
                if (iDType.equals("7") && !idTypeEndDate.equals(DateTimeUtil.getAppointDate(new SimpleDateFormat("yyyy-MM-dd").parse(birthDay), 3, -1))) {
                    return ResultUtil.error("家属证件类型为“出生证”时，则证件有效期默认“客户出生日期+3年-1天”且不允许修改；");
                }
                String errmsg = CheckUtils.checkIdtypeEndDate(staffage, staffiDType, staffidTypeEndDate, staffbirthDay);
                if (!StringUtil.isEmpty(errmsg)) {
                    return ResultUtil.error("家属:" + errmsg);
                }
            }
            //校验家属手机号
            if (!StringUtil.isEmpty(staffmobilePhone)) {
                if (!CheckUtils.checkMobilePhone(staffmobilePhone)) {
                    return ResultUtil.error("家属手机号格式错误，请检查");
                }
            }
            //使用公共方法校验证件有效期
            String msag = CheckUtils.checkDailyIdtypeEndDate(staffiDType, staffidTypeEndDate, staffbirthDay);
            if (!StringUtil.isEmpty(msg)) {
                return ResultUtil.error("被保人" + msag);
            }
            //2. 校验 员工及家属 职业 相关
            //校验人员--add by wudezhong
            Map<String, String> mapss = new HashMap<>();
            mapss.put("sign", "2");//1：员工 2：家属
            mapss.put("idType", staffiDType);//证件类型
            mapss.put("idNo", staffiDNo);//证件号
            mapss.put("birthDay", staffbirthDay);//出生日期
            mapss.put("sex", staffsex);//性别
            mapss.put("nativeplace", staffnativeplace);//国籍
            mapss.put("idTypeEndDate", staffidTypeEndDate);//证件有效期
            mapss.put("occupationCode", staffoccupationCode);//职业代码
            String resultMsg = CheckUtils.checkSinglePeople(mapss);
            if (!StringUtil.isEmpty(resultMsg)) {
                result.put("success", false);
                result.put("code", "500");
                result.put("message", resultMsg);
                return result;
            }


            if ("2".equals(relation)) {  //配偶  需要 异性
                if (sex.equals(staffsex)) {
                    return ResultUtil.error("配偶关系，性别必须是一男一女!");
                }
                if (staffage < 18) {
                    return ResultUtil.error("配偶关系年龄需大于等于18");
                }
            } else if ("1".equals(relation)) {
                if (age > staffage) {
                    return ResultUtil.error("父母年龄不能小于员工年龄！");
                }
            } else if ("3".equals(relation)) {
                if (age < staffage) {
                    return ResultUtil.error("子女年龄不能大于员工年龄！");
                }
            }
            //校验校验详细地址：长度大于等于7，包括真实性汉字
            if (FamilydetaileAddress.length() < 9) {
                return ResultUtil.error("员工详细地址长度大于等于9！");
            }
            if (!FamilydetaileAddress.contains("村") && !FamilydetaileAddress.contains("街") && !FamilydetaileAddress.contains("路") && !FamilydetaileAddress.contains("道") &&
                    !FamilydetaileAddress.contains("弄") && !FamilydetaileAddress.contains("胡同") && !FamilydetaileAddress.contains("院") && !FamilydetaileAddress.contains("信箱") &&
                    !FamilydetaileAddress.contains("小区") && !FamilydetaileAddress.contains("号") && !FamilydetaileAddress.contains("房") && !FamilydetaileAddress.contains("室") &&
                    !FamilydetaileAddress.contains("栋") && !FamilydetaileAddress.contains("楼") && !FamilydetaileAddress.contains("幢")) {
                return ResultUtil.error("请正确输入员工家属的详细地址！");
            }
        }
        if (fcOrderBnfs != null && fcOrderBnfs.size() > 0) {
            for (Map<String, String> bnfMap : fcOrderBnfs) {
                String bnfNo = bnfMap.get("bnfNo");
                String bnfrelation = bnfMap.get("relation");
                String bnfname = bnfMap.get("name");
                String bnfsex = bnfMap.get("sex");
                String bnfbirthday = bnfMap.get("birthday");
                String bnfidType = bnfMap.get("IDType");
                String bnfidNo = bnfMap.get("IDNo");
                String bnfidTypeEndDate = bnfMap.get("idTypeEndDate");
                String bnfmobilePhone = bnfMap.get("mobilePhone");
                String bnfprovince = bnfMap.get("province");
                String bnfcity = bnfMap.get("city");
                String bnfcounty = bnfMap.get("county");
                String bnfdetaileAddress = bnfMap.get("detaileAddress");
                String bnfbnfOrder = bnfMap.get("bnfOrder");
                String bnfnativeplace = bnfMap.get("nativeplace");
                String bnfzipCode = bnfMap.get("zipCode");
                //****************
                //校验姓名和 电话
                if (!StringUtil.isEmpty(bnfidType) && !bnfidType.equals("1")) {
                    String s = CheckUtils.checkChineseName(bnfname);
                    if (!StringUtil.isEmpty(s)) {
                        return ResultUtil.error(s);
                    }
                } else if (!StringUtil.isEmpty(bnfidType) && bnfidType.equals("1")) {
//                    String s = CheckUtils.checkEnglishName(bnfname);
                    String trim = bnfname.trim();
                    bnfname = trim.replaceAll(" +", " ");
//                    if (!StringUtil.isEmpty(s)) {
//                        return ResultUtil.error(s);
//                    }
                }
                //****************

                int bnfage = Integer.parseInt(DateTimeUtil.getAge(bnfbirthday));
                String mssg = CheckUtils.checkNativeplaceByIdTypeandIdNoandAge("1", bnfnativeplace, bnfidType, bnfidNo, bnfage, bnfbirthday, bnfsex);
                if (!StringUtil.isEmpty(mssg)) {
                    return ResultUtil.error("受益人:" + mssg);
                }
                //校验受益人校验证件
                if (!StringUtil.isEmpty(bnfidTypeEndDate)) {
                    if (iDType.equals("7") && !idTypeEndDate.equals(DateTimeUtil.getAppointDate(new SimpleDateFormat("yyyy-MM-dd").parse(birthDay), 3, -1))) {
                        return ResultUtil.error("受益人证件类型为“出生证”时，则证件有效期默认“客户出生日期+3年-1天”且不允许修改；");
                    }
                    String errmsg = CheckUtils.checkIdtypeEndDate(bnfage, bnfidType, bnfidTypeEndDate, bnfbirthday);
                    if (!StringUtil.isEmpty(errmsg)) {
                        return ResultUtil.error("受益人:" + errmsg);
                    }
                }
                //校验手机号
                if (!StringUtil.isEmpty(bnfmobilePhone)) {
                    if (!CheckUtils.checkMobilePhone(bnfmobilePhone)) {
                        return ResultUtil.error("受益人 " + bnfname + " 手机号格式错误，请检查");
                    }
                }
                String amsg = CheckUtils.checkDailyIdtypeEndDate(bnfidType, bnfidTypeEndDate, bnfbirthday);
                if (!StringUtil.isEmpty(amsg)) {
                    return ResultUtil.error("受益人:" + bnfname + amsg);
                }


                if ("2".equals(bnfrelation)) {  //配偶  需要 异性
                    if (sex.equals(bnfrelation)) {
                        return ResultUtil.error("受益人<" + bnfname + ">的配偶关系，性别必须是一男一女!");
                    }
                    if (bnfage < 18) {
                        return ResultUtil.error("受益人<" + bnfname + ">配偶关系年龄需大于等于18");
                    }
                } else if ("1".equals(bnfrelation)) {
                    if (age > bnfage) {
                        return ResultUtil.error("受益人<" + bnfname + ">父母年龄不能小于员工年龄！");
                    }
                } else if ("3".equals(bnfrelation)) {
                    if (age < bnfage) {
                        return ResultUtil.error("受益人<" + bnfname + ">子女年龄不能大于员工年龄！");
                    }
                }
                //校验校验详细地址：长度大于等于9，包括真实性汉字
                if (bnfdetaileAddress.length() < 9) {
                    return ResultUtil.error("受益人<" + bnfname + ">详细地址长度大于等于9！");
                }
                if (!bnfdetaileAddress.contains("村") && !bnfdetaileAddress.contains("街") && !bnfdetaileAddress.contains("路") && !bnfdetaileAddress.contains("道") &&
                        !bnfdetaileAddress.contains("弄") && !bnfdetaileAddress.contains("胡同") && !bnfdetaileAddress.contains("院") && !bnfdetaileAddress.contains("信箱") &&
                        !bnfdetaileAddress.contains("小区") && !bnfdetaileAddress.contains("号") && !bnfdetaileAddress.contains("房") && !bnfdetaileAddress.contains("室") &&
                        !bnfdetaileAddress.contains("栋") && !bnfdetaileAddress.contains("楼") && !bnfdetaileAddress.contains("幢")) {
                    return ResultUtil.error("受益人<" + bnfname + ">的详细地址请正确输入！");
                }
            }
        }
        return result;
    }

    private Map<String, Object> checkocc(String occupationCode, String birthDay, String sex) {
        Map<String, Object> resultMap = new HashMap<>();
        //校验家属年龄和职业信息的关系
        if (StringUtil.isEmpty(occupationCode)) {
            resultMap.put("code", "200");
            resultMap.put("message", "此处无需校验");
            return resultMap;
        }
        List<String> list = new ArrayList();
        list.add("2099907");
        list.add("3020109");
        list.add("8000101");
        list.add("8000102");
        boolean contains = list.contains(occupationCode);
        int age = DateTimeUtil.getCurrentAge(birthDay, DateTimeUtil.getCurrentDate());//年龄
        if ((age <= 6 && !occupationCode.equals("2099908")) || (age > 6 && occupationCode.equals("2099908")) || (sex == "0" && occupationCode.equals("4071203"))
                || ((age >= 7 || age <= 16) && contains)
                || ((age < 7 || age > 16) && contains)) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "职业信息与年龄不匹配，请重新确认！");
            return resultMap;
        }
        resultMap.put("code", "200");
        resultMap.put("message", "校验成功");
        return resultMap;
    }


    /**
     * 受益人信息校验
     *
     * @param fcOrderBnfs
     * @return
     */
    private Map<String, Object> checkBnfsInfo(List<Map<String, String>> fcOrderBnfs) {
        Map<String, Object> result = new HashMap<>();
        List<String> errorMsgList = new ArrayList<>();
        for (Map<String, String> fcOrderBnf : fcOrderBnfs) {
            Map<String, Object> map = checkInisEmpty(fcOrderBnf);//校验非空
            if ("500".equals(map.get("code"))) {
                return map;
            }
            String idTypeEndDate = fcOrderBnf.get("idTypeEndDate");
            String name = fcOrderBnf.get("name");
            String iDType = fcOrderBnf.get("IDType");
            String iDNo = fcOrderBnf.get("IDNo");
            String sex = fcOrderBnf.get("sex");
            String birthday = fcOrderBnf.get("birthday");
            String nativeplace = fcOrderBnf.get("nativeplace");
            //校验证件有效期
            if (StringUtils.isEmpty(idTypeEndDate)) {
                errorMsgList.add("证件有效期不能为空！");
            } else {
                if (!DateTimeUtil.isDate(idTypeEndDate)) {
                    errorMsgList.add("证件有效期有误！");
                } else {
                    if (!DateTimeUtil.checkDate(DateTimeUtil.getCurrentDate(), idTypeEndDate)) {
                        errorMsgList.add("证件有效期不得小于当前日期！");
                    }
                }
            }
            //校验姓名
            if (org.apache.commons.lang.StringUtils.isEmpty(name)) {
                errorMsgList.add("姓名不能为空！");
            } else {
                //证件类型是身份证和户口本
               /* if ("0".equals(iDType) || "4".equals(iDType)) {
                    if (!name.matches("^[\\u0391-\\uFFE5]+$")) {
                        errorMsgList.add("姓名仅能输入中文!");
                    }
                }*/
            }

            //校验证件号码
            if (org.apache.commons.lang.StringUtils.isEmpty(iDNo)) {
                errorMsgList.add("证件号码不能为空！");
            } else if (errorMsgList.size() == 0) {
                //身份证号+户口簿的校验规则一致
                if ("0".equals(iDType) || "4".equals(iDType)) {
                    // 经由核心、运营确认，身份证号仅能为18位 add by wudezhong 2021.5.27
                    if (iDNo.length() != 18) {
                        errorMsgList.add("证件号码长度应为18位！");
                    } else {
                        String errMsg = IDCardUtil.checkIDCard(iDNo, sex, birthday);
                        if (org.apache.commons.lang.StringUtils.isEmpty(errMsg)) {
                            iDNo = iDNo.toUpperCase();
                        } else {
                            errorMsgList.add(errMsg);
                        }
                    }
                } else {
                    if (iDNo.length() < 3) {
                        errorMsgList.add("证件号码长度小于3位！");
                    }
                }
            }
            //校验国籍
            if (errorMsgList.size() == 0) {
                if (StringUtils.isEmpty(nativeplace)) {
                    errorMsgList.add("受益人国籍不能为空！");
                } else {
                    int age = Integer.parseInt(DateTimeUtil.getAge(birthday));
                    String msg = CheckUtils.checkNativeplaceByIdTypeandIdNoandAge("1", nativeplace, iDType, iDNo, age, birthday, sex);
                    if (!org.apache.commons.lang.StringUtils.isEmpty(msg)) {
                        errorMsgList.add(msg);
                    }
                    String checkNationality = addressCheckService.checkNationalityCode(nativeplace);
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(checkNationality)) {
                        errorMsgList.add(checkNationality);
                    }
                }
            }
            if (errorMsgList.size() > 0) {
                result = ResultUtil.error(org.apache.commons.lang3.StringUtils.join(errorMsgList, ","));
                return result;
            }
        }
        return ResultUtil.success("校验受益人信息成功");
    }

    private Map<String, Object> checkInisEmpty(Map<String, String> fcOrderBnf) {
        if (StringUtil.isEmpty(fcOrderBnf.get("relation"))) {
            Map<String, Object> error = ResultUtil.error("受益人关系不能为空！");
            return error;
        }
        if (StringUtil.isEmpty(fcOrderBnf.get("name"))) {
            Map<String, Object> error = ResultUtil.error("受益人姓名不能为空！");
            return error;
        }
        if (StringUtil.isEmpty(fcOrderBnf.get("IDType"))) {
            Map<String, Object> error = ResultUtil.error("受益人证件类别不能为空！");
            return error;
        }
        if (StringUtil.isEmpty(fcOrderBnf.get("IDNo"))) {
            Map<String, Object> error = ResultUtil.error("受益人证件号码不能为空！");
            return error;
        }
        if (StringUtil.isEmpty(fcOrderBnf.get("birthday"))) {
            Map<String, Object> error = ResultUtil.error("受益人出生日期不能为空！");
            return error;
        }

        if (StringUtil.isEmpty(fcOrderBnf.get("sex"))) {
            Map<String, Object> error = ResultUtil.error("受益人性别不能为空！");
            return error;
        }
        if (StringUtil.isEmpty(fcOrderBnf.get("idTypeEndDate"))) {
            Map<String, Object> error = ResultUtil.error("受益人证件有效期不能为空！");
            return error;
        }
        if (StringUtil.isEmpty(fcOrderBnf.get("bnfOrder"))) {
            Map<String, Object> error = ResultUtil.error("受益人受益顺序不能为空！");
            return error;
        }
        if (StringUtil.isEmpty(fcOrderBnf.get("bnfRatio"))) {
            Map<String, Object> error = ResultUtil.error("受益人受益比例不能为空！");
            return error;
        }
        if (StringUtil.isEmpty(fcOrderBnf.get("city")) && StringUtil.isEmpty(fcOrderBnf.get("detaileAddress")) && StringUtil.isEmpty(fcOrderBnf.get("county")) && StringUtil.isEmpty(fcOrderBnf.get("province"))) {
            Map<String, Object> error = ResultUtil.error("受益人联系地址不能为为空！");
            return error;
        }

        if (StringUtil.isEmpty(fcOrderBnf.get("mobilePhone"))) {
            Map<String, Object> error = ResultUtil.error("受益人电话号码不能为空！");
            return error;
        }


        return ResultUtil.success("校验非空成功");
    }

    /**
     * 订单信息校验
     *
     * @param prem
     * @param fcOrderItemDetail
     * @return
     */
    public Map<String, Object> checkOrderDetail(Double prem, FCOrderItemDetail fcOrderItemDetail, FCPerson fcPerson) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isEmpty(prem)) {
            return ResultUtil.error("保费不能为空！");
        }
        //产品编码（险种编码）
        String productCode = fcOrderItemDetail.getProductCode();
        //产品责任编码（计划编码）
        String dutyCode = fcOrderItemDetail.getDutyCode();
        //保额
        double insuredAmount = fcOrderItemDetail.getInsuredAmount();
        //保险期间 01-保至85周岁  02-终身
        String insurePeriod = fcOrderItemDetail.getInsurePeriod();
        //缴费频次  1-趸交  2-年交
        String payFrequency = fcOrderItemDetail.getPayFrequency();
        //缴费期间 01-一次性交清  02-5年交   03-10年交  04-20年交
        String payPeriod = fcOrderItemDetail.getPayPeriod();
        if (StringUtils.isEmpty(insuredAmount)) {
            return ResultUtil.error("请录入保额！");
        } else {
            int amnt = new Double(insuredAmount).intValue();
            if (amnt < 0 || amnt > 200) {
//                return ResultUtil.error("保额为0-200的整数！");
            }
        }
        if (StringUtils.isEmpty(dutyCode)) {
            return ResultUtil.error("计划编码不能为空！");
        }
        if (StringUtils.isEmpty(insurePeriod)) {
            return ResultUtil.error("保险期间不能为空！");
        }
        if (StringUtils.isEmpty(payFrequency)) {
            return ResultUtil.error("缴费频次不能为空！");
        }
        if (StringUtils.isEmpty(payPeriod)) {
            return ResultUtil.error("缴费期间不能为空！");
        }
        if ("1".equals(payFrequency)) {
            if (!"01".equals(payPeriod)) {
                return ResultUtil.error("当缴费频次为趸交时，缴费期间应选择一次性交情！");
            }
        } else {
            if ("01".equals(payPeriod)) {
                return ResultUtil.error("当缴费频次为年交时，缴费期间不应选择一次性交情！");
            }
        }
        // 校验选择的被保险人 年龄是否符合要求，年龄与缴费期间、缴费频次是否 关联正确。
        int age = DateTimeUtil.getCurrentAge(fcPerson.getBirthDate(), DateTimeUtil.getCurrentDate());
        if (age >= 16 && age <= 45) {
            //当年龄大于等于16周岁小于等于45周岁，交费频次选择“年交”，缴费期间只能选择“5年交”、“10年交”、“20年交”
            if (payFrequency.equals("2") && payPeriod.equals("01")) {
                return ResultUtil.error("交费频次和缴费期间冲突。", "303");
            }
        }
        if (age >= 46 && age <= 55) {
//          当年龄大于等于46周岁小于等于55周岁，交费频次选择“年交”，缴费期间只能选择“5年交”、“10年交”。
            if ((payFrequency.equals("2") && payPeriod.equals("01")) || (payFrequency.equals("2") && payPeriod.equals("04"))) {
                return ResultUtil.error("交费频次和缴费期间冲突。", "303");
            }
        }
        if (payFrequency.equals("1") && !payPeriod.equals("01")) {
//          当交费频次选择“趸缴”，缴费期间只能选择“一次性交清”；
            return ResultUtil.error("交费频次和缴费期间冲突。", "303");
        }
        //保费试算
        DailyPremTrail dailyPremTrail = new DailyPremTrail();
        dailyPremTrail.setAmount(String.valueOf(CommonUtil.mul(insuredAmount, 10000.0)));
        dailyPremTrail.setBirthDay(fcPerson.getBirthDate());
        dailyPremTrail.setGender(fcPerson.getSex());
        //基础单传投保日期，个人投保传投保当天
        dailyPremTrail.setInsureDate(DateTimeUtil.getCurrentDate());
        dailyPremTrail.setInsurePeriod(insurePeriod);
        dailyPremTrail.setPayPeriod(payPeriod);
        dailyPremTrail.setPlanCode(dutyCode);
        dailyPremTrail.setRiskCode(productCode);

        Map<String, Object> premMap = premTrailService.dailyPremTrail(dailyPremTrail);
        if (premMap.get("Prem") == null) {
            return ResultUtil.error("计算保费发生异常，请查看", "303");
        }
        String newPrem = String.valueOf(premMap.get("Prem"));
        String oldprem = String.valueOf(prem);
        if (new BigDecimal(newPrem).compareTo(new BigDecimal(oldprem)) != 0) {
            return ResultUtil.success("保费出现变更，请知悉！", newPrem, "303");
        }
        return result;
    }


    /**
     * 家属信息校验
     *
     * @param staffFamilyInfo
     */
    private Map<String, Object> checkStaffFamilyInfo(String perNo, FCPerson staffFamilyInfo) throws Exception {
        Map<String, Object> result = new HashMap<>();
        List<String> errorMsgList = new ArrayList<>();
        //查询所有的证件类型码值
        List<String> idTypeList = fdCodeMapper.selectCodeKeyByCodeType("IDType");
        String personID = staffFamilyInfo.getPersonID();
        String name = staffFamilyInfo.getName();
        String iDType = staffFamilyInfo.getIDType();
        String iDNo = staffFamilyInfo.getIDNo();
        String birthDay = staffFamilyInfo.getBirthDate();
        String sex = staffFamilyInfo.getSex();
        String nativeplace = staffFamilyInfo.getNativeplace();
        String idTypeEndDate = staffFamilyInfo.getIdTypeEndDate();
        String occupationCode = staffFamilyInfo.getOccupationCode();
        String occupationType = staffFamilyInfo.getOccupationType();
        //校验证件号码
        if (StringUtils.isEmpty(iDNo)) {
            errorMsgList.add("证件号码不能为空！");
        } else if (errorMsgList.size() == 0) {
            //身份证号+户口本的校验规则一致
            if ("0".equals(iDType) || "4".equals(iDType)) {
                String errMsg = IDCardUtil.checkIDCard(iDNo, sex, birthDay);
                if (StringUtils.isEmpty(errMsg)) {
                    iDNo = iDNo.toUpperCase();
                } else {
                    errorMsgList.add(errMsg);
                }
            } else {
                if (iDNo.length() < 3) {
                    errorMsgList.add("证件号码长度小于3位！");
                }
            }
            //校验正式表 当前企业下是否存在同一证件号  存在 进行五要素比对 fcperson
            if (!ensureMakeService.checkStuIdNoIsExists(iDNo)) {
                Map<String, String> checkMap = new HashMap<>();
                checkMap.put("idNo", iDNo);
                checkMap.put("idType", iDType);
                checkMap.put("name", name);
                checkMap.put("sex", sex);
                checkMap.put("birthday", birthDay);
                if (!ensureMakeService.checkStuIdNoIsExistsTwo(checkMap)) {        //五要素不同
                    errorMsgList.add("当前系统存在相同的家属证件号，但是姓名、性别、出生日期、证件类型不同，请核对后输入！");
                }
            }
        }


        //如果是修改家属信息，判断家属信息是否可以修改
        if (!StringUtils.isEmpty(personID)) {
            //判断家属信息是否可以修改
            Integer fixedCount = fpInsurePlanMapper.selectStaffFamilyInsured(perNo, personID);
            Integer flexCount = fpInsureEflexPlanMapper.selectStaffFamilyInsured(perNo, personID);
            Integer count = fcOrderInsuredMapper.selectStaffFamilyInsured(personID);
            if (fixedCount > 0 || flexCount > 0 || count > 0) {
                //家属已投保，不能修改五要素
                result = checkStaffInfo(staffFamilyInfo);
                return result;
            }
        }
        //校验职业是否存在
        if (StringUtil.isEmpty(occupationCode)) {
            errorMsgList.add("职业代码不能为空！");
        }
        //2. 校验 员工及家属 职业 相关
        //校验人员--add by wudezhong
        Map<String, String> maps = new HashMap<>();
        maps.put("sign", "2");//1：员工 2：家属
        maps.put("idType", iDType);//证件类型
        maps.put("idNo", iDNo);//证件号
        maps.put("birthDay", birthDay);//出生日期
        maps.put("sex", sex);//性别
        maps.put("nativeplace", nativeplace);//国籍
        maps.put("idTypeEndDate", idTypeEndDate);//证件有效期
        maps.put("occupationCode", occupationCode);//职业代码
        String resultMsg = CheckUtils.checkSinglePeople(maps);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(resultMsg)) {
            result.put("success", false);
            result.put("code", "500");
            result.put("message", resultMsg);
            return result;
        }

        //校验除五要素的其他信息
        result = checkStaffInfo(staffFamilyInfo);
        if ("500".equals(result.get("code"))) {
            return result;
        }

        //校验姓名
        if (StringUtils.isEmpty(name)) {
            errorMsgList.add("姓名不能为空！");
        } else {
            //证件类型是身份证和户口本
          /*  if ("0".equals(iDType) || "4".equals(iDType)) {
                if (!name.matches("^[\\u0391-\\uFFE5]+$")) {
                    errorMsgList.add("姓名仅能输入中文!");
                }
            }*/
        }
        //校验出生日期
        if (StringUtils.isEmpty(birthDay)) {
            errorMsgList.add("出生日期不能为空！");
        } else {
            if (!DateTimeUtil.isDate(birthDay) || !birthDay.matches("^[0-9]{4}-[0-9]{2}-[0-9]{2}")) {
                errorMsgList.add("出生日期格式有误！");
            } else {
                if (!DateTimeUtil.checkDate(birthDay, DateTimeUtil.getCurrentDate())) {
                    errorMsgList.add("出生日期不得大于当前日期！");
                } else {
//                    int age = Integer.parseInt(DateTimeUtil.getAge(birthDay));
                    int age = DateTimeUtil.getCurrentAge(birthDay, DateTimeUtil.getCurrentDate());
//                    if (age < 16 || age > 65) {
//                        errorMsgList.add("被保人年龄必须在16周岁到65周岁！");
//                    }
                    long distanceDays = DateTimeUtil.getDistanceDays(birthDay, DateTimeUtil.getCurrentDate());//出生天数
                    if (distanceDays < 28 || age > 65) {
                        errorMsgList.add("被保人年龄必须在28天到65周岁！");
                    }
                }
            }
        }
        //校验证件类型
        if (StringUtils.isEmpty(iDType)) {
            errorMsgList.add("证件类型不能为空！");
        } else {
            if (!idTypeList.contains(iDType)) {
                errorMsgList.add("证件类型填写有误！");
            }
        }
        //校验性别
        if (StringUtils.isEmpty(sex)) {
            errorMsgList.add("性别不能为空！");
        } else {
            if (!sex.matches("^(0|1)")) {
                errorMsgList.add("性别录入有误！");
            }
        }
        if (errorMsgList.size() > 0) {
            result = ResultUtil.error(org.apache.commons.lang3.StringUtils.join(errorMsgList, ","));
        }
        return result;
    }

    /**
     * 处理被保人信息
     *
     * @param token
     * @param perNo
     * @param staffInfo
     * @param staffFamilyInfo
     */
    @Transactional
    public Map<String, Object> dealFcPersonInfo(String token, String perNo, String relation, FCPerson staffInfo, FCPerson staffFamilyInfo) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        try {
            //员工肯定是修改(同时修改 员工表和  被保人表)
            staffInfo.setOperator(globalInput.getUserNo());
            staffInfo = CommonUtil.initObject(staffInfo, "UPDATE");
            fcPersonMapper.updateByPrimaryKeySelective(staffInfo);
            //修改员工正式表
            FCPerInfo fcPerInfo = new FCPerInfo();
            fcPerInfo.setPerNo(perNo);
            fcPerInfo.setIdTypeEndDate(staffInfo.getIdTypeEndDate());
            fcPerInfo.setNativeplace(staffInfo.getNativeplace());
            fcPerInfo.setProvince(staffInfo.getProvince());
            fcPerInfo.setCity(staffInfo.getCity());
            fcPerInfo.setCounty(staffInfo.getCounty());
            fcPerInfo.setDetaileAddress(staffInfo.getDetaileAddress());
            fcPerInfo.setZipCode(staffInfo.getZipCode());
            fcPerInfo.setMobilePhone(staffInfo.getMobilePhone());
            fcPerInfo.setOperator(globalInput.getUserNo());
            fcPerInfo = CommonUtil.initObject(fcPerInfo, "UPDATE");
            fcPerInfoMapper.updateByPrimaryKeySelective(fcPerInfo);

            if (!"0".equals(relation)) {
                String personID = staffFamilyInfo.getPersonID();
                //判断是新增还是修改（新增的肯定不是员工，员工只能修改，如果新增的为已存在家属则更新）
                String name = staffFamilyInfo.getName();
                String iDType = staffFamilyInfo.getIDType();
                String iDNo = staffFamilyInfo.getIDNo();
                String birthDay = staffFamilyInfo.getBirthDate();
                String sex = staffFamilyInfo.getSex();
                Map<String, String> checkMap = new HashMap<>();
                checkMap.put("idNo", iDNo.toUpperCase());
                checkMap.put("idType", iDType);
                checkMap.put("name", name);
                checkMap.put("sex", sex);
                checkMap.put("birthday", birthDay);
                checkMap.put("perNO", perNo);
                List<String> personIdList = ensureMakeService.checkNewPersonIsExists(checkMap);
                if (StringUtils.isEmpty(personID)) {
                    if (personIdList != null && personIdList.size() > 0) {
                        for (String personIdstr : personIdList) {
                            staffFamilyInfo.setPersonID(personIdstr);
                            staffFamilyInfo.setOperator(globalInput.getUserNo());
                            staffFamilyInfo = CommonUtil.initObject(staffFamilyInfo, "UPDATE");
                            fcPersonMapper.updateByPrimaryKeySelectiveNoidno(staffFamilyInfo);
                        }
                    } else {
                        //新增个人客户表
                        personID = maxNoService.createMaxNo("PersonID", "", 20);
                        staffFamilyInfo.setPersonID(personID);
                        staffFamilyInfo.setOperator(globalInput.getUserNo());
                        staffFamilyInfo = CommonUtil.initObject(staffFamilyInfo, "INSERT");
                        fcPersonMapper.insertSelective(staffFamilyInfo);
                        //员工家属信息关联表
                        FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
                        fcStaffFamilyRela.setPerNo(perNo);
                        fcStaffFamilyRela.setPersonID(personID);
                        fcStaffFamilyRela.setRelation(relation);
                        fcStaffFamilyRela.setRelationProve("");
                        fcStaffFamilyRela.setOperator(globalInput.getUserNo());
                        fcStaffFamilyRela = CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
                        fcStaffFamilyRelaMapper.insertSelective(fcStaffFamilyRela);
                    }
                } else {
                    staffFamilyInfo.setOperator(globalInput.getUserNo());
                    staffFamilyInfo = CommonUtil.initObject(staffFamilyInfo, "UPDATE");
//                    fcPersonMapper.updateByPrimaryKeySelective(staffFamilyInfo);
                    fcPersonMapper.updateByPrimaryKeySelectiveNoidno(staffFamilyInfo);
                }
            }
            resultMap = ResultUtil.success("被保人信息配置成功！");
        } catch (Exception e) {
            Log.info("被保人配置失败：" + e.getMessage());
            throw new RuntimeException();
        }
        return resultMap;

    }

    /**
     * 处理订单信息
     *
     * @param grpNo
     * @param ensureCode
     * @param fcOrderItemDetail
     */
    @Transactional
    public Map<String, Object> dealOrderInfo(String token, String grpNo, String ensureCode, String perNo, String orderItemNo, Double prem,
                                             FCOrderItemDetail fcOrderItemDetail, FCPerson fcPerson, FCPerson staffInfo, String relation) {
        Map<String, Object> orderResultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        try {
            String name = fcPerson.getName();
            String idType = fcPerson.getIDType();
            String name1 = staffInfo.getName();
            String idType1 = staffInfo.getIDType();
            if (!StringUtil.isEmpty(idType) && idType.equals("1")) {
                String s = name.replaceAll(" +", " ");
                String trim = s.trim();
                fcPerson.setName(trim);
            }
            if (!StringUtil.isEmpty(idType1) && idType1.equals("1")) {
                String s = name1.replaceAll(" +", " ");
                String trim = s.trim();
                staffInfo.setName(trim);
            }
            //查询支付方式
            Map<String, Object> param = new HashMap<>();
            param.put("ensureCode", ensureCode);
            param.put("grpNo", grpNo);
            param.put("configNo", "021");
            String fcEnsureConfig_021 = fcEnsureConfigMapper.selectOnlyValue(param);
            String orderItemDetailNo = fcOrderItemDetail.getOrderItemDetailNo();
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
            String grpOrderNo = fcGrpOrder.getGrpOrderNo();
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            //获取代理人信息
            List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
            FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);

            //被保人信息
            FCOrderInsured fcOrderInsured = new FCOrderInsured();
            fcOrderInsured.setPersonID(fcPerson.getPersonID());
            fcOrderInsured.setName(fcPerson.getName());
            fcOrderInsured.setSex(fcPerson.getSex());
            fcOrderInsured.setBirthDay(fcPerson.getBirthDate());
            fcOrderInsured.setIDType(fcPerson.getIDType());
            fcOrderInsured.setIDNo(fcPerson.getIDNo());
            fcOrderInsured.setMobilePhone(fcPerson.getMobilePhone());
            fcOrderInsured.setEMail(fcPerson.getEMail());
            fcOrderInsured.setZipCode(fcPerson.getZipCode());
            fcOrderInsured.setProvince(fcPerson.getProvince());
            fcOrderInsured.setCity(fcPerson.getCity());
            fcOrderInsured.setCounty(fcPerson.getCounty());
            fcOrderInsured.setDetaileAddress(fcPerson.getDetaileAddress());
            fcOrderInsured.setOperator(globalInput.getUserNo());
            FCPerson fcPerson1 = fcPersonMapper.selectByPrimaryKey(fcPerson.getPersonID());
            String occupationType = fcPerson1.getOccupationType();
            String occupationCode = fcPerson1.getOccupationCode();
            fcOrderInsured.setOccupationCode(occupationCode);
            fcOrderInsured.setOccupationType(occupationType);
            //添加主被保人信息
            fcOrderInsured.setMainProvince(staffInfo.getProvince());
            fcOrderInsured.setMainCity(staffInfo.getCity());
            fcOrderInsured.setMainCounty(staffInfo.getCounty());
            fcOrderInsured.setMainDetaileAddress(staffInfo.getDetaileAddress());
            fcOrderInsured.setMainMobilePhone(staffInfo.getMobilePhone());
            fcOrderInsured.setMainEMail(staffInfo.getEMail());
            fcOrderInsured.setMainZipCode(staffInfo.getZipCode());
            if (!StringUtil.isEmpty(relation) && !relation.equals("0")) {
                fcOrderInsured.setMainYearSalary(staffInfo.getYearSalary());
            }

            //判断是新增还是修改
            if (StringUtils.isEmpty(orderItemNo)) {
                orderItemDetailNo = maxNoService.createMaxNo("OrderItemDetailNo", "", 20);//子订单详情编号
                orderResultMap.put("orderItemNo", orderItemNo);
                //新增订单表
                FCOrder fcOrder = new FCOrder();
                String orderNo = maxNoService.createMaxNo("OrderNo", DateTimeUtil.getTimeString().substring(0, 4), 8);
                fcOrder.setOrderNo(orderNo);
                //订单状态: 03投保中
                fcOrder.setOrderStatus("03");
                //订单类型:01固定套餐  02年度福利  03日常福利
                fcOrder.setOrderType("03");
                fcOrder.setGrpOrderNo(grpOrderNo);
                fcOrder.setGrpNo(grpNo);
                fcOrder.setPerNo(perNo);
                fcOrder.setOperator(globalInput.getUserNo());
                fcOrder.setIsLock("0");
                fcOrder.setCommitDate(DateTimeUtil.getCurrentDate());
                fcOrder.setOrderSign("2");//订单标识  1-基础单生成的订单  2-移动端生成的订单
                fcOrder = CommonUtil.initObject(fcOrder, "INSERT");
                fcOrderMapper.insertSelective(fcOrder);


                //新增子订单表
                FCOrderItem fcOrderItem = new FCOrderItem();
                orderItemNo = maxNoService.createMaxNo("OrderItemNo", "", 20);
                fcOrderItem.setOrderItemNo(orderItemNo);
                fcOrderItem.setOrderNo(orderNo);
                String rule = "99" + fdAgentInfo.getManageCom().substring(2, 4) + DateTimeUtil.getCurrentYear().substring(2, 4);
                String contNo = maxNoService.createMaxNo("ContNo" + DateTimeUtil.getCurrentYear(), rule, 9);
                contNo = contNo + "8";
                fcOrderItem.setContNo(contNo);
                fcOrderItem.setOrderItemDetailNo(orderItemDetailNo);
                fcOrderItem.setOperator(globalInput.getUserNo());
                fcOrderItem.setGrpContNo(fcGrpOrder.getGrpContNo());
                fcOrderItem.setPayPersonId(staffInfo.getPersonID());
                //保费
                if ("0".equals(fcEnsureConfig_021)) {
                    fcOrderItem.setGrpPrem(prem);
                }
                if ("1".equals(fcEnsureConfig_021)) {
                    fcOrderItem.setSelfPrem(prem);
                }
                fcOrderItem = CommonUtil.initObject(fcOrderItem, "INSERT");
                fcOrderItemMapper.insertSelective(fcOrderItem);
                orderResultMap.put("orderItemNo", orderItemNo);

                //新增子订单详情表
                fcOrderItemDetail.setOrderItemDetailNo(orderItemDetailNo);
                //产品编码
                fcOrderItemDetail.setProductCode("16380");
                fcOrderItemDetail.setProductEleCode("001");
                fcOrderItemDetail.setOperator(globalInput.getUserNo());
                fcOrderItemDetail = CommonUtil.initObject(fcOrderItemDetail, "INSERT");
                fcOrderItemDetailMapper.insertSelective(fcOrderItemDetail);

                //被保人信息
                fcOrderInsured.setOrderItemNo(orderItemNo);
                fcOrderInsured.setOrderNo(orderNo);
                fcOrderInsured.setGrpOrderNo(grpOrderNo);
                fcOrderInsured = CommonUtil.initObject(fcOrderInsured, "INSERT");
                fcOrderInsuredMapper.insertSelective(fcOrderInsured);

            } else {
                if (StringUtils.isEmpty(orderItemNo)) {
                    return ResultUtil.error("子订单详情编号不能为空！");
                }
                //修改子订单和详情表
                FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
                //保费
                if ("0".equals(fcEnsureConfig_021)) {
                    fcOrderItem.setGrpPrem(prem);
                }
                if ("1".equals(fcEnsureConfig_021)) {
                    fcOrderItem.setSelfPrem(prem);
                }
                fcOrderItem.setOperator(globalInput.getUserNo());
                fcOrderItem = CommonUtil.initObject(fcOrderItem, "UPDATE");
                fcOrderItemMapper.updateByPrimaryKeySelective(fcOrderItem);

                fcOrderItemDetail.setOperator(globalInput.getUserNo());
                fcOrderItemDetail = CommonUtil.initObject(fcOrderItemDetail, "UPDATE");
                fcOrderItemDetailMapper.updateByPrimaryKeySelect(fcOrderItemDetail);
                //修改被保险人
                fcOrderInsured.setOrderItemNo(orderItemNo);
                fcOrderInsured.setGrpOrderNo(grpOrderNo);
                fcOrderInsured = CommonUtil.initObject(fcOrderInsured, "UPDATE");
                fcOrderInsuredMapper.updateByPrimaryKeySelective(fcOrderInsured);
            }
            orderResultMap.put("success", true);
            orderResultMap.put("code", "200");
            orderResultMap.put("message", "订单信息配置成功");
        } catch (Exception e) {
            Log.info("订单信息配置失败！：" + e.getMessage());
            throw new RuntimeException();
        }
        return orderResultMap;
    }

    /**
     * 订单信息查询
     *
     * @param token
     * @param ensureCode
     * @param perNo
     * @param personId
     * @param orderItemNo
     * @return
     */
    public String getOrderInfo(String token, String ensureCode, String perNo, String personId, String orderItemNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //企业信息
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByEnsureCode(ensureCode);
            resultMap.put("grpName", fcGrpInfo.getGrpName());
            //员工信息
            String relation = "0";
            Map<String, String> params = new HashMap<>();
            params.put("perNo", perNo);
            params.put("relation", relation);
            params.put("orderItemNo", orderItemNo);
            List<Map<String, String>> fcPersonList = fcPersonMapper.selectMainFcPersonInfoByParams(params);
            resultMap.put("staffInfo", fcPersonList.get(0));
            //查询被保人信息
            //根据perNo和personId查询被保人信息
            params.clear();
            params.put("perNo", perNo);
            params.put("personId", personId);
            params.put("orderItemNo", orderItemNo);
            List<Map<String, String>> orderInsuredInfos = fcPersonMapper.selectInfoFcPersonInfoByParams(params);
            Map<String, String> orderInsuredInfo = orderInsuredInfos.get(0);
            //前端需要
            resultMap.put("relation", orderInsuredInfo.get("relation"));
            if (!"0".equals(orderInsuredInfo.get("relation"))) {
                resultMap.put("orderInsuredInfo", orderInsuredInfo);
            }
            FCOrder fcOrder = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
            String orderSign = fcOrder.getOrderSign();//订单标识  1-基础单生成的订单  2-移动端生成的订单
            //查询支付方式
            Map<String, Object> param = new HashMap<>();
            param.put("ensureCode", ensureCode);
            param.put("configNo", "021");
            String fcEnsureConfig_021 = fcEnsureConfigMapper.selectOnlyValue(param);
            //查询订单信息
            Map<String, String> orderItemInfo = fcOrderItemDetailMapper.selectOrderItemInfo(orderItemNo);
            String payFrequency = orderItemInfo.get("payFrequency");
            String payPeriod = orderItemInfo.get("payPeriod");
            Double prem1 = 0.0;
            if (!StringUtil.isEmpty(orderSign) && orderSign.equals("2")) {
                if ("0".equals(fcEnsureConfig_021)) {
                    prem1 = Double.valueOf(orderItemInfo.get("grpPrem"));
                } else {
                    prem1 = Double.valueOf(orderItemInfo.get("selfPrem"));
                }
            } else {
                prem1 = Double.valueOf(orderItemInfo.get("grpPrem"));
            }


            //计算续期保费 1-趸交  2-年交
            if ("2".equals(payFrequency)) {
                int year = 0;
                switch (payPeriod) {
                    case "02":
                        year = 5;
                        break;
                    case "03":
                        year = 10;
                        break;
                    case "04":
                        year = 20;
                        break;
                    default:
                        break;
                }
                Double prem2 = CommonUtil.mul(prem1, Double.valueOf(year - 1));
                resultMap.put("prem2", prem2);
            }
            resultMap.put("prem1", prem1);
            resultMap.put("orderItemInfo", orderItemInfo);
            //查询受益人信息
            List<Map<String, String>> bnfInfos = fcOrderBnfMapper.selectBnfInfos(orderItemNo);
            resultMap.put("bnfInfos", bnfInfos);
            //查询健告信息
            List<FCAppntImpartInfoPersonal> fcAppntImpartInfoPersonals = fcAppntImpartInfoPersonalMapper.selectAllByOrderItemNo(orderItemNo);
            for (FCAppntImpartInfoPersonal fcAppntImpartInfoPersonal : fcAppntImpartInfoPersonals) {
                String impartCode1 = fcAppntImpartInfoPersonal.getImpartCode();
                if (StringUtil.isEmpty(impartCode1)) {
                    return JSON.toJSONString(ResultUtil.error("封装数据时没有发现健康告知信息！"));
                }
                if (impartCode1.equals("11201-A")) {//驾驶本
                    String impartParamModle = fcAppntImpartInfoPersonal.getImpartParamModle();//传回的值为：是/C1驾驶证/   需要切割。
                    String sign = impartParamModle.split("/")[0];//“是” 或者“否”
                    if (sign.equals("是")) {
                        String DrivearrNo = impartParamModle.split("/")[1];//代表驾驶证的码值
                        String drivearr = fdCodeMapper.selectNameByCode("Drivearr", DrivearrNo);
                        resultMap.put("drivearr", drivearr);
                    }
                }
            }
            params.clear();
            params.put("orderItemNo", orderItemNo);
            List<FCAppntImpartInfoPersonal> fcAppntImpartInfoPhoneList = fcAppntImpartInfoPersonalMapper.selectList(params);
            if (fcAppntImpartInfoPhoneList != null && fcAppntImpartInfoPhoneList.size() > 0) {
                Map<String, String> map = new HashMap<String, String>();
                for (FCAppntImpartInfoPersonal fcAppntImpartInfoPersonal : fcAppntImpartInfoPhoneList) {
                    map.put(fcAppntImpartInfoPersonal.getImpartCode(), fcAppntImpartInfoPersonal.getImpartParamModle());
                }
                resultMap.put("data", map);
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "没有投保告知信息！");
                Log.info("没有投保告知信息！");
            }

            resultMap.put("orderSign", orderSign);
            resultMap.put("fcAppntImpartInfoPersonals", fcAppntImpartInfoPersonals);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "订单查询成功");
        } catch (Exception e) {
            Log.info("订单信息查询失败" + e.getMessage());
            resultMap = ResultUtil.error("订单信息查询失败!");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 确认签名查询
     *
     * @param token
     * @param ensureCode
     * @param perNo
     * @param personId
     * @param orderItemNo
     * @return
     */
    public String getConfirmInfo(String token, String ensureCode, String perNo, String personId, String orderItemNo, String shareSign) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, String> resultname = new HashMap<>();
        Map<String, String> resultid = new HashMap<>();
        List<String> list = new ArrayList<>();
        List<Map<String, String>> lists = new ArrayList<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByEnsureCode(ensureCode);
            String grpNo = fcGrpInfo.getGrpNo();
            //查询支付方式
            Map<String, Object> param = new HashMap<>();
            param.put("ensureCode", ensureCode);
            param.put("grpNo", grpNo);
            param.put("configNo", "021");
            String fcEnsureConfig_021 = fcEnsureConfigMapper.selectOnlyValue(param);
            //查询订单信息
            Map<String, String> orderItemInfo = fcOrderItemDetailMapper.selectOrderItemInfo(orderItemNo);
            String selfPrem = orderItemInfo.get("selfPrem");
            String grpPrem = orderItemInfo.get("grpPrem");
            Map<String, String> params = new HashMap<>();
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            String payPersonId = fcOrderItem.getPayPersonId();
            String relation = "";
            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(orderItemNo);
            Map<String, String> map = new HashMap<String, String>();
            map.put("perNo", perNo);
            map.put("personID", personId);
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(map);
            FCStaffFamilyRela fcStaffFamilyRela1 = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
            String personID = fcStaffFamilyRela1.getPersonID();//员工 personID
            relation = fcStaffFamilyRela.getRelation();
            resultMap.put("relation", relation);
            //支付方式 0-企业定期结算 1-个人实时支付
            FCOrder fcOrder = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
            String orderSign = fcOrder.getOrderSign();//订单标识  1-基础单生成的订单  2-移动端生成的订单
            if (!StringUtil.isEmpty(orderSign) && orderSign.equals("2")) {
                resultMap.put("payType", fcEnsureConfig_021);
                if ("0".equals(fcEnsureConfig_021)) {
                    resultMap.put("prem", grpPrem);
                    resultname.put("payName", fcGrpInfo.getGrpName());
                    resultname.put("sign", "0");
                    resultMap.put("grpName", resultname);
                } else {
                    resultMap.put("prem", selfPrem);
//                ii.	若基础单配置线上投保支付方式为“个人实时支付”，被保险人为“本人”时，选项包括：员工；被保险人为非“本人”时，选项包括：员工和被保险人。
                    if (!StringUtil.isEmpty(relation) && !relation.equals("0")) {
                        if (!StringUtil.isEmpty(payPersonId)) {
                            if (payPersonId.equals(personID)) {
                                //代表 员工 给家属投保  员工为付款人
                                resultname.put("payName", globalInput.getName());
                                resultname.put("personID", personID);
                                resultname.put("sign", "0");

                                resultid.put("payName", fcOrderInsured.getName());
                                resultid.put("personID", personId);
                                resultid.put("sign", "1");
                                lists.add(resultname);
                                lists.add(resultid);
                                resultMap.put("ThisPay", lists);
                            } else {
                                //代表 员工 给家属投保   家属为 付款人
                                resultname.put("payName", globalInput.getName());
                                resultname.put("personID", personID);
                                resultname.put("sign", "1");

                                resultid.put("payName", fcOrderInsured.getName());
                                resultid.put("personID", personId);
                                resultid.put("sign", "0");
                                lists.add(resultname);
                                lists.add(resultid);
                                resultMap.put("ThisPay", lists);
                            }
                        } else {
                            //代表 员工 给家属投保
                            resultname.put("payName", globalInput.getName());
                            resultname.put("personID", personID);
                            resultname.put("sign", "0");

                            resultid.put("payName", fcOrderInsured.getName());
                            resultid.put("personID", personId);
                            resultid.put("sign", "1");
                            lists.add(resultname);
                            lists.add(resultid);
                            resultMap.put("ThisPay", lists);
                        }

                    }
                    if (!StringUtil.isEmpty(relation) && relation.equals("0")) {
                        //代表被保险人是 员工本人
                        resultname.put("payName", globalInput.getName());
                        resultname.put("personID", personID);
                        resultname.put("sign", "0");
                        lists.add(resultname);
                        resultMap.put("ThisPay", lists);
                    }
                }
                if (!StringUtils.isEmpty(payPersonId)) {
                    resultMap.put("payPersonId", payPersonId);
                }
            } else {
                //基础单 回显付款人为企业
                resultMap.put("prem", grpPrem);
                resultname.put("payName", fcGrpInfo.getGrpName());
                resultname.put("sign", "0");
                resultMap.put("grpName", resultname);
            }
            //员工信息
            params.clear();
            params.put("perNo", perNo);
            params.put("relation", "0");
            List<Map<String, String>> fcPersonList = fcPersonMapper.selectFcPersonInfoByParams(params);
            resultMap.put("staffInfo", fcPersonList.get(0));
            //查询被保人信息
            //根据perNo和personId查询被保人信息
            params.clear();
            params.put("perNo", perNo);
            params.put("personId", personId);
            List<Map<String, String>> orderInsuredInfos = fcPersonMapper.selectFcPersonInfoByParams(params);
            if (orderInsuredInfos.size() > 0) {
                Map<String, String> orderInsuredInfo = orderInsuredInfos.get(0);
                relation = orderInsuredInfo.get("relation");
                if (!"0".equals(relation)) {
                    resultMap.put("orderInsuredInfo", orderInsuredInfo);
                }
            }

            //查询员工本人的影像信息
            params.clear();
            params.put("orderItemNo", orderItemNo);
            params.put("relation", "0");
            params.put("fileType", "");
            List<FCPersonImage> staffImage = fcPersonImageMapper.selectImages(params);
            resultMap.put("staffImage", staffImage);
            //查询家属的影像信息
            params.put("relation", "");
            List<FCPersonImage> staffFamilyImage = fcPersonImageMapper.selectImages(params);
            if (staffFamilyImage.size() > 0) {
                resultMap.put("staffFamilyImage", staffFamilyImage);
            }
            //查询（员工）人脸识别信息
            String bestFrame = fcPersonImageMapper.selectOrderFace(orderItemNo, fcPersonList.get(0).get("personId"));
            resultMap.put("bestFrame", bestFrame == null ? "" : bestFrame);
            resultMap.put("bestFrameStatus", StringUtil.isEmpty(bestFrame) ? "1" : "0");//0--识别通过；1--识别未通过
            if (!StringUtil.isEmpty(shareSign) && shareSign.equals("1")) {
                //shareSign ==1  的时候 是分享出来的页面。此时要进行锁定订单。
                fcOrder.setIsLock("1");
                fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
            }
            FCOrder fcOrder1 = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
            String isLock = fcOrder1.getIsLock();

            FcOrderItemShareRela fcOrderItemShareRela = fcOrderItemMapper.selectShareRelaByorderiterNo(orderItemNo);
            if (fcOrderItemShareRela != null) {
                String shareDetalNo = fcOrderItemShareRela.getShareDetalNo();
                resultMap.put("shareDetalNo", shareDetalNo);
            }
            resultMap.put("isLock", isLock);
            resultMap.put("code", "200");
            resultMap.put("message", "查询成功");
        } catch (Exception e) {
            Log.info("查询失败：" + e.getMessage());
            resultMap = ResultUtil.error("查询失败!");
        }
        return JSON.toJSONString(resultMap);
    }

    public String getConfirmInfoShare(String token, String orderItemNo, String shareSign) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, String> resultname = new HashMap<>();
        Map<String, String> resultid = new HashMap<>();
        List<String> list = new ArrayList<>();
        List<Map<String, String>> lists = new ArrayList<>();
        try {
            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(orderItemNo);
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            FCOrder fcOrder = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
            String personId = fcOrderInsured.getPersonID();
            System.out.println("token为：" + token);
            GlobalInput globalInput = userService.getSession(token);
            System.out.println("globalInput为::>>>>>>>>>>" + globalInput);
            String ensureCode = globalInput.getEnsureCode();
            String perNo = globalInput.getCustomNo();
            System.out.println("ensureCode为：" + ensureCode + "perNo为：" + perNo);
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByEnsureCode(ensureCode);
            String grpNo = fcGrpInfo.getGrpNo();
            //查询支付方式
            Map<String, Object> param = new HashMap<>();
            param.put("ensureCode", ensureCode);
            param.put("grpNo", grpNo);
            param.put("configNo", "021");
            String fcEnsureConfig_021 = fcEnsureConfigMapper.selectOnlyValue(param);
            //查询订单信息
            Map<String, String> orderItemInfo = fcOrderItemDetailMapper.selectOrderItemInfo(orderItemNo);
            String selfPrem = orderItemInfo.get("selfPrem");
            String grpPrem = orderItemInfo.get("grpPrem");
            Map<String, String> params = new HashMap<>();

            String payPersonId = fcOrderItem.getPayPersonId();
            String relation = "";

            Map<String, String> map = new HashMap<String, String>();
            map.put("perNo", perNo);
            map.put("personID", personId);
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(map);
            FCStaffFamilyRela fcStaffFamilyRela1 = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
            String personID = fcStaffFamilyRela1.getPersonID();//员工 personID
            relation = fcStaffFamilyRela.getRelation();
            resultMap.put("relation", relation);
            //支付方式 0-企业定期结算 1-个人实时支付

            String orderSign = fcOrder.getOrderSign();//订单标识  1-基础单生成的订单  2-移动端生成的订单
            if (!StringUtil.isEmpty(orderSign) && orderSign.equals("2")) {
                resultMap.put("payType", fcEnsureConfig_021);
                if ("0".equals(fcEnsureConfig_021)) {
                    resultMap.put("prem", grpPrem);
                    resultname.put("payName", fcGrpInfo.getGrpName());
                    resultname.put("sign", "0");
                    resultMap.put("grpName", resultname);
                } else {
                    resultMap.put("prem", selfPrem);
//                ii.	若基础单配置线上投保支付方式为“个人实时支付”，被保险人为“本人”时，选项包括：员工；被保险人为非“本人”时，选项包括：员工和被保险人。
                    if (!StringUtil.isEmpty(relation) && !relation.equals("0")) {
                        if (!StringUtil.isEmpty(payPersonId)) {
                            if (payPersonId.equals(personID)) {
                                //代表 员工 给家属投保  员工为付款人
                                resultname.put("payName", globalInput.getName());
                                resultname.put("personID", personID);
                                resultname.put("sign", "0");

                                resultid.put("payName", fcOrderInsured.getName());
                                resultid.put("personID", personId);
                                resultid.put("sign", "1");
                                lists.add(resultname);
                                lists.add(resultid);
                                resultMap.put("ThisPay", lists);
                            } else {
                                //代表 员工 给家属投保   家属为 付款人
                                resultname.put("payName", globalInput.getName());
                                resultname.put("personID", personID);
                                resultname.put("sign", "1");

                                resultid.put("payName", fcOrderInsured.getName());
                                resultid.put("personID", personId);
                                resultid.put("sign", "0");
                                lists.add(resultname);
                                lists.add(resultid);
                                resultMap.put("ThisPay", lists);
                            }
                        } else {
                            //代表 员工 给家属投保
                            resultname.put("payName", globalInput.getName());
                            resultname.put("personID", personID);
                            resultname.put("sign", "0");

                            resultid.put("payName", fcOrderInsured.getName());
                            resultid.put("personID", personId);
                            resultid.put("sign", "1");
                            lists.add(resultname);
                            lists.add(resultid);
                            resultMap.put("ThisPay", lists);
                        }

                    }
                    if (!StringUtil.isEmpty(relation) && relation.equals("0")) {
                        //代表被保险人是 员工本人
                        resultname.put("payName", globalInput.getName());
                        resultname.put("personID", personID);
                        resultname.put("sign", "0");
                        lists.add(resultname);
                        resultMap.put("ThisPay", lists);
                    }
                }
                if (!StringUtils.isEmpty(payPersonId)) {
                    resultMap.put("payPersonId", payPersonId);
                }
            } else {
                //基础单 回显付款人为企业
                resultMap.put("prem", grpPrem);
                resultname.put("payName", fcGrpInfo.getGrpName());
                resultname.put("sign", "0");
                resultMap.put("grpName", resultname);
            }
            //员工信息
            params.clear();
            params.put("perNo", perNo);
            params.put("relation", "0");
            List<Map<String, String>> fcPersonList = fcPersonMapper.selectFcPersonInfoByParams(params);
            resultMap.put("staffInfo", fcPersonList.get(0));
            //查询被保人信息
            //根据perNo和personId查询被保人信息
            params.clear();
            params.put("perNo", perNo);
            params.put("personId", personId);
            List<Map<String, String>> orderInsuredInfos = fcPersonMapper.selectFcPersonInfoByParams(params);
            if (orderInsuredInfos.size() > 0) {
                Map<String, String> orderInsuredInfo = orderInsuredInfos.get(0);
                relation = orderInsuredInfo.get("relation");
                if (!"0".equals(relation)) {
                    resultMap.put("orderInsuredInfo", orderInsuredInfo);
                }
            }

            //查询员工本人的影像信息
            params.clear();
            params.put("orderItemNo", orderItemNo);
            params.put("relation", "0");
            params.put("fileType", "");
            List<FCPersonImage> staffImage = fcPersonImageMapper.selectImages(params);
            resultMap.put("staffImage", staffImage);
            //查询家属的影像信息
            params.put("relation", "");
            List<FCPersonImage> staffFamilyImage = fcPersonImageMapper.selectImages(params);
            if (staffFamilyImage.size() > 0) {
                resultMap.put("staffFamilyImage", staffFamilyImage);
            }
            //查询（员工）人脸识别信息
            String bestFrame = fcPersonImageMapper.selectOrderFace(orderItemNo, fcPersonList.get(0).get("personId"));
            resultMap.put("bestFrame", bestFrame == null ? "" : bestFrame);
            resultMap.put("bestFrameStatus", StringUtil.isEmpty(bestFrame) ? "1" : "0");//0--识别通过；1--识别未通过

            FCOrder fcOrder1 = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
            String isLock = fcOrder1.getIsLock();
            resultMap.put("isLock", isLock);
            resultMap.put("perNo", perNo);
            resultMap.put("personId", personId);
            resultMap.put("ensureCode", ensureCode);
            resultMap.put("code", "200");
            resultMap.put("message", "查询成功");
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("查询失败：" + e.getMessage());
            resultMap = ResultUtil.error("查询失败!");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 确认签名
     *
     * @param token
     * @param confirmSignReq
     * @return
     */
    public String confirmSignInfo(String token, ConfirmSignReq confirmSignReq) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = confirmSignReq.getEnsureCode();
            String orderItemNo = confirmSignReq.getOrderItemNo();
            String perNo = confirmSignReq.getPerNo();
            String personId = confirmSignReq.getPersonId();
            String relation = confirmSignReq.getRelation();
            String payType = confirmSignReq.getPayType();
            Double prem = confirmSignReq.getPrem();
            //根据perNo和personId查询被保人信息
            Map<String, String> params = new HashMap<>();
            params.put("perNo", perNo);
            params.put("personId", personId);
            List<Map<String, String>> orderInsuredInfos = fcPersonMapper.selectFcPersonInfoByParams(params);
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
            String StaffpersonID = fcStaffFamilyRela.getPersonID();
            Map<String, String> orderInsuredInfo = orderInsuredInfos.get(0);
            String idType = orderInsuredInfo.get("idType");
            String birthDate = orderInsuredInfo.get("birthDate");
//            int age = Integer.parseInt(orderInsuredInfo.get("age"));
            int age = DateTimeUtil.getCurrentAge(birthDate, DateTimeUtil.getCurrentDate());
            if (age > 65) {
                return JSON.toJSONString(ResultUtil.error("当前被保险人年龄大于65周岁，不能投保！", "304"));
            }
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            String orderItemDetailNo = fcOrderItem.getOrderItemDetailNo();
            FCOrderItemDetail fcOrderItemDetail = fcOrderItemDetailMapper.selectByPrimaryKey_No(orderItemDetailNo);
            //产品编码（险种编码）
            String productCode = fcOrderItemDetail.getProductCode();
            //产品责任编码（计划编码）
            String dutyCode = fcOrderItemDetail.getDutyCode();
            //保额
            double insuredAmount = fcOrderItemDetail.getInsuredAmount();
            //保险期间 01-保至85周岁  02-终身
            String insurePeriod = fcOrderItemDetail.getInsurePeriod();
            //缴费频次  1-趸交  2-年交
            String payFrequency = fcOrderItemDetail.getPayFrequency();
            //缴费期间 01-一次性交清  02-5年交   03-10年交  04-20年交
            String payPeriod = fcOrderItemDetail.getPayPeriod();
            //计算总保费
            Double total = 0.0;
            int sign = 0;
            String signName = "一次交清";
            if ("1".equals(payFrequency)) {
                total = prem;
            } else {
                int year = 0;
                switch (payPeriod) {
                    case "02":
                        year = 5;
                        sign = 5;
                        signName = "5年交";
                        break;
                    case "03":
                        year = 10;
                        sign = 10;
                        signName = "10年交";
                        break;
                    case "04":
                        year = 20;
                        sign = 20;
                        signName = "20年交";
                        break;
                    default:
                        break;
                }
                total = CommonUtil.mul(prem, Double.valueOf(year));
            }
            //校验缴费期间
//            int i = age + Integer.parseInt(payPeriod);
            int i = age + sign;
            if (i > 65) {
                return JSON.toJSONString(ResultUtil.error("您当前的年龄不能选择" + signName + "，请返回重新选择！", "304"));
            }
            //校验被保人影像
            resultMap = checkPersonImage(idType, total, orderItemNo, relation, age, personId, StaffpersonID);
            if ("304".equals(resultMap.get("code")) || "500".equals(resultMap.get("code"))) {
                return JSON.toJSONString(resultMap);
            }

            //保费试算
            DailyPremTrail dailyPremTrail = new DailyPremTrail();
            dailyPremTrail.setAmount(String.valueOf(CommonUtil.mul(insuredAmount, 10000.0)));//单位是万元，所以加0000
            dailyPremTrail.setBirthDay(orderInsuredInfo.get("birthDate"));
            dailyPremTrail.setGender(orderInsuredInfo.get("sex"));
            dailyPremTrail.setInsureDate(DateTimeUtil.getCurrentDate());//基础单传投保日期，个人投保传投保当天
            dailyPremTrail.setInsurePeriod(insurePeriod);
            dailyPremTrail.setPayPeriod(payPeriod);
            dailyPremTrail.setPlanCode(dutyCode);
            dailyPremTrail.setRiskCode(productCode);

            Map<String, Object> premMap = premTrailService.dailyPremTrail(dailyPremTrail);
            String newPrem = String.valueOf(premMap.get("Prem"));
            String oldprem = String.valueOf(prem);
            if (new BigDecimal(newPrem).compareTo(new BigDecimal(oldprem)) != 0) {
//                return JSON.toJSONString(ResultUtil.error("保费出现变更，请知悉！", newPrem));
                return JSON.toJSONString(ResultUtil.success("由于投保年龄发生变化，保费也做调整", newPrem, "303"));
            }

            //调用核心自然人预核保
            String funcFlag = "YF0003";
            Map<String, Object> paramsMap = dailyIssueService.dailyNaturalSignBill(ensureCode, funcFlag, perNo, orderItemNo);
            //获取核心自然人预核保返回报文
            String result = String.valueOf(paramsMap.get("result"));
            String flag = JSONObject.parseObject(result).getString("flag");
            //identity 传 true的时候 是企业支付。这个时候用来判断是否跳转到订单查询页面。因为个人支付会走支付页面，所以个人支付传false，企业支付传true
            resultMap.put("identity", false);
            if ("0".equals(flag)) {
                JSONObject body = JSON.parseObject(result).getJSONObject("body");
                //自核通过标志 0-自核通过 1-自核不通过  “”-为空投保规则不通过
                String autoUWFlag = body.getString("autoUWFlag");
                Log.info("调用核心自然人预核保-autoUWFlag: {}", autoUWFlag);
                Log.info("调用核心自然人预核保-body数据为: {}", body);
                FCOrder fcOrder = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
                if ("".equals(autoUWFlag)) {
                    String remark = String.valueOf(body.get("remark"));
                    return JSON.toJSONString(ResultUtil.success(remark, null, "303"));
                }
                if ("0".equals(autoUWFlag)) {
                    //判断交费方式
                    if ("0".equals(payType)) {
                        //自核通过、支付方式为企业缴纳，直接调签单接口
                        Map<String, Object> insureResultMap = dailyIssueService.dailyNaturalSignBill(ensureCode, "YF0005", perNo, orderItemNo);
                        String insureResult = String.valueOf(insureResultMap.get("result"));
                        String insureFlag = JSONObject.parseObject(insureResult).getString("flag");
                        fcOrder.setOrderStatus(insureFlag.equals("0") ? "08" : "011");
                        fcOrder = CommonUtil.initObject(fcOrder, "UPDATE");
                        fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
                        resultMap.put("identity", true);
                        resultMap.put("orderNo", fcOrder.getOrderNo());
                        resultMap.put("prem", newPrem);
                        resultMap.put("orderItemNo", orderItemNo);
                    }
                    if ("1".equals(payType)) {
                        //订单状态改为待支付
                        fcOrder.setOrderStatus("04");
                        fcOrder = CommonUtil.initObject(fcOrder, "UPDATE");
                        fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
                        //自核通过、支付方式为个人支付，调用中台支付中心（实时支付则签单、批扣则进核保）
                        String goToPayment = dailyPlanPhoneService.goToPayment(token, orderItemNo, "01");
                        return goToPayment;
                    }
                }
                if ("1".equals(autoUWFlag)) {
                    //判断交费方式
                    if ("0".equals(payType)) {
                        Log.info("自核不通过、支付方式为企业全缴，调用核保接口。");
                        Map<String, Object> insureResultMap = dailyIssueService.dailyNaturalSignBill(ensureCode, "YF0004", perNo, orderItemNo);
                        String insureResult = String.valueOf(insureResultMap.get("result"));
                        String insureFlag = JSONObject.parseObject(insureResult).getString("flag");
                        fcOrder.setOrderStatus(insureFlag.equals("0") ? "010" : "011");
                        fcOrder = CommonUtil.initObject(fcOrder, "UPDATE");
                        fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
                        resultMap.put("identity", true);
                        resultMap.put("orderNo", fcOrder.getOrderNo());
                        resultMap.put("prem", newPrem);
                        resultMap.put("orderItemNo", orderItemNo);
                    }
                    if ("1".equals(payType)) {
                        Log.info("自核不通过、支付方式为个人支付，调用中台支付中心，之后进核保。");
                        //015 自核不通过 待支付
                        fcOrder.setOrderStatus("015");
                        fcOrder = CommonUtil.initObject(fcOrder, "UPDATE");
                        fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
                        String goToPayment = dailyPlanPhoneService.goToPayment(token, orderItemNo, "02");
                        return goToPayment;
                    }
                }
            } else {
                Map<String, Object> error = ResultUtil.error("预核保失败：" + JSONObject.parseObject(result).getString("desc"));
                return JSON.toJSONString(error);
            }
            resultMap.put("payType", payType);
            resultMap.put("success", true);
            resultMap.put("code", "200");
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("确认签名失败失败:" + e.getMessage());
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    //更新订单付款人信息
    public void updatePayPersonId(String orderItemNo, String personId) {
        FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
        fcOrderItem.setPayPersonId(personId);
        fcOrderItem = CommonUtil.initObject(fcOrderItem, "UPDATE");
        fcOrderItemMapper.updateByPrimaryKeySelective(fcOrderItem);
    }

    /**
     * 校验影像信息
     */
    public Map<String, Object> checkPersonImage(String idType, Double totalPrem, String orderItemNo, String Relation, int age, String insuredpersonId, String StaffpersonID) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            List<FCPersonImage> fCPersonImageList = fcPersonImageMapper.selectByOrderItemNo(orderItemNo);
//        	if(fCPersonImageList==null || fCPersonImageList.size()==0) {
//        		 return ResultUtil.error("请上传影像件！");
//        	}
            Log.info("校验影像参数 被保人personID: {}  主被保人personID{} ", insuredpersonId, StaffpersonID);
            String StaffidType = "";
            if (!StringUtil.isEmpty(StaffpersonID)) {
                FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(StaffpersonID);
                StaffidType = fcPerson.getIDType();
            } else {
                return ResultUtil.error("校验影像方法参数 员工personid为空！", "304");
            }
            List<ImageReq> staffImage = new ArrayList<ImageReq>();
            List<ImageReq> staffFamilyImage = new ArrayList<ImageReq>();
            for (FCPersonImage fCPersonImage : fCPersonImageList) {
                String relation = fCPersonImage.getRelation();
                if (relation.equals("0")) {
                    ImageReq imageReq = new ImageReq();
                    imageReq.setImageType(fCPersonImage.getImageType());
                    staffImage.add(imageReq);
                } else {
                    ImageReq imageReq = new ImageReq();
                    imageReq.setImageType(fCPersonImage.getImageType());
                    staffFamilyImage.add(imageReq);
                }
            }
            //证件
            List<String> idTypeImageList1 = new ArrayList<>();
            List<String> idTypeImageList2 = new ArrayList<>();
            //病历
            List<String> illnessImageList = new ArrayList<>();
            //其他资料
            List<String> otherImageList = new ArrayList<>();
            //签名
            List<String> signImageList1 = new ArrayList<>();
            List<String> signImageList2 = new ArrayList<>();
            //本人拍照
            List<String> photoImageList1 = new ArrayList<>();
            List<String> photoImageList2 = new ArrayList<>();
            if ((staffFamilyImage == null || staffFamilyImage.size() == 0) && (!StringUtil.isEmpty(Relation) && Relation.equals("0"))) {     //家属影像为空，这里是给本人投保时 的校验
                for (ImageReq imageReq : staffImage) {
                    String imageType = imageReq.getImageType();
                    switch (imageType) {
                        case "0801":
                            idTypeImageList1.add(imageType);
                            break;
                        case "0802":
                            illnessImageList.add(imageType);
                            break;
                        case "0803":
                            otherImageList.add(imageType);
                            break;
                        case "0804":
                            signImageList1.add(imageType);
                            break;
                        case "0805":
                            photoImageList1.add(imageType);
                            break;
                        default:
                            break;
                    }
                }
                //如果总保费大于20W
                if (new BigDecimal(totalPrem).compareTo(new BigDecimal("200000")) > 0) {
                    if (idTypeImageList1.size() == 0) {
                        return ResultUtil.error("请上传证件影像件！", "304");
                    }
                }
                if (idTypeImageList1.size() > 0) {
                    if ("0".equals(idType)) {
                        if (idTypeImageList1.size() != 2) {
                            return ResultUtil.error("证件影像件只能上传2张！", "304");
                        }
                    } else {
                        if (idTypeImageList1.size() > 5) {
                            return ResultUtil.error("证件影像件最多上传5张！", "304");
                        }
                    }
                }
                if (signImageList1.size() == 0) {
                    return ResultUtil.error("请先进行本人签字！", "304");
                }

                //校验是否人脸识别
                if ("0".equals(idType)) {
//                    String bestFrame = fcPersonImageMapper.selectOrderFace(orderItemNo, insuredpersonId);
                    int faceNum = fcPersonImageMapper.selectOrderFaceishad(orderItemNo, insuredpersonId);
                    if (faceNum < 1) {
                        return ResultUtil.error("请先进行人脸识别！", "304");
                    }
                } else {
                    if (photoImageList1.size() == 0) {
                        return ResultUtil.error("请先手持证件拍照！", "304");
                    }
                }
                if (illnessImageList.size() > 20) {
                    return ResultUtil.error("病历资料最多支持上传20张哦！", "304");
                }
                if (otherImageList.size() > 20) {
                    return ResultUtil.error("其他资料最多支持上传20张哦！", "304");
                }
            } else {
                //员工影像信息
                for (ImageReq imageReq : staffImage) {        //先对员工 的证件 进行校验。
                    String imageType = imageReq.getImageType();
                    switch (imageType) {
                        case "0801":
                            idTypeImageList1.add(imageType); //主被保险人身份证明类单证
                            break;
                        case "0804":
                            signImageList1.add(imageType);//签名  根据 关系来区分是本人的还是 家属的
                            break;
                        case "0805":
                            photoImageList1.add(imageType);//本人拍照
                            break;
                        default:
                            break;
                    }
                }

                for (ImageReq imageReq : staffFamilyImage) {    //再对 家属的 证件进行校验
                    String imageType = imageReq.getImageType();
                    switch (imageType) {
                        case "0801":
                            idTypeImageList2.add(imageType);//主被保险人身份证明类单证
                            break;
                        case "0802":
                            illnessImageList.add(imageType);//病历
                            break;
                        case "0803":
                            otherImageList.add(imageType);//其他
                            break;
                        case "0804":
                            signImageList2.add(imageType);//签名  根据 关系来区分是本人的还是 家属的
                            break;
                        case "0805":
                            photoImageList2.add(imageType);//家属拍照
                            break;
                        default:
                            break;
                    }
                }
                //给家属投保，如果家属 与员工关系为“子女”且年龄“小于18周岁”时，家属不展示本人拍照  和 本人签名
                //如果总保费大于20W
                if (new BigDecimal(totalPrem).compareTo(new BigDecimal("200000")) > 0) {
                    if (idTypeImageList1.size() == 0 || idTypeImageList2.size() == 0) {
                        return ResultUtil.error("请上传证件影像件！", "304");
                    }
                }
                //****************员工影像校验**********************************************

                if (idTypeImageList1.size() > 0) {
                    if ("0".equals(StaffidType)) {
                        if (idTypeImageList1.size() != 2) {
                            return ResultUtil.error("主被保人证件影像件只能上传2张！", "304");
                        }
                    } else {
                        if (idTypeImageList1.size() > 5) {
                            return ResultUtil.error("主被保人证件影像件最多上传5张！", "304");
                        }
                    }
                }

                if (signImageList1.size() == 0) {
                    return ResultUtil.error("主被保人请先进行本人签字！", "304");
                }
                //校验是否人脸识别
                if ("0".equals(StaffidType)) {
//                    String bestFrame = fcPersonImageMapper.selectOrderFace(orderItemNo, StaffpersonID);
                    int faceNum = fcPersonImageMapper.selectOrderFaceishad(orderItemNo, StaffpersonID);
                    if (faceNum < 1) {
                        return ResultUtil.error("主被保人请先进行人脸识别！", "304");
                    }
                } else {
                    if (photoImageList1.size() == 0) {
                        return ResultUtil.error("主被保人请先手持证件拍照！", "304");
                    }
                }

                //***************家属影像校验****************************************
                //证件影像
                if (idTypeImageList2.size() > 0) {
                    if ("0".equals(idType)) {
                        if (idTypeImageList2.size() != 2) {
                            return ResultUtil.error("被保人证件影像件只能上传2张！", "304");
                        }
                    } else {
                        if (idTypeImageList2.size() > 5) {
                            return ResultUtil.error("被保人证件影像件最多上传5张！", "304");
                        }
                    }
                }
                if (age < 18 && Relation.equals("3")) {
                    Log.info("被保人是家属，且年龄小于18并且关系为子女时不需要人脸识别/本人拍照", "304");
                } else {
                    //-------人脸识别---------------
                    if ("0".equals(idType)) {
//                        String bestFrame = fcPersonImageMapper.selectOrderFace(orderItemNo, insuredpersonId);
                        int faceNum = fcPersonImageMapper.selectOrderFaceishad(orderItemNo, insuredpersonId);
                        if (faceNum < 1) {
                            return ResultUtil.error("被保人请先进行人脸识别！", "304");
                        }
                    } else {
                        if (photoImageList2.size() == 0) {
                            return ResultUtil.error("被保人请先手持证件拍照！", "304");
                        }
                    }
                    //-------被保人签名---------------
                    if (signImageList2.size() == 0) {
                        return ResultUtil.error("被保人请先进行本人签字！", "304");
                    }
                }
                if (illnessImageList.size() > 20) {
                    return ResultUtil.error("病历资料最多支持上传20张哦！", "304");
                }
                if (otherImageList.size() > 20) {
                    return ResultUtil.error("其他资料最多支持上传20张哦！", "304");
                }
            }
            resultMap = ResultUtil.success("被保人影像校验成功");
        } catch (Exception e) {
            Log.info("被保人影像文件校验失败：" + e.getMessage());
            resultMap = ResultUtil.success("被保人影像校验失败！");
        }
        return resultMap;
    }


    /**
     * 删除受益人信息
     *
     * @param token
     * @param orderItemNo
     * @param bnfNo
     * @return
     */
    @Transactional
    public String deleteBnf(String token, String orderItemNo, String bnfNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            fcOrderBnfRelaMapper.deleteByOrderItemNoAndBnfNo(orderItemNo, bnfNo);
            fcOrderBnfMapper.deleteByPrimaryKey(bnfNo);
            resultMap = ResultUtil.success("删除受益人成功!");
        } catch (Exception e) {
            Log.info("删除受益人失败：" + e.getMessage());
            resultMap = ResultUtil.error("删除受益人失败!");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 删除影像
     *
     * @param token
     * @param imageNo
     * @return
     */
    @Transactional
    public String deletePersonImage(String token, String imageNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            fcPersonImageMapper.deleteByPrimaryKey(imageNo);
            resultMap = ResultUtil.success("删除影像成功!");
        } catch (Exception e) {
            Log.info("删除影像失败：" + e.getMessage());
            //TODO 事务回滚测试
            resultMap = ResultUtil.error("删除影像失败!");
        }
        return JSON.toJSONString(resultMap);
    }


}
