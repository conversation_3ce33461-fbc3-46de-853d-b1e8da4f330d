package com.sinosoft.eflex.service.dailyinsure.impl;

import com.hqins.common.utils.HttpUtils;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.constants.EflexConstants;
import com.sinosoft.eflex.constants.GatewayConstants;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.DailyInsurePeriodEnum;
import com.sinosoft.eflex.model.BatchInsureInterface.ESView;
import com.sinosoft.eflex.model.BatchInsureInterface.Page;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.convert.FCEnsureConfigConvert;
import com.sinosoft.eflex.model.convert.FcEnsureConvert;
import com.sinosoft.eflex.model.convert.FcGrpOrderConvert;
import com.sinosoft.eflex.model.convert.PageConvert;
import com.sinosoft.eflex.model.core.CoreDailyPlanResDTO;
import com.sinosoft.eflex.model.core.CoreResponseDTO;
import com.sinosoft.eflex.model.dailyplan.*;
import com.sinosoft.eflex.model.dailyplan.convert.*;
import com.sinosoft.eflex.service.AmountTrailService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.service.dailyinsure.DailyPlanService;
import com.sinosoft.eflex.service.dailyinsure.FcStaffInfoService;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.ConstantUtil;
import com.sinosoft.eflex.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 日常计划*
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AsyncDailyPlanInsureService {

    /**
     * Q-免收*
     */
    private static final String FREE = "Q";

    private final MyProps myProps;
    private final FcStaffInfoService fcStaffInfoService;
    private final MaxNoService maxNoService;
    private final AmountTrailService amountTrailService;


    private final FCEnsureMapper fcEnsureMapper;
    private final FCGrpOrderMapper fcGrpOrderMapper;
    private final FCOrderMapper fcOrderMapper;
    private final FCOrderItemMapper fcOrderItemMapper;
    private final FCPerInfoMapper fcPerInfoMapper;
    private final FdUserMapper fdUserMapper;
    private final FCPerRegistDayMapper fcPerRegistDayMapper;
    private final FCEnsureConfigMapper fcEnsureConfigMapper;
    private final FcEnsureContactMapper fcEnsureContactMapper;
    private final FcDailyInsureRiskInfoMapper fcDailyInsureRiskInfoMapper;
    private final FCOrderItemDetailMapper fcOrderItemDetailMapper;
    private final FCOrderInsuredMapper fcOrderInsuredMapper;
    private final FCAppntImpartInfoMapper fcAppntImpartInfoMapper;
    private final FDCodeMapper fdCodeMapper;
    private final FCPersonMapper fcPersonMapper;
    private final FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    private final FDPlaceMapper fdPlaceMapper;
    private final FDAgentInfoMapper fdAgentInfoMapper;


    /**
     * 安颐无忧年金*
     */
    private static final String RISK_CODE = "14110";

    /**
     * *
     */
    @Async("eFlexExecutor-0")
    public void asyncDailyPlanInsure(FCEnsure fcEnsure, FCGrpOrder fcGrpOrder, String tPrtNo, FCGrpInfo fcGrpInfo) {
        // 参数汇总
        TranData tranData = this.dailyContInfo(fcGrpOrder, fcGrpInfo, fcEnsure, tPrtNo);
        UnderWriteReq underWriteReq = new UnderWriteReq();
        underWriteReq.setTranData(tranData);
        log.info("dailyPlanInsure tranData :{}", JsonUtil.toJSON(underWriteReq));
        try {
            HashMap<String, String> headerMap = new HashMap<>(3);
            headerMap.put("Content-Type", "application/json;charset=UTF-8");
            headerMap.put("app_id", myProps.getCoreAppId());
            headerMap.put("app_secret", myProps.getCoreAppSecret());
            String result = HttpUtils.postJSON(myProps.getBasicsSignUrl(), JsonUtil.toJSON(underWriteReq), headerMap);
            log.info("AsyncDailyPlanInsure response result:{}", result);
            if (StringUtils.isEmpty(result)) {
                log.error("AsyncDailyPlanInsure result null！");
            }
            CoreResponseDTO coreResponseDTO = JsonUtil.fromJSON(result, CoreResponseDTO.class);
            log.info("AsyncDailyPlanInsure response coreResponseDTO:{}", JsonUtil.toJSON(coreResponseDTO));
            if (EflexConstants.FLAG_ZERO.equals(coreResponseDTO.getFlag())) {
                CoreDailyPlanResDTO coreDailyPlanResDTO = coreResponseDTO.getBody();
                if (null != coreDailyPlanResDTO) {
                    log.info("AsyncDailyPlanInsure coreDailyPlanResDTO :{}", JsonUtil.toJSON(coreDailyPlanResDTO));
                    if (EflexConstants.FLAG_ZERO.equals(coreDailyPlanResDTO.getAutoUWFlag())) {
                        // 更新核心交互状态
                        this.updateFcPrtAndCoreRela(coreDailyPlanResDTO.getGrpPrtNo(), "02", "承保成功");
                        //  修改团单状态
                        fcGrpOrderMapper.updateByPrimaryKey(FcGrpOrderConvert.convert(fcGrpOrder, coreDailyPlanResDTO.getGrpContNo()));
                        // 修改订单状态，已提交至核心
                        this.updateOrderGrpContNo(fcEnsure.getEnsureCode(), coreDailyPlanResDTO.getGrpContNo());
                        // 更新福利状态为已承保，保单状态已承保
                        fcEnsureMapper.updateByPrimaryKeySelective(FcEnsureConvert.convert(fcEnsure));
                        // 更新用户信息
                        this.updateUserInfo(fcEnsure);
                        log.info("asyncDailyPlanInsure success");
                    }
                    // 错误信息
                    log.info("asyncDailyPlanInsure error :{},{}", coreDailyPlanResDTO.getRemark(), coreDailyPlanResDTO.getAutoUWInfoList());
                }
            } else {
                // 签单失败更新状态
                this.updateInsureState(fcEnsure, coreResponseDTO, tPrtNo);
            }
        } catch (Exception e) {
            log.error("AsyncDailyPlanInsure error 调用核心基础单签单接口失败！");
        }
    }


    private TranData dailyContInfo(FCGrpOrder fcGrpOrder, FCGrpInfo fcGrpInfo, FCEnsure fcEnsure, String tPrtNo) {
        TranData tranData = new TranData();
        //封装 head 数据
        DailyHead head = getHead(GatewayConstants.YF0002);
        tranData.setHead(head);
        DailyBody dailyBody = new DailyBody();
        // 险种信息
        String riskCode = fcDailyInsureRiskInfoMapper.selectByEnsureCode(fcEnsure.getEnsureCode());
        log.info("dailyPlanInsure riskCode :{}", riskCode);

        // 获取代理人信息
        List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
        log.info("dailyPlanInsure fdAgentInfoList :{}", JsonUtil.toJSON(fdAgentInfoList));
        FDAgentInfo fdAgentInfo = fdAgentInfoList.stream().findFirst().orElse(new FDAgentInfo());
        log.info("dailyPlanInsure fdAgentInfo :{}", JsonUtil.toJSON(fdAgentInfo));

        // 特约信息
        Map<String, Object> params = new HashMap<>();
        params.put("ensureCode", fcGrpOrder.getEnsureCode());
        params.put("grpNo", fcGrpOrder.getGrpNo());
        params.put("configNo", "018");
        String specContent = fcEnsureConfigMapper.selectOnlyValue(params);
        log.info("dailyPlanInsure specContent :{}", specContent);

        // 基础
        DailyContInfo dailyContInfo = DailyContInfoConvert.convert(tPrtNo, fcEnsure, fdAgentInfo, specContent);

        // 公司信息
        this.companyInfo(dailyContInfo, fcEnsure.getEnsureCode(), fcGrpInfo);
        // 险种信息
        this.riskCode(riskCode, dailyContInfo, fcEnsure);
        // 订单 被保人
        this.order(fcGrpOrder, fcEnsure, dailyContInfo, riskCode);
        dailyBody.setContInfo(dailyContInfo);
        tranData.setBody(dailyBody);
        return tranData;
    }


    private DailyContInfo companyInfo(DailyContInfo dailyContInfo, String ensureCode, FCGrpInfo fcGrpInfo) {
        // 投保人数
        int insuredPeoples = fcOrderItemMapper.getEnsureNum(ensureCode);

        // 健康告知
        List<AppntImpart> appntImpartList = fcAppntImpartInfoMapper.selectImpartInfo(ensureCode);
        log.info("dailyPlanInsure  companyInfo appntImpartList :{}", JsonUtil.toJSON(appntImpartList));

        // 福利配置信息
        FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(ensureCode);
        if (StringUtils.isEmpty(fcEnsureContact.getIdType())) {
            fcEnsureContact.setIdType("0");
        }
        log.info("dailyPlanInsure companyInfo fcEnsureContact :{}", JsonUtil.toJSON(fcEnsureContact));

        FDCodeKey codeKey = new FDCodeKey();
        codeKey.setCodeKey(fcGrpInfo.getGrpType());
        codeKey.setCodeType("GrpNature");
        FDCode code = fdCodeMapper.selectByPrimaryKey(codeKey);
        String grpNature = StringUtils.isEmpty(code.getOtherSign()) ? null : code.getOtherSign();

        dailyContInfo.setCompanyInfo(DailyCompanyInfoConvert.convert(fcGrpInfo, String.valueOf(insuredPeoples), appntImpartList, DailyContactsConvert.convert(fcEnsureContact), grpNature));
        // 公司影像信息
        List<Page> page = PageConvert.convert(fcGrpInfo, myProps.getPolicySignBaseAddress());
        dailyContInfo.setEsViewList(EsViewConvert.convert(page));
        return dailyContInfo;
    }


    private void riskCode(String riskCode, DailyContInfo dailyContInfo, FCEnsure fcEnsure) {
        String ensureCode = fcEnsure.getEnsureCode();
        // 基础单签单：YF0002   只要有一个人投保日期和生效日期计算的年龄不一致，就不传生效日期。
        String cvaliDateEnd = fcEnsure.getCvaliDate();
        List<FCOrderInsured> fcOrderInsureds = fcOrderInsuredMapper.selectBasePerple(ensureCode);
        for (FCOrderInsured fcOrderInsured : fcOrderInsureds) {
            String birthdayInsured = fcOrderInsured.getBirthday();
            int startAge = DateTimeUtil.getCurrentAge(birthdayInsured, fcEnsure.getStartAppntDate());
            int cvaliAge = DateTimeUtil.getCurrentAge(birthdayInsured, fcEnsure.getCvaliDate());
            if (startAge != cvaliAge) {
                cvaliDateEnd = "";
                break;
            }
        }
        // 生效日期
        dailyContInfo.setCvalidate(cvaliDateEnd);
        if (RISK_CODE.equals(riskCode)) {
            // 佣金/服务津贴率
            FcDailyInsureRiskInfo fcDailyInsureRiskInfo = fcDailyInsureRiskInfoMapper.selectByEnsureCodeAndRiskCode(ensureCode, riskCode);
            dailyContInfo.setCommRate(Double.valueOf(fcDailyInsureRiskInfo.getCommissionOrAllowanceRatio()));
            // 生效日期
            String currentDateNow = DateTimeUtil.getCurrentDate();
            cvaliDateEnd = DateTimeUtil.getAppointDate(DateTimeUtil.strToDate(currentDateNow, ""), 1, "D");
            // 更新福利的状态
            fcEnsure.setCvaliDate(cvaliDateEnd);
            fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
        }
    }


    private void order(FCGrpOrder fcGrpOrder, FCEnsure fcEnsure, DailyContInfo dailyContInfo, String riskCode) {
        // 订单信息
        List<FCOrder> fcOrderList = fcOrderMapper.selectList(Collections.singletonMap("grpOrderNo", fcGrpOrder.getGrpOrderNo()));
        List<DailyInsuredInfo> insuredInfoList = new ArrayList<>();
        Double premSum = 0.0;
        for (FCOrder fcOrder : fcOrderList) {
            // 获取监护人银行信息
            Map<String, String> signBankInfoMap = fcPerInfoMapper.getSignBankInfo(fcOrder.getPerNo(), fcEnsure.getEnsureCode());

            List<FCOrderItem> fcOrderItemList = fcOrderItemMapper.selectList(Collections.singletonMap("orderNo", fcOrder.getOrderNo()));

            for (FCOrderItem fcOrderItem : fcOrderItemList) {
                // 查询 子订单产品要素详情表 获取 计划编码
                List<FCOrderItemDetail> fcOrderItemDetailList = fcOrderItemDetailMapper.selectList(Collections.singletonMap("orderItemDetailNo", fcOrderItem.getOrderItemDetailNo()));
                FCOrderItemDetail fcOrderItemDetail = fcOrderItemDetailList.stream().findFirst().orElse(new FCOrderItemDetail());
                // 查询被保人信息
                FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(fcOrderItem.getOrderItemNo());
                // 拿fcOrderInsured中没有的字段 国籍
                FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(fcOrderInsured.getPersonID());

                // 家庭关系
                Map<String, String> map = new HashMap<>();
                map.put("perNo", fcOrder.getPerNo());
                map.put("personID", fcOrderInsured.getPersonID());
                FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(map);

                // 关系转换
                FDCodeKey key = new FDCodeKey();
                key.setCodeType("Relation");
                key.setCodeKey(fcStaffFamilyRela.getRelation());
                FDCode MainRelation = fdCodeMapper.selectByPrimaryKey(key);

                String county = fdPlaceMapper.selectPlaceNameByPlaceCode(fcOrderInsured.getCounty());
                String city = fdPlaceMapper.selectPlaceNameByPlaceCode(fcOrderInsured.getCity());
                if (DailyPlanService.checkcountname(fcOrderInsured.getCity())) {
                    city = fcOrderInsured.getCity();
                }
                if (DailyPlanService.checkcountname(fcOrderInsured.getCounty())) {
                    county = fcOrderInsured.getCounty();
                }
                String address = fdPlaceMapper.selectPlaceNameByPlaceCode(fcOrderInsured.getProvince()) + city + county + fcOrderInsured.getDetaileAddress();

                Double prem = CommonUtil.add(fcOrderItem.getSelfPrem(), fcOrderItem.getGrpPrem());
                premSum = CommonUtil.add(premSum, prem);
                String payType = prem == 0.0 ? FREE : "D";
                // 险种责任信息
                List<DailyRiskInfo> riskList = new ArrayList<>();
                DailyRiskInfo riskInfo = getRiskInfo(fcEnsure.getEnsureCode(), fcOrderItem, fcOrderItemDetail);
                riskList.add(riskInfo);
                insuredInfoList.add(DailyInsuredInfoConvert.convert(fcOrderInsured, riskList, signBankInfoMap, fcPerson, address, MainRelation, fcOrderItem, payType));

                // 更新金额
                if (RISK_CODE.equals(riskCode)) {
                    Map<String, Object> amountTrail = amountTrailService.dailyAmountTrail(DailyAmountTrailConvert.convert(riskCode, fcOrderItemDetail, fcOrderInsured, fcOrderItem, fcEnsure.getStartAppntDate()));
                    String amonut = String.valueOf(amountTrail.get("Amount"));
                    double d = Double.parseDouble(amonut);
                    fcOrderItemDetail.setInsuredAmount(d);
                    CommonUtil.initObject(fcOrderItemDetail, "update");
                    fcOrderItemDetailMapper.updateByPrimaryKeySelect(fcOrderItemDetail);
                }

                // 影像信息
                List<FCPerInfo> fcPerInfos = fcPerInfoMapper.selectByIdNo(fcOrderInsured.getIDNo());
                FCPerInfo fcPerInfo = fcPerInfos.stream().findFirst().orElse(new FCPerInfo());
                List<ESView> esViewList = dailyContInfo.getEsViewList();
                List<Page> pageList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(esViewList)) {
                    pageList = esViewList.get(0).getPageList();
                }
                log.info("pageList :{}", JsonUtil.toJSON(pageList));
                List<Page> page = PageConvert.convert(fcPerInfo, myProps.getPolicySignBaseAddress(), pageList);
                dailyContInfo.setEsViewList(EsViewConvert.convert(page));
            }
        }
        // 赠险
        dailyContInfo.setComplimentaryFlag(premSum == 0.0 ? "1" : "2");
        DailyCompanyInfo companyInfo = dailyContInfo.getCompanyInfo();
        companyInfo.setGetFlag(premSum == 0.0 ? FREE : null);
        dailyContInfo.setCompanyInfo(companyInfo);
        // 被保人信息
        dailyContInfo.setInsuredInfoList(insuredInfoList);
    }


    /**
     * 封装险种计划信息
     *
     * @param fcOrderItem
     * @param fcOrderItemDetail
     * @return
     */
    private DailyRiskInfo getRiskInfo(String ensureCode, FCOrderItem fcOrderItem, FCOrderItemDetail fcOrderItemDetail) {
        // 根据福利编码查询福利的详情信息
        Map<String, Object> params = new HashMap<>();
        params.put("ensureCode", ensureCode);
        params.put("configNo", "021");
        Double prem = null;
        Double amnt = null;
        String fcEnsureConfig_021 = fcEnsureConfigMapper.selectOnlyValue(params);
        DailyRiskInfo riskInfo = new DailyRiskInfo();
        //若福利险种为14110，则保险期间、交费频次、交费期间全部要换成相应的数据
        String riskCode = fcDailyInsureRiskInfoMapper.selectByEnsureCode(ensureCode);
        if ("14110".equals(riskCode)) {
            riskInfo.setRiskCode("14110");
            riskInfo.setMainRiskCode("14110");
            String insurePeriod = fcOrderItemDetail.getInsurePeriod();
            //TODO 员工预核保时未和核心商定不同保险期间、交费频次、交费期间对应的值
            if (DailyInsurePeriodEnum.TWENTYFIVEPERIOD.getCode().equals(insurePeriod)) {
                //保险年期
                riskInfo.setYears("25");
                //保险期间
                riskInfo.setInsuYear("25");
                //保险期间单位
                riskInfo.setInsuYearFlag("Y");
            } else if (DailyInsurePeriodEnum.THIRTYPERIOD.getCode().equals(insurePeriod)) {
                //保险年期
                riskInfo.setYears("30");
                //保险期间
                riskInfo.setInsuYear("30");
                //保险期间单位
                riskInfo.setInsuYearFlag("Y");
            } else if (DailyInsurePeriodEnum.THIRTYFIVEPERIOD.getCode().equals(insurePeriod)) {
                //保险年期
                riskInfo.setYears("35");
                //保险期间
                riskInfo.setInsuYear("35");
                //保险期间单位
                riskInfo.setInsuYearFlag("Y");
            } else if (DailyInsurePeriodEnum.FORTYPERIOD.getCode().equals(insurePeriod)) {
                //保险年期
                riskInfo.setYears("40");
                //保险期间
                riskInfo.setInsuYear("40");
                //保险期间单位
                riskInfo.setInsuYearFlag("Y");
            }
            //交费频次
            String payFrequency = fcOrderItemDetail.getPayFrequency();
            riskInfo.setPayIntv(payFrequency);
            //交费期间
            String payPeriod = fcOrderItemDetail.getPayPeriod();
            switch (payPeriod) {
                //一次性交清
                case "1":
                    riskInfo.setPayEndYear("1000");
                    break;
                //三年交
                case "3":
                    riskInfo.setPayEndYear("3");
                    break;
                //五年交
                case "5":
                    riskInfo.setPayEndYear("5");
                    break;
                default:
                    break;
            }
            //交费年期
            riskInfo.setPayYears(riskInfo.getPayEndYear());
            riskInfo.setPayEndYearFlag("Y");
            riskInfo.setMult("1");
            // 保费
            prem = CommonUtil.add(fcOrderItem.getSelfPrem(), fcOrderItem.getGrpPrem());
            riskInfo.setPrem(prem);
            // 保额
            amnt = CommonUtil.mul(fcOrderItemDetail.getInsuredAmount(), 1.0);
            riskInfo.setAmnt(amnt);
            // 安颐无忧年金新增字段 领取方式：liveGetMode
            riskInfo.setLiveGetMode("2");
            // 安颐无忧年金新增字段
        } else {
            // 如果是16380险种
            riskInfo.setRiskCode("16380");
            riskInfo.setMainRiskCode("16380");
            // 交费间隔
            String payFrequency = fcOrderItemDetail.getPayFrequency();
            if ("1".equals(payFrequency)) {
                riskInfo.setPayIntv("0");
            } else if ("2".equals(payFrequency)) {
                riskInfo.setPayIntv("12");
            }
            //保险期间 01-保至85周岁  02-终身
            String insurePeriod = fcOrderItemDetail.getInsurePeriod();
            if ("01".equals(insurePeriod)) {
                //保险年期
                riskInfo.setYears("85");
                //保险期间
                riskInfo.setInsuYear("85");
                //保险期间单位
                riskInfo.setInsuYearFlag("A");
            } else if ("02".equals(insurePeriod)) {
                //保险年期
                riskInfo.setYears("1000");
                //保险期间
                riskInfo.setInsuYear("1000");
                //保险期间单位
                riskInfo.setInsuYearFlag("Y");
            }

            //交费期间
            String payPeriod = fcOrderItemDetail.getPayPeriod();
            switch (payPeriod) {
                case "01":
                    riskInfo.setPayEndYear("1000");
                    break;
                case "02":
                    riskInfo.setPayEndYear("5");
                    break;
                case "03":
                    riskInfo.setPayEndYear("10");
                    break;
                case "04":
                    riskInfo.setPayEndYear("20");
                    break;
                default:
                    break;
            }
            //交费年期
            riskInfo.setPayYears(riskInfo.getPayEndYear());
            riskInfo.setPayEndYearFlag("Y");
            riskInfo.setMult("1");
            // 保额以及保费
            prem = CommonUtil.add(fcOrderItem.getSelfPrem(), fcOrderItem.getGrpPrem());
            riskInfo.setPrem(prem);
            amnt = CommonUtil.mul(fcOrderItemDetail.getInsuredAmount(), 10000.0);
            riskInfo.setAmnt(amnt);
        }
        PayInfo pay = new PayInfo();
        String payType = "";
        //查询支付订单表  拿到支付方式  缴费类型  01-企业缴纳 02-个人实时支付+批扣 03-微信实时支付 04-银行卡实时扣费 05-银行卡批扣
        FCOrderPay fcOrderPay = fcOrderMapper.selectorderPay(fcOrderItem.getOrderItemNo());
        if ("0".equals(fcEnsureConfig_021)) {
            pay.setGroupPayMode("T");
            pay.setGroupPayMoney(fcOrderItem.getGrpPrem());
        }
        if ("1".equals(fcEnsureConfig_021)) {
            if (fcOrderPay == null) {
                payType = "0";
            } else {
                payType = fcOrderPay.getPayType().equals("03") ? "W" : "0";
            }
            pay.setPersonPayMode(payType);
            pay.setPersonPayMoney(fcOrderItem.getSelfPrem());
        }
        riskInfo.setPay(pay);
        /**********************  dutyList 责任信息 *************************************/
        List<DailyDutyInfo> dutyList = new ArrayList<>();
        DailyDutyInfo dutyInfo = new DailyDutyInfo();
        dutyInfo.setDutyCode(fcOrderItemDetail.getDutyCode());
        dutyInfo.setAmnt(amnt);
        dutyInfo.setPrem(prem);
        //免赔额
        dutyInfo.setGetLimit(0.0);
        //赔付比例
        dutyInfo.setGetRate(1.0);
        //计算规则
        dutyInfo.setCalRule("");
        dutyList.add(dutyInfo);
        riskInfo.setDutyList(dutyList);
        return riskInfo;
    }


    /**
     * 封装Head对象
     *
     * @param funcFlag
     * @return
     */
    public DailyHead getHead(String funcFlag) {
        DailyHead head = new DailyHead();
        head.setTransRefGUID(UUID.randomUUID().toString().replaceAll("-", ""));
        head.setTranDate(DateTimeUtil.getCurrentDate());
        head.setTranTime(DateTimeUtil.getCurrentTime());
        //基础单预核保：YF0001  基础单签单：YF0002 自然人预核：YF0003 自然人核保：YF0004 自然人签单：YF0005
        head.setFuncFlag(funcFlag);
        head.setSource("D0F28136208FBA26707C936071057EA0");
        head.setSubSource("01");
        return head;
    }


    /**
     * 修改订单状态 &更新子订单号中的团体保单号*
     *
     * @param fcEnsure        计划信息
     * @param coreResponseDTO 核心响应信息
     */
    public void updateInsureState(FCEnsure fcEnsure, CoreResponseDTO coreResponseDTO, String tPrtNo) {
        fcEnsure.setEnsureState(ConstantUtil.EnsureState_018);
        CommonUtil.initObject(fcEnsure, "UPDATE");
        fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
        // 01-已发送，02-承保成功，03-承保失败, 04-待承保, 05-投保失败
        updateFcPrtAndCoreRela(tPrtNo, "03", "发送失败! ;" + coreResponseDTO.getDesc());
        String serialNo = maxNoService.createMaxNo("EnsureConfig", "", 20);
        fcEnsureConfigMapper.insertSelective(FCEnsureConfigConvert.convert(fcEnsure, serialNo, coreResponseDTO));
    }


    /**
     * 用户信息更新*
     *
     * @param fcEnsure 福利计划信息
     */
    public void updateUserInfo(FCEnsure fcEnsure) {
        String ensureCode = fcEnsure.getEnsureCode();
        // 生成个人登录用户
        List<FCPerInfo> fcPerInfos = fcPerInfoMapper.selectFromFCPerRegistDayByEnsureCode(ensureCode);
        for (FCPerInfo fcPerInfo : fcPerInfos) {
            // 查询用户是否存在
            FdUser fdUser = fdUserMapper.selectByPerNoAndIdNo(fcPerInfo.getPerNo(), fcPerInfo.getIDNo());
            if (ObjectUtils.isEmpty(fdUser)) {
                fcPerInfo.setOperator(fcEnsure.getOperator());
                fcStaffInfoService.insertUser(fcPerInfo);
            } else {
                FCPerInfoTemp fcPerInfoTemp = new FCPerInfoTemp();
                fcPerInfoTemp.setName(fcPerInfo.getName());
                fcPerInfoTemp.setMobilePhone(fcPerInfo.getMobilePhone());
                fcPerInfoTemp.setIDNo(fcPerInfo.getIDNo());
                fcStaffInfoService.updateFdUser(fdUser, fcPerInfoTemp);
            }
        }
        // 更新注册期表
        FCPerRegistDay fcPerRegistDay = new FCPerRegistDay();
        fcPerRegistDay.setEnsureCode(ensureCode);
        fcPerRegistDay.setLockState("0");
        fcPerRegistDayMapper.updateCloseDayByEnsureCode(fcPerRegistDay);
    }


    /**
     * 修改订单状态 &更新子订单号中的团体保单号*
     *
     * @param ensureCode 计划编码
     * @param grpContNo  团体保单号
     */
    public void updateOrderGrpContNo(String ensureCode, String grpContNo) {
        // 修改订单状态，已提交至核心
        List<FCOrder> fcOrders = fcOrderMapper.selectOrderInfoByEnsureCode(ensureCode);
        for (FCOrder fcOrder : fcOrders) {
            fcOrder.setOrderStatus("08");
            fcOrder = CommonUtil.initObject(fcOrder, "UPDATE");
            fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
            // 更新子订单号中的团体保单号
            List<FCOrderItem> fcOrderItems = fcOrderItemMapper.selectList(Collections.singletonMap("orderNo", fcOrder.getOrderNo()));
            for (FCOrderItem fcOrderItem : fcOrderItems) {
                fcOrderItem.setGrpContNo(grpContNo);
                fcOrderItemMapper.updateByPrimaryKeySelective(fcOrderItem);
            }
        }
    }

    /**
     * 状态更新*
     *
     * @param tprtNo   核心投保单号
     * @param status   发送状态
     * @param describe 发送状态
     */
    public void updateFcPrtAndCoreRela(String tprtNo, String status, String describe) {
        Map<String, String> map = new HashMap<>();
        map.put("tPrtNo", tprtNo);
        map.put("status", status);
        map.put("describe", describe);
        fcGrpOrderMapper.updateFcPrtAndCoreRela(map);
    }


}
