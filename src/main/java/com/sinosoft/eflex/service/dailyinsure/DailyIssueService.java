package com.sinosoft.eflex.service.dailyinsure;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.CoreIdType;
import com.sinosoft.eflex.enums.DailyInsurePeriodEnum;
import com.sinosoft.eflex.enums.GenderType;
import com.sinosoft.eflex.enums.InterfaceType;
import com.sinosoft.eflex.model.AddressEntity.CheckCustomerVO;
import com.sinosoft.eflex.model.AddressEntity.EvaluationCustomer;
import com.sinosoft.eflex.model.AddressEntity.IdCardVerifyRequest;
import com.sinosoft.eflex.model.BatchInsureInterface.ESView;
import com.sinosoft.eflex.model.BatchInsureInterface.Page;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.dailyplan.*;
import com.sinosoft.eflex.service.AddressCheckService;
import com.sinosoft.eflex.service.AmountTrailService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.util.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2020/5/6 17:35
 * @desc
 */

@Service
@Slf4j
public class DailyIssueService {
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;
    @Autowired
    private FcEnsureContactMapper fcEnsureContactMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private FCOrderMapper fcOrderMapper;
    @Autowired
    private FCOrderItemMapper fcOrderItemMapper;
    @Autowired
    private FCOrderItemDetailMapper fcOrderItemDetailMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private MyProps myProps;

    @Autowired
    private FDAgentInfoMapper fdAgentInfoMapper;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private FCAppntImpartInfoMapper fcAppntImpartInfoMapper;
    @Autowired
    private FcStaffInfoService fcStaffInfoService;
    @Autowired
    private FDPlaceMapper fdPlaceMapper;
    @Autowired
    private FCOrderBnfMapper fcOrderBnfMapper;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FCAppntImpartInfoPersonalMapper fcAppntImpartInfoPersonalMapper;
    @Autowired
    private FCFileDocMainMapper fcFileDocMainMapper;
    @Autowired
    private FCPersonImageMapper fcPersonImageMapper;
    @Autowired
    private FDFTPInfoMapper fdftpInfoMapper;
    @Autowired
    private DailyMakeProposalFormService dailyMakePropdosalFormService;
    @Autowired
    private FcDailyInsureRiskInfoMapper fcDailyInsureRiskInfoMapper;
    @Autowired
    private FCPerRegistDayMapper fcPerRegistDayMapper;
    @Autowired
    private AmountTrailService amountTrailService;
    @Autowired
    private AddressCheckService addressCheckService;
    private static Logger Log = (Logger) LoggerFactory.getLogger(DailyIssueService.class);


    /**
     * Q-免收*
     */
    private static final String FREE = "Q";

    /**
     * 日常计划签单
     *
     * @param token
     * @param ensureCode
     * @return
     */
    @Transactional
    public String dailyIssue(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        List<Map<String, String>> maps = fcPerInfoMapper.selectInsuredPerson_Alls(ensureCode);
        List<EvaluationCustomer> customerList = new ArrayList<>();
        for (Map<String, String> map : maps) {
            customerList.add(EvaluationCustomer.builder()
                    .name(map.get("name"))
                    .idType(CoreIdType.getNameByCoreId(map.get("iDType")).name())
                    .idNo(map.get("iDNo"))
                    .gender(GenderType.getGenderByCoreId(map.get("sex")).name())
                    .businessNo(ensureCode)
                    .birthday(map.get("birthDay"))
                    .nationality(map.get("country")).build());
        }

        List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
        //校验二要素
        String failVerifies = addressCheckService.checkIdCard(verifies);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(failVerifies)) {
            resultMap.put("message", failVerifies);
            return JSON.toJSONString(resultMap);
        }
        FCEnsure fcEnsure = new FCEnsure();
        fcEnsure.setEnsureCode(ensureCode);
        fcEnsure.setEnsureState(ConstantUtil.EnsureState_020);
        fcEnsure = CommonUtil.initObject(fcEnsure, "UPDATE");
        fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
        CompletableFuture.runAsync(() -> {
            dailyIssue2(token, ensureCode);
        });
        resultMap.put("success", true);
        resultMap.put("code", "200");
        resultMap.put("message", "已提交核心,请稍后查看!!");
        return JSON.toJSONString(resultMap);
    }

    public void updateFcPrtAndCoreRela(String tprtNo, String status, String describe) {
        Map<String, String> map = new HashMap<>();
        map.put("tPrtNo", tprtNo);//核心投保单号
        map.put("status", status);//发送状态
        map.put("describe", describe); // 状态描述
        fcGrpOrderMapper.updateFcPrtAndCoreRela(map);
    }

    @Transactional
    @Async
    public String dailyIssue2(String token, String ensureCode) {
        log.info("日常计划签单 :::: dailyIssue2  执行");
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
        //规则校验
        if (ObjectUtils.isEmpty(fcGrpOrder)) {
            return JSON.toJSONString(ResultUtil.error("未查询到该福利相关团单，请确认后福利编码无误后再次签单!"));
        }
        if (!"1".equals(fcEnsure.getPolicyState())) {
            return JSON.toJSONString(ResultUtil.error("当前福利已存在签单记录，不可再次签单!"));
        }
        //封装数据
        String funcFlag = "YF0002";
        Map<String, Object> paramsMap = signBill(ensureCode, funcFlag, null);
        UnderWriteReq underWriteReq = (UnderWriteReq) paramsMap.get("data");
        String tPrtNo = fcGrpOrderMapper.selectbyPrtNo06(fcGrpOrder.getPrtNo());//根据平台投保单号和待发送状态 查找到对应核心投保单号
        updateFcPrtAndCoreRela(tPrtNo, "01", "已发送!");
        // 更新平台与核心投保单号对照关系表状态为"已发送"
        Map<String, String> map = new HashMap<>();
        long startStamp = System.currentTimeMillis();
        //调核心接口
        String params = JSONObject.toJSONString(underWriteReq, SerializerFeature.DisableCircularReferenceDetect);
        Log.info("\n调用核心基础单签单请求入参:{}\n", JsonUtil.toJSON(params));
        String result = null;
        try {
            result = HttpUtil.postHttpRequestJson(myProps.getBasicsSignUrl(), params, myProps.getCoreAppId(), myProps.getCoreAppSecret());
            if (StringUtils.isEmpty(result)) {
                return JSON.toJSONString(ResultUtil.error("调用核心基础单签单接口失败！"));
            }
        } catch (Exception e) {
            Log.info("用核心基础单签单接口失败!");
            return JSON.toJSONString(ResultUtil.error("调用核心基础单签单接口失败！"));
        }
        log.info("\n调用核心基础单签单接口返回报文：\n" + result);
        String flag = JSONObject.parseObject(result).getString("flag");
        long endStamp = System.currentTimeMillis();
        log.info("调用核心基础单签单接口用时：" + (endStamp - startStamp));
        if ("0".equals(flag)) {
            JSONObject body = JSON.parseObject(result).getJSONObject("body");
            //自核通过标志
            String autoUWFlag = body.getString("autoUWFlag");
            //自核结论描述
            String remark = body.getString("remark");
            if ("0".equals(autoUWFlag)) {
                //团体保单号
                String grpContNo = body.getString("grpContNo");
                //团体投保单号
                String grpPrtNo = body.getString("grpPrtNo");
                //01-已发送，02-承保成功，03-承保失败, 04-待承保, 05-投保失败
                map.put("tPrtNo", grpPrtNo);//核心投保单号
                map.put("status", "02");//发送状态
                map.put("describe", "承保成功"); // 状态描述
                fcGrpOrderMapper.updateFcPrtAndCoreRela(map);
                // 团体订单状态 01-未提交 02-已提交 03-提交失败 04-投保成功 05-投保失败
                fcGrpOrder.setGrpOrderStatus("04");
                fcGrpOrder.setGrpContNo(grpContNo);
                fcGrpOrder = (FCGrpOrder) CommonUtil.initObject(fcGrpOrder, "UPDATE");
                fcGrpOrderMapper.updateByPrimaryKey(fcGrpOrder);
                //修改订单状态，已提交至核心
                List<FCOrder> fcOrders = fcOrderMapper.selectOrderInfoByEnsureCode(ensureCode);
                for (FCOrder fcOrder : fcOrders) {
                    fcOrder.setOrderStatus("08");
                    fcOrder = CommonUtil.initObject(fcOrder, "UPDATE");
                    fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
                    //更新子订单号中的团体保单号
                    Map<String, Object> map1 = new HashMap<>();
                    map1.put("orderNo", fcOrder.getOrderNo());
                    List<FCOrderItem> fcOrderItems = fcOrderItemMapper.selectList(map1);
                    for (FCOrderItem fcOrderItem : fcOrderItems) {
                        fcOrderItem.setGrpContNo(grpContNo);
                        fcOrderItemMapper.updateByPrimaryKeySelective(fcOrderItem);
                    }
                }
                //更新福利状态为已承保，保单状态已承保
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_015);
                fcEnsure.setPolicyState("3");
                fcEnsure.setOperator(globalInput.getUserNo());
                fcEnsure = CommonUtil.initObject(fcEnsure, "UPDATE");
                fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
                //生成个人登录用户
                List<FCPerInfo> fcPerInfos = fcPerInfoMapper.selectFromFCPerRegistDayByEnsureCode(ensureCode);
                for (FCPerInfo fcPerInfo : fcPerInfos) {
                    //查询用户是否存在
                    FdUser fdUser = fdUserMapper.selectByPerNoAndIdNo(fcPerInfo.getPerNo(), fcPerInfo.getIDNo());
                    if (ObjectUtils.isEmpty(fdUser)) {
                        fcPerInfo.setOperator(globalInput.getUserNo());
                        fcStaffInfoService.insertUser(fcPerInfo);
                    } else {
                        FCPerInfoTemp fcPerInfoTemp = new FCPerInfoTemp();
                        fcPerInfoTemp.setName(fcPerInfo.getName());
                        fcPerInfoTemp.setMobilePhone(fcPerInfo.getMobilePhone());
                        fcPerInfoTemp.setIDNo(fcPerInfo.getIDNo());
                        fcStaffInfoService.updateFdUser(fdUser, fcPerInfoTemp);
                    }
                }
                // 更新注册期表
                FCPerRegistDay fcPerRegistDay = new FCPerRegistDay();
                fcPerRegistDay.setEnsureCode(ensureCode);
                fcPerRegistDay.setLockState("0");
                fcPerRegistDayMapper.updateCloseDayByEnsureCode(fcPerRegistDay);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "签单成功！");
            } else {
                JSONArray jsonArray = body.getJSONArray("autoUWInfoList");
                String errorMsg = "";
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject json = jsonArray.getJSONObject(i);
                    errorMsg += json.getString("notification") + "/";
                }
                return JSON.toJSONString(ResultUtil.error(remark + ":" + errorMsg));
            }
        } else {
            fcEnsure.setEnsureState(ConstantUtil.EnsureState_018);
            fcEnsure = CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
            // 01-已发送，02-承保成功，03-承保失败, 04-待承保, 05-投保失败
            String desc = JSONObject.parseObject(result).getString("desc");
            updateFcPrtAndCoreRela(tPrtNo, "03", "发送失败! ;" + desc);
            resultMap.put("relaSn", map.get("RelaSn"));
            resultMap.put("message", JSONObject.parseObject(result));
            saveConfig(ensureCode, resultMap, globalInput, fcEnsure);
        }
        log.info("日常计划签单 :::: dailyIssue2  执行结束");
        return JSON.toJSONString(resultMap);
    }

    private void saveConfig(String ensureCode, Map<String, Object> resultMap, GlobalInput globalInput, FCEnsure fcEnsure) {
        FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
        fcEnsureConfig.setEnsureCode(ensureCode);
        fcEnsureConfig.setGrpNo(fcEnsure.getGrpNo());
        fcEnsureConfig.setOperator(globalInput.getUserNo());
        // 增加查询 基础单签单结果
        fcEnsureConfig.setConfigNo("500");
        fcEnsureConfig.setConfigValue(JSON.toJSONString(resultMap));
        fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
        fcEnsureConfig = CommonUtil.initObject(fcEnsureConfig, "INSERT");
        fcEnsureConfigMapper.insertSelective(fcEnsureConfig);
    }


    /**
     * 基础单封装数据
     *
     * @param ensureCode
     * @return
     */
    public Map<String, Object> signBill(String ensureCode, String funcFlag, String perNo) {
        Map<String, Object> resultMap = new HashMap<>();
        UnderWriteReq underWriteReq = null;
        try {
            if (!"YF0002".equals(funcFlag)) {
                if (StringUtils.isEmpty(perNo)) {
                    return ResultUtil.error("请求参数缺失！");
                }
            }
            underWriteReq = new UnderWriteReq();
            TranData tranData = new TranData();
            //封装 head 数据
            DailyHead head = getHead(funcFlag);
            tranData.setHead(head);
            //封装 Body数据
            DailyBody body = new DailyBody();
            DailyContInfo contInfo = new DailyContInfo();
            // 参数容器
            Map<String, Object> params = new HashMap<>();
            //福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            //团体订单信息
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
            //获取代理人信息
            List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
            FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);
            // 获取企业信息
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(fcGrpOrder.getGrpNo());
            //ToDo 核心支付统一传D  以前是2
            String payType = "D";
            // 获取福利联系人
            params.clear();
            params.put("grpNo", fcGrpOrder.getGrpNo());
            params.put("contactType", "01");
            FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(ensureCode);
            if (StringUtils.isEmpty(fcEnsureContact.getIdType())) {
                fcEnsureContact.setIdType("0");
            }
            String tPrtNo = fcGrpOrderMapper.selectbyPrtNo06(fcGrpOrder.getPrtNo());//根据平台投保单号和待发送状态 查找到对应核心投保单号
//            // 团体投保单号
            contInfo.setGrpPrtNo(tPrtNo);
            // 管理机构
            contInfo.setManageCom(fdAgentInfo.getManageCom());
            // 一级销售渠道
            contInfo.setSaleChnl(fdAgentInfo.getBranchType2());
            // 二级销售渠道
            contInfo.setSellType("01");
            // 三级销售渠道  ？？
            contInfo.setAgentType("245");
            String startAppntDate = fcEnsure.getStartAppntDate();//投保开放日期
            String cvaliDate = fcEnsure.getCvaliDate();//基础保单生效日期
            // 投保申请日期
            contInfo.setPolApplyDate(startAppntDate);
            // 保单生效日期
            String CvaliDateend = "";
            String riskCode = fcDailyInsureRiskInfoMapper.selectByEnsureCode(ensureCode);
            if (funcFlag.equals("YF0002")) {
                if ("14110".equals(riskCode)) {
                    String currentDateNow = DateTimeUtil.getCurrentDate();
                    CvaliDateend = DateTimeUtil.getAppointDate(DateTimeUtil.strToDate(currentDateNow, ""), 1, "D");
                    // TODO 签单时需要重新计算被保人的保额
                    // 获得被保人清单
                    long startStamp = System.currentTimeMillis();
                    Map<String, Object> param = new HashMap<String, Object>();
                    param.put("grpOrderNo", fcGrpOrder.getGrpOrderNo());
                    // 查询出来团体的订单号
                    List<FCOrder> fcOrderList = fcOrderMapper.selectList(param);
                    for (FCOrder fcOrder : fcOrderList) {
                        param.clear();
                        param.put("orderNo", fcOrder.getOrderNo());
                        List<FCOrderItem> fcOrderItemList = fcOrderItemMapper.selectList(param);
                        for (FCOrderItem fcOrderItem : fcOrderItemList) {
                            param.clear();
                            param.put("orderItemDetailNo", fcOrderItem.getOrderItemDetailNo());
                            List<FCOrderItemDetail> fcOrderItemDetailList = fcOrderItemDetailMapper.selectList(param);
                            FCOrderItemDetail fcOrderItemDetail = fcOrderItemDetailList.get(0);
                            // 查询被保人信息
                            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper
                                    .selectByPrimaryKey(fcOrderItem.getOrderItemNo());
                            DailyAmountTrail dailyAmountTrail = new DailyAmountTrail();
                            // 获取被保人年龄
                            String birthday = fcOrderInsured.getBirthday();
                            // 获取保险期间
                            String insurePeriod = fcOrderItemDetail.getInsurePeriod();
                            // 获取缴费期间
                            String payPeriod = fcOrderItemDetail.getPayPeriod();
                            dailyAmountTrail.setRiskCode(riskCode);
                            dailyAmountTrail.setBirthDay(birthday);
                            dailyAmountTrail.setInsurePeriod(insurePeriod);
                            dailyAmountTrail.setPayPeriod(payPeriod);
                            dailyAmountTrail.setGender(fcOrderInsured.getSex());
                            dailyAmountTrail.setInsureDate(startAppntDate);
                            // 保费
                            Double prem = CommonUtil.add(fcOrderItem.getSelfPrem(), fcOrderItem.getGrpPrem());
                            dailyAmountTrail.setPrem(String.valueOf(prem));
                            Map<String, Object> map = amountTrailService.dailyAmountTrail(dailyAmountTrail);
                            String amonut = map.get("Amount").toString();
                            double d = Double.valueOf(amonut).doubleValue();
                            fcOrderItemDetail.setInsuredAmount(d);
                            fcOrderItemDetail = CommonUtil.initObject(fcOrderItemDetail, "update");
                            fcOrderItemDetailMapper.updateByPrimaryKeySelect(fcOrderItemDetail);
                        }
                    }
                    long endStamp = System.currentTimeMillis();
                    Log.info("安颐无忧年金签单重新计算保额用时：" + (endStamp - startStamp));
                    // 更新福利的状态
                    fcEnsure.setCvaliDate(CvaliDateend);
                    fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
                    // 手续费比率
                    contInfo.setChargeFeeRate(0.00);
                    // 佣金/服务津贴率
                    FcDailyInsureRiskInfo fcDailyInsureRiskInfo = fcDailyInsureRiskInfoMapper
                            .selectByEnsureCodeAndRiskCode(ensureCode, riskCode);
                    contInfo.setCommRate(Double.valueOf(fcDailyInsureRiskInfo.getCommissionOrAllowanceRatio()));

                    // todo 基础单签单：YF0002   只要有一个人投保日期和生效日期计算的年龄不一致，就不传生效日期。
                    List<FCOrderInsured> fcOrderInsureds = fcOrderInsuredMapper.selectBasePerple(ensureCode);
                    CvaliDateend = cvaliDate;
                    for (FCOrderInsured fcOrderInsured : fcOrderInsureds) {
                        String birthdayInsured = fcOrderInsured.getBirthday();
                        int Startage = DateTimeUtil.getCurrentAge(birthdayInsured, startAppntDate);
                        int Cvaliage = DateTimeUtil.getCurrentAge(birthdayInsured, cvaliDate);
                        if (Startage != Cvaliage) {
                            CvaliDateend = "";
                            break;
                        }
                    }
                } else {
//                基础单签单：YF0002   只要有一个人投保日期和生效日期计算的年龄不一致，就不传生效日期。
                    List<FCOrderInsured> fcOrderInsureds = fcOrderInsuredMapper.selectBasePerple(ensureCode);
                    CvaliDateend = cvaliDate;
                    for (FCOrderInsured fcOrderInsured : fcOrderInsureds) {
                        String birthdayInsured = fcOrderInsured.getBirthday();
                        int Startage = DateTimeUtil.getCurrentAge(birthdayInsured, startAppntDate);
                        int Cvaliage = DateTimeUtil.getCurrentAge(birthdayInsured, cvaliDate);
                        if (Startage != Cvaliage) {
                            CvaliDateend = "";
                            break;
                        }
                    }
                }
            } else if (funcFlag.equals("YF0001")) {
                if ("14110".equals(riskCode)) {
                    CvaliDateend = "";
                } else {
//                基础单预核保：YF0001   如果投保日期和生效日期计算的年龄不一致，就不传生效日期，一致，传生效日期
                    FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
                    int Startage = DateTimeUtil.getCurrentAge(fcPerInfo.getBirthDay(), startAppntDate);
                    int Cvaliage = DateTimeUtil.getCurrentAge(fcPerInfo.getBirthDay(), cvaliDate);
                    if (Startage == Cvaliage) {
                        CvaliDateend = cvaliDate;
                    }
                }
            }
            contInfo.setCvalidate(CvaliDateend);
            //基础单投保接口，统一传1，自然人投保传2
            contInfo.setContTypeFlag("1");
            // 业务员代码
            contInfo.setAgentCode(fdAgentInfo.getAgentCode());
            // 业务员姓名
            contInfo.setAgentName(fdAgentInfo.getName());
            // 所属机构  同管理机构
            contInfo.setAgentManageCom(contInfo.getManageCom());
            // 所属分部  BranchCode代理人组别
            contInfo.setBranchAttr(fdAgentInfo.getBranchCode());
            //缴费方式  基础单企业全缴
            contInfo.setGrpPayMode("1");
            //扣款缴费银行编码
            contInfo.setGrpBankCode("");
            //扣款缴费账户名
            contInfo.setGrpBankAccName("");
            //扣款缴费银行账号
            contInfo.setGrpBankAccNo("");
            //获取 特别约定
            params.clear();
            params.put("ensureCode", fcGrpOrder.getEnsureCode());
            params.put("grpNo", fcGrpOrder.getGrpNo());
            params.put("configNo", "018");
            String fcEnsureConfig_018 = fcEnsureConfigMapper.selectOnlyValue(params);
//            特约信息
            contInfo.setSpecContent(fcEnsureConfig_018);
            /**********************  companyInfo 投保单位信息 *************************************/
            DailyCompanyInfo companyInfo = new DailyCompanyInfo();
            // 投保单位名称
            companyInfo.setGrpName(fcGrpInfo.getGrpName());
            companyInfo.setMainBussiness(fcGrpInfo.getBusinesses());
            // 企业规模类型
            companyInfo.setBusSizeType(fcGrpInfo.getGrpScaleType());
            // 证件类型 1-统一社会信用代码
            companyInfo.setComType("1");
            // 证件号
            companyInfo.setComNo(fcGrpInfo.getGrpIdNo());
            //营业执照的有效期
            companyInfo.setComNoExpDate(fcGrpInfo.getGrpTypeEndDate());
            // 投保单位性质  见码表1核心提供码表
            companyInfo.setGrpNature(fcGrpInfo.getGrpType());
            FDCodeKey codeKey = new FDCodeKey();
            codeKey.setCodeKey(fcGrpInfo.getGrpType());
            codeKey.setCodeType("GrpNature");
            FDCode code = fdCodeMapper.selectByPrimaryKey(codeKey);
            if (null != code) {
                companyInfo.setGrpNature1(code.getOtherSign());
            }
            // 行业类别 所属行业
            companyInfo.setBusinessType(fcGrpInfo.getTrade());
            companyInfo.setPeoples(String.valueOf(fcGrpInfo.getPeoples()));
            // 投保人数
            int insuredPeoples = fcOrderItemMapper.getEnsureNum(ensureCode);
            companyInfo.setPeoples2(insuredPeoples + "");
            // 单位法人代表
            companyInfo.setCorporation(fcGrpInfo.getCorporationMan());
            companyInfo.setCorporationIDNo(fcGrpInfo.getLegID());
            companyInfo.setCorporationIDType(fcGrpInfo.getLegIDType());
            companyInfo.setCorporationIDStartPeriod(fcGrpInfo.getLegIDStartDate());
            companyInfo.setCorporationIDPeriodOfValidity(fcGrpInfo.getLegIDEndDate());
            companyInfo.setCorporationGender(fcGrpInfo.getLegSex());
            companyInfo.setCorporationBirthday(fcGrpInfo.getLegBirthday());
            companyInfo.setCorporationNationality(fcGrpInfo.getLegNationality());
            // 同质风险减人百分比
            companyInfo.setNZProportion("50");
            // 单位地址
            companyInfo.setGrpAddress(fcGrpInfo.getGrpAddRess());
            companyInfo.setFoundDate(fcGrpInfo.getGrpEstablishDate());
            companyInfo.setComNoStartDate(fcGrpInfo.getGrpTypeStartDate());
            //注册地
            companyInfo.setRegestedPlace(fcGrpInfo.getGrpRegisterAddress());
            // 邮政编码
            companyInfo.setGrpZipCode(fcGrpInfo.getZipCode());
            /********************** 联系人 *************************************/
            DailyContacts contacts = new DailyContacts();
            //姓名
            contacts.setLinkMan(fcEnsureContact.getName());
            contacts.setGender1(fcEnsureContact.getSex());
            contacts.setBirthday1(fcEnsureContact.getBirthDay());
            contacts.setNationality1(fcEnsureContact.getNativeplace());
            //证件类型
            contacts.setInsContIDType(fcEnsureContact.getIdType());
            //证件号码
            contacts.setInsContIDNo(fcEnsureContact.getIdNo());
            //证件有效期
            contacts.setInsContIDPeriodOfValidityType(fcEnsureContact.getIdTypeEndDate());
            contacts.setInsContIDStartPeriod(fcEnsureContact.getIdTypeStartDate());
            //E-MAIL
            contacts.setEmail(fcEnsureContact.getEmail());
            //手机号
            contacts.setMobile(fcEnsureContact.getMobilePhone());
            //所属部门
            contacts.setDepartment(fcEnsureContact.getDepartment());
            companyInfo.setContacts(contacts);
            /**********************  AppntImpartInfo 企业健告 *************************************/
            List<AppntImpart> appntImpartList = fcAppntImpartInfoMapper.selectImpartInfo(ensureCode);
            companyInfo.setAppntImpartInfo(appntImpartList);
            // 投保企业信息
            contInfo.setCompanyInfo(companyInfo);
            /**********************  insuredInfoList 被保人清单 *************************************/
            // 被保人清单
            List<DailyInsuredInfo> insuredInfoList = new ArrayList<>();
            // 获取以员工为单位的子订单（家庭）
            params.clear();
            //perNo不为空，则为单个员工预核保
            if (!StringUtils.isEmpty(perNo)) {
                params.put("perNo", perNo);
            }
            params.put("grpOrderNo", fcGrpOrder.getGrpOrderNo());
            //查询出来团体的订单号
            List<FCOrder> fcOrderList = fcOrderMapper.selectList(params);

            Double premSum = 0.0;
            for (FCOrder fcOrder : fcOrderList) {
                // 获取每个家庭的被保人集合
                params.clear();
                params.put("orderNo", fcOrder.getOrderNo());
                //获取监护人银行信息
                Map<String, String> signBankInfoMap = fcPerInfoMapper.getSignBankInfo(fcOrder.getPerNo(), ensureCode);
                List<FCOrderItem> fcOrderItemList = fcOrderItemMapper.selectList(params);
                for (FCOrderItem fcOrderItem : fcOrderItemList) {
                    // 查询 子订单产品要素详情表 获取 计划编码
                    params.clear();
                    params.put("orderItemDetailNo", fcOrderItem.getOrderItemDetailNo());
                    List<FCOrderItemDetail> fcOrderItemDetailList = fcOrderItemDetailMapper.selectList(params);
                    FCOrderItemDetail fcOrderItemDetail = fcOrderItemDetailList.get(0);
                    // 查询被保人信息
                    FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(fcOrderItem.getOrderItemNo());
                    //拿fcOrderInsured中没有的字段 国籍
                    FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(fcOrderInsured.getPersonID());
                    /**********************  insured 个人信息 *************************************/
                    DailyInsuredInfo insured = new DailyInsuredInfo();
                    Map<String, String> map = new HashMap<>();
                    map.put("perNo", fcOrder.getPerNo());
                    map.put("personID", fcOrderInsured.getPersonID());
                    FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(map);
                    insured.setName(fcOrderInsured.getName());
                    insured.setSex(fcOrderInsured.getSex());
                    insured.setBirthday(fcOrderInsured.getBirthDay());
                    insured.setIdType(fcOrderInsured.getIDType());
                    insured.setIdNo(fcOrderInsured.getIDNo());
                    insured.setNationality(fcPerson.getNativeplace());
                    insured.setContNo(fcOrderItem.getContNo());
                    FDCodeKey key = new FDCodeKey();
                    key.setCodeType("Relation");
                    key.setCodeKey(fcStaffFamilyRela.getRelation());
                    FDCode fdCode = fdCodeMapper.selectByPrimaryKey(key);
                    insured.setMainRelation(fdCode.getCoreCode());
                    insured.setOccupationCode(fcOrderInsured.getOccupationCode());
                    insured.setOccupationType(fcOrderInsured.getOccupationType());
                    insured.setMobile(fcOrderInsured.getMobilePhone());
                    insured.setEmail(fcOrderInsured.getEMail());
                    String county = "";
                    String city = "";
                    if (DailyPlanService.checkcountname(fcOrderInsured.getCity())) {
                        city = fcOrderInsured.getCity();
                    } else {
                        city = fdPlaceMapper.selectPlaceNameByPlaceCode(fcOrderInsured.getCity());
                    }
                    if (DailyPlanService.checkcountname(fcOrderInsured.getCounty())) {
                        county = fcOrderInsured.getCounty();
                    } else {
                        county = fdPlaceMapper.selectPlaceNameByPlaceCode(fcOrderInsured.getCounty());
                    }
                    String address = fdPlaceMapper.selectPlaceNameByPlaceCode(fcOrderInsured.getProvince())
                            + city
                            + county + fcOrderInsured.getDetaileAddress();
                    insured.setAddress(address);
                    insured.setZipCode(fcOrderInsured.getZipCode());
                    insured.setIdExpDate(fcPerson.getIdTypeEndDate());
                    // 默认否 险种计算保费涉及社保标志时必传 0-无 1-有
                    if (StringUtils.isEmpty(fcOrderInsured.getJoinMedProtect())) {
                        insured.setSocialInsuFlag("0");
                    } else {
                        insured.setSocialInsuFlag(fcOrderInsured.getJoinMedProtect());
                    }

                    Double prem = CommonUtil.add(fcOrderItem.getSelfPrem(), fcOrderItem.getGrpPrem());
                    premSum = CommonUtil.add(premSum, prem);
                    // 传当前计划编码
                    insured.setContPlanCode("");
                    insured.setNewPayMode(prem == 0.0 ? FREE : payType);
                    if (signBankInfoMap == null) {
                        insured.setAccBankCode("");
                        insured.setAccName("");
                        insured.setAccNo("");
                        insured.setAccBankProvince("");
                    } else {
                        insured.setAccBankCode(signBankInfoMap.get("PayBankCode"));
                        insured.setAccName(signBankInfoMap.get("Name"));
                        insured.setAccNo(signBankInfoMap.get("BankAccount"));
                        insured.setAccBankProvince("");
                    }
                    /**********************  insuredImpartList 个人告知信息 *************************************/
                    //默认传空
                    /**********************  riskList 险种责任信息 *************************************/
                    //险种责任信息
                    List<DailyRiskInfo> riskList = new ArrayList<>();
                    DailyRiskInfo riskInfo = getRiskInfo(ensureCode, fcOrderItem, fcOrderItemDetail);
                    riskList.add(riskInfo);
                    insured.setRiskList(riskList);
                    insuredInfoList.add(insured);
                }
            }

            contInfo.setComplimentaryFlag(premSum == 0.0 ? "1" : "2");
            contInfo.setInsuredInfoList(insuredInfoList);
            List<ESView> esViewList = new ArrayList<ESView>();
            List<Page> grpPpageList = new ArrayList<Page>();
            if (!StringUtil.isEmpty(fcGrpInfo.getGrpIDImage1()) || !StringUtil.isEmpty(fcGrpInfo.getGrpIDImage2())) {
                if (!StringUtil.isEmpty(fcGrpInfo.getGrpIDImage1())) {
                    Page page = new Page();
                    page.setImageName("营业执照");
                    String grpIDImage1 = fcGrpInfo.getGrpIDImage1();
                    String str1 = grpIDImage1.substring(0, grpIDImage1.indexOf("?"));
                    String str2 = grpIDImage1.substring(str1.length() + 1);
                    page.setImageUrl(myProps.getPolicySignBaseAddress() + str2);
                    page.setPageCode("1");
                    grpPpageList.add(page);
                }
                if (!StringUtil.isEmpty(fcGrpInfo.getGrpIDImage2())) {
                    Page page = new Page();
                    page.setImageName("营业执照");
                    String grpIDImage2 = fcGrpInfo.getGrpIDImage2();
                    String str1 = grpIDImage2.substring(0, grpIDImage2.indexOf("?"));
                    String str2 = grpIDImage2.substring(str1.length() + 1);
                    page.setImageUrl(myProps.getPolicySignBaseAddress() + str2);
                    page.setPageCode(String.valueOf(grpPpageList.size() + 1));
                    grpPpageList.add(page);
                }
            }
            if (!StringUtil.isEmpty(fcGrpInfo.getLegIDImage1()) || !StringUtil.isEmpty(fcGrpInfo.getLegIDImage2())) {
                if (!StringUtil.isEmpty(fcGrpInfo.getLegIDImage1())) {
                    Page page = new Page();
                    page.setImageName("法人证件影像");
                    String legIDImage1 = fcGrpInfo.getLegIDImage1();
                    String str1 = legIDImage1.substring(0, legIDImage1.indexOf("?"));
                    String str2 = legIDImage1.substring(str1.length() + 1);
                    page.setImageUrl(myProps.getPolicySignBaseAddress() + str2);
                    page.setPageCode(String.valueOf(grpPpageList.size() + 1));
                    grpPpageList.add(page);
                }
                if (!StringUtil.isEmpty(fcGrpInfo.getLegIDImage2())) {
                    Page page = new Page();
                    page.setImageName("法人证件影像");
                    String legIDImage2 = fcGrpInfo.getLegIDImage2();
                    String str1 = legIDImage2.substring(0, legIDImage2.indexOf("?"));
                    String str2 = legIDImage2.substring(str1.length() + 1);
                    page.setImageUrl(myProps.getPolicySignBaseAddress() + str2);
                    page.setPageCode(String.valueOf(grpPpageList.size() + 1));
                    grpPpageList.add(page);
                }
            }
            // TODO 如果是基础单签单的话，需要传输证件影像照
            if ("YF0002".equals(funcFlag)) {
                for (FCOrder fcOrder : fcOrderList) {
                    // 获取每个家庭的被保人集合
                    params.clear();
                    params.put("orderNo", fcOrder.getOrderNo());
                    List<FCOrderItem> fcOrderItemList = fcOrderItemMapper.selectList(params);
                    for (FCOrderItem fcOrderItem : fcOrderItemList) {
                        // 查询被保人信息
                        FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(fcOrderItem.getOrderItemNo());
                        // 判断该用户是否存储了影像件信息
                        List<FCPerInfo> fcPerInfos = fcPerInfoMapper.selectByIdNo(fcOrderInsured.getIDNo());
                        FCPerInfo fcPerInfo = fcPerInfos.get(0);
                        if (!StringUtil.isEmpty(fcPerInfo.getIdImage1()) || !StringUtil.isEmpty(fcPerInfo.getIdImage2())) {
                            if (!StringUtil.isEmpty(fcPerInfo.getIdImage1())) {
                                Page page = new Page();
                                page.setImageName("被保人" + fcPerInfo.getName() + "证件影像照");
                                String insuredIDImage1 = fcPerInfo.getIdImage1();
                                String str1 = insuredIDImage1.substring(0, insuredIDImage1.indexOf("?"));
                                String str2 = insuredIDImage1.substring(str1.length() + 1);
                                page.setImageUrl(myProps.getPolicySignBaseAddress() + str2);
                                page.setPageCode(String.valueOf(grpPpageList.size() + 1));
                                grpPpageList.add(page);
                            }
                            if (!StringUtil.isEmpty(fcPerInfo.getIdImage2())) {
                                Page page = new Page();
                                page.setImageName("被保人" + fcPerInfo.getName() + "证件影像照");
                                String insuredIDImage2 = fcPerInfo.getIdImage2();
                                String str1 = insuredIDImage2.substring(0, insuredIDImage2.indexOf("?"));
                                String str2 = insuredIDImage2.substring(str1.length() + 1, insuredIDImage2.length());
                                page.setImageUrl(myProps.getPolicySignBaseAddress() + str2);
                                page.setPageCode(String.valueOf(grpPpageList.size() + 1));
                                grpPpageList.add(page);
                            }
                        }
                    }
                }
            }
            if (grpPpageList.size() > 0) {
                ESView esView = new ESView();
                esView.setPageList(grpPpageList);
                esView.setPageNum(grpPpageList.size() + "");
                esView.setSubType("111251");//单证类型待确认
                esViewList.add(esView);
            }
            if (esViewList.size() > 0) contInfo.setEsViewList(esViewList);
            body.setContInfo(contInfo);
            tranData.setBody(body);
            underWriteReq.setTranData(tranData);
            resultMap = ResultUtil.success("封装数据成功", underWriteReq);
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("封装数据失败：" + e.getMessage());
            throw new RuntimeException();
        }
        return resultMap;
    }

    /**
     * 封装Head对象
     *
     * @param funcFlag
     * @return
     */
    public DailyHead getHead(String funcFlag) {
        DailyHead head = new DailyHead();
        head.setTransRefGUID(UUID.randomUUID().toString().replaceAll("-", ""));
        head.setTranDate(DateTimeUtil.getCurrentDate());
        head.setTranTime(DateTimeUtil.getCurrentTime());
        //基础单预核保：YF0001  基础单签单：YF0002 自然人预核：YF0003 自然人核保：YF0004 自然人签单：YF0005
        head.setFuncFlag(funcFlag);
        head.setSource("D0F28136208FBA26707C936071057EA0");
        head.setSubSource("01");
        return head;
    }

    /**
     * 自然人封装数据
     *
     * @param ensureCode
     * @return
     */
    public Map<String, Object> naturalSignBill(String ensureCode, String funcFlag, String perNo, String orderItemNo) {
        Map<String, Object> resultMap = new HashMap<>();
        UnderWriteReq underWriteReq = null;
        try {
            //福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            //获取代理人信息
            List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
            FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);
            //团体订单信息
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
            // 获取企业信息
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(fcGrpOrder.getGrpNo());
            //子订单信息
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            String orderItemDetailNo = fcOrderItem.getOrderItemDetailNo();
            // 获取福利联系人
            FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(ensureCode);
            if (StringUtils.isEmpty(fcEnsureContact.getIdType())) {
                fcEnsureContact.setIdType("0");
            }
            // 参数容器
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("ensureCode", ensureCode);
            params.put("configNo", "021");
            String fcEnsureConfig_021 = fcEnsureConfigMapper.selectOnlyValue(params);
            //开始封装数据
            underWriteReq = new UnderWriteReq();
            TranData tranData = new TranData();
            //封装 head 数据
            DailyHead head = getHead(funcFlag);
            tranData.setHead(head);
            //封装 Body数据
            DailyBody body = new DailyBody();
            DailyContInfo contInfo = new DailyContInfo();
            /**********************  contInfo 信息 *************************************/
            // 团体投保单号
            //根据平台投保单号查询核心投保单号
            String grpPrtNo = fcGrpOrderMapper.selectbyPrtNo(fcGrpOrder.getPrtNo());
            //缴费方式  基础单企业全缴  0-企业定期结算 1-个人实时支付
            String grpPayMode = "";
            if ("0".equals(fcEnsureConfig_021)) {
                grpPayMode = "1";
            }
            if ("1".equals(fcEnsureConfig_021)) {
                grpPayMode = "";
            }
            contInfo.setGrpPayMode(grpPayMode);
            contInfo.setGrpPrtNo(grpPrtNo);
            // 团体合同号
            contInfo.setGrpContNo(fcGrpOrder.getGrpContNo());
            // 管理机构
            contInfo.setManageCom(fdAgentInfo.getManageCom());
            // 一级销售渠道
            contInfo.setSaleChnl(fdAgentInfo.getBranchType2());
            // 二级销售渠道
            contInfo.setSellType("01");
            // 三级销售渠道  ？？
            contInfo.setAgentType("245");
            // 业务员代码
            contInfo.setAgentCode(fdAgentInfo.getAgentCode());
            // 所属机构  同管理机构
            contInfo.setAgentManageCom(contInfo.getManageCom());
            // 投保申请日期
            contInfo.setPolApplyDate(DateTimeUtil.getCurrentDate());
            // 保单生效日期
            contInfo.setCvalidate("");
            //基础单投保接口，统一传1，自然人投保传2
            contInfo.setContTypeFlag("2");
            //先取 合同号
            contInfo.setPrtNo(fcOrderItem.getContNo());
            //获取 特别约定
            params.clear();
            params.put("ensureCode", fcGrpOrder.getEnsureCode());
            params.put("grpNo", fcGrpOrder.getGrpNo());
            params.put("configNo", "018");
            String fcEnsureConfig_018 = fcEnsureConfigMapper.selectOnlyValue(params);
//            特约信息
            contInfo.setSpecContent(fcEnsureConfig_018);
            /**********************  AppntInfo 投保人信息 *************************************/
            FCAppntImpartInfoPersonal fcAppntImpartInfoPersonal2 = fcAppntImpartInfoPersonalMapper.selectByPrimaryKey(orderItemNo, "10001");
            String yearSalary = "";
            if (!ObjectUtils.isEmpty(fcAppntImpartInfoPersonal2)) {
                if (!StringUtils.isEmpty(fcAppntImpartInfoPersonal2.getImpartParamModle())) {
                    yearSalary = fcAppntImpartInfoPersonal2.getImpartParamModle().split("/")[0];
                }
            }
            AppntInfo appntInfo = new AppntInfo();
            FCPerson fcPerson = fcPersonMapper.selectFcpersonByPerNo(perNo);
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
            appntInfo.setName(fcPerson.getName());
            appntInfo.setSex(fcPerson.getSex());
            appntInfo.setBirthday(fcPerson.getBirthDate());
            appntInfo.setIdType(fcPerson.getIDType());
            appntInfo.setIdNo(fcPerson.getIDNo());
            //************
            String county1 = "";
            String city1 = "";
            if (DailyPlanService.checkcountname(fcPerson.getCity())) {
                city1 = fcPerson.getCity();
            } else {
                city1 = fdPlaceMapper.selectPlaceNameByPlaceCode(fcPerson.getCity());
            }
            if (DailyPlanService.checkcountname(fcPerson.getCounty())) {
                county1 = fcPerson.getCounty();
            } else {
                county1 = fdPlaceMapper.selectPlaceNameByPlaceCode(fcPerson.getCounty());
            }
            String appntInfoAddress = fdPlaceMapper.selectPlaceNameByPlaceCode(fcPerson.getProvince())
                    + city1
                    + county1 + fcPerson.getDetaileAddress();
            appntInfo.setAddress(appntInfoAddress);
            appntInfo.setMobile(fcPerson.getMobilePhone());
            appntInfo.setZipCode(fcPerson.getZipCode());
            appntInfo.setEmail(fcPerson.getEMail());
            appntInfo.setJobCode(fcPerson.getOccupationCode());
            appntInfo.setCompany(fcGrpInfo.getGrpName());
            appntInfo.setNationality(fcPerson.getNativeplace());
            // 查询被保人信息
            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(orderItemNo);
            appntInfo.setYearSalary(fcOrderInsured.getMainYearSalary());
            appntInfo.setIdExpDate(fcPerson.getIdTypeEndDate());
            //纳税身份 默认传中国税收01-仅为中国税收居民 02-仅为非居民 03-既是中国税收居民又是其他国家（地区）税收居民
            appntInfo.setTaxpayerType("01");
            contInfo.setAppntInfo(appntInfo);
            /**********************  insuredInfoList 被保人清单 *************************************/
            // 被保人清单
            List<DailyInsuredInfo> insuredInfoList = new ArrayList<>();
            /**********************  insured 个人信息 *************************************/
            DailyInsuredInfo insured = new DailyInsuredInfo();
            //获取付款人银行信息
            Map<String, String> signBankInfoMap = fcPerInfoMapper.getPayBankInfo(orderItemNo);
            //子订单详情
            FCOrderItemDetail fcOrderItemDetail = fcOrderItemDetailMapper.selectByPrimaryKey_No(orderItemDetailNo);
       /*     // 查询被保人信息
            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(orderItemNo);*/
            //拿fcOrderInsured中没有的字段 国籍
            FCPerson fcPerson1 = fcPersonMapper.selectByPrimaryKey(fcOrderInsured.getPersonID());
            //查询健告里面被保人身高体重收入等信息
            FCAppntImpartInfoPersonal fcAppntImpartInfoPersonal1 = fcAppntImpartInfoPersonalMapper.selectByPrimaryKey(orderItemNo, "10000");
            //查询支付订单表  拿到支付方式  缴费类型  01-企业缴纳 02-个人实时支付+批扣 03-微信实时支付 04-银行卡实时扣费 05-银行卡批扣
            FCOrderPay fcOrderPay = fcOrderMapper.selectorderPay(orderItemNo);
            String stature = "";
            String weight = "";
            if (!ObjectUtils.isEmpty(fcAppntImpartInfoPersonal1)) {
                String msg = fcAppntImpartInfoPersonal1.getImpartParamModle();
                stature = msg.split("/")[0];
                weight = msg.split("/")[1];
            }
            Map<String, String> map = new HashMap<>();
            map.put("perNo", perNo);
            map.put("personID", fcOrderInsured.getPersonID());
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(map);
            //被保人职业代码
            insured.setOccupationCode(fcOrderInsured.getOccupationCode());
            insured.setOccupationType(fcOrderInsured.getOccupationType());
            insured.setName(fcOrderInsured.getName());
            insured.setSex(fcOrderInsured.getSex());
            insured.setBirthday(fcOrderInsured.getBirthDay());
            insured.setIdType(fcOrderInsured.getIDType());
            insured.setIdNo(fcOrderInsured.getIDNo());
            insured.setNationality(fcPerson1.getNativeplace());
            insured.setContNo(fcOrderItem.getContNo());
            insured.setSalary(yearSalary);
            FDCodeKey key = new FDCodeKey();
            key.setCodeType("Relation");
            key.setCodeKey(fcStaffFamilyRela.getRelation());
            FDCode fdCode = fdCodeMapper.selectByPrimaryKey(key);
            String codeKey = fdCode.getCodeKey();
            if (!StringUtil.isEmpty(codeKey) && codeKey.equals("1")) {
                insured.setMainRelation("03");//平台对应的是父母，  核心平台对应的是子女。
            } else if (!StringUtil.isEmpty(codeKey) && codeKey.equals("3")) {
                insured.setMainRelation("01");//平台对应的是子女，  核心平台对应的是父母。
            } else {
                insured.setMainRelation(fdCode.getCoreCode());
            }
            insured.setMobile(fcOrderInsured.getMobilePhone());
            insured.setEmail(fcOrderInsured.getEMail());
            insured.setStature(stature);
            insured.setWeight(weight);
            String personID = fcOrderInsured.getPersonID();
            FCPerson fcPerson2 = fcPersonMapper.selectByPrimaryKey(personID);
            String idTypeEndDate = fcPerson2.getIdTypeEndDate();
            insured.setIdExpDate(idTypeEndDate);
            String county = "";
            String city = "";
            if (DailyPlanService.checkcountname(fcOrderInsured.getCity())) {
                city = fcOrderInsured.getCity();
            } else {
                city = fdPlaceMapper.selectPlaceNameByPlaceCode(fcOrderInsured.getCity());
            }
            if (DailyPlanService.checkcountname(fcOrderInsured.getCounty())) {
                county = fcOrderInsured.getCounty();
            } else {
                county = fdPlaceMapper.selectPlaceNameByPlaceCode(fcOrderInsured.getCounty());
            }
            String address = fdPlaceMapper.selectPlaceNameByPlaceCode(fcOrderInsured.getProvince())
                    + city
                    + county + fcOrderInsured.getDetaileAddress();
            insured.setAddress(address);
            insured.setZipCode(fcOrderInsured.getZipCode());
            // 默认否 险种计算保费涉及社保标志时必传 0-无 1-有
            if (StringUtils.isEmpty(fcOrderInsured.getJoinMedProtect())) {
                insured.setSocialInsuFlag("0");
            } else {
                insured.setSocialInsuFlag(fcOrderInsured.getJoinMedProtect());
            }
            // 保险计划  BBC业务传空
            insured.setContPlanCode("");
            //首期缴费方式
            String payType = "";
            if ("0".equals(fcEnsureConfig_021)) {
                payType = "T";
                contInfo.setBatchFlag("0");
            }
            if ("1".equals(fcEnsureConfig_021)) {
                if (fcOrderPay == null) {
                    payType = "0";
                    contInfo.setBatchFlag("0");
                    System.out.println("这里 是 因为 付款表为空！");
                } else {
                    payType = fcOrderPay.getPayType().equals("03") ? "W" : "0";
                    Log.info("开始赋值   支付类型为：" + fcOrderPay.getPayType());
                    if (funcFlag.equals("YF0004") && !StringUtil.isEmpty(fcOrderPay.getPayType()) && fcOrderPay.getPayType().equals("05")) {
                        contInfo.setBatchFlag("1");
                        System.out.println("赋值 1");
                    } else {
                        contInfo.setBatchFlag("0");
                        System.out.println("赋值 0");
                    }
                    Log.info("结束赋值   支付类型为：" + fcOrderPay.getPayType());
                }
            }
            insured.setNewPayMode(payType);
            if (signBankInfoMap == null) {
                insured.setAccBankCode("");
                insured.setAccName("");
                insured.setAccNo("");
                insured.setAccBankProvince("");
            } else {
                String wPayType = signBankInfoMap.get("PayType");
                if (wPayType.equals("03")) {
                    FCPerson payPersonInfo = fcPersonMapper.selectByPrimaryKey(fcOrderItem.getPayPersonId());
                    insured.setAccBankCode("");
                    insured.setAccName(payPersonInfo.getName());
                    insured.setAccNo("");
                    insured.setAccBankProvince("");
                } else {
                    insured.setAccBankCode(signBankInfoMap.get("BankCode"));
                    insured.setAccName(signBankInfoMap.get("BankAccName"));
                    insured.setAccNo(signBankInfoMap.get("BankAccNo"));
                    insured.setAccBankProvince("");
                }
            }
            /**********************  riskList 险种责任信息 *************************************/
            List<DailyRiskInfo> riskList = new ArrayList<>();
            DailyRiskInfo riskInfo = getRiskInfo(ensureCode, fcOrderItem, fcOrderItemDetail);
            riskList.add(riskInfo);
            insured.setRiskList(riskList);
            /**********************  insuredImpartList 健告信息 *************************************/
            List<AppntImpart> insuredImpartList = null;
            List<AppntImpart> babyInsured = fcAppntImpartInfoPersonalMapper.selectByOrderItemNoAndCode(orderItemNo);//判断少儿告知是否需要传
            if (babyInsured.size() == 0) {
                return ResultUtil.error("封装数据时没有发现健康告知信息！");
            }
            String impartCode = babyInsured.get(0).getImpartReply();
            String sign = impartCode.split("/")[0];
            if (!StringUtil.isEmpty(sign) && sign.equals("0")) {
                insuredImpartList = fcAppntImpartInfoPersonalMapper.selectByOrderItemNos(orderItemNo);//查出没有身高体重 少儿告知的数据
            } else {
                insuredImpartList = fcAppntImpartInfoPersonalMapper.selectByOrderItemNo(orderItemNo);
            }
            insured.setInsuredImpartList(insuredImpartList);
            /**********************  bnfList 受益人信息 *************************************/
            List<BnfInfo> bnfList = new ArrayList<>();
            List<Map<String, String>> bnfInfos = fcOrderBnfMapper.selectBnfInfos(orderItemNo);
            for (Map<String, String> bnf : bnfInfos) {
                BnfInfo bnfInfo = new BnfInfo();
                bnfInfo.setType(bnf.get("bnfType"));
                bnfInfo.setGrade(bnf.get("bnfOrder"));
                key.setCodeType("Relation");
                key.setCodeKey(bnf.get("relation"));
                fdCode = fdCodeMapper.selectByPrimaryKey(key);
                bnfInfo.setRelation(fdCode.getCoreCode());
                bnfInfo.setName(bnf.get("name"));
                bnfInfo.setSex(bnf.get("sex"));
                bnfInfo.setIdType(bnf.get("idType"));
                bnfInfo.setIdNo(bnf.get("idNo"));
                bnfInfo.setIdExpDate(bnf.get("idTypeEndDate"));
                bnfInfo.setBirthday(bnf.get("birthday"));
                bnfInfo.setRate(bnf.get("coreBnfRatio"));
                bnfInfo.setMobile(bnf.get("mobilePhone"));
                String bnfAddress = bnf.get("provinceName") + bnf.get("cityName") + bnf.get("countyName") + bnf.get("detaileAddress");
                bnfInfo.setAddress(bnfAddress);
                bnfInfo.setNativePlace(bnf.get("nativePlace"));
                bnfList.add(bnfInfo);
            }
            insured.setBnfList(bnfList);
            insuredInfoList.add(insured);
            contInfo.setInsuredInfoList(insuredInfoList);
            //获取病例资料影像
            Map<String, String> paramsimage = new HashMap<String, String>();
            paramsimage.put("orderItemNo", orderItemNo);
            paramsimage.put("fileType", "0802");
            List<FCPersonImage> selectImages = fcPersonImageMapper.selectImagesSign(paramsimage);
            if (selectImages != null && selectImages.size() > 0) {
                //影像标志（1表示含病例资料）
                contInfo.setImageFlag("1");
            } else {
                contInfo.setImageFlag("");
            }
            String staffpersonID = fcPerson.getPersonID();
            String faceFlag = "";
            String insuredIDType = fcOrderInsured.getIDType();
            String staffidType = fcPerson.getIDType();
            if (!StringUtil.isEmpty(staffpersonID) && !StringUtil.isEmpty(personID)) {
                if (insuredIDType.equals("0") && staffidType.equals("0")) {
                    //给员工投保 和 家属员工都是身份证
                    faceFlag = "1";
                    String insuredbestFrame = fcPersonImageMapper.selectOrderFace(orderItemNo, personID);//被保人personid
                    String staffbestFrame = fcPersonImageMapper.selectOrderFace(orderItemNo, staffpersonID);//员工personid
                    if (!StringUtil.isEmpty(insuredbestFrame) && !StringUtil.isEmpty(staffbestFrame)) {
                        //人脸识别标志（1表示人脸识别不通过）
                        faceFlag = "";
                    }
                } else if (!insuredIDType.equals("0") && staffidType.equals("0")) {
                    //被保人不是身份证  员工是身份证
                    faceFlag = "1";
                    String staffbestFrame = fcPersonImageMapper.selectOrderFace(orderItemNo, staffpersonID);//员工personid
                    if (!StringUtil.isEmpty(staffbestFrame)) {
                        //人脸识别标志（1表示人脸识别不通过）
                        faceFlag = "";
                    }

                } else if (insuredIDType.equals("0") && !staffidType.equals("0")) {
                    //被保人是身份证  员工不是身份证
                    String birthday = fcOrderInsured.getBirthday();
                    int age = DateTimeUtil.getCurrentAge(birthday, DateTimeUtil.getCurrentDate());
                    if (age >= 18) {
                        //大于等于十八周岁会有人脸识别
                        faceFlag = "1";
                        String insuredbestFrame = fcPersonImageMapper.selectOrderFace(orderItemNo, personID);//被保人personid
                        if (!StringUtil.isEmpty(insuredbestFrame)) {
                            //人脸识别标志（1表示人脸识别不通过）
                            faceFlag = "";
                        }
                    }
                }
            }
            contInfo.setFaceFlag(faceFlag);
            /**核保及签单接口，影像件上传至ftp，封装影像件信息**/
            if (funcFlag.equals("YF0004") || funcFlag.equals("YF0005")) {
                //1、生成签名文件并上传至ftp
                //获取主被保人签名文件
                Map<String, String> paramsSign = new HashMap<String, String>();
                paramsSign.put("orderItemNo", orderItemNo);
                paramsSign.put("fileType", "0804");
                List<FCPersonImage> selectImagesInfo = fcPersonImageMapper.selectImagesSign(paramsSign);
                String mainInsuredSign = "", insuredSign = "";
                if (selectImagesInfo == null || selectImagesInfo.size() < 1) {
                    return ResultUtil.error("封装数据时没有签名信息！");
                } else {
                    for (FCPersonImage fcPersonImage : selectImagesInfo) {
                        String relation = fcPersonImage.getRelation();
                        String imageUrl = fcPersonImage.getImageUrl();
                        if (relation.equals("0")) {
                            mainInsuredSign = imageUrl;
                        } else {
                            insuredSign = imageUrl;
                        }
                    }
                }
                insuredSign = insuredSign.equals("") ? mainInsuredSign : insuredSign;
                Map<String, String> signMap = new HashMap<String, String>();
                signMap.put("mainInsuredSign", mainInsuredSign);
                signMap.put("insuredSign", insuredSign);
                FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
                SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
                //第一页直接上传至文件服务器（提示书）
                FCFileDocMain fcFileDocMain21 = fcFileDocMainMapper.selectByPrimaryKey("00000021");
                sFtp.uploadFile(FileUtil.getFtpPath(fdftpInfo.getFtprootpath(), "0807", orderItemNo, fcStaffFamilyRela.getRelation()) + fcFileDocMain21.getFileSaveName() + fcFileDocMain21.getFileSuffix(), fcFileDocMain21.getFilePath() + fcFileDocMain21.getFileSaveName() + fcFileDocMain21.getFileSuffix());
                //第二页首先添加电子签名，然后生成到指定目录（提示书）
                FCFileDocMain fcFileDocMain22 = fcFileDocMainMapper.selectByPrimaryKey("00000022");
                CreateWaterMarkPdf.addWatermark(fcFileDocMain22.getFilePath() + fcFileDocMain22.getFileSaveName() + fcFileDocMain22.getFileSuffix(),
                        FileUtil.getLocalPath("0807", orderItemNo, fcStaffFamilyRela.getRelation()) + fcFileDocMain22.getFileSaveName() + fcFileDocMain22.getFileSuffix(),
                        "",
                        signMap,
                        "2",
                        fdftpInfo);//提示书
                insertFcpersonImage(fdftpInfo.getFtprootpath(), orderItemNo, fcStaffFamilyRela.getRelation(), "0807", perNo, fcFileDocMain21.getFileSaveName() + fcFileDocMain21.getFileSuffix());
                insertFcpersonImage(fdftpInfo.getFtprootpath(), orderItemNo, fcStaffFamilyRela.getRelation(), "0807", perNo, fcFileDocMain22.getFileSaveName() + fcFileDocMain22.getFileSuffix());

                //第二页直接上传至文件服务器（税收）
                FCFileDocMain fcFileDocMain24 = fcFileDocMainMapper.selectByPrimaryKey("00000024");
                sFtp.uploadFile(FileUtil.getFtpPath(fdftpInfo.getFtprootpath(), "0806", orderItemNo, fcStaffFamilyRela.getRelation()) + fcFileDocMain24.getFileSaveName() + fcFileDocMain24.getFileSuffix(), fcFileDocMain24.getFilePath() + fcFileDocMain24.getFileSaveName() + fcFileDocMain24.getFileSuffix());
                //第一页首先添加电子签名，然后生成到指定目录（税收）
                FCFileDocMain fcFileDocMain23 = fcFileDocMainMapper.selectByPrimaryKey("00000023");
                CreateWaterMarkPdf.addWatermark(fcFileDocMain23.getFilePath() + fcFileDocMain23.getFileSaveName() + fcFileDocMain23.getFileSuffix(),
                        FileUtil.getLocalPath("0806", orderItemNo, fcStaffFamilyRela.getRelation()) + fcFileDocMain23.getFileSaveName() + fcFileDocMain23.getFileSuffix(),
                        fcOrderItem.getContNo(),
                        signMap,
                        "1",
                        fdftpInfo);//税收
                insertFcpersonImage(fdftpInfo.getFtprootpath(), orderItemNo, fcStaffFamilyRela.getRelation(), "0806", perNo, fcFileDocMain23.getFileSaveName() + fcFileDocMain23.getFileSuffix());
                insertFcpersonImage(fdftpInfo.getFtprootpath(), orderItemNo, fcStaffFamilyRela.getRelation(), "0806", perNo, fcFileDocMain24.getFileSaveName() + fcFileDocMain24.getFileSuffix());

                Log.info("自然人封装数据，水印完成。下面影像开始");
                List<FCPersonImage> fcPersonImageList = fcPersonImageMapper.selectByOrderItemNo(orderItemNo);
                List<FCPersonImage> ftpUrlList = new ArrayList<FCPersonImage>();
                List<ESView> esViewList = new ArrayList<ESView>();
                if (fcPersonImageList.size() > 0) {
                    List<Page> pageList911021 = new ArrayList<Page>();
                    List<Page> pageList911031 = new ArrayList<Page>();
                    List<Page> pageList111251 = new ArrayList<Page>();
                    List<Page> pageList911071 = new ArrayList<Page>();
                    List<Page> pageList311141 = new ArrayList<Page>();
                    for (FCPersonImage fcPersonImage : fcPersonImageList) {
                        FCPersonImage fcPersonImageUrl = new FCPersonImage();
                        String imageType = fcPersonImage.getImageType();
                        String relation = fcPersonImage.getRelation();
                        String imageUrl = fcPersonImage.getImageUrl();
                        fcPersonImageUrl.setImageNo(fcPersonImage.getImageNo());
                        ftpUrlList.add(fcPersonImageUrl);
                        if (imageType.equals("0801") && relation.equals("0")) {//对应主被保人身份证件---911021
                            Page page = new Page();
                            page.setImageName("主被保险人身份证明类单证");
                            page.setImageUrl(myProps.getPolicySignAddress() + "filePath=" + imageUrl);
                            page.setPageCode((pageList911021.size() + 1) + "");
                            pageList911021.add(page);
                        } else if (imageType.matches("^080(2|3|5)$")) {//对应其他资料---111251
                            Page page = new Page();
                            page.setImageName("其他类");
                            page.setImageUrl(myProps.getPolicySignAddress() + "filePath=" + imageUrl);
                            page.setPageCode((pageList111251.size() + 1) + "");
                            pageList111251.add(page);
                        } else if (imageType.equals("0801") && !relation.equals("0")) {//对应被保人身份证件---911031
                            Page page = new Page();
                            page.setImageName("被保险人身份证明类单证");
                            page.setImageUrl(myProps.getPolicySignAddress() + "filePath=" + imageUrl);
                            page.setPageCode((pageList911031.size() + 1) + "");
                            pageList911031.add(page);
                        } else if (imageType.equals("0806")) {//对应个人税收居民身份声明文件（已签字）---911071
                            Page page = new Page();
                            page.setImageName("个人税收居民身份声明文件");
                            page.setImageUrl(myProps.getPolicySignAddress() + "filePath=" + imageUrl);
                            page.setPageCode((pageList911071.size() + 1) + "");
                            pageList911071.add(page);
                        } else if (imageType.equals("0807")) {//对应人身保险投保提示书（已签字）---311141
                            Page page = new Page();
                            page.setImageName("人身保险投保提示书（团险渠道）");
                            page.setImageUrl(myProps.getPolicySignAddress() + "filePath=" + imageUrl);
                            page.setPageCode((pageList311141.size() + 1) + "");
                            pageList311141.add(page);
                        }
                    }
                    if (pageList911021.size() > 0) {
                        ESView esView = new ESView();
                        esView.setSubType("911021");
                        esView.setPageNum(pageList911021.size() + "");
                        esView.setPageList(pageList911021);
                        esViewList.add(esView);
                    }
                    if (pageList911031.size() > 0) {
                        ESView esView = new ESView();
                        esView.setSubType("911031");
                        esView.setPageNum(pageList911031.size() + "");
                        esView.setPageList(pageList911031);
                        esViewList.add(esView);
                    }
                    if (pageList111251.size() > 0) {
                        ESView esView = new ESView();
                        esView.setSubType("111251");
                        esView.setPageNum(pageList111251.size() + "");
                        esView.setPageList(pageList111251);
                        esViewList.add(esView);
                    }
                    if (pageList911071.size() > 0) {
                        ESView esView = new ESView();
                        esView.setSubType("911071");
                        esView.setPageNum(pageList911071.size() + "");
                        esView.setPageList(pageList911071);
                        esViewList.add(esView);
                    }
                    if (pageList311141.size() > 0) {
                        ESView esView = new ESView();
                        esView.setSubType("311141");
                        esView.setPageNum(pageList311141.size() + "");
                        esView.setPageList(pageList311141);
                        esViewList.add(esView);
                    }
                }
                Log.info("自然人封装数据  影像完成，下面电子投保书开始");
                if (ftpUrlList.size() > 0) fcPersonImageMapper.updateImageFtpUrlByImageNo(ftpUrlList);
                //添加电子投保书
                Map<String, String> resulMap = dailyMakePropdosalFormService.MakeProposalForm(ensureCode, perNo, orderItemNo);
                Log.info("自然人封装数据  电子投保书方法里出来了。》》》》》》》》》》");
                String code = resulMap.get("code");
                if (code.equals("200")) {
                    List<Page> pageList = new ArrayList<Page>();
                    Page page = new Page();
                    page.setImageName("团体保险个人电子投保书");
                    page.setImageUrl(resulMap.get("innerNetUrl"));
                    page.setPageCode("1");
                    pageList.add(page);
                    ESView esView = new ESView();
                    esView.setSubType("511051");
                    esView.setPageNum("1");
                    esView.setPageList(pageList);
                    esViewList.add(esView);
                }
                contInfo.setEsViewList(esViewList);
            }
            Log.info("自然人封装数据  电子投保书完成，");
            body.setContInfo(contInfo);
            tranData.setBody(body);
            underWriteReq.setTranData(tranData);
            resultMap = ResultUtil.success("封装数据成功", underWriteReq);
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("封装数据失败：" + e.getMessage());
            resultMap = ResultUtil.error("封装数据失败！");
        }
        return resultMap;
    }

    public String insertFcpersonImage(String ftprootpath, String orderItemNo, String relation, String fileType, String operator, String fileName) {
        Integer imageOrder = fcPersonImageMapper.getImageOrderInfo(orderItemNo, fileType);
        FCPersonImage fcPersonImage = new FCPersonImage();
        String imageNo = maxNoService.createMaxNo("ImageNo", "", 20);
        fcPersonImage.setImageNo(imageNo);
        fcPersonImage.setOrderItemNo(orderItemNo);
        fcPersonImage.setRelation(relation);
        fcPersonImage.setImageType(fileType);
        fcPersonImage.setImageOrder(StringUtil.isEmpty(imageOrder) ? "1" : imageOrder + "");
        fcPersonImage.setOperator(operator);
        fcPersonImage.setImageUrl(FileUtil.getFtpPath(ftprootpath, fileType, orderItemNo, relation) + fileName);
        fcPersonImage = CommonUtil.initObject(fcPersonImage, "INSERT");
        fcPersonImageMapper.insertSelective(fcPersonImage);
        return imageNo;
    }

    /**
     * 封装险种计划信息
     *
     * @param fcOrderItem
     * @param fcOrderItemDetail
     * @return
     */
    private DailyRiskInfo getRiskInfo(String ensureCode, FCOrderItem fcOrderItem, FCOrderItemDetail fcOrderItemDetail) {
        //交费方式
        // 参数容器
        // 根据福利编码查询福利的详情信息
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("ensureCode", ensureCode);
        params.put("configNo", "021");
        Double prem = null;
        Double amnt = null;
        String fcEnsureConfig_021 = fcEnsureConfigMapper.selectOnlyValue(params);
        DailyRiskInfo riskInfo = new DailyRiskInfo();
        //若福利险种为14110，则保险期间、交费频次、交费期间全部要换成相应的数据
        String riskCode = fcDailyInsureRiskInfoMapper.selectByEnsureCode(ensureCode);
        if ("14110".equals(riskCode)) {
            riskInfo.setRiskCode("14110");
            riskInfo.setMainRiskCode("14110");
            String insurePeriod = fcOrderItemDetail.getInsurePeriod();
            //TODO 员工预核保时未和核心商定不同保险期间、交费频次、交费期间对应的值
            if (DailyInsurePeriodEnum.TWENTYFIVEPERIOD.getCode().equals(insurePeriod)) {
                //保险年期
                riskInfo.setYears("25");
                //保险期间
                riskInfo.setInsuYear("25");
                //保险期间单位
                riskInfo.setInsuYearFlag("Y");
            } else if (DailyInsurePeriodEnum.THIRTYPERIOD.getCode().equals(insurePeriod)) {
                //保险年期
                riskInfo.setYears("30");
                //保险期间
                riskInfo.setInsuYear("30");
                //保险期间单位
                riskInfo.setInsuYearFlag("Y");
            } else if (DailyInsurePeriodEnum.THIRTYFIVEPERIOD.getCode().equals(insurePeriod)) {
                //保险年期
                riskInfo.setYears("35");
                //保险期间
                riskInfo.setInsuYear("35");
                //保险期间单位
                riskInfo.setInsuYearFlag("Y");
            } else if (DailyInsurePeriodEnum.FORTYPERIOD.getCode().equals(insurePeriod)) {
                //保险年期
                riskInfo.setYears("40");
                //保险期间
                riskInfo.setInsuYear("40");
                //保险期间单位
                riskInfo.setInsuYearFlag("Y");
            }
            //交费频次
            String payFrequency = fcOrderItemDetail.getPayFrequency();
            riskInfo.setPayIntv(payFrequency);
            //交费期间
            String payPeriod = fcOrderItemDetail.getPayPeriod();
            switch (payPeriod) {
                //一次性交清
                case "1":
                    riskInfo.setPayEndYear("1000");
                    break;
                //三年交
                case "3":
                    riskInfo.setPayEndYear("3");
                    break;
                //五年交
                case "5":
                    riskInfo.setPayEndYear("5");
                    break;
                default:
                    break;
            }
            //交费年期
            riskInfo.setPayYears(riskInfo.getPayEndYear());
            riskInfo.setPayEndYearFlag("Y");
            riskInfo.setMult("1");
            // 保费
            prem = CommonUtil.add(fcOrderItem.getSelfPrem(), fcOrderItem.getGrpPrem());
            riskInfo.setPrem(prem);
            // 保额
            amnt = CommonUtil.mul(fcOrderItemDetail.getInsuredAmount(), 1.0);
            riskInfo.setAmnt(amnt);
            // 安颐无忧年金新增字段 领取方式：liveGetMode
            riskInfo.setLiveGetMode("2");
            // 安颐无忧年金新增字段
        } else {
            //如果是16380险种
            riskInfo.setRiskCode("16380");
            riskInfo.setMainRiskCode("16380");
            //交费间隔
            String payFrequency = fcOrderItemDetail.getPayFrequency();
            if ("1".equals(payFrequency)) {
                riskInfo.setPayIntv("0");
            } else if ("2".equals(payFrequency)) {
                riskInfo.setPayIntv("12");
            }
            //保险期间 01-保至85周岁  02-终身
            String insurePeriod = fcOrderItemDetail.getInsurePeriod();
            if ("01".equals(insurePeriod)) {
                //保险年期
                riskInfo.setYears("85");
                //保险期间
                riskInfo.setInsuYear("85");
                //保险期间单位
                riskInfo.setInsuYearFlag("A");
            } else if ("02".equals(insurePeriod)) {
                //保险年期
                riskInfo.setYears("1000");
                //保险期间
                riskInfo.setInsuYear("1000");
                //保险期间单位
                riskInfo.setInsuYearFlag("Y");
            }

            //交费期间
            String payPeriod = fcOrderItemDetail.getPayPeriod();
            switch (payPeriod) {
                case "01":
                    riskInfo.setPayEndYear("1000");
                    break;
                case "02":
                    riskInfo.setPayEndYear("5");
                    break;
                case "03":
                    riskInfo.setPayEndYear("10");
                    break;
                case "04":
                    riskInfo.setPayEndYear("20");
                    break;
                default:
                    break;
            }
            //交费年期
            riskInfo.setPayYears(riskInfo.getPayEndYear());
            riskInfo.setPayEndYearFlag("Y");
            riskInfo.setMult("1");
            // 保额以及保费
            prem = CommonUtil.add(fcOrderItem.getSelfPrem(), fcOrderItem.getGrpPrem());
            riskInfo.setPrem(prem);
            amnt = CommonUtil.mul(fcOrderItemDetail.getInsuredAmount(), 10000.0);
            riskInfo.setAmnt(amnt);
        }
        PayInfo pay = new PayInfo();
        String payType = "";
        //查询支付订单表  拿到支付方式  缴费类型  01-企业缴纳 02-个人实时支付+批扣 03-微信实时支付 04-银行卡实时扣费 05-银行卡批扣
        FCOrderPay fcOrderPay = fcOrderMapper.selectorderPay(fcOrderItem.getOrderItemNo());
        if ("0".equals(fcEnsureConfig_021)) {
            pay.setGroupPayMode("T");
            pay.setGroupPayMoney(fcOrderItem.getGrpPrem());
        }
        if ("1".equals(fcEnsureConfig_021)) {
            if (fcOrderPay == null) {
                payType = "0";
            } else {
                payType = fcOrderPay.getPayType().equals("03") ? "W" : "0";
            }
            pay.setPersonPayMode(payType);
            pay.setPersonPayMoney(fcOrderItem.getSelfPrem());
        }
        riskInfo.setPay(pay);
        /**********************  dutyList 责任信息 *************************************/
        List<DailyDutyInfo> dutyList = new ArrayList<>();
        DailyDutyInfo dutyInfo = new DailyDutyInfo();
        dutyInfo.setDutyCode(fcOrderItemDetail.getDutyCode());
        dutyInfo.setAmnt(amnt);
        dutyInfo.setPrem(prem);
        //免赔额
        dutyInfo.setGetLimit(0.0);
        //赔付比例
        dutyInfo.setGetRate(1.0);
        //计算规则
        dutyInfo.setCalRule("");
        dutyList.add(dutyInfo);
        riskInfo.setDutyList(dutyList);
        return riskInfo;
    }

    /**
     * 自然人签单
     *
     * @param ensureCode
     * @param funcFlag
     * @param perNo
     * @param orderItemNo
     * @return
     */
    public Map<String, Object> dailyNaturalSignBill(String ensureCode, String funcFlag, String perNo, String orderItemNo) {
        String funcFlagName = "";
        if (funcFlag.equals("YF0003")) {
            funcFlagName = "自然人预核保";
        } else if (funcFlag.equals("YF0004")) {
            funcFlagName = "自然人核保";
        } else if (funcFlag.equals("YF0005")) {
            funcFlagName = "自然人签单";
        } else if (funcFlag.equals("YF0001")) {
            funcFlagName = "基础单预核保";
        } else if (funcFlag.equals("YF0002")) {
            funcFlagName = "基础单签单";
        }
        Map<String, Object> resultMap = naturalSignBill(ensureCode, funcFlag, perNo, orderItemNo);
        if ("500".equals(resultMap.get("code"))) {
            Log.info("{}封装数据失败", funcFlagName);
            return resultMap;
        }
        UnderWriteReq underWriteReq = (UnderWriteReq) resultMap.get("data");
        long startStamp = System.currentTimeMillis();
        //调核心接口 基础单预核保
        String param = JSONObject.toJSONString(underWriteReq, SerializerFeature.DisableCircularReferenceDetect);
        Log.info("\n调用核心{}请求入参:{}\n", funcFlagName, param);
        String url = "";
        //基础单预核保：YF0001  基础单签单：YF0002 自然人预核：YF0003 自然人核保：YF0004 自然人签单：YF0005
        switch (funcFlag) {
            case "YF0003":
                url = myProps.getNaturalPreCheckUrl();
                break;
            case "YF0004":
                url = myProps.getNaturalCheckUrl();
                break;
            case "YF0005":
                url = myProps.getNaturalSignUrl();
                break;
            default:
                break;
        }
        String result = null;
        try {
            result = HttpUtil.postHttpRequestJson(url, param, myProps.getCoreAppId(), myProps.getCoreAppSecret());
            if (StringUtils.isEmpty(result)) {
                return ResultUtil.error("调用核心{}接口失败！", funcFlagName);
            }
        } catch (Exception e) {
            Log.info("调用核心{}接口失败!", funcFlagName);
            return ResultUtil.error("调用核心{}接口失败！", funcFlagName);
        }
        Log.info("\n调用核心{}返回：" + result, funcFlagName);
        resultMap.put("result", result);
        long endStamp = System.currentTimeMillis();
        Log.info("调用核心接口用时：" + (endStamp - startStamp));
        return resultMap;
    }

}

