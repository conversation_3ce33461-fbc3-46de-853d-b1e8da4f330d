package com.sinosoft.eflex.service.dailyinsure;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageHelper;
import com.hqins.common.helper.BeanCopier;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.dao.admin.EnsureAuditMapper;
import com.sinosoft.eflex.enums.*;
import com.sinosoft.eflex.enums.status.EnsureStateEnum;
import com.sinosoft.eflex.model.AddressEntity.EvaluationCustomer;
import com.sinosoft.eflex.model.AddressEntity.IdCardVerifyRequest;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.dailyplan.DailyInsureInfo;
import com.sinosoft.eflex.model.dailyplan.UnderWriteReq;
import com.sinosoft.eflex.model.sendmessage.SendMessageResp;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import com.sinosoft.eflex.service.*;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> wenying Xia
 * @date : 2020-04-15 10:44
 **/
@Service
public class DailyPlanService {
    /**
     * 日志
     */
    private static Logger Log = (Logger) LoggerFactory.getLogger(DailyPlanService.class);

    /**
     * 注入Dao层
     */
    @Autowired
    private EnsureAuditMapper ensureAuditMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;
    @Autowired
    private FcDailyInsureRiskInfoMapper fcDailyInsureRiskInfoMapper;
    @Autowired
    private FcDailyInsureRiskDetailInfoMapper fcDailyInsureRiskDetailInfoMapper;
    @Autowired
    private FcGrpContactMapper fcGrpContactMapper;
    @Autowired
    private FcEnsureContactMapper fcEnsureContactMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private FCContactGrpRelaMapper fcContactGrpRelaMapper;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private FCOrderMapper fcOrderMapper;
    @Autowired
    private FCOrderItemMapper fcOrderItemMapper;
    @Autowired
    private FCOrderItemDetailMapper fcOrderItemDetailMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FCPerRegistDayMapper fcPerRegistDayMapper;
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private MyProps myProps;
    @Autowired
    private FDAgentInfoMapper fdAgentInfoMapper;
    @Autowired
    private FCGrpApplicantMapper fcGrpApplicantMapper;
    @Autowired
    private FCGrpApplicantContactMapper fcGrpApplicantContactMapper;
    @Autowired
    private PremTrailService premTrailService;
    @Autowired
    private EnsureQueryService ensureQueryService;
    @Autowired
    private FDPlaceMapper fdPlaceMapper;    //地址mapper
    @Autowired
    private DailyIssueService dailyIssueService;
    @Autowired
    private FCMailInfoMapper fcMailInfoMapper;
    @Autowired
    private FCOrderBnfRelaMapper fcOrderBnfRelaMapper;
    @Autowired
    private FCPerInfoTempMapper fcPerInfoTempMapper;
    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;
    @Autowired
    private FdDaliyRiskInsureConfigMapper fdDaliyRiskInsureConfigMapper;
    @Autowired
    private FDFTPInfoMapper fdftpInfoMapper;
    @Autowired
    private AmountTrailService amountTrailService;
    @Autowired
    private AddressCheckService addressCheckService;

    /**
     * 日常计划查询
     *
     * @param token
     * @param params
     * @param pageNo
     * @param pageSize
     * @param isReal
     * @return
     */
    @Transactional
    public String getDailyplanList(String token, Map<String, String> params, int pageNo, int pageSize, String isReal) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            String manageCom = globalInput.getManageCom();
            if (StringUtils.isBlank(manageCom)) {
                throw new SystemException("初审岗用户管理机构为空！");
            } else {
                params.put("manageCom", manageCom);
            }
            List<HashMap<String, Object>> hashMaps = null;
            String ensureState = params.get("ensureState");
            //查询所有新增计划 所有状态的计划。
            PageHelper.startPage(pageNo, pageSize);
            hashMaps = fcEnsureMapper.selectAllDailyPlan(params);

//            //1. 要素查询计划状态为  空  得时候
//            if (ensureState == null || ensureState.equals("")) {
//                PageHelper.startPage(pageNo, pageSize);
//                //查询出所有的计划
//                hashMaps = fcEnsureMapper.selectAllDailyPlan(params);
//
//            //2. 要素查询计划状态为  待审核  得时候
//            else if (ensureState != null && ensureState.equals("013")) {
//                //查询所有新增计划 待审核状态的计划。
//                PageHelper.startPage(pageNo, pageSize);
//                hashMaps = fcEnsureMapper.selectByEnsureState013(params);
//            }
//            //3. 要素查询计划状态为 审核退回  得时候
//            else if (ensureState != null && ensureState.equals("017")) {
//                //查询所有新增计划 审核退回 状态的计划。
//                PageHelper.startPage(pageNo, pageSize);
//                hashMaps = fcEnsureMapper.selectByEnsureState017(params);
//            }
//            //4. 要素查询计划状态为  已承保  得时候
//            else if (ensureState != null && ensureState.equals("015")) {
//                //查询新增状态 已承保的计划和 变更计划所有的计划
//                //新建分页工具类
//                PageHelper.startPage(pageNo, pageSize);
//                hashMaps = fcEnsureMapper.selectByEnsureState015(params);
//
//            } else {
//                //正常查就可以
//                PageHelper.startPage(pageNo, pageSize);
//                hashMaps = ensureAuditMapper.getDailyplanList_addPrtNo(params);
//            }
            for (HashMap<String, Object> stringObjectHashMap : hashMaps) {
                stringObjectHashMap.put("PlanType", "日常计划");
                String ensureStateName = fdCodeMapper.selectNameByCode("EnsureState", stringObjectHashMap.get("EnsureState") != null ? (String) stringObjectHashMap.get("EnsureState") : "");
                stringObjectHashMap.put("ensureStateName", ensureStateName);
            }
            PageHelperUtil<HashMap<String, Object>> teamPageInfo = new PageHelperUtil<>(hashMaps);
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("list", teamPageInfo.getList());
            resultMap.put("data", dataMap);

            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "获取日常计划信息成功！");
        } catch (Exception e) {
            Log.info("获取日常计划信息失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "获取日常计划信息失败！");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 日常计划定制  基础信息页面初始化
     *
     * @param ensureCode
     * @return
     */
    @Transactional
    public String getBaseInfoByCodeList(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (ensureCode != null && !ensureCode.equals("null")) {
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
                Map<String, String> mapInfo = new HashMap<>();
                mapInfo.put("grpNo", fcEnsure.getGrpNo());
                // 获取token信息
                GlobalInput globalInput = userService.getSession(token);
                String manageCom = globalInput.getManageCom();
                if (StringUtils.isEmpty(manageCom) && !globalInput.getCustomType().matches("^(1|2)")) {
                    throw new SystemException("当前操作用户管理机构为空！");
                } else {
                    mapInfo.put("manageCom", manageCom);
                }
                FCGrpInfo fcGrpInfo = fcGrpInfoMapper.getGrpListByNameOrCode(mapInfo).get(0);
                List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate(ensureCode);
                ArrayList list = new ArrayList();
                for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos) {
                    String planCode = fcDailyInsureRiskDetailInfo.getPlanCode();
                    if (!list.contains(planCode)) {
                        list.add(planCode);
                    }
                }
                Object[] insuredType = list.toArray();
                //将险种编码和险种名称返回至前台
                List<Map<String, String>> riskinfo = fdRiskInfoMapper.selectRiskNameAndRiskCodeByEnsureCode(ensureCode);
                //初始化页面时也要添加新增字段    手续费比率以及佣金/服务津贴率
                Map<String, String> riskMap = riskinfo.get(0);
                Log.info("该福利对应的险种编码为: {}", riskMap.get("RiskCode"));
                String riskCode = riskMap.get("RiskCode");
                FcDailyInsureRiskInfo fcDailyInsureRiskInfo = fcDailyInsureRiskInfoMapper
                        .selectByEnsureCodeAndRiskCode(ensureCode, riskCode);
                Object commissionOrAllowanceRatio = fcDailyInsureRiskInfo.getCommissionOrAllowanceRatio();
                if (!ObjectUtils.isEmpty(commissionOrAllowanceRatio)) {
                    BigDecimal a1 = new BigDecimal(Double.valueOf(String.valueOf(commissionOrAllowanceRatio)));
                    BigDecimal a2 = new BigDecimal(100);
                    Double commissionOrAllowanceRatio1 = a1.multiply(a2).doubleValue();
                    fcDailyInsureRiskInfo
                            .setCommissionOrAllowanceRatio(String.valueOf(commissionOrAllowanceRatio1));
                }
                //初始化页面的时候也要把团体订单号返回至前台
                FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
                if (fcGrpOrder != null) {
                    resultMap.put("grpOrderNo", fcGrpOrder.getGrpOrderNo());
                } else {
                    resultMap.put("grpOrderNo", "");
                }
                resultMap.put("rateinfo", fcDailyInsureRiskInfo);
                resultMap.put("riskinfo", riskinfo);
                resultMap.put("insuredType", insuredType);
                resultMap.put("fcEnsureInfo", fcEnsure);
                resultMap.put("fcGrpInfo", fcGrpInfo);
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "日常计划定制 基础信息查询成功");
                //返回的 支付方式和 特别约定  复用的接口没有返回
                addPayTypeandsome(resultMap, ensureCode);
            } else {
                Log.info("这是 新增计划。");
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "日常计划定制-新增计划列表");
            }
        } catch (Exception e) {
            Log.info("", e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "日常计划定制 基础信息查询失败，请联系运维人员");
        } finally {
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 日常计划定制查询列表 --删除按钮
     *
     * @param authorization
     * @param ensureCode
     * @return
     */
    @Transactional
    public String deleteEnsureInfoByCodeList(String authorization, String ensureCode, String PrtNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtils.isEmpty(ensureCode)) {
                return JSON.toJSONString(ResultUtil.error("日常计划定制查询列表删除失败：请求参数为空！"));
            }
            //删除被保险人 相关的订单表和用户表
            List<Map<String, String>> maps = fcPerInfoMapper.selectInsuredPerson_All(ensureCode);
            if (maps != null) {
                for (Map<String, String> map : maps) {
                    String idNo = map.get("iDNo");
                    String grpNo = map.get("grpNo");
                    String grpOrderNo = map.get("grpOrderNo");
                    deleteisEnsuredPeople(authorization, ensureCode, idNo, grpNo, grpOrderNo);
                }
            }
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
            String grpAppNo = fcGrpOrder.getGrpAppNo();//团体投保人编号
            fcEnsureMapper.deleteByPrimaryKey(ensureCode);
            fcEnsureConfigMapper.deleteByConfigNo_021(ensureCode);
            fcEnsureConfigMapper.deleteByConfigNo_018(ensureCode);
            fcEnsureConfigMapper.deleteByConfigNo_011(ensureCode);
            fcGrpOrderMapper.deleteByEnsureCode(ensureCode);

            fcDailyInsureRiskDetailInfoMapper.deleteByEnsureCode(ensureCode);
            fcDailyInsureRiskInfoMapper.deleteByEnsureCode(ensureCode);

            fcGrpApplicantMapper.deleteByPrimaryKey(grpAppNo);
            fcGrpApplicantContactMapper.deleteByGrpAppNo(grpAppNo);


            resultMap.put("code", "200");
            resultMap.put("success", false);
            resultMap.put("message", "日常计划定制删除成功");
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "日常计划定制查询列表删除失败，请联系运维人员");
        } finally {
            return JSON.toJSONString(resultMap);
        }


    }

    /**
     * 点击下一步进行 ：：保存日常计划  险种信息
     *
     * @param authorization
     * @param params
     * @return
     */
    @Transactional
    public String savePlanInfo(String authorization, Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            String grpNo = (String) params.get("grpNo");
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("grpNo", grpNo);
            // 佣金/服务津贴率
            Double commissionOrAllowanceRatio = 0.0;
            //安颐无忧年金不用校验基础保单生效日期  大于  基础保单投保日期，因为安颐无忧年金默认生效日期是投保日期的下一天
            if ("14110".equals(params.get("planTypeCode"))) {
                String startAppntDate = String.valueOf(params.get("startAppntDate"));//基础保单投保日期
                //获取安颐无忧年金的基础单生效日期
                String cvaliDate = DateTimeUtil.getAppointDate(DateTimeUtil.strToDate(startAppntDate, ""), 1, "D");
                Log.info("基础单生效日期为: {}", cvaliDate);
                //校验必传参数    佣金/服务津贴率：commissionOrAllowanceRatio 是否为空, 不为空则是否在1-100之间的整数
                if (ObjectUtils.isEmpty(params.get("commissionOrAllowanceRatio"))) {
                    Log.info("日常计划佣金/服务津贴率不能为空");
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", "日常计划佣金/服务津贴率不能为空");
                } else {
                    commissionOrAllowanceRatio = Double
                            .parseDouble(String.valueOf(params.get("commissionOrAllowanceRatio")));
                    if (commissionOrAllowanceRatio < 0 || commissionOrAllowanceRatio > 100) {
                        resultMap.put("message", "佣金/服务津贴率为0-100之间的数 且 佣金率仅支持录入小数点后两位");
                    }
                }
            } else {
                //判断基础保单生效日期  大于  基础保单投保日期
                String startAppntDate = String.valueOf(params.get("startAppntDate"));//基础保单投保日期
                String cvaliDate = String.valueOf(params.get("cvaliDate"));//生效日期
                //比较日期 前>后：false
                if ((DateTimeUtil.checkDate(cvaliDate, startAppntDate)) || startAppntDate.equals(cvaliDate)) {
                    Log.info("基础保单生效日期应该大于基础保单投保日期");
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", "基础保单生效日期应该大于基础保单投保日期");
                    return JSON.toJSONString(resultMap);
                }
            }
            String ensureCode = (String) params.get("ensureCode");
            String ensureCode1 = "";
            String grpOrderNo = "";
            // 获取session
            GlobalInput globalInput = userService.getSession(authorization);
            FCEnsureConfig config023 = new FCEnsureConfig();
            setEnsureConfig023(globalInput,config023);
            // 如果参数中没有 dailyPlanCode ，那说明是新增入库的操作
            if (ensureCode == null || ensureCode.equals("")) {
                try {
                    if ("16380".equals(params.get("planTypeCode"))) {
                        //查出 该企业 所有的 日常定制福利
                        List<FCEnsure> fcEnsures = fcEnsureMapper.selectEnsureList_Daily(paramsMap);
                        if (fcEnsures.size() > 0) {
                            Log.info("该企业 定制过日常计划定制 ");
                            resultMap.put("success", false);
                            resultMap.put("code", "500");
                            resultMap.put("message", "该企业已定制横琴福裕团体重大疾病保险险种的基础单，暂不支持重复定制！");
                            return JSON.toJSONString(resultMap);
                        }
                    }
                    grpOrderNo = maxNoService.createMaxNo("GrpOrderNo", null, 20);
                    //生成 “日常计划编码”   对应表中的  福利编号
                    String dailyPlanCode = "FL" + maxNoService.createMaxNo("EnsureCode", "", 18);
                    //配置流水号
                    String DeployNo = maxNoService.createMaxNo("EnsureConfig", "", 20);
                    String prtNo = maxNoService.createMaxNo("TPrtNo", "", 20);
                    // 团体投保单号
                    String grpPrtNo = maxNoService.createMaxNo("PrtNo", "A511041", 8);
                    FCEnsure fcEnsure = new FCEnsure();
                    FCEnsureConfig specialConfig = new FCEnsureConfig();
                    FCEnsureConfig paytypeonInternal = new FCEnsureConfig(); //支付方式和特别约定
                    FCGrpOrder fcGrpOrder = new FCGrpOrder();
                    FcDailyInsureRiskInfo fcDailyInsureRiskInfo = new FcDailyInsureRiskInfo();//日常投保险种信息配置表
                    FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo = new FcDailyInsureRiskDetailInfo();//日常投保险种信息配置详情表
                    //根据企业名称和统一机构信用代码（税号）  查询出对应的 GrpNo（企业客户号）
                    setEnsureInfo(fcEnsure, authorization, params);
                    specialConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));//福利配置流水号
                    setEnsureConfig018(authorization, params, specialConfig);
                    paytypeonInternal.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));//福利配置流水号
                    setEnsureConfig021(authorization, params, paytypeonInternal);
                    setDailyInsureRiskInfo(fcDailyInsureRiskInfo);
                    fcDailyInsureRiskInfo.setOperator(globalInput.getUserNo());
                    //基本信息
                    String GrpNo = grpNo;
                    fcEnsure.setEnsureCode(dailyPlanCode);
                    //企业客户号
                    fcEnsure.setGrpNo(GrpNo);
                    //福利编号
                    specialConfig.setEnsureCode(dailyPlanCode);
                    //企业客户号
                    specialConfig.setGrpNo(GrpNo);
                    //福利编号
                    paytypeonInternal.setEnsureCode(dailyPlanCode);
                    //企业客户号
                    paytypeonInternal.setGrpNo(GrpNo);
                    //险种信息
                    fcDailyInsureRiskInfo.setDeployNo(DeployNo);
                    fcDailyInsureRiskInfo.setEnsureCode(dailyPlanCode);
                    fcDailyInsureRiskInfo.setFeeRatio((String) params.get("feeRatio"));
                    fcDailyInsureRiskInfo.setCommissionOrAllowanceRatio(Double.valueOf(commissionOrAllowanceRatio / 100).toString());
                    fcDailyInsureRiskInfo.setRiskCode((String) params.get("planTypeCode"));
                    // 获取当前企业信息
                    FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(grpNo);
                    //根据 grpNo在 企业联系人关系表中 得到 hr的编号
                    String contactNo = fcContactGrpRelaMapper.selectContactNoByGrpNoandcontactType(grpNo);
                    // 获取当前 企业联系人
                    FcGrpContact fcGrpContact = fcGrpContactMapper.selectByPrimaryKey(contactNo);
                    String grpAppNo = "";
                    //PolicyState首次入库即复核通过 ==null 证明是首次提交
                    // 插入 团体投保人表 FCGrpApplicant
                    // 团体投保人编号
                    grpAppNo = maxNoService.createMaxNo("GrpAppNo", "", 20);
                    FCGrpApplicant fcGrpApplicant = new FCGrpApplicant();
                    BeanUtils.copyProperties(fcGrpInfo, fcGrpApplicant);
                    fcGrpApplicant.setGrpAppNo(grpAppNo);
                    fcGrpApplicant.setOperator(globalInput.getUserNo());
                    fcGrpApplicant = CommonUtil.initObject(fcGrpApplicant, "INSERT");
                    fcGrpApplicantMapper.insertSelective(fcGrpApplicant);

                    FCGrpApplicantContact fcGrpApplicantContact = new FCGrpApplicantContact();
                    String serialNo = maxNoService.createMaxNo("GrpAppContact", "", 20);
                    fcGrpApplicantContact.setSerialNo(serialNo);
                    fcGrpApplicantContact.setGrpAppNo(grpAppNo);
                    fcGrpApplicantContact.setGrpNo(grpNo);
                    fcGrpApplicantContact.setNativeplace(fcGrpContact == null ? "" : fcGrpContact.getNativeplace());
                    fcGrpApplicantContact.setName(fcGrpContact == null ? "" : fcGrpContact.getName());
                    fcGrpApplicantContact.setSex(fcGrpContact == null ? "" : fcGrpContact.getSex());
                    fcGrpApplicantContact.setBirthDay(fcGrpContact == null ? "" : fcGrpContact.getBirthDay());
                    fcGrpApplicantContact.setIdType(fcGrpContact == null ? "" : fcGrpContact.getIdType());
                    fcGrpApplicantContact.setIdNo(fcGrpContact == null ? "" : fcGrpContact.getIdNo());
                    fcGrpApplicantContact.setIdTypeStartDate(fcGrpContact == null ? "" : fcGrpContact.getIdTypeStartDate());
                    fcGrpApplicantContact.setIdTypeEndDate(fcGrpContact == null ? "" : fcGrpContact.getIdTypeEndDate());
                    fcGrpApplicantContact.setDepartment(fcGrpContact == null ? "" : fcGrpContact.getDepartment());
                    fcGrpApplicantContact.setMobilePhone(fcGrpContact == null ? "" : fcGrpContact.getMobilePhone());
                    fcGrpApplicantContact.setEmail(fcGrpContact == null ? "" : fcGrpContact.getEmail());
                    fcGrpApplicantContact.setIdImage1(fcGrpContact == null ? "" : fcGrpContact.getIdImage1());
                    fcGrpApplicantContact.setIdImage2(fcGrpContact == null ? "" : fcGrpContact.getIdImage2());
                    fcGrpApplicantContact.setOperator(globalInput.getUserNo());
                    fcGrpApplicantContact = CommonUtil.initObject(fcGrpApplicantContact, "INSERT");
                    fcGrpApplicantContactMapper.insertSelective(fcGrpApplicantContact);
                    //团体订单号

                    fcGrpOrder.setGrpOrderNo(grpOrderNo);
                    fcGrpOrder.setGrpOrderType("02");//团体订单类型:01年度福利02日常福利03集中采购04小微企业
                    fcGrpOrder.setEnsureCode(dailyPlanCode);
                    fcGrpOrder.setGrpOrderStatus("01");
                    //安颐无忧年金不用校验基础保单生效日期  大于  基础保单投保日期，因为安颐无忧年金默认生效日期是投保日期的下一天
                    if ("14110".equals(params.get("planTypeCode"))) {
                        String startAppntDate = String.valueOf(params.get("startAppntDate"));//基础保单投保日期
                        //获取安颐无忧年金的基础单生效日期
                        Date nowDate = DateTimeUtil.strToDate(startAppntDate, "yyyy-MM-dd");
                        Calendar c = Calendar.getInstance();
                        c.setTime(nowDate);
                        // 日期加1天
                        c.set(Calendar.DATE, c.get(Calendar.DATE) + 1);
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        String cvaliDate = dateFormat.format(c.getTime());
                        fcGrpOrder.setCommitDate(startAppntDate);
                        fcGrpOrder.setEffectDate(cvaliDate);
                    } else {
                        fcGrpOrder.setCommitDate((String) params.get("startAppntDate"));
                        fcGrpOrder.setEffectDate((String) params.get("cvaliDate"));
                    }
                    fcGrpOrder.setPrtNo(prtNo);//投保单号
//                    fcGrpOrder.setGrpContNo(GrpContNo);//团体保单号   核心生成的
                    fcGrpOrder.setOperator(globalInput.getUserNo());
                    fcGrpOrder.setGrpNo(GrpNo);
                    fcGrpOrder.setGrpAppNo(grpAppNo);//团体投保人编号
                    fcGrpOrder = CommonUtil.initObject(fcGrpOrder, "INSERT");
                    fcGrpOrderMapper.insertSelective(fcGrpOrder);

                    //**************************************
                    //更新平台与核心投保单号对照关系表状态为”已发送“
                    Map<String, String> map = new HashMap<String, String>();
                    //平台投保单号
                    map.put("PrtNo", fcGrpOrder.getPrtNo());
                    //核心投保单号(团体)
                    map.put("tPrtNo", grpPrtNo);
                    map.put("RelaSn", maxNoService.createMaxNo("RelaSn", "", 20));
                    //状态描述
                    map.put("describe", "待发送");
                    map.put("currentDate", DateTimeUtil.getCurrentDate());
                    map.put("currentTime", DateTimeUtil.getCurrentTime());
//                    fcGrpOrderMapper.insertFcPrtAndCoreRela(map);
                    fcGrpOrderMapper.insertFcPrtAndCoreRela06(map);
                    //**************************************

                    //险种计划相关信息
                    List<String> insuredType = (List<String>) params.get("insuredType");
                    //如果是安颐无忧年金的话
                    if ("14110".equals(params.get("planTypeCode"))) {
                        if (insuredType.size() > 0) {
                            for (String s : insuredType) {
                                setDailyInsureRiskDetailInfo(authorization, params, fcDailyInsureRiskDetailInfo);
                                fcDailyInsureRiskDetailInfo.setEnsureCode(dailyPlanCode);
                                fcDailyInsureRiskDetailInfo.setDeployNo(DeployNo);
                                fcDailyInsureRiskDetailInfo
                                        .setDeployDetalNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcDailyInsureRiskDetailInfo.setPlanCode(s);
                                fcDailyInsureRiskDetailInfo.setPlanState("0");
                                fcDailyInsureRiskDetailInfo.setPlanUnderwritingStatus("1");
                                fcDailyInsureRiskDetailInfo.setOperator(globalInput.getUserNo());
                                fcDailyInsureRiskDetailInfoMapper.insert(fcDailyInsureRiskDetailInfo);
                            }
                        } else {
                            setDailyInsureRiskDetailInfo(authorization, params, fcDailyInsureRiskDetailInfo);
                            fcDailyInsureRiskDetailInfo.setEnsureCode(dailyPlanCode);
                            fcDailyInsureRiskDetailInfo.setDeployNo(DeployNo);
                            fcDailyInsureRiskDetailInfo
                                    .setDeployDetalNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                            // TODO 计划编码暂时设为ID4110
                            fcDailyInsureRiskDetailInfo.setPlanCode("ID4110");
                            fcDailyInsureRiskDetailInfo.setPlanState("0");
                            fcDailyInsureRiskDetailInfo.setPlanUnderwritingStatus("1");
                            fcDailyInsureRiskDetailInfo.setOperator(globalInput.getUserNo());
                            fcDailyInsureRiskDetailInfoMapper.insert(fcDailyInsureRiskDetailInfo);
                        }
                    } else {
                        if (insuredType != null) {
                            for (String i : insuredType) {
                                setDailyInsureRiskDetailInfo(authorization, params, fcDailyInsureRiskDetailInfo);
                                fcDailyInsureRiskDetailInfo.setEnsureCode(dailyPlanCode);
                                fcDailyInsureRiskDetailInfo.setDeployNo(DeployNo);
                                fcDailyInsureRiskDetailInfo.setDeployDetalNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcDailyInsureRiskDetailInfo.setPlanCode(i);
                                fcDailyInsureRiskDetailInfo.setPlanState("0");
                                fcDailyInsureRiskDetailInfo.setPlanUnderwritingStatus("1");
                                fcDailyInsureRiskDetailInfo.setOperator(globalInput.getUserNo());
                                fcDailyInsureRiskDetailInfoMapper.insert(fcDailyInsureRiskDetailInfo);
                            }
                        } else {
                            resultMap.put("success", false);
                            resultMap.put("code", "500");
                            resultMap.put("message", "可投保计划选择不能为空，请检查");
                            return JSON.toJSONString(resultMap);
                        }
                    }
                    try {
                        String clientno = fcGrpInfo.getClientno();
                        fcEnsure.setClientNo(clientno);
                        fcEnsure.setGreenInsurance((Boolean) params.get("greenInsurance"));

                        fcEnsureMapper.insertSelective(fcEnsure);
                        fcEnsureConfigMapper.insertSelective(specialConfig);
                        fcEnsureConfigMapper.insertSelective(config023);
                        fcEnsureConfigMapper.insertSelective(paytypeonInternal);
                        fcDailyInsureRiskInfoMapper.insert(fcDailyInsureRiskInfo);
                        dailyPlanCode = fcEnsure.getEnsureCode();
                        Log.info("保存日常计划  险种信息完成");
                    } catch (Exception e) {
                        Log.info("error", e);
                        resultMap.put("code", "500");
                        resultMap.put("success", false);
                        resultMap.put("message", "日常计划  险种信息保存操作出了问题");
                        return JSON.toJSONString(resultMap);
                    }
                    ensureCode1 = dailyPlanCode;
                } catch (Exception e) {
                    Log.info("error", e);
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", "日常计划  险种信息保存过程失败.");
                    return JSON.toJSONString(resultMap);
                }
            } else {
                ensureCode1 = ensureCode;
                FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
                String grpAppNo = fcGrpOrder.getGrpAppNo();//团体投保人编号
                FCGrpApplicant fcGrpApplicant = fcGrpApplicantMapper.selectByPrimaryKey(grpAppNo);
                if (!fcGrpApplicant.getGrpNo().equals(grpNo)) {
                    //说明 改变了 企业。此时 团体投保被联系人表、团体投保人表  团体订单表 都需要更改
                    fcGrpOrder.setGrpOrderType("02");//团体订单类型:01年度福利02日常福利03集中采购04小微企业
                    fcGrpOrder.setGrpOrderStatus("01");
                    //如果是安颐无忧年金，则生效日期为投保日期加一天
                    if ("14110".equals(params.get("planTypeCode"))) {
                        String startAppntDate = String.valueOf(params.get("startAppntDate"));//基础保单投保日期
                        //获取安颐无忧年金的基础单生效日期
                        Date nowDate = DateTimeUtil.strToDate(startAppntDate, "yyyy-MM-dd");
                        Calendar c = Calendar.getInstance();
                        c.setTime(nowDate);
                        // 日期加1天
                        c.set(Calendar.DATE, c.get(Calendar.DATE) + 1);
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        String cvaliDate = dateFormat.format(c.getTime());
                        fcGrpOrder.setCommitDate(startAppntDate);
                        fcGrpOrder.setEffectDate(cvaliDate);
                        //校验必传参数    佣金/服务津贴率：commissionOrAllowanceRatio 是否为空, 不为空则是否在1-100之间的整数
                        if (null == params.get("commissionOrAllowanceRatio") || "".equals(params.get("commissionOrAllowanceRatio"))) {
                            Log.info("日常计划佣金/服务津贴率不能为空");
                            resultMap.put("code", "500");
                            resultMap.put("success", false);
                            resultMap.put("message", "日常计划佣金/服务津贴率不能为空");
                        } else {
                            commissionOrAllowanceRatio = Double.parseDouble(String.valueOf(params.get("commissionOrAllowanceRatio")));
                            if (commissionOrAllowanceRatio < 0 || commissionOrAllowanceRatio > 100) {
                                resultMap.put("message", "佣金/服务津贴率为0-100之间的整数");
                            }
                        }
                        //更新日常投保险种信息配置表 新增字段 佣金/服务津贴率 是否发生变化
                        FcDailyInsureRiskInfo fcDailyInsureRiskInfo = new FcDailyInsureRiskInfo();
                        fcDailyInsureRiskInfo.setEnsureCode(ensureCode);
                        fcDailyInsureRiskInfo.setFeeRatio((String) params.get("feeRatio"));
                        fcDailyInsureRiskInfo.setCommissionOrAllowanceRatio(
                                Double.valueOf(commissionOrAllowanceRatio / 100).toString());
                        fcDailyInsureRiskInfoMapper.updateRatioByEnsureCode(fcDailyInsureRiskInfo);
                    } else {
                        fcGrpOrder.setCommitDate((String) params.get("startAppntDate"));
                        fcGrpOrder.setEffectDate((String) params.get("cvaliDate"));
                    }
                    fcGrpOrder.setEffectDate((String) params.get("cvaliDate"));
                    fcGrpOrder.setOperator(globalInput.getUserNo());
                    fcGrpOrder.setGrpNo(grpNo);
                    fcGrpOrder.setGrpAppNo(grpAppNo);//团体投保人编号
                    fcGrpOrder = CommonUtil.initObject(fcGrpOrder, "UPDATE");
                    fcGrpOrderMapper.updateByPrimaryKeySelective(fcGrpOrder);
                    // 获取当前企业信息
                    FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(grpNo);
                    //根据 grpNo在 企业联系人关系表中 得到 hr的编号
                    String contactNo = fcContactGrpRelaMapper.selectContactNoByGrpNoandcontactType(grpNo);
                    // 获取当前 企业联系人
                    FcGrpContact fcGrpContact = fcGrpContactMapper.selectByPrimaryKey(contactNo);
                    fcGrpApplicant.setGrpNo(fcGrpInfo.getGrpNo());
                    fcGrpApplicant.setGrpName(fcGrpInfo.getGrpName());
                    fcGrpApplicant.setGrpAddRess(fcGrpInfo.getGrpAddRess());
                    fcGrpApplicant.setZipCode(fcGrpInfo.getZipCode());
                    fcGrpApplicant.setUnifiedsociCode(fcGrpInfo.getUnifiedsociCode());
                    fcGrpApplicant.setGrpType(fcGrpInfo.getGrpType());
                    fcGrpApplicant.setAccName(fcGrpInfo.getAccName());
                    fcGrpApplicant.setGrpBankCode(fcGrpInfo.getGrpBankCode());
                    fcGrpApplicant.setGrpBankAccNo(fcGrpInfo.getGrpBankAccNo());
                    fcGrpApplicant.setPeoples(fcGrpInfo.getPeoples());
                    fcGrpApplicant.setCorporationMan(fcGrpInfo.getCorporationMan());
                    fcGrpApplicant.setTelphone(fcGrpInfo.getTelphone());
                    fcGrpApplicant.setRegaddress(fcGrpInfo.getRegaddress());
                    fcGrpApplicant.setEmail(fcGrpInfo.getEmail());
                    fcGrpApplicant.setOperator(globalInput.getUserNo());
                    fcGrpApplicant = CommonUtil.initObject(fcGrpApplicant, "UPDATE");
                    fcGrpApplicantMapper.updateByPrimaryKeySelective(fcGrpApplicant);
                    FCGrpApplicantContact fcGrpApplicantContact = fcGrpApplicantContactMapper.selectByGrpNoandGrpAppNo(fcGrpApplicant.getGrpNo(), grpAppNo);
                    if (fcGrpApplicantContact != null) {
                        fcGrpApplicantContact.setGrpAppNo(grpAppNo);
                        fcGrpApplicantContact.setGrpNo(grpNo);
                        fcGrpApplicantContact.setName(fcGrpContact == null ? "" : fcGrpContact.getName());
                        fcGrpApplicantContact.setSex(fcGrpContact == null ? "" : fcGrpContact.getSex());
                        fcGrpApplicantContact.setIdType(fcGrpContact == null ? "" : fcGrpContact.getIdType());
                        fcGrpApplicantContact.setIdNo(fcGrpContact == null ? "" : fcGrpContact.getIdNo());
                        fcGrpApplicantContact.setMobilePhone(fcGrpContact == null ? "" : fcGrpContact.getMobilePhone());
                        fcGrpApplicantContact.setEmail(fcGrpContact == null ? "" : fcGrpContact.getEmail());
                        fcGrpApplicantContact.setOperator(globalInput.getUserNo());
                        fcGrpApplicantContact = CommonUtil.initObject(fcGrpApplicantContact, "UPDATE");
                        fcGrpApplicantContactMapper.updateByPrimaryKeySelective(fcGrpApplicantContact);
                    }
                }
                //如果是安颐无忧年金，则生效日期为投保日期加一天
                if ("14110".equals(params.get("planTypeCode"))) {
                    String startAppntDate = String.valueOf(params.get("startAppntDate"));//基础保单投保日期
                    //获取安颐无忧年金的基础单生效日期
                    Date nowDate = DateTimeUtil.strToDate(startAppntDate, "yyyy-MM-dd");
                    Calendar c = Calendar.getInstance();
                    c.setTime(nowDate);
                    // 日期加1天
                    c.set(Calendar.DATE, c.get(Calendar.DATE) + 1);
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    String cvaliDate = dateFormat.format(c.getTime());
                    fcGrpOrder.setCommitDate(startAppntDate);
                    fcGrpOrder.setEffectDate(cvaliDate);
                    //校验必传参数    佣金/服务津贴率：commissionOrAllowanceRatio 是否为空, 不为空则是否在1-100之间的整数
                    if (null == params.get("commissionOrAllowanceRatio") || "".equals(params.get("commissionOrAllowanceRatio"))) {
                        Log.info("日常计划佣金/服务津贴率不能为空");
                        resultMap.put("code", "500");
                        resultMap.put("success", false);
                        resultMap.put("message", "日常计划佣金/服务津贴率不能为空");
                    } else {
                        commissionOrAllowanceRatio = Double
                                .parseDouble(String.valueOf(params.get("commissionOrAllowanceRatio")));
                        if (commissionOrAllowanceRatio < 0 || commissionOrAllowanceRatio > 100) {
                            resultMap.put("message", "佣金/服务津贴率为0-100之间的整数");
                        }
                    }
                    //校验必传参数    手续费比率：feeRatio
                    if (null == params.get("feeRatio") || "".equals("feeRatio")) {
                        Log.info("日常计划手续费比率不能为空");
                        resultMap.put("code", "500");
                        resultMap.put("success", false);
                        resultMap.put("message", "日常计划手续费比率不能为空");
                    }
                    //更新日常投保险种信息配置表 新增字段 佣金/服务津贴率 是否发生变化
                    FcDailyInsureRiskInfo fcDailyInsureRiskInfo = new FcDailyInsureRiskInfo();
                    fcDailyInsureRiskInfo.setEnsureCode(ensureCode);
                    fcDailyInsureRiskInfo.setFeeRatio((String) params.get("feeRatio"));
                    fcDailyInsureRiskInfo
                            .setCommissionOrAllowanceRatio(Double.valueOf(commissionOrAllowanceRatio / 100).toString());
                    fcDailyInsureRiskInfoMapper.updateRatioByEnsureCode(fcDailyInsureRiskInfo);
                }
                //判断参数中是否有编号，如果有说明是从上一步来的。需要就行修改的操作，而不是新增
                // 获取参数
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
                if (fcEnsure == null) {
                    Log.info("福利编号" + ensureCode + "对应的福利信息不存在！");
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "福利编号" + ensureCode + "对应的福利信息不存在，请检查");
                    return JSON.toJSONString(resultMap);
                }
                setEnsureInfo11(fcEnsure, authorization, params);
                fcEnsure.setGrpNo(grpNo);
                if (!Objects.isNull(params.get("greenInsurance"))) {
                    fcEnsure.setGreenInsurance((Boolean) params.get("greenInsurance"));
                }
                fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
                //特别约定
                Map<String, Object> ensureMap = new HashMap<>();
                ensureMap.put("ensureCode", ensureCode);
                ensureMap.put("configNo", "018");
                FCEnsureConfig fcEnsureConfig018 = fcEnsureConfigMapper.selectFcensureconfig(ensureMap);
                setEnsureConfig018(authorization, params, fcEnsureConfig018);
                fcEnsureConfigMapper.updateByPrimaryKeySelective(fcEnsureConfig018);
                Map<String, Object> ensureMap021 = new HashMap<>();
                ensureMap.put("ensureCode", ensureCode);
                ensureMap.put("configNo", "021");
                FCEnsureConfig fcEnsureConfig021 = fcEnsureConfigMapper.selectFcensureconfig(ensureMap);
                setEnsureConfig021(authorization, params, fcEnsureConfig021);
                fcEnsureConfigMapper.updateByPrimaryKeySelective(fcEnsureConfig021);
                /*ensureMap.put("ensureCode", ensureCode);
                ensureMap.put("configNo", "023");
                FCEnsureConfig fcEnsureConfig023 = fcEnsureConfigMapper.selectFcensureconfig(ensureMap);
                setEnsureConfig023(authorization,fcEnsureConfig023);
                fcEnsureConfigMapper.updateByPrimaryKeySelective(fcEnsureConfig023);*/
                //获取一下 团体订单号
                grpOrderNo = fcGrpOrder.getGrpOrderNo();
                //先把数据里面所有的计划改成无效的。然后根据下面最新的把计划改成有效的。
                fcDailyInsureRiskDetailInfoMapper.updatePlanCodeTobad(ensureCode);
                //险种信息
                //先判断可投投保计划是否更改
                List<String> insuredType = (List<String>) params.get("insuredType");
                if ("14110".equals(params.get("planTypeCode"))) {
                    if (insuredType.size() > 0) {
                        for (String s : insuredType) {
                            FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo = fcDailyInsureRiskDetailInfoMapper
                                    .selectByPlanCodeAndEnsureCode(ensureCode, "1", "14110");
                            if (fcDailyInsureRiskDetailInfo != null) {
                                fcDailyInsureRiskDetailInfo.setPlanState("0");
                                fcDailyInsureRiskDetailInfoMapper.updateByPrimaryKeySelective(fcDailyInsureRiskDetailInfo);
                            } else {
                                String DeployNo = maxNoService.createMaxNo("EnsureConfig", "", 20);// 配置流水号
                                // 如果数据库中没有这个字段，那么需要新增
                                FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo1 = new FcDailyInsureRiskDetailInfo();
                                setDailyInsureRiskDetailInfo(authorization, params, fcDailyInsureRiskDetailInfo1);
                                fcDailyInsureRiskDetailInfo1.setEnsureCode(ensureCode);
                                fcDailyInsureRiskDetailInfo1.setDeployNo(DeployNo);
                                fcDailyInsureRiskDetailInfo1.setDeployDetalNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                                fcDailyInsureRiskDetailInfo1.setPlanCode(s);
                                fcDailyInsureRiskDetailInfo1.setPlanState("0");
                                fcDailyInsureRiskDetailInfo1.setPlanUnderwritingStatus("1");
                                fcDailyInsureRiskDetailInfoMapper.insert(fcDailyInsureRiskDetailInfo1);
                            }
                        }
                    } else {
                        FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo = fcDailyInsureRiskDetailInfoMapper.selectByPlanCodeAndEnsureCode(ensureCode, "1", "14110");
                        if (fcDailyInsureRiskDetailInfo != null) {
                            fcDailyInsureRiskDetailInfo.setPlanState("0");
                            fcDailyInsureRiskDetailInfoMapper.updateByPrimaryKeySelective(fcDailyInsureRiskDetailInfo);
                        } else {
                            String DeployNo = maxNoService.createMaxNo("EnsureConfig", "", 20);// 配置流水号
                            // 如果数据库中没有这个字段，那么需要新增
                            FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo1 = new FcDailyInsureRiskDetailInfo();
                            setDailyInsureRiskDetailInfo(authorization, params, fcDailyInsureRiskDetailInfo1);
                            fcDailyInsureRiskDetailInfo1.setEnsureCode(ensureCode);
                            fcDailyInsureRiskDetailInfo1.setDeployNo(DeployNo);
                            fcDailyInsureRiskDetailInfo1.setDeployDetalNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                            // TODO 因为安颐无忧年金业务还未给出计划详情，故定制时后台直接写死的ID4110
                            fcDailyInsureRiskDetailInfo1.setPlanCode("ID4110");
                            fcDailyInsureRiskDetailInfo1.setPlanState("0");
                            fcDailyInsureRiskDetailInfo1.setPlanUnderwritingStatus("1");
                            fcDailyInsureRiskDetailInfoMapper.insert(fcDailyInsureRiskDetailInfo1);
                        }
                    }
                } else if ("16380".equals(params.get("planTypeCode"))) {
                    for (String s : insuredType) {
                        FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo = fcDailyInsureRiskDetailInfoMapper.selectByPlanCodeAndEnsureCode(ensureCode, s, "16380");
                        if (fcDailyInsureRiskDetailInfo != null) {
                            fcDailyInsureRiskDetailInfo.setPlanState("0");
                            fcDailyInsureRiskDetailInfoMapper.updateByPrimaryKeySelective(fcDailyInsureRiskDetailInfo);
                        } else {
                            String DeployNo = maxNoService.createMaxNo("EnsureConfig", "", 20);//配置流水号
                            //如果数据库中没有这个字段，那么需要新增
                            FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo1 = new FcDailyInsureRiskDetailInfo();
                            setDailyInsureRiskDetailInfo(authorization, params, fcDailyInsureRiskDetailInfo1);
                            fcDailyInsureRiskDetailInfo1.setEnsureCode(ensureCode);
                            fcDailyInsureRiskDetailInfo1.setDeployNo(DeployNo);
                            fcDailyInsureRiskDetailInfo1.setDeployDetalNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                            fcDailyInsureRiskDetailInfo1.setPlanCode(s);
                            fcDailyInsureRiskDetailInfo1.setPlanState("0");
                            fcDailyInsureRiskDetailInfo1.setPlanUnderwritingStatus("1");
                            fcDailyInsureRiskDetailInfoMapper.insert(fcDailyInsureRiskDetailInfo1);
                        }
                    }

                }
            }
            //短信发送
            /**
             * 使用角色 userRole 初审 -- 0 HR -- 1
             */
            // 若使用角色为初审，则需要发送信息给HR，若使用角色为HR，则不发送信息
            String userRole = String.valueOf(params.get("userRole"));
            if ("0".equals(userRole)) {
                Map<String, Object> mapInfo = new HashMap<>();
                mapInfo.put("grpNo", grpNo);
                mapInfo.put("contactType", "01");
                List<FcGrpContact> fcGrpContact = fcGrpContactMapper.selectContactsInfo(mapInfo);
                String Phone = fcGrpContact.get(0).getMobilePhone();
                // 短信发送
                // add by zch 20210223
                FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(grpNo);
                SendSMSReq sendSMSReq = new SendSMSReq();
                sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_011.getCode());
                sendSMSReq.setPhones(Phone);
                Map<String, Object> map = new HashMap<>();
                map.put("grp_name", fcGrpInfo.getGrpName());
                map.put("ensure_name", params.get("ensureName"));
                sendSMSReq.setParam(map);
                sendMessageService.sendSMS(sendSMSReq);
            }

            //返回一些数据 以至于前端能第一时间获取到这些值，然后再根据这些数据作为参数调回显的接口。
            String contactNo = fcContactGrpRelaMapper.selectContactNoByGrpNoandcontactType(grpNo);
            resultMap.put("ensureCode", ensureCode1);
            resultMap.put("contactNo", contactNo);
            resultMap.put("grpNo", grpNo);
            resultMap.put("grpOrderNo", grpOrderNo);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "日常计划定制信息填写成功");
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "日常计划定信息保存失败，请联系运维人员");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);

    }

    private void setDailyInsureRiskInfo(FcDailyInsureRiskInfo fcDailyInsureRiskInfo) {
        //险种信息
        fcDailyInsureRiskInfo.setOperatorCom("");
        fcDailyInsureRiskInfo.setMakeDate((DateTimeUtil.getCurrentDateTime()));
        fcDailyInsureRiskInfo.setMakeTime(DateTimeUtil.getCurrentTime());
        fcDailyInsureRiskInfo.setModifyDate((DateTimeUtil.getCurrentDateTime()));
        fcDailyInsureRiskInfo.setModifyTime(DateTimeUtil.getCurrentTime());


    }

    private void setEnsureConfig018(String authorization, Map<String, Object> params, FCEnsureConfig specialConfig) {

        specialConfig.setConfigNo("018");//特别约定编号
        specialConfig.setConfigValue((String) params.get("specialAgreement"));//特别约定内容
        specialConfig.setMakeDate(DateTimeUtil.getCurrentDateTime());
        specialConfig.setMakeTime(DateTimeUtil.getCurrentTime());
        specialConfig.setModifyDate(DateTimeUtil.getCurrentDateTime());
        specialConfig.setModifyTime(DateTimeUtil.getCurrentTime());
        GlobalInput globalInput = userService.getSession(authorization);
        specialConfig.setOperator(globalInput.getUserNo());


    }

    private void setEnsureConfig021(String authorization, Map<String, Object> params, FCEnsureConfig paytypeonInternal) {

        paytypeonInternal.setConfigNo("021");//线上投保支付方式*******************************************************是不是 021？不知道
        paytypeonInternal.setConfigValue((String) params.get("payType"));//线上投保支付方式
        GlobalInput globalInput = userService.getSession(authorization);
        paytypeonInternal.setOperator(globalInput.getUserNo());
        paytypeonInternal.setMakeDate(DateTimeUtil.getCurrentDateTime());
        paytypeonInternal.setMakeTime(DateTimeUtil.getCurrentTime());
        paytypeonInternal.setModifyDate(DateTimeUtil.getCurrentDateTime());
        paytypeonInternal.setModifyTime(DateTimeUtil.getCurrentTime());
    }
    private void setEnsureConfig023(GlobalInput globalInput,FCEnsureConfig config) {
        config.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));//福利配置流水号
        config.setConfigNo("023");
        config.setConfigValue("D");
        config.setOperator(globalInput.getUserNo());
        config.setMakeDate(DateTimeUtil.getCurrentDateTime());
        config.setMakeTime(DateTimeUtil.getCurrentTime());
        config.setModifyDate(DateTimeUtil.getCurrentDateTime());
        config.setModifyTime(DateTimeUtil.getCurrentTime());
    }


    private void setEnsureInfo(FCEnsure fcEnsure, String authorization, Map<String, Object> params) {
        // 生成计划状态--同时进入企业信息 状态为“定制中”
        fcEnsure.setEnsureState(EnsureStateEnum.TOHRCUSTOMIZATION.getCode());
        fcEnsure.setEnsureName((String) params.get("ensureName"));//日常计划名称  对应表中  福利名称
        fcEnsure.setPremCalType("0");//保费计算方式  0--表定费率    默认表定费率
        //如果是安颐无忧年金，则生效日期为投保日期加一天
        if ("14110".equals(params.get("planTypeCode"))) {
            String startAppntDate = String.valueOf(params.get("startAppntDate"));//基础保单投保日期
            //获取安颐无忧年金的基础单生效日期
            Date nowDate = DateTimeUtil.strToDate(startAppntDate, "yyyy-MM-dd");
            Calendar c = Calendar.getInstance();
            c.setTime(nowDate);
            // 日期加1天
            c.set(Calendar.DATE, c.get(Calendar.DATE) + 1);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String cvaliDate = dateFormat.format(c.getTime());
            //基础保单投保日期  对应表中  投保开放日期
            fcEnsure.setStartAppntDate(startAppntDate);
            fcEnsure.setCvaliDate(cvaliDate);
        } else {
            //基础保单投保日期  对应表中  投保开放日期
            fcEnsure.setStartAppntDate((String) params.get("startAppntDate"));
            fcEnsure.setCvaliDate((String) params.get("cvaliDate"));//基础保单生效日期  对应表中 保单生效日
        }
        fcEnsure.setEndAppntDate((String) params.get("endAppntDate"));//寄出保单终止日期 对应表中 保单结束日期
        fcEnsure.setMakeDate(DateTimeUtil.getCurrentDateTime());
        fcEnsure.setMakeTime(DateTimeUtil.getCurrentTime());
        fcEnsure.setModifyDate(DateTimeUtil.getCurrentDateTime());
        fcEnsure.setModifyTime(DateTimeUtil.getCurrentTime());
        fcEnsure.setPlanType("2");
        fcEnsure.setAppDate(DateTimeUtil.getCurrentDateTime());
        fcEnsure.setAuditType("0");
        fcEnsure.setPolicyState("1");
//        fcEnsure.setAppntYear("2020");
        fcEnsure.setAppntYear(DateTimeUtil.getCurrentYear());
        fcEnsure.setEnsureType("0");
        fcEnsure.setPolicyEndDate("9999-12-31");//保单截止日
        GlobalInput globalInput = userService.getSession(authorization);
        fcEnsure.setOperator(globalInput.getUserNo());
//        fcEnsure.setChangeType("4");
    }

    private void setEnsureInfo1(FCEnsure fcEnsure, String authorization, Map<String, Object> params) {
        // 生成计划状态--同时进入企业信息 状态为“定制中”
        fcEnsure.setEnsureName((String) params.get("ensureName"));//日常计划名称  对应表中  福利名称
        fcEnsure.setPremCalType("0");//保费计算方式  0--表定费率    默认表定费率
        fcEnsure.setStartAppntDate((String) params.get("cvaliDate"));//基础保单投保日期  对应表中  投保开放日期
        fcEnsure.setCvaliDate((String) params.get("startAppntDate"));//基础保单生效日期  对应表中 保单生效日
        fcEnsure.setEndAppntDate((String) params.get("endAppntDate"));//寄出保单终止日期 对应表中 保单结束日期
        fcEnsure.setMakeDate(DateTimeUtil.getCurrentDateTime());
        fcEnsure.setMakeTime(DateTimeUtil.getCurrentTime());
        fcEnsure.setModifyDate(DateTimeUtil.getCurrentDateTime());
        fcEnsure.setModifyTime(DateTimeUtil.getCurrentTime());
        fcEnsure.setPlanType("2");
        fcEnsure.setAppDate(DateTimeUtil.getCurrentDateTime());
        fcEnsure.setAuditType("0");
        fcEnsure.setPolicyState("1");
        fcEnsure.setAppntYear(DateTimeUtil.getCurrentYear());
        fcEnsure.setEnsureType("0");
        fcEnsure.setPolicyEndDate("9999-12-31");//保单截止日
        GlobalInput globalInput = userService.getSession(authorization);
        fcEnsure.setOperator(globalInput.getUserNo());
//     fcEnsure.setChangeType("4");
    }

    private void setEnsureInfo11(FCEnsure fcEnsure, String authorization, Map<String, Object> params) {
        // 生成计划状态--同时进入企业信息 状态为“定制中” 对于之前的险种
        //通过福利编码查询它的险种编码，对于14110险种他的下一步福利的状态改为待HR定制
        List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectListBydailyPlanCode(fcEnsure.getEnsureCode());
        FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo = fcDailyInsureRiskDetailInfos.get(0);
        if ("14110".equals(fcDailyInsureRiskDetailInfo.getRiskCode())) {
            if (EnsureStateEnum.TOHRCUSTOMIZATION.getCode().equals(fcEnsure.getEnsureState())) {
                fcEnsure.setEnsureState(EnsureStateEnum.HRCUSTOMIZATIONING.getCode());
            } else if (EnsureStateEnum.HRRETURNCUSTOMBACKGROUND.getCode().equals(fcEnsure.getEnsureState())) {
                fcEnsure.setEnsureState(EnsureStateEnum.TOHRCUSTOMIZATION.getCode());
            } else if (EnsureStateEnum.ADMINISTRATORRETURNCUSTOMBACKGROUND.getCode()
                    .equals(fcEnsure.getEnsureState())) {
                fcEnsure.setEnsureState(EnsureStateEnum.TOHRCUSTOMIZATION.getCode());
            }
        }
        fcEnsure.setEnsureName((String) params.get("ensureName"));//日常计划名称  对应表中  福利名称
        fcEnsure.setPremCalType("0");//保费计算方式  0--表定费率    默认表定费率
        //如果是安颐无忧年金，则生效日期为投保日期加一天
        if ("14110".equals(params.get("planTypeCode"))) {
            String startAppntDate = String.valueOf(params.get("startAppntDate"));//基础保单投保日期
            //获取安颐无忧年金的基础单生效日期
            Date nowDate = DateTimeUtil.strToDate(startAppntDate, "yyyy-MM-dd");
            Calendar c = Calendar.getInstance();
            c.setTime(nowDate);
            // 日期加1天
            c.set(Calendar.DATE, c.get(Calendar.DATE) + 1);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String cvaliDate = dateFormat.format(c.getTime());
            //基础保单投保日期  对应表中  投保开放日期
            fcEnsure.setStartAppntDate(startAppntDate);
            fcEnsure.setCvaliDate(cvaliDate);
        } else {
            //基础保单投保日期  对应表中  投保开放日期
            fcEnsure.setStartAppntDate((String) params.get("startAppntDate"));
            fcEnsure.setCvaliDate((String) params.get("cvaliDate"));//基础保单生效日期  对应表中 保单生效日
        }
        fcEnsure.setEndAppntDate((String) params.get("endAppntDate"));//寄出保单终止日期 对应表中 保单结束日期
        fcEnsure.setMakeDate(DateTimeUtil.getCurrentDateTime());
        fcEnsure.setMakeTime(DateTimeUtil.getCurrentTime());
        fcEnsure.setModifyDate(DateTimeUtil.getCurrentDateTime());
        fcEnsure.setModifyTime(DateTimeUtil.getCurrentTime());
        fcEnsure.setPlanType("2");
        fcEnsure.setAppDate(DateTimeUtil.getCurrentDateTime());
        fcEnsure.setAuditType("0");
//        fcEnsure.setPolicyState("1");
        fcEnsure.setAppntYear(DateTimeUtil.getCurrentYear());
        fcEnsure.setEnsureType("0");
        fcEnsure.setPolicyEndDate("9999-12-31");//保单截止日
        GlobalInput globalInput = userService.getSession(authorization);
        fcEnsure.setOperator(globalInput.getUserNo());
//     fcEnsure.setChangeType("4");
    }

    private void setDailyInsureRiskDetailInfo(String authorization, Map<String, Object> params, FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo) {
        GlobalInput globalInput = userService.getSession(authorization);
        fcDailyInsureRiskDetailInfo.setRiskCode(String.valueOf(params.get("planTypeCode")));
        fcDailyInsureRiskDetailInfo.setOperatorCom("");
        fcDailyInsureRiskDetailInfo.setOperator(globalInput.getUserNo());
        fcDailyInsureRiskDetailInfo = CommonUtil.initObject(fcDailyInsureRiskDetailInfo, "INSERT");
    }

    /**
     * 企业信息保存
     *
     * @param authorization
     * @param params
     * @return
     */
    public String saveGrpInfo(String authorization, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(authorization);
        try {
            String grpNo = params.get("grpNo");
            String ensureCode = params.get("ensureCode");
            String povaliDity = params.get("povaliDity");
            String clientNo = params.get("clientNo");

            String birthDay = params.get("birthDay");
            String nativeplace = params.get("nativeplace");
            String iDType = params.get("iDType");
            String iDNo = params.get("iDNo");
            String sex = params.get("sex");
            String name = params.get("name").trim().replaceAll(" +", " ");
            params.put("name", name);
            String departMent = params.get("departMent");
            String email = params.get("eMail");
            String mobilePhone = params.get("mobilePhone");

            // 校验手机号
            if (!CheckUtils.checkMobilePhone(mobilePhone)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "联系人电话格式错误，请检查");
                return JSON.toJSONString(resultMap);
            }
            // 校验邮箱
            if (!CheckUtils.checkEmail(email)) {
                return JSON.toJSONString(ResultUtil.error("联系人邮箱录入有误，请检查！"));
            }

            String Phone = "";//业务员手机号
            if (!StringUtil.isEmpty(iDType) && !StringUtil.isEmpty(name)) {
                if (iDType.equals("1")) {
                    String trim = name.trim();
                    name = trim.replaceAll(" +", " ");
                }
            }
            List<EvaluationCustomer> customerList = Arrays.asList(EvaluationCustomer.builder()
                    .name(name)
                    .idType(CoreIdType.getNameByCoreId(iDType).name())
                    .idNo(iDNo)
                    .gender(GenderType.getGenderByCoreId(sex).name())
                    .birthday(birthDay)
                    .nationality(nativeplace)
                    .businessNo(ensureCode)
                    .build());

            List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
            //校验二要素
            String failVerifies = addressCheckService.checkIdCard(verifies);
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(failVerifies)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", failVerifies);
                return JSON.toJSONString(resultMap);
            }
            FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpContact(iDNo);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            FcEnsureContact fcEnsureContact1 = fcEnsureContactMapper.selectByPrimaryKey(ensureCode);
            if (fcEnsureContact1 == null) {
                //如果更改了 联系人的信息，在这里需要保存操作的。
                fcEnsure.setClientNo(clientNo);
                fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
                FcEnsureContact fcEnsureContact = new FcEnsureContact();
                fcEnsureContact.setEnsureCode(ensureCode);
                fcEnsureContact.setName(name);
                fcEnsureContact.setSex(sex);
                fcEnsureContact.setDepartment(departMent);//所在部门
                fcEnsureContact.setEmail(email);
                fcEnsureContact.setMobilePhone(mobilePhone);
                fcEnsureContact.setBirthDay(birthDay);
                fcEnsureContact.setNativeplace(nativeplace);
                fcEnsureContact.setIdType(iDType);
                fcEnsureContact.setIdNo(iDNo);
                fcEnsureContact.setIdTypeEndDate(povaliDity);//证件有效期
                fcEnsureContact.setIdTypeStartDate(fcGrpContact.getIdTypeStartDate());//证件有效期
                fcEnsureContact.setOperator(globalInput.getUserNo());
                fcEnsureContact = CommonUtil.initObject(fcEnsureContact, "INSERT");
                fcEnsureContactMapper.insertSelective(fcEnsureContact);

            } else {
                fcEnsure.setClientNo(clientNo);
                fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);

                //不是新增了。是修改
                FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(ensureCode);
                fcEnsureContact.setEnsureCode(ensureCode);
                fcEnsureContact.setName(name);
                fcEnsureContact.setSex(sex);
                fcEnsureContact.setDepartment(departMent);//所在部门
                fcEnsureContact.setEmail(email);
                fcEnsureContact.setMobilePhone(mobilePhone);
                fcEnsureContact.setBirthDay(birthDay);
                fcEnsureContact.setNativeplace(nativeplace);
                fcEnsureContact.setIdType(iDType);
                fcEnsureContact.setIdNo(iDNo);
                fcEnsureContact.setIdTypeEndDate(povaliDity);//证件有效期
                fcEnsureContact.setIdTypeStartDate(fcGrpContact.getIdTypeStartDate());//证件有效期
                fcEnsureContact.setOperator(globalInput.getUserNo());
                fcEnsureContact.setModifyDate(DateTimeUtil.getCurrentDateTime());
                fcEnsureContact.setModifyTime(DateTimeUtil.getCurrentTime());
                fcEnsureContactMapper.updateByPrimaryKeySelective(fcEnsureContact);
                Map<String, String> mapInfo = new HashMap<>();
                mapInfo.put("grpNo", grpNo);
                mapInfo.put("clientno", clientNo);
                fcGrpInfoMapper.updateclientNoBygrpNo(mapInfo);
            }
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("configNo", "011");
            List<FCEnsureConfig> fcEnsureConfigs = fcEnsureConfigMapper.selectcountByensureCode011(mapInfo);
            //*******************************************************************
            //获取 业务员 手机号，用于用户管理申请发票使用
            FDAgentInfo fdagentinfo = fdAgentInfoMapper.getAgentByEnsureCode(ensureCode);
            if (fdagentinfo != null) {
                if (StringUtils.isNotBlank(fdagentinfo.getPhone()) || StringUtils.isNotBlank(fdagentinfo.getMobile())) {
                    Phone = fdagentinfo.getMobile().equals("") ? fdagentinfo.getPhone() : fdagentinfo.getMobile();
                } else {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "代理人联系方式请手动维护！");
                    return JSON.toJSONString(resultMap);
                }
            }
            //*******************************************************************
            if (fcEnsureConfigs.size() > 0) {
                Map<String, Object> configNoMap = new HashMap<>();
//                configNoMap.put("grpNo", grpNo);
                configNoMap.put("ensureCode", ensureCode);
                configNoMap.put("configNo", "011");
                FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(configNoMap);
                fcEnsureConfig.setConfigValue(Phone);//财务联系方式 011
                fcEnsureConfig.setOperator(globalInput.getUserNo());
                fcEnsureConfig.setEnsureCode(ensureCode);
                fcEnsureConfig.setGrpNo(grpNo);
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "UPDATE");
                fcEnsureConfigMapper.updateByPrimaryKey(fcEnsureConfig);
            } else {
                FCEnsureConfig FinancialContact = new FCEnsureConfig(); //财务联系方式 011
                FinancialContact.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));//福利配置流水号
                FinancialContact.setConfigNo("011");//财务联系方式 011
                FinancialContact.setEnsureCode(ensureCode);
                FinancialContact.setGrpNo(grpNo);
                FinancialContact.setConfigValue(Phone);//财务联系方式 011
                FinancialContact.setOperator(globalInput.getUserNo());
                FinancialContact = (FCEnsureConfig) CommonUtil.initObject(FinancialContact, "INSERT");
                fcEnsureConfigMapper.insertSelective(FinancialContact);
            }
            //如果都一样说明是保存的最新的
            Log.info("企业信息彻底保存成功。。");
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "企业信息保存成功！");
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "企业信息保存失败，请联系运维人员");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 检查企业信息是否已经保存
     *
     * @param authorization
     * @param params
     * @return
     */
    public String checkgrpInfoisSave(String authorization, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();

        String ensureCode = params.get("ensureCode");
        ArrayList list = new ArrayList();
        try {
            FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(ensureCode);
            if (fcEnsureContact == null) {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "根据ensureCode=" + ensureCode + "查询不到这个数据！");
                Log.info("企业没有进行保存");
                return JSON.toJSONString(resultMap);
            }
            if (fcEnsureContact.getName().equals(params.get("name")) && fcEnsureContact.getSex().equals(params.get("sex")) && fcEnsureContact.getDepartment().equals(params.get("departMent")) && fcEnsureContact.getEmail().equals(params.get("eMail")) && fcEnsureContact.getMobilePhone().equals(params.get("mobilePhone")) && fcEnsureContact.getBirthDay().equals(params.get("birthDay")) && fcEnsureContact.getNativeplace().equals(params.get("nativeplace")) && fcEnsureContact.getIdType().equals(params.get("iDType")) && fcEnsureContact.getIdNo().equals(params.get("iDNo")) && fcEnsureContact.getIdTypeEndDate().equals(params.get("povaliDity"))) {
                Log.info("企业是保存的。。。。。");
                //可以先把更改被保险人界面的 其中数据：计划名称查询出来返回去。这是一个下拉框，只有计划状态为0 的时候才有显示
                List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate(ensureCode);
                for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos) {
                    Map<String, Object> dataMap = new HashMap<>();
                    String codeKey = fcDailyInsureRiskDetailInfo.getPlanCode();
                    String codeName = fdCodeMapper.selectNameByCode("planCode", codeKey);
                    dataMap.put("CodeName", codeName == null ? "" : codeName);
                    dataMap.put("CodeKey", codeKey);
                    list.add(dataMap);
                }
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
                String startAppntDate = fcEnsure.getStartAppntDate();//基础单投保日期
                //如果都一样说明是保存的最新的
                resultMap.put("startAppntDate", startAppntDate);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "企业信息已经保存");
                resultMap.put("planCode", list);
                Log.info("企业信息已经保存");
            } else {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "只有保存企业信息后才可添加被保险人！");
                Log.info("企业没有进行保存");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "校验企业信息是否保存过程发生了错误！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 保存被保险人信息和 投保信息---保存和修改   1-新增  2-修改
     *
     * @param authorization
     * @param params
     * @return
     */
    @Transactional
    public String saveissuredInfo(String authorization, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(authorization);
        try {
            String num = params.get("num");
            String planCode = params.get("dutyCode");
            String ensureCode = params.get("ensureCode");
            String IDNo = params.get("iDNo");
            String oldiDNo = params.get("oldiDNo");//证件号旧的
            String grpNo = params.get("grpNo");//表单数据 外
            String grpOrderNo = params.get("GrpOrderNo");//团体订单号
            String department = params.get("department");//部门
            String sex = params.get("sex");//部门
            String province = params.get("province");//省
            String city = params.get("city");//市
            String county = params.get("area");//县
            String detaileAdddress = params.get("regAddress");//详细地址
            String zipCode = params.get("ZipCode");//邮编
            String iDType = params.get("iDType");
            String birthDay = params.get("birthDay");
            String idTypeEndDay = params.get("idTypeEndDay");
            String occupationCode = params.get("occupationCode");
            String nativeplace = params.get("country");
            String mobilePhone = params.get("mobilePhone");
            String email = params.get("email1");
            String name = params.get("name").trim().replaceAll(" +", " ");
            //新传输证件影像照1
            String idImage1 = params.get("idImage1");
            String idImage2 = params.get("idImage2");
            //新传输证件影像照2
            params.put("name", name);
            //2. 校验 员工及家属 职业 相关
            Map<String, String> mapss = new HashMap<>();
            mapss.put("sign", "2");//1：员工 2：家属
            mapss.put("idType", iDType);//证件类型
            mapss.put("idNo", IDNo);//证件号
            mapss.put("birthDay", birthDay);//出生日期
            mapss.put("sex", sex);//性别
            mapss.put("nativeplace", nativeplace);//国籍
            mapss.put("idTypeEndDate", idTypeEndDay);//证件有效期
            mapss.put("occupationCode", occupationCode);//职业代码
            String resultMsg = CheckUtils.checkSinglePeople(mapss);
            if (StringUtils.isNotBlank(resultMsg)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", resultMsg);
                return JSON.toJSONString(resultMap);
            }
            // 校验电话
            if (!CheckUtils.checkMobilePhone(mobilePhone)) {
                return JSON.toJSONString(ResultUtil.error("手机号格式有误！"));
            }
            // 校验邮箱
            if (StringUtils.isNotBlank(email) && !CheckUtils.checkEmail(email)) {
                return JSON.toJSONString(ResultUtil.error("邮箱录入有误，请检查！"));
            }
            //********判断当前选的计划是否有效*********************
            // TODO 判断当前选的计划是否有效时因为前台就没有传输计划编码，计划定制的时候后台写死为1存储到数据库中，故判断时，暂时应该也将计划编码写死为ID4110
            if ("".equals(planCode) || null == planCode) {
                planCode = "ID4110";
            }
            if (ensureCode != null) {
                FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndEnsureCode(planCode, ensureCode);
                if (fcDailyInsureRiskDetailInfo != null) {
                    if (fcDailyInsureRiskDetailInfo.getPlanState().equals("1")) {
                        resultMap.put("code", "500");
                        resultMap.put("success", false);
                        resultMap.put("message", "计划是无效的，请及时更改！");
                        return JSON.toJSONString(resultMap);
                    }
                } else {
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", "数据库中没有 有效的计划！");
                    return JSON.toJSONString(resultMap);
                }
            } else {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "计划编码不能为空！");
                return JSON.toJSONString(resultMap);
            }

            String errorMsg = "";
            List<EvaluationCustomer> customerList = Arrays.asList(EvaluationCustomer.builder()
                    .name(name)
                    .idType(CoreIdType.getNameByCoreId(iDType).name())
                    .idNo(IDNo)
                    .gender(GenderType.getGenderByCoreId(sex).name())
                    .birthday(birthDay)
                    .nationality(nativeplace)
                    .businessNo(ensureCode)
                    .build());

            List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
            //校验二要素
            String failVerifies = addressCheckService.checkIdCard(verifies);
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(failVerifies)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", failVerifies);
                return JSON.toJSONString(resultMap);
            }

            //个人客户号，预核保用
            String corePerNo = "";

            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectByPrimaryKey(grpOrderNo);
            if ("1".equals(num)) {      //新增
                String personId = maxNoService.createMaxNo("PersonId", "", 20);//个人编码
                String perNo = maxNoService.createMaxNo("PerNo", "", 20);//个人编码
                String registDayNo = maxNoService.createMaxNo("RegDay", "", 20);
                String orderNo = maxNoService.createMaxNo("OrderNo", DateTimeUtil.getTimeString().substring(0, 4), 8);//订单号
                String orderItemNo = maxNoService.createMaxNo("OrderItemNo", "", 20);//子订单号
                String orderItemDetailNo = maxNoService.createMaxNo("OrderItemDetailNo", "", 20);//子订单详情编号
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
                //获取代理人信息
                List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
                FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);
                String cvaliDate = fcEnsure.getCvaliDate();//保单生效日
                //等于 1  的时候 是新增保存。   先判断该企业是否增加过基础单,返回的是查询该员工在企业里面的数量
                //这时说明我们新增的员工，没有基础单存在，所有所有的 员工表和 订单表都需要存
                //首先保存基本信息（保存fcstafffamilyrela（员工家属信息关联表）、fcperinfo（个人客户信息表）、fcperson（个人信息表)
                FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
                FCPerInfo fcPerInfo = new FCPerInfo();
                FCPerson fcPerson = new FCPerson();
                FCPerRegistDay fcPerRegistDay = new FCPerRegistDay();
                FCOrder fcOrder = new FCOrder();
                FCOrderItem fcOrderItem = new FCOrderItem();
                FCOrderItemDetail fcOrderItemDetail = new FCOrderItemDetail();
                FCOrderInsured fcOrderInsured = new FCOrderInsured();
                Integer integer = fcOrderInsuredMapper.selectPeopleNum(params.get("iDNo"), grpOrderNo);
                if (integer > 0) {
                    //说明该用户已经被添加了。此时提示不能添加第二次
                    Map<String, Object> error = ResultUtil.error("同一个人不能添加两次");
                    return JSON.toJSONString(error);
                }
                //判断被保人是否可以在 HR 员工管理查询到 该员工。如果能查询到则是修改。
                Map<String, String> paramsstaff = new HashMap<>();
                List<Map<String, String>> fcStaffInfos = new ArrayList<>();
                paramsstaff.put("grpNo", grpNo);
                paramsstaff.put("name", name);
                paramsstaff.put("iDType", iDType);
                paramsstaff.put("iDNo", IDNo);
                fcStaffInfos = fcPerInfoTempMapper.selectByGrpNoAndParams(paramsstaff);
                //查询临时表中的职级
                String levelCode = fcPerInfoTempMapper.selectLevelCodeByEnsureCodeAndIdNo(fcEnsure.getEnsureCode(), IDNo);
                if (fcStaffInfos != null && fcStaffInfos.size() > 0) {
                    num = "2";
                    perNo = fcStaffInfos.get(0).get("perNo");
                    FCStaffFamilyRela fcStaffFamilyRela1 = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
                    personId = fcStaffFamilyRela1.getPersonID();
                }
                fcPerRegistDay.setRegistDayNo(registDayNo);//注册期流水号
                fcPerRegistDay.setEnsureCode(ensureCode);
                fcPerRegistDay.setPerNo(perNo);
                fcPerRegistDay.setOpenDay(fcEnsure.getStartAppntDate());//注册期开放日
                fcPerRegistDay.setCloseDay(fcEnsure.getEndAppntDate());//注册期结束日
                fcPerRegistDay.setPersonType("1");
                fcPerRegistDay.setIsValidy("1");
                fcPerRegistDay.setValidydate(cvaliDate);//生效日
                fcPerRegistDay.setValidydateType("0");
                fcPerRegistDay.setRegistDayToManNo(perNo);
                fcPerRegistDay.setOperator(globalInput.getUserNo());
                fcPerRegistDay.setLevelCode(levelCode);//职级
                fcPerRegistDay = CommonUtil.initObject(fcPerRegistDay, "INSERT");
                fcPerRegistDayMapper.insertSelective(fcPerRegistDay);

                fcPerson.setPersonID(personId);
                fcPerson.setName(params.get("name"));
                fcPerson.setSex(params.get("sex"));
                fcPerson.setBirthDate((params.get("birthDay")));
                fcPerson.setIDType(params.get("iDType"));
                fcPerson.setIDNo(params.get("iDNo"));
                fcPerson.setIdTypeEndDate(params.get("idTypeEndDay"));
                fcPerson.setNativeplace(params.get("country"));
                fcPerson.setMobilePhone(params.get("mobilePhone"));
                fcPerson.setPhone(params.get("mobilePhone"));
                fcPerson.setEMail(params.get("email1"));
//                String occupationCode = params.get("occupationCode");
                fcPerson.setOccupationCode(occupationCode);//职业代码  应该对应职业
                //查询职业类别
                String occupationType = fdCodeMapper.selectOccupationType(occupationCode);
                fcPerson.setOccupationType(occupationType);//职业类别  应该对应职业详情
                fcPerson.setOperator(globalInput.getUserNo());
                fcPerson.setProvince(province);
                fcPerson.setCity(city);
                fcPerson.setCounty(county);
                fcPerson.setDetaileAddress(detaileAdddress);
                fcPerson.setZipCode(zipCode);
                fcPerson.setNativeplaceName(params.get("country"));
                fcPerInfo.setPerNo(perNo);//客户号
                fcPerInfo.setGrpNo(grpNo);
                fcPerInfo.setName(params.get("name"));
                fcPerInfo.setIDType(params.get("iDType"));
                fcPerInfo.setIdTypeEndDate(params.get("idTypeEndDay"));
                fcPerInfo.setIDNo(params.get("iDNo"));
                fcPerInfo.setSex(params.get("sex"));
                fcPerInfo.setBirthDay(params.get("birthDay"));
                fcPerInfo.setNativeplace(params.get("country"));
                fcPerInfo.setMobilePhone(params.get("mobilePhone"));
                fcPerInfo.setPhone(params.get("mobilePhone"));
                fcPerInfo.setEmail(params.get("email1"));
                fcPerInfo.setOccupationCode(occupationCode);
                fcPerInfo.setOccupationType(occupationType);
                fcPerInfo.setDepartment(department);
                fcPerInfo.setOperator(globalInput.getUserNo());
                fcPerInfo.setZipCode(zipCode);
                fcPerInfo.setProvince(province);
                fcPerInfo.setCity(city);
                fcPerInfo.setCounty(county);
                fcPerInfo.setDetaileAddress(detaileAdddress);
                //新增证件影像照1
                fcPerInfo.setIdImage1(idImage1);
                //新增证件影像照2
                fcPerInfo.setIdImage2(idImage2);
                corePerNo = fcPerInfo.getPerNo();

                fcStaffFamilyRela.setPerNo(perNo);
                fcStaffFamilyRela.setPersonID(personId);
                fcStaffFamilyRela.setRelation("0");
                fcStaffFamilyRela.setRelationProve("本人");
                fcStaffFamilyRela.setOperator(globalInput.getUserNo());
                if ("1".equals(num)) {
                    fcPerson = (FCPerson) CommonUtil.initObject(fcPerson, "INSERT");
                    fcPersonMapper.insertSelective(fcPerson);

                    fcPerInfo = (FCPerInfo) CommonUtil.initObject(fcPerInfo, "INSERT");
                    fcPerInfoMapper.insertSelective(fcPerInfo);

                    fcStaffFamilyRela = (FCStaffFamilyRela) CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
                    fcStaffFamilyRelaMapper.insertSelective(fcStaffFamilyRela);
                } else if ("2".equals(num)) {
                    fcPerson = (FCPerson) CommonUtil.initObject(fcPerson, "UPDATE");
                    fcPersonMapper.updateByPrimaryKeySelective(fcPerson);

                    fcPerInfo = (FCPerInfo) CommonUtil.initObject(fcPerInfo, "UPDATE");
                    fcPerInfoMapper.updateByPrimaryKeySelective(fcPerInfo);

                    fcStaffFamilyRela = (FCStaffFamilyRela) CommonUtil.initObject(fcStaffFamilyRela, "UPDATE");
                    fcStaffFamilyRelaMapper.updateByPrimaryKeySelective(fcStaffFamilyRela);
                }
                //再次保存投保信息
                //点击添加保存之后。上面的 被保险人的个人信息保存三个表成功。    接下来保存下面的投保信息，
                // （包含订单、子订单fcorderitem、子订单要素详情fcorderitemdetail（产品编码存险种编码，产品责任编码存计划编码；新增保额、保险期间、交费频次等字段））及被保人表fcorderinsured

                fcOrder.setOrderNo(orderNo);//订单号
                fcOrder.setOrderStatus("01");//订单状态: 01-待提交核心  02-已提交核心
                fcOrder.setOrderType("03");//订单类型:01固定套餐  02年度福利  03日常福利
                fcOrder.setGrpOrderNo(grpOrderNo);//团体订单号
                fcOrder.setGrpNo(grpNo);//企业客户号
                fcOrder.setPerNo(perNo);//个人客户号
                fcOrder.setOrderSource("01");
                fcOrder.setCommitDate(fcEnsure.getStartAppntDate());
                fcOrder.setEffectDate(fcEnsure.getCvaliDate());
                fcOrder.setClientNo(fcEnsure.getClientNo());
                fcOrder.setOrderSign("1");//订单标识  1-基础单生成的订单  2-移动端生成的订单
                fcOrder.setOperator(globalInput.getUserNo());//操作员
                fcOrder = (FCOrder) CommonUtil.initObject(fcOrder, "INSERT");
                fcOrderMapper.insertSelective(fcOrder);

                fcOrderItem.setOrderItemNo(orderItemNo);//子订单号
                fcOrderItem.setOrderNo(orderNo);
                String rule = "99" + fdAgentInfo.getManageCom().substring(2, 4) + DateTimeUtil.getCurrentYear().substring(2, 4);
                String contNo = maxNoService.createMaxNo("ContNo" + DateTimeUtil.getCurrentYear(), rule, 9);
                contNo = contNo + "8";
                fcOrderItem.setContNo(contNo);
                fcOrderItem.setOrderItemDetailNo(orderItemDetailNo);
                fcOrderItem.setOperator(globalInput.getUserNo());
                fcOrderItem.setGrpPrem(Double.parseDouble(params.get("dailyPrem")));//保费
                fcOrderItem.setSelfPrem(0.00);
                fcOrderItem = (FCOrderItem) CommonUtil.initObject(fcOrderItem, "INSERT");
                fcOrderItemMapper.insertSelective(fcOrderItem);
                // 子订单详情编号
                fcOrderItemDetail.setOrderItemDetailNo(orderItemDetailNo);
                // 产品编码 存的是险种编码
                fcOrderItemDetail.setProductCode("14110");
                // TODO 因为安颐无忧年金业务未给出具体的计划信息，故前台无法传输选择的计划编码，此时先将其责任编码设置为ID4110
                if ("".equals(params.get("dutyCode")) || null == params.get("dutyCode")) {
                    String dutyCode = "";
                    dutyCode = "ID4110";
                    // 产品责任编码 存 计划编码
                    fcOrderItemDetail.setDutyCode(dutyCode);
                } else {
                    fcOrderItemDetail.setDutyCode(params.get("dutyCode"));// 产品责任编码 存 计划编码
                }
                //新增字段   保额、保险期间、交费频次等字段
                // 保额
                fcOrderItemDetail.setInsuredAmount(Double.parseDouble(params.get("insuredAmount")));
                // 保险期间
                fcOrderItemDetail.setInsurePeriod(params.get("insurePeriod"));
                // 交费频次
                fcOrderItemDetail.setPayFrequency(params.get("payFrequency"));
                // 缴费期间
                fcOrderItemDetail.setPayPeriod(params.get("payPeriod"));
                fcOrderItemDetail.setProductEleCode("001");
                fcOrderItemDetail.setOperator(globalInput.getUserNo());
                fcOrderItemDetail = CommonUtil.initObject(fcOrderItemDetail, "INSERT");
                fcOrderItemDetailMapper.insertAll(fcOrderItemDetail);

                fcOrderInsured.setOrderItemNo(orderItemNo);
                fcOrderInsured.setOrderNo(orderNo);
                fcOrderInsured.setPersonID(personId);
                fcOrderInsured.setName(params.get("name"));
                fcOrderInsured.setSex(params.get("sex"));
                fcOrderInsured.setBirthDay(params.get("birthDay"));
                fcOrderInsured.setIDType(params.get("iDType"));
                fcOrderInsured.setIDNo(params.get("iDNo"));
                fcOrderInsured.setMobilePhone(params.get("mobilePhone"));
                fcOrderInsured.setDepartment(department);//部门
                fcOrderInsured.setOccupationCode(occupationCode);
                fcOrderInsured.setOccupationType(occupationType);
                fcOrderInsured.setEMail(params.get("email1"));
                fcOrderInsured.setOperator(globalInput.getUserNo());
                fcOrderInsured.setProvince(province);
                fcOrderInsured.setCity(city);
                fcOrderInsured.setCounty(county);
                fcOrderInsured.setDetaileAddress(detaileAdddress);
                fcOrderInsured.setZipCode(zipCode);

                fcOrderInsured.setMainEMail(params.get("email1"));
                fcOrderInsured.setMainDetaileAddress(detaileAdddress);
                fcOrderInsured.setMainMobilePhone(params.get("mobilePhone"));
                fcOrderInsured.setMainZipCode(zipCode);
                fcOrderInsured.setMainProvince(province);
                fcOrderInsured.setMainCity(city);
                fcOrderInsured.setMainCounty(county);

                fcOrderInsured = CommonUtil.initObject(fcOrderInsured, "INSERT");
                fcOrderInsured.setGrpOrderNo(grpOrderNo);
                fcOrderInsuredMapper.insertSelective(fcOrderInsured);

                FCOrderBnfRela fcOrderBnfRela = new FCOrderBnfRela();
                String bnfNo = maxNoService.createMaxNo("BnfNo", "", 20);
                fcOrderBnfRela.setOrderNo(fcOrder.getOrderNo());
                fcOrderBnfRela.setOrderItemNo(orderItemNo);
                fcOrderBnfRela.setBnfNo(bnfNo);
                fcOrderBnfRela.setBnfType("0");
                fcOrderBnfRela.setRelation("06");
                fcOrderBnfRela.setOperator(globalInput.getUserNo());
                fcOrderBnfRela = CommonUtil.initObject(fcOrderBnfRela, "INSERT");
                fcOrderBnfRelaMapper.insertSelective(fcOrderBnfRela);
            } else {        //修改
                //等于2 的时候是  就是的走的修改被保险人 的流程
                /*说明该企业 的这个员工，之前也注册过基础单。这个时候修改个人信息的时候，就不能修改跟费率有关的参数即（出生日期和性别）
                      因为这两个字段在个人表中，之前的费率是根据这两个字段计算的。如果我们修改了这两个，那么之前的就对应不上了。*/
                if (!oldiDNo.equals(IDNo)) {
                    //说明修改了 被保险人的证件号。此时需要校验这个新的证件号是否 已经存在
                    Integer integer = fcOrderInsuredMapper.selectPeopleNum(IDNo, grpOrderNo);
                    if (integer > 0) {
                        //说明该用户已经被添加了。此时提示不能添加第二次
                        Map<String, Object> error = ResultUtil.error("同一个人不能添加两次");
                        return JSON.toJSONString(error);
                    }
                }
                FCPerInfo fcPerInfo = fcPerInfoMapper.selectbyIdNoandGrpNo(oldiDNo, grpNo);
                String perNo = fcPerInfo.getPerNo();
                Map<String, Object> map = new HashMap<>();
                map.put("grpOrderNo", grpOrderNo);
                map.put("perNo", perNo);
                FCOrder fcOrder = fcOrderMapper.selectOrderByperNo(map);
                FCPerson fcPerson = fcPersonMapper.selectFcpersonByPerNo(perNo);
                String personID = fcPerson.getPersonID();
                //这时说明我们新增的员工，没有基础单存在，所有的 员工表和 订单表都需要修改
                fcPerson.setName(params.get("name"));
                fcPerson.setIDType(params.get("iDType"));
                fcPerson.setIDNo(params.get("iDNo"));
                fcPerson.setBirthDate(params.get("birthDay"));
                fcPerson.setSex(params.get("sex"));
                fcPerson.setIdTypeEndDate(params.get("idTypeEndDay"));
                fcPerson.setNativeplace(params.get("country"));
                fcPerson.setMobilePhone(params.get("mobilePhone"));
                fcPerson.setEMail(params.get("email1"));
//                String occupationCode = params.get("occupationCode");
                fcPerson.setOccupationCode(occupationCode);//职业代码  应该对应职业
                //查询职业类别
                String occupationType = fdCodeMapper.selectOccupationType(occupationCode);
                fcPerson.setOccupationType(occupationType);//职业类别  应该对应职业详情
                fcPerson.setProvince(province);
                fcPerson.setCity(city);
                fcPerson.setCounty(county);
                fcPerson.setDetaileAddress(detaileAdddress);
                fcPerson.setZipCode(zipCode);
                fcPerson = CommonUtil.initObject(fcPerson, "UPDATE");
                fcPersonMapper.updateByPersonId(fcPerson);


                fcPerInfo.setName(params.get("name"));
                fcPerInfo.setIDType(params.get("iDType"));
                fcPerInfo.setIdTypeEndDate(params.get("idTypeEndDay"));
                fcPerInfo.setIDNo(params.get("iDNo"));
                fcPerInfo.setNativeplace(params.get("country"));
                fcPerInfo.setMobilePhone(params.get("mobilePhone"));
                fcPerInfo.setEmail(params.get("email1"));
                fcPerInfo.setOccupationCode(occupationCode);
                fcPerInfo.setOccupationType(occupationType);
                fcPerInfo.setDepartment(params.get("department"));
                fcPerInfo.setBirthDay(params.get("birthDay"));
                fcPerInfo.setSex(params.get("sex"));
                fcPerInfo.setZipCode(zipCode);
                fcPerInfo.setProvince(province);
                fcPerInfo.setCity(city);
                fcPerInfo.setCounty(county);
                fcPerInfo.setDetaileAddress(detaileAdddress);
                //新增的证件影像照1
                fcPerInfo.setIdImage1(idImage1);
                //新增的证件影像照2
                fcPerInfo.setIdImage2(idImage2);
                fcPerInfo = CommonUtil.initObject(fcPerInfo, "UPDATE");
                fcPerInfoMapper.updateByPerNoOrIdNoSelective(fcPerInfo);
                corePerNo = fcPerInfo.getPerNo();

                String grpContNo = fcGrpOrder.getGrpContNo();//对应核心团单号
                String orderNo = fcOrder.getOrderNo();
                Map<String, String> map1 = new HashMap<>();
                map1.put("grpContNo", grpContNo);
                map1.put("orderNo", orderNo);
                FCOrderItem fcOrderItem = fcOrderItemMapper.selectBygrpContNoAndOrderNo(map1);
                fcOrderItem.setGrpPrem(Double.parseDouble(params.get("dailyPrem")));//保费
                fcOrderItem.setSelfPrem(0.00);
                fcOrderItem = (FCOrderItem) CommonUtil.initObject(fcOrderItem, "UPDATE");
                fcOrderItemMapper.updateByPrimaryKeySelective(fcOrderItem);

                String orderItemDetailNo = fcOrderItem.getOrderItemDetailNo();
                FCOrderItemDetail fcOrderItemDetail = fcOrderItemDetailMapper.selectByPrimaryKey_No(orderItemDetailNo);
                fcOrderItemDetail.setProductCode("14110");//产品编码  存的是险种编码
                fcOrderItemDetail.setDutyCode(params.get("dutyCode"));//产品责任编码 存 计划编码
                //新增字段   保额、保险期间、交费频次等字段
                fcOrderItemDetail.setInsuredAmount(Double.parseDouble(params.get("insuredAmount")));//保额
                fcOrderItemDetail.setInsurePeriod(params.get("insurePeriod"));//保险期间    1-保至85周岁  2-终身
                fcOrderItemDetail.setPayFrequency(params.get("payFrequency"));//交费次      1-趸交  2-年交
                fcOrderItemDetail.setPayPeriod(params.get("payPeriod"));// 缴费期间     1-一次性交清  2-5年交   3-10年交  4-20年交
                fcOrderItemDetail.setOperator(globalInput.getUserNo());
                fcOrderItemDetail = (FCOrderItemDetail) CommonUtil.initObject(fcOrderItemDetail, "UPDATE");
                fcOrderItemDetailMapper.updateByPrimaryKeySelective(fcOrderItemDetail);

                FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectOrderItemNo(grpOrderNo, orderNo, personID);
                fcOrderInsured.setName(params.get("name"));
                fcOrderInsured.setSex(params.get("sex"));
                fcOrderInsured.setBirthDay(params.get("birthDay"));
                fcOrderInsured.setIDType(params.get("iDType"));
                fcOrderInsured.setIDNo(params.get("iDNo"));
                fcOrderInsured.setMobilePhone(params.get("mobilePhone"));
                fcOrderInsured.setDepartment(department);//部门
                fcOrderInsured.setOccupationCode(occupationCode);
                fcOrderInsured.setOccupationType(occupationType);
                fcOrderInsured.setEMail(params.get("email1"));
                fcOrderInsured.setOperator(globalInput.getUserNo());
                fcOrderInsured = (FCOrderInsured) CommonUtil.initObject(fcOrderInsured, "INSERT");
                fcOrderInsured.setGrpOrderNo(grpOrderNo);
                fcOrderInsured.setProvince(province);
                fcOrderInsured.setCity(city);
                fcOrderInsured.setCounty(county);
                fcOrderInsured.setDetaileAddress(detaileAdddress);
                fcOrderInsured.setZipCode(zipCode);
                fcOrderInsured.setMainEMail(params.get("email1"));
                fcOrderInsured.setMainDetaileAddress(detaileAdddress);
                fcOrderInsured.setMainMobilePhone(params.get("mobilePhone"));
                fcOrderInsured.setMainZipCode(zipCode);
                fcOrderInsured.setMainProvince(province);
                fcOrderInsured.setMainCity(city);
                fcOrderInsured.setMainCounty(county);

                fcOrderInsuredMapper.updateByPrimaryKeySelective(fcOrderInsured);

            }
            //调用核心预核保
            //封装数据
            String funcFlag = "YF0001";
            Map<String, Object> paramsMap = dailyIssueService.signBill(ensureCode, funcFlag, corePerNo);
            UnderWriteReq underWriteReq = (UnderWriteReq) paramsMap.get("data");
            long startStamp = System.currentTimeMillis();
            //调核心接口 基础单预核保
            String param = JSONObject.toJSONString(underWriteReq, SerializerFeature.DisableCircularReferenceDetect);
            Log.info("\n调用核心基础单预核保请求入参:{}\n", param);
            String result = null;
            try {
                result = HttpUtil.postHttpRequestJson(myProps.getBasicsPreCheckUrl(), param, myProps.getCoreAppId(), myProps.getCoreAppSecret());
                if (StringUtils.isEmpty(result)) {
                    //手动回滚事务
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return JSON.toJSONString(ResultUtil.error("基础单预核保失败！"));
                }
            } catch (Exception e) {
                Log.info("基础单预核保失败!");
                throw new RuntimeException();
            }
            Log.info("\n调用核心基础单预核保返回报文：" + result);
            String flag = JSONObject.parseObject(result).getString("flag");
            long endStamp = System.currentTimeMillis();
            Log.info("调用核心基础单预核保接口用时：" + (endStamp - startStamp));
            if ("0".equals(flag)) {
                JSONObject body = JSON.parseObject(result).getJSONObject("body");
                // 自核通过标志
                String autoUWFlag = body.getString("autoUWFlag");
                // 自核结论描述
                String remark = body.getString("remark");
                if ("0".equals(autoUWFlag)) {
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", "被险人信息保存成功");
                } else {
                    JSONArray jsonArray = body.getJSONArray("autoUWInfoList");
                    String errorMsgs = "";
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject json = jsonArray.getJSONObject(i);
                        errorMsgs += json.getString("notification") + "/";
                    }
                    // 手动回滚事务
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return JSON.toJSONString(ResultUtil.error(remark + ":" + errorMsg));
                }
            } else {
                // 手动回滚事务
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return JSON.toJSONString(ResultUtil.error("预核保失败：" + JSONObject.parseObject(result).getString("desc")));
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("保存保险人信息失败:", e.getMessage());
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 查询被保险人信息（最少三个人）
     *
     * @param authorization
     * @param ensureCode
     * @param pageNo
     * @param pageSize
     * @param isReal
     * @return
     */
    public String getIsensurePerson(String authorization, String ensureCode, int pageNo, int pageSize, String isReal) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            PageHelper.startPage(pageNo, pageSize);
            List<Map<String, String>> maps = fcPerInfoMapper.selectInsuredPerson_All(ensureCode);
            for (Map<String, String> map : maps) {
                String occupationCode = map.get("occupationCode");
                String city = map.get("city");
                String area = map.get("area");
                String province = map.get("province");
                String codeName = fdCodeMapper.selectNameByCode("OccupationDetail", occupationCode);
                map.put("occupationCodeName", codeName);
                if (checkcountname(city)) {
                    //如果存在汉字,不查码表
                    map.put("cityName", city);
                } else {
                    FDPlace fdPlacecity = fdPlaceMapper.selectByPrimaryKey(city);
                    map.put("cityName", fdPlacecity != null ? fdPlacecity.getPlaceName() : "");
                }
                if (checkcountname(area)) {
                    map.put("areaName", area);
                } else {
                    FDPlace fdPlacearea = fdPlaceMapper.selectByPrimaryKey(area);
                    map.put("areaName", fdPlacearea != null ? fdPlacearea.getPlaceName() : "");
                }
                FDPlace fdPlace = fdPlaceMapper.selectByPrimaryKey(province);
                map.put("provinceName", fdPlace != null ? fdPlace.getPlaceName() : "");

                //脱敏处理
                map.put("iDNo", Base64AndMD5Util.base64SaltEncode(map.get("iDNo"), "130"));
                map.put("mobilePhone", Base64AndMD5Util.base64SaltEncode(map.get("mobilePhone"), "130"));

            }
            PageHelperUtil<Map<String, String>> teamPageInfo = new PageHelperUtil<>(maps);
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("list", teamPageInfo.getList());
            resultMap.put("data", dataMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "获取被保险人信息成功！！！");
        } catch (Exception e) {
            Log.info("获取被保险人信息失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "获取被保险人信息失败！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);

    }

    /**
     * 判断是否存在汉字
     *
     * @param countname
     * @return
     */
    public static boolean checkcountname(String countname) {
        Pattern p = Pattern.compile("[\u2E80-\uFE4F]");
        Matcher m = p.matcher(countname);
        if (m.find()) {
            return true;
        }
        return false;
    }

    /**
     * 企业信息回显特别约定和支付方式
     *
     * @param map1
     * @param ensureCode
     * @return
     */
    public String addPayTypeandsome(Map<String, Object> map1, String ensureCode) {
//        Map<String, Object> map1 = (Map<String, Object>) JSON.parse(responseInfo);
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
            //查找 支付方式
            Map<String, Object> ensureMap = new HashMap<>();
            ensureMap.put("EnsureCode", ensureCode);
            ensureMap.put("ConfigNo", "021");
            FCEnsureConfig fcEnsureConfig1 = fcEnsureConfigMapper.selectPayType(ensureMap);
            String PayType = fcEnsureConfig1.getConfigValue();
            //查找特别约定
            Map<String, Object> ensureMap1 = new HashMap<>();
            ensureMap1.put("EnsureCode", ensureCode);
            ensureMap1.put("ConfigNo", "018");
            FCEnsureConfig fcEnsureConfig2 = fcEnsureConfigMapper.selectPayType(ensureMap1);
            String SpecialEngagement = fcEnsureConfig2.getConfigValue();
            map1.put("PayType", PayType);
            map1.put("SpecialEngagement", SpecialEngagement);
        } catch (Exception e) {
            Log.info("企业信息回显特别约定和支付方式失败了:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "企业信息回显特别约定和支付方式失败了！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(map1);
    }

    /**
     * 被保险人信息修改
     *
     * @param authorization
     * @param params
     * @return
     */
    public String updateIsensuredPeople(String authorization, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> list = new HashMap<>();
        ArrayList planlist = new ArrayList();
        try {
            String IDNo = params.get("idNo");
            String grpNo = params.get("grpNo");
            String planCode = params.get("planCode");
            String ensureCode = params.get("ensureCode");
            String amnt = params.get("amnt");
            String insurePeriod = params.get("insurePeriod");
            String payFrequency = params.get("payFrequency");
            String payPeriod = params.get("payPeriod");
            String prem = params.get("prem");
            if (grpNo == null) {
                String unifiedsociCode = params.get("unifiedsociCode");
                //得到 grpNo
                FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectGrpInfo(unifiedsociCode);
                grpNo = fcGrpInfo.getGrpNo();
            }
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectbyIdNoandGrpNo(IDNo, grpNo);
            list.put("IDNo", IDNo);
            list.put("prem", prem);
            list.put("grpNo", grpNo);
            list.put("planCode", planCode);
            list.put("ensureCode", ensureCode);
            list.put("amnt", amnt);
            list.put("insurePeriod", insurePeriod);
            list.put("payFrequency", payFrequency);
            list.put("payPeriod", payPeriod);
            List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate(ensureCode);
            for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos) {
                Map<String, Object> dataMap = new HashMap<>();
                String codeKey = fcDailyInsureRiskDetailInfo.getPlanCode();
                String codeName = fdCodeMapper.selectNameByCode("planCode", codeKey);
                dataMap.put("CodeName", codeName == null ? "" : codeName);
                dataMap.put("CodeKey", codeKey);
                planlist.add(dataMap);
            }
            resultMap.put("list", list);
            resultMap.put("planCode", planlist);
            resultMap.put("fcPerInfo", fcPerInfo);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "被保险人信息编辑回显接口成功！！！");
        } catch (Exception e) {
            Log.info("被保险人信息修改失败了:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "被保险人信息编辑回显接口失败！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 被保险人列表删除
     *
     * @param authorization
     * @param ensureCode
     * @return
     */
    public String deleteisEnsuredPeople(String authorization, String ensureCode, String IDNo, String grpNo, String grpOrderNo) {

        Map<String, Object> resultMap = new HashMap<>();
        try {
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectbyIdNoandGrpNo(IDNo, grpNo);
            //为了获取personID
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectByPrimaryKey(grpOrderNo);
            String grpContNo = fcGrpOrder.getGrpContNo();
            String perNo = fcPerInfo.getPerNo();
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
            String personID = fcStaffFamilyRela.getPersonID();

            //为了获取 ordeNo
            Map<String, Object> map = new HashMap<>();
            map.put("perNo", perNo);
            map.put("grpOrderNo", grpOrderNo);
            FCOrder fcOrder = fcOrderMapper.selectOrderByperNo(map);
            String orderNo = fcOrder.getOrderNo();

            //为了获取 表fcorderinsured 中的OrderItemNo
            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectOrderItemNo(grpOrderNo, orderNo, personID);
            String orderItemNo = fcOrderInsured.getOrderItemNo();

            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            String orderItemDetailNo = fcOrderItem.getOrderItemDetailNo();

//            int count = fcEnsureMapper.selectishasDaily(grpOrderNo, IDNo);
            int count = fcEnsureMapper.selectishasonDaily(IDNo);
            if (count > 1) {
                /*如果 count大于 1  就说明 该企业的该员工 已经定制过基础单，这个时候删除的话 不能删除 企业对应的员工表（fcperson、fcperinfo、fcstaffamilyrela）
                需要删除 订单表（fcperregistDay、fcorder、fcorderItem、fcorderItemDetail、fcorderInsured）*/

                fcPerRegistDayMapper.deleteByParams(ensureCode, perNo);
                fcOrderMapper.deleteByPrimaryKey(orderNo);
                fcOrderItemMapper.deleteByPrimaryKey(orderItemNo);
                fcOrderItemDetailMapper.deleteByKey(orderItemDetailNo);
                fcOrderInsuredMapper.deleteByPrimaryKey(orderItemNo);
            } else {
                //说明该企业 该员工 没有参加过 其他的基础单。这个时候 就可以把员工表对应的所有数据全面删除。
                fcOrderInsuredMapper.deleteByPrimaryKey(orderItemNo);
                fcPersonMapper.deleteByPrimaryKey(personID);
                fcPerInfoMapper.deleteByPrimaryKey(perNo);
                Map<String, String> map1 = new HashMap<>();
                map1.put("perNo", perNo);
                map1.put("personID", personID);
                fcStaffFamilyRelaMapper.deleteByPrimaryKey(map1);
                fcPerRegistDayMapper.deleteByParams(ensureCode, perNo);
                fcOrderMapper.deleteByPrimaryKey(orderNo);
                fcOrderItemMapper.deleteByPrimaryKey(orderItemNo);
                fcOrderItemDetailMapper.deleteByKey(orderItemDetailNo);
                //删除员工的同时也要将ftp服务器上的影像件删除
                //由于影像件上传不是必录的，故删除影像件之前要判断影像件是否存在
                //判断证件影像照1是否存在
                if (!(null == fcPerInfo.getIdImage1() || "".equals(fcPerInfo.getIdImage1()))) {
                    String[] split1 = fcPerInfo.getIdImage1().split("=");
                    String localFilePath1 = split1[1];
                    File detelefile1 = new File(localFilePath1);
                    if (detelefile1.exists()) {
                        FileUtil.delete(detelefile1);
                    }
                }
                //判断证件影像照2是否存在
                if (!(null == fcPerInfo.getIdImage2() || "".equals(fcPerInfo.getIdImage2()))) {
                    String[] split2 = fcPerInfo.getIdImage2().split("=");
                    String localFilePath2 = split2[1];
                    File detelefile2 = new File(localFilePath2);
                    if (detelefile2.exists()) {
                        FileUtil.delete(detelefile2);
                    }
                }
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "被保险人列表删除接口成功！！！");
        } catch (Exception e) {
            Log.info("被保险人信息修改失败了:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "被保险人列表删除失败了！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 根据生日计算出年龄 的工类（根据基础单投保日期计算年龄）
     *
     * @param birthday
     * @return
     */
    public int getAgeByBirth(String birthday, String ensureCode) {
        //返回 -1 代表报错或者没到一岁。返回age代表的是年龄
        int age = -1;
        try {
            long dayByBirth = getDayByBirth(birthday);
            if (dayByBirth == -1) {
                return -1;
            }
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
            String startAppntDate = fcEnsure.getStartAppntDate();//基础单投保日期 用来计算年龄
            Calendar now = Calendar.getInstance();
            Calendar birth = Calendar.getInstance();
            //把string转化为date
            DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
            Date date = fmt.parse(startAppntDate);
            Date datebirth = fmt.parse(birthday);
            now.setTime(date);//  数据库里面的基础单投保日期 转成 Date 格式
            birth.setTime(datebirth);//  数据库里面的基础单投保日期 转成 Date 格式
            age = now.get(Calendar.YEAR) - birth.get(Calendar.YEAR);
            if (now.get(Calendar.DAY_OF_YEAR) > birth.get(Calendar.DAY_OF_YEAR)) {
                age += 1;
            }
            return age;
        } catch (Exception e) {//兼容性更强,异常后返回数据
            e.printStackTrace();
            return -1;
        }
    }

    /**
     * 计算出生天数
     *
     * @param birthday
     * @return
     */
    public long getDayByBirth(String birthday) {
        try {
            // 指定日期的格式
            SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd");
// 直接创建现在的日期
            Date nowDate = new Date();
// 将现在的日期转换成字符串
            String now = s.format(nowDate);
            Date oldDate = s.parse(birthday);
            String old = s.format(oldDate);
// 将出生日期和当前日期转换成毫秒值.getTime()
            long birthdayold = oldDate.getTime();
            long nowday = nowDate.getTime();
            long time = nowday - birthdayold;
            // 除以计算得到天数
            Log.info("出生到现在经历了这么多天:" + time / 1000 / 60 / 60 / 24);
            return (time / 1000 / 60 / 60 / 24);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return -1;
    }


    /**
     * 复核，新增计划的审核 操作 回显数据
     *
     * @param token
     * @param ensureCode
     * @return
     */
    public String initEnsureInfoAdmin(String token, String ensureCode) {
        GlobalInput globalInput = userService.getSession(token);
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        try {
            if (ensureCode != null && !ensureCode.equals("null")) {
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
                if (fcEnsure.getEnsureState().equals("013")) {
                    //点击了审核按钮。福利状态改为 审核中
                    fcEnsure.setEnsureState("016");
                    fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
                }
                Map<String, String> mapInfo = new HashMap<>();
                String grpNo = fcEnsure.getGrpNo();
                FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectTranscodingGrpInfo(grpNo);
                List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate(ensureCode);
                ArrayList list = new ArrayList();
                for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos) {
                    String planCode = fcDailyInsureRiskDetailInfo.getPlanCode();
                    if (!list.contains(planCode)) {
                        list.add(planCode);
                    }
                }
                Object[] insuredType = list.toArray();
                resultMap.put("insuredType", insuredType);//存放的是 list。里面是计划编码
                resultMap.put("FCGrpInfo", fcGrpInfo);
                //查找 支付方式
                Map<String, Object> ensureMap = new HashMap<>();
                ensureMap.put("EnsureCode", ensureCode);
                ensureMap.put("ConfigNo", "021");
                FCEnsureConfig fcEnsureConfig1 = fcEnsureConfigMapper.selectPayType(ensureMap);
                String PayType = fcEnsureConfig1.getConfigValue();
                //查找特别约定
                Map<String, Object> ensureMap1 = new HashMap<>();
                ensureMap1.put("EnsureCode", ensureCode);
                ensureMap1.put("ConfigNo", "018");
                FCEnsureConfig fcEnsureConfig2 = fcEnsureConfigMapper.selectPayType(ensureMap1);
                String SpecialEngagement = fcEnsureConfig2.getConfigValue();
                resultMap.put("PayType", PayType);
                resultMap.put("SpecialEngagement", SpecialEngagement);
                FcEnsureContact fcEnsureContactInfo = fcEnsureContactMapper.selectByPrimaryKey(ensureCode);
                String lastEnsureCode = "";
                if (fcEnsureContactInfo == null) {
                    if ("2".equals(globalInput.getCustomType())) {
                        FCEnsure lastFcensure = fcEnsureMapper.getEnsureByGrpNo(grpNo, globalInput.getUserName());
                        if (lastFcensure != null) {
                            lastEnsureCode = lastFcensure.getEnsureCode();
                        }
                    } else {
                        lastEnsureCode = ensureCode;
                    }
                } else {
                    lastEnsureCode = ensureCode;
                }
                Map<String, Object> configNoMap = new HashMap<>();
                configNoMap.put("grpNo", grpNo);
                configNoMap.put("ensureCode", ensureCode);
                configNoMap.put("configNo", "008");
                FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(configNoMap);
                if (fcEnsure != null) {
                    resultMap.put("FcEnsure", fcEnsure);
                    FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(lastEnsureCode);
                    if (fcEnsureContact == null) {
                        String customNo = fcContactGrpRelaMapper.selectContactNoByGrpNoandcontactType(grpNo);
                        FcGrpContact fcGrpContact = fcGrpContactMapper.selectByPrimaryKey(customNo);//联系人编号
                        dataMap.put("contact", fcGrpContact);
                    } else {
                        dataMap.put("contact", fcEnsureContact);
                    }
                }
                dataMap.put("ensureConfig", fcEnsureConfig);
            }
            //根据福利编号得到险种编码
            List<Map<String, String>> riskinfo = fdRiskInfoMapper.selectRiskNameAndRiskCodeByEnsureCode(ensureCode);
            //初始化页面时也要添加新增字段    手续费比率以及佣金/服务津贴率
            Map<String, String> riskMap = riskinfo.get(0);
            Log.info("该福利对应的险种编码为: {}", riskMap.get("RiskCode"));
            String riskCode = riskMap.get("RiskCode");
            FcDailyInsureRiskInfo fcDailyInsureRiskInfo = fcDailyInsureRiskInfoMapper
                    .selectByEnsureCodeAndRiskCode(ensureCode, riskCode);
            Object commissionOrAllowanceRatio = fcDailyInsureRiskInfo.getCommissionOrAllowanceRatio();
            if (!ObjectUtils.isEmpty(commissionOrAllowanceRatio)) {
                BigDecimal a1 = new BigDecimal(Double.valueOf(String.valueOf(commissionOrAllowanceRatio)));
                BigDecimal a2 = new BigDecimal(100);
                Double commissionOrAllowanceRatio1 = a1.multiply(a2).doubleValue();
                fcDailyInsureRiskInfo
                        .setCommissionOrAllowanceRatio(String.valueOf(commissionOrAllowanceRatio1));
            }
            resultMap.put("rateinfo", fcDailyInsureRiskInfo);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "复核新增计划的审核-查询数据成功");
            resultMap.put("data", dataMap);
        } catch (Exception e) {
            Log.info("复核新增计划的审核回显数据失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "复核新增计划的审核回显数据失败！");
            return JSON.toJSONString(resultMap);
        }
        if (ensureCode != null && !"".equals(ensureCode)) {
            //不同角色都调用了该接口，
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if ("02".equals(fcEnsure.getEnsureState()) && "3".equals(globalInput.getCustomType())) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_03);
                Log.info("更新福利表fcEnsure投保状态完成。。。后台审核中");
            } else if ("08".equals(fcEnsure.getEnsureState())) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_09);
                Log.info("更新福利表fcEnsure投保状态完成。。。弹性计划 Hr定制中");
            } else if ("12".equals(fcEnsure.getEnsureState())) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_012);
                Log.info("更新福利表fcEnsure投保状态完成。。。日常计划计划 初审定制中");
            }
            fcEnsure.setOperator(globalInput.getUserNo());
            fcEnsure = (FCEnsure) CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 日常计划-点击变更计划回显数据
     *
     * @param authorization
     * @param ensureCode
     * @return
     */
    public String addDailyPlanInfo(String authorization, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String ensureState = "";
        List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = null;
        try {
            //变更计划 待审核
            FCEnsure fcEnsure1 = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
            String ensureState1 = fcEnsure1.getEnsureState();
            if (ensureState1.equals("017")) {
                //变更计划审核退回
                fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate013(ensureCode);//查询有效的计划
            } else {
                //便更计划 审核通过  待审核
                fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstateAll(ensureCode);//查询所有的计划
            }
//            List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstateAll(ensureCode);//查询有效的计划
            ArrayList list = new ArrayList();
            for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos) {
                String planCode = fcDailyInsureRiskDetailInfo.getPlanCode();
                if (!list.contains(planCode)) {
                    list.add(planCode);
                }
            }
            if (ensureCode != null) {
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
                if (fcEnsure != null) {
                    ensureState = fcEnsure.getEnsureState();
                    String codeType = fdCodeMapper.selectNameByCode("ensureState", ensureState);
                    resultMap.put("ensureState", ensureState);
                    resultMap.put("ensureName", codeType);
                } else {
                    Log.info("点击变更计划回显数据失败--查询fcensure 表失败");
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "点击变更计划回显数据失败！--查询fcensure 表失败");
                    return JSON.toJSONString(resultMap);
                }
            } else {
                Log.info("ensureCode不能为空啊大哥！");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "ensureCode不能为空啊大哥！");
                return JSON.toJSONString(resultMap);
            }

            Object[] insuredType = list.toArray();
            resultMap.put("insuredType", insuredType);
            resultMap.put("RiskCode", "16380");
            resultMap.put("RiskName", "横琴福裕团体重大疾病保险");
        } catch (Exception e) {
            Log.info("点击变更计划回显数据失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "点击变更计划回显数据失败！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 变更计划添加可投保计划
     *
     * @param authorization
     * @param params
     * @return
     */
    public String addDailyPlan(String authorization, Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        ArrayList arrayList = new ArrayList();
        List<String> insuredType = (List<String>) params.get("insuredType");
        String DeployNo = maxNoService.createMaxNo("EnsureConfig", "", 20);//配置流水号
        try {
            String ensureCode = (String) params.get("ensureCode");
            if (insuredType != null) {
                List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate(ensureCode);//查询有效的计划
                List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos_All = fcDailyInsureRiskDetailInfoMapper.selectListBydailyPlanCode(ensureCode);//查询所有的计划
                if (fcDailyInsureRiskDetailInfos_All != null) {
                    for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos_All) {
                        String planCode = fcDailyInsureRiskDetailInfo.getPlanCode();
                        arrayList.add(planCode);
                    }
                    // 利用list中的元素创建HashSet集合，此时set中进行了去重操作
                    HashSet set = new HashSet(insuredType);
                    // 清空list集合
                    insuredType.clear();
                    // 将去重后的元素重新添加到list中
                    insuredType.addAll(set);
                    // 利用list中的元素创建HashSet集合，此时set中进行了去重操作
                    HashSet set1 = new HashSet(arrayList);
                    // 清空list集合
                    arrayList.clear();
                    // 将去重后的元素重新添加到list中
                    arrayList.addAll(set1);//旧的
                    int num = 0;
                    for (String s : insuredType) {//循环新的，所有的。
                        if (!arrayList.contains(s)) {//如果旧的里面不包含新的，说明这个新的 是新增的。需要新增。
                            FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo = new FcDailyInsureRiskDetailInfo();//日常投保险种信息配置详情表
                            setDailyInsureRiskDetailInfo(authorization, params, fcDailyInsureRiskDetailInfo);
                            fcDailyInsureRiskDetailInfo.setEnsureCode(ensureCode);
                            fcDailyInsureRiskDetailInfo.setDeployNo(DeployNo);
                            fcDailyInsureRiskDetailInfo.setDeployDetalNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                            fcDailyInsureRiskDetailInfo.setPlanCode(s);
                            fcDailyInsureRiskDetailInfo.setPlanState("1");//复审暂时没有审核，所以这个时候 应该不是有效状态的计划
                            fcDailyInsureRiskDetailInfo.setPlanUnderwritingStatus("3");
                            fcDailyInsureRiskDetailInfoMapper.insert(fcDailyInsureRiskDetailInfo);
                        } else {   //如果旧的里面有新的。需要判断旧的是不是失效状态。此时，直接把旧的状态改成 有效即可，这样做不需要区分是否为无效。统一进行操作。
                            FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo = fcDailyInsureRiskDetailInfoMapper.selectBycodeandPlanCode(ensureCode, s);
                            if (fcDailyInsureRiskDetailInfo.getPlanState().equals("1")) {
                                fcDailyInsureRiskDetailInfo.setPlanUnderwritingStatus("3");
                                fcDailyInsureRiskDetailInfoMapper.updateByPrimaryKeySelective(fcDailyInsureRiskDetailInfo);
                            }
                            if (fcDailyInsureRiskDetailInfo.getPlanState().equals("0")) {
                                //状态等于零说明这个 计划没有改变
                                num = num + 1;
                            }
                        }
                    }
                    if (insuredType.size() == num) {
                        Map<String, Object> error = ResultUtil.error("计划没有改变。");
                        return JSON.toJSONString(error);
                    }
                    FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
                    fcEnsure.setEnsureState("013");
//                    fcEnsure.setChangeType("0");
                    fcEnsure.setAuditType("1");
                    fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", "变更计划添加可投保计划-成功！");
                } else {
                    Log.info("变更计划添加可投保计划-查询有效计划失败:");
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "变更计划添加可投保计划-查询有效计划失败:！");
                    return JSON.toJSONString(resultMap);
                }
            } else {
                Log.info("变更计划添加可投保计划-计划不能为空");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "变更计划添加可投保计划-计划不能为空！");
                return JSON.toJSONString(resultMap);
            }

        } catch (Exception e) {
            Log.info("变更计划添加可投保计划失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "变更计划添加可投保计划失败！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);

    }

    /**
     * 复核变更计划的审核回显操作
     *
     * @param token
     * @param ensureCode
     * @return
     */
    @Transactional
    public String selectUpdatePlanInfo(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (ensureCode != null && !ensureCode.equals("null")) {
                //根据福利编号查出所有的日常计划
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
                //点击了审核按钮。福利状态改为 审核中
                fcEnsure.setEnsureState("016");
                fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
                Map<String, String> mapInfo = new HashMap<>();
                mapInfo.put("grpNo", fcEnsure.getGrpNo());
                // 获取token信息
                GlobalInput globalInput = userService.getSession(token);
                String manageCom = globalInput.getManageCom();
                if (org.springframework.util.StringUtils.isEmpty(manageCom)) {
                    throw new SystemException("当前操作用户管理机构为空！");
                } else {
                    mapInfo.put("manageCom", manageCom);
                }
                //根据 grpNo 找到 企业信息
                FCGrpInfo fcGrpInfo = fcGrpInfoMapper.getGrpListByNameOrCode(mapInfo).get(0);

                //这是险种计划。在此处根据  PlanUnderwritingStatus  计划承保状态 0-待承保  1-承保成功  2-承保失败  3-追加计划待审核    查出变更前和变更后 的 计划
                List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate(ensureCode);
                List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos_success = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate_success(ensureCode);//1
                List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos_update = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate_Update(ensureCode);//3
                ArrayList list_BeforChange = new ArrayList();
                ArrayList list_AfterChange = new ArrayList();
                for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo_success : fcDailyInsureRiskDetailInfos_success) {
                    String planCode = fcDailyInsureRiskDetailInfo_success.getPlanCode();
                    list_BeforChange.add(planCode);//旧的
                }
                for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo_update : fcDailyInsureRiskDetailInfos_update) {
                    String planCode = fcDailyInsureRiskDetailInfo_update.getPlanCode();
                    if (!list_AfterChange.contains(planCode)) {
                        list_AfterChange.add(planCode);
                    }
                }
                for (Object o : list_BeforChange) {
                    list_AfterChange.add(o);
                }

                resultMap.put("insuredType_After", list_AfterChange);
                resultMap.put("insuredType_Before", list_BeforChange);
                resultMap.put("fcEnsureInfo", fcEnsure);
                resultMap.put("fcGrpInfo", fcGrpInfo);
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "复核变更计划的审核回显操作成功！");
                //返回的 支付方式和 特别约定  复用的接口没有返回
                addPayTypeandsome(resultMap, ensureCode);
            } else {
                Log.error("此时的日常计划编号不能为空。。");
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "复核变更计划的审核回显操作失败！");
            }
        } catch (Exception e) {
            Log.info(e.getMessage());
            e.printStackTrace();
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "日常计划定制 复核变更计划的审核回显操作，请联系运维人员");
        } finally {
            return JSON.toJSONString(resultMap);
        }

    }

    /**
     * 管理员复核 日常计划信息查询
     *
     * @param token
     * @param params
     * @param pageNo
     * @param pageSize
     * @param isReal
     * @return
     */
    @Transactional
    public String getDailyplan_check(String token, Map<String, String> params, int pageNo, int pageSize, String isReal) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            String manageCom = globalInput.getManageCom();
            if (StringUtils.isEmpty(manageCom)) {
                throw new SystemException("初审岗用户管理机构为空！");
            } else {
                params.put("manageCom", manageCom);
            }
            // 审核类型
            String auditType = params.get("AuditType");
            // 状态
            String ensureState = params.get("ensureState");

            //审核类型 为空  状态为 已承保
            List<HashMap<String, Object>> hashMaps = null;
            if ((auditType.equals("") && (ensureState != null && ensureState.equals("015")))) {
                //新增计划时查询 015   变更计划查询所有
                PageHelper.startPage(pageNo, pageSize);
                //查询出所有的计划
                /*List<HashMap<String, Object>>*/
                hashMaps = fcEnsureMapper.selectByEnsureState015(params);
            }
            //审核类型 为空  状态为 待审核
            else if ((auditType.equals("") && (ensureState != null && ensureState.equals("013")))) {
                // 013
                PageHelper.startPage(pageNo, pageSize);
                //查询出所有的计划
                hashMaps = fcEnsureMapper.selectByEnsureState013_check(params);
            }
            //审核类型 为空  状态为 审核中
            else if ((auditType.equals("") && (ensureState != null && ensureState.equals("016")))) {
                // 013
                PageHelper.startPage(pageNo, pageSize);
                //查询出所有的审核中计划
                hashMaps = fcEnsureMapper.selectByEnsureState013_check(params);
            }
            //审核类型 有值  状态为 已承保
            else if ((auditType.equals("") && (ensureState != null && ensureState.equals("016")))) {
                // 017  只能查询出 新增计划的退回
                PageHelper.startPage(pageNo, pageSize);
                //查询出所有的审核中计划
                hashMaps = fcEnsureMapper.selectByEnsureState017(params);
            } else if ((!StringUtil.isEmpty(auditType) && auditType.equals("0")) && (!StringUtil.isEmpty(ensureState) && ensureState.equals("015"))) {
                //新增状态
                PageHelper.startPage(pageNo, pageSize);
                //查询出所有的计划
                hashMaps = fcEnsureMapper.selectByEnsure0(params);
            } else if ((!StringUtil.isEmpty(auditType) && auditType.equals("1")) && (!StringUtil.isEmpty(ensureState) && ensureState.equals("015"))) {
                //变更状态
                PageHelper.startPage(pageNo, pageSize);
                //查询出所有的计划
                hashMaps = fcEnsureMapper.selectByEnsure1(params);
            } else {
                //正常查就可以
                PageHelper.startPage(pageNo, pageSize);
                /* List<HashMap<String, Object>> */
                hashMaps = ensureAuditMapper.getDailyplanList_check(params);
            }
            for (HashMap<String, Object> alllist : hashMaps) {
                alllist.put("PlanType", "日常计划");
                String ensureStateName = fdCodeMapper.selectNameByCode("EnsureState", alllist.get("EnsureState") != null ? (String) alllist.get("EnsureState") : "");
                String auditType1 = String.valueOf(alllist.get("AuditType"));
                String AuditTypeName = "新增计划";
                if (auditType1.equals("1")) {
                    AuditTypeName = "变更计划";
                } else {
                    AuditTypeName = "新增计划";
                }
                alllist.put("ensureStateName", ensureStateName);
                alllist.put("AuditTypeName", AuditTypeName);

            }
            PageHelperUtil<HashMap<String, Object>> teamPageInfo = new PageHelperUtil<>(hashMaps);
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("list", teamPageInfo.getList());
            resultMap.put("data", dataMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "获取日常计划信息成功！");
        } catch (Exception e) {
            Log.info("获取日常计划信息失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "获取日常计划信息失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    private List<HashMap<String, Object>> getDailyList(Map<String, String> params) {
        List<HashMap<String, Object>> fcEnsureMap = ensureAuditMapper.getDailyplanList_check(params);
        String ensureState = params.get("ensureState");// 013   015
        String AuditType = params.get("AuditType");
        int y = 1;
        Iterator<HashMap<String, Object>> it = fcEnsureMap.iterator();
        while (it.hasNext()) {
            HashMap<String, Object> alllist = it.next();
            String auditType = String.valueOf(alllist.get("AuditType"));
            String ensureState1 = String.valueOf(alllist.get("EnsureState"));
            alllist.put("PlanType", "日常计划");

            if (auditType != null && !auditType.equals("") && auditType.equals("1")) {//变更计划
                if (ensureState != null && !ensureState.equals("") && auditType.equals("1") && ensureState.equals("013")) {//待审核
                    it.remove();
                    y = 2;
                }
            }
            if (auditType != null && !auditType.equals("") && auditType.equals("0")) {//新增计划
                if (ensureState != null && !ensureState.equals("") && auditType.equals("0") && ensureState.equals("015") && ensureState1.equals("013")) {//待审核
                    it.remove();
                    y = 2;
                } else if (ensureState != null && !ensureState.equals("") && auditType.equals("0") && ensureState.equals("015") && ensureState1.equals("017")) {//审核退回
                    it.remove();
                    y = 2;
                }
            }
            if (y == 1) {
                String ensureStateName = fdCodeMapper.selectNameByCode("EnsureState", alllist.get("EnsureState") != null ? (String) alllist.get("EnsureState") : "");
                alllist.put("ensureStateName", ensureStateName);

            }
            y = 1;
        }
        return fcEnsureMap;
    }

    /**
     * 管理员复核日常计划审核通过接口
     *
     * @param authorization
     * @param ensureCode
     * @return
     */
    public String Dailyplan_pass(String authorization, String ensureCode, String UnifiedsociCode, Boolean greenInsurance) {
        Map<String, Object> resultMap = new HashMap<>();
        ResponseMsg<String> responseMsg = new ResponseMsg<>();
        try {
            //击【审核通过】，该计划审核通过，计划状态变为“待收费签单”
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
            fcEnsure.setGreenInsurance(null == greenInsurance ? fcEnsure.getGreenInsurance() : greenInsurance);
            if (fcEnsure.getAuditType().equals("0")) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("ensureCode", ensureCode);
                paramMap.put("configNo", "011");
                FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(paramMap);
                if (ObjectUtils.isEmpty(fcEnsureConfig)) {
                    return JSON.toJSONString(ResultUtil.error("未查询到财务的联系方式。短信发送失败！"));
                }
                //获取企业名称、投保单号、总保费、被保人姓名及对应分单号、分单保费
                Map<String, Object> dailyMessageInfo = fcEnsureConfigMapper.getDailyMessageInfo(ensureCode);
                if (dailyMessageInfo != null) {
                    String mobilePhone = fcEnsureConfig.getConfigValue();
                    //短信发送
                    SendSMSReq sendSMSReq = new SendSMSReq();
                    sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_010.getCode());
                    sendSMSReq.setPhones(mobilePhone);
                    Map<String, Object> map = new HashMap<>();
                    map.put("grp_name", dailyMessageInfo.get("grpname"));
                    map.put("tprtno", FormatUtils.formatProposalNo((String) dailyMessageInfo.get("tprtno")));
                    map.put("totalPrem", dailyMessageInfo.get("totalPrem"));
                    sendSMSReq.setParam(map);
                    SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
                    if (!sendMessageResp.getSuccess()) {
                        responseMsg.errorStatus().message("复核通过审核之后 通知业务线下收费短信发送失败！!");
                        return JSON.toJSONString(responseMsg);
                    }
                    //新增计划改变状态并且发短信
                    fcEnsure.setEnsureState("014");
                    fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
                } else {
                    Log.info("复核通过审核之后 通知业务线下收费短信发送失败！");
                    responseMsg.errorStatus().message("复核通过审核之后 通知业务线下收费短信发送失败！!");
                    return JSON.toJSONString(responseMsg);
                }
            } else if (fcEnsure.getAuditType().equals("1")) {
                //改变计划 的有效状态。
                List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstateAll(ensureCode);
                for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos) {
                    if (fcDailyInsureRiskDetailInfo.getPlanUnderwritingStatus() != null) {
                        if (fcDailyInsureRiskDetailInfo.getPlanUnderwritingStatus().equals("3") && fcDailyInsureRiskDetailInfo.getPlanState().equals("1")) {
                            fcDailyInsureRiskDetailInfo.setPlanState("0");
                            fcDailyInsureRiskDetailInfoMapper.updateByPrimaryKeySelective(fcDailyInsureRiskDetailInfo);
                        }
                    }
                }
                //变更计划 改变状态
                fcEnsure.setEnsureState("015");
                fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "日常计划-审核通过接口成功！");
        } catch (Exception e) {
            Log.info("日常计划-审核通过接口失败！:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "日常计划-审核通过接口失败！");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 日常计划定制，回显 企业信息和联系人信息
     *
     * @param token
     * @param ensureCode
     * @param grpNo
     * @param customNo
     * @param isOperation
     * @return
     */
    public String initEnsureInfoAdmin_Daily(String token, String ensureCode, String grpNo, String customNo, String isOperation) {
        GlobalInput globalInput = userService.getSession(token);
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        if ("".equals(customNo) || null == customNo) {
            customNo = fcContactGrpRelaMapper.selectContactNoByGrpNoandcontactType(grpNo);
        }
        // 查询企业信息
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectTranscodingGrpInfo(grpNo);
        // 查询企业当前保障信息
        if (!StringUtil.isEmpty(ensureCode)) {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if (fcEnsure != null) {
                if (fcEnsure.getClientNo() == null) {
                    fcEnsure.setClientNo(fcGrpInfo.getClientno());
                    fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
                }
            }

            FcEnsureContact fcEnsureContactInfo = fcEnsureContactMapper.selectByPrimaryKey(ensureCode);
            String lastEnsureCode = "";
            if (fcEnsureContactInfo == null) {
                if ("2".equals(globalInput.getCustomType())
                        && !EnsureStateEnum.HRCUSTOMIZATIONING.getCode().equals(fcEnsure.getEnsureState())) {
                    FCEnsure lastFcensure = fcEnsureMapper.getEnsureByGrpNo(grpNo, globalInput.getUserName());
                    if (lastFcensure != null) {
                        lastEnsureCode = lastFcensure.getEnsureCode();
                    }
                } else {
                    lastEnsureCode = ensureCode;
                }
            } else {
                lastEnsureCode = ensureCode;
            }
            Map<String, Object> configNoMap = new HashMap<>();
            configNoMap.put("grpNo", grpNo);
            configNoMap.put("ensureCode", ensureCode);
            configNoMap.put("configNo", "008");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(configNoMap);
            if (fcEnsure != null) {
                dataMap.put("FcEnsure", fcEnsure);
                FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(lastEnsureCode);
                if (fcEnsureContact == null) {
                    FcGrpContact fcGrpContact = fcGrpContactMapper.selectByPrimaryKey(customNo);
//                    fcGrpContact.setIdImage1(null);
//                    fcGrpContact.setIdImage2(null);
                    dataMap.put("contact", fcGrpContact);
                } else {
                    dataMap.put("contact", fcEnsureContact);
                }
            }
            dataMap.put("ensureConfig", fcEnsureConfig);
        } else {
            FCEnsure fcEnsure = new FCEnsure();
            fcEnsure.setClientNo(fcGrpInfo.getClientno());
            fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
            dataMap.put("FcEnsure", fcEnsure);
            // 获取联系人
            FCEnsure lastFcensure = fcEnsureMapper.getEnsureByGrpNo(grpNo, globalInput.getUserName());
            if (lastFcensure != null) {
                FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(lastFcensure.getEnsureCode());
                dataMap.put("contact", fcEnsureContact);
            } else {
                FcGrpContact fcGrpContact = fcGrpContactMapper.selectByPrimaryKey(customNo);
                fcGrpContact.setIdImage1(null);
                fcGrpContact.setIdImage2(null);
                dataMap.put("contact", fcGrpContact);
            }
        }
        dataMap.put("FCGrpInfo", fcGrpInfo);
        resultMap.put("success", true);
        resultMap.put("code", "200");
        resultMap.put("message", "查询保障信息成功");
        resultMap.put("data", dataMap);
        return JSON.toJSONString(resultMap);
    }

    /**
     * HR-日常计划列表页面及查询
     *
     * @param token
     * @param params
     * @param pageNo
     * @param pageSize
     * @return
     */
    public String getDailyPlanList(String token, Map<String, String> params, int pageNo, int pageSize) {
        Map<String, Object> resultMap;
        try {
            //判断查询条件生效时间开始时间  是否小于 生效结束时间
            String endDate = params.get("endDate");
            String beginDate = params.get("beginDate");
            boolean b = DateTimeUtil.checkDate(beginDate, endDate);//比较日期 前>后：false‘
            if (!b) {
                Map<String, Object> error = ResultUtil.error("生效开始日期不能大于生效结束日期");
                return JSON.toJSONString(error);
            }
            GlobalInput globalInput = userService.getSession(token);
            String grpNo = globalInput.getGrpNo();
            params.put("grpNo", grpNo);
            PageHelper.startPage(pageNo, pageSize);
            List<Map<String, String>> dailyPlanList = fcEnsureMapper.selectDailyPlan(params);
            PageHelperUtil<Map<String, String>> teamPageInfo = new PageHelperUtil<>(dailyPlanList);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("dailyPlanList", teamPageInfo.getList());
            resultMap = ResultUtil.success("日常计划列表查询成功", dataMap);
        } catch (Exception e) {
            Log.info("查询失败：" + e.getMessage());
            resultMap = ResultUtil.error("日常计划列表查询失败！");
        }

        return JSON.toJSONString(resultMap);
    }

    /**
     * 查看日常计划保单
     *
     * @param token
     * @param ensureCode
     * @return
     */
    public String getDailyPolicy(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();

        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            // TODO 根据险种重新计算保费
            String riskCode = fcDailyInsureRiskInfoMapper.selectByEnsureCode(ensureCode);
            List<Map<String, String>> daliyPlanInfos = null;
            if ("14110".equals(riskCode)) {
                daliyPlanInfos = fcEnsureMapper.selectDaliyPlanInfoNew(ensureCode);
            } else {
                daliyPlanInfos = fcEnsureMapper.selectDaliyPlanInfo(ensureCode);
            }
            // 若是14110险种的话需要重新计算保额

            //合同信息
            List<HashMap<String, Object>> contractInfo = new ArrayList<>();
            if ("3".equals(fcEnsure.getPolicyState()) || "4".equals(fcEnsure.getPolicyState())) {
                contractInfo = ensureQueryService.ensureRisk(ensureCode, token);
            }
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("contractInfo", contractInfo);
            resultMap.put("daliyPlanInfos", daliyPlanInfos);
            resultMap.put("fcGrpOrder", fcGrpOrder);
            resultMap.put("message", "查看保单成功");
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("查看保单失败：" + e.getMessage());
            resultMap = ResultUtil.error("查看保单失败");
        }
        return JSON.toJSONString(resultMap);

    }

    /**
     * 日常计划投保清单查看详情
     *
     * @param authorization
     * @param orderItemNo
     * @return
     */
    public String getInsureInfo(String authorization, String orderItemNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            double sumPrem = 0.00;
            List<Map<String, String>> fcPlanRiskDutyList = fcOrderItemDetailMapper.selectDailyInsuredInfo(orderItemNo);
            Map<String, String> totalPremInfo = fcOrderItemDetailMapper.selectTotalPremInfo(orderItemNo);
            resultMap.put("data", fcPlanRiskDutyList);
            resultMap.put("totalPremInfo", totalPremInfo);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "投保清单详情查询成功");
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap = ResultUtil.error("投保清单详情查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询该企业是否有生成基础单
     *
     * @param authorization
     * @param UnifiedsociCode
     * @return
     */
    public String checkIsHavedDaily(String authorization, String UnifiedsociCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //这里是这个企业 所选择的所有有效的 可投保计划。再次建立基础单的时候，这些计划应该隐藏
            List<String> PlanLists = fcGrpInfoMapper.selectPlanByUnifiedsociCode(UnifiedsociCode);
            resultMap.put("PlanLists", PlanLists);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询该企业已存在基础单的 可投保计划集合。");
        } catch (Exception e) {
            Log.info("-！:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询该企业是否有可投保计划失败！！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR> Xia
     * @description校验业务员工号
     * @modified
     */
    public String getClientoInfo(String cliento, String birthday, String d, String ensureCode, String nativeplace, String iDType, String iDNo, String sex, String name) {
        Map<String, Object> resultMap = new HashMap<>();
        List<String> errorMsgList = new ArrayList<>();
        try {
            //校验姓名
            if (StringUtils.isEmpty(name)) {
                errorMsgList.add("姓名不能为空！");
            }
            //校验姓名和 电话
            if (!StringUtil.isEmpty(iDType) && !iDType.equals("1")) {
                String s = CheckUtils.checkChineseName(name);
                if (!StringUtils.isEmpty(s)) {
                    return JSON.toJSONString(ResultUtil.error(s));
                }
            } else if (!StringUtil.isEmpty(iDType) && iDType.equals("1")) {
                String s = CheckUtils.checkForeignName(name);
                String trim = name.trim();
                name = trim.replaceAll(" +", " ");
                if (!StringUtils.isEmpty(s)) {
                    return JSON.toJSONString(ResultUtil.error(s));
                }
            }
            //校验国籍
            if (errorMsgList.size() == 0) {
                int age = Integer.parseInt(DateTimeUtil.getAge(birthday));
                String msg = CheckUtils.checkNativeplaceByIdTypeandIdNoandAge("1", nativeplace, iDType, iDNo, age, birthday, sex);
                if (!StringUtils.isEmpty(msg)) {

                    errorMsgList.add("经办人 " + msg);
                }
                String checkNationality = addressCheckService.checkNationalityCode(nativeplace);
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(checkNationality)) {
                    errorMsgList.add(checkNationality);
                }
            }
            //校验证件有效期
            if (StringUtils.isEmpty(d)) {
                errorMsgList.add("证件有效期不能为空！");
            } else {
                if (!DateTimeUtil.isDate(d)) {
                    errorMsgList.add("证件有效期有误！");
                } else {
                    String msg = CheckUtils.checkDailyIdtypeEndDate(iDType, d, birthday);
                    if (!StringUtils.isEmpty(msg)) {
                        errorMsgList.add(msg);
                    }
                }
            }
            //校验证件号码
            if (StringUtils.isEmpty(iDNo)) {
                errorMsgList.add("证件号码不能为空！");
            } else if (errorMsgList.size() == 0) {
                //身份证号+户口簿的校验规则一致
                if ("0".equals(iDType) || "4".equals(iDType)) {
                    String errMsg = IDCardUtil.checkIDCard(iDNo, sex, birthday);
                    if (StringUtils.isEmpty(errMsg)) {
                        iDNo = iDNo.toUpperCase();
                    } else {
                        errorMsgList.add(errMsg);
                    }
                } else {
                    if (iDNo.length() < 3) {
                        errorMsgList.add("证件号码长度小于3位！");
                    }
                }
            }
            if (errorMsgList.size() > 0) {
                resultMap = ResultUtil.error(org.apache.commons.lang3.StringUtils.join(errorMsgList, ","));
                return JSON.toJSONString(resultMap);
            }
            //国籍码值
            List<String> nativeplaceList = fdCodeMapper.selectCodeKeyByCodeType("Nativeplace");
            //校验国籍
            if (StringUtils.isEmpty(nativeplace)) {
                Log.info("国籍不能为空");
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "国籍不能为空");
                return JSON.toJSONString(resultMap);
            } else {
                if (!nativeplaceList.contains(nativeplace)) {
                    Log.info("国籍填写有误");
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", "国籍填写有误");
                    return JSON.toJSONString(resultMap);
                }
                String checkNationality = addressCheckService.checkNationalityCode(nativeplace);
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(checkNationality)) {
                    Log.info(checkNationality);
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", checkNationality);
                    return JSON.toJSONString(resultMap);
                }
            }
            //校验证件有效期
            if (StringUtils.isEmpty(d)) {
                Log.info("证件有效期不能为空");
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "证件有效期不能为空");
                return JSON.toJSONString(resultMap);
            } else {
                if (!DateTimeUtil.isDate(d)) {
                    Log.info("证件有效期有误");
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", "证件有效期有误");
                    return JSON.toJSONString(resultMap);
                } else {
                    if (!DateTimeUtil.checkDate(DateTimeUtil.getCurrentDate(), d)) {
                        Log.info("证件有效期不得小于当前日期");
                        resultMap.put("code", "500");
                        resultMap.put("success", false);
                        resultMap.put("message", "证件有效期不得小于当前日期");
                        return JSON.toJSONString(resultMap);
                    }
                }
            }
            int ageByBirth = getAgeByBirth(birthday, ensureCode);//返回 -1 代表报错或者没到一岁。返回age代表的是年龄
            if (ageByBirth == -1) {
                Log.info("年龄计算失败了");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "年龄计算失败了");
                return JSON.toJSONString(resultMap);
            }
//            1. 未满46周岁，身份证有效期不能为长期
            if (ageByBirth < 46 && d.equals("9999-12-31")) {
                Log.info("未满46周岁，身份证有效期不能为长期");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "未满46周岁，身份证有效期不能为长期");
                return JSON.toJSONString(resultMap);
            }
            //2. 判断入参是否为空  并查询业务员信息
            if (cliento == null || "".equals(cliento)) {
                resultMap.put("message", "业务员工号不能为空");
                return JSON.toJSONString(resultMap);
            }
            List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(cliento);
            if (fdAgentInfoList.size() < 1) {
                resultMap.put("message", "该工号的数据不存在");
                return JSON.toJSONString(resultMap);
            }
            Map<String, String> map = new HashMap<>();
            map.put("name", fdAgentInfoList.get(0).getName());
            map.put("mobile", fdAgentInfoList.get(0).getMobile());
            map.put("cliento", fdAgentInfoList.get(0).getAgentCode());
            resultMap.put("clientoInfo", map);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询业务员工号信息成功");
        } catch (Exception e) {
            Log.info("查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 日常计划--投保清单查询
     *
     * @param token
     * @param ensureCode
     * @param garName
     * @param garIDNo
     * @param gradeLevelCode
     * @param famName
     * @param famIDNo
     * @param isCheck
     * @return
     */
    public String getDailyInsuredDetail(String token, String ensureCode, String garName, String garIDNo, String gradeLevelCode, String famName, String famIDNo, String isCheck) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, String> params = new HashMap<>();
            params.put("ensureCode", ensureCode);
            params.put("garName", garName);
            params.put("garIDNo", garIDNo);
            params.put("gradeLevelCode", gradeLevelCode);
            params.put("famName", famName);
            params.put("famIDNo", famIDNo);
            params.put("isCheck", isCheck);
            List<Map<String, String>> insuredMapList = fcOrderInsuredMapper.selectDailyInsuredDetail(params);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("data", insuredMapList);
            resultMap.put("message", "投保清单查询成功。");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "投保清单查询失败。");
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 返回上一步修改计划，之后再次校验被保险人列表
     * 校验投保年龄、校验可投保计划是否合格、校验保费是否发生变化。
     *
     * @param authorization
     * @param ensureCode
     * @return
     */
    public String checkPeoples(String authorization, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> resultMap1 = new HashMap<>();
        List<Object> list = new ArrayList<>();
        try {
            List<Map<String, String>> maps = fcPerInfoMapper.selectInsuredPerson_All(ensureCode);
            for (Map<String, String> people : maps) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Map<String, String> errPeople = new HashMap<>();
                String planCode = people.get("dutyCode");
                String birthday = people.get("birthDay");
                String name = people.get("name");
                String sex = people.get("sex");
                //安颐无忧新产品保险期间25年期、30年期、35年期、40年期      横琴福裕团体重大疾病保险保险期间 01-保至85周岁  02-终身
                //字段insurePeriod 目前数据库
                String insurePeriod = people.get("insurePeriod");
                //安颐无忧新产品保险期间          缴费期间  趸交——一次性交清  年缴——3年缴  5年缴
                //横琴福裕团体重大疾病保险保险期间 缴费期间  01-一次性交清  02-5年交   03-10年交  04-20年交
                String payPeriod = people.get("payPeriod");
                String dutyCode = people.get("dutyCode");
                //安颐无忧新产品，产品代码，产品代码14110
                //横琴福裕团体重大疾病保险，产品代码16380
                String productCode = people.get("riskCode");
                //保费
                String dailyPrem = people.get("dailyPrem");
                //保额
                String insuredAmount = String.valueOf(people.get("insuredAmount"));
                //交费频次  1-趸交  2-年交
                String payFrequency = String.valueOf(people.get("payFrequency"));
                //1. 校验年龄
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
                //投保开放日期
                String startAppntDate = fcEnsure.getStartAppntDate();
                //基础保单生效日期
                int Startage = DateTimeUtil.getCurrentAge(birthday, startAppntDate);
                String cvaliDate = fcEnsure.getCvaliDate();
                int Cvaliage = DateTimeUtil.getCurrentAge(birthday, cvaliDate);
               /* if (Startage != Cvaliage) {
                    Map<String, Object> error = ResultUtil.error("由于被保险人出生日期在投保日期和生效日期之间，导致年龄计算不一致，请变更被保险人或者修改生效日期");
                    return JSON.toJSONString(error);
                }*/
                //把string转化为date
                DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
                Date startAppntDateone = fmt.parse(startAppntDate);
                // 16380为横琴福裕团体重大疾病保险，保险年龄段为16周岁-55周岁 14110为安颐无忧新产品，保险年龄段为28天-80周岁
                if ("14110".equals(productCode)) {
                    //获取指定时间  把日期往后增加一年.整数往后推,负数往前移动
                    String startAppntDatetwo = DateTimeUtil.getAppointDate(startAppntDateone, -81, 1);
                    //获取指定时间  把日期往后增加一年.整数往后推,负数往前移动
                    String startAppntDatethree = DateTimeUtil.getAppointDate(startAppntDateone, -28, 3);
                    Date startAppntDatetwochange = fmt.parse(startAppntDatetwo);
                    //获取指定时间  把日期往后增加一年.整数往后推,负数往前移动
                    String startAppntDatetwoend = DateTimeUtil.getAppointDate(startAppntDatetwochange, 1, 3);
                    if (!birthday.equals(startAppntDatetwoend) && !birthday.equals(startAppntDatethree)) {
                        //比较日期 前>后：false
                        boolean b = DateTimeUtil.checkDate(birthday, startAppntDatetwoend);
                        //比较日期 前>后：false
                        boolean c = DateTimeUtil.checkDate(startAppntDatethree, birthday);
                        if (b || c) {
                            errPeople.put("message", name + "年龄不符合要求!");
                            list.add(errPeople);
                            continue;
                        }
                    }
                } else if ("16380".equals(productCode)) {
                    //获取指定时间  把日期往后增加一年.整数往后推,负数往前移动
                    String startAppntDatetwo = DateTimeUtil.getAppointDate(startAppntDateone, -56, 1);
                    //获取指定时间  把日期往后增加一年.整数往后推,负数往前移动
                    String startAppntDatethree = DateTimeUtil.getAppointDate(startAppntDateone, -16, 1);
                    Date startAppntDatetwochange = fmt.parse(startAppntDatetwo);
                    //获取指定时间  把日期往后增加一年.整数往后推,负数往前移动
                    String startAppntDatetwoend = DateTimeUtil.getAppointDate(startAppntDatetwochange, 1, 3);
                    if (!birthday.equals(startAppntDatetwoend) && !birthday.equals(startAppntDatethree)) {
                        //比较日期 前>后：false
                        boolean b = DateTimeUtil.checkDate(birthday, startAppntDatetwoend);
                        //比较日期 前>后：false
                        boolean c = DateTimeUtil.checkDate(startAppntDatethree, birthday);
                        if (b || c) {
                            errPeople.put("message", name + "年龄不符合要求!");
                            list.add(errPeople);
                            continue;
                        }
                    }
                }
                //校验参数
                Map<String, Object> map = checkPrem(birthday, sex, insurePeriod, payPeriod, planCode, productCode, insuredAmount, payFrequency, startAppntDate, dailyPrem);
                if (map.get("code").equals("303")) {
                    return JSON.toJSONString(map);
                }
                //2. 校验可投保计划是否合格
                FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndEnsureCode(planCode, ensureCode);
                String planState = fcDailyInsureRiskDetailInfo.getPlanState();
                if (planState.equals("1")) {
                    //说明这个联系人的计划状态是 无效的。提示错误
                    Log.info("被保人 " + name + " 计划状态是无效的");
                    errPeople.put("message", name + "计划状态是无效的");
                    list.add(errPeople);
                    continue;
                }
                //3. 校验保费是否发生变化
                //先被报人的信息 进行保费计算，再对比数据库中的保费是否一致。
                //16380为横琴福裕团体重大疾病保险
                //14110为安颐无忧新产品，“交费频次”为“趸交”，“交费期间”只能选择“一次性交清”；“交费频次”为“年交”，“交费期间”只能选择“3年交、5年交”
                if ("14110".equals(productCode)) {
                    if (birthday != null && !birthday.equals("") &&
                            sex != null && !sex.equals("") &&
                            dailyPrem != null && !dailyPrem.equals("") &&
                            startAppntDate != null && !startAppntDate.equals("") &&
                            insurePeriod != null && !insurePeriod.equals("") &&
                            payPeriod != null && !payPeriod.equals("") &&
                            planCode != null && !planCode.equals("") &&
                            productCode != null && !productCode.equals("")
                    ) {
                        DailyAmountTrail dailyAmountTrail = new DailyAmountTrail();
                        dailyAmountTrail.setBirthDay(birthday);
                        dailyAmountTrail.setGender(sex);
                        dailyAmountTrail.setPrem(dailyPrem);
                        dailyAmountTrail.setInsureDate(startAppntDate);
                        dailyAmountTrail.setInsurePeriod(insurePeriod);
                        dailyAmountTrail.setPayPeriod(payPeriod);
                        dailyAmountTrail.setPlanCode(planCode);
                        dailyAmountTrail.setRiskCode(productCode);

                        resultMap1 = amountTrailService.dailyAmountTrail(dailyAmountTrail);
                        if (!resultMap1.get("code").equals("200")) {
                            Log.info("保额计算失败");
                            resultMap.put("code", "500");
                            resultMap.put("success", false);
                            resultMap.put("message", "保额计算失败");
                            return JSON.toJSONString(resultMap);
                        }
                        String code = String.valueOf(resultMap1.get("code"));
                        String Amount = String.valueOf(people.get("insuredAmount"));
                        double v = Double.parseDouble(Amount);
                        double prem1 = Double.parseDouble(String.valueOf(resultMap1.get("Amount")));
                        if (code.equals("200") && Double.doubleToLongBits(v) != Double.doubleToLongBits(prem1)) {
                            Log.info("被保人 " + name + " 保额发生变化");
                            errPeople.put("message", name + "保额发生变化");
                            list.add(errPeople);
                            continue;
                        }
                    } else {
                        Log.info("被保人 " + name + " 保额发生了改变");
                        errPeople.put("message", name + "保额发生了改变");
                        list.add(errPeople);
                        continue;
                    }
                } else if ("16380".equals(productCode)) {
                    if (insuredAmount != null && !insuredAmount.equals("") &&
                            birthday != null && !birthday.equals("") &&
                            sex != null && !sex.equals("") &&
                            insurePeriod != null && !insurePeriod.equals("") &&
                            payPeriod != null && !payPeriod.equals("") &&
                            dutyCode != null && !dutyCode.equals("") &&
                            productCode != null && !productCode.equals("")
                    ) {
                        DailyPremTrail dailyPremTrail = new DailyPremTrail();
                        dailyPremTrail.setAmount(String.valueOf(CommonUtil.mul(Double.valueOf(insuredAmount), 10000.0)));//单位是万元，所以加0000
                        dailyPremTrail.setBirthDay(birthday);
                        dailyPremTrail.setGender(people.get("sex"));
//                    DateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
//                    String InsureDate = sdf2.format(new Date());
//                    FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
                        fcEnsure.getStartAppntDate();
//                    String startAppntDate = fcEnsure.getStartAppntDate();
                        dailyPremTrail.setInsureDate(startAppntDate);//基础单传投保日期，个人投保传投保当天
                        dailyPremTrail.setInsurePeriod(people.get("insurePeriod"));
                        dailyPremTrail.setPayPeriod(people.get("payPeriod"));
                        dailyPremTrail.setPlanCode(people.get("dutyCode"));
                        dailyPremTrail.setRiskCode("16380");

                        resultMap1 = premTrailService.dailyPremTrail(dailyPremTrail);
                        if (!resultMap1.get("code").equals("200")) {
                            Log.info("保费计算失败");
                            resultMap.put("code", "500");
                            resultMap.put("success", false);
                            resultMap.put("message", "保费计算失败");
                            return JSON.toJSONString(resultMap);
                        }
                        String code = String.valueOf(resultMap1.get("code"));
                        String prem = String.valueOf(people.get("dailyPrem"));
                        double v = Double.parseDouble(prem);
                        double prem1 = Double.parseDouble(String.valueOf(resultMap1.get("Prem")));
                        if (code.equals("200") && Double.doubleToLongBits(v) != Double.doubleToLongBits(prem1)) {
                            Log.info("被保人 " + name + " 保费发生变化");
                            errPeople.put("message", name + "保费发生变化");
                            list.add(errPeople);
                            continue;
                        }
                    } else {
                        Log.info("被保人 " + name + " 保费发生了改变");
                        errPeople.put("message", name + "保费发生了改变");
                        list.add(errPeople);
                        continue;
                    }
                }
            }
            if (list.size() > 0) {
                Log.info("校验投保年龄、校验可投保计划是否合格、校验保费是否发生变化-失败。");
                resultMap.put("code", "500");
                resultMap.put("arrayList", list);
                resultMap.put("success", false);
                resultMap.put("message", "校验投保年龄、校验可投保计划是否合格、校验保费是否发生变化-失败。");
                return JSON.toJSONString(resultMap);
            }

            // 日常计划安颐无忧年金修改福利的状态为待审核
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
            // 如果是日常计划的话需要修改他的计划状态
            if (fcEnsure != null) {
                // 改状态
                fcEnsure.setEnsureState(EnsureStateEnum.TOAUDIT.getCode());
                fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
            }
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "校验投保年龄、校验可投保计划是否合格、校验保费是否发生变化-成功。");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "校验投保年龄、校验可投保计划是否合格、校验保费是否发生变化-失败。");
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 日常计划-初次 校验被保险人
     *
     * @param authorization
     * @return
     */
    public String checkIsensureInfofirst(String authorization, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> resultMap1 = new HashMap<>();
        Map<String, String> hashMap = new HashMap<>();
        List<String> errorMsgList = new ArrayList<>();
        List<String> planList = new ArrayList<>();
        //保费
        String prem = params.get("dailyPrem");
        //保额
        String insuredAmount = params.get("insuredAmount");
        try {
            String grpOrderNo = params.get("grpOrderNo");
            String sex = params.get("sex");
            String num = params.get("num");
            String iDType = params.get("iDType");
            String birthDay = params.get("birthDay");
            String ensureCode = params.get("ensureCode");
            String iDNo = params.get("iDNo");
            String grpNo = params.get("grpNo");
            String mobilePhone = params.get("mobilePhone");
            String email = params.get("email1");
            String zipCode = params.get("ZipCode");
            String province = params.get("province");
            String city = params.get("city");
            String county = params.get("area");
            String name = params.get("name");
            // 详细地址
            String regAddress = params.get("regAddress");
            //缴费期间
            //14110-安颐无忧年金险种        01-一次性交清    02-3年缴    03-5年交
            //16380-横琴福裕团体重大疾病保险 01-一次性交清    02-5年交    03-10年交     04-20年交
            String payPeriod = params.get("payPeriod");
            //交费频次
            //14110-安颐无忧年金险种        1-趸交    2-月交    3-季交    4-半年交   5-年交
            //16380-横琴福裕团体重大疾病保险 1-趸交    2-年交
            String payFrequency = params.get("payFrequency");
            String planCode = params.get("dutyCode");
            //保险期间
            //14110-安颐无忧年金险种        1-25年    2-30年    3-35年    4-40年
            //16380-横琴福裕团体重大疾病保险 1-保至85周岁  2-终身
            String insurePeriod = params.get("insurePeriod");
            String nativeplace = params.get("country");
            String idTypeEndDay = params.get("idTypeEndDay");
            String occupationCode = params.get("occupationCode");
            String RiskCode = params.get("riskCode");


            //2. 校验计划是否有效
            List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate(ensureCode);
            // TODO 校验计划是否有效时，因为安颐无忧年金没有给具体的计划编码，故此时写死为ID4110，方便流程往下走
            if ("".equals(planCode) || null == planCode) {
                planCode = "ID4110";
            }
            for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos) {
                planList.add(fcDailyInsureRiskDetailInfo.getPlanCode());
            }
            if (!planList.contains(planCode)) {
                Map<String, Object> error = ResultUtil.error("该计划无效，请重新选择。");
                return JSON.toJSONString(error);
            }
            // 2.5
            // 校验电话
            if (!CheckUtils.checkMobilePhone(mobilePhone)) {
                Map<String, Object> error = ResultUtil.error("手机号格式错误，请检查！");
                return JSON.toJSONString(error);
            }
            // 校验邮箱
            if (StringUtils.isNotBlank(email) && !CheckUtils.checkEmail(email)) {
                Map<String, Object> error = ResultUtil.error("电子邮箱录入有误，请检查！");
                return JSON.toJSONString(error);
            }
            //3.校验校验详细地址：长度大于等于7，包括真实性汉字
            if (regAddress.length() < 9) {
                Map<String, Object> error = ResultUtil.error("详细地址长度大于等于9！");
                return JSON.toJSONString(error);
            }
            if (!regAddress.contains("村") && !regAddress.contains("街") && !regAddress.contains("路") && !regAddress.contains("道") && !regAddress.contains("弄") &&
                    !regAddress.contains("胡同") && !regAddress.contains("院") && !regAddress.contains("信箱") && !regAddress.contains("小区") && !regAddress.contains("号") &&
                    !regAddress.contains("房") && !regAddress.contains("室") && !regAddress.contains("栋") && !regAddress.contains("楼") && !regAddress.contains("幢")) {
                Map<String, Object> error = ResultUtil.error("请输入正确的详细地址！");
                return JSON.toJSONString(error);
            }

            // 4.校验计算保费相关字段
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
            String startAppntDate = fcEnsure.getStartAppntDate();//投保开放日期
            String cvaliDate = fcEnsure.getCvaliDate();//基础保单生效日期
            int Startage = DateTimeUtil.getCurrentAge(birthDay, startAppntDate);
            int Cvaliage = DateTimeUtil.getCurrentAge(birthDay, cvaliDate);
           /* if (Startage != Cvaliage) {
                Map<String, Object> error = ResultUtil.error("由于被保险人出生日期在投保日期和生效日期之间，导致年龄计算不一致，请变更被保险人或者修改生效日期");
                return JSON.toJSONString(error);
            }*/
            Map<String, Object> map = checkPrem(birthDay, sex, insurePeriod, payPeriod, planCode, RiskCode, insuredAmount, payFrequency, startAppntDate, prem);
            if (map.get("code").equals("303")) {
                return JSON.toJSONString(map);
            }
            //5. 保费是否发生变化
            //校验保费是否发生变化
            //先被报人的信息 进行保费计算，再对比数据库中的保费是否一致。
            //若为安颐无忧年金的话需校验保额是否发生了变化（安颐无忧年金是通过保费来计算保额的）
            if ("14110".equals(RiskCode)) {
                DailyAmountTrail dailyAmountTrail = new DailyAmountTrail();
                dailyAmountTrail.setBirthDay(birthDay);
                dailyAmountTrail.setGender(sex);
                dailyAmountTrail.setPrem(prem);
                dailyAmountTrail.setInsureDate(startAppntDate);
                dailyAmountTrail.setInsurePeriod(insurePeriod);
                dailyAmountTrail.setPayPeriod(payPeriod);
                dailyAmountTrail.setPlanCode(planCode);
                dailyAmountTrail.setRiskCode(RiskCode);
                resultMap1 = amountTrailService.dailyAmountTrail(dailyAmountTrail);
                if (!resultMap1.get("code").equals("200") || !resultMap1.get("Amount").equals(insuredAmount)) {
                    Log.info("该被保人保额因更改了信息而改变");
                    resultMap.put("code", "500");
                    resultMap.put("amount", resultMap1.get("Amount"));
                    resultMap.put("success", false);
                    resultMap.put("message", "该被保人保额因更改了信息而改变");
                    return JSON.toJSONString(resultMap);
                }
            } else if ("16380".equals(RiskCode)) {
                DailyPremTrail dailyPremTrail = new DailyPremTrail();
                dailyPremTrail.setAmount(String.valueOf(CommonUtil.mul(Double.valueOf(insuredAmount), 10000.0)));//单位是万元，所以加0000
                dailyPremTrail.setBirthDay(birthDay);
                dailyPremTrail.setGender(sex);
                fcEnsure.getStartAppntDate();
                dailyPremTrail.setInsureDate(startAppntDate);//基础单传投保日期，个人投保传投保当天
                dailyPremTrail.setInsurePeriod(insurePeriod);
                dailyPremTrail.setPayPeriod(payPeriod);
                dailyPremTrail.setPlanCode(planCode);
                dailyPremTrail.setRiskCode("16380");
                resultMap1 = premTrailService.dailyPremTrail(dailyPremTrail);
                if (!resultMap1.get("code").equals("200") || !resultMap1.get("Prem").equals(prem)) {
                    Log.info("该被保人保费因更改了信息而改变");
                    resultMap.put("code", "500");
                    resultMap.put("prem", resultMap1.get("Prem"));
                    resultMap.put("success", false);
                    resultMap.put("message", "该被保人保费因更改了信息而改变");
                    return JSON.toJSONString(resultMap);
                }
            }
            //8. 校验 省市县 均为必录
            if (StringUtil.isEmpty(province) || StringUtil.isEmpty(city) || StringUtil.isEmpty(county)) {
                Log.info("省市县不能为空！");
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "省市县不能为空！");
                return JSON.toJSONString(resultMap);
            }
            if (errorMsgList.size() == 0) {
                int age1 = Integer.parseInt(DateTimeUtil.getAge(birthDay));
                String msg = CheckUtils.checkNativeplaceByIdTypeandIdNoandAge("1", nativeplace, iDType, iDNo, age1, birthDay, sex);
                if (!StringUtils.isEmpty(msg)) {
                    errorMsgList.add(name + msg);
                }
            }
            //校验证件号码
            if (StringUtils.isEmpty(iDNo)) {
                errorMsgList.add("证件号码不能为空！");
            } else if (errorMsgList.size() == 0) {
                //身份证号+户口簿的校验规则一致
                if ("0".equals(iDType) || "4".equals(iDType)) {
                    // 经由核心、运营确认，身份证号仅能为18位 add by wudezhong 2021.5.27
                    if (iDNo.length() != 18) {
                        errorMsgList.add("行证件号码长度应为18位！");
                    } else {
                        String errMsg = IDCardUtil.checkIDCard(iDNo, sex, birthDay);
                        if (StringUtils.isEmpty(errMsg)) {
                            iDNo = iDNo.toUpperCase();
                        } else {
                            errorMsgList.add(errMsg);
                        }
                    }
                } else {
                    if (iDNo.length() < 3) {
                        errorMsgList.add("证件号码长度小于3位！");
                    }
                }
            }
            //校验证件有效期D
            if (StringUtils.isEmpty(idTypeEndDay)) {
                errorMsgList.add("证件有效期不能为空！");
            } else {
                if (!DateTimeUtil.isDate(idTypeEndDay)) {
                    errorMsgList.add("证件有效期有误！");
                } else {
                    String msg = CheckUtils.checkDailyIdtypeEndDate(iDType, idTypeEndDay, birthDay);
                    if (!StringUtils.isEmpty(msg)) {
                        errorMsgList.add(msg);
                    }
                }
            }
            //校验姓名
            if (StringUtils.isEmpty(name)) {
                errorMsgList.add("姓名不能为空！");
            } else {
                //证件类型是身份证和户口本
             /*   if ("0".equals(iDType) || "4".equals(iDType)) {
                    if (!name.matches("^[\\u0391-\\uFFE5]+$")) {
                        errorMsgList.add("姓名仅能输入中文!");
                    }
                }*/
                if (!"1".equals(iDType)) {
                    //如果不是外国公民护照  则是中文名字校验
                    String s = CheckUtils.checkChineseName(name);
                    if (!StringUtils.isEmpty(s)) {
                        errorMsgList.add(s);
                    }
                } else {
                    String s = CheckUtils.checkForeignName(name);
                    String trim = name.trim();
                    name = trim.replaceAll(" +", " ");
                    if (!StringUtils.isEmpty(s)) {
                        errorMsgList.add(s);
                    }
                }
            }
//            校验证件号码是否在系统中存在，若存在，要求其他四要素也相同
            List<FCPerson> fcPeople = fcPersonMapper.selectIDNo(iDNo);
            if (fcPeople.size() > 1) {
                FCPerson fcPerson = fcPeople.get(0);
                String name1 = fcPerson.getName();
                String sex1 = fcPerson.getSex();
                String nativeplace1 = fcPerson.getNativeplace();
                String birthDate1 = fcPerson.getBirthDate();
                String idType = fcPerson.getIDType();
                if (!name.equals(name1) || !sex.equals(sex1) || !birthDay.equals(birthDate1) || !idType.equals(iDType)) {
                    errorMsgList.add("该证件号与系统对应的四要素不同，请更改!");
                }
            }

            if (errorMsgList.size() > 0) {
                resultMap = ResultUtil.error(org.apache.commons.lang3.StringUtils.join(errorMsgList, ","));
                return JSON.toJSONString(resultMap);
            }
            resultMap.put("code", "200");
            resultMap.put("prem", prem);
            resultMap.put("success", true);
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            Log.info(e.getMessage());
            Log.info("初次 校验-失败");
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("prem", prem);
            resultMap.put("message", "信息有误。");
            return JSON.toJSONString(resultMap);
        }
    }


    //注册页面地址的三级联动
    public List<FDPlace> getThreeAddress(String upplaceCode) {
        if (!"".equals(upplaceCode) && upplaceCode != null) return fdPlaceMapper.getThr(upplaceCode);
        return fdPlaceMapper.getParent();
    }

    /**
     * 被保险人保费实时变化    param有一标志 ：sign =1代表基础单计算保费      sign=2 代表个人端计算保费。
     *
     * @param params
     * @return
     */
    public String changePrem(String authorization, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(authorization);
        try {
            String insureDate = "";
            if (!StringUtil.isEmpty(params.get("birthDay")) && !StringUtil.isEmpty(params.get("gender")) && !StringUtil.isEmpty(params.get("insurePeriod")) &&
                    !StringUtil.isEmpty(params.get("payPeriod")) && !StringUtil.isEmpty(params.get("planCode")) && !StringUtil.isEmpty(params.get("riskCode"))
                    && !StringUtil.isEmpty(params.get("amount"))) {
                String birthDay = params.get("birthDay");
                String gender = params.get("gender");
                String insurePeriod = params.get("insurePeriod");
                String payPeriod = params.get("payPeriod");
                String planCode = params.get("planCode");
                String riskCode = params.get("riskCode");
                String amount = params.get("amount");
                String payFrequency = params.get("payFrequency");
                String sign = params.get("sign");//num =1代表基础单计算保费      num=2 代表个人端计算保费。}

                //保费
                String dailyPrem = params.get("dailyPrem");

                if (!StringUtil.isEmpty(sign) && sign.equals("1")) {
                    if (StringUtil.isEmpty(params.get("ensureCode"))) {
                        ResultUtil.error("计算保费方法中福利编号不能为空");
                    }
                    String ensureCode = params.get("ensureCode");
                    FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
                    insureDate = fcEnsure.getStartAppntDate();
                    //校验最大最小年龄    基础单  16周岁-55周岁
                    int age = DateTimeUtil.getCurrentAge(birthDay, insureDate);
                    if (age > 55) {
                        Map<String, Object> error = ResultUtil.error("年龄大于投保要求。", "303");
                        return JSON.toJSONString(error);
                    }
                    if (age < 16) {
                        Map<String, Object> error = ResultUtil.error("年龄小于投保要求。", "303");
                        return JSON.toJSONString(error);
                    }
                } else if (!StringUtil.isEmpty(sign) && sign
                        .equals("2")) {
                    Date d = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    insureDate = sdf.format(d);
                    //校验最小最大年龄  个人端 28天-65周岁
                    int age = DateTimeUtil.getCurrentAge(birthDay, insureDate);
                    long dayByBirth = getDayByBirth(birthDay);
                    if (age > 65) {
                        Map<String, Object> error = ResultUtil.error("年龄大于投保要求。", "303");
                        return JSON.toJSONString(error);
                    }
                    if (dayByBirth < 28) {
                        Map<String, Object> error = ResultUtil.error("年龄小于投保要求。", "303");
                        return JSON.toJSONString(error);
                    }
                } else {
                    ResultUtil.error("基础单和个人端标志出现了问题。");
                }
                // 校验计算保费相关字段
                Map<String, Object> map = checkPrem(birthDay, gender, insurePeriod, payPeriod, planCode, riskCode, amount, payFrequency, insureDate, dailyPrem);
                if (map.get("code").equals("303")) {
                    return JSON.toJSONString(map);
                }
                //若“出生日期”、“性别”、“计划名称”、“保险期间”、“缴费期间”和“保额”均不为空时，根据所选计划、年龄、性别、缴费期间、保险期间，实时查询费率表 显示保费计算后的值，
                //1.重新计算保费；   参数都不为空，既可以调 计算保费接口。
                DailyPremTrail dailyPremTrail = new DailyPremTrail();
//                dailyPremTrail.setAmount(params.get("amount") + "0000");//单位是万元，所以加0000
                dailyPremTrail.setAmount(String.valueOf(CommonUtil.mul(Double.valueOf(amount), 10000.0)));//单位是万元，所以加0000
                dailyPremTrail.setBirthDay(params.get("birthDay"));
                dailyPremTrail.setGender(params.get("gender"));
                dailyPremTrail.setInsureDate(insureDate);//基础单传投保日期，个人投保传投保当天
                dailyPremTrail.setInsurePeriod(params.get("insurePeriod"));
                dailyPremTrail.setPayPeriod(params.get("payPeriod"));
                dailyPremTrail.setPlanCode(params.get("planCode"));
                dailyPremTrail.setRiskCode(params.get("riskCode"));
                resultMap = premTrailService.dailyPremTrail(dailyPremTrail);
            } else {
                Log.info("计算保费参数没有填写完成。");
                resultMap.put("code", "500");
                resultMap.put("success", true);
                resultMap.put("message", "计算保费参数没有填写完成！");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            Log.info("保费计算发生异常：" + e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "保费计算失败");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    private Map<String, Object> checkPrem(String birthDay, String gender, String insurePeriod, String payPeriod, String planCode, String riskCode, String amount, String payFrequency, String insureDate, String dailyPrem) {
        Map<String, Object> resultMap = new HashMap<>();
        //临时缴费期间  需更改和上面同步
        try {
            //14110-安颐无忧年金险种        校验保费    安颐无忧是通过保费来计算保额的
            //16380-横琴福裕团体重大疾病保险 校验保额   福裕是通过保额来计算所需的保费
            if ("16380".equals(riskCode)) {
                if (CommonUtil.isPureDigital(amount)) {
                    int i = Integer.parseInt(amount);
                    if (i < 1 || i > 200) {
                        Map<String, Object> error = ResultUtil.error("保额请录入1到200之间的整数", "303");
                        return error;
                    }
                } else {
                    Map<String, Object> error = ResultUtil.error("保额请录入1到200之间的整数", "303");
                    return error;
                }
            }
            //3. 判断缴费期间  和  交费频次  是否 对应。
            //缴费期间
            //14110-安颐无忧年金险种        01-一次性交清    02-3年缴    03-5年交
            //16380-横琴福裕团体重大疾病保险 01-一次性交清    02-5年交    03-10年交     04-20年交
            if ("14110".equals(riskCode)) {
                if (payFrequency.equals(PaymentFrequencyEnum.SINGLEPAYMENT.getCode()) && !payPeriod.equals(PaymentPeriodEnum.SINGLEPAYMENT.getCode())) {
                    Map<String, Object> error = ResultUtil.error("趸交只能是一次性交清。", "303");
                    return error;
                }
                if (payFrequency.equals(PaymentFrequencyEnum.YEARPAYMENT.getCode()) && payPeriod.equals(PaymentPeriodEnum.SINGLEPAYMENT.getCode())) {
                    //年交不能是一次性交清
                    Map<String, Object> error = ResultUtil.error("年交不能是一次性交清。", "303");
                    return error;
                }
            } else if ("16380".equals(riskCode)) {
                if (payFrequency.equals("1") && !payPeriod.equals("01")) {
                    Map<String, Object> error = ResultUtil.error("趸交只能是一次性交清。", "303");
                    return error;
                }
                if (payFrequency.equals("2") && payPeriod.equals("01")) {
                    //年交不能是一次性交清
                    Map<String, Object> error = ResultUtil.error("年交不能是一次性交清。", "303");
                    return error;
                }
            }
            //4. 校验选择的被保险人 年龄是否符合要求，年龄与缴费期间、缴费频次是否 关联正确。
            int age = DateTimeUtil.getCurrentAge(birthDay, insureDate);
            if ("14110".equals(riskCode)) {
                if (age >= 0 && age <= 65) {
                    // 当年龄大于等于28天小于等于65周岁，保险期间只能选择“25年期、30年期、35年期、40年期”
                    if (!("25,30,35,40".contains(insurePeriod))) {
                        Map<String, Object> error = ResultUtil.error("当年龄大于等于16周岁小于等于65周岁，保险期间只能选择“25年期”、“30年期”、“35年期”、“40年期”。", "303");
                        return error;
                    }
                    //当年龄大于等于16周岁小于等于65周岁，交费频次选择“年交”，缴费期间只能选择“3年交”、“5年交”，
                    if (payFrequency.equals(PaymentFrequencyEnum.YEARPAYMENT.getCode()) && payPeriod.equals(PaymentPeriodEnum.SINGLEPAYMENT.getCode())) {
                        Map<String, Object> error = ResultUtil.error("当年龄大于等于16周岁小于等于65周岁，交费频次选择“年交”，缴费期间只能选择“3年交”、“5年交”。", "303");
                        return error;
                    }
                }
                if (age >= 66 && age <= 70) {
                    //当年龄大于等于66周岁小于等于70周岁，保险期间只能选择“25年期、30年期、35年期”
                    if (!("25,30,35".contains(insurePeriod))) {
                        Map<String, Object> error = ResultUtil.error("当年龄大于等于16周岁小于等于65周岁，保险期间只能选择“25年期”、“30年期”、“35年期”。", "303");
                        return error;
                    }
//                    当年龄大于等于66周岁小于等于70周岁，交费频次选择“年交”，缴费期间只能选择“3年交”、“5年交”。
                    if (payFrequency.equals(PaymentFrequencyEnum.YEARPAYMENT.getCode()) && payPeriod.equals(PaymentPeriodEnum.SINGLEPAYMENT.getCode())) {
                        Map<String, Object> error = ResultUtil.error("当年龄大于等于66周岁小于等于70周岁，交费频次选择“年交”，缴费期间只能选择“3年交”、“5年交”。", "303");
                        return error;
                    }
                }
                if (age >= 71 && age <= 75) {
                    //当年龄大于等于71周岁小于等于75周岁，保险期间只能选择“25年期、30年期”
                    if (!("25,30".contains(insurePeriod))) {
                        Map<String, Object> error = ResultUtil.error("当年龄大于等于71周岁小于等于75周岁，保险期间只能选择“25年期”、“30年期”。", "303");
                        return error;
                    }
//                    当年龄大于等于71周岁小于等于75周岁，交费频次选择“年交”，缴费期间只能选择“3年交”、“5年交”。
                    if (payFrequency.equals(PaymentFrequencyEnum.YEARPAYMENT.getCode()) && payPeriod.equals(PaymentPeriodEnum.SINGLEPAYMENT.getCode())) {
                        Map<String, Object> error = ResultUtil.error("当年龄大于等于71周岁小于等于75周岁，交费频次选择“年交”，缴费期间只能选择“3年交”、“5年交”。", "303");
                        return error;
                    }
                }
                if (age >= 76 && age <= 77) {
                    //当年龄大于等于76周岁小于等于74周岁，保险期间只能选择“25年期”
                    if (!("25".contains(insurePeriod))) {
                        Map<String, Object> error = ResultUtil.error("当年龄大于等于76周岁小于等于77周岁，保险期间只能选择“25年期”。", "303");
                        return error;
                    }
//                    当年龄大于等于76周岁小于等于77周岁，交费频次选择“年交”，缴费期间只能选择“3年交”。
                    if (payFrequency.equals(PaymentFrequencyEnum.YEARPAYMENT.getCode()) && !payPeriod.equals(PaymentPeriodEnum.THREEYEARPAYMENT.getCode())) {
                        Map<String, Object> error = ResultUtil.error("当年龄大于等于76周岁小于等于77周岁，交费频次选择“年交”，缴费期间只能选择“3年交”。", "303");
                        return error;
                    }
                }
                if (age >= 78 && age <= 80) {
                    //当年龄大于等于78周岁小于等于80周岁，保险期间只能选择“25年期”
                    if (!("25".contains(insurePeriod))) {
                        Map<String, Object> error = ResultUtil.error("当年龄大于等于78周岁小于等于80周岁，保险期间只能选择“25年期”。", "303");
                        return error;
                    }
//                    当年龄大于等于78周岁小于等于80周岁，交费频次选择“趸交”，缴费期间只能选择“趸交”。
                    if (payFrequency.equals(PaymentFrequencyEnum.SINGLEPAYMENT.getCode()) && !payPeriod.equals(PaymentPeriodEnum.SINGLEPAYMENT.getCode())) {
                        Map<String, Object> error = ResultUtil.error("当年龄大于等于78周岁小于等于80周岁，交费频次选择“趸交”，缴费期间只能选择“一次性交清”。", "303");
                        return error;
                    }
                }
                if (payFrequency.equals(PaymentFrequencyEnum.SINGLEPAYMENT.getCode()) && !payPeriod.equals(PaymentPeriodEnum.SINGLEPAYMENT.getCode())) {
//                当交费频次选择“趸缴”，缴费期间只能选择“一次性交清”；
                    Map<String, Object> error = ResultUtil.error("当交费频次选择“趸缴”，缴费期间只能选择“一次性交清”。", "303");
//                Map<String, Object> error = ResultUtil.error("交费频次和缴费期间冲突。", "303");
                    return error;
                }
            } else if ("16380".equals(riskCode)) {
                if (age >= 16 && age <= 45) {
                    //当年龄大于等于16周岁小于等于45周岁，交费频次选择“年交”，缴费期间只能选择“5年交”、“10年交”、“20年交”
                    if (payFrequency.equals("2") && payPeriod.equals("01")) {
                        Map<String, Object> error = ResultUtil.error("当年龄大于等于16周岁小于等于45周岁，交费频次选择“年交”，缴费期间只能选择“5年交”、“10年交”、“20年交”。", "303");
//                    Map<String, Object> error = ResultUtil.error("交费频次和缴费期间冲突。", "303");
                        return error;
                    }
                }
                if (age >= 46 && age <= 55) {
//                    当年龄大于等于46周岁小于等于55周岁，交费频次选择“年交”，缴费期间只能选择“5年交”、“10年交”。
                    if ((payFrequency.equals("2") && payPeriod.equals("01")) || (payFrequency.equals("2") && payPeriod.equals("04"))) {
                        Map<String, Object> error = ResultUtil.error("当年龄大于等于46周岁小于等于55周岁，交费频次选择“年交”，缴费期间只能选择“5年交”、“10年交”。", "303");
//                    Map<String, Object> error = ResultUtil.error("交费频次和缴费期间冲突。", "303");
                        return error;
                    }
                }
                if (payFrequency.equals("1") && !payPeriod.equals("01")) {
//                当交费频次选择“趸缴”，缴费期间只能选择“一次性交清”；
                    Map<String, Object> error = ResultUtil.error("当交费频次选择“趸缴”，缴费期间只能选择“一次性交清”。", "303");
//                Map<String, Object> error = ResultUtil.error("交费频次和缴费期间冲突。", "303");
                    return error;
                }
            }
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "保费参数校验成功。");
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("code", "303");
            resultMap.put("success", false);
            resultMap.put("message", "校验保费参数失败。");
            return resultMap;
        }
        return resultMap;
    }

    public String getOccupationCode(String codeType, String codeDesc) {
        Map<String, Object> resultMap = new HashMap<>();
//        List<HashMap<String, Object>> code = fdCodeMapper.getOccupationCode(codeType,codeDesc);
        List<HashMap<String, Object>> code = fdCodeMapper.getOccupationCodeOneToFour(codeType, codeDesc);
        if (code == null || code.size() < 1) {
            Map<String, Object> error = ResultUtil.error("基础单不支持该类型职业,请重新选择。");
            List<String> list = new ArrayList();
            error.put("list", list);
            return JSON.toJSONString(error);
        }
        resultMap.put("codelist", code);
        resultMap.put("code", "200");
        resultMap.put("message", "查询成功");
        return JSON.toJSONString(resultMap);
    }

    /**
     * 作废计划
     *
     * @param Authorization
     * @param ensureCode
     * @return
     */
    public String voidPlan(String Authorization, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtil.isEmpty(ensureCode)) {
                Map<String, Object> error = ResultUtil.error("参数不能为空，福利编号为空！");
                return JSON.toJSONString(error);
            }
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
            fcEnsure.setEnsureState("019");
            fcEnsure = (FCEnsure) CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKeySelective(fcEnsure);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "作废计划成功。");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "作废计划失败。");
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 计算周岁+实时保费计算
     *
     * @param authorization
     * @param params
     * @return
     */
    public String getAgeAndChangePrem(String authorization, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            String birthDay = params.get("birthDay");
            String ensureCode = params.get("ensureCode");
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
            String startAppntDate = fcEnsure.getStartAppntDate();
            int currentAge = DateTimeUtil.getCurrentAge(birthDay, startAppntDate);//计算周岁  不到一周岁返回0
            String s = changePrem(authorization, params);
            JSONObject jsonObject = JSON.parseObject(s);
            jsonObject.put("age", currentAge);
            if (!jsonObject.get("code").equals("200")) {
                return JSON.toJSONString(jsonObject);
            }
            jsonObject.remove("message");
            jsonObject.put("message", "计算周岁+实时保费计算成功。");
            return JSON.toJSONString(jsonObject);
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "计算周岁+实时保费计算失败。");
            return JSON.toJSONString(resultMap);
        }
    }


    /**
     * 日常计划 -申请发票  （因为第二个参数 和公共方法 不一样，所以把代码复制过来修改了一下。虽然冗余，但是思路清晰）
     *
     * @param token
     * @param fcMailInfo
     * @param grpContNo
     * @return
     */
    @Transactional
    public String referMailInfo(String token, FCMailInfo fcMailInfo, String grpContNo) {
        Map<String, Object> resultMap = new HashMap<>();
        if (org.apache.commons.lang3.StringUtils.isEmpty(fcMailInfo.getEnsureCode()) || org.apache.commons.lang3.StringUtils.isEmpty(grpContNo)) {
            return JSON.toJSONString(ResultUtil.error("请求参数缺失！"));
        }
        //判断该福利是否过期
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(fcMailInfo.getEnsureCode());
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date policyEndDates = sf.parse(fcEnsure.getPolicyEndDate());
            if (!(new Date().before(policyEndDates))) {
                resultMap.put("code", "400");
                resultMap.put("message", "保险合同日期已过，无法进行纸质发票申请");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("错误信息：" + e.getMessage());
        }
        try {
            if (!CheckUtils.checkMobilePhone(fcMailInfo.getTelPhone())) {
                resultMap.put("code", "400");
                resultMap.put("message", "联系人手机格式错误，请重新录入。");
                return JSON.toJSONString(resultMap);
            }
            GlobalInput globalInput = userService.getSession(token);
            String ensurecode = fcMailInfo.getEnsureCode();
            if (ensurecode == null || "".equals(ensurecode)) {
                ensurecode = globalInput.getEnsureCode();
            }
            //根据福利编号查询是否申请过发票
            int i = fcMailInfoMapper.selectByEnsureCode(ensurecode);
            if (i > 0) {
                return JSON.toJSONString(ResultUtil.error("发票已申请，请勿重复提交！"));
            }
            resultMap.put("ensureCode", ensurecode);
            resultMap.put("configNo", "011");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(resultMap);
            if (ObjectUtils.isEmpty(fcEnsureConfig)) {
                return JSON.toJSONString(ResultUtil.error("未查询到财务的联系方式。发票申请失败！"));
            }
            resultMap.clear();
            //将发票信息发送核心，核心发送密码或邮件（待定）
            //获取发票流水号
            String customType = fcMailInfo.getCustomType();
            String invoiceType = fcMailInfo.getInvoiceType();
            if (customType == ConstantUtil.CustomType_2 || ConstantUtil.CustomType_2.equals(customType)) {
                if (invoiceType == ConstantUtil.CustomType_1 || invoiceType.equals(ConstantUtil.CustomType_1)) {
                    //处理日期为当前日期加3 天
                    /*String dealDate = DateTimeUtil.getdateYMD(3);
                    //发送短信
                    SendSMSReq sendSMSReq = new SendSMSReq();
                    sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_001.getCode());
                    sendSMSReq.setPhones(fcEnsureConfig.getConfigValue());
                    Map<String, Object> map = new HashMap<>();
                    map.put("grp_cont_no", FormatUtils.formatPolicyNo(grpContNo));
                    map.put("get_address", fcMailInfo.getProvince() + "-" + fcMailInfo.getCity() + "-" + fcMailInfo.getArea() + "-" + fcMailInfo.getAddress());
                    map.put("zipcode", fcMailInfo.getZipcode());
                    map.put("receiver", fcMailInfo.getReceiver());
                    map.put("tel_phone", FormatUtils.formatPhone(fcMailInfo.getTelPhone()));
                    map.put("deal_date", dealDate);
                    sendSMSReq.setParam(map);
                    SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
                    if (!sendMessageResp.getSuccess().equals("true")) {
                        resultMap.put("phoneMessage", sendMessageResp.getMsg());
                    }*/
                    resultMap.put("phoneMessage", "");
                }
            }
            String invoiceInfoSN = maxNoService.createMaxNo("fcMailInfo", null, 20);
            String location = fcMailInfo.getProvince() + "-" + fcMailInfo.getCity() + "-" + fcMailInfo.getArea() + "-" + fcMailInfo.getAddress();
            fcMailInfo.setLocation(location);
            fcMailInfo.setEnsureCode(ensurecode);
            fcMailInfo.setInvoiceInfoSN(invoiceInfoSN);
            fcMailInfo.setCustomType(globalInput.getCustomType());
            fcMailInfo.setApplicantName(globalInput.getName());
            fcMailInfo.setGrpNo(globalInput.getGrpNo());
            fcMailInfo.setOperator(globalInput.getUserNo());
            fcMailInfo = (FCMailInfo) CommonUtil.initObject(fcMailInfo, "INSERT");
            fcMailInfoMapper.insert(fcMailInfo);
            resultMap.put("code", "200");
            resultMap.put("message", "提交成功");
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("错误信息：" + e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("message", "申请发票失败");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * @return String  --json
     * @description 日常计划 -保单信息查询
     * <AUTHOR>
     * @date 2020/6/12 11:59
     * @params token
     * policyNo 保单号
     */
    public void getGuaranteeDetail(HttpServletResponse response, String policyNo) {
        try {
            //参数信息不为空 直接调用打印平台
            Map<String, Object> param = new HashMap<>();
            //保单号
            param.put("policyNo", policyNo);
            //保单类型   P 纸质  E 电子
            param.put("policyType", "E");
            //组合参数信息转化成jsonString
            String guaranteeParam = JSONObject.toJSONString(param);
            Log.info("开始调用打印平台查询保单信息，请求参数信息: {}", guaranteeParam);
            String guaranteeResponseData = HttpUtil.post(myProps.getPolicyDataSearchUrl(), guaranteeParam);
            if (!"".equals(guaranteeResponseData)) {
                //判断接口返回信息状态码
                /**
                 * 0000	正常完成
                 * 0001	系统异常
                 * 0004	账户校验不通过或权限不足
                 * 0005	缺少请求参数
                 * 0006	传入参数有错误
                 * 0007	PDF文件不存在
                 */
                Log.info("开始调用打印平台查询保单信息返回参数信息: {}", guaranteeResponseData);
                String rescode = JSON.parseObject(guaranteeResponseData).getString("rescode");
                if ("0000".equals(rescode)) {
                    //接口返回信息成功，获取数据开始处理
                    JSONObject jsonObject = JSON.parseObject(guaranteeResponseData).getJSONObject("result");
                    //解析row数据
                    JSONArray rows = jsonObject.getJSONArray("row");
                    if (rows != null && rows.size() > 0) {
                        String bucketName = rows.getJSONObject(0).getString("bucketName");
                        String innerNet = rows.getJSONObject(0).getString("innerNet");
                        String pdfFileId = rows.getJSONObject(0).getString("pdfFileId");
                        pdfFileId = "http://" + bucketName + "." + innerNet + "/" + pdfFileId;
                        if (!StringUtil.isEmpty(pdfFileId)) {
                            URL url = new URL(pdfFileId);
                            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                            connection.setRequestMethod("GET");
                            connection.connect();
                            InputStream fis = connection.getInputStream();
                            ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
                            byte[] buff = new byte[100];
                            int rc = 0;
                            while ((rc = fis.read(buff, 0, 100)) > 0) {
                                swapStream.write(buff, 0, rc);
                            }
                            byte[] buffer = swapStream.toByteArray();
                            swapStream.close();
                            fis.close();
                            response.reset();
                            // 设置response的Header
                            response.addHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode("团体合同（或个人凭证）.pdf", "UTF-8"));
                            //response.addHeader("Content-Length", "" + file.length());
                            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
                            response.setContentType("application/octet-stream");
                            toClient.write(buffer);
                            toClient.flush();
                            toClient.close();
                        } else {
                            Log.info("调用打印平台查询保单信息异常，查询信息失败，保单号 : {}", policyNo);
                        }
                    } else {
                        Log.info("调用打印平台查询保单信息异常，查询信息失败，保单号 : {}", policyNo);
                    }
                } else {
                    Log.info("调用打印平台查询保单信息异常，查询信息失败，保单号 : {}, 接口返回状态码 : {}", policyNo, rescode);
                }
            } else {
                Log.info("调用打印平台查询保单信息，返回的信息为空，保单号 : {}", policyNo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("try catch 异常，异常信息{}", e.getMessage());
        }
    }


    /**
     * 查询职业码表-限制在 1-6类
     *
     * @param codeType
     * @param codeDesc
     * @return
     */
    public String getOccupationCode1(String codeType, String codeDesc) {
        Map<String, Object> resultMap = new HashMap<>();
        List<HashMap<String, Object>> code = fdCodeMapper.getOccupationCodeOneTosix(codeType, codeDesc);
        if (code == null || code.size() < 1) {
            Map<String, Object> error = ResultUtil.error("基础单不支持该类型职业,请重新选择。");
            List<String> list = new ArrayList();
            error.put("list", list);
            return JSON.toJSONString(error);
        }
        resultMap.put("codelist", code);
        resultMap.put("code", "200");
        resultMap.put("message", "查询成功");
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询省市县信息
     *
     * @return
     */
    public String getAreaInfo() {
        Map<String, Object> resultMap = new HashMap<>();
        List<HashMap<String, Object>> province_list = fdCodeMapper.getprovince_list("province");
        List<HashMap<String, Object>> city_list = fdCodeMapper.getcity_list("city");
        List<HashMap<String, Object>> county_list = fdCodeMapper.getcounty_list("county");
        Map<String, Object> areaMap = new HashMap<String, Object>();
        Map<String, Object> provinceMap = new HashMap<String, Object>();
        Map<String, Object> cityMap = new HashMap<String, Object>();
        Map<String, Object> countyMap = new HashMap<String, Object>();
        province_list.forEach((HashMap<String, Object> proMap) -> {
            provinceMap.put(proMap.get("placeCode").toString(), proMap.get("PlaceName"));
        });
        city_list.forEach((HashMap<String, Object> ciMap) -> {
            cityMap.put(ciMap.get("placeCode").toString(), ciMap.get("PlaceName"));
        });
        county_list.forEach((HashMap<String, Object> coMap) -> {
            countyMap.put(coMap.get("placeCode").toString(), coMap.get("PlaceName"));
        });
        areaMap.put("province_list", provinceMap);
        areaMap.put("city_list", cityMap);
        areaMap.put("county_list", countyMap);
        resultMap.put("arealist", areaMap);
        resultMap.put("code", "200");
        resultMap.put("message", "查询成功");
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询属于日常计划的险种的编码信息
     *
     * @return RiskCode
     */
    public String queryRiskList(Map<String, String> map) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            String planType = "2";
            String ensureCode = map.get("ensureCode");
            //如果是计划新增的话
            if ("".equals(ensureCode) || null == ensureCode) {
                List<Map<String, String>> riskCodeList = fdRiskInfoMapper.selectRiskCodeByPlanType(planType);
                if (riskCodeList.size() > 0) {
                    resultMap.put("data", riskCodeList);
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", "获取日常计划险种编码成功！");
                } else {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "当前暂无在售的日常产品！");
                }
            } else {
                //根据福利编码查询福利的状态
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
                //如果他的福利状态为初审岗定制，则返回现在没有能够定制日常计划的险种
                if (ConstantUtil.EnsureState_012.equals(fcEnsure.getEnsureState())) {
                    List<Map<String, String>> riskCodeList = fdRiskInfoMapper.selectRiskCodeByPlanType(planType);
                    if (riskCodeList.size() > 0) {
                        resultMap.put("data", riskCodeList);
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                        resultMap.put("message", "获取日常计划险种编码成功！");
                    } else {
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        resultMap.put("message", "当前暂无在售的日常产品！");
                    }
                    //若状态时审核退回
                } else if (ConstantUtil.EnsureState_017.equals(fcEnsure.getEnsureState())) {
                    List<Map<String, String>> riskCodeList = fdRiskInfoMapper.selectRiskCodeByPlanType(planType);
                    if (riskCodeList.size() > 0) {
                        resultMap.put("data", riskCodeList);
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                        resultMap.put("message", "获取日常计划险种编码成功！");
                    } else {
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        resultMap.put("message", "该福利下的产品已下架，无法修改！");
                    }
                } else if (ConstantUtil.EnsureState_015.equals(fcEnsure.getEnsureState()) && "1".equals(map.get("changePlanFlag"))) {
                    List<Map<String, String>> riskCodeList = fdRiskInfoMapper.selectRiskCodeByPlanType(planType);
                    if (riskCodeList.size() > 0) {
                        resultMap.put("data", riskCodeList);
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                        resultMap.put("message", "获取日常计划险种编码成功！");
                    } else {
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        resultMap.put("message", "该日常产品已下架，不支持变更操作");
                    }
                } else {
                    //查询该福利选择的险种编码以及险种名称
                    List<Map<String, String>> list = fdRiskInfoMapper.selectRiskNameAndRiskCodeByEnsureCode(ensureCode);
                    resultMap.put("data", list);
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", "获取日常计划险种编码成功！");
                }
            }
        } catch (Exception e) {
            Log.info("获取日常计划险种编码失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "获取日常计划险种比编码失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    public String queryPlanListByRiskCode(String Authorization, String riskCode, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //获取已选定的计划
            Object[] insuredType = null;
            //如果前台传参ensureCode为空，说明是新增入库，此时的福利编号还未生成，故选中的计划也为空
            if (ensureCode == null || ensureCode.equals("")) {
                insuredType = null;
            } else {
                List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate(ensureCode);
                ArrayList list = new ArrayList();
                for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos) {
                    String planCode = fcDailyInsureRiskDetailInfo.getPlanCode();
                    if (!list.contains(planCode)) {
                        list.add(planCode);
                    }
                }
                insuredType = list.toArray();
            }
            //根据险种查询计划列表
            List<FdRIskPlanInfo> fdRIskPlanInfos = fdRiskInfoMapper.selectByRiskCode(riskCode);
            if (riskCode.equals("14110")) {
                fdRIskPlanInfos.clear();
            }
            //根据险种编码查询险种名称
            String riskName = fdRiskInfoMapper.selectRiskNameByRiskCode(riskCode);
            resultMap.put("riskName", riskName);
            resultMap.put("insuredPlan", insuredType);
            resultMap.put("planList", fdRIskPlanInfos);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "获取日常计划险种信息成功！");
        } catch (Exception e) {
            Log.info("获取日常计划险种信息失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "获取日常计划险种信息失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 初审岗定制日常计划基础单添加人员时根据险种以及年龄判断去展示不同的保险期间、缴费频次、缴费期间，出生日期不录入时不展示任何信息。
     * order by wuShiHao    2021/2/3
     *
     * @param dailyInsureInfo
     * @return
     */
    public String autoGetPlanInfoByAgeAndRiskCode(DailyInsureInfo dailyInsureInfo) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> mapOne = new HashMap<>();
        try {
            //如果是安颐无忧年金
            if ("14110".equals(dailyInsureInfo.getRiskCode())) {
                //得到年龄(该险种下只需要计算年龄，不需要计算出生天数)
                int age = DateTimeUtil.getCurrentAge(dailyInsureInfo.getBirthDay(),
                        StringUtils.isEmpty(dailyInsureInfo.getInsureDate()) ? DateTimeUtil.getCurrentDate()
                                : dailyInsureInfo.getInsureDate());
                Log.info("被保人的年龄为: {}", age);
                dailyInsureInfo.setAge(String.valueOf(age));
                // 如果年龄为0，说明是未满一周岁，此时要判断他的出生天数是否是大于等于28天
                if (age == 0) {
                    int days = (int) DateTimeUtil.getDistanceDays(dailyInsureInfo.getBirthDay(),
                            DateTimeUtil.getCurrentDate());
                    if (days < 28) {
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        resultMap.put("message", "被保人的出生天数必须大于等于28天！");
                        return JSON.toJSONString(resultMap);
                    }
                } else if (age > 80) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "被保人年龄不符合最大投保年龄 80 周岁 要求！");
                    return JSON.toJSONString(resultMap);
                }
                //根据年龄查询保险期间
                List<Map<String, String>> dailyInsurePeriodList = fdDaliyRiskInsureConfigMapper.selectDailyInsurePeriodInfo(dailyInsureInfo);
                Log.info("该被保人的保险期间可选项为: {}", dailyInsurePeriodList);
                mapOne.put("dailyInsurePeriodList", dailyInsurePeriodList);
                //查询年龄和保险期间查询对应的交费频次
                List<Map<String, String>> paymentFrequencyList = new ArrayList<>();
                if ("".equals(dailyInsureInfo.getDailyInsurePeriodType()) || null == dailyInsureInfo.getDailyInsurePeriodType()) {
                    mapOne.put("paymentFrequencyList", paymentFrequencyList);
                    Log.info("该被保人的保险期间可选项为: {}", paymentFrequencyList);
                } else {
                    paymentFrequencyList = fdDaliyRiskInsureConfigMapper.selectPaymentFrequencyInfo(dailyInsureInfo);
                    mapOne.put("paymentFrequencyList", paymentFrequencyList);
                    Log.info("该被保人的保险期间可选项为: {}", paymentFrequencyList);
                }
                //根据年龄、保险期间和交费频次来展示交费期间
                List<Map<String, String>> paymentPeriodList = new ArrayList<>();
                if ("".equals(dailyInsureInfo.getPaymentFrequencyType()) || null == dailyInsureInfo.getPaymentFrequencyType() ||
                        "".equals(dailyInsureInfo.getDailyInsurePeriodType()) || null == dailyInsureInfo.getDailyInsurePeriodType()) {
                    Log.info("该被保人的交费期间为: {}", paymentPeriodList);
                    mapOne.put("paymentPeriodList", paymentPeriodList);
                } else {
                    paymentPeriodList = fdDaliyRiskInsureConfigMapper.selectPaymentPeriodInfo(dailyInsureInfo);
                    Log.info("该被保人的交费期间为: {}", paymentPeriodList);
                    mapOne.put("paymentPeriodList", paymentPeriodList);
                }
            }
            resultMap.put("date", mapOne);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "动态展示成功！");
        } catch (Exception e) {
            Log.info("动态展示报错：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "动态展示失败！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 日常计划基础单添加人员相关文件上传
     *
     * @param files
     * @return
     */
    public String personnelFileUpload(Map<String, MultipartFile> files, String ensureCode) {
        Map<String, Object> filePathMap = new HashMap<>();
        filePathMap.put("success", false);
        filePathMap.put("code", "500");
        try {
            //查询ftp信息上传到SFTP
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            //获取文件上传相对路径  -日常计划基础单添加人员影像件文件夹
            String ftpPath = FileUtil.getFtpPath("0909", "");
            String ftpFilePath = fdftpInfo.getFtprootpath() + ftpPath + ensureCode + "/";
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
            filePathMap = sFtp.uploadSftp(files, ftpFilePath, filePathMap);
            if ("200".equals(filePathMap.get("code"))) {
                filePathMap.put("success", true);
                filePathMap.put("message", "日常计划添加员工相关影像件上传成功");
            }
        } catch (Exception e) {
            Log.info("文件上传：" + e);
            filePathMap.put("message", "日常计划添加员工相关影像件上传失败");
        }
        return JSON.toJSONString(filePathMap);
    }

    /**
     * 复核退回HR/后台定制
     *
     * @param token
     * @param ensureCode
     * @param returnNode   0--退回HR定制   1--退回后台定制
     * @param returnReason
     * @return
     */
    @Transactional
    public String dailyPlanReturn(String token, String ensureCode, String returnNode, String returnReason) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            String error = "";
            GlobalInput globalInput = userService.getSession(token);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if (StringUtils.isBlank(ensureCode)) {
                error = "日常计划回退失败：福利编号缺失，请联系平台维护人员。";
            } else if (StringUtils.isBlank(returnNode)) {
                error = "日常计划回退失败：退回节点不可为空。";
            } else if (StringUtils.isBlank(returnReason)) {
                error = "日常计划回退失败：退回原因不可为空。";
            }
            if (StringUtils.isNotBlank(error)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", error);
                return JSON.toJSONString(resultMap);
            }
            //退回 新增之前，如果已经退回，删除原来数据configNo=10或12的
            int count = fcEnsureConfigMapper.selectEnsureConfigByEnsureCode(ensureCode);
            if (count != 0) {
                fcEnsureConfigMapper.deleteByEnsureCode(ensureCode);
            }
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            List<FCEnsureConfig> fcEnsureConfigList = new ArrayList<>();
            FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
            fcEnsureConfig.setEnsureCode(ensureCode);
            fcEnsureConfig.setGrpNo(fcEnsure.getGrpNo());
            fcEnsureConfig.setConfigNo("010");
            fcEnsureConfig.setConfigValue(returnReason);
            fcEnsureConfig.setOperator(globalInput.getUserNo());
            fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
            fcEnsureConfig = CommonUtil.initObject(fcEnsureConfig, "INSERT");
            fcEnsureConfigList.add(fcEnsureConfig);
            fcEnsureConfigMapper.inserts(fcEnsureConfigList);

            //复核退回Hr定制
            if ("0".equals(returnNode)) {
                fcEnsure.setEnsureState(EnsureStateEnum.RETURNHRCUSTOMIZATION.getCode());
            }//复核退回后台定制
            else if ("1".equals(returnNode)) {
                fcEnsure.setEnsureState(EnsureStateEnum.ADMINISTRATORRETURNCUSTOMBACKGROUND.getCode());
            }//HR退回至初审
            else if ("2".equals(returnNode)) {
                fcEnsure.setEnsureState(EnsureStateEnum.HRRETURNCUSTOMBACKGROUND.getCode());
            }
            fcEnsure.setOperator(globalInput.getUserNo());
            fcEnsure = (FCEnsure) CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKey(fcEnsure);
            Log.info("日常计划复核退回：" + (fcEnsure.getEnsureState().equals("04") ? "HR" : "后台"));
            if ("0".equals(returnNode)) {
                String Phone = fcEnsureContactMapper.selectByPrimaryKey(ensureCode).getMobilePhone();
                //短信发送
                SendSMSReq sendSMSReq = new SendSMSReq();
                sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_006.getCode());
                sendSMSReq.setPhones(Phone);
                Map<String, Object> map = new HashMap<>();
                map.put("ensure_name", fcEnsure.getEnsureName());
                sendSMSReq.setParam(map);
                SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
                if (sendMessageResp.getSuccess()) {
                    resultMap.put("fcEnsure", fcEnsure);
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", "日常计划已成功退回。");
                }
            }
            resultMap.put("fcEnsure", fcEnsure);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "日常计划已成功退回。");
        } catch (Exception e) {
            Log.info(e.getMessage());
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    public String selectError(String authorization, String ensureCode) {
        Map params = new HashMap();
        params.put("ensureCode", ensureCode);
        params.put("configNo", "500");
        return fcEnsureConfigMapper.selectOnlyValue(params);
    }
}