package com.sinosoft.eflex.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageHelper;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.MenuGrpCodeTypeEnum;
import com.sinosoft.eflex.enums.SMSTemplateNoEnum;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.sendmessage.SendMessageResp;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.encrypt.LisIDEA;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-08-13 17:35
 */
@Service("RegistRegService")
@Slf4j
@RequiredArgsConstructor
public class RegistRegService {
    private final FCGrpInfoMapper fcGrpInfoMapper;
    private final RegAuditMapper regAuditMapper;
    private final MaxNoService maxNoService;
    private final FcGrpRegAuditMapper fcGrpRegAuditMapper;
    private final FCEnsureConfigMapper fcEnsureConfigMapper;
    private final FcGrpContactMapper fcGrpContactMapper;
    private final FCContactGrpRelaMapper fcContactGrpRelaMapper;
    private final FCGrpInfoMapper fCGrpInfoMapper;
    private final FdUserMapper fdUserMapper;
    private final UserService userService;
    private final FDPwdHistMapper fdPwdHistMapper;
    private final SendMessageService sendMessageService;
    private final FDUserRoleMapper fdUserRoleMapper;
    private final FDusertomenugrpMapper fDusertomenugrpMapper;
    private final ImageConvertService imageConvertService;


    /**
     * <AUTHOR>
     * @description 将注册信息以列表形式显示
     * @date 17:56 17:56
     * @modified
     */
    public String findByPage(String token, RegistRegFind registRegFind, Integer page, Integer rows) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            String manageCom = globalInput.getManageCom();
            if (StringUtils.isEmpty(manageCom)) {
                throw new SystemException("初审岗用户管理机构为空！");
            } else {
                registRegFind.setManageCom(manageCom);
            }
            if (StringUtil.isEmpty(page) && StringUtil.isEmpty(rows)) {
                resultMap.put("message", "传入的数据为空");
                return JSON.toJSONString(resultMap);
            }
            PageHelper.startPage(page, rows);
            //去除空格  add by wudezhong
            if (registRegFind.getName() != null) {
                registRegFind.setName(registRegFind.getName().trim());

            }
            if (registRegFind.getGrpName() != null) {
                registRegFind.setGrpName(registRegFind.getGrpName().trim());
            }
            if (registRegFind.getIDNo() != null) {
                registRegFind.setIDNo(registRegFind.getIDNo().trim());
            }
            if (registRegFind.getMobilePhone() != null) {
                registRegFind.setMobilePhone(registRegFind.getMobilePhone().trim());
            }
            List<HashMap<String, Object>> registResult = regAuditMapper.findPageInfo(registRegFind);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (HashMap<String, Object> map : registResult) {
                Date date = (Date) map.get("MakeDate");
                String str = sdf.format(date) + " " + map.get("MakeTime");
                map.put("applicationDate", str);

                //脱敏处理
                if (!StringUtils.isEmpty(map.get("IDNo"))) {
                    map.put("IDNo", Base64AndMD5Util.base64SaltEncode(map.get("IDNo").toString(), "130"));
                }
                if (!StringUtils.isEmpty(map.get("MobilePhone"))) {
                    map.put("MobilePhone", Base64AndMD5Util.base64SaltEncode(map.get("MobilePhone").toString(), "130"));
                }

            }
            PageHelperUtil<HashMap<String, Object>> teamPageInfo = new PageHelperUtil<>(registResult);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("list", teamPageInfo.getList());
            resultMap.put("data", dataMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询信息成功");
        } catch (Exception e) {
            log.info("查询信息失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询信息失败，" + e.getMessage());
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询所有的姓名
     */
    public String findAllName() {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //查询HR姓名
            List<String> hrNameList = regAuditMapper.selectAllhrName();
            //查询企业名称
            List<String> grpNameList = regAuditMapper.selectAllgrpName();

            resultMap.put("HRNameList", hrNameList);
            resultMap.put("GrpNameList", grpNameList);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询成功");
        } catch (Exception e) {
            log.info("查询失败", e);
            resultMap.put("message", "查询失败");
            resultMap.put("code", "500");
            resultMap.put("success", false);
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * <AUTHOR>
     * @description 查询企业信息和联系人信息以及审核详情
     * @date 11:52 11:52
     * @modified
     */
    public String getPersonInfo(String registSN) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (registSN == null || "".equals(registSN)) {
                resultMap.put("message", "流水号不能为空");
                return JSON.toJSONString(resultMap);
            }
            //查询企业信息和联系人信息以及审核详情
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("registSN", registSN);
            List<HrRegist> hrRegistContactList = fcGrpContactMapper.selectContactsTemp(params);
            hrRegistContactList = imageConvertService.convertHrRegister(hrRegistContactList);
            if (hrRegistContactList.size() < 1) {
                resultMap.put("message", "联系人信息不存在");
                return JSON.toJSONString(resultMap);
            }
            List<HrRegist> hrRegistInfoList = fCGrpInfoMapper.selectGrpInfoListTemp(params);
            hrRegistInfoList = imageConvertService.convertHrRegister(hrRegistInfoList);
            if (hrRegistInfoList.size() < 1) {
                resultMap.put("message", "该企业信息不存在");
                return JSON.toJSONString(resultMap);
            }
            //查询注册审核信息
            HrRegist regAudit = fcGrpRegAuditMapper.selectTranscodingAuditResult(registSN);
            //查询股东业务配置
            resultMap.put("hrRegistContactList", hrRegistContactList);
            resultMap.put("hrRegistInfoList", hrRegistInfoList);
            resultMap.put("regAudit", regAudit);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询成功");
        } catch (Exception e) {
            log.info("查询失败", e);
            resultMap.put("message", "查询失败");
            resultMap.put("code", "500");
            resultMap.put("success", false);
        }
        return JSON.toJSONString(resultMap, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 查询注册后的保障配置信息
     */
    public String getEnsureConfigInfo(String grpNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (grpNo == null || "".equals(grpNo)) {
                resultMap.put("message", "企业编号不能为空");
                return JSON.toJSONString(resultMap);
            }
            //查询企业保障配置信息
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("grpNo", grpNo);
            List<FCEnsureConfig> fcEnsureConfigList = fcEnsureConfigMapper.selectFcensureconfigList(params);
            resultMap.put("fcEnsureConfig", fcEnsureConfigList);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询成功");
        } catch (Exception e) {
            log.info("查询失败", e);
            resultMap.put("message", "查询失败");
            resultMap.put("code", "500");
            resultMap.put("success", false);
        }
        return JSON.toJSONString(resultMap, SerializerFeature.WriteMapNullValue);
    }

    /**
     * <AUTHOR>
     * @description将审核结果插入数据库
     * @date 17:05 17:05
     * @modified
     * @update by wudezhong on 2019/4/23
     */
    @Transactional(isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED)
    public String insertRegInfo(RegInfo regInfo, String registSN, String token) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        String contactStatus = "";//1存在
        try {
            if (regInfo == null && registSN.isEmpty()) {
                resultMap.put("message", "传入的数据不能为空");
                return JSON.toJSONString(resultMap);
            }
            //****************
            //校验法人代表和企业注册电话
            String corporationMan = regInfo.getCorporationMan();
            String telphone = regInfo.getTelphone();
            if (!StringUtil.isEmpty(telphone)) {
                String s = CheckUtils.checkTel(telphone);
                if (!s.equals("")) {
                    return JSON.toJSONString(ResultUtil.error(s));
                }
            }
            if (!StringUtil.isEmpty(corporationMan)) {
                String name = "";
                if (CheckUtils.checkcountname(corporationMan)) {
                    name = CheckUtils.checkChineseName(corporationMan);
                } else {
                    name = CheckUtils.checkEnglishName(corporationMan);
                    String trim = corporationMan.trim();
                    corporationMan = trim.replaceAll(" +", " ");
                }

                if (!name.equals("")) {
                    return JSON.toJSONString(ResultUtil.error(name));
                }
            }
            String auditResult = regInfo.getAuditResult();
            String grpNo = "";

            //首先获取临时表信息
            HrRegist hrRegist = fcGrpContactMapper.selectAllFCHrRegistTemp(registSN);

            /**审核通过*/
            if ("1".equals(auditResult)) {
                //一个HR对应多个企业判断联系人是否已存在
                FdUser fdUser = new FdUser();
                Map<String, String> params = new HashMap<>();
                params.put("idType", hrRegist.getIdType());
                params.put("idNo", hrRegist.getIdNo());
                //判断是否已经存在
                if (fcGrpContactMapper.isExist(params) != 0) {
                    contactStatus = "1";
                    //获取FDuser的信息
                    fdUser = fdUserMapper.findUserByIdno(hrRegist.getIdNo());
                } else {
                    contactStatus = "0";
                    //用户信息表
                    fdUser = insertFdUser(hrRegist);
                }

                /*企业信息表*/
                FCGrpInfo fcGrpInfo = insertGrpInfo(hrRegist, fdUser);
                fcGrpInfoMapper.insert(fcGrpInfo);
                /*企业联系人表*/
                Map<String, Object> map = insertFcGrpContact(hrRegist, fdUser, fcGrpInfo);
                FcGrpContact fcGrpContact = (FcGrpContact) map.get("fcGrpContact");
                String contactState = map.get("contactState").toString();//1：新增，0：修改
                if ("1".equals(contactState)) {
                    fcGrpContactMapper.insertSelective(fcGrpContact);
                } else {
                    fcGrpContactMapper.updateByPrimaryKeySelective(fcGrpContact);
                }
                /*企业与联系人关联表*/
                FCContactGrpRela fcContactGrpRela = initFCContactGrpRela(fcGrpContact, fcGrpInfo, globalInput);
                fcContactGrpRelaMapper.insert(fcContactGrpRela);
                /*将注册信息插入注册审核表中*/
                FcGrpRegAudit fcGrpRegAudit = insertFcGrpRegAudit(fcGrpContact, fcGrpInfo, fdUser, auditResult, regInfo);
                fcGrpRegAuditMapper.insert(fcGrpRegAudit);
                //HR已经注册后这些表不会存
                if ("0".equals(contactStatus)) {
                    /*用户与菜单表*/
                    FDusertomenugrpKey fDusertomenugrpKey = initFDusertomenugrpKey(fdUser);
                    fDusertomenugrpMapper.insert(fDusertomenugrpKey);
                    /*密码历史表*/
                    FDPwdHist fdPwdHist = insertFdPwdHist(hrRegist, fdUser);
                    fdPwdHistMapper.insert(fdPwdHist);
                    /*用户角色表*/
                    FDUserRole fdUserRole = insertFDUserRole(fdUser);
                    fdUserRoleMapper.insert(fdUserRole);
                    /*用户表*/
                    fdUser.setCustomNo(fcGrpContact.getContactNo());
                    fdUserMapper.insert(fdUser);
                }
                //股东业务配置
                hrRegist.setShareBusiness(regInfo.getShareBusiness());
                hrRegist.setShareholdersName(regInfo.getShareholdersName());
                fcGrpContactMapper.updateByshareholders(hrRegist);
                /*福利配置表*/
                int num = fcEnsureConfigMapper.selectOnceFcensureconfig(fcGrpInfo.getGrpNo());
                if (num > 0) {
                    fcEnsureConfigMapper.deleteShareholdersByGrpNo(fcGrpInfo.getGrpNo());
                }
                //将股东业务配置插入到数据库
                String shareBusiness = regInfo.getShareBusiness();
                FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
                //0代表否，1代表是
                if ("0".equals(shareBusiness)) {
                    fcEnsureConfig.setConfigNo("007");
                    fcEnsureConfig.setConfigValue("N");
                } else {
                    fcEnsureConfig.setConfigNo("007");
                    fcEnsureConfig.setConfigValue("Y");
                    if (!insertEnsureConfig(regInfo, token, fcGrpInfo.getGrpNo())) {
                        resultMap.put("message", "插入企业保障配置失败");
                        return JSON.toJSONString(resultMap);
                    }
                }
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", null, 20));
                fcEnsureConfig.setGrpNo(fcGrpInfo.getGrpNo());
                fcEnsureConfig.setOperator(globalInput.getUserName());
                fcEnsureConfigMapper.insert(CommonUtil.initObject(fcEnsureConfig, "INSERT"));
                /*用户表修改状态*/
                FdUser fdUser2 = fdUserMapper.selectByPrimaryKey(fdUser.getUserNo());
                //如果审核结果为通过，则修改用户状态为有效
                fdUser2.setUserState("1");
                fdUserMapper.updateUserState(CommonUtil.initObject(fdUser2, "UPDATE"));
                grpNo = fcGrpInfo.getGrpNo();
            }
            //配置短信发送内容
            SendSMSReq sendSMSReq = new SendSMSReq();
            Map<String, Object> map1 = new HashMap<>();
            String sendMessage = "";
            switch (auditResult) {
                case "1":
                    if ("0".equals(contactStatus)) {
                        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_002.getCode());
                        sendMessage = "尊敬的用户：感谢注册的横琴人寿弹福利平台！您的注册审核已通过，登记账户为您的身份证号、初始密码为您身份证后六位，请及时登录修改密码并定制福利计划，如有疑问，请随时与您的业务经理联系。";
                    } else {
                        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_003.getCode());
                        sendMessage = "尊敬的用户：感谢注册的横琴人寿弹福利平台！您的注册审核已通过。如有疑问，请随时与您的业务经理联系。";
                    }
                    break;
                case "2":
                    sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_004.getCode());
                    map1.put("audit_opinion", regInfo.getAuditOpinion());
                    sendMessage = "尊敬的用户,您注册的企业审核未通过,未通过原因:[" + regInfo.getAuditOpinion() + "],请登录平台进行修改提交!";
                    break;
            }
            //短信发送
            sendSMSReq.setPhones(hrRegist.getMobilePhone());
            map1.put("grp_name", hrRegist.getGrpName());
            sendSMSReq.setParam(map1);
            SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
            log.info("手机号" + hrRegist.getMobilePhone());
            log.info("发送信息" + sendMessage);
            log.info("是否成功" + sendMessageResp.getSuccess());
            //修改临时表审核状态
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("registSN", registSN);
            paramMap.put("grpNo", grpNo);
            paramMap.put("checkStatus", auditResult);
            paramMap.put("auditOpinion", regInfo.getAuditOpinion());
            paramMap.put("oprator", globalInput.getUserNo());
            paramMap.put("currentDate", DateTimeUtil.getCurrentDate());
            paramMap.put("currentTime", DateTimeUtil.getCurrentTime());
            fcGrpRegAuditMapper.updateFCHrRegistTemp(paramMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "提交信息成功");
        } catch (Exception e) {
            log.info("提交信息失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "提交信息失败");
            throw new RuntimeException("提交信息失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description股东名称
     * @date 17:24 17:24
     * @modified
     */
    public boolean insertEnsureConfig(RegInfo regInfo, String token, String grpNo) {
        GlobalInput globalInput = userService.getSession(token);
        FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
        String serialNo = maxNoService.createMaxNo("EnsureConfig", null, 20);
        fcEnsureConfig.setConfigNo("009");
        fcEnsureConfig.setConfigValue(regInfo.getShareholdersName());
        fcEnsureConfig.setSerialNo(serialNo);
        fcEnsureConfig.setGrpNo(grpNo);
        fcEnsureConfig.setOperator(globalInput.getUserNo());
        fcEnsureConfigMapper.insert(CommonUtil.initObject(fcEnsureConfig, "INSERT"));
        return true;
    }

    /**
     * <AUTHOR>
     * @description将注册信息存入企业信息表
     * @date 16:03 16:03
     * @modified
     */
    public FCGrpInfo insertGrpInfo(HrRegist hrRegist, FdUser fdUser) {
        FCGrpInfo fcGrpInfo = new FCGrpInfo();
        //企业客户号
        fcGrpInfo.setGrpNo(maxNoService.createMaxNo("GrpNo", null, 20));
        //公司名称
        fcGrpInfo.setGrpName(hrRegist.getGrpName());
        //邮编
        fcGrpInfo.setZipCode(hrRegist.getZipCode());
        //企业证件图片1
        fcGrpInfo.setGrpIDImage1(hrRegist.getGrpIDImage1());
        fcGrpInfo.setGrpImageFront(hrRegist.getGrpImageFront());

        //企业证件图片2
        fcGrpInfo.setGrpIDImage2(hrRegist.getGrpIDImage2());
        fcGrpInfo.setGrpImageBack(hrRegist.getGrpImageBack());
        //公司性质
        fcGrpInfo.setGrpType(hrRegist.getGrpType());
        fcGrpInfo.setGrpNatureType(hrRegist.getGrpNatureType());

        //开户账户名名称
        fcGrpInfo.setAccName(hrRegist.getAccName());
        //开户账号
        fcGrpInfo.setGrpBankAccNo(hrRegist.getGrpBankaccno());
        //统一社会信用代码
        fcGrpInfo.setUnifiedsociCode(hrRegist.getUnifiedsociCode());
        // 企业证件类型
        fcGrpInfo.setGrpIdType(hrRegist.getGrpIdType());
        // 企业证件号
        fcGrpInfo.setGrpIdNo(hrRegist.getGrpIdNo());
        //注册地址
        fcGrpInfo.setRegaddress(hrRegist.getRegAddress());
        //开户编码*
        fcGrpInfo.setGrpBankCode(hrRegist.getGrpBankcode());
        //企业注册电话号
        fcGrpInfo.setTelphone(hrRegist.getTelphone());
        //企业地址
        fcGrpInfo.setGrpAddRess(hrRegist.getGrpAddress());
        //业务员工号
        fcGrpInfo.setClientno(hrRegist.getClientNo());
        //企业法人代表
        fcGrpInfo.setCorporationMan(hrRegist.getCorporationMan());
        //f法人证件1
        fcGrpInfo.setLegIDImage1(hrRegist.getLegIDImage1());
        //法人证件2
        fcGrpInfo.setLegIDImage2(hrRegist.getLegIDImage2());
        fcGrpInfo.setLegalImgBack(hrRegist.getLegalImgBack());

        //法人证件2
        fcGrpInfo.setLegalImgFront(hrRegist.getLegalImgFront());
        //营业期限
        fcGrpInfo.setBusinessTerm(hrRegist.getBusinessTerm());
        // 所属行业
        fcGrpInfo.setTrade(hrRegist.getTrade());
        //企业总人数
        fcGrpInfo.setPeoples(hrRegist.getPeoples());
        //企业有效起期
        fcGrpInfo.setGrpTypeStartDate(hrRegist.getGrpTypeStartDate());
        //企业有效止期
        fcGrpInfo.setGrpTypeEndDate(hrRegist.getGrpTypeEndDate());
        //企业成立日期
        fcGrpInfo.setGrpEstablishDate(hrRegist.getGrpEstablishDate());
        //企业规模类型
        fcGrpInfo.setGrpScaleType(hrRegist.getGrpScaleType());
        //参加社会统筹标志
        fcGrpInfo.setSociologyPlanSign(hrRegist.getSociologyPlanSign());
        //注册资本
        fcGrpInfo.setRegisteredCapital(hrRegist.getRegisteredCapital());
        //客户类别
        fcGrpInfo.setGrpCategory(hrRegist.getGrpCategory());
        //企业信息操作员
        fcGrpInfo.setOperator(fdUser.getUserNo());
        //企业法定代表人证件号
        fcGrpInfo.setLegID(hrRegist.getLegID());
        //企业法定代表人性别
        fcGrpInfo.setLegSex(hrRegist.getLegSex());
        //企业法定代表人出生日期
        fcGrpInfo.setLegBirthday(hrRegist.getLegBirthday());
        //企业法定代表人国籍
        fcGrpInfo.setLegNationality(hrRegist.getLegNationality());
        //企业法定代表人证件类型
        fcGrpInfo.setLegIDType(hrRegist.getIdType());
        //企业法定代表人证件有效起期
        fcGrpInfo.setLegIDStartDate(hrRegist.getLegIDStartDate());
        //企业法定代表人证件有效止期
        fcGrpInfo.setLegIDEndDate(hrRegist.getLegIDEndDate());
        //企业注册地
        fcGrpInfo.setGrpRegisterAddress(hrRegist.getGrpRegisterAddress());
        fcGrpInfo.setBusinesses(hrRegist.getBusinesses());
        return CommonUtil.initObject(fcGrpInfo, "INSERT");
    }

    /**
     * <AUTHOR>
     * @description将注册信息存入用户表
     * @date 16:03 16:03
     * @modified
     */
    public FdUser insertFdUser(HrRegist hrRegist) {
        FdUser fdUser = new FdUser();
        LisIDEA encryPassword = new LisIDEA();
        //用户编号
        fdUser.setUserNo(maxNoService.createMaxNo("UserNo", null, 20));
        //用户类型
        fdUser.setCustomType("2");
        //登陆姓名为证件号
        fdUser.setUserName(hrRegist.getIdNo());
        //昵称
        fdUser.setNickName(hrRegist.getName());
        //hr证件号
        fdUser.setIDNo(hrRegist.getIdNo());
        //用户密码为证件号后6位
        fdUser.setPassWord(encryPassword.encryptString(hrRegist.getIdNo().substring(hrRegist.getIdNo().length() - 6)));
        //手机号
        fdUser.setPhone(hrRegist.getMobilePhone());
        //是否锁定
        fdUser.setIsLock("0");
        //是否是VIP
        fdUser.setIsVIP("N");
        //用户状态
        fdUser.setUserState("0");
        //屏蔽首次登录功能 ： 为1时不用首次登录验证。为空时需要
        fdUser.setPWDState("0");
        //登录失败次数
        fdUser.setLoginFailTimes(0);
        //操作员
        fdUser.setOperator(fdUser.getUserNo());
        fdUser = (FdUser) CommonUtil.initObject(fdUser, "INSERT");
        return fdUser;
    }

    /**
     * <AUTHOR>
     * @description将注册信息存入联系人表
     * @date 16:03 16:03
     * @modified
     */
    public Map<String, Object> insertFcGrpContact(HrRegist hrRegist, FdUser fdUser, FCGrpInfo fcGrpInfo) {
        String contactState = "0";
        //判断是更新还是插入
        FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpContact(hrRegist.getIdNo());//之前有数据
        if (fcGrpContact == null) {
            fcGrpContact = new FcGrpContact();
            contactState = "1";
            //联系人编号
            fcGrpContact.setContactNo(maxNoService.createMaxNo("ContactNo", null, 20));
        }
        //联系人类型
        fcGrpContact.setContactType("01");
        //联系人姓名
        fcGrpContact.setName(hrRegist.getName());
        //性别
        fcGrpContact.setSex(hrRegist.getSex());
        //国籍
        fcGrpContact.setNativeplace(hrRegist.getNativeplace());
        //证件号
        fcGrpContact.setIdNo(hrRegist.getIdNo());
        //证件有效止期
        fcGrpContact.setIdTypeEndDate(hrRegist.getIdTypeEndDate());
        fcGrpContact.setIdTypeStartDate(hrRegist.getIdTypeStartDate());
        //所属部门
        fcGrpContact.setDepartment(hrRegist.getDepartment());
        //手机号码
        fcGrpContact.setMobilePhone(hrRegist.getMobilePhone());
        //邮箱
        fcGrpContact.setEmail(hrRegist.getEmail());
        //证件图片1(正面)
        fcGrpContact.setIdImage1(hrRegist.getIdImage1());
        //证件图片2(反面)
        fcGrpContact.setIdImage2(hrRegist.getIdImage2());
        //证件类型
        fcGrpContact.setIdType(hrRegist.getIdType());
        //出生日期
        fcGrpContact.setBirthDay(hrRegist.getBirthday());
        //企业联系人表-操作员
        fcGrpContact.setOperator(fdUser.getUserNo());
        //企业联系人表-企业客户号
        fcGrpContact.setGrpNo(fcGrpInfo.getGrpNo());
        fcGrpContact = CommonUtil.initObject(fcGrpContact, "INSERT");
        Map<String, Object> map = new HashMap<>();
        map.put("fcGrpContact", fcGrpContact);
        map.put("contactState", contactState);
        return map;
    }

    public FCContactGrpRela initFCContactGrpRela(FcGrpContact fcGrpContact, FCGrpInfo fcGrpInfo, GlobalInput globalInput) {
        FCContactGrpRela fcContactGrpRela = new FCContactGrpRela();
        fcContactGrpRela.setContactNo(fcGrpContact.getContactNo());
        fcContactGrpRela.setContactType("01");
        fcContactGrpRela.setLockState("0");
        fcContactGrpRela.setGrpNo(fcGrpInfo.getGrpNo());
        fcContactGrpRela.setOperator(fcGrpInfo.getOperator());
        fcContactGrpRela = CommonUtil.initObject(fcContactGrpRela, "INSERT");
        return fcContactGrpRela;
    }

    public FDusertomenugrpKey initFDusertomenugrpKey(FdUser fdUser) {
        FDusertomenugrpKey fDusertomenugrpKey = new FDusertomenugrpKey();
        //菜单表的用户编号
        fDusertomenugrpKey.setUserNo(fdUser.getUserNo());
        //绑定菜单
        fDusertomenugrpKey.setMenuGrpCode(MenuGrpCodeTypeEnum.HR.getCode());
        return fDusertomenugrpKey;
    }

    /**
     * <AUTHOR>
     * @description将注册日期及密码存入历史密码表将注册日期及密码存入历史密码表
     * @date 16:02 16:02
     * @modified
     */
    public FDPwdHist insertFdPwdHist(HrRegist hrRegist, FdUser fdUser) {
        FDPwdHist fdPwdHist = new FDPwdHist();
        //登录密码流水号
        fdPwdHist.setPassWordSN(maxNoService.createMaxNo("PassWordSN", null, 20));
        fdPwdHist = (FDPwdHist) CommonUtil.initObject(fdPwdHist, "INSERT");
        //角色表用户密码
        fdPwdHist.setPassWord(fdUser.getPassWord());
        //密码历史表用户编码
        fdPwdHist.setUserNo(fdUser.getUserNo());
        //密码历史表操作员
        fdPwdHist.setOperator(fdUser.getUserNo());
        return fdPwdHist;
    }

    /**
     * <AUTHOR>
     * @description 将注册信息存入注册审核表中
     * @date 19:25 19:25
     * @modified
     */
    public FcGrpRegAudit insertFcGrpRegAudit(FcGrpContact fcGrpContact, FCGrpInfo fcGrpInfo, FdUser fdUser, String auditResult, RegInfo regInfo) {
        FcGrpRegAudit fcGrpRegAudit = new FcGrpRegAudit();
        //企业注册流水号
        fcGrpRegAudit.setGrpRegNo(maxNoService.createMaxNo("grpRegNo", null, 20));
        //审核结论
        fcGrpRegAudit.setAuditResult("0");
        CommonUtil.initObject(fcGrpRegAudit, "INSERT");
        //hr客户编号
        fcGrpRegAudit.setContactNo(fcGrpContact.getContactNo());
        //企业客户号
        fcGrpRegAudit.setGrpNo(fcGrpInfo.getGrpNo());
        //审核状态操作员
        fcGrpRegAudit.setOperator(fdUser.getUserNo());
        fcGrpRegAudit.setAuditResult(auditResult);
        fcGrpRegAudit.setAuditOpinion(regInfo.getAuditOpinion());
        return fcGrpRegAudit;
    }


    /**
     * <AUTHOR>
     * @description 将注册信息存入用户角色表
     * @date 19:27 19:27
     * @modified
     */
    public FDUserRole insertFDUserRole(FdUser fdUser) {
        FDUserRole fdUserRole = new FDUserRole();
        //用户角色流水号
        fdUserRole.setUserRoleSN(maxNoService.createMaxNo("UserRoleSN", null, 20));
        //角色类型
        fdUserRole.setRoleType("2");
        CommonUtil.initObject(fdUserRole, "INSERT");
        //角色表用户编号
        fdUserRole.setUserNo(fdUser.getUserNo());
        //角色操作员
        fdUserRole.setOperator(fdUser.getUserNo());
        return fdUserRole;
    }
}
