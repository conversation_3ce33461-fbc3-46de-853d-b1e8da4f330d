package com.sinosoft.eflex.service.edor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.hqins.common.helper.BeanCopier;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.*;
import com.sinosoft.eflex.model.AddressEntity.CheckCustomerVO;
import com.sinosoft.eflex.model.AddressEntity.EvaluationCustomer;
import com.sinosoft.eflex.model.AddressEntity.IdCardVerifyRequest;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.edor.*;
import com.sinosoft.eflex.service.*;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.rest.exception.BusinessException;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;
import com.thoughtworks.xstream.XStream;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-09-05 17:12
 */
@Service
public class EdorService {

    /**
     * 日志
     */
    private static final Logger log = LoggerFactory.getLogger(EdorService.class);

    @Autowired
    private FDEdorItemMapper fdEdorItemMapper;
    @Autowired
    private FCGrpEdorConfigMapper fcGrpEdorConfigMapper;
    @Autowired
    private FDFTPInfoMapper fdftpInfoMapper;
    @Autowired
    private MyProps myProps;
    @Autowired
    private UserService userService;
    @Autowired
    private FileService fileService;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FCEdorReduInsuredMapper fcEdorReduInsuredMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FcAsyncInfoMapper fcAsyncInfoMapper;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FDCodeZipMapper fdCodeZipMapper;
    @Autowired
    private FCEdorACInfoMapper fcEdorACInfoMapper;
    @Autowired
    private FCEdoruploadfileMapper fcEdoruploadfileMapper;
    @Autowired
    private FCEdorAddPlanInfoMapper fcEdorAddPlanInfoMapper;
    @Autowired
    private FCEdorAddPlanRiskInfoMapper fcEdorAddPlanRiskInfoMapper;
    @Autowired
    private FCEdorAddPlanRiskDutyInfoMapper fcEdorAddPlanRiskDutyInfoMapper;
    @Autowired
    private FcEdorItemMapper fcEdorItemMapper;
    @Autowired
    private FCEdorHomogeneousRiskInsuredMapper fcEdorHomogeneousRiskInsuredMapper;
    @Autowired
    private FCPerInfoTempMapper fcPerInfoTempMapper;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private EdorNIService edorNIService;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private AddressCheckService addressCheckService;

    /**
     * 被保人重要资料变更保全试算类
     */
    @Autowired
    private EdorICBL edorICBL;
    /**
     * 投保人资料变更保全试算类
     */
    @Autowired
    private EdorACBL edorACBL;
    /**
     * 受益人资料变更保全试算类
     */
    @Autowired
    private EdorBCBL edorBCBL;
    /**
     * 新增被保人试算类
     */
    @Autowired
    private EdorNIBL edorNIBL;
    /**
     * 减少被保人试算类
     */
    @Autowired
    private EdorZTBL edorZTBL;
    /**
     * 同质风险加减人试算类
     */
    @Autowired
    private EdorHNIBL edorHNIBL;
    /**
     * 新增被保人导入清单解析类
     */
    @Autowired
    private ImportInsuredNI importInsuredNI;
    /**
     * 新增被保人导入清单解析类
     */
    @Autowired
    private ImportInsuredByPlanNI importInsuredByPlanNI;
    /**
     * 新增被保人申请类
     */
    @Autowired
    private EdorConfirmNI edorConfirmNI;
    /**
     * 同质风险加减人申请类
     */
    @Autowired
    private EdorConfirmHNI edorConfirmHNI;
    /**
     * 减少被保人申请类
     */
    @Autowired
    private EdorConfirmZT edorConfirmZT;
    /**
     * 同质风险加减人解析类
     */
    @Autowired
    private ImportHomogeneousRiskNI importHomogeneousRiskNI;
    /**
     * 投保人资料变更申请类
     */
    @Autowired
    private EdorConfirmAC edorConfirmAC;
    /**
     * 受益人资料变更申请类
     */
    @Autowired
    private EdorConfirmBC edorConfirmBC;
    /**
     * 被保人资料变更申请类
     */
    @Autowired
    private EdorConfirmIC edorConfirmIC;
    @Autowired
    private FCEdorAddInsuredMapper fcEdorAddInsuredMapper;
    @Autowired
    private FdGrpInsureConfigMapper fdGrpInsureConfigMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private FdAsyncThresholdMapper fdAsyncThresholdMapper;

    // 证件类型和性别转码
    public static Map<String, String> getSexByIdType(String idType, String idTypeExcel, List<HashMap<String, Object>> idTypeCodeList, String sex, String sexExcel, List<HashMap<String, Object>> sexCodeList) {
        Map<String, String> map = new HashMap<>();
        for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
            if (idTypeExcel.equals(hashMap1.get("CodeName").toString())) {
                idType = hashMap1.get("CodeKey").toString();
                break;
            }
        }
        map.put("idType", idType);
        for (HashMap<String, Object> hashMap2 : sexCodeList) {
            if (sexExcel.equals(hashMap2.get("CodeName").toString())) {
                sex = hashMap2.get("CodeKey").toString();
                break;
            }
        }
        map.put("sex", sex);
        return map;
    }

    public static String getGenderByIdCard(String idCard) {
        String sGender = "未知";
        String sCardNum = idCard.substring(16, 17);
        if (Integer.parseInt(sCardNum) % 2 != 0) {
            sGender = "男";
        } else {
            sGender = "女";
        }
        return sGender;
    }

    /**
     * 保全项目列表查询
     *
     * @return
     */
    public String findEdorInfo() {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            List<FDEdorItem> EdorInfo = fdEdorItemMapper.findEdorInfo();
            resultMap.put("Edorinfo", EdorInfo);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "保全项目列表查询成功");
        } catch (Exception e) {
            log.error("保全项目列表查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保全项目列表查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 企业保全项目查询
     *
     * @param token
     * @return
     */
    public String findGrpEdorInfo(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        // 获取session
        GlobalInput globalInput = userService.getSession(token);
        try {
            List<FCGrpEdorConfig> fcGrpEdorConfig = fcGrpEdorConfigMapper.findGrpEdorInfo(globalInput.getGrpNo());
            resultMap.put("fcGrpEdorConfig", fcGrpEdorConfig);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "企业保全项目查询成功");
        } catch (Exception e) {
            log.error("企业保全项目查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "企业保全项目查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 保全申请确认接口 1、被保人重要资料变更 IC 2、投保人资料变更 AC 3、受益人资料变更 BC 4、增加被保险人 NI 5、减少被保险人 ZT
     * 6、同质风险加减人 HNI
     *
     * @param edoraApplyConfirmInfo
     * @param token
     * @return
     */
    @SuppressWarnings({"unused", "unchecked"})
    public String applyConfirmIO(EdoraApplyConfirmInfo edoraApplyConfirmInfo, String token) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);

        boolean insuredFlag = false;
        boolean policyHoldFlag = false;
        boolean bnfFlag = false;
        boolean addFlag = false;
        boolean decFlag = false;
        boolean hrarFlag = false;

        boolean code300 = false;
        boolean code200 = false;
        String resultCode = "500";
        List<String> errMsg = new ArrayList<String>();

        // 封装减少被保险人数据参数
        Map<String, Object> descInsuredMap = new HashMap<>();
        descInsuredMap.put("decBatch", edoraApplyConfirmInfo.getDecBatch());
        descInsuredMap.put("grpNo", globalInput.getGrpNo());
        // 封装添加被保险人数据参数
        Map<String, Object> addInsuredMap = new HashMap<>();
        addInsuredMap.put("addBatch", edoraApplyConfirmInfo.getAddBatch());
        addInsuredMap.put("grpNo", globalInput.getGrpNo());
        if (edoraApplyConfirmInfo == null) {
            resultMap.put("errMsg", Arrays.asList("添加被保险人申请数据不能为空"));
            resultMap.put("resultCode", "500");
            return JSON.toJSONString(resultMap);
        }
        // 增加被保险人申请接口
        if (edoraApplyConfirmInfo.isNIFlag()) {
            // 将文件上传到FTP服务器，而后处理申请确认调用核心接口
            Map<String, Object> map = new HashMap<>();
            map.put("grpContNo", edoraApplyConfirmInfo.getGrpContNo());
            map.put("batch", edoraApplyConfirmInfo.getAddBatch());
            map.put("docType", "0304");
            List<FCEdoruploadfile> uploadfilelist = fcEdoruploadfileMapper.selectUploadfileByGrpContNo(map);// 查询出的是服务器上保全文件上传信息列表
            //暂时不需要判断
//			if(uploadfilelist.size()==0){
//                resultMap.put("success",false);
//                resultMap.put("resultCode","500");
//                resultMap.put("errMsg",Arrays.asList("被保人告知书为空!"));
//                return JSON.toJSONString(resultMap);
//            }
            //查询ftp信息上传到SFTP
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
            for (FCEdoruploadfile fcEdoruploadfile : uploadfilelist) {
                // 封装上传实体类
                boolean flag = sFtp.uploadFile(fcEdoruploadfile.getFtpPath(), fcEdoruploadfile.getLocalPath());
                if (!flag) {
                    log.info("被保人告知书上传FTP服务器失败！");
                    resultMap.put("success", false);
                    resultMap.put("resultCode", "500");
                    resultMap.put("errMsg", Arrays.asList("被保人告知书上传失败!"));
                    return JSON.toJSONString(resultMap);
                }
            }
            Map<String, Object> mapNI = edorConfirmNI.addInsuredApply(edoraApplyConfirmInfo.getGrpContNo(), addInsuredMap, edoraApplyConfirmInfo.getEdorAppNI(), uploadfilelist);
            addFlag = (boolean) mapNI.get("resultFlag");
            if (addFlag) {
                // edorAppNo = (String)mapNI.get("edorAppNo");
                resultMap.put("edorAppNoNI", (String) mapNI.get("edorAppNo"));
                resultMap.put("NIInsuredList", mapNI.get("NIInsuredList"));
                code200 = true;
                //更新当前保全申请状态
                updateEdorState(token, edoraApplyConfirmInfo.getAddBatch(), EdorStateEnum.APPLYDONE.getCode());
                log.info("增加被保险人申请接口: success！");
            } else {
                errMsg.addAll((List<String>) mapNI.get("errMsg"));
                code300 = true;
                log.info("增加被保险人申请接口: 申请失败！");
            }
        }
        // 减少被保险人申请接口
        if (edoraApplyConfirmInfo.isZTFlag()) {
            // 将文件上传到FTP服务器，而后处理申请确认调用核心接口
            Map<String, Object> map = new HashMap<>();
            map.put("grpContNo", edoraApplyConfirmInfo.getGrpContNo());
            map.put("batch", edoraApplyConfirmInfo.getDecBatch());
            map.put("docType", "0605");
            List<FCEdoruploadfile> uploadfilelist = fcEdoruploadfileMapper.selectUploadfileByGrpContNo(map);// 查询出的是服务器上保全文件上传信息列表
            // 查询ftp信息上传到SFTP
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
            for (FCEdoruploadfile fcEdoruploadfile : uploadfilelist) {
                // 封装上传实体类
                boolean flag = sFtp.uploadFile(fcEdoruploadfile.getFtpPath(), fcEdoruploadfile.getLocalPath());
                if (!flag) {
                    log.info("减少被保险人上传FTP服务器失败！");
                    resultMap.put("success", false);
                    resultMap.put("resultCode", "500");
                    resultMap.put("errMsg", Arrays.asList("减少被保险人上传失败!"));
                    return JSON.toJSONString(resultMap);
                }
            }
            Map<String, Object> mapZT = edorConfirmZT.decInsuredApply(descInsuredMap, edoraApplyConfirmInfo.getEdorAppZT(), edoraApplyConfirmInfo.getGrpContNo(), uploadfilelist);
            decFlag = (boolean) mapZT.get("resultFlag");
            if (decFlag) {
                // edorAppNo = (String)mapZT.get("edorAppNo");
                resultMap.put("edorAppNoZT", (String) mapZT.get("edorAppNo"));
                resultMap.put("ZTInsuredList", (List<Insured>) mapZT.get("ZTInsuredList"));
                code200 = true;
                //更新当前保全申请状态
                updateEdorState(token, edoraApplyConfirmInfo.getDecBatch(), EdorStateEnum.APPLYDONE.getCode());
                log.info("减少被保险人申请接口: success！");
            } else {
                errMsg.addAll((List<String>) mapZT.get("errMsg"));
                code300 = true;
                log.info("减少被保险人申请接口: 申请失败！");
            }
        }
        // 同质风险加减人申请接口
        if (edoraApplyConfirmInfo.isHNIFlag()) {
            // 封装同质风险加减人被保险人数据参数
            FCEdorHomogeneousRiskInsured fcEdorHomogeneousRiskInsured = new FCEdorHomogeneousRiskInsured();
            fcEdorHomogeneousRiskInsured.setBatch(String.valueOf(edoraApplyConfirmInfo.getHrarBatch()));
            fcEdorHomogeneousRiskInsured.setGrpNo(globalInput.getGrpNo());
            // 将文件上传到FTP服务器，而后处理申请确认调用核心接口
            Map<String, Object> map = new HashMap<>();
            map.put("grpContNo", edoraApplyConfirmInfo.getGrpContNo());
            map.put("batch", edoraApplyConfirmInfo.getHrarBatch());
            map.put("docType", "0604");
            List<FCEdoruploadfile> uploadfilelist = fcEdoruploadfileMapper.selectUploadfileByGrpContNo(map);// 查询出的是服务器上保全文件上传信息列表
            // 查询ftp信息上传到SFTP
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
            for (FCEdoruploadfile fcEdoruploadfile : uploadfilelist) {
                // 封装上传实体类
                boolean flag = sFtp.uploadFile(fcEdoruploadfile.getFtpPath(), fcEdoruploadfile.getLocalPath());
                if (!flag) {
                    log.info("同质风险加减人上传FTP服务器失败！");
                    resultMap.put("success", false);
                    resultMap.put("resultCode", "500");
                    resultMap.put("errMsg", Arrays.asList("同质风险加减人上传失败!"));
                    return JSON.toJSONString(resultMap);
                }
            }
            Map<String, Object> mapHNI = edorConfirmHNI.hrarInsuredApply(edoraApplyConfirmInfo.getGrpContNo(), edoraApplyConfirmInfo.getEdorAppHNI(), fcEdorHomogeneousRiskInsured, uploadfilelist);
            hrarFlag = Boolean.valueOf(mapHNI.get("success").toString());
            if (hrarFlag) {
                resultMap.put("edorAppNoHNI", String.valueOf(mapHNI.get("edorAppNo")));
                resultMap.put("HNIInsuredList", (List<Insured>) mapHNI.get("HNIInsuredList"));
                code200 = true;
                //更新当前保全申请状态
                updateEdorState(token, edoraApplyConfirmInfo.getHrarBatch(), EdorStateEnum.APPLYDONE.getCode());
                log.info("同质风险加减人申请接口: success！");
            } else {
                errMsg.addAll((List<String>) mapHNI.get("errMsg"));
                code300 = true;
                log.info("同质风险加减人申请接口: 申请失败！");
            }
        }
        // 投保人资料变更申请确认接口
        if (edoraApplyConfirmInfo.getPolicyHolderChgTrialIO().isEdorFlag()) {
            // 将文件上传到FTP服务器，而后处理申请确认调用核心接口
            Map<String, Object> map = new HashMap<>();
            map.put("grpContNo", edoraApplyConfirmInfo.getGrpContNo());
            map.put("batch", edoraApplyConfirmInfo.getAddBatch());
            map.put("docType", "0601");
            List<FCEdoruploadfile> uploadfilelist = fcEdoruploadfileMapper.selectUploadfileByGrpContNo(map);// 查询出的是服务器上保全文件上传信息列表
            // 查询ftp信息上传到SFTP
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
            for (FCEdoruploadfile fcEdoruploadfile : uploadfilelist) {
                // 封装上传实体类
                boolean flag = sFtp.uploadFile(fcEdoruploadfile.getFtpPath(), fcEdoruploadfile.getLocalPath());
                if (!flag) {
                    log.info("投保人资料变更上传FTP服务器失败！");
                    resultMap.put("success", false);
                    resultMap.put("resultCode", "500");
                    resultMap.put("errMsg", Arrays.asList("投保人资料变更上传失败!"));
                    return JSON.toJSONString(resultMap);
                }
            }
            Map<String, Object> mapAC = edorConfirmAC.insurerInfoChgApplyIO(edoraApplyConfirmInfo.getPolicyHolderChgTrialIO(), edoraApplyConfirmInfo.getGrpContNo(), globalInput, uploadfilelist);
            policyHoldFlag = (boolean) mapAC.get("resultFlag");
            if (policyHoldFlag) {
                // edorAppNo = (String)mapAC.get("edorAppNo");
                resultMap.put("edorAppNoAC", (String) mapAC.get("edorAppNo"));
                code200 = true;
                log.info("投保人重要资料变更保全申请接口: success！");
            } else {
                errMsg.addAll((List<String>) mapAC.get("errMsg"));
                code300 = true;
                log.info("投保人重要资料变更保全申请接口: 申请失败！");
            }
        }
        // 被保人重要资料变更申请确认接口
        if (edoraApplyConfirmInfo.getInsuredChgTrialIO().isEdorFlag()) {
            // 将文件上传到FTP服务器，而后处理申请确认调用核心接口
            Map<String, Object> map = new HashMap<>();
            map.put("grpContNo", edoraApplyConfirmInfo.getGrpContNo());
            map.put("batch", edoraApplyConfirmInfo.getAddBatch());
            map.put("docType", "0602");
            List<FCEdoruploadfile> uploadfilelist = fcEdoruploadfileMapper.selectUploadfileByGrpContNo(map);// 查询出的是服务器上保全文件上传信息列表
            // 查询ftp信息上传到SFTP
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
            for (FCEdoruploadfile fcEdoruploadfile : uploadfilelist) {
                // 封装上传实体类
                boolean flag = sFtp.uploadFile(fcEdoruploadfile.getFtpPath(), fcEdoruploadfile.getLocalPath());
                if (!flag) {
                    log.info("被保人重要资料变更上传FTP服务器失败！");
                    resultMap.put("success", false);
                    resultMap.put("resultCode", "500");
                    resultMap.put("errMsg", Arrays.asList("被保人重要资料变更上传失败!"));
                    return JSON.toJSONString(resultMap);
                }
            }
            Map<String, Object> mapIC = edorConfirmIC.InsuredInfoChgApplyIO(edoraApplyConfirmInfo.getInsuredChgTrialIO(), edoraApplyConfirmInfo.getGrpContNo(), uploadfilelist);
            insuredFlag = (boolean) mapIC.get("resultFlag");
            if (insuredFlag) {
                resultMap.put("edorAppNoIC", (String) mapIC.get("edorAppNo"));
                code200 = true;
                log.info("被保人重要资料变更保全申请接口: success！");
            } else {
                errMsg.addAll((List<String>) mapIC.get("errMsg"));
                code300 = true;
                log.info("被保人重要资料变更保全申请接口: 申请失败！");
            }
        }
        // 受益人资料变更保全申请接口
        if (edoraApplyConfirmInfo.getBnfChgTrialIO().isEdorFlag()) {
            // 将文件上传到FTP服务器，而后处理申请确认调用核心接口
            Map<String, Object> map = new HashMap<>();
            map.put("grpContNo", edoraApplyConfirmInfo.getGrpContNo());
            map.put("batch", edoraApplyConfirmInfo.getAddBatch());
            map.put("docType", "0603");
            List<FCEdoruploadfile> uploadfilelist = fcEdoruploadfileMapper.selectUploadfileByGrpContNo(map);// 查询出的是服务器上保全文件上传信息列表
            // 查询ftp信息上传到SFTP
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
            for (FCEdoruploadfile fcEdoruploadfile : uploadfilelist) {
                // 封装上传实体类
                boolean flag = sFtp.uploadFile(fcEdoruploadfile.getFtpPath(), fcEdoruploadfile.getLocalPath());
                if (!flag) {
                    log.info("受益人资料变更上传FTP服务器失败！");
                    resultMap.put("success", false);
                    resultMap.put("resultCode", "500");
                    resultMap.put("errMsg", Arrays.asList("受益人资料变更上传失败!"));
                    return JSON.toJSONString(resultMap);
                }
            }
            Map<String, Object> mapBC = edorConfirmBC.edorApply(edoraApplyConfirmInfo.getBnfChgTrialIO(), edoraApplyConfirmInfo.getGrpContNo(), uploadfilelist);
            bnfFlag = (boolean) mapBC.get("resultFlag");
            if (bnfFlag) {
                resultMap.put("edorAppNoBC", (String) mapBC.get("edorAppNo"));
                code200 = true;
                log.info("受益人资料变更申请接口: success！");
            } else {
                errMsg.addAll((List<String>) mapBC.get("errMsg"));
                code300 = true;
                log.info("受益人资料变更申请接口: 申请失败！");
            }
        }
        if (code200) {
            if (!code300) {
                resultCode = "200";
            } else {
                resultCode = "300";
            }
        }
        resultMap.put("resultCode", resultCode);
        resultMap.put("errMsg", errMsg);
        // resultMap.put("edorAppNo", edorAppNo);
        resultMap.put("insuredFlag", insuredFlag);
        resultMap.put("policyHoldFlag", policyHoldFlag);
        resultMap.put("bnfFlag", bnfFlag);
        resultMap.put("addFlag", addFlag);
        resultMap.put("decFlag", decFlag);
        return JSON.toJSONString(resultMap);
    }

    // 导入增加被保险人-根据计划增人
    public String importPlusInsuredExcelByPlan(String token, MultipartFile file, String batch, String grpContNo, String policyeffectDate, String policyendDate, List<String> fCEdorPlanInfolist) {
        Map<String, Object> resultMap = new HashMap<>();
        /**
         * 校验数据
         */
        if (StringUtils.isBlank(batch)) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保全增人批次号不能为空，请退出重试！");
            return JSON.toJSONString(resultMap);
        }

        /**
         * 处理数据
         */
        String path = FileUtil.getLocalPath("0303");// 根据计划增人模板
        String fileName = file.getOriginalFilename();
        String filePath = path + fileName;
        boolean uploadSuccess = fileService.fileUpload(file, filePath, "0303", "");
        if (!uploadSuccess) {
            log.info("新增保险人清单导入失败：文件上传失败！");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "新增保险人清单导入文件失败");
            return JSON.toJSONString(resultMap);
        }
        // json数据
        List<FCEdorPlanInfo> fcEdorPlanInfolist = JSONObject.parseArray(fCEdorPlanInfolist.toString(), FCEdorPlanInfo.class);
        // 计划列表
        List<String> planlist = new ArrayList<>();
        // 计划保费集合
        Map<String, Double> planInfo = new HashMap<>();
        for (FCEdorPlanInfo fcEdorPlanInfo : fcEdorPlanInfolist) {
            planlist.add(fcEdorPlanInfo.getPlanCode());
            planInfo.put(fcEdorPlanInfo.getPlanCode(), fcEdorPlanInfo.getPrem());
        }
        if (planlist.size() < 1) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "新增保险人清单导入文件失败:保单计划为空！");
            return JSON.toJSONString(resultMap);
        }
        Workbook wb = null;
        try {
            wb = ExcelUtil.initWorkbook(filePath);
            resultMap = importInsuredByPlanNI.dealPlusInsuredExcelByPlan(token, wb, batch, grpContNo, policyeffectDate, policyendDate, planlist, planInfo);
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "根据计划增加被保险人失败");
            return JSON.toJSONString(resultMap);
        }
        // 记录当前保全申请状态
        FcEdorItem fcEdorItem = fcEdorItemMapper.selectByPrimaryKey(batch);
        if (ObjectUtils.isEmpty(fcEdorItem)) {
            saveEdorState(token, batch, grpContNo, EdorTypeEnum.EDORNI.getCode(), EdorStateEnum.APPLYING.getCode());
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 保全增人试算接口
     *
     * @param token
     * @param edoraNITrialInfo
     * @return
     */
    @Transactional
    public String edorNITrialIO(String token, EdoraNITrialInfo edoraNITrialInfo) {
        /**
         * 定义返回报文
         */
        SelectEdorApplyInfoResp selectEdorApplyInfoResp = new SelectEdorApplyInfoResp();

        /**
         * 校验数据
         */
        String grpContNo = edoraNITrialInfo.getGrpContNo();
        if (StringUtils.isBlank(grpContNo)) {
            throw new SystemException("保单号不能为空！");
        }
        String addBatch = edoraNITrialInfo.getBatch();
        if (StringUtils.isBlank(addBatch)) {
            throw new SystemException("保全增人批次号不能为空！");
        }
        GlobalInput globalInput = userService.getSession(token);
        String grpNo = globalInput.getGrpNo();

        /**
         * 处理数据
         */
        // 获取保全增人数据
        FCEdorAddInsured fcEdorAddInsuredParam = new FCEdorAddInsured();
        fcEdorAddInsuredParam.setGrpContNo(grpContNo);
        fcEdorAddInsuredParam.setBatch(addBatch);
        fcEdorAddInsuredParam.setGrpNo(grpNo);
        List<FCEdorAddInsured> fcEdorAddInsuredList = fcEdorAddInsuredMapper.selectEdorAddInsuredLst(fcEdorAddInsuredParam);
        if (fcEdorAddInsuredList.size() < 1) {
            throw new SystemException("当前保全批次下无增人数据！");
        }
        // 判断保全申请批次下是否包含错误的人员信息
        int selectErrorEdorAddInsuredCount = fcEdorAddInsuredMapper.selectErrorEdorAddInsured(grpContNo, addBatch);
        if (selectErrorEdorAddInsuredCount > 0) {
            throw new SystemException("当前保全批次下包含错误人员信息！");
        }
        /**
         * 获取人数异步阈值的配置
         */
        //场地险保全增人试算阈值
        FdAsyncThreshold fdAsyncThresholdNITrialIOSite = fdAsyncThresholdMapper.selectByPrimaryKey(AsyncThresholdTypeEnum.EDORNITRIALIOSITE.getCode());
        Integer edorNITrialIOSitePeopleLimit = fdAsyncThresholdNITrialIOSite.getPeopleLimit();
        Integer edorNITrialIOSiteSingleDealTime = fdAsyncThresholdNITrialIOSite.getSingleDealTime();
        //非场地险保全增人试算阈值
        FdAsyncThreshold fdAsyncThresholdNITrialIO = fdAsyncThresholdMapper.selectByPrimaryKey(AsyncThresholdTypeEnum.EDORNITRIALIO.getCode());
        Integer edorNITrialIOPeopleLimit = fdAsyncThresholdNITrialIO.getPeopleLimit();
        Integer edorNITrialIOSingleDealTime = fdAsyncThresholdNITrialIO.getSingleDealTime();

        /**
         * 场地险的执行逻辑
         */
        // 判断是否为场地险企业投保
        FdGrpInsureConfig fdGrpInsureConfig = fdGrpInsureConfigMapper.selectByPrimaryKey(grpNo);
        if (!ObjectUtils.isEmpty(fdGrpInsureConfig)) {
            /**
             * 场地险的执行逻辑，平台进行保费试算
             */
            //保单挂起的校验
            RemoteDelegate rd = RemoteDelegate.getInstance();
            String requestXml = "<?xml version=\"1.0\"?>\n" + "<RequestInfo>\n" + "\t<HEAD>\n" + "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" + "\t\t<TransType>YUG013</TransType>\n" + "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" + "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" + "\t</HEAD>\n" + "\t<BODY>\n" + "\t\t\t<GrpContNo>" + grpContNo + "</GrpContNo>\n" + "\t</BODY>\n" + "</RequestInfo>";
            log.info("调用核心保全挂起查询接口请求报文：" + requestXml);
            long startStamp = System.currentTimeMillis();
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "BenefitService", requestXml);
            long endStamp = System.currentTimeMillis();
            log.info("调用核心保全挂起查询接口用时：" + (endStamp - startStamp));
            if (success) {
                Map<String, Object> responseXml = rd.getResult();
                Body body = (Body) responseXml.get("Body");
                log.info("调用核心保全挂起查询接口返回报文：" + JSON.toJSONString(responseXml));
                if (body.getGrpConts().getGrpContInfo().getResult()) {
                    log.info("当前保单{}已挂起！", grpContNo);
                    throw new SystemException("当前保单已挂起，不能进行保费试算！");
                }
            } else {
                throw new SystemException("调用核心保全挂起查询接口失败！");
            }


            // 小于100人同步实时处理，大于100人走异步处理
            if (fcEdorAddInsuredList.size() <= edorNITrialIOSitePeopleLimit) {
                log.info("场地险增加被保险人试算接口，实时处理 start .....");
                edorNIService.edorNITrial(grpContNo, addBatch);
                selectEdorApplyInfoResp.setEdorApplyState("02");
                selectEdorApplyInfoResp.setEdorApplyMsg("处理完成！");
                // 更新保全申请信息表的保单申请号
                FcEdorItem fcEdorItem = new FcEdorItem();
                fcEdorItem.setEdorBatch(addBatch);
                String edorAppNo = maxNoService.createMaxNo("edorAppNo", "3" + grpContNo, 4);
                fcEdorItem.setEdorAppNo(edorAppNo);
                fcEdorItem = CommonUtil.initObject(fcEdorItem, "update");
                fcEdorItemMapper.updateByPrimaryKeySelective(fcEdorItem);
                return JSONObject.toJSONString(ResponseResultUtil.success(selectEdorApplyInfoResp));
            } else {
                log.info("场地险增加被保险人试算接口，异步处理 start .....");
                // 异步处理
                asyncService.edorAddInsuredTrialIOAsync1(globalInput, addBatch, grpContNo, fcEdorAddInsuredList, fdAsyncThresholdNITrialIOSite);
                selectEdorApplyInfoResp.setEdorApplyState("01");
                // 预估处理时长
                DecimalFormat decimalFormat = new DecimalFormat("#.##");
                Double dealMin = CommonUtil.div(Double.valueOf(fcEdorAddInsuredList.size() * edorNITrialIOSiteSingleDealTime), Double.valueOf(60000), 1);
                selectEdorApplyInfoResp.setEdorApplyMsg("当前保单下" + AsyncBusinessTypeEnum.getValueByCode(AsyncBusinessTypeEnum.NITRIALIO.getCode()) + "由于人数较多，正在快马加鞭处理，请稍后进行保全项相关操作，预计处理时长" + decimalFormat.format(dealMin) + "分钟！");
                return JSONObject.toJSONString(ResponseResultUtil.success(selectEdorApplyInfoResp));
            }
        } else {
            /**
             * 非场地险的执行逻辑，调用核心进行试算
             */
            // 小于等于10人同步实时处理，大于10人走异步处理
            if (fcEdorAddInsuredList.size() <= edorNITrialIOPeopleLimit) {
                log.info("增加被保险人试算接口，实时处理 start .....");
                long startTime = System.currentTimeMillis();
                // 请求核心试算接口
                Map<String, Object> mapNI = edorNIBL.submitData(fcEdorAddInsuredList, addBatch, grpContNo);
                long endTime = System.currentTimeMillis();
                log.info("\n调用核心保全增人试算接口执行所用时间" + (endTime - startTime) + "毫秒。");
                // 处理返回结果
                boolean addFlag = (boolean) mapNI.get("resultFlag");
                // 定义返回对象
                EdorTrialIOResultInfo edorTrialIOResultInfo = new EdorTrialIOResultInfo();
                if (addFlag) {
                    log.info("增加被保险人试算接口: success！");
                    String edorAppNo = (String) mapNI.get("edorAppNo");
                    edorTrialIOResultInfo.setEdorAppNo(edorAppNo);
                    List<Insured> coreInsuredInfos = (List<Insured>) mapNI.get("NIInsuredList");
                    edorTrialIOResultInfo.setEdorAddInsuredList(coreInsuredInfos);
                    // 更新保全增人的信息，记录保费
                    coreInsuredInfos.forEach((Insured insured) -> {
                        FCEdorAddInsured fcEdorAddInsured = new FCEdorAddInsured();
                        fcEdorAddInsured.setGrpContNo(grpContNo);
                        fcEdorAddInsured.setBatch(addBatch);
                        fcEdorAddInsured.setIdType(insured.getIDType());
                        fcEdorAddInsured.setIdNo(insured.getIDNo());
                        fcEdorAddInsured.setTrialStatus(TrialStateEnum.TRIALDONE.getCode());
                        fcEdorAddInsured.setPremSource(insured.getPremSource());
                        fcEdorAddInsured.setAccountType(insured.getAccountType());
                        fcEdorAddInsured.setIsError(StateEnum.INVALID.getCode());
                        fcEdorAddInsured.setPrem(insured.getPrem());
                        fcEdorAddInsured = CommonUtil.initObject(fcEdorAddInsured, "update");
                        fcEdorAddInsuredMapper.updateEdorAddInsured(fcEdorAddInsured);
                    });
                    // 更新保全申请信息表的保单申请号
                    FcEdorItem fcEdorItem = new FcEdorItem();
                    fcEdorItem.setEdorBatch(addBatch);
                    fcEdorItem.setEdorAppNo(edorAppNo);
                    fcEdorItem = CommonUtil.initObject(fcEdorItem, "update");
                    fcEdorItemMapper.updateByPrimaryKeySelective(fcEdorItem);
                    selectEdorApplyInfoResp.setEdorApplyState("02");
                    selectEdorApplyInfoResp.setEdorApplyMsg("处理完成！");
                    return JSONObject.toJSONString(ResponseResultUtil.success(selectEdorApplyInfoResp));
                } else {
                    log.info("增加被保险人试算接口: 试算失败！{}", mapNI.get("errMsg"));
                    selectEdorApplyInfoResp.setEdorApplyState("03");
                    selectEdorApplyInfoResp.setEdorApplyMsg("试算失败！");
                    return JSONObject.toJSONString(ResponseResultUtil.success(selectEdorApplyInfoResp));
                }
            } else {
                log.info("增加被保险人试算接口，异步处理 start .....");
                // 异步处理
                asyncService.edorAddInsuredTrialIOAsync(globalInput, addBatch, grpContNo, fcEdorAddInsuredList, fdAsyncThresholdNITrialIO);
                selectEdorApplyInfoResp.setEdorApplyState("01");
                // 预估处理时长
                DecimalFormat decimalFormat = new DecimalFormat("#.##");
                Double dealMin = CommonUtil.div(Double.valueOf(fcEdorAddInsuredList.size() * edorNITrialIOSingleDealTime), Double.valueOf(60000), 1);
                selectEdorApplyInfoResp.setEdorApplyMsg("当前保单下" + AsyncBusinessTypeEnum.getValueByCode(AsyncBusinessTypeEnum.NITRIALIO.getCode()) + "由于人数较多，正在快马加鞭处理，请稍后进行保全项相关操作，预计处理时长为" + decimalFormat.format(dealMin) + "分钟！");
                return JSONObject.toJSONString(ResponseResultUtil.success(selectEdorApplyInfoResp));
            }
        }

    }

    /**
     * 保全试算接口
     *
     * @param edoraTrialInfo
     * @param token
     * @return
     */
    @Transactional
    public String trialIO(EdoraTrialInfo edoraTrialInfo, String token) {

        List<Insurers> insurersList = edoraTrialInfo.getInsuredChgTrialIO().getInsurersList();
        for (Insurers insurers : insurersList) {
            String newName = insurers.getNewName();
            String newIdType = insurers.getNewIdType();
            String newNames = "";
            if (!StringUtil.isEmpty(newIdType) && newIdType.equals("1")) {
                if (!StringUtil.isEmpty(newName)) {
                    newNames = CheckUtils.checkForeignName(newName);
                }
            } else if (!StringUtil.isEmpty(newIdType) && !newIdType.equals("1")) {
                if (!StringUtil.isEmpty(newName)) {
                    newNames = CheckUtils.checkChineseName(newName);
                }
            }
            if (!StringUtil.isEmpty(newNames)) {
                return JSON.toJSONString(ResultUtil.error(newNames));
            }

            String nname = insurers.getName();
            String iidType = insurers.getiDType();
            String nnames = "";
            if (!StringUtil.isEmpty(iidType) && iidType.equals("1")) {
                if (!StringUtil.isEmpty(nname)) {
//                    nnames = CheckUtils.checkEnglishName(nname);
                }
            } else if (!StringUtil.isEmpty(iidType) && !iidType.equals("1")) {
                if (!StringUtil.isEmpty(nname)) {
                    nnames = CheckUtils.checkChineseName(nname);
                }
            }
            if (!StringUtil.isEmpty(nnames)) {
                return JSON.toJSONString(ResultUtil.error(nnames));
            }
        }
        List<Insureds> insuredList = edoraTrialInfo.getBnfChgTrialIO().getInsuredList();
        for (Insureds insureds : insuredList) {
            List<BnfInsured> bnfList = insureds.getBnfList();
            for (BnfInsured bnfInsured : bnfList) {
                String bnfName = bnfInsured.getBnfName();
                String bnfIdType = bnfInsured.getBnfIdType();
                String bnfNam = "";
                if (!StringUtil.isEmpty(bnfIdType) && bnfIdType.equals("1")) {
                    if (!StringUtil.isEmpty(bnfName)) {
//                        bnfNam = CheckUtils.checkEnglishName(bnfName);
                    }
                } else if (!StringUtil.isEmpty(bnfIdType) && !bnfIdType.equals("1")) {
                    if (!StringUtil.isEmpty(bnfName)) {
                        bnfNam = CheckUtils.checkChineseName(bnfName);
                    }
                }
                if (!StringUtil.isEmpty(bnfNam)) {
                    return JSON.toJSONString(ResultUtil.error(bnfNam));
                }
            }
            // 下面注释的应该是 原资料姓名
            String iDType = insureds.getiDType();
            String name = insureds.getName();
            String errName = "";
            if (!StringUtil.isEmpty(iDType) && iDType.equals("1")) {
                if (!StringUtil.isEmpty(name)) {
//                    errName = CheckUtils.checkEnglishName(name);
                }
            } else if (!StringUtil.isEmpty(iDType) && !iDType.equals("1")) {
                errName = CheckUtils.checkChineseName(name);
            }
            if (!StringUtil.isEmpty(errName)) {
                return JSON.toJSONString(ResultUtil.error(errName));
            }
        }
        // 对姓名进行校验
        PolicyHolderInfo policyHolderInfo = edoraTrialInfo.getPolicyHolderChgTrialIO().getPolicyHolderInfo();
        policyHolderInfo.setCorporation(policyHolderInfo.getCorporation().trim().replaceAll(" +", " "));
        policyHolderInfo.setLinkMan1(policyHolderInfo.getLinkMan1().trim().replaceAll(" +", " "));
        if (policyHolderInfo != null) {
            String corporation = policyHolderInfo.getCorporation();
            String linkMan1 = policyHolderInfo.getLinkMan1();
            String s = "";
            String z = "";
            if (!StringUtil.isEmpty(corporation)) {
                if (CheckUtils.checkcountname(corporation)) {
                    s = CheckUtils.checkChineseName(corporation);
                } else {
                    s = CheckUtils.checkEnglishName(corporation);
                    String trim = corporation.trim();
                    corporation = trim.replaceAll(" +", " ");
                    policyHolderInfo.setCorporation(corporation);
                }
            }
            if (!StringUtil.isEmpty(linkMan1)) {
                if (CheckUtils.checkcountname(linkMan1)) {
                    z = CheckUtils.checkChineseName(linkMan1);
                } else {
                    z = CheckUtils.checkEnglishName(linkMan1);
                    String trim = linkMan1.trim();
                    linkMan1 = trim.replaceAll(" +", " ");
                    policyHolderInfo.setLinkMan1(linkMan1);
                }
            }
            if (!StringUtil.isEmpty(s)) {
                return JSON.toJSONString(ResultUtil.error(s));
            }
            if (!StringUtil.isEmpty(z)) {
                return JSON.toJSONString(ResultUtil.error(z));
            }
            // 校验电话
            String mobilePhone = policyHolderInfo.getMobilePhone1();
            if (StringUtils.isNotBlank(mobilePhone) && !CheckUtils.checkMobilePhone(mobilePhone)) {
                throw new SystemException("联系人手机格式错误，请检查！");
            }
            // 校验邮箱
            String email = policyHolderInfo.getEmail();
            if (StringUtils.isNotBlank(email) && !CheckUtils.checkEmail(email)) {
                throw new SystemException("联系人E-Mail录入有误，请检查！");
            }

        }

        // 返回数据容器
        Map<String, Object> resultMap = new HashMap<>();
        String resultCode = "500";
        List<String> errMsg = new ArrayList<String>();
        boolean insuredFlag = false;
        boolean policyHoldFlag = false;
        boolean bnfFlag = false;
        boolean addFlag = false;
        boolean decFlag = false;
        boolean hrarFlag = false;

        boolean code300 = false;
        boolean code200 = false;
        if (edoraTrialInfo == null) {
            errMsg.add("未检测到录入数据，请录入后重试！");
            resultMap.put("resultCode", "500");
            resultMap.put("errMsg", errMsg);
            return JSON.toJSONString(resultMap);
        }
        log.info("保全试算 start >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        // 获取企业编号
        GlobalInput globalInput = userService.getSession(token);
        // 获取企业信息
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(globalInput.getGrpNo());
        /*********************** 调用核心试算接口 start.... *****************************/
        // 1、被保人重要资料变更试算接口
        if (edoraTrialInfo.getInsuredChgTrialIO().isEdorFlag()) {
            log.info("被保人重要资料变更保全试算接口 start .....");
            /**
             * 存储被保人变更信息
             */
            //saveEdorChangeInsured();


            Map<String, Object> mapIC = edorICBL.submitData(edoraTrialInfo.getInsuredChgTrialIO(), edoraTrialInfo.getGrpContNo());
            insuredFlag = (boolean) mapIC.get("resultFlag");
            if (insuredFlag) {
                resultMap.put("edorAppNoIC", (String) mapIC.get("edorAppNo"));
                code200 = true;
                log.info("被保人重要资料变更保全试算接口: success！");
            } else {
                errMsg.addAll((List<String>) mapIC.get("errMsg"));
                code300 = true;
                log.info("被保人重要资料变更保全试算接口: 试算失败！");
            }
        }

        // 2、投保人资料变更保全试算接口
        if (edoraTrialInfo.getPolicyHolderChgTrialIO().isEdorFlag()) {
            log.info("投保人资料变更保全试算接口 start .....");
            Map<String, Object> mapAC = edorACBL.submitData(edoraTrialInfo.getPolicyHolderChgTrialIO(), edoraTrialInfo.getGrpContNo(), fcGrpInfo);
            policyHoldFlag = (boolean) mapAC.get("resultFlag");
            if (policyHoldFlag) {
                // edorAppNo = (String)mapAC.get("edorAppNo");
                resultMap.put("edorAppNoAC", (String) mapAC.get("edorAppNo"));
                code200 = true;
                log.info("投保人重要资料变更保全试算接口: success！");
            } else {
                errMsg.addAll((List<String>) mapAC.get("errMsg"));
                code300 = true;
                log.info("投保人重要资料变更保全试算接口: 试算失败！");
            }
        }

        // 3、受益人资料变更试算接口
        if (edoraTrialInfo.getBnfChgTrialIO().isEdorFlag()) {
            log.info("受益人资料变更试算接口 start .....");
            Map<String, Object> mapBC = edorBCBL.submitData(edoraTrialInfo.getBnfChgTrialIO(), edoraTrialInfo.getGrpContNo());
            bnfFlag = (boolean) mapBC.get("resultFlag");
            if (bnfFlag) {
                resultMap.put("edorAppNoBC", (String) mapBC.get("edorAppNo"));
                code200 = true;
                log.info("受益人资料变更试算接口: success！");
            } else {
                errMsg.addAll((List<String>) mapBC.get("errMsg"));
                code300 = true;
                log.info("受益人资料变更试算接口: 试算失败！");
            }
        }

        // 4、增加被保险人试算接口
        if (edoraTrialInfo.isNIFlag()) {
            log.info("增加被保险人试算接口 start .....{}", edoraTrialInfo.getAddBatch());
            // 封装添加被保险人数据参数
            Map<String, Object> addInsuredMap = new HashMap<>();
            addInsuredMap.put("addBatch", edoraTrialInfo.getAddBatch());
            addInsuredMap.put("grpNo", globalInput.getGrpNo());
            List<FCEdorAddInsured> fcEdorAddInsuredList = fcEdorAddInsuredMapper.getAddInsuredInfo(addInsuredMap);
            Map<String, Object> mapNI = edorNIBL.submitData(fcEdorAddInsuredList, edoraTrialInfo.getAddBatch(), edoraTrialInfo.getGrpContNo());
            addFlag = (boolean) mapNI.get("resultFlag");
            if (addFlag) {
                // edorAppNo = (String)mapNI.get("edorAppNo");
                resultMap.put("edorAppNoNI", (String) mapNI.get("edorAppNo"));
                resultMap.put("NIInsuredList", mapNI.get("NIInsuredList"));
                code200 = true;
                log.info("增加被保险人试算接口: success！");
            } else {
                errMsg.addAll((List<String>) mapNI.get("errMsg"));
                code300 = true;
                log.info("增加被保险人试算接口: 试算失败！");
            }
        }

        // 5、减少被保险人试算接口
        if (edoraTrialInfo.isZTFlag()) {
            log.info("减少被保险人试算接口 start .....");
            // 封装减少被保险人数据参数
            Map<String, Object> descInsuredMap = new HashMap<>();
            descInsuredMap.put("decBatch", edoraTrialInfo.getDecBatch());
            descInsuredMap.put("grpNo", globalInput.getGrpNo());
            List<FCEdorReduInsured> fcEdorReduInsuredList = fcEdorReduInsuredMapper.getDecInsuredInfo(descInsuredMap);

            Map<String, Object> mapZT = edorZTBL.submitData(fcEdorReduInsuredList, edoraTrialInfo.getAddBatch(), edoraTrialInfo.getGrpContNo());
            decFlag = (boolean) mapZT.get("resultFlag");
            if (decFlag) {
                // edorAppNo = (String)mapZT.get("edorAppNo");
                resultMap.put("edorAppNoZT", (String) mapZT.get("edorAppNo"));
                resultMap.put("ZTInsuredList", (List<Insured>) mapZT.get("ZTInsuredList"));
                code200 = true;
                log.info("减少被保险人试算接口: success！");
            } else {
                errMsg.addAll((List<String>) mapZT.get("errMsg"));
                code300 = true;
                log.info("减少被保险人试算接口: 试算失败！");
            }
        }
        // 6、同质风险加减人试算接口
        if (edoraTrialInfo.isHNIFlag()) {
            // 封装同质风险加减人被保险人数据参数
            Map<String, Object> mapHNI = edorHNIBL.submitData(edoraTrialInfo.getGrpContNo(), edoraTrialInfo.getHrarBatch(), globalInput.getGrpNo());
            hrarFlag = (boolean) mapHNI.get("resultFlag");
            if (hrarFlag) {
                resultMap.put("edorAppNoHNI", String.valueOf(mapHNI.get("edorAppNo")));
                resultMap.put("HNIInsuredList", mapHNI.get("HNIInsuredList"));
                code200 = true;
                log.info("同质风险加减人试算接口: success！");
            } else {
                errMsg.addAll((List<String>) mapHNI.get("errMsg"));
                code300 = true;
                log.info("同质风险加减人试算接口: 试算失败！");
            }
        }
        /*********************** 调用核心试算接口 end.... *****************************/
        // resultCode : 200申请的保全项目全部试算成功; 300部分保全项目试算成功; 500保全项目全部试算失败(不跳申请页面);
        if (code200) {
            if (!code300) {
                resultCode = "200";
            } else {
                resultCode = "300";
            }
        }
        resultMap.put("resultCode", resultCode);
        resultMap.put("errMsg", errMsg);
        // resultMap.put("edorAppNo", edorAppNo);
        resultMap.put("insuredFlag", insuredFlag);
        resultMap.put("policyHoldFlag", policyHoldFlag);
        resultMap.put("bnfFlag", bnfFlag);
        resultMap.put("addFlag", addFlag);
        resultMap.put("decFlag", decFlag);
        resultMap.put("HNIFlag", hrarFlag);
        return JSON.toJSONString(resultMap);
    }

    // 导入增加被保险人-根据险种增人
    public String importPlusInsuredExcel(String token, MultipartFile file, String batch, String grpContNo, String policyeffectDate, String policyendDate) {
        Map<String, Object> resultMap = new HashMap<>();
        String path = FileUtil.getLocalPath("0301");
        String fileName = file.getOriginalFilename();
        String filePath = path + fileName;
        boolean uploadSuccess = fileService.fileUpload(file, filePath, "0301", "");
        if (!uploadSuccess) {
            log.info("新保险人清单导入失败：文件上传失败！");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "新增保险人清单导入文件失败");
            return JSON.toJSONString(resultMap);
        }
        Workbook wb = null;
        try {
//			//本地测试
//			String filePath = "C:\\Users\\<USER>\\Desktop\\HQ\\new增加被保险人导入模版.xlsx";
            wb = ExcelUtil.initWorkbook(filePath);
            // 处理业务，解析计划以及人员清单
            resultMap = importInsuredNI.dealPlusInsuredExcel(token, wb, batch, grpContNo, policyeffectDate, policyendDate);
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "新增被保险人失败");
            return JSON.toJSONString(resultMap);
        }
        //记录当前保全申请状态
        saveEdorState(token, batch, grpContNo, EdorTypeEnum.EDORNI.getCode(), EdorStateEnum.APPLYING.getCode());
        return JSON.toJSONString(resultMap);
    }

    // 导入减少被保险人
    public String importReduInsuredExcel(String token, MultipartFile file, String batch, String grpContNo) {
        Map<String, Object> resultMap = new HashMap<>();
        String path = FileUtil.getLocalPath("0302");
        String fileName = file.getOriginalFilename();
        String filePath = path + fileName;
        boolean uploadSuccess = fileService.fileUpload(file, filePath, "0302", "");
        if (!uploadSuccess) {
            log.info("减少保险人清单导入失败：文件上传失败！");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "减少保险人清单导入文件失败");
            return JSON.toJSONString(resultMap);
        }
        Workbook wb = null;
        try {
            wb = ExcelUtil.initWorkbook(filePath);
            // 处理业务
            resultMap = dealReduInsuredExcel(token, wb, batch, grpContNo);
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "减少被保险人失败");
            return JSON.toJSONString(resultMap);
        }
        //记录当前保全申请状态
        saveEdorState(token, batch, grpContNo, EdorTypeEnum.EDORZT.getCode(), EdorStateEnum.APPLYING.getCode());
        return JSON.toJSONString(resultMap);
    }

    // 减少被保人列表数据是否为空
    public String checkInsuredIsEmpty(int i, Row row) {
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(1)))) {
            return "第" + i + "姓名不能为空";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellFormula(row.getCell(4)))) {
            return "第" + i + "性别不能为空";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellFormula(row.getCell(5)))) {
            return "第" + i + "出生日期不能为空";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(2)))) {
            return "第" + i + "证件类型不能为空";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(3)))) {
            return "第" + i + "证件号不能为空";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(6)))) {
            return "第" + i + "减保生效日期不能为空";
        }
        /*
         * if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(7)))){ return
         * "第"+i+"退费说明不能为空"; }
         */
        return "";
    }

    // 查询保全增加被保险人信息
    public String selectEdorAddInsured(String token, SelectfcedoraddinsuredReq selectfcedoraddinsuredReq) {
        // 获取参数
        String pageSource = selectfcedoraddinsuredReq.getPageSource();
        Integer pageNum = selectfcedoraddinsuredReq.getPageNum();
        Integer pageSize = selectfcedoraddinsuredReq.getPageSize();
        PageHelper.startPage(pageNum, pageSize);
        // 区分页面
        if (pageSource.equals("1")) {
            selectfcedoraddinsuredReq.setTrialStates(Arrays.asList(TrialStateEnum.INSUREDPREPARE.getCode(), TrialStateEnum.TRIALDONE.getCode()));
        } else {
            selectfcedoraddinsuredReq.setTrialStates(Arrays.asList(TrialStateEnum.TRIALDONE.getCode()));
        }
        List<EdorAddInsuredInfo> fcEdorAddInsuredList = fcEdorAddInsuredMapper.getInsuredExist1(selectfcedoraddinsuredReq);
        return JSONObject.toJSONString(ResponseResultUtil.success(PageResp.restPage(fcEdorAddInsuredList)));
    }

    // 科学记数法
    public boolean checkScientifiNotation(String str) {
        if (str.contains("E") || str.contains(".")) {
            return false;
        }
        return true;
    }

    // 解析减少被保险人清单excel
    public Map<String, Object> dealReduInsuredExcel(String token, Workbook wb, String bacth, String grpContNo) {
        GlobalInput globalInput = userService.getSession(token);
        Map<String, Object> resultMap = new HashMap<>();
        // 获取性别Value-Key
        List<HashMap<String, Object>> sexCodeList = fdCodeMapper.CodeInfo("Sex");
        // 获取证件类型和证件号码的Value-Key
        List<HashMap<String, Object>> idTypeCodeList = fdCodeMapper.CodeInfo("IDType");
        String errorMsg = "";
        int count = 0;
        List<FCEdorReduInsured> insuredList = new ArrayList<>();
        List<EvaluationCustomer> checkCustomer = new ArrayList<>();
        try {
            // 覆盖导入
            Map<String, Object> parmap = new HashMap<>();
            parmap.put("grpContNo", grpContNo);
            parmap.put("decBatch", bacth);
            parmap.put("grpNo", globalInput.getGrpNo());
            List<FCEdorReduInsured> fcEdorReduInsuredList = fcEdorReduInsuredMapper.getDecInsuredInfo(parmap);
            if (fcEdorReduInsuredList.size() > 0 && fcEdorReduInsuredList != null) {
                fcEdorReduInsuredMapper.deleteByBacth(parmap);
            }
            //删除之前的申请信息
            fcEdorItemMapper.deleteByPrimaryKey(bacth);
            List<HashMap<String, String>> mapList = new ArrayList<>();
            List<String> idNoList = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            int rowNum = 0;
            Sheet sheet = wb.getSheetAt(0);
            Row row = null;
            log.info(sheet.getLastRowNum() + "");
            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                rowNum = i - 1;
                row = sheet.getRow(i);
                if (ExcelUtil.isRowEmpty(row, 5)) {
                    log.info("第" + (i - 1) + "行是无效数据。");
                } else {
                    /* count++; */
                    // 校验录入数据是否为空
                    errorMsg = checkInsuredIsEmpty(i + 1, row);
                    if (!"".equals(errorMsg)) {
                        throw new RuntimeException();
                    }
                    if (!checkScientifiNotation(ExcelUtil.getCellFormula(row.getCell(3)))) {
                        errorMsg = "第" + (i - 1) + "行,证件号填写格式有误!";
                        throw new RuntimeException();
                    }
					/*if(!"0".equals(ExcelUtil.getCellFormula(row.getCell(7))) && !"1".equals(ExcelUtil.getCellFormula(row.getCell(7))) && !"2".equals(ExcelUtil.getCellFormula(row.getCell(7)))){
						errorMsg = "第" + (i - 1) + "行,退费说明填入0、1、2!";
						throw new RuntimeException();
					}*/
                    String name = ExcelUtil.getCellValue(row.getCell(1));
                    String idTypeName = ExcelUtil.getCellValue(row.getCell(2));
                    String NmaeErr = "";
                    if (!StringUtil.isEmpty(name) && !StringUtil.isEmpty(idTypeName)) {
                        if (idTypeName.equals("外国公民护照")) {
//                            NmaeErr = CheckUtils.checkEnglishName(name);
                        } else {
                            NmaeErr = CheckUtils.checkChineseName(name);
                        }
                        if (!StringUtil.isEmpty(NmaeErr)) {
                            errorMsg = "第" + (i - 1) + "行,姓名填写格式有误：" + NmaeErr;
                            throw new RuntimeException();
                        }
                    }
                    // 校验减人的减保生效日期应不超过往前的30天（只支持往前回溯）
                    String ztaliDate = ExcelUtil.getCellValue(row.getCell(6));
                    if (StringUtils.isNotBlank(ztaliDate)) {
                        if (ztaliDate.compareTo(DateTimeUtil.getCurrentDate()) > 0) {
                            errorMsg += "清单第" + (i - 1) + "行，减保生效日应小于等于当前日期；";
                        } else {
                            String endDate = DateTimeUtil.plusDay(-30, DateTimeUtil.getCurrentDate());
                            if (endDate.compareTo(ztaliDate) > 0) {
                                errorMsg += "清单第" + (i - 1) + "行，减保生效日期应大于等于当前日期-30天";
                            }
                        }
                    }

                    HashMap<String, String> hashMap = new HashMap<>();
                    hashMap.put("serialNo", String.valueOf(rowNum));
                    hashMap.put("name", ExcelUtil.getCellValue(row.getCell(1)));
                    // 证件类型码值转换
                    String sexExcel = ExcelUtil.getCellFormula(row.getCell(4));
                    String sex = "";
                    String idTypeExcel = ExcelUtil.getCellValue(row.getCell(2));
                    String idType = "";
                    Map<String, String> map = getSexByIdType(idType, idTypeExcel, idTypeCodeList, sex, sexExcel, sexCodeList);
                    hashMap.put("sex", map.get("sex"));
                    hashMap.put("idType", map.get("idType"));
                    hashMap.put("birthDay", ExcelUtil.getCellFormula(row.getCell(5)));
                    hashMap.put("idNo", ExcelUtil.getCellValue(row.getCell(3)));
                    hashMap.put("ztaliDate", ExcelUtil.getCellValue(row.getCell(6)));
                    //校验国籍
                    String nativeplaceKey = "";
                    if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(2)))) {
                        nativeplaceKey = fdCodeMapper.selectKeyByCodeName("nativeplace", ExcelUtil.getCellValue(row.getCell(2)));
                    }
                    //人员信息
                    checkCustomer.add(EvaluationCustomer.builder()
                            .name(name)
                            .idType(CoreIdType.getNameByCoreId(map.get("idType")).name())
                            .idNo(ExcelUtil.getCellValue(row.getCell(3)))
                            .gender(GenderType.getGenderByCoreId(map.get("sex")).name())
                            .birthday(ExcelUtil.getCellFormula(row.getCell(5)))
                            .nationality(nativeplaceKey)
                            .build());
                    /* hashMap.put("refundInstruct",ExcelUtil.getCellFormula(row.getCell(7))); */
                    mapList.add(hashMap);
                    idNoList.add(ExcelUtil.getCellValue(row.getCell(3)));
                }
            }
            // 判断模板中是否存在相同的证件号
            HashSet<String> set = new HashSet<>(idNoList);
            if (idNoList.size() != set.size()) {
                // 获得list与set的差集
                Collection rs = CollectionUtils.disjunction(idNoList, set);
                // 将collection转换为list
                List<String> list1 = new ArrayList<>(rs);
                for (String str : list1) {
                    String serialNo = "";
                    for (Map<String, String> hashMap : mapList) {
                        if (hashMap.get("idNo").equals(str)) {
                            serialNo += hashMap.get("serialNo") + ",";
                        }
                    }
                    errorMsg = "第" + serialNo.substring(0, serialNo.length() - 1) + "行证件号重复";
                    throw new RuntimeException();
                }
            }
            for (HashMap<String, String> packageMap : mapList) {
                FCEdorReduInsured fcEdorReduInsured = new FCEdorReduInsured();
                String decInsuredSn = maxNoService.createMaxNo("FcEdorReduInsured", "SUB", 20);
                fcEdorReduInsured.setGrpNo(globalInput.getGrpNo());
                // 流水号
                fcEdorReduInsured.setDecInsuredSn(decInsuredSn);
                // 姓名
                fcEdorReduInsured.setName(packageMap.get("name"));
                // 性别
                fcEdorReduInsured.setSex(packageMap.get("sex"));
                // 出生日期
                fcEdorReduInsured.setBirthDay(packageMap.get("birthDay"));
                // 证件类型
                fcEdorReduInsured.setIdType(packageMap.get("idType"));
                // 证件号
                fcEdorReduInsured.setIdNo(packageMap.get("idNo"));
                // 减保生效日期
                fcEdorReduInsured.setZtaliDate(packageMap.get("ztaliDate"));
                // 退费说明
                fcEdorReduInsured.setRefundInstruct("0");
                // 批次号(待定)
                fcEdorReduInsured.setBatch(bacth);
                //// 试算状态0-未提交1-已提交2-申请完成
                fcEdorReduInsured.setTrialStatus("0");
                // 团体保单号(待定)
                fcEdorReduInsured.setGrpContNo(grpContNo);
                // 保单类型
                fcEdorReduInsured.setEdorType("NT");
                // 操作员
                fcEdorReduInsured.setOperator(globalInput.getUserNo());
                // 时间日期
                fcEdorReduInsured = CommonUtil.initObject(fcEdorReduInsured, "INSERT");
                insuredList.add(fcEdorReduInsured);
                count++;
            }
        } catch (Exception e) {
            log.info("", e);
            log.info("减少被保险人清单导入失败：" + errorMsg);
        }

        List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(checkCustomer, IdCardVerifyRequest.Verify.class);
        //校验二要素
        String failVerifies = addressCheckService.checkIdCard(verifies);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(failVerifies)) {
            errorMsg += failVerifies;
        }
        CheckCustomerVO checkLevelVO = addressCheckService.checkCustomerLevel(InterfaceType.NATURAL_PERSONS.name(), checkCustomer);
        String privateRiskLevel = checkLevelVO.getPrivateRiskLevel();
        String publicRiskLevel = checkLevelVO.getPublicRiskLevel();
        if (StringUtils.isNotEmpty(privateRiskLevel)) {
            errorMsg += "尊敬的客户，您本次提交的保险业务申请，因客户" + privateRiskLevel + "的身份信息需要进一步确认，暂不接受线上申请！如有疑问，可联系您的横琴人寿对接人员或客服热线400-69-12345咨询。";
        }
        if (StringUtils.isNotEmpty(publicRiskLevel)) {
            errorMsg += "尊敬的客户，您本次提交的保险业务申请，因客户" + publicRiskLevel + "的身份信息需要进一步确认，暂不接受线上申请！如有疑问，可联系您的横琴人寿对接人员或客服热线400-69-12345咨询。";
        }
        if (!errorMsg.equals("")) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", errorMsg);
            return resultMap;
        }
        try {
            fcEdorReduInsuredMapper.insertNext(insuredList);
            resultMap.put("success", true);
            resultMap.put("count", count);
            resultMap.put("code", "200");
            resultMap.put("message", "减少被保人清单导入成功");
        } catch (Exception e) {
            log.info("员工清单导入失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "减少被保人清单导入失败");
            throw new RuntimeException();
        }
        return resultMap;
    }

    //保全申请结果反馈
    public String insuredReponseResult(String requestInfo) {
        Map<String, Object> resultMap = new HashMap<>();
        TransResult transResult = new TransResult();
        try {
            if ("".equals(requestInfo)) {
                return "请求报文不能为空";
            }
            JSONObject json = JSON.parseObject(requestInfo);
            String transRefGUID = json.getString("transRefGUID");
            String transType = json.getString("transType");
            String transExeDate = json.getString("transExeDate");
            String transExeTime = json.getString("transExeTime");
            List<ReponseResultInfo> requestInfoList = JSONArray.parseArray(json.getJSONArray("EdorResult").toString(), ReponseResultInfo.class);
            if ("".equals(transRefGUID)) {
                return "交易ID不能为空";
            }
            if ("".equals(transType)) {
                return "交易类型不能为空";
            }
            if ("".equals(transExeDate)) {
                return "交易日期不能为空";
            }
            if ("".equals(transExeTime)) {
                return "交易时间不能为空";
            }
            if (requestInfoList.size() < 1) {
                return "保全申请结果不能为空";
            }
            log.info("requestInfoList长度：" + requestInfoList.size());
            for (ReponseResultInfo reponseResultInfo : requestInfoList) {
                log.info("反馈结果状态：" + reponseResultInfo.getEdorStatus());
                if ("0".equals(reponseResultInfo.getEdorStatus())) {
                    log.info("保全受理号：" + reponseResultInfo.getEdorAcceptNo());
                    String edorAppNo = reponseResultInfo.getEdorAcceptNo();
                    FCEdorACInfo fcEdorACInfo = fcEdorACInfoMapper.selectByPrimaryKey(edorAppNo);
                    log.info("投保资料是否为空：" + (fcEdorACInfo == null));
                    if (fcEdorACInfo != null) {
                        FCGrpInfo fcGrpInfo = new FCGrpInfo();
                        fcGrpInfo.setGrpNo(fcEdorACInfo.getGrpNo());
                        fcGrpInfo.setGrpName(fcEdorACInfo.getGrpName());
                        fcGrpInfo.setCorporationMan(fcEdorACInfo.getCorporation());
                        fcGrpInfo.setGrpBankCode(fcEdorACInfo.getBankCode());
                        fcGrpInfo.setGrpBankAccNo(fcEdorACInfo.getBankAccNo());
                        fcGrpInfo.setGrpAddRess(fcEdorACInfo.getGrpAddress());
                        fcGrpInfo.setZipCode(fcEdorACInfo.getZipCode());
                        fcGrpInfo.setEmail(fcEdorACInfo.getEmail());
                        fcGrpInfo = (FCGrpInfo) CommonUtil.initObject(fcGrpInfo, "UPDATE");
                        log.info(JSON.toJSONString(fcGrpInfo));
                        int i = fcGrpInfoMapper.updateByPrimaryKeySelective(fcGrpInfo);
                        log.info("同步结果" + 1);
                        if (i > 0) {
                            log.info("投保企业资料变更数据同步更新完成。。。");
                        }
                    }
                }
            }
            resultMap.put("TransRefGUID", UUID.randomUUID().toString().replaceAll("-", ""));
            resultMap.put("TransType", transType);
            resultMap.put("TransExeDate", DateTimeUtil.getCurrentDate());
            resultMap.put("TransExeTime", DateTimeUtil.getCurrentTime());
            transResult.setResultCode("200");
            transResult.setResultInfo("调用接口成功");
            resultMap.put("TransResult", transResult);
        } catch (Exception e) {
            transResult.setResultCode("500");
            transResult.setResultInfo("调用接口失败");
            resultMap.put("TransResult", transResult);

        }
        return JSON.toJSONString(resultMap);
    }

    //保全查询
    public String getEdorInfo(Map<String, String> paramMap) {
        Map<String, Object> resultMap = new HashMap<>();
        RemoteDelegate rd = RemoteDelegate.getInstance();
        try {
            if (paramMap.get("GrpContNo") == null || "".equals(paramMap.get("GrpContNo"))) {
                log.info("getEdorInfo根据保单号查询该保单的保全申请记录：团体保单号为必录项");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "团体保单号为必录项！");
                return JSON.toJSONString(resultMap);
            }
            String requestXml = "<?xml version=\"1.0\"?>\n" + "<RequestInfo>\n" + "\t<HEAD>\n" + "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" + "\t\t<TransType>YUG005</TransType>\n" + "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" + "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" + "\t</HEAD>\n" + "\t<BODY>\n" + "\t\t<EdorQuery>\n" + "\t\t\t<GrpContNo>" + paramMap.get("GrpContNo") + "</GrpContNo>\n" + "\t\t\t<StartDate>" + paramMap.get("StartDate") + "</StartDate>\n" + "\t\t\t<EndDate>" + paramMap.get("EndDate") + "</EndDate>\n" + "\t\t\t<EdorNo>" + paramMap.get("EdorNo") + "</EdorNo>\t\n" + "\t\t</EdorQuery>\n" + "\t</BODY>\n" + "</RequestInfo>";
            log.info("调用核心保全申请记录查询接口请求报文：" + requestXml);
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "BenefitService", requestXml);
            log.info("调用核心保全申请记录查询接口用时：" + "");
            if (success) {
                Map<String, Object> responseXml = rd.getResult();
                Body body = (Body) responseXml.get("Body");
                resultMap.put("data", body);
                resultMap.put("code", "200");
                resultMap.put("message", "保全申请记录查询成功");
            }
        } catch (Exception e) {
            resultMap.put("code", "500");
            resultMap.put("message", "保全申请记录查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 邮编校验接口
     *
     * @param ZipCode
     * @return
     */
    public Map<String, Object> ZipCodeCheck(String ZipCode) {
        Map<String, Object> resultMap = new HashMap<>();
        FDCodeZip fdCodeZip = fdCodeZipMapper.selectByPrimaryKey(ZipCode);
        if (fdCodeZip == null) {
            resultMap.put("success", false);
            resultMap.put("message", "所录邮政编码不存在,请重新录入。");
            resultMap.put("code", "500");
        } else {
            resultMap.put("success", true);
            resultMap.put("code", "200");
        }
        return resultMap;
    }

    // 查询最大批次号
    public String findInsuredBatch(GetInsuredBatchReq getInsuredBatchReq) {
        // todo 此处的批次处理的逻辑存在问题，没有设置前缀，后面查询和生成保全受理号的逻辑放在申请保全按钮的位置去调用。
        /**
         * 定义返回结果
         */
        Map<String, Object> resultMap = new HashMap<>();

        /**
         * 保全的批次号
         */
        // 定义加人最大的批次号，该批次号也用作被保险人资料变更、身故受益人资料变更、投保单位变更
        String addInsuredBatch = "";
        // 定义减人最大的批次号
        String reduInsuredBatch = "";
        // 定义同质风险加减人的批次号
        String hrarInsuredBatch = "";
        // 定义被保险人资料变更
        String insuredChgBatch = "";
        // 定义身故受益人及资料变更
        String bnfChgBatch = "";
        // 定义投保单位资料变更
        String policyHolderChgBatch = "";

        // 查询申请中的批次号
        String grpContNo = getInsuredBatchReq.getGrpContNo();
        FcEdorItem fcEdorItem = new FcEdorItem();
        fcEdorItem.setGrpContNo(grpContNo);
        fcEdorItem.setEdorState(EdorStateEnum.APPLYING.getCode());
        List<FcEdorItem> fcEdorItems = fcEdorItemMapper.selectEdorItemInfo(fcEdorItem);
        if (fcEdorItems.size() > 3) {
            resultMap.put("code", "500");
            resultMap.put("message", "保单下保全申请项数据存在问题，存在多条申请中的保全项！");
            return JSON.toJSONString(resultMap);
        }
        for (FcEdorItem edorItem : fcEdorItems) {
            if (edorItem.getEdorType().equals(EdorTypeEnum.EDORNI.getCode())) {
                addInsuredBatch = edorItem.getEdorBatch();
            }
            if (edorItem.getEdorType().equals(EdorTypeEnum.EDORZT.getCode())) {
                reduInsuredBatch = edorItem.getEdorBatch();
            }
            if (edorItem.getEdorType().equals(EdorTypeEnum.EDORNZ.getCode())) {
                hrarInsuredBatch = edorItem.getEdorBatch();
            }
            if (edorItem.getEdorType().equals(EdorTypeEnum.EDORIC.getCode())) {
                insuredChgBatch = edorItem.getEdorBatch();
            }
            if (edorItem.getEdorType().equals(EdorTypeEnum.EDORBC.getCode())) {
                bnfChgBatch = edorItem.getEdorBatch();
            }
            if (edorItem.getEdorType().equals(EdorTypeEnum.EDORAC.getCode())) {
                policyHolderChgBatch = edorItem.getEdorBatch();
            }

        }
        if (StringUtils.isBlank(addInsuredBatch)) {
            // 获取加人最大的批次号
            addInsuredBatch = maxNoService.createMaxNo("addInsuredBatch", null, 20);
        }
        if (StringUtils.isBlank(reduInsuredBatch)) {
            // 获取减人最大的批次号
            reduInsuredBatch = maxNoService.createMaxNo("reduInsuredBatch", null, 20);
        }
        if (StringUtils.isBlank(hrarInsuredBatch)) {
            // 获取同质风险加减人的批次号
            hrarInsuredBatch = maxNoService.createMaxNo("hrarInsuredBatch", null, 20);
        }
        if (StringUtils.isBlank(insuredChgBatch)) {
            // 获取被保险人资料变更的批次号
            insuredChgBatch = maxNoService.createMaxNo("insuredChgBatch", null, 20);
        }
        if (StringUtils.isBlank(bnfChgBatch)) {
            // 获身故受益人及资料变更的批次号
            bnfChgBatch = maxNoService.createMaxNo("bnfChgBatch", null, 20);
        }
        if (StringUtils.isBlank(policyHolderChgBatch)) {
            // 获取投保单位资料的批次号
            policyHolderChgBatch = maxNoService.createMaxNo("policyHolderChgBatch", null, 20);
        }

        resultMap.put("addInsuredBatch", addInsuredBatch);
        resultMap.put("reduInsuredBatch", reduInsuredBatch);
        resultMap.put("hrarInsuredBatch", hrarInsuredBatch);
        resultMap.put("insuredChgBatch", insuredChgBatch);
        resultMap.put("bnfChgBatch", bnfChgBatch);
        resultMap.put("policyHolderChgBatch", policyHolderChgBatch);
        resultMap.put("code", "200");
        resultMap.put("message", "查询成功");
        return JSON.toJSONString(resultMap);
    }

    public boolean InsertEdorAddPlanInfo(GlobalInput globalInput, String grpContNo, String batch, List<Plan> planList) {
        List<FCEdorAddPlanInfo> fcEdorAddPlanInfoList = new ArrayList<>();
        List<FCEdorAddPlanRiskInfo> fcEdorAddPlanRiskInfoList = new ArrayList<>();
        List<FCEdorAddPlanRiskDutyInfo> fcEdorAddPlanRiskDutyInfoList = new ArrayList<>();
        try {
            for (int i = 0; i < planList.size(); i++) {
                FCEdorAddPlanInfo fcEdorAddPlanInfo = new FCEdorAddPlanInfo();
                String edorAddPlanSN = maxNoService.createMaxNo("EdorAddPlanSN", null, 20);
                fcEdorAddPlanInfo.setEdorAddPlanSN(edorAddPlanSN);
                fcEdorAddPlanInfo.setGrpContNo(grpContNo);
                fcEdorAddPlanInfo.setBatch(batch);
                fcEdorAddPlanInfo.setPlanCode(planList.get(i).getPlancode());
                fcEdorAddPlanInfo.setPlanName(planList.get(i).getPlanName());
                fcEdorAddPlanInfo.setPlanObject(planList.get(i).getPlanObject());
                fcEdorAddPlanInfo.setTotalPrem(BigDecimal.valueOf(Double.parseDouble(planList.get(i).getTotalPrem())));
                fcEdorAddPlanInfo.setOperator(globalInput.getCustomNo());
                fcEdorAddPlanInfo = CommonUtil.initObject(fcEdorAddPlanInfo, "INSERT");
                fcEdorAddPlanInfoList.add(fcEdorAddPlanInfo);
                for (Risk risk : planList.get(i).getRiskList()) {
                    FCEdorAddPlanRiskInfo fcEdorAddPlanRiskInfo = new FCEdorAddPlanRiskInfo();
                    fcEdorAddPlanRiskInfo.setEdorAddPlanSN(edorAddPlanSN);
                    fcEdorAddPlanRiskInfo.setRiskCode(risk.getRiskCode());
                    fcEdorAddPlanRiskInfo.setGrpContNo(grpContNo);
                    fcEdorAddPlanRiskInfo.setRiskName(risk.getRiskName());
                    fcEdorAddPlanRiskInfo.setRiskAmnt(BigDecimal.valueOf(Double.parseDouble(risk.getRiskAmnt())));
                    fcEdorAddPlanRiskInfo.setRiskPrem(BigDecimal.valueOf(Double.parseDouble(risk.getRiskPrem())));
                    fcEdorAddPlanRiskInfo.setOperator(globalInput.getCustomNo());
                    fcEdorAddPlanRiskInfo = CommonUtil.initObject(fcEdorAddPlanRiskInfo, "INSERT");
                    fcEdorAddPlanRiskInfoList.add(fcEdorAddPlanRiskInfo);
                    for (Duty duty : risk.getDutyList()) {
                        FCEdorAddPlanRiskDutyInfo fcEdorAddPlanRiskDutyInfo = new FCEdorAddPlanRiskDutyInfo();
                        fcEdorAddPlanRiskDutyInfo.setEdorAddPlanSN(edorAddPlanSN);
                        fcEdorAddPlanRiskDutyInfo.setRiskCode(risk.getRiskCode());
                        fcEdorAddPlanRiskDutyInfo.setDutyCode(duty.getDutyCode());
                        fcEdorAddPlanRiskDutyInfo.setGrpContNo(grpContNo);
                        fcEdorAddPlanRiskDutyInfo.setDutyName(duty.getDutyName());
                        fcEdorAddPlanRiskDutyInfo.setDutyAmnt(BigDecimal.valueOf(Double.parseDouble(duty.getDutyAmnt())));
                        fcEdorAddPlanRiskDutyInfo.setDutyPrem(BigDecimal.valueOf(Double.parseDouble(duty.getDutyPrem())));
                        fcEdorAddPlanRiskDutyInfo.setGetLimit(StringUtils.isBlank(duty.getGetLimit()) ? null : Double.parseDouble(duty.getGetLimit()));
                        fcEdorAddPlanRiskDutyInfo.setGetRate(StringUtils.isBlank(duty.getGetRate()) ? null : Double.parseDouble(duty.getGetRate()));
                        fcEdorAddPlanRiskDutyInfo.setOperator(globalInput.getCustomNo());
                        fcEdorAddPlanRiskDutyInfo = CommonUtil.initObject(fcEdorAddPlanRiskDutyInfo, "INSERT");
                        fcEdorAddPlanRiskDutyInfoList.add(fcEdorAddPlanRiskDutyInfo);
                    }
                }
            }
            int i = fcEdorAddPlanInfoMapper.insertEdorAddPlanList(fcEdorAddPlanInfoList);
            int i1 = fcEdorAddPlanRiskInfoMapper.insertEdorAddPlanRiskList(fcEdorAddPlanRiskInfoList);
            int i2 = fcEdorAddPlanRiskDutyInfoMapper.insertEdorAddPlanRiskDutyList(fcEdorAddPlanRiskDutyInfoList);
            System.out.println("=====" + i + i1 + i2);
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            return false;
        }
        return true;
    }

    // 增加被保险人-告知书上传
    public String InsuredZipFileUpload(String token, MultipartFile file, String grpContNo) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", true);
        resultMap.put("code", "200");
        resultMap.put("message", "被保人告知书上传成功!");
        try {
            // 上传到本地
            String localFilePath = FileUtil.getLocalPath("0304");// 被保人告知书上传
            String fileName = file.getOriginalFilename();
            String filePath = localFilePath + fileName;
            boolean uploadSuccess = fileService.fileUpload(file, filePath, "0304", "");
            if (!uploadSuccess) {
                log.info("被保人告知书上传本地失败！");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "被保人告知书上传失败!");
            } else {
                // 上传到FTP
                String ftpFilePath = FileUtil.getFtpPath("0304", "");
                // 查询ftp信息上传到SFTP
                FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
                // 封装上传实体类
                SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
                // 上传操作
                boolean flag = sFtp.uploadFile(fdftpInfo.getFtprootpath() + ftpFilePath + fileName, localFilePath + fileName);
                if (!flag) {
                    log.info("被保人告知书上传FTP服务器失败！");
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "被保人告知书上传失败!");
                }
            }

        } catch (Exception e) {
            log.info("被保人告知书上传失败!：" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "被保人告知书上传失败!");
        }

        return JSON.toJSONString(resultMap);
    }

    // 保全证明文件上传
    public String uploadZipOrRARFile(String token, MultipartFile file, String grpContNo, String batch, String docType) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", true);
        resultMap.put("code", "200");
        resultMap.put("message", "保全证明文件上传成功!");
        GlobalInput globalInput = userService.getSession(token);
        try {
            // 校验参数
            if (StringUtils.isBlank(grpContNo) || StringUtils.isBlank(batch)) {
                resultMap.put("success", true);
                resultMap.put("code", "500");
                resultMap.put("message", "保全证明文件上传请求参数缺失，请重新申请保全项！");
                return JSON.toJSONString(resultMap);
            }
            // 校验上传文件格式
            String fileName = file.getOriginalFilename();
            String suffixFileName = fileName.substring(fileName.lastIndexOf("."));
            String suffixFilelistName = fileName.substring(0, fileName.lastIndexOf("."));
            if (!suffixFileName.matches("^\\.(?i)(zip)$")) {
                resultMap.put("success", true);
                resultMap.put("code", "500");
                resultMap.put("message", "保全证明文件上传格式不正确！(暂时仅支持上传zip格式压缩文件)");
                return JSON.toJSONString(resultMap);
            } else {
/*********************************************************本地处理文件***************************************************/
                // 上传到本地,再对其进行操作
                String localFilePath = FileUtil.getLocalPath(docType, batch);// 保全证明文件上传
                // 替换原来的文件（不在解压文件类处理，预防问题的严重性）
                if (StringUtils.isNotBlank(localFilePath)) {
                    File detelefile = new File(localFilePath);
                    if (detelefile.exists()) {
                        FileUtil.delete(detelefile);
                    }
                }
                String filePath = localFilePath + fileName;
                boolean uploadSuccess = fileService.fileUpload(file, filePath, docType, "");
                if (!uploadSuccess) {
                    log.info("保全证明文件上传本地失败！");
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "保全证明文件上传到本地失败!");
                    return JSON.toJSONString(resultMap);
                } else {
                    /*
                     * localFilePath =
                     * localFilePath+fileName.substring(0,fileName.lastIndexOf("."))+"/";
                     */
                    switch (suffixFileName.toLowerCase()) {
                        case ".zip":
                            // 解压文件
                            UnFileUtil.unZipFiles(new File(filePath), localFilePath);
                            break;
					/*case ".rar":
						//解压文件
						UnFileUtil.unRarFile(filePath,localFilePath);
						break;*/
                    }
                    // 获取文件,校验文件
                    List<File> Filelist = getFiles(localFilePath + suffixFilelistName);
                    for (File Singlefile : Filelist) {
                        //统一定义错误返回
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        //规则校验项
                        if (!Singlefile.isFile() || Singlefile.list() != null || Singlefile.isDirectory()) {
                            log.info("保全证明文件内部文件格式不正确,存有多个层级！");
                            resultMap.put("message", "保全证明文件压缩包内部文件格式不正确（压缩包中不允许存在文件夹）!");
                            return JSON.toJSONString(resultMap);
                        } else {
                            String SinglefileName = Singlefile.getName();
                            if (org.springframework.util.StringUtils.isEmpty(SinglefileName)) {
                                resultMap.put("message", "保全证明文件名称为空！");
                                return JSON.toJSONString(resultMap);
                            }
                            String prefixSingleFileName = SinglefileName.substring(0, SinglefileName.lastIndexOf("."));
                            log.info("压缩包内部文件名称: {}", prefixSingleFileName);
                            if (!prefixSingleFileName.matches("^[-\\u2E80-\\uFE4F_a-zA-Z0-9]+$")) {
                                resultMap.put("message", "保全内部文件名称不正确，不能含有特殊字符!");
                                return JSON.toJSONString(resultMap);
                            }
                            String suffixSingleFileName = SinglefileName.substring(SinglefileName.lastIndexOf("."));
                            if (!suffixSingleFileName.matches("^\\.(?i)(jpg|png|jpeg|)$")) {
                                resultMap.put("message", "保全证明文件内部文件格式不正确（仅支持图片）!（暂时仅支持jpg/png/jpeg格式图片）");
                                return JSON.toJSONString(resultMap);
                            }
                        }
                        //复位
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                    }
                    new ImageToTif().dealImage(Filelist);// 将图片转为TIF
/*********************************************************上传文件到FTP(这个操作暂时不放在这里，只存表)***************************************************/
                    // 删除保全影像件表
                    Map<String, Object> map = new HashMap<>();
                    map.put("grpContNo", grpContNo);
                    map.put("batch", batch);
                    map.put("docType", docType);
                    fcEdoruploadfileMapper.deleteUploadfile(map);
                    // 插入保全影像件表
                    for (File Onefile : Filelist) {
                        String OnefileName = Onefile.getName();
                        OnefileName = OnefileName.replace(OnefileName.substring(OnefileName.lastIndexOf(".") + 1, OnefileName.length()), "tif");
                        // 上传文件到FTP
                        String ftpFilePath = FileUtil.getFtpPath(docType, "");
                        // 查询ftp信息上传到SFTP
                        FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
                        // 上传操作
                        String localPath = localFilePath + suffixFilelistName + "/" + OnefileName;
                        String ftpPath = fdftpInfo.getFtprootpath() + ftpFilePath + batch + "/" + suffixFilelistName + "/" + OnefileName;
                        FCEdoruploadfile fcEdoruploadfile = new FCEdoruploadfile();
                        fcEdoruploadfile.setGrpContNo(grpContNo);
                        fcEdoruploadfile.setBatch(batch);
                        fcEdoruploadfile.setDocType(docType);
                        fcEdoruploadfile.setFileName(OnefileName);
                        fcEdoruploadfile.setLocalPath(localPath);
                        fcEdoruploadfile.setFtpPath(ftpPath);
                        fcEdoruploadfile.setOperator(globalInput.customNo);
                        fcEdoruploadfile = CommonUtil.initObject(fcEdoruploadfile, "INSERT");
                        fcEdoruploadfileMapper.insertSelective(fcEdoruploadfile);
                    }
                }

            }
        } catch (Exception e) {
            log.info("保全证明文件上传失败!：" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保全证明文件上传失败!");
        }

        return JSON.toJSONString(resultMap);
    }

    // 保全证明文件删除
    public String deleteUploadZipOrRARFile(String token, Map<String, String> parmmap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", true);
        resultMap.put("code", "200");
        resultMap.put("message", "保全证明文件删除成功!");
        try {
            String grpContNo = parmmap.get("grpContNo");
            String batch = parmmap.get("batch");
            String fileName = parmmap.get("fileName");
            String docType = parmmap.get("docType");
            if (StringUtils.isBlank(grpContNo) || StringUtils.isBlank(batch) || StringUtils.isBlank(fileName) || StringUtils.isBlank(docType)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "保全证明文件删除失败:参数有误!");
                return JSON.toJSONString(resultMap);
            }
            String localFilePath = FileUtil.getLocalPath(docType, batch);// 保全证明文件上传地址
            String suffixFilelistName = fileName.substring(0, fileName.lastIndexOf("."));// 文件名无后缀
            // 替换原来的文件（不在解压文件类处理，预防问题的严重性）
            if (StringUtils.isNotBlank(localFilePath) && StringUtils.isNotBlank(suffixFilelistName)) {
                // 删除表中的数据
                Map<String, Object> map = new HashMap<>();
                map.put("grpContNo", grpContNo);
                map.put("batch", batch);
                map.put("docType", docType);
//				map.put("localPath",localFilePath + suffixFilelistName);
                fcEdoruploadfileMapper.deleteUploadfile(map);
                // 删除文件
                File detelefile = new File(localFilePath);
                if (detelefile.exists()) {
                    FileUtil.delete(detelefile);
                }
            }
        } catch (Exception e) {
            log.info("保全证明文件删除失败!：" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保全证明文件删除失败!");
        }
        return JSON.toJSONString(resultMap);
    }

    // 同质风险加减人
    public String importHomogeneousRiskAddOrReducePeople(String token, MultipartFile file, String batch, String grpContNo) {
        Map<String, Object> resultMap = new HashMap<>();
        String path = FileUtil.getLocalPath("0305");
        String fileName = file.getOriginalFilename();
        String filePath = path + fileName;
        boolean uploadSuccess = fileService.fileUpload(file, filePath, "0305", "");
        if (!uploadSuccess) {
            log.info("同质风险加减人清单导入失败：文件上传失败！");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "同质风险加减人清单导入文件失败");
            return JSON.toJSONString(resultMap);
        }
        Workbook wb = null;
        try {
            wb = ExcelUtil.initWorkbook(filePath);
            // 处理业务，解析计划以及人员清单
            resultMap = importHomogeneousRiskNI.dealHomogeneousRiskAddOrReducePeopleExcel(token, wb, batch, grpContNo);
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "同质风险加减人失败");
            return JSON.toJSONString(resultMap);
        }
        //记录当前保全申请状态
        saveEdorState(token, batch, grpContNo, EdorTypeEnum.EDORNZ.getCode(), EdorStateEnum.APPLYING.getCode());
        return JSON.toJSONString(resultMap);

    }

    /**
     * 保单计划列表查询
     *
     * @param token
     * @param selectPolicyPlanReq
     * @return
     */
    @Transactional
    public String selectPolicyPlan(String token, SelectPolicyPlanReq selectPolicyPlanReq) {
        /**
         * 定义返回结果
         */
        Map<String, Object> resultMap = new HashMap<>();

        /**
         * 校验请求参数
         */
        // 保单号
        String grpContNo = selectPolicyPlanReq.getGrpContNo();

        RemoteDelegate rd = RemoteDelegate.getInstance();
        try {
            String batch = selectPolicyPlanReq.getBatch();

            GlobalInput globalInput = userService.getSession(token);
            // 校验入参
            if (StringUtils.isBlank(grpContNo)) {
                log.info("保单计划列表查询失败,保单号为空");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "保单号为必录项！");
                return JSON.toJSONString(resultMap);
            }
            String requestXml = "<?xml version=\"1.0\"?>\n" + "<RequestInfo>\n" + "\t<HEAD>\n" + "\t\t<TransRefGUID>"
                    + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n"
                    + "\t\t<TransType>YUG012</TransType>\n" + "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate()
                    + "</TransExeDate>\n" + "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n"
                    + "\t</HEAD>\n" + "\t<BODY>\n" + "\t\t<GrpContNo>" + grpContNo + "</GrpContNo>\n" + "\t\t<PageNum>"
                    + selectPolicyPlanReq.getPageNum() + "</PageNum>\n" + "\t\t<PageSize>"
                    + selectPolicyPlanReq.getPageSize() + "</PageSize>\n" + "\t</BODY>\n" + "</RequestInfo>";
            log.info("调用核心保单计划列表查询接口请求报文：" + requestXml);
            long startStamp = System.currentTimeMillis();
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"),
                    myProps.getServiceInfo().get("operation"), "BenefitService", requestXml);
            long endStamp = System.currentTimeMillis();
            log.info("调用接口用时：" + (endStamp - startStamp));
            if (success) {
                Map<String, Object> responseXml = rd.getResult();
                Body body = (Body) responseXml.get("Body");
                List<Plan> list = body.getPlanList();
                if (list.size() > 0) {
                    // 先删除，后新增
                    fcEdorAddPlanInfoMapper.deleteByGrpContNo(grpContNo);
                    fcEdorAddPlanRiskInfoMapper.deleteByGrpContNo(grpContNo);
                    fcEdorAddPlanRiskDutyInfoMapper.deleteByGrpContNo(grpContNo);
                    if (InsertEdorAddPlanInfo(globalInput, grpContNo, batch, list)) {
                        log.info("保单计划列表存储成功。");
                    } else {
                        log.info("保单计划列表落地平台数据库失败。");
                    }
                } else {
                    log.info("List<plan>.size()=0");
                }
                return JSONObject.toJSONString(ResponseResultUtil.success(PageResp.restPage(list)));
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("code", "500");
            resultMap.put("message", "保单计划列表查询失败");
        }
        return JSONObject.toJSONString(resultMap);
    }


    // 循环遍历文件
    public ArrayList<File> getFiles(String path) throws Exception {
        // 目标集合fileList
        ArrayList<File> fileList = new ArrayList<File>();
        File file = new File(path);
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (File fileIndex : files) {
                // 如果这个文件是目录，则进行递归搜索
//				if(fileIndex.isDirectory()){
//					getFiles(fileIndex.getPath());
//				}else {
                // 如果文件是普通文件，则将文件句柄放入集合中
                fileList.add(fileIndex);
//				}
            }
        }
        return fileList;
    }

    /**
     * 获取同质风险加减人列表
     *
     * @param token
     * @param selectEdorHomogeneousRiskInsuredReq
     */
    public String selectEdorHomogeneousRiskInsured(String token, SelectEdorHomogeneousRiskInsuredReq selectEdorHomogeneousRiskInsuredReq) {
        /**
         * 获取基本参数
         */
        GlobalInput globalInput = userService.getSession(token);
        String grpNo = globalInput.getGrpNo();
        selectEdorHomogeneousRiskInsuredReq.setGrpNo(grpNo);
        // 获取页面参数
        Integer page = selectEdorHomogeneousRiskInsuredReq.getPageNum();
        Integer rows = selectEdorHomogeneousRiskInsuredReq.getPageSize();

        /**
         * 封装请求参数
         */
        PageHelper.startPage(page, rows);
        List<FCEdorHomogeneousRiskInsuredInfo> fcEdorHomogeneousRiskInsuredInfos = fcEdorHomogeneousRiskInsuredMapper.selectEdorHomogeneousRiskInsured(selectEdorHomogeneousRiskInsuredReq);
        return JSONObject.toJSONString(ResponseResultUtil.success(PageResp.restPage(fcEdorHomogeneousRiskInsuredInfos)));
    }


    /**
     * 记录当前保全申请状态
     *
     * @param token
     * @param batch     保全批次号
     * @param grpContNo 保单号
     * @param edorType  保全类型
     * @param edorState 保全状态
     */
    public void saveEdorState(String token, String batch, String grpContNo, String edorType, String edorState) {
        GlobalInput globalInput = userService.getSession(token);
        FcEdorItem fcEdorItem = new FcEdorItem();
        fcEdorItem.setEdorBatch(batch);
        fcEdorItem.setGrpContNo(grpContNo);
        fcEdorItem.setEdorType(edorType);
        fcEdorItem.setEdorState(edorState);
        // 获取团体订单信息，判断是否为平台的保单 0-否 1-是
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByGrpContNo(grpContNo);
        if (ObjectUtils.isEmpty(fcGrpOrder)) {
            fcEdorItem.setIsEflexPolicy("0");
        } else {
            fcEdorItem.setIsEflexPolicy("1");
        }
        fcEdorItem.setOperator(globalInput.getUserNo());
        fcEdorItem.setOperatorCom(globalInput.getGrpNo());
        fcEdorItem = CommonUtil.initObject(fcEdorItem, "INSERT");
        fcEdorItemMapper.insertSelective(fcEdorItem);
    }

    //更新当前保全申请状态
    public void updateEdorState(String token, String batch, String edorState) {
        GlobalInput globalInput = userService.getSession(token);
        FcEdorItem fcEdorItem = new FcEdorItem();
        fcEdorItem.setEdorBatch(batch);
        fcEdorItem.setEdorState(edorState);
        fcEdorItem.setOperator(globalInput.getUserNo());
        fcEdorItem.setOperatorCom(globalInput.getGrpNo());
        fcEdorItem = CommonUtil.initObject(fcEdorItem, "UPDATE");
        fcEdorItemMapper.updateByPrimaryKeySelective(fcEdorItem);
    }

    /**
     * 查询保全减人人员信息列表
     *
     * @param token
     * @param selectEdorReduInsuredReq
     * @return
     */
    public String selectEdorReduInsured(String token, SelectEdorReduInsuredReq selectEdorReduInsuredReq) {
        Integer pageNum = selectEdorReduInsuredReq.getPageNum();
        Integer pageSize = selectEdorReduInsuredReq.getPageSize();
        // 增加分页
        PageHelper.startPage(pageNum, pageSize);
        List<FCEdorReduInsured> fcEdorReduInsuredList = fcEdorReduInsuredMapper
                .selectEdorReduInsured(selectEdorReduInsuredReq);
        return JSONObject.toJSONString(ResponseResultUtil.success(PageResp.restPage(fcEdorReduInsuredList)));
    }

    /**
     * 处理保全增人信息
     *
     * @param token
     * @param fcEdorAddInsured
     * @return
     */
    public String dealEdorAddInsured(String token, String operation, FCEdorAddInsured fcEdorAddInsured) {
        /**
         * 准备数据
         */
        String errorMsg;
        List<EvaluationCustomer> customerList = Collections.singletonList(EvaluationCustomer.builder()
                .name(fcEdorAddInsured.getName())
                .idType(CoreIdType.getNameByCoreId(fcEdorAddInsured.getIdType()).name())
                .idNo(fcEdorAddInsured.getIdNo())
                .gender(GenderType.getGenderByCoreId(fcEdorAddInsured.getSex()).name())
                .birthday(fcEdorAddInsured.getBirthday())
                .nationality(fcEdorAddInsured.getNativeplace())
                .build());
        List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
        //校验二要素
        String failVerifies = addressCheckService.checkIdCard(verifies);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(failVerifies)) {
            return JSON.toJSONString(ResultUtil.error(failVerifies));
        }
        CheckCustomerVO checkLevelVO = addressCheckService.checkCustomerLevel(InterfaceType.NATURAL_PERSONS.name(), customerList);
        String privateRiskLevel = checkLevelVO.getPrivateRiskLevel();
        String publicRiskLevel = checkLevelVO.getPublicRiskLevel();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(privateRiskLevel)) {
            errorMsg = "尊敬的客户，您本次提交的保险业务申请，因客户" + privateRiskLevel + "的身份信息需要进一步确认，暂不接受线上申请！如有疑问，可联系您的横琴人寿对接人员或客服热线400-69-12345咨询。";
            return JSON.toJSONString(ResultUtil.error(errorMsg));
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(publicRiskLevel)) {
            errorMsg = "尊敬的客户，您本次提交的保险业务申请，因客户" + publicRiskLevel + "的身份信息需要进一步确认，暂不接受线上申请！如有疑问，可联系您的横琴人寿对接人员或客服热线400-69-12345咨询。";
            return JSON.toJSONString(ResultUtil.error(errorMsg));
        }

        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);
        // 获取团体订单信息
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByGrpContNo(fcEdorAddInsured.getGrpContNo());
        //todo 这里存在矛盾，如果平台的保单没有成功反馈到平台，这样根据保单号也是查询不到团单信息的，这样就和非平台的保单冲突了。
        // 解决方案：1、核心同时把投保单号进行返回。
        // 是否为平台团单
        Boolean isEflexGrpOrder = true;
        if (ObjectUtils.isEmpty(fcGrpOrder)) {
            isEflexGrpOrder = false;
        }
        //查询企业信息
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectGrpInfo1(globalInput.getGrpNo());

        /**
         * 校验数据
         */
        checkEdorAddInsured(operation, isEflexGrpOrder, isEflexGrpOrder ? fcGrpOrder.getGrpOrderNo() : "", fcEdorAddInsured, fcGrpInfo);

        /**
         * 存储数据
         */
        if (operation.equals("add")) {
            String plusInsuredSN = maxNoService.createMaxNo("PlusInsuredSN", null, 20);
            // 被保人流水编号,最大号
            fcEdorAddInsured.setPlusInsuredSN(plusInsuredSN);
        }
        // 企业编号
        fcEdorAddInsured.setGrpNo(globalInput.getGrpNo());
        // 操作员
        fcEdorAddInsured.setOperator(globalInput.getUserNo());
        // 操作机构
        fcEdorAddInsured.setOperatorCom(globalInput.getGrpNo());
        // 提交试算状态0-未提交1-已提交
        fcEdorAddInsured.setTrialStatus(StateEnum.INVALID.getCode());
        // 与投保人关系
        fcEdorAddInsured.setRelationToAppnt(fcEdorAddInsured.getRelationship());
        // 保全类型
        fcEdorAddInsured.setEdorType("NT");
        // 单位全缴
        fcEdorAddInsured.setPayMethod("1");
        // 核心定义人员身份 SubsidiaryInsuredFlag
        // 1-非附属被保人
        // 2-附属被保人（未生成客户号，同批次添加）
        // 3-附属被保人（已经生成客户号，非同批次添加）
        if (fcEdorAddInsured.getRelation().equals(RelationMarkEnum.MYSELF.getCode())) {
            fcEdorAddInsured.setSubsidiaryInsuredFlag("1");
        } else {
            // 查询同批次的主被保险人的信息
            FCEdorAddInsured mainEdorAddInsuredParam = new FCEdorAddInsured();
            mainEdorAddInsuredParam.setGrpContNo(fcEdorAddInsured.getGrpContNo());
            mainEdorAddInsuredParam.setBatch(fcEdorAddInsured.getBatch());
            mainEdorAddInsuredParam.setIdNo(fcEdorAddInsured.getMainIdNo());
            mainEdorAddInsuredParam.setSubsidiaryInsuredFlag("1");
            FCEdorAddInsured mainEdorAddInsured = fcEdorAddInsuredMapper.selectMainEdorInsured(mainEdorAddInsuredParam);
            if (!ObjectUtils.isEmpty(mainEdorAddInsured)) {
                if (!mainEdorAddInsured.getName().equals(fcEdorAddInsured.getStaffName())) {
                    throw new SystemException("主被保险人姓名与已添加的主被保险人姓名不一致！");
                }
                if (!mainEdorAddInsured.getIdType().equals(fcEdorAddInsured.getMainIdType())) {
                    throw new SystemException("主被保险人证件类型与已添加的主被保险人证件类型不一致！");
                }
                fcEdorAddInsured.setSubsidiaryInsuredFlag("2");
            } else {
                fcEdorAddInsured.setSubsidiaryInsuredFlag("3");
            }
        }
        //保险期间
        try {
            GrpContInfo grpContInfo = selectGrpContInfo(fcGrpInfo, fcEdorAddInsured.getGrpContNo());
            Long days = DateTimeUtil.getDistanceDays(fcEdorAddInsured.getPlusEffectDate(), grpContInfo.getEndDate());
            if (days == 364 || days == 365) {
                fcEdorAddInsured.setInsuYear("1");
                fcEdorAddInsured.setInsuYearFlag("Y");
            } else {
                String daysTwo = String.valueOf(days + 1);
                fcEdorAddInsured.setInsuYear(daysTwo);
                fcEdorAddInsured.setInsuYearFlag("D");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 缴费方式 1-代表单位全缴 2-单位代扣 3-混合缴费
        fcEdorAddInsured.setPayMethod("1");
        FCEdorAddPlanInfo fcEdorAddPlanInfo = fcEdorAddPlanInfoMapper.selectEdorPlanInfo(fcEdorAddInsured.getGrpContNo(), fcEdorAddInsured.getPlanCode());
        fcEdorAddInsured.setComPayment(StringUtil.isEmpty(fcEdorAddPlanInfo.getTotalPrem()) ? 0.0 : Double.valueOf(fcEdorAddPlanInfo.getTotalPrem().doubleValue()));
        fcEdorAddInsured.setPerPayment(0.0);
        // 存储核心关系码值，已改为试算的时候转码
        // FDCode fdCode = new FDCode();
        // fdCode.setCodeKey(fcEdorAddInsured.getRelation());
        // fdCode.setCodeType("relation");
        // String coereRelation = fdCodeMapper.selectCoreCode(fdCode);
        // fcEdorAddInsured.setRelation(coereRelation);
        if (operation.equals("add")) {
            fcEdorAddInsured = CommonUtil.initObject(fcEdorAddInsured, "INSERT");
            int i = fcEdorAddInsuredMapper.insertSelective(fcEdorAddInsured);
            log.info("人员添加成功" + i + "条！ ");
        } else {
            fcEdorAddInsured.setIsError(StateEnum.INVALID.getCode());
            fcEdorAddInsured.setErrorDesc("");
            fcEdorAddInsured = CommonUtil.initObject(fcEdorAddInsured, "UPDATE");
            fcEdorAddInsuredMapper.updateByPrimaryKeySelective(fcEdorAddInsured);
            log.info("人员修改成功");
        }
        // 记录当前保全申请状态
        FcEdorItem fcEdorItem = fcEdorItemMapper.selectByPrimaryKey(fcEdorAddInsured.getBatch());
        if (ObjectUtils.isEmpty(fcEdorItem)) {
            saveEdorState(token, fcEdorAddInsured.getBatch(), fcEdorAddInsured.getGrpContNo(), EdorTypeEnum.EDORNI.getCode(), EdorStateEnum.APPLYING.getCode());
        }
        return JSON.toJSONString(ResultUtil.success("处理成功"));
    }

    /**
     * 校验被保险人信息
     *
     * @param operation
     * @param fcEdorAddInsured
     */
    private void checkEdorAddInsured(String operation, Boolean isEflexGrpOrder, String grpOrderNo, FCEdorAddInsured fcEdorAddInsured, FCGrpInfo fcGrpInfo) {

        // 获取证件类型集合
        List<String> idTypeList = fcPerInfoTempMapper.getIdTypeCodeList("01");
        // 获取职业类别集合
        List<String> occupationTypeList = fcPerInfoTempMapper.getOccupationTypeList("01");
        // 获取职业集合
        List<String> occupationCodeList = fcPerInfoTempMapper.getOccupationCodeList("01");
        // 获取职业类别与职业代码对应关系
        Map<String, List<String>> occupationTypeCodeMap = getOccupationTypeCode();

        if (StringUtils.isEmpty(operation) || !operation.matches("add|update")) {
            throw new SystemException("您的操作有误，请重新操作！");
        }
        if ("update".equals(operation)) {
            if (StringUtils.isEmpty(fcEdorAddInsured.getPlusInsuredSN())) {
                throw new SystemException("被保险人编号不能为空！");
            }
        }
        // 校验字段非空
        String errorMsg = checkOneIsEmpTy(fcEdorAddInsured);
        if (!"".equals(errorMsg)) {
            throw new SystemException(errorMsg);
        }
        // 校验证件类型代码、职业类别代码、职业代码 是否录入符合规则
        errorMsg = checkOneCodeKey(fcEdorAddInsured, idTypeList, occupationTypeList, occupationCodeList);
        if (!"".equals(errorMsg)) {
            throw new SystemException(errorMsg);
        }
        // 校验字段内容
        errorMsg = checkField(operation, isEflexGrpOrder, grpOrderNo, fcEdorAddInsured);
        if (!"".equals(errorMsg)) {
            throw new SystemException("被保人 " + fcEdorAddInsured.getName() + errorMsg);
        }
        // 校验当前职业代码是否符合当前职业类别
        if (!occupationTypeCodeMap.get(fcEdorAddInsured.getJobType()).contains(fcEdorAddInsured.getJobCode())) {
            throw new SystemException("职业类别不包含所录职业！");
        }
        // 校验增员生效日期应在保单的生效日期和截至日期之间
        if (isEflexGrpOrder) {
            /**
             * 平台的团单
             */
            FCEnsure fcEnsure = fcEnsureMapper.getFcensureByGrpContNo(fcEdorAddInsured.getGrpContNo());
            if (ObjectUtils.isEmpty(fcEnsure)) {
                throw new SystemException("未查询到对应的福利信息，请确认保单号是否正确同步！");
            }
            if (StringUtils.isNotBlank(fcEdorAddInsured.getPlusEffectDate())) {
                if (!(fcEdorAddInsured.getPlusEffectDate().compareTo(fcEnsure.getCvaliDate()) >= 0 && fcEnsure.getPolicyEndDate().compareTo(fcEdorAddInsured.getPlusEffectDate()) > 0)) {
                    throw new SystemException("增员生效日期应在保单的生效日期和截至日期之间");
                } else {
                    String endDate = null;
                    String beginDate = null;
                    try {
                        endDate = DateTimeUtil.plusDay(30, DateTimeUtil.getCurrentDate());
                        beginDate = DateTimeUtil.plusDay(-30, DateTimeUtil.getCurrentDate());
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    if (endDate.compareTo(fcEdorAddInsured.getPlusEffectDate()) < 0) {
                        throw new SystemException("增员生效日期应小于等于当前日期+30天!");
                    }
                    if (beginDate.compareTo(fcEdorAddInsured.getPlusEffectDate()) > 0) {
                        throw new SystemException("增员生效日期应大于等于当前日期-30天!");
                    }
                }
            }
        } else {
            /**
             * 非平台的单子
             */
            GrpContQuery grpContQuery = new GrpContQuery();
            grpContQuery.setGrpIdType(fcGrpInfo.getGrpIdType());
            grpContQuery.setGrpIdTypeName(fcGrpInfo.getGrpIdTypeName());
            grpContQuery.setGrpIdNo(fcGrpInfo.getGrpIdNo());
            grpContQuery.setGrpContNo(fcEdorAddInsured.getGrpContNo());
            grpContQuery.setPageNum("1");
            grpContQuery.setPageSize("10");
            XStream xStream = new XStream();
            xStream.alias("GrpContQuery", GrpContQuery.class);
            String reqXmlBody = xStream.toXML(grpContQuery);
            String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + "<RequestInfo>\n" + "\t<HEAD>\n" +
                    /* 交易流水号 */
                    "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                    /* 接口交易类型 */
                    "\t\t<TransType>BF0004</TransType>\n" +
                    /* 交易日期 */
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    /* 交易时间 */
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" + "\t</HEAD>\n" + "\t<BODY>\n" + reqXmlBody + "\t</BODY>\n" + "</RequestInfo>";
            log.info("企业保单查询接口请求报文: {}", reqXml);
            long startStamp = System.currentTimeMillis();
            //将请求报文发送核心
            RemoteDelegate rd = RemoteDelegate.getInstance();
            Map<String, String> myPrpsMap = myProps.getServiceInfo();
            boolean success = rd.submitData(myPrpsMap.get("serviceUrl"), myPrpsMap.get("operation"), "", reqXml); //BenefitService
            long endStamp = System.currentTimeMillis();
            log.info("调用核心接口用时：" + (endStamp - startStamp));
            if (success) {
                //接收返回结果
                Map<String, Object> responseXml = rd.getResult();
                List<GrpContInfo> list = (List<GrpContInfo>) responseXml.get("GrpContList");
                if (list.size() != 1) {
                    throw new BusinessException("核心保单查询接口有误！");
                } else {
                    GrpContInfo grpContInfo = list.get(0);
                    if (StringUtils.isNotBlank(fcEdorAddInsured.getPlusEffectDate())) {
                        if (!(fcEdorAddInsured.getPlusEffectDate().compareTo(grpContInfo.getEffectDate()) >= 0 && grpContInfo.getEndDate().compareTo(fcEdorAddInsured.getPlusEffectDate()) > 0)) {
                            throw new SystemException("增员生效日期应在保单的生效日期和截至日期之间");
                        } else {
                            String endDate = null;
                            String beginDate = null;
                            try {
                                endDate = DateTimeUtil.plusDay(30, DateTimeUtil.getCurrentDate());
                                beginDate = DateTimeUtil.plusDay(-30, DateTimeUtil.getCurrentDate());
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            if (endDate.compareTo(fcEdorAddInsured.getPlusEffectDate()) < 0) {
                                throw new SystemException("增员生效日期应小于等于当前日期+30天!");
                            }
                            if (beginDate.compareTo(fcEdorAddInsured.getPlusEffectDate()) > 0) {
                                throw new SystemException("增员生效日期应大于等于当前日期-30天!");
                            }
                        }
                    }
                }
            } else {
                throw new BusinessException("核心保单查询接口调用失败！");
            }
        }
    }

    /**
     * 保全员工删除
     *
     * @param deleteEdorAddInsuredReq
     * @return
     */
    @Transactional
    public void deleteEdorAddInsured(String token, DeleteEdorAddInsuredReq deleteEdorAddInsuredReq) {
        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);
        String plusInsuredSN = deleteEdorAddInsuredReq.getPlusInsuredSN();
        /**
         * 数据校验
         */
        if (StringUtils.isBlank(plusInsuredSN)) {
            throw new SystemException("保全增人ID不能为空！");
        }
        // 查询保全增人信息
        FCEdorAddInsured fcEdorAddInsured = fcEdorAddInsuredMapper.selectByPrimaryKey(plusInsuredSN);
        if (ObjectUtils.isEmpty(fcEdorAddInsured)) {
            throw new SystemException("删除的人员信息不存在！");
        }
        // 如果存在的关系为（0-本人）
        if (fcEdorAddInsured.getRelation().equals(RelationMarkEnum.MYSELF.getCode())) {
            FCEdorAddInsured mainEdorAddInsured = new FCEdorAddInsured();
            mainEdorAddInsured.setGrpContNo(fcEdorAddInsured.getGrpContNo());
            mainEdorAddInsured.setBatch(fcEdorAddInsured.getBatch());
            mainEdorAddInsured.setStaffName(fcEdorAddInsured.getName());
            mainEdorAddInsured.setMainIdType(fcEdorAddInsured.getMainIdType());
            mainEdorAddInsured.setMainIdNo(fcEdorAddInsured.getMainIdNo());
            List<FCEdorAddInsured> fcEdorAddInsureds = fcEdorAddInsuredMapper.selectSubEdorInsured(mainEdorAddInsured);
            // 删除同批次连带家属信息
            if (fcEdorAddInsureds.size() > 1) {
                List<String> plusInsuredSNList = fcEdorAddInsureds.stream().map(FCEdorAddInsured::getPlusInsuredSN).collect(Collectors.toList());
                int i = fcEdorAddInsuredMapper.deleteEdorAddInsuredList(plusInsuredSNList);
                log.info(globalInput.getUserName() + "(" + globalInput.getUserNo() + ") 操作了保全增人的删除，成功删除了" + i + "条家属。");
            }
        }
        /**
         * 数据处理
         */
        int i = fcEdorAddInsuredMapper.deleteEdorAddInsured(plusInsuredSN);
        log.info(globalInput.getUserName() + "(" + globalInput.getUserNo() + ") 操作了保全增人的删除，成功删除了" + i + "条。");
    }

    public boolean checkIdNoIsExists(String idNo, String batch) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("idNo", idNo);
        map.put("batch", batch);
        int val = fcEdorAddInsuredMapper.checkIdNoIsExists(map);
        if (val > 0) {
            return false;
        }
        return true;
    }

    public String checkOneIsEmpTy(FCEdorAddInsured edorAddInsured) {
        if (StringUtil.isEmpty(edorAddInsured.getName())) {
            return "姓名不能为空,";
        } else {
            edorAddInsured.setName(edorAddInsured.getName().trim().replaceAll(" +", " "));
        }
        if (StringUtil.isEmpty(edorAddInsured.getNativeplace())) {
            return "国籍不能为空,";
        }
        if (StringUtil.isEmpty(edorAddInsured.getSex())) {
            return "性别不能为空,";
        }
        if (StringUtil.isEmpty(edorAddInsured.getBirthday())) {
            return "出生日期不能为空,";
        }
        if (StringUtil.isEmpty(edorAddInsured.getMobile())) {
            return "手机号不能为空,";
        }
        if (StringUtil.isEmpty(edorAddInsured.getIdType())) {
            return "证件类型不能为空,";
        }
        if (StringUtil.isEmpty(edorAddInsured.getIdNo())) {
            return "证件号不能为空,";
        }
        if (StringUtil.isEmpty(edorAddInsured.getJobType())) {
            return "职业类别不能为空,";
        }
        if (StringUtil.isEmpty(edorAddInsured.getJobCode())) {
            return "职业编码不能为空,";
        }
        if (StringUtil.isEmpty(edorAddInsured.getMedicareStatus())) {
            return "有无医保不能为空,";
        }
        if (StringUtil.isEmpty(edorAddInsured.getPlanCode())) {
            return "保障计划名称不能为空,";
        }
        if (StringUtil.isEmpty(edorAddInsured.getPlusEffectDate())) {
            return "增员生效日不能为空,";
        }
        if (StringUtil.isEmpty(edorAddInsured.getRelation())) {
            return "与主被保人关系不能为空,";
        } else if (!edorAddInsured.getRelation().equals(RelationMarkEnum.MYSELF.getCode())) {
            if (StringUtil.isEmpty(edorAddInsured.getStaffName())) {
                return "主被保险人姓名不能为空,";
            }
            if (StringUtil.isEmpty(edorAddInsured.getMainIdType())) {
                return "主被保险人证件类型不能为空,";
            }
            if (StringUtil.isEmpty(edorAddInsured.getMainIdNo())) {
                return "主被保险人证件号不能为空,";
            }
        } else if (edorAddInsured.getRelation().equals(RelationMarkEnum.MYSELF.getCode())) {
            if (!StringUtil.isEmpty(edorAddInsured.getStaffName()) || !StringUtil.isEmpty(edorAddInsured.getMainIdType()) || !StringUtil.isEmpty(edorAddInsured.getMainIdNo())) {
                return "主被保险人信息应为空,";
            }
        }
        return "";
    }

    public String checkField(String operation, Boolean isEflexGrpOrder, String grpOrderNo, FCEdorAddInsured edorAddInsured) {
        // 判断同一个批次号下，有没有相同的证件号
        if (operation.equals("add")) {
            if (!checkOneIdNoIsExists(edorAddInsured)) {
                // 存在相同的证件号
                return "当前保全增人申请批次存在相同的证件号!";
            }
            List<FCEdorAddInsured> fcEdorAddInsureds = fcEdorAddInsuredMapper.selectEdorAddInsured(edorAddInsured);
            if (fcEdorAddInsureds.size() > 0) {
                return "当前保单下用户已存在!";
            }
        } else {
            if (!checkOneIdNoIsExistsUpdate(edorAddInsured)) {
                // 存在相同的证件号
                return "当前保全增人申请批次存在相同的证件号!";
            }
            List<FCEdorAddInsured> fcEdorAddInsureds = fcEdorAddInsuredMapper.selectEdorAddInsuredForUpdate(edorAddInsured);
            if (fcEdorAddInsureds.size() > 0) {
                return "当前保单下用户已存在!";
            }
        }
        // 校验用户的唯一性
        if (isEflexGrpOrder) {
            FCOrderInsured fcOrderInsured = new FCOrderInsured();
            fcOrderInsured.setGrpOrderNo(grpOrderNo);
            fcOrderInsured.setIDNo(edorAddInsured.getIdNo());
            int insuredCount = fcOrderInsuredMapper.selectOrderInsured(fcOrderInsured);
            if (insuredCount > 0) {
                return "当前保单下用户已存在!";
            }
        }
        // 校验姓名
        String idType = edorAddInsured.getIdType();
        String name = edorAddInsured.getName().trim().replaceAll(" +", " ");
        String phone = edorAddInsured.getMobile();
        if (!StringUtil.isEmpty(idType) && !idType.equals("1")) {
            String s = CheckUtils.checkChineseName(name);
            if (!StringUtil.isEmpty(s)) {
                return s;
            }
        }
        String relation = edorAddInsured.getRelation();
        // 校验手机号格式是否正确
        if (!CheckUtils.checkMobilePhone(phone)) {
            return "手机号格式错误，请检查";
        } else {
            int checkint = fdUserMapper.checkByPhone(phone, edorAddInsured.getIdNo(), "1");
            int i = 0;
            if (relation.equals(RelationMarkEnum.MYSELF.getCode())) {
                switch (operation) {
                    case "add":
                        i = fcEdorAddInsuredMapper.selectByPhone(edorAddInsured);
                        break;
                    default:
                        i = fcEdorAddInsuredMapper.selectByPhoneUpdate(edorAddInsured);
                        break;
                }
            }
            if (checkint > 0 || i > 0) {
                return "手机号已注册,请更换!";
            }
        }
        // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确
        if (edorAddInsured.getIdType().equals("0")) {
            String checkOneIDCardResult = checkOneIDCard(edorAddInsured);
            if (StringUtils.isNotEmpty(checkOneIDCardResult)) {
                return checkOneIDCardResult;
            }
        }
        // 校验人员
        Map<String, String> map = new HashMap<>();
        if (relation.equals(RelationMarkEnum.MYSELF.getCode())) {
            map.put("sign", "1");
        } else {
            map.put("sign", "2");
            // 平台保单
            if (isEflexGrpOrder) {
                // 校验所属员工是否存在
                String mainName = edorAddInsured.getStaffName();
                String mainIdType = edorAddInsured.getMainIdType();
                String mainIdNo = edorAddInsured.getMainIdNo();
                // 查询所属员工是否在保单中存在
                FCOrderInsured fcOrderInsured = new FCOrderInsured();
                fcOrderInsured.setGrpOrderNo(grpOrderNo);
                fcOrderInsured.setName(mainName);
                fcOrderInsured.setIDType(mainIdType);
                fcOrderInsured.setIDNo(mainIdNo);
                int selectOrderInsuredCount = fcOrderInsuredMapper.selectMianOrderInsured(fcOrderInsured);
                // 查询所属员工是否在本批次中
                FCEdorAddInsured fcEdorAddInsured = new FCEdorAddInsured();
                fcEdorAddInsured.setGrpContNo(fcEdorAddInsured.getGrpContNo());
                fcEdorAddInsured.setName(mainName);
                fcEdorAddInsured.setIdType(mainIdType);
                fcEdorAddInsured.setIdNo(mainIdNo);
                fcEdorAddInsured.setSubsidiaryInsuredFlag("1");// 员工
                FCEdorAddInsured fcEdorAddInsured1 = fcEdorAddInsuredMapper.selectMainEdorInsured(fcEdorAddInsured);
                if (selectOrderInsuredCount == 0 && ObjectUtils.isEmpty(fcEdorAddInsured1)) {
                    return "该家属所属员工不存在！";
                }

            }
        }
        map.put("idType", edorAddInsured.getIdType());// 证件类型
        map.put("idNo", edorAddInsured.getIdNo());// 证件号
        map.put("birthDay", edorAddInsured.getBirthday());// 出生日期
        map.put("sex", edorAddInsured.getSex());// 性别
        map.put("nativeplace", edorAddInsured.getNativeplace());// 国籍
        map.put("idTypeEndDate", edorAddInsured.getIdTypeEndDate());// 证件有效期
        map.put("occupationCode", edorAddInsured.getJobCode());// 职业代码
        map.put("jobType", edorAddInsured.getJobType());// 福利生效日期
        String resultMsg = CheckUtils.checkSinglePeople(map);
        if (StringUtils.isNotBlank(resultMsg)) {
            return resultMsg;
        }
        return "";
    }

    public String checkOneIDCard(FCEdorAddInsured edorAddInsured) {
        DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            String idno = edorAddInsured.getIdNo();
            String sex = edorAddInsured.getSex();
            String birthday = edorAddInsured.getBirthday();
            if (!IDCardUtil.isIDCard(idno)) {
                return "身份证号格式错误,";
            }
            date = format1.parse(birthday);
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            String dateString = formatter.format(date);
            String idBir = idno.substring(6, 14);
            if (!dateString.equals(idBir)) {
                return "出生日期与身份证不符,";
            }
            if (!getGenderByIdCard(idno).equals("0".equals(sex) ? "男" : "女")) {
                return "性别与身份证不符,";
            }
        } catch (ParseException e) {
            return "数据异常,";
        }
        return "";
    }

    public boolean checkIdNoIsExists(String batch, List<HashMap<String, String>> listEdorAddInsured) {
        int val = fcEdorAddInsuredMapper.checkIdNoIsExists2(batch, listEdorAddInsured);
        if (val > 0)
            return false;
        return true;
    }

    public List<FCEdorAddInsured> checkOtherIsEsistsTemp(String batch, List<HashMap<String, String>> listPerIfo) {
        List<FCEdorAddInsured> list = fcEdorAddInsuredMapper.checkOtherIsEsists(batch, listPerIfo);
        return list;
    }

    public Map<String, List<String>> getOccupationTypeCode() {
        Map<String, List<String>> hashMap = new HashMap<>();
        List<Map<String, String>> list = fcPerInfoTempMapper.selectOccupationList("01");
        if (list != null && list.size() > 0) {
            for (Map map : list) {
                List<String> codeList = new ArrayList<String>();
                String codeKey = map.get("occupationType").toString().trim();
                List<Map<String, String>> codelist = fcPerInfoTempMapper.selectOccupationCode(codeKey);
                if (codelist != null && codelist.size() > 0) {
                    for (int i = 0; i < codelist.size(); i++) {
                        codeList.add(codelist.get(i).get("occupationCode").toString().trim());
                    }
                }
                hashMap.put(map.get("occupationType").toString().trim(), codeList);
            }
        }
        return hashMap;
    }

    public String checkOneCodeKey(FCEdorAddInsured edorAddInsured, List<String> idTypeList, List<String> occupationTypeList, List<String> occupationCodeList) {
        // 证件类型码值转换
        String idType = edorAddInsured.getIdType();
        String occupationType = edorAddInsured.getJobType();
        String occupationCode = edorAddInsured.getJobCode();
        if (!idTypeList.contains(idType)) {
            return "证件类型录入错误";
        }
        if (!occupationTypeList.contains(occupationType)) {
            return "职业类别录入错误";
        }
        if (!occupationCodeList.contains(occupationCode)) {
            return "职业代码录入错误";
        }
        return "";
    }

    public boolean checkOneIdNoIsExists(FCEdorAddInsured fcEdorAddInsured) {
        int val = fcEdorAddInsuredMapper.checkOneIdNoIsExists(fcEdorAddInsured);
        if (val > 0) {
            return false;
        }
        return true;
    }

    public boolean checkOneIdNoIsExistsUpdate(FCEdorAddInsured fcEdorAddInsured) {
        int val = fcEdorAddInsuredMapper.checkOneIdNoIsExistsUpdate(fcEdorAddInsured);
        if (val > 0) {
            return false;
        }
        return true;
    }

    public boolean checkOneOtherIsEsists(FCEdorAddInsured fcEdorAddInsured) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("idNo", fcEdorAddInsured.getIdNo());
        map.put("name", fcEdorAddInsured.getName());
        map.put("birthday", fcEdorAddInsured.getBirthday());
        map.put("sex", fcEdorAddInsured.getSex());
        map.put("idType", fcEdorAddInsured.getIdType());
        map.put("batch", fcEdorAddInsured.getBatch());
        int val = fcEdorAddInsuredMapper.checkOneOtherIsEsists(map);
        if (val > 0) {
            return false;
        }
        return true;
    }

    /**
     * 保全申请接口
     */
    @Transactional
    public String edorApply(EdorApplyReq edorApplyReq) {
        /**
         * 校验数据
         */
        if (StringUtil.isEmpty(edorApplyReq.getGrpContNo())) {
            throw new SystemException("团体保单号不能为空！");
        }
        if (StringUtil.isEmpty(edorApplyReq.getEdorType())) {
            throw new SystemException("保全类型不能为空！");
        }

        /**
         * 查询申请中的批次号
         */
        String batch = "";
        String grpContNo = edorApplyReq.getGrpContNo();
        FcEdorItem fcEdorItem = new FcEdorItem();
        fcEdorItem.setGrpContNo(grpContNo);
        fcEdorItem.setEdorState(EdorStateEnum.APPLYING.getCode());
        fcEdorItem.setEdorType(edorApplyReq.getEdorType());
        List<FcEdorItem> fcEdorItems = fcEdorItemMapper.selectEdorItemInfo(fcEdorItem);
        switch (fcEdorItems.size()) {
            case 0:
                batch = maxNoService.createMaxNo("addInsuredBatch", null, 20);
                break;
            case 1:
                batch = fcEdorItems.get(0).getEdorBatch();
                break;
            default:
                throw new SystemException("保单下保全申请项数据存在问题，存在多条申请中的保全项！");
        }

        // 定义返回参数
        EdorApplyResp edorApplyResp = new EdorApplyResp(batch);

        return JSONObject.toJSONString(ResponseResultUtil.success(edorApplyResp));
    }

    /**
     * 查询保全异步处理信息 状态00：未进行 01：处理中 02：处理完成，成功 03：处理完成，失败
     *
     * @param selectEdorApplyInfoReq
     * @return
     */
    public String selectEdorApplyInfo(SelectEdorApplyInfoReq selectEdorApplyInfoReq) {
        /**
         * 校验保全申请请求参数
         */
        if (StringUtil.isEmpty(selectEdorApplyInfoReq.getGrpContNo()) || StringUtil.isEmpty(selectEdorApplyInfoReq.getBatch())) {
            throw new SystemException("保单号、批次号不能为空！");
        }

        /**
         * 处理数据，如果有试算在途的保单进行判断，如果有在途的则等试算结果，然后直接跳转保全申请确认页面，否则进入保费试算之前的页面
         */
        SelectEdorApplyInfoResp selectEdorApplyInfoResp = new SelectEdorApplyInfoResp();
        selectEdorApplyInfoResp.setEdorApplyState("00");
        switch (EdorTypeEnum.getTypeByCode(selectEdorApplyInfoReq.getEdorType())) {
            case EDORNI:
                // 校验保单下是否存在未处理完成的保费试算或保全申请确认的操作
                FcAsyncInfo fcAsyncInfoParam = new FcAsyncInfo();
                fcAsyncInfoParam.setBusinessId(selectEdorApplyInfoReq.getBatch());
                FcAsyncInfo fcAsyncInfo = fcAsyncInfoMapper.selectAsyncInfo(fcAsyncInfoParam);
                if (!ObjectUtils.isEmpty(fcAsyncInfo)) {
                    DecimalFormat decimalFormat = new DecimalFormat("#.##");
                    selectEdorApplyInfoResp.setBusinessType(fcAsyncInfo.getBusinessType());
                    /**
                     * 处理中
                     */
                    if (fcAsyncInfo.getDealState().equals(StateEnum.VALID.getCode())) {
                        selectEdorApplyInfoResp.setEdorApplyState("01");
                        // 预估处理时长
                        Double dealMin = CommonUtil.div(Double.valueOf(fcAsyncInfo.getEstimateDealTime()), Double.valueOf(60000), 1);
                        // 预估处理完成时间
                        String estimateCompleteTime = fcAsyncInfo.getEstimateCompleteTime();
                        // 当前时间
                        String currentDateTime = DateTimeUtil.getCurrentDateTime();
                        // 预估处理完成时间与当前时间间隔时间
                        long distanceTimes = DateTimeUtil.getDistanceTimes(estimateCompleteTime, currentDateTime);
                        Double distanceMins = CommonUtil.div(Double.valueOf(distanceTimes), Double.valueOf(60000), 1);
                        if (!DateTimeUtil.checkDate(currentDateTime, estimateCompleteTime)) {
                            log.info("超过了预估处理时长！！！");
                            dealMin = distanceMins - dealMin;
                            // todo 调用核心查询进度
                            selectEdorApplyInfoResp.setEdorApplyMsg("当前保单下" + AsyncBusinessTypeEnum.getValueByCode(fcAsyncInfo.getBusinessType()) + "由于人数较多，正在快马加鞭处理，请稍后进行保全项相关操作，已超出预估处理时间" + decimalFormat.format(dealMin) + "分钟！");
                        } else {
                            dealMin = distanceMins;
                            selectEdorApplyInfoResp.setEdorApplyMsg("当前保单下" + AsyncBusinessTypeEnum.getValueByCode(fcAsyncInfo.getBusinessType()) + "由于人数较多，正在快马加鞭处理，请稍后进行保全项相关操作，预计处理时长" + decimalFormat.format(dealMin) + "分钟！");
                        }
                    } else {
                        /**
                         * 处理完成
                         */
                        // 增人试算，增人申请确认
                        if (fcAsyncInfo.getBusinessType().equals(AsyncBusinessTypeEnum.NITRIALIO.getCode()) || fcAsyncInfo.getBusinessType().equals(AsyncBusinessTypeEnum.NICONFIRMIO.getCode())) {
                            // 处理完成，异步处理无异常
                            if (fcAsyncInfo.getResultCode().equals(StateEnum.INVALID.getCode())) {
                                selectEdorApplyInfoResp.setEdorApplyState("02");
                                selectEdorApplyInfoResp.setEdorApplyMsg(fcAsyncInfo.getResultMessage());
                            }
                            // 处理完成，但是存在错误信息
                            if (fcAsyncInfo.getResultCode().equals(StateEnum.VALID.getCode())) {
                                selectEdorApplyInfoResp.setEdorApplyState("03");
                                selectEdorApplyInfoResp.setEdorApplyMsg(fcAsyncInfo.getResultMessage());
                            }
                        }
                    }
                }
                break;
            default:
                // 其他非保全增人逻辑，暂时不处理
                break;
        }

        return JSONObject.toJSONString(ResponseResultUtil.success(selectEdorApplyInfoResp));
    }


    /**
     * 保全增人申请确认接口
     *
     * @param edorAddInsuredConfirmReq
     * @param token
     * @return
     */
    @Transactional
    public String edorAddInsuredConfirmIO(EdorAddInsuredConfirmReq edorAddInsuredConfirmReq, String token) {

        /**
         * 定义返回报文
         */
        SelectEdorApplyInfoResp selectEdorApplyInfoResp = new SelectEdorApplyInfoResp();

        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);

        // 封装添加被保险人数据参数
        Map<String, Object> addInsuredMap = new HashMap<>();
        addInsuredMap.put("addBatch", edorAddInsuredConfirmReq.getBatch());
        addInsuredMap.put("grpNo", globalInput.getGrpNo());
        // 保全申请
        FcEdorItem fcEdorItem = fcEdorItemMapper.selectByPrimaryKey(edorAddInsuredConfirmReq.getBatch());
        if (StringUtils.isBlank(fcEdorItem.getEdorAppNo())) {
            throw new SystemException("保全受理号不能为空！");
        }

        /**
         * 获取人数异步阈值的配置
         */
        //保全申请确认的阈值
        Integer edorNIConfirmIOPeopleLimit = fdAsyncThresholdMapper.selectPeopleLimit(AsyncThresholdTypeEnum.EDORNICONFIRMIO.getCode());

        /**
         * 获取申请确认的人员信息
         */
        List<FCEdorAddInsured> fcEdorAddInsuredList = fcEdorAddInsuredMapper.getAddInsuredInfo(addInsuredMap);
        if (fcEdorAddInsuredList.size() == 0) {
            throw new SystemException("未查询到当前保全增人申请批次下的人员信息！");
        }
        if (fcEdorAddInsuredList.size() <= edorNIConfirmIOPeopleLimit) {
            /**
             * 实时处理保全申请确认
             */
            // 将文件上传到FTP服务器，而后处理申请确认调用核心保全申请确认接口
            Map<String, Object> map = new HashMap<>();
            map.put("grpContNo", edorAddInsuredConfirmReq.getGrpContNo());
            map.put("batch", edorAddInsuredConfirmReq.getBatch());
            map.put("docType", "0304");
            // 查询出服务器上保全文件上传信息列表
            List<FCEdoruploadfile> uploadfilelist = fcEdoruploadfileMapper.selectUploadfileByGrpContNo(map);
            // 查询ftp信息上传到SFTP
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
            for (FCEdoruploadfile fcEdoruploadfile : uploadfilelist) {
                // 封装上传实体类
                boolean flag = sFtp.uploadFile(fcEdoruploadfile.getFtpPath(), fcEdoruploadfile.getLocalPath());
                if (!flag) {
                    log.info("被保人告知书上传FTP服务器失败！");
                    throw new SystemException("被保人告知书上传失败!");
                }
            }
            Map<String, Object> mapNI = edorConfirmNI.addInsuredApply(edorAddInsuredConfirmReq.getGrpContNo(), addInsuredMap, fcEdorItem.getEdorAppNo(), uploadfilelist);
            Boolean addFlag = (boolean) mapNI.get("resultFlag");
            if (addFlag) {
                // 更新当前保全申请状态
                updateEdorState(token, edorAddInsuredConfirmReq.getBatch(), EdorStateEnum.APPLYDONE.getCode());
                selectEdorApplyInfoResp.setEdorApplyState("02");
                selectEdorApplyInfoResp.setEdorApplyMsg("成功！");
                log.info("增加被保险人申请接口: success！");
            } else {
                log.info("增加被保险人申请接口: 申请失败！");
                selectEdorApplyInfoResp.setEdorApplyState("03");
                selectEdorApplyInfoResp.setEdorApplyMsg(StringUtils.join(mapNI.get("errMsg"), ","));
            }
        } else {
            /**
             * 异步处理保全申请确认
             */
            asyncService.edorAddInsuredConfirmIOAsync(token, edorAddInsuredConfirmReq, addInsuredMap, fcEdorItem);
            selectEdorApplyInfoResp.setEdorApplyState("01");
            // 预估处理时长
            DecimalFormat decimalFormat = new DecimalFormat("#.##");
            Double dealMin = CommonUtil.div(Double.valueOf(fcEdorAddInsuredList.size() * 30 * 1000), Double.valueOf(60000), 1);
            selectEdorApplyInfoResp.setEdorApplyMsg("当前保单下" + AsyncBusinessTypeEnum.getValueByCode(AsyncBusinessTypeEnum.NITRIALIO.getCode()) + "由于人数较多，正在快马加鞭处理，请稍后进行保全项相关操作，预计处理时长为" + decimalFormat.format(dealMin) + "分钟！");
        }

        return JSONObject.toJSONString(ResponseResultUtil.success(selectEdorApplyInfoResp));
    }



    /**
     * 查询核心的保单信息
     *
     * @param fcGrpInfo
     * @param grpContNo
     * @return
     */
    private GrpContInfo selectGrpContInfo(FCGrpInfo fcGrpInfo, String grpContNo) {
        /**
         * 非平台的单子
         */
        GrpContQuery grpContQuery = new GrpContQuery();
        grpContQuery.setGrpIdType(fcGrpInfo.getGrpIdType());
        grpContQuery.setGrpIdTypeName(fcGrpInfo.getGrpIdTypeName());
        grpContQuery.setGrpIdNo(fcGrpInfo.getGrpIdNo());
        grpContQuery.setGrpContNo(grpContNo);
        grpContQuery.setPageNum("1");
        grpContQuery.setPageSize("10");
        XStream xStream = new XStream();
        xStream.alias("GrpContQuery", GrpContQuery.class);
        String reqXmlBody = xStream.toXML(grpContQuery);
        String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + "<RequestInfo>\n" + "\t<HEAD>\n" +
                /* 交易流水号 */
                "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                /* 接口交易类型 */
                "\t\t<TransType>BF0004</TransType>\n" +
                /* 交易日期 */
                "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                /* 交易时间 */
                "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" + "\t</HEAD>\n" + "\t<BODY>\n" + reqXmlBody + "\t</BODY>\n" + "</RequestInfo>";
        log.info("企业保单查询接口请求报文: {}", reqXml);
        long startStamp = System.currentTimeMillis();
        //将请求报文发送核心
        RemoteDelegate rd = RemoteDelegate.getInstance();
        Map<String, String> myPrpsMap = myProps.getServiceInfo();
        boolean success = rd.submitData(myPrpsMap.get("serviceUrl"), myPrpsMap.get("operation"), "", reqXml); //BenefitService
        long endStamp = System.currentTimeMillis();
        log.info("调用核心接口用时：" + (endStamp - startStamp));
        if (success) {
            //接收返回结果
            Map<String, Object> responseXml = rd.getResult();
            List<GrpContInfo> list = (List<GrpContInfo>) responseXml.get("GrpContList");
            if (list.size() != 1) {
                throw new BusinessException("核心保单查询接口有误！");
            } else {
                return list.get(0);
            }
        } else {
            throw new BusinessException("核心保单查询接口调用失败！");
        }
    }

}
