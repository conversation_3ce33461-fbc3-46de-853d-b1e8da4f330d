package com.sinosoft.eflex.service.edor;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FCEdorPlanInfoMapper;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.dao.FDCodeMapper;
import com.sinosoft.eflex.model.Body;
import com.sinosoft.eflex.model.FCEdorAddInsured;
import com.sinosoft.eflex.model.FDCode;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @ClassName: EdorNIBL
 * @Auther: hhw
 * @Date: 2019/3/4 10:04:16
 * @Description: 新增被保人试算类
 * @Version: 1.0 todo 该类迟早要被淘汰，2021.9.1
 */
@Service
public class EdorNIBL {

    // 工具类加载
    private static final Logger log = LoggerFactory.getLogger(EdorNIBL.class);

    @Autowired
    private MyProps myProps;

    @Autowired
    private FCEdorPlanInfoMapper fcEdorPlanInfoMapper;

    private static final String RESULTFLAG = "resultFlag";

    private static final String ERRMSG = "errMsg";

    @Autowired
    private FDCodeMapper fdCodeMapper;


    /**
     * 业务处理接口
     *
     * @param fcEdorAddInsuredList
     * @param batch
     * @param grpContNo
     * @return
     */
    public Map<String, Object> submitData(List<FCEdorAddInsured> fcEdorAddInsuredList, String batch, String grpContNo) {
        /**
         * 获取数据
         */
        Map<String, Object> resultMap = getInputData(fcEdorAddInsuredList, batch, grpContNo);
        if (!(boolean)resultMap.get(RESULTFLAG)) {
            return resultMap;
        }
        /**
         * 校验数据
         */
        resultMap = checkData();
        if (!(boolean)resultMap.get(RESULTFLAG)) {
            return resultMap;
        }
        /**
         * 处理数据
         */
        return dealData(fcEdorAddInsuredList, batch, grpContNo);
    }


    /**
     * 从输入数据中得到所有对象
     * @param fcEdorAddInsuredList
     * @param batch
     * @param grpContNo
     * @return
     */
    private Map<String, Object> getInputData(List<FCEdorAddInsured>  fcEdorAddInsuredList , String batch, String grpContNo){
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = true;
        List<String> errMsg = new ArrayList<String>();
        if(fcEdorAddInsuredList == null || fcEdorAddInsuredList.size() == 0 ){
            errMsg.add("请至少录入一条被保人信息！");
            resultFlag = false;
        }
        if(grpContNo == null || "".equals(grpContNo) ){
            errMsg.add("团体保单号不能为空！");
            resultFlag = false;
        }
        resultMap.put(RESULTFLAG, resultFlag);
        resultMap.put(ERRMSG, errMsg);
        return resultMap;
    }

    /**
     * 业务数据校验
     * @return
     */
    private Map<String, Object> checkData(){
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = true;
        List<String> errMsg = new ArrayList<String>();
        /****************************** 这里校验各个保全项目的必录项 start ******************************************************/
        log.info("被保人变更试算：开始校验数据的合法性 ............");
        // 被保人资料变更校验

        log.info("被保人变更试算：数据合法性校验通过..........");
        /**************************************** 这里校验各个保全项目的必录项 end   ******************************************************/
        resultMap.put(RESULTFLAG, resultFlag);
        resultMap.put(ERRMSG, errMsg);
        return resultMap;
    }


    private Map<String, Object> dealData(List<FCEdorAddInsured>  fcEdorAddInsuredList,String batch, String grpContNo){
        return addInsuredTrial(fcEdorAddInsuredList, batch, grpContNo);
    }

    /**
     * 新增被保险人试算
     * @param fcEdorAddInsuredList
     * @param batch
     * @param grpContNo
     * @return
     */
    public Map<String, Object> addInsuredTrial(List<FCEdorAddInsured>  fcEdorAddInsuredList, String batch, String grpContNo){
        // 定义返回结果
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = false;
        List<String> errMsg = new ArrayList<String>();
        RemoteDelegate rd = RemoteDelegate.getInstance();
        // 获取员福和核心对应的关系码值对应
        Map relationMap = fdCodeMapper.selectCodeAndCoreCode("relation").stream()
                .collect(Collectors.toMap(FDCode::getCodeKey, FDCode::getCoreCode));
        try{
            String insuredXml="";
            String esViewXml="";
            for(FCEdorAddInsured fcEdorAddInsured:fcEdorAddInsuredList) {
                Map<String,Object> planMap=new HashMap<>();
                planMap.put("planCode",fcEdorAddInsured.getPlanCode());
                planMap.put("batch",batch);
                String riskXml="";
                /*List<FCEdorPlanInfo> riskList=fcEdorPlanInfoMapper.selectRiskListByPlan(planMap);
                for(FCEdorPlanInfo risk : riskList){
                    planMap.put("riskCode", risk.getRiskCode());
                    List<FCEdorPlanInfo> dutyList = fcEdorPlanInfoMapper.selectRiskDutyListByPlanRisk(planMap);
                    riskXml+="\t\t\t\t<Risk>\n"+
                        "\t\t\t\t\t<RiskCode>"+risk.getRiskCode()+"</RiskCode>\n"+
                        "\t\t\t\t\t<MainRiskCode></MainRiskCode>\n"+
                        "\t\t\t\t\t<Amnt>"+risk.getAmnt()+"</Amnt>\n"+
                        "\t\t\t\t\t<Prem>"+risk.getPrem()+"</Prem>\n"+
                        "\t\t\t\t\t<Mult>1</Mult>\n"+
                        "\t\t\t\t\t<PayIntv>0</PayIntv>\n"+
                        "\t\t\t\t\t<PayYears>1</PayYears>\n"+
                        "\t\t\t\t\t<PayEndYearFlag>M</PayEndYearFlag>\n"+
                        "\t\t\t\t\t<PayEndYear>1</PayEndYear>\n"+
                        "\t\t\t\t\t<Years></Years>\n"+
                        "\t\t\t\t\t<InsuYearFlag>M</InsuYearFlag>\n"+
                        "\t\t\t\t\t<InsuYear>12</InsuYear>\n"+
                        "\t\t\t\t\t<CalRule>3</CalRule>\n"+
                        "\t\t\t\t\t<BonusGetMode>1</BonusGetMode>\n"+
                        "\t\t\t\t\t<FullBonusGetMode></FullBonusGetMode>\n"+
                        "\t\t\t\t\t<GetYearFlag></GetYearFlag>\n"+
                        "\t\t\t\t\t<GetYear></GetYear>\n"+
                        "\t\t\t\t\t<GetTerms></GetTerms>\n"+
                        "\t\t\t\t\t<GetIntv></GetIntv>\n"+
                        "\t\t\t\t\t<GetBankCode></GetBankCode>\n"+
                        "\t\t\t\t\t<GetBankAccNo></GetBankAccNo>\n"+
                        "\t\t\t\t\t<GetAccName></GetAccName>\n"+
                        "\t\t\t\t\t<CompensationRatio>"+(risk.getGetLimit()==null?"":risk.getGetLimit())+"</CompensationRatio>\n"+
                        //"\t\t\t\t\t\t<CompensationValue></CompensationValue>\n"+
                        "\t\t\t\t\t<StandbyFlag1></StandbyFlag1>\n"+
                        "\t\t\t\t\t<StandbyFlag2></StandbyFlag2>\n"+
                        "\t\t\t\t\t<StandbyFlag3></StandbyFlag3>\n";

                    String dutyXml = "\t\t\t\t\t<DutyList>\n";
                    for(FCEdorPlanInfo duty : dutyList){
                        dutyXml += "\t\t\t\t\t\t<Duty>\n"
                                +"\t\t\t\t\t\t<DutyCode>"+duty.getDutyCode()+"</DutyCode>\n"
                                +"\t\t\t\t\t\t<Amnt>"+duty.getAmnt()+"</Amnt>\n"
                                +"\t\t\t\t\t\t<Prem>"+duty.getPrem()+"</Prem>\n"
                                +"\t\t\t\t\t\t<GetLimit>"+(duty.getGetLimit()==null?"":duty.getGetLimit())+"</GetLimit>\n"
                                +"\t\t\t\t\t\t<GetLimitType>"+(duty.getGetLimitType()==null?"":duty.getGetLimitType())+"</GetLimitType>\n"
                                +"\t\t\t\t\t\t<GetRate>"+(duty.getGetRatio()==null?"":duty.getGetRatio())+"</GetRate>\n"
                                +"\t\t\t\t\t\t<CalRule>3</CalRule>\n"
                                +"\t\t\t\t\t\t</Duty>\n";
                    }
                    riskXml += dutyXml +"\t\t\t\t\t\t</DutyList>\n" + "\t\t\t\t\t</Risk>\n";
                    //"\t\t\t\t\t\t<PaySource></PaySource>\n"+
                }*/

                String bnfXml = "\t\t\t\t<BnfList>\n" +
                        /*"\t<Bnf>\n" +
                        "\t\t<Type></Type>\n" +
                        "\t\t<Grade></Grade>\n" +
                        "\t\t<Relation></Relation>\n" +
                        "\t\t<Name></Name>\n" +
                        "\t\t<Sex></Sex>\n" +
                        "\t\t<IDType></IDType>\n" +
                        "\t\t<IDNo></IDNo>\n" +
                        "\t\t<IdExpDate></IdExpDate>\n" +
                        "\t\t<Birthday></Birthday>\n" +
                        "\t\t<Rate></Rate>\n" +
                        "\t</Bnf>\n" +*/
                        "\t\t\t\t</BnfList>\n";
                
                //保全增人新增字段--SubsidiaryInsuredFlag字段的是否为
                //1-非附属被保人
                //2-附属被保人（未生成客户号，同批次添加）
                //3-附属被保人（已经生产客户号，非同批次添加）
                String insuredAddXml = 
                	    "\t\t\t\t<EmployeeName></EmployeeName>\n" +
                		"\t\t\t\t<EmployeeIdType></EmployeeIdType>\n" +
                		"\t\t\t\t<EmpolyeeIdNo></EmpolyeeIdNo>\n" +
                		"\t\t\t\t<EmployeeRelation></EmployeeRelation>\n"+
                        "\t\t\t\t<EmployeeNo></EmployeeNo>\n";


                if(fcEdorAddInsured.getSubsidiaryInsuredFlag().equals("2") || fcEdorAddInsured.getSubsidiaryInsuredFlag().equals("3")) {
                    insuredAddXml = "\t\t\t\t<EmployeeName>" + fcEdorAddInsured.getStaffName() + "</EmployeeName>\n" + "\t\t\t\t<EmployeeIdType>" + fcEdorAddInsured.getMainIdType() + "</EmployeeIdType>\n" + "\t\t\t\t<EmpolyeeIdNo>" + fcEdorAddInsured.getMainIdNo() + "</EmpolyeeIdNo>\n" + "\t\t\t\t<EmployeeRelation>" + fdCodeMapper.selectCoreCodeKey("Relation", fcEdorAddInsured.getRelation()) + "</EmployeeRelation>\n";
                    //todo 如果SubsidiaryInsuredFlag为3的话，则需要传输客户号，确认下客户号从哪里来。
                    if (fcEdorAddInsured.getSubsidiaryInsuredFlag().equals("3")) {
                        insuredAddXml += "\t\t\t\t<EmployeeNo>123</EmployeeNo>\n";
                    } else {
                        insuredAddXml += "\t\t\t\t<EmployeeNo></EmployeeNo>\n";
                    }
                }

                insuredXml+=
                    "\t\t\t<Insured>\n" +
                        "\t\t\t\t<Name>"+fcEdorAddInsured.getName()+"</Name>\n" +
                        "\t\t\t\t<Sex>"+fcEdorAddInsured.getSex()+"</Sex>\n" +
                        "\t\t\t\t<Birthday>"+fcEdorAddInsured.getBirthday()+"</Birthday>\n" +
                        "\t\t\t\t<IDType>"+fcEdorAddInsured.getIdType()+"</IDType>\n" +
                        "\t\t\t\t<IDNo>"+fcEdorAddInsured.getIdNo()+"</IDNo>\n" +
                        "\t\t\t\t<JobCode>"+fcEdorAddInsured.getJobCode()+"</JobCode>\n" +
                        "\t\t\t\t<JobLeve>"+fcEdorAddInsured.getJobType()+"</JobLeve>\n" +
                        "\t\t\t\t<JoinCompanyDate></JoinCompanyDate>\n" +
                        "\t\t\t\t<Mobile>"+(fcEdorAddInsured.getMobile()==null?"":fcEdorAddInsured.getMobile())+"</Mobile>\n" +
                        "\t\t\t\t<InsuredPeoples>"+ 1 +"</InsuredPeoples>\n" +
                        "\t\t\t\t<WorkNo></WorkNo>\n" + "\t\t\t\t<Email></Email>\n" + "\t\t\t\t<MedicareStatus>" + fcEdorAddInsured.getMedicareStatus() + "</MedicareStatus>\n" + "\t\t\t\t<ContPlanCode>" + (fcEdorAddInsured.getPlanCode() == null ? "" : fcEdorAddInsured.getPlanCode()) + "</ContPlanCode>\n" + "\t\t\t\t<GrpPayMode>" + fcEdorAddInsured.getPayMethod() + "</GrpPayMode>\n" + "\t\t\t\t<GrpBankCode>" + (fcEdorAddInsured.getDebitPayBank() == null ? "" : fcEdorAddInsured.getDebitPayBank()) + "</GrpBankCode>\n" + "\t\t\t\t<GrpBankAccName>" + (fcEdorAddInsured.getDebitPayName() == null ? "" : fcEdorAddInsured.getDebitPayName()) + "</GrpBankAccName>\n" + "\t\t\t\t<GrpBankAccNo>" + (fcEdorAddInsured.getDebitPayCode() == null ? "" : fcEdorAddInsured.getDebitPayCode()) + "</GrpBankAccNo>\n" + "\t\t\t\t<SelfPayMoney>" + (fcEdorAddInsured.getPerPayment() == null ? "" : fcEdorAddInsured.getPerPayment()) + "</SelfPayMoney>\n" + "\t\t\t\t<GrpPayMoney>" + (fcEdorAddInsured.getComPayment() == null ? "" : fcEdorAddInsured.getComPayment()) + "</GrpPayMoney>\n" + "\t\t\t\t<ContCValiDate>" + (fcEdorAddInsured.getPlusEffectDate() == null ? "" : fcEdorAddInsured.getPlusEffectDate()) + "</ContCValiDate>\n" +
                            //增加保险期间字段 add by 2022.3.11
                            "\t\t\t\t<InsuYear>" + (fcEdorAddInsured.getInsuYear() == null ? "" : fcEdorAddInsured.getInsuYear()) + "</InsuYear>\n" + "\t\t\t\t<InsuYearFlag>" + (fcEdorAddInsured.getInsuYearFlag() == null ? "" : fcEdorAddInsured.getInsuYearFlag()) + "</InsuYearFlag>\n" + "\t\t\t\t<GrpCompanFree></GrpCompanFree>\n" + "\t\t\t\t<AccountCode></AccountCode>\n" + "\t\t\t\t<JoinCompanyDate></JoinCompanyDate>\n" + "\t\t\t\t<Salary></Salary>\n" + "\t\t\t\t<ExecuteCom></ExecuteCom>\n" + "\t\t\t\t<CertifyCode></CertifyCode>\n" + "\t\t\t\t<StartCode></StartCode>\n" + "\t\t\t\t<MainInsuredName></MainInsuredName>\n" + "\t\t\t\t<MainInsuredNo></MainInsuredNo>\n" + "\t\t\t\t<MainRelation>" + relationMap.get(fcEdorAddInsured.getRelation())
                                + "</MainRelation>\n" +
                        //增人试算需求中新增字段---------start--------------
                        "\t\t\t\t<SubsidiaryInsuredFlag>"+ fcEdorAddInsured.getSubsidiaryInsuredFlag() +"</SubsidiaryInsuredFlag>\n" +
                            insuredAddXml +
                        //---------------------------end----------------
                        "\t\t\t\t<HealthFlag></HealthFlag>\n" +
                        "\t\t\t\t<RelationToAppnt>"+(fcEdorAddInsured.getRelationToAppnt()==null?"":fcEdorAddInsured.getRelationToAppnt())+"</RelationToAppnt>\n" +
                            "\t\t\t\t<IdExpDate>" + (StringUtils.isBlank(fcEdorAddInsured.getIdTypeEndDate()) ? ""
                            : fcEdorAddInsured.getIdTypeEndDate())
                            + "</IdExpDate>\n" +
                        //增人试算需求中字段要求必录
                        "\t\t\t\t<Nationality>"+ (fcEdorAddInsured.getNativeplace()==null?"":fcEdorAddInsured.getNativeplace()) +"</Nationality>\n"+
                        "\t\t\t\t<NativePlace>"+ (fcEdorAddInsured.getNativeplace()==null?"":fcEdorAddInsured.getNativeplace()) +"</NativePlace>\n"+
                            bnfXml +
                        "\t\t\t\t<RiskList>\n"+
                            riskXml +
                        "\t\t\t\t</RiskList>\n"+
                        "\t\t\t</Insured>\n";
            }

            esViewXml="\t\t<ESViewList>\n"+
            			//影像件循环节点
	                    "\t\t<ESView>\n" +
	                    "\t\t<SubType></SubType>\n" +
	                    "\t\t<PageNum></PageNum>\n" +
	                    //影像文件列表
	                    "\t\t<PageList>\n" +
		                    "\t\t<Page>\n" +
		                    "\t\t<ImageUrl></ImageUrl>\n" +
		                    "\t\t<ImageName></ImageName>\n" +
		                    "\t\t<PageCode></PageCode>\n" +
		                    "\t\t</Page>\n" +
	                    "\t\t</PageList>\n" +
	                    "\t\t</ESView>\n" +
                    "\t\t</ESViewList>\n";

            String requestXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
                    "\t\t<TransRefGUID>"+ UUID.randomUUID().toString().replaceAll("-", "") +"</TransRefGUID>\n" +
                    "\t\t<TransType>YUG001</TransType>\n" +
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<GrpContNo>"+grpContNo+"</GrpContNo>\n"+
                    "\t\t<EdorType>NI</EdorType>\n"+
                    "\t\t<EdorAppDate>"+ DateTimeUtil.getCurrentDate()+"</EdorAppDate>\n"+
                    "\t\t<InsuredList>\n"+
                        insuredXml+
                    "\t\t</InsuredList>\n"+
                        //影像件节点
                        esViewXml+
                    "\t</BODY>\n"+
                    "</RequestInfo>";

                    //log.info("调用接口请求报文：" + requestXml);
                    /*String requestXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "\t<Body>\n" +
                    "\t\t<GrpContNo>"+grpContNo+"</GrpContNo>\n"+
                    "\t\t<EdorType>NI</EdorType>\n"+
                    "\t\t<EdorAppDate>"+ DateTimeUtil.getCurrentDate()+"</EdorAppDate>\n"+
                    "\t\t<InsuredList>\n"+
                    insuredXml+
                    "\t\t</InsuredList>\n"+
                    "\t</BODY>\n";*/

            log.info("新增被保人试算接口请求报文：" + requestXml);
            long startStamp = System.currentTimeMillis();
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "", requestXml);
            long endStamp = System.currentTimeMillis();
            log.info("调用接口用时：" +  (endStamp - startStamp));
            if (success){
                Map<String, Object> responseXml=rd.getResult();
				Body body = (Body) responseXml.get("Body");
                log.info("新增被保人试算接口返回报文：" + JSON.toJSONString(responseXml));
                log.info("新增被保人试算接口返回报文 body: {}" ,JSON.toJSONString(body));
                if("0".equals(body.getEdorFlag())&&body.getInsuredList().size()>0){
                    resultFlag = true;
                    resultMap.put("NIInsuredList",body.getInsuredList());
                    resultMap.put("edorAppNo", body.getEdorAppNo());
                }else{
                    resultFlag = false;
                    errMsg.add(body.getEdorMark());
                }
            }else {
                resultFlag = false;
                errMsg.add("新增被保险人调用核心失败");
                log.info("新增被保险人试算：调用核心失败");
            }
        }catch (Exception e){
            resultFlag = false;
            log.info("错误异常"+e.getMessage());
            errMsg.add("新增被保险人试算: 失败");
        }finally {
            resultMap.put(RESULTFLAG, resultFlag);
            resultMap.put(ERRMSG, errMsg);
            return resultMap;
        }
    }

}
