package com.sinosoft.eflex.service.edor;/**
 * @DESCRIPTION
 * @create 2019-03-13 13:52:48
 **/

import com.google.common.base.Splitter;
import com.mysql.fabric.xmlrpc.base.Array;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.model.FCEdorAddInsured;
import com.sinosoft.eflex.model.FCEdorHomogeneousRiskInsured;
import com.sinosoft.eflex.model.FCEdorPlanInfo;
import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.FDRiskDutyInfo;
import com.sinosoft.eflex.model.FDRiskInfo;
import com.sinosoft.eflex.model.GlobalInput;
import com.sinosoft.eflex.service.*;
import com.sinosoft.eflex.util.CheckUtils;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.ExcelUtil;
import com.sinosoft.eflex.util.IDCardUtil;
import com.sinosoft.eflex.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class ImportHomogeneousRiskNI {

    /**
     * 日志打印工具
     */
    private static final Logger log = LoggerFactory.getLogger(EdorService.class);
    @Autowired
    private UserService userService;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
	private FCEdorHomogeneousRiskInsuredMapper fcEdorHomogeneousRiskInsuredMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FCPerInfoTempMapper fcPerInfoTempMapper;
    @Autowired
    private FcEdorItemMapper fcEdorItemMapper;
	@Autowired
	private AddressCheckService addressCheckService;
    
    //解析同质风险加减人清单excel
    @Transactional
    public Map<String,Object> dealHomogeneousRiskAddOrReducePeopleExcel(String token, Workbook wb, String batch, String grpContNo){
    	//公用变量
    	GlobalInput globalInput=userService.getSession(token);
        String grpNo=globalInput.getGrpNo();
        //返回结果
        Map<String,Object> resultMap=new HashMap<>();
        try {
        	//覆盖导入当前批次的同质风险加减人
        	Map<String,Object> insuerdNumMap1 =new HashMap<>();
        	insuerdNumMap1.put("grpNo",grpNo);
        	insuerdNumMap1.put("batch",batch);
        	int addInsuredNum1=fcEdorHomogeneousRiskInsuredMapper.selectInsuredCount(insuerdNumMap1);
        	if(addInsuredNum1 != 0) {
        		fcEdorHomogeneousRiskInsuredMapper.deleteHomogeneousRiskInsured(batch);
            }
            //删除之前批次申请信息
            fcEdorItemMapper.deleteByPrimaryKey(batch);
        	//解析同质风险加减人Excel
        	Sheet sheet = wb.getSheetAt(0);
            resultMap = dealHomogeneousRiskInsured(token,sheet,batch,grpContNo);
            return resultMap;
        }catch (Exception e){
            log.info("失败信息"+e.getMessage());
            resultMap.put("code","500");
            resultMap.put("message","导入失败");
            return resultMap;
        }
    }
   
    public Map<String,Object> dealHomogeneousRiskInsured(String token,Sheet sheet,String batch,String grpContNo){
    	//结果
    	Map<String,Object> resultMap=new HashMap<>();
    	//错误信息
    	String errorMsg = "";
    	try {
    		/**公共变量*/
    		GlobalInput globalInput = userService.getSession(token);
            String grpNo = globalInput.getGrpNo();
    		//原被保险人证件号集合
    		List<String> idNoList = new ArrayList<>();
    		idNoList.clear();
    		//新被保险人证件号
    		List<String> newidNoList = new ArrayList<>();
    		//序号
    		List<String> orderNoList = new ArrayList<>();
    		// 获取证件类型集合
            List<String> idTypeList = fcPerInfoTempMapper.getIdTypeList("01");
            // 获取职业类别集合
            List<String> occupationTypeList = fcPerInfoTempMapper.getOccupationTypeList("01");
            // 获取职业集合
            List<String> occupationCodeList = fcPerInfoTempMapper.getOccupationCodeList("01");
            // 获取职业类别与职业代码对应关系
            Map<String, List<String>> occupationTypeCodeMap = getOccupationTypeCode();
    		//同质风险加减人人员信息
    		List<FCEdorHomogeneousRiskInsured> insuredlist = new ArrayList<>(); 
    		insuredlist.clear();
            Row row = null;
            /**解析开始*/
            log.info("同质风险加减人Excel总行数："+sheet.getLastRowNum());
			for(int i=2;i<sheet.getLastRowNum();i++){
				row = sheet.getRow(i);
				if(ExcelUtil.isRowEmpty(row,8)){
					log.info("同质风险加减人清单第" + (i + 1) + "行是无效数据。");
				}else {
					//校验录入数据是否为空parm(行数，名称列，数据列)
					errorMsg=checkIsEmpty(i,sheet.getRow(1),row);
					if(!"".equals(errorMsg)){
						throw new RuntimeException();
					}
					//获取数据
					String oldName = ExcelUtil.getCellFormula(row.getCell(1));
					String oldIdType = ExcelUtil.getCellFormula(row.getCell(2));
					String oldIdNo = ExcelUtil.getCellFormula(row.getCell(3));
					String oldSex = ExcelUtil.getCellFormula(row.getCell(4));
					String oldBirthday = ExcelUtil.getCellFormula(row.getCell(5));
					String newName = ExcelUtil.getCellFormula(row.getCell(6));
					String newNativeplace = ExcelUtil.getCellFormula(row.getCell(7));
					String newIdType = ExcelUtil.getCellFormula(row.getCell(8));
					String newIdNo = ExcelUtil.getCellFormula(row.getCell(9));
					String newIdTypeEndDate = ExcelUtil.getCellFormula(row.getCell(10));
					String newSex = ExcelUtil.getCellFormula(row.getCell(11));
					String newBirthday = ExcelUtil.getCellFormula(row.getCell(12));
					String newMobilePhone = ExcelUtil.getCellFormula(row.getCell(13));
					String newJobType = ExcelUtil.getCellFormula(row.getCell(14));
					String newJobCode = ExcelUtil.getCellFormula(row.getCell(15));
					String newJoinMedProtect = ExcelUtil.getCellFormula(row.getCell(16));
					// add 同质风险加减人的生效日期 by wudezhong 2020.11.4
                    String nzValidate = ExcelUtil.getCellFormula(row.getCell(17));

					//校验姓名
					String changeNewName = "";
					String changeOldName = "";
					if (!StringUtil.isEmpty(newIdType) && newIdType.equals("外国公民护照")) {
						if (!StringUtil.isEmpty(newName)) {
							 changeNewName = CheckUtils.checkForeignName(newName);
						}
					} else if (!StringUtil.isEmpty(newIdType) && !newIdType.equals("外国公民护照")) {
						if (!StringUtil.isEmpty(newName)) {
							changeNewName = CheckUtils.checkChineseName(newName);
						}
					}
					if (!StringUtil.isEmpty(oldIdType) && oldIdType.equals("外国公民护照")) {
						if (!StringUtil.isEmpty(oldName)) {
							changeOldName = CheckUtils.checkForeignName(oldName);
						}
					} else if (!StringUtil.isEmpty(oldIdType) && !oldIdType.equals("外国公民护照")) {
						if (!StringUtil.isEmpty(oldName)) {
							changeOldName = CheckUtils.checkChineseName(oldName);
						}
					}
					if (!StringUtil.isEmpty(changeNewName)) {
						errorMsg = "清单第" + (i - 1) + "行，新被保险人姓名填写格式有误 :"+changeNewName;
						throw new RuntimeException();
					}
					if (!StringUtil.isEmpty(changeOldName)) {
						errorMsg = "清单第" + (i - 1) + "行，原被保险人姓名填写格式有误 :"+changeOldName;
						throw new RuntimeException();
					}
					//校验数据的合法性
					if(!checkScientifiNotation(oldIdNo)){
						errorMsg = "清单第" + (i - 1) + "行，原被保险人证件号码填写格式有误!";
						throw new RuntimeException();
					}
					if(!checkScientifiNotation(newIdNo)){
						errorMsg = "清单第" + (i - 1) + "行，新被保险人证件号码填写格式有误!";
						throw new RuntimeException();
					}
					if(!checkScientifiNotation(newMobilePhone) ||
					   !CheckUtils.checkMobilePhone(newMobilePhone)) {
						errorMsg = "清单第" + (i - 1) + "行，新被保险人手机号填写格式有误!";
						throw new RuntimeException();
					}
					/**校验规则*/
					//校验证件类型
					if (!idTypeList.contains(oldIdType)) {
						errorMsg += "清单第" + (i - 1) + "行原被保险人证件类型录入错误!";
			        }
					if (!idTypeList.contains(newIdType)) {
						errorMsg += "清单第" + (i - 1) + "行新被保险人证件类型录入错误!";
			        }
					//校验职业类别
                    if (!occupationTypeList.contains(newJobType)) {
                    	errorMsg += "清单第" + (i - 1) + "行新被保险人职业类别录入错误!";
                    }
                    //校验职业编码
                    if (!occupationCodeList.contains(newJobCode)) {
                    	errorMsg += "清单第" + (i - 1) + "行新被保险人职业代码录入错误；";
                    }
                    // 校验当前职业代码是否符合当前职业类别
                    if (!occupationTypeCodeMap.get(newJobType).contains(newJobCode)) {
                        errorMsg += "清单第" + (i - 1) + "行新被保险人职业类别不包含所录职业；";
                    }
                    //校验国籍
					if(StringUtils.isNotBlank(newNativeplace)) {
						newNativeplace = fdCodeMapper.selectKeyByCodeName("nativeplace",newNativeplace);
						if(StringUtils.isBlank(newNativeplace)) {
							errorMsg += "清单第" + (i - 1) + "行,新被保险人国籍填写有误；";
						}
						String checkNationality = addressCheckService.checkNationalityCode(newNativeplace);
						if (org.apache.commons.lang3.StringUtils.isNotEmpty(checkNationality)){
							errorMsg += "清单第" + (i - 1) + "行,新被保险人国籍填写有误；";
						}
					}
					//校验身份证
                    if (oldIdType.equals("身份证")) {
                        errorMsg += checkIDCard(i - 1,"原被保险人",oldIdNo,oldSex,oldBirthday);
                    }
                    if (newIdType.equals("身份证")) {
                        errorMsg += checkIDCard(i - 1,"新被保险人",newIdNo,newSex,newBirthday);
                    }
                    //判断序号是否为连续的整数。
//        			if((orderNoList.get(orderNoList.size()-1)+1).equals(orderNo) {
//        				errorMsg += "清单第" + (i - 1) + "行,新被保险人序号不是连续的整数；";
//        			}
                    //校验同质风险加减人保全生效日期应不超过往后的30天（只支持往后回溯） add by wudezhong 2020.11.4
                    if (StringUtils.isNotBlank(nzValidate)){
                        if(nzValidate.compareTo(DateTimeUtil.getCurrentDate()) > 0){
                            errorMsg += "清单第" + (i - 1) + "行，同质风险加减人生效日期不能超过当前日期！";
                        }else{
                            String traceabilityDate = DateTimeUtil.plusDay(-30,DateTimeUtil.getCurrentDate());
                            if (nzValidate.compareTo(traceabilityDate) < 0){
                                errorMsg += "清单第" + (i - 1) + "行，同质风险加减人生效日期应大于等于（当前日期-30天）天";
                            }
                        }
                    }
                    //else{
                    //    nzValidate = DateTimeUtil.plusDay(1,DateTimeUtil.getCurrentDate());
                    //}

                    
                    idNoList.add(oldIdNo);
                    newidNoList.add(newIdNo);
                    
                    /**码值转换*/
                    String oldIdTypeCode = fdCodeMapper.selectKeyByCodeName("IDType",oldIdType);
                    String newIdTypeCode = fdCodeMapper.selectKeyByCodeName("IDType",newIdType);
                    String oldSexCode = fdCodeMapper.selectKeyByCodeName("Sex",oldSex);
                    String newSexCode = fdCodeMapper.selectKeyByCodeName("Sex",newSex);
                    String newJoinMedProtectCode = fdCodeMapper.selectKeyByCodeName("isHas",newJoinMedProtect);
                    /**20需求的校验*/
					Map<String,String> map = new HashMap<>();
					map.put("sign","1");//1：员工 2：家属
					map.put("idType",newIdTypeCode);//证件类型
					map.put("idNo",newIdNo);//证件号
					map.put("birthDay", newBirthday);//出生日期
					map.put("sex",newSexCode);//性别
					map.put("nativeplace",newNativeplace);//国籍
					map.put("idTypeEndDate",newIdTypeEndDate);//证件有效期
					map.put("occupationCode",newJobCode);//职业代码
					String resultMsg = CheckUtils.checkSinglePeople(map);
					if(StringUtils.isNotBlank(resultMsg)) {
                        errorMsg += "清单第" + (i - 1) + "行"+resultMsg;
					}
					/**封装数据*/
					FCEdorHomogeneousRiskInsured fcEdorHomogeneousRiskInsured = new FCEdorHomogeneousRiskInsured();
					//同质被保险人流水号
					fcEdorHomogeneousRiskInsured.setHomogeneousRiskInsuredSN(maxNoService.createMaxNo("HomogeneousRiskInsuredSN", null, 20));
					//保单号
					fcEdorHomogeneousRiskInsured.setGrpContNo(grpContNo);
					//批次号
					fcEdorHomogeneousRiskInsured.setBatch(batch);
					//企业号
					fcEdorHomogeneousRiskInsured.setGrpNo(grpNo);
					//原被保险人姓名
					fcEdorHomogeneousRiskInsured.setOldName(oldName);
					//原被保险人证件类型
					fcEdorHomogeneousRiskInsured.setOldIdType(oldIdTypeCode);
					//原被保险人证件号
					fcEdorHomogeneousRiskInsured.setOldIdNo(oldIdNo);
					//原被保险人性别
					fcEdorHomogeneousRiskInsured.setOldSex(oldSexCode);
					//原被保险人出生日期
					fcEdorHomogeneousRiskInsured.setOldBirthday(oldBirthday);
					//新被保险人姓名
					fcEdorHomogeneousRiskInsured.setNewName(newName);
					//新被保险人国籍
					fcEdorHomogeneousRiskInsured.setNewNativeplace(newNativeplace);
					//新被保险人证件类型
					fcEdorHomogeneousRiskInsured.setNewIdType(newIdTypeCode);
					//新被保险人证件号
					fcEdorHomogeneousRiskInsured.setNewIdNo(newIdNo);
					//新被保险人证件有效期
					if (StringUtils.isNotBlank(newIdTypeEndDate)){
						fcEdorHomogeneousRiskInsured.setNewIdTypeEndDate(newIdTypeEndDate);
					}else {
						fcEdorHomogeneousRiskInsured.setNewIdTypeEndDate(null);
					}
					//新被保险人性别
					fcEdorHomogeneousRiskInsured.setNewSex(newSexCode);
					//新被保险人出生日期
					fcEdorHomogeneousRiskInsured.setNewBirthday(newBirthday);
					//新被保险人手机号
					fcEdorHomogeneousRiskInsured.setNewMobilePhone(newMobilePhone);
					//新被保险人职业类别
					fcEdorHomogeneousRiskInsured.setNewJobType(newJobType);
					//新被保险人职业代码
					fcEdorHomogeneousRiskInsured.setNewJobCode(newJobCode);
					//新被保险人医保标记1-是0-否
					fcEdorHomogeneousRiskInsured.setNewJoinMedProtect(newJoinMedProtectCode);
					//同质风险加减人保全生效日期
                    fcEdorHomogeneousRiskInsured.setNzValidate(nzValidate);
					fcEdorHomogeneousRiskInsured.setOperator(globalInput.getUserNo());
					fcEdorHomogeneousRiskInsured = CommonUtil.initObject(fcEdorHomogeneousRiskInsured, "INSERT");
					//存储数据
					insuredlist.add(fcEdorHomogeneousRiskInsured);
				}
			}
			
			//判断模板中是否存在相同的证件号的原被保险人
            HashSet<String> set = new HashSet<>(idNoList);
            if (idNoList.size() != set.size()) {
                // 获得list与set的差集
                Collection rs = CollectionUtils.disjunction(idNoList, set);
                // 将collection转换为list
                List<String> list1 = new ArrayList<>(rs);
                for (String str : list1) {
                	String sameindex = "";
                	for (int j = 0; j < idNoList.size(); j++) {
						if(idNoList.get(j).equals(str)) {
							if(StringUtils.isNotBlank(sameindex)) {
								sameindex += ","+(j+3);
							}else {
								sameindex += (j+3);
							}
						}
					}
                    errorMsg += "第" + sameindex + "行原被保险人证件号重复；";
                }
            }
			//判断模板中是否存在相同的证件号的新被保险人
            HashSet<String> newset = new HashSet<>(newidNoList);
            if (newidNoList.size() != newset.size()) {
                // 获得list与set的差集
                Collection rs1 = CollectionUtils.disjunction(newidNoList, newset);
                // 将collection转换为list
                List<String> newlist1 = new ArrayList<>(rs1);
                for (String str : newlist1) {
                	String sameindex = "";
                	for (int j = 0; j < idNoList.size(); j++) {
						if(newidNoList.get(j).equals(str)) {
							if(StringUtils.isNotBlank(sameindex)) {
								sameindex += ","+(j+3);
							}else {
								sameindex += (j+3);
							}
						}
					}
                    errorMsg += "第" + sameindex + "行新被保险人证件号重复；";
                }
            }
    		//原被保险人与新被保险人证件号重复
            Collection rs2 = CollectionUtils.intersection(idNoList,newidNoList);
            //将collection转换为list
            HashSet<String> newset2 = new HashSet<>(rs2);
            if(newset2.size() > 0) {
            	for (String str : newset2) {
            		String oldsameindex = "";
            		String newsameindex = "";
            		for (int j = 0; j < idNoList.size(); j++) {
            			if(idNoList.get(j).equals(str)) {
            				if(StringUtils.isNotBlank(oldsameindex)) {
            					oldsameindex += ","+(j+3);
            				}else {
            					oldsameindex += (j+3);
            				}
            			}
            		}
            		for (int j = 0; j < newidNoList.size(); j++) {
            			if(newidNoList.get(j).equals(str)) {
            				if(StringUtils.isNotBlank(newsameindex)) {
            					newsameindex += ","+(j+3);
            				}else {
            					newsameindex += (j+3);
            				}
            			}
            		}
            		errorMsg += "第" + oldsameindex + "行原被保险人证件号与第"+newsameindex+"行新被保险人证件号重复；";
            	}
            }
            //返回错误信息
            if (StringUtils.isNotBlank(errorMsg)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", errorMsg);
                return resultMap;
            }
            //存储到表
            if(insuredlist.size()<1) {
            	resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "导入的同质加减人不能为空！");
                return resultMap;
            }else {
            	fcEdorHomogeneousRiskInsuredMapper.insertNext(insuredlist);
            	Map<String,Object> insuerdNumMap=new HashMap<>();
            	insuerdNumMap.put("grpNo",grpNo);
            	insuerdNumMap.put("batch",batch);
            	int addInsuredNum=fcEdorHomogeneousRiskInsuredMapper.selectInsuredCount(insuerdNumMap);
            	resultMap.put("InsuredNum",addInsuredNum);
            	resultMap.put("code","200");
            	resultMap.put("message","导入成功");
            }
		} catch (Exception e) {
			resultMap.put("success", false);
			resultMap.put("code", "500");
			resultMap.put("message",StringUtils.isNotBlank(errorMsg)?errorMsg:e.getMessage());
		}
    	return resultMap;
    }
    
    
    //解析的规则----------------------------Start-----------------------------------------------
    //校验校验所录数据是否为空
    public String checkIsEmpty(int i,Row namerow,Row datarow){
    	String errMsg = "";
    	for (int j = 1; j <= (datarow.getLastCellNum()-1); j++) {
    		if(StringUtil.isEmpty(ExcelUtil.getCellValue(datarow.getCell(j))) && j != 10 && j != 17){
            	errMsg += "第"+(i+1)+"行"+ExcelUtil.getCellValue(namerow.getCell(j))+"不能为空；";
            } 
		}
        if(!errMsg.equals("")) {
        	errMsg = "同质风险加减人清单中："+errMsg;
        }
        return errMsg;
    }
    
    //科学记数法
    public boolean checkScientifiNotation(String str){
        if (str.contains("E") || str.contains(".")){
            return false;
        }
        return true;
    }
    
    // 校验性别及证件号公式
    public static String getGenderByIdCard(String idCard) {
        String sGender = "未知";
        String sCardNum = IDCardUtil.sex(idCard);
        if (Integer.parseInt(sCardNum) == 0) {
            sGender = "男";
        } else {
            sGender = "女";
        }
        return sGender;
    }

    //校验被保险人证件号
    public String checkIDCard(int i,String flagName,String idno,String sex,String birthday){
        DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            if (!IDCardUtil.isIDCard(idno)) {
                return "清单第" + i + "行"+flagName+"身份证号格式错误！";
            } else if (idno.length() != 18) {
                // 经由核心、运营确认，身份证号仅能为18位 add by wudezhong 2021.5.27
                return "清单第" + i + "行证件号码长度应为18位！";
            } else {
                date = format1.parse(birthday);
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String dateString = formatter.format(date);
                String idBir = IDCardUtil.dateOfBirth(idno);
                if (!dateString.equals(idBir)) {
                    return "清单第" + i + "行" + flagName + "出生日期与身份证不符！";
                }
                if (!getGenderByIdCard(idno).equals(sex)) {
                    return "清单第" + i + "行" + flagName + "性别与身份证不符！";
                }
            }
        } catch (ParseException e) {
            return "清单第" + i + "行数据异常！";
        }
        return "";
    }

    //获取职业类型和职业代码
    public Map<String, List<String>> getOccupationTypeCode() {
        Map<String, List<String>> occupationMap = new HashMap<>();
        List<Map<String, String>> list = fcPerInfoTempMapper.selectOccupationList("01");
        if(list!=null&&list.size()>0){
            for(Map<String,String> typeMap:list){
                List<String> codeList = new ArrayList<String>();
                String codeKey = typeMap.get("occupationType").toString().trim();
                List<Map<String, String>> codelist = fcPerInfoTempMapper
                        .selectOccupationCode(codeKey);
                if (codelist != null && codelist.size() > 0) {
                    for (int i = 0; i < codelist.size(); i++) {
                        codeList.add(codelist.get(i).get("occupationCode")
                                .toString().trim());
                    }
                }
                occupationMap.put(typeMap.get("occupationType").toString().trim(),
                        codeList);
            }
        }
        return occupationMap;
    }
}
