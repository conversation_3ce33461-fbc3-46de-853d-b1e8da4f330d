package com.sinosoft.eflex.service.edor;/**
 * @DESCRIPTION
 * @create 2019-03-18 10:14:38
 **/

import java.util.*;

import com.sinosoft.eflex.dao.FDCodeMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FCEdorACInfoMapper;
import com.sinosoft.eflex.dao.FCGrpInfoMapper;
import com.sinosoft.eflex.dao.FCGrpOrderMapper;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.edor.PolicyHolderChgTrialIO;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;

/**
 * @ClassName: EdorConfirmAC
 * @Auther: hhw
 * @Date: 2019/3/18 10:14:38
 * @Description: TODO
 * @Version: 1.0
 */
@Service
public class EdorConfirmAC {
    private static final Logger log = LoggerFactory.getLogger(EdorService.class);

    private static final String RESULTFLAG = "resultFlag";

    private static final String ERRMSG = "errMsg";

    private static final String EDORAPPNO = "edorAppNo";

    @Autowired
    private MyProps myProps;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FCEdorACInfoMapper fcEdorACInfoMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;

    //投保人资料变更保全申请确认接口
    public Map<String, Object> insurerInfoChgApplyIO(PolicyHolderChgTrialIO insuredInfoChgTrialIO,  String grpContNo, GlobalInput globalInput, List<FCEdoruploadfile> uploadfilelist){
        Map<String,Object> resultMap=new HashMap<>();
        boolean resultFlag = false;
        List<String> errMsg = new ArrayList<String>();
        try {
            String grpNo=globalInput.getGrpNo();
            //获取企业信息
            FCGrpInfo fcGrpInfo= fcGrpInfoMapper.selectByPrimaryKey(grpNo);
            // 投保单位性质  见码表1核心提供码表
            String grpNature1 = fdCodeMapper.selectOtherSign("GrpNature", fcGrpInfo.getGrpType());
            //判断法人是否存在
            insuredInfoChgTrialIO.getPolicyHolderInfo().setBusinessType(fcGrpInfo.getTrade());
            insuredInfoChgTrialIO.getPolicyHolderInfo().setGrpNature(fcGrpInfo.getGrpType());
            //投保人资料变更影像件
            String esViewXml="";
            String fileXml = "";
            for (int i = 0; i < uploadfilelist.size(); i++) {
                //影像文件列表
                String ftpPath = uploadfilelist.get(i).getFtpPath().substring(0,uploadfilelist.get(i).getFtpPath().lastIndexOf("/")+1);
                fileXml +=
                        "\t\t<Page>\n" +
                                "\t\t<ImageUrl>"+ftpPath+"</ImageUrl>\n" +
                                "\t\t<ImageName>"+ uploadfilelist.get(i).getFileName() +"</ImageName>\n" +
                                "\t\t<PageCode>"+ i +"</PageCode>\n" +
                                "\t\t</Page>\n";

            }
            if (uploadfilelist.size()>0){
                esViewXml="\t\t<ESViewList>\n"+
                        //影像件循环节点
                        "\t\t<ESView>\n" +
                        "\t\t<SubType>312011</SubType>\n" +
                        "\t\t<PageNum>1</PageNum>\n" +
                        "\t\t<PageList>\n" +
                        fileXml +
                        "\t\t</PageList>\n"+
                        "\t\t</ESView>\n" +
                        "\t\t</ESViewList>\n";
            }else{
                esViewXml="\t\t<ESViewList>\n"+
                        "\t\t</ESViewList>\n";
            }
            String requestXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
				/* 交易流水号 */
                    "\t\t<TransRefGUID>"+ UUID.randomUUID().toString().replaceAll("-", "")+"</TransRefGUID>\n" +
				/* 接口交易类型 */
                    "\t\t<TransType>YUG004</TransType>\n" +
				/* 交易日期 */
                    "\t\t<TransExeDate>"+ DateTimeUtil.getCurrentDate()+"</TransExeDate>\n" +
				/* 交易时间 */
                    "\t\t<TransExeTime>"+DateTimeUtil.getCurrentTime()+"</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<GrpContNo>"+grpContNo+"</GrpContNo>\n" +
                    "\t\t<EdorType>AC</EdorType>\n" +
                    "\t\t<EdorAppNo>"+insuredInfoChgTrialIO.getEdorAppNo()+"</EdorAppNo>\n" +
                    "\t\t<EdorAppDate>"+DateTimeUtil.getCurrentDate()+"</EdorAppDate>\n" +
                    "\t\t<Appnt>\n"+
                    "\t\t\t<GrpName>"+insuredInfoChgTrialIO.getPolicyHolderInfo().getGrpName()+"</GrpName>\n" +
                    "\t\t\t<Sex></Sex>\n" +
                    "\t\t\t<BirthDate></BirthDate>\n" +
                    "\t\t\t<IdType></IdType>\n" +
                    "\t\t\t<IdNo></IdNo>\n" +
                    "\t\t\t<OccupationCode></OccupationCode>\n" +
                    "\t\t\t<BirthDate></BirthDate>\n" +
                    "\t\t\t<Email>"+insuredInfoChgTrialIO.getPolicyHolderInfo().getEmail()+"</Email>\n" +
                    "\t\t\t<AppntNativePlace></AppntNativePlace>\n" +
                    "\t\t\t<GrpNature1>"+grpNature1+"</GrpNature1>\n" +
                    "\t\t\t<GrpNature>"+insuredInfoChgTrialIO.getPolicyHolderInfo().getGrpNature()+"</GrpNature>\n" +
                    "\t\t\t<BusinessType>"+insuredInfoChgTrialIO.getPolicyHolderInfo().getBusinessType()+"</BusinessType>\n" +
                    "\t\t\t<Phone></Phone>\n" +
                    "\t\t\t<Fax></Fax>\n" +
                    "\t\t\t<FoundDate></FoundDate>\n" +
                    "\t\t\t<GetFlag>"+insuredInfoChgTrialIO.getPolicyHolderInfo().getGetFlag()+"</GetFlag>\n" +
                    "\t\t\t<BankCode>"+insuredInfoChgTrialIO.getPolicyHolderInfo().getBankCode()+"</BankCode>\n" +
                    "\t\t\t<Peoples></Peoples>\n" +
                    "\t\t\t<BankAccNo>"+insuredInfoChgTrialIO.getPolicyHolderInfo().getBankAccNo()+"</BankAccNo>\n" +
                    "\t\t\t<AppIDPeriodOfValidityType></AppIDPeriodOfValidityType>\n" +
                    "\t\t\t<AppIDPeriodOfValidity></AppIDPeriodOfValidity>\n" +
                    "\t\t\t<HodingPeoples></HodingPeoples>\n" +
                    "\t\t\t<Corporation>"+insuredInfoChgTrialIO.getPolicyHolderInfo().getCorporation()+"</Corporation>\n" +
                    "\t\t\t<GrpAddress>"+insuredInfoChgTrialIO.getPolicyHolderInfo().getGrpAddress()+"</GrpAddress>\n" +
                    "\t\t\t<GrpZipCode>"+insuredInfoChgTrialIO.getPolicyHolderInfo().getGrpZipCode()+"</GrpZipCode>\n" +
                    "\t\t\t<LinkMan1>"+insuredInfoChgTrialIO.getPolicyHolderInfo().getLinkMan1()+"</LinkMan1>\n" +
                    "\t\t\t<InsContIDType1></InsContIDType1>\n" +
                    "\t\t\t<InsContIDNo1></InsContIDNo1>\n" +
                    "\t\t\t<InsContIDPeriodOfValidityType1></InsContIDPeriodOfValidityType1>\n" +
                    "\t\t\t<InsContIDPeriodOfValidity1></InsContIDPeriodOfValidity1>\n" +
                    "\t\t\t<InterFaceType1>"+(StringUtils.isBlank(insuredInfoChgTrialIO.getPolicyHolderInfo().getMobilePhone1()) ? "": "2" )+"</InterFaceType1>\n" +
                    "\t\t\t<MobilePhone1>"+insuredInfoChgTrialIO.getPolicyHolderInfo().getMobilePhone1()+"</MobilePhone1>\n" +
                    "\t\t\t<Phone1></Phone1>\n" +
                    "\t\t\t<NZProportion>"+(insuredInfoChgTrialIO.getPolicyHolderInfo().getNzProportion()==null ? "":insuredInfoChgTrialIO.getPolicyHolderInfo().getNzProportion())+"</NZProportion>\n" +
                    "\t\t</Appnt>\n" +
                    //影像件信息
                    esViewXml+
                    "\t</BODY>\n" +
                    "</RequestInfo>";
            log.info("请求报文："+requestXml);

            long a = System.currentTimeMillis();
            // 调用核心接口
            RemoteDelegate remoteDelegate = RemoteDelegate.getInstance();
            boolean success=remoteDelegate.submitData(myProps.getServiceInfo().get("serviceUrl"),
                    myProps.getServiceInfo().get("operation"), "", requestXml);
            long b = System.currentTimeMillis();
            log.info("连接核心接口所用时间"+ ((a - b) / 1000.0)+"秒");
            if (success){
                Map<String,Object> responseXml = remoteDelegate.getResult();
                Body body = (Body) responseXml.get("Body");
                if ("0".equals(body.getEdorFlag())){
                    Map<String ,Object> paremateMap = new HashMap<>();
                    // 非平台保单同样也需要记录保全变更信息，修改企业
                    // paremateMap.put("grpContNo",grpContNo);
                    paremateMap.put("grpNo",fcGrpInfo.getGrpNo());
                    FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectByGrpContNo(paremateMap);
                    if (fcGrpOrder != null) {
                        FCEdorACInfo fcEdorACInfo = new FCEdorACInfo();
                        fcEdorACInfo.setGrpNo(fcGrpInfo.getGrpNo());
                        fcEdorACInfo.setGrpContNo(grpContNo);
                        fcEdorACInfo.setEdorAppNo(insuredInfoChgTrialIO.getEdorAppNo());
                        fcEdorACInfo.setGrpName(insuredInfoChgTrialIO.getPolicyHolderInfo().getGrpName());
                        fcEdorACInfo.setCorporation(insuredInfoChgTrialIO.getPolicyHolderInfo().getCorporation());
                        fcEdorACInfo.setGetFlag(insuredInfoChgTrialIO.getPolicyHolderInfo().getGetFlag());
                        fcEdorACInfo.setBankCode(insuredInfoChgTrialIO.getPolicyHolderInfo().getBankCode());
                        fcEdorACInfo.setBankAccNo(insuredInfoChgTrialIO.getPolicyHolderInfo().getBankAccNo());
                        fcEdorACInfo.setGrpAddress(insuredInfoChgTrialIO.getPolicyHolderInfo().getGrpAddress());
                        fcEdorACInfo.setZipCode(insuredInfoChgTrialIO.getPolicyHolderInfo().getGrpZipCode());
                        fcEdorACInfo.setEmail(insuredInfoChgTrialIO.getPolicyHolderInfo().getEmail());
                        fcEdorACInfo = CommonUtil.initObject(fcEdorACInfo,"INSERT");
                        int i = fcEdorACInfoMapper.insertSelective(fcEdorACInfo);
                        if (i>0){
                            log.info("企业变更数据已存储至平台。。。");
                        }
                    }
                    resultFlag = true;
                    resultMap.put("ACInsuredBody",body);
                }else{
                    resultFlag = false;
                    errMsg.add(body.getEdorMark());
                }
            }else {
                resultFlag = false;
                errMsg.add("投保人资料变更调用核心失败");
                log.info("投保人资料变更申请：调用核心失败");
            }
        }catch (Exception e){
            resultFlag = false;
            log.info("错误异常", e);
            errMsg.add("投保人资料变更申请失败");
        }finally {
            resultMap.put(RESULTFLAG, resultFlag);
            resultMap.put(ERRMSG, errMsg);
            return resultMap;
        }
    }
}
