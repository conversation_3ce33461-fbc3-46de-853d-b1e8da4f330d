package com.sinosoft.eflex.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.CoreIdType;
import com.sinosoft.eflex.enums.GenderType;
import com.sinosoft.eflex.enums.IDTypeEnum;
import com.sinosoft.eflex.enums.NationalityEnums;
import com.sinosoft.eflex.model.AddressEntity.EvaluationCustomer;
import com.sinosoft.eflex.model.AddressEntity.IdCardVerifyRequest;
import com.sinosoft.eflex.model.AddressEntity.convert.CheckSameCustomerConvert;
import com.sinosoft.eflex.model.BatchInsureInterface.Head;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.checkPerson.CheckPersonRequest;
import com.sinosoft.eflex.rpc.model.CoreCustomerInfoResDTO;
import com.sinosoft.eflex.rpc.service.CoreCustomerService;
import com.sinosoft.eflex.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-08-20 15:33
 */
@Service
@Slf4j
public class FamilyQueryService {

    @Autowired
    private EmpAndFamilyMapper empAndFamilyMapper;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private UserService userService;
    @Autowired
    private FPInsurePlanMapper fpInsurePlanMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private FCOrderMapper fcOrderMapper;
    @Autowired
    private FCOrderItemMapper fcOrderItemMapper;
    @Autowired
    private FCOrderBnfRelaMapper fcOrderBnfRelaMapper;
    @Autowired
    private FCOrderBnfMapper fcOrderBnfMapper;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private MyProps myProps;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private FCPerRegistDayMapper fcPerRegistDayMapper;
    @Autowired
    private FCOrderItemDetailMapper fcOrderItemDetailMapper;
    @Autowired
    private AddressCheckService addressCheckService;
    @Autowired
    private CoreCustomerService coreCustomerService;

    public String selectPersonalInfoById(String token, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String perNo = globalInput.getCustomNo();
            if (personId == null || "".equals(personId)) {
                resultMap.put("message", "个人编号不能为空");
                resultMap.put("code", "500");
            } else {
                List<FCPerson> familyList = empAndFamilyMapper.selectFamily(perNo, personId);
                if (familyList != null && familyList.size() > 0) {
                    FCPerson fcPerson = familyList.get(0);
                    Map<String, Object> map = new HashMap<>();
                    map.put("personId", fcPerson.getPersonID());
                    map.put("name", fcPerson.getName());
                    map.put("relation", fcPerson.getRelation());
                    map.put("relationName", fcPerson.getRelationName());
                    map.put("birthDate", fcPerson.getBirthDate());
                    map.put("sex", fcPerson.getSex());
                    map.put("iDNo", fcPerson.getIDNo());
                    map.put("iDType", fcPerson.getIDType());
                    map.put("mobilePhone", fcPerson.getMobilePhone());
                    map.put("occupationCode", fcPerson.getOccupationCode());
                    map.put("occupationName", fcPerson.getOccupationName());
                    map.put("relationProve", fcPerson.getRelationProve());
                    map.put("occupationType", fcPerson.getOccupationType());
                    map.put("occupationTypeName", fcPerson.getOccupationTypeName());
                    map.put("openBank", fcPerson.getOpenBank());
                    map.put("openAccount", fcPerson.getOpenAccount());
                    resultMap.put("data", fcPerson);
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", "查询信息成功");
                } else {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "该成员不存在。");
                }
            }
        } catch (Exception e) {
            log.info("查询家庭成员信息失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询员工信息及家属信息
     *
     * @param token
     * @param pageSource
     * @return
     */
    public Map<String, Object> selectFamilyInfo_welfareMake(String token, String pageSource) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String perNo = globalInput.getCustomNo();
            String ensureCode = globalInput.getEnsureCode();
            //获取福利类型
            String ensureType = "0";
            FCEnsure fcensure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            FCPerInfo fcperinfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
            if (StringUtils.isNotBlank(ensureCode) && fcensure == null) {
                resultMap.put("message", "请先选择福利！");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                return resultMap;
            }
            if (fcensure != null) {
                ensureType = fcensure.getEnsureType();
            }
            String planType = fcensure.getPlanType();
            if (perNo == null || "".equals(perNo)) {
                resultMap.put("message", "个人编号不能为空");
                resultMap.put("code", "500");
            } else if (StringUtils.isBlank(ensureType)) {
                resultMap.put("message", "福利类型不能为空");
                resultMap.put("code", "500");
            } else {
                //ensureType:0-企事业单位投保、1-学生投保、2-场地险投保
                //获取家属数据
                List<FCPerson> familyList = new ArrayList<>();
                List<Map<String, Object>> list = new LinkedList<>();
                //学生投保只查询当前福利下的家属信息
                if ("1".equals(ensureType) && "2".equals(pageSource)) {
                    // 获取学生的 personid
                    Map<String, String> map = new HashMap<>();
                    map.put("perNo", perNo);
                    map.put("ensureCode", ensureCode);
                    familyList = empAndFamilyMapper.selectFamilyByIsManual(map);
                } else {
                    // 查询员工下所有家属的证件号
                    List<String> idnolist = empAndFamilyMapper.selectFamilyIdNo(perNo);
                    // 查询家属证件号的对应的最新的家属personid
                    List<String> perosnidlist = new ArrayList<>();
                    for (String idno : idnolist) {
                        Map<String, String> map1 = new HashMap<>();
                        map1.put("IdNo", idno);
                        map1.put("perNo", perNo);
                        String personid = empAndFamilyMapper.selectNewFamilyPersonid(map1);
                        if (StringUtils.isNotBlank(personid)) {
                            perosnidlist.add(personid);
                        }
                    }
                    //根据perno获取员工personid
                    List<String> staffPersonIdList = fcPersonMapper.getEmployPersonId(perNo);
                    perosnidlist.add(staffPersonIdList.get(0));
                    Map<String, Object> paraMap = new HashMap<>();
                    paraMap.put("perosnidlist", perosnidlist);
                    paraMap.put("idNo", fcperinfo.getIDNo());
                    // 获取人员信息
                    familyList = empAndFamilyMapper.selectAllFamilyInfo(paraMap);
                }

                for (FCPerson fcPerson : familyList) {
                    if ("0".equals(fcPerson.getRelation()) && !fcPerson.getIDNo().equals(fcperinfo.getIDNo())) {
                        continue;
                    }
                    if (!"0".equals(fcPerson.getRelation()) && fcPerson.getIDNo().equals(fcperinfo.getIDNo())) {
                        continue;
                    }
                    if (!fcperinfo.getIDNo().equals(fcPerson.getPerIDNo())) {
                        continue;
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("personId", fcPerson.getPersonID());
                    map.put("name", fcPerson.getName());
                    map.put("relation", fcPerson.getRelation());
                    map.put("relationProve", fcPerson.getRelationProve());
                    map.put("relationName", fcPerson.getRelationName());
                    map.put("birthDate", fcPerson.getBirthDate());
                    map.put("sex", fcPerson.getSex());
                    map.put("iDNo", fcPerson.getIDNo());
                    map.put("iDType", fcPerson.getIDType());
                    map.put("nativeplace", fcPerson.getNativeplace());
                    map.put("nativeplaceName", fcPerson.getNativeplaceName());
                    map.put("idTypeEndDate", fcPerson.getIdTypeEndDate());
                    map.put("email", fcPerson.getEMail());
                    map.put("mobilePhone", fcPerson.getMobilePhone());
                    map.put("occupationCode", fcPerson.getOccupationCode());
                    map.put("occupationName", fcPerson.getOccupationName());
                    map.put("occupationType", fcPerson.getOccupationType());
                    map.put("occupationTypeName", fcPerson.getOccupationTypeName());
                    map.put("openBank", fcPerson.getOpenBank());
                    map.put("openAccount", fcPerson.getOpenAccount());
                    map.put("JoinMedProtect", fcPerson.getJoinMedProtect());

                    // 国籍补充
                    if (StringUtils.isEmpty(fcPerson.getNativeplace())) {
                        String nativePlace = NationalityCheckUtils.defaultNationality(fcPerson.getIDType(), fcPerson.getIDNo());
                        if (StringUtils.isNotEmpty(nativePlace)) {
                            String nativePlaceName = NationalityEnums.getByCode(nativePlace).getValue();
                            fcPersonMapper.updateByPersonId(new FCPerson(fcPerson.getPersonID(), nativePlace, nativePlaceName));
                            map.put("nativeplace", nativePlace);
                            map.put("nativeplaceName", nativePlaceName);
                        }
                    }
                    //查询家属在开放期内是否存有学生的身份
                    Integer count = fcEnsureMapper.selectFamTempStudentCount(fcPerson.getPersonID());
                    if (count > 0) {
                        map.put("isHasStuRule", "Y");
                    } else {
                        map.put("isHasStuRule", "N");
                    }
                    //查询家属是否已投保，查询相同证件号的家属的是否投过保，因为不能确认personid
                    if (!"0".equals(fcPerson.getRelation())) {
                        List<Map<String, Object>> isplanList = new ArrayList<>();
                        if ("0".equals(ensureType) || "2".equals(ensureType)) {
                            //查询同一个员工下相同证件号的家属的所有计划
                            Map<String, String> map1 = new HashMap<>();
                            map1.put("personid", fcPerson.getPersonID());
                            map1.put("perno", perNo);
                            List<String> personIdlist = empAndFamilyMapper.selectSameStaffPersonid(map1);
                            //固定计划
                            if (planType == null || "".equals(planType) || planType.equals("0")) {
                                for (String personid : personIdlist) {
                                    List<Map<String, Object>> planList = empAndFamilyMapper.selectIsPlan(ensureCode, personid);
                                    if (planList.size() > 0) {
                                        if (pageSource.equals("2")) {//福利定制时查询有计划的personid
                                            map.put("personId", personid);
                                        }
                                        isplanList = planList;
                                    }
                                }
                            } else {
                                //弹性计划
                                for (String personid : personIdlist) {
                                    List<Map<String, Object>> planList = empAndFamilyMapper.selectIsEflexPlan(ensureCode, perNo, personid);
                                    if (planList.size() > 0) {
                                        if ("2".equals(pageSource)) {//福利定制时查询有计划的personid
                                            map.put("personId", personid);
                                        }
                                        isplanList = planList;
                                    }
                                }
                            }
                        } else {
                            //学生直接查询投保计划表有没有数据 即可判断是否投过保
                            isplanList = empAndFamilyMapper.selectIsPlan(ensureCode, fcPerson.getPersonID());
                        }
                        if (isplanList != null && isplanList.size() > 0) {
                            map.put("isPlan", "1");
                        } else {
                            map.put("isPlan", "0");
                        }
                    }
                    if ("0".equals(fcPerson.getRelation())) {
                        Map<String, Object> params = new HashMap<>();
                        params.put("ensureCode", ensureCode);
                        params.put("perNo", perNo);
                        FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(params);
                        Double staffGrpPrem = fcPerRegistDay.getStaffGrpPrem();
                        Double familyGrpPrem = fcPerRegistDay.getFamilyGrpPrem();
                        map.put("staffGrpPrem", staffGrpPrem == null ? 0.0 : staffGrpPrem);
                        map.put("familyGrpPrem", familyGrpPrem == null ? 0.0 : familyGrpPrem);
                    }
                    if (!"0".equals(fcPerson.getRelation())) {
                        list.add(map);
                    } else {
                        list.add(0, map);
                    }
                }
                List<Map<String, Object>> isPlan = list.stream().filter(x -> x.get("isPlan") != null && "1".equals(x.get("isPlan"))).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(isPlan)) {
                    for (Map<String, Object> stringObjectMap : list) {
                        stringObjectMap.put("isPlan", "1");
                    }
                }
                resultMap.put("data", list);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "查询信息成功");
                log.info("查询信息成功!");
            }
        } catch (Exception e) {
            log.info("查询家庭成员信息失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return resultMap;
    }

    public Map<String, Object> selectFamilyInfo_familyQuery(String token, String pageSource) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String perNo = globalInput.getCustomNo();
            FCPerInfo fcperinfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
            if (perNo == null || "".equals(perNo)) {
                resultMap.put("message", "个人编号不能为空");
                resultMap.put("code", "500");
            } else {
                //获取家属数据
                List<FCPerson> familyList = new ArrayList<>();
                List<Map<String, Object>> list = new LinkedList<>();
                //查询是否存在相同证件号的员工最新的员工信息
                FCPerInfo fcPerInfo = fcPerInfoMapper.selectNewFcperinfo(perNo);
                Integer count = empAndFamilyMapper.checkPerType(fcPerInfo.getPerNo());
                List<String> personidlist = new ArrayList<>();
                if (count == 0 && pageSource.equals("1")) { //是监护人
                    //查询证件号相同的员工所对应的Perosnd
                    personidlist = empAndFamilyMapper.selectStaffPersonId(perNo);
                    //家庭信息维护页面需展示监护人信息
                    FCPerInfo fcperInfo = empAndFamilyMapper.selectGuardianInfo(perNo);
                    Map<String, Object> map = new HashMap<>();
                    map.put("name", fcperInfo.getName());
                    map.put("relation", "0");
                    map.put("relationProve", "");
                    map.put("relationName", "监护人");
                    map.put("birthDate", fcperInfo.getBirthDay());
                    map.put("sex", fcperInfo.getSex());
                    map.put("iDNo", fcperInfo.getIDNo());
                    map.put("iDType", fcperInfo.getIDType());
                    map.put("nativeplace", fcperInfo.getNativeplace());
                    map.put("nativeplaceName", fcperInfo.getNativeplaceName());
                    map.put("idTypeEndDate", fcperInfo.getIdTypeEndDate());
                    map.put("email", fcperInfo.getEmail());
                    map.put("mobilePhone", fcperInfo.getMobilePhone());
                    map.put("occupationCode", fcperInfo.getOccupationCode());
                    map.put("occupationName", fcperInfo.getOccupationName());
                    map.put("occupationType", fcperInfo.getOccupationType());
                    map.put("occupationTypeName", fcperInfo.getOccupationTypeName());
                    map.put("openBank", fcperInfo.getOpenBank());
                    map.put("openAccount", fcperInfo.getOpenAccount());
                    map.put("isPlan", "0");
                    map.put("JoinMedProtect", fcperInfo.getJoinMedProtect());
                    list.add(0, map);
                }
                List<String> idnolist = empAndFamilyMapper.selectFamilyIDNO(perNo);
                List<String> perosnidlist = new ArrayList<>();
                for (String idno : idnolist) {
                    Map<String, String> map1 = new HashMap<>();
                    map1.put("IdNo", idno);
                    map1.put("perNo", perNo);
                    String personid = empAndFamilyMapper.selectNewFamilyPersonid(map1);
                    if (StringUtils.isNotBlank(personid)) {
                        perosnidlist.add(personid);
                    }
                }
                Map<String, Object> paraMap = new HashMap<String, Object>();
                paraMap.put("perosnidlist", perosnidlist);
                paraMap.put("idNo", fcperinfo.getIDNo());
                familyList = empAndFamilyMapper.selectAllFamilyInfo(paraMap);
                //删除信息
                if (count == 0 && pageSource.equals("1")) {
                    for (FCPerson fcPerson : familyList) {
                        if (personidlist.size() > 1 && personidlist.contains(fcPerson.getPersonID())) {
                            familyList.remove(fcPerson);
                        }
                    }
                }
                //查询所有的福利
                List<FCEnsureGrpInfo> ensureInfolist = fcEnsureMapper.selectAllEnsure(perNo);
                for (FCPerson fcPerson : familyList) {
                    if (fcPerson.getRelation().equals("0") && !fcPerson.getIDNo().equals(fcperinfo.getIDNo())) continue;
                    if (!fcPerson.getRelation().equals("0") && fcPerson.getIDNo().equals(fcperinfo.getIDNo())) continue;
                    if (!fcperinfo.getIDNo().equals(fcPerson.getPerIDNo())) continue;
                    Map<String, Object> map = new HashMap<>();
                    map.put("personId", fcPerson.getPersonID());
                    map.put("name", fcPerson.getName());
                    map.put("relation", fcPerson.getRelation());
                    map.put("relationProve", fcPerson.getRelationProve());
                    map.put("relationName", fcPerson.getRelationName());
                    map.put("birthDate", fcPerson.getBirthDate());
                    map.put("sex", fcPerson.getSex());
                    map.put("iDNo", fcPerson.getIDNo());
                    map.put("iDType", fcPerson.getIDType());
                    map.put("nativeplace", fcPerson.getNativeplace());
                    map.put("nativeplaceName", fcPerson.getNativeplaceName());
                    map.put("idTypeEndDate", fcPerson.getIdTypeEndDate());
                    map.put("email", fcPerson.getEMail());
                    map.put("mobilePhone", fcPerson.getMobilePhone());
                    map.put("occupationCode", fcPerson.getOccupationCode());
                    map.put("occupationName", fcPerson.getOccupationName());
                    map.put("occupationType", fcPerson.getOccupationType());
                    map.put("occupationTypeName", fcPerson.getOccupationTypeName());
                    map.put("openBank", fcPerson.getOpenBank());
                    map.put("openAccount", fcPerson.getOpenAccount());
                    map.put("JoinMedProtect", fcPerson.getJoinMedProtect());
                    // 国籍补充
                    if (StringUtils.isEmpty(fcPerson.getNativeplace())) {
                        String nativePlace = NationalityCheckUtils.defaultNationality(fcPerson.getIDType(), fcPerson.getIDNo());
                        if (StringUtils.isNotEmpty(nativePlace)) {
                            String nativePlaceName = NationalityEnums.getByCode(nativePlace).getValue();
                            fcPersonMapper.updateByPersonId(new FCPerson(fcPerson.getPersonID(), nativePlace, nativePlaceName));
                            map.put("nativeplace", nativePlace);
                            map.put("nativeplaceName", nativePlaceName);
                        }
                    }
                    //查询家属是否已投保
                    if (!"0".equals(fcPerson.getRelation()) && ensureInfolist != null && ensureInfolist.size() > 0) {
                        List<Map<String, Object>> isplanList = new ArrayList<>();
                        for (FCEnsureGrpInfo fCEnsureGrpInfo : ensureInfolist) {
                            String ensureType = fCEnsureGrpInfo.getEnsureType();
                            String planType = fCEnsureGrpInfo.getPlanType();
                            String ensureCode = fCEnsureGrpInfo.getEnsureCode();
                            if (ensureType.equals("0") || ensureType.equals("2")) {
                                //查询同一个员工下相同证件号的家属的所有计划
                                Map<String, String> map1 = new HashMap<>();
                                map1.put("personid", fcPerson.getPersonID());
                                map1.put("perno", perNo);
                                List<String> personIdlist = empAndFamilyMapper.selectSameStaffPersonid(map1);
                                if (planType == null || "".equals(planType) || planType.equals("0")) {//固定计划
                                    for (String personid : personIdlist) {
                                        List<Map<String, Object>> planList = empAndFamilyMapper.selectIsPlan(ensureCode, personid);
                                        if (planList.size() > 0) {
                                            if (pageSource.equals("2")) {//福利定制时查询有计划的personid
                                                map.put("personId", personid);
                                            }
                                            isplanList = planList;
                                        }
                                    }
                                } else {//弹性计划
                                    for (String personid : personIdlist) {
                                        List<Map<String, Object>> planList = empAndFamilyMapper.selectIsEflexPlan(ensureCode, perNo, personid);
                                        if (planList.size() > 0) {
                                            if (pageSource.equals("2")) {//福利定制时查询有计划的personid
                                                map.put("personId", personid);
                                            }
                                            isplanList = planList;
                                        }
                                    }
                                }
                            } else {
                                isplanList = empAndFamilyMapper.selectIsPlan(ensureCode, fcPerson.getPersonID());
                            }
                        }
                        if (isplanList != null && isplanList.size() > 0) {
                            map.put("isPlan", "1");
                        } else {
                            map.put("isPlan", "0");
                        }
                    }
                    //查询家属在开放期内是否存有学生的身份
                    count = fcEnsureMapper.selectFamTempStudentCount(fcPerson.getPersonID());
                    if (count > 0) {
                        map.put("isHasStuRule", "Y");
                    } else {
                        map.put("isHasStuRule", "N");
                    }
                    if (!"0".equals(fcPerson.getRelation())) {
                        list.add(map);
                    } else {
                        list.add(0, map);
                    }
                }
                resultMap.put("data", list);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "查询信息成功");
                log.info("查询信息成功!");
            }
        } catch (Exception e) {
            log.info("查询家庭成员信息失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return resultMap;
    }

    public Map<String, Object> selectFamilyInfo_welfareQuery(String token, String pageSource) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String perNo = globalInput.getCustomNo();
            FCPerInfo fcperinfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
            if (perNo == null || "".equals(perNo)) {
                resultMap.put("message", "个人编号不能为空");
                resultMap.put("code", "500");
            } else {
                //获取家属数据
                List<FCPerson> familyList = new ArrayList<>();
                List<Map<String, Object>> list = new LinkedList<>();
                //查询是否存在相同证件号的员工最新的员工信息
                FCPerInfo fcPerInfo = fcPerInfoMapper.selectNewFcperinfo(perNo);
                Integer count = empAndFamilyMapper.checkPerType(fcPerInfo.getPerNo());
                List<String> idnolist = empAndFamilyMapper.selectFamilyIDNO(perNo);
                List<String> perosnidlist = new ArrayList<>();
                for (String idno : idnolist) {
                    Map<String, String> map1 = new HashMap<>();
                    map1.put("IdNo", idno);
                    map1.put("perNo", perNo);
                    String personid = empAndFamilyMapper.selectNewFamilyPersonid(map1);
                    if (StringUtils.isNotBlank(personid)) {
                        perosnidlist.add(personid);
                    }
                }
                Map<String, Object> paraMap = new HashMap<String, Object>();
                paraMap.put("perosnidlist", perosnidlist);
                paraMap.put("idNo", fcperinfo.getIDNo());
                familyList = empAndFamilyMapper.selectAllFamilyInfo(paraMap);
                for (FCPerson fcPerson : familyList) {
                    if ("0".equals(fcPerson.getRelation()) && !fcPerson.getIDNo().equals(fcperinfo.getIDNo())) {
                        continue;
                    }
                    if (!"0".equals(fcPerson.getRelation()) && fcPerson.getIDNo().equals(fcperinfo.getIDNo())) {
                        continue;
                    }
                    if (!fcperinfo.getIDNo().equals(fcPerson.getPerIDNo())) {
                        continue;
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("personId", fcPerson.getPersonID());
                    map.put("name", fcPerson.getName());
                    map.put("relation", fcPerson.getRelation());
                    map.put("relationProve", fcPerson.getRelationProve());
                    map.put("relationName", fcPerson.getRelationName());
                    map.put("birthDate", fcPerson.getBirthDate());
                    map.put("sex", fcPerson.getSex());
                    map.put("iDNo", fcPerson.getIDNo());
                    map.put("iDType", fcPerson.getIDType());
                    map.put("nativeplace", fcPerson.getNativeplace());
                    map.put("nativeplaceName", fcPerson.getNativeplaceName());
                    map.put("idTypeEndDate", fcPerson.getIdTypeEndDate());
                    map.put("email", fcPerson.getEMail());
                    map.put("mobilePhone", fcPerson.getMobilePhone());
                    map.put("occupationCode", fcPerson.getOccupationCode());
                    map.put("occupationName", fcPerson.getOccupationName());
                    map.put("occupationType", fcPerson.getOccupationType());
                    map.put("occupationTypeName", fcPerson.getOccupationTypeName());
                    map.put("openBank", fcPerson.getOpenBank());
                    map.put("openAccount", fcPerson.getOpenAccount());
                    map.put("JoinMedProtect", fcPerson.getJoinMedProtect());
                    // 国籍补充
                    if (StringUtils.isEmpty(fcPerson.getNativeplace())) {
                        String nativePlace = NationalityCheckUtils.defaultNationality(fcPerson.getIDType(), fcPerson.getIDNo());
                        if (StringUtils.isNotEmpty(nativePlace)) {
                            String nativePlaceName = NationalityEnums.getByCode(nativePlace).getValue();
                            fcPersonMapper.updateByPersonId(new FCPerson(fcPerson.getPersonID(), nativePlace, nativePlaceName));
                            map.put("nativeplace", nativePlace);
                            map.put("nativeplaceName", nativePlaceName);
                        }
                    }
                    //查询家属在开放期内是否存有学生的身份
                    count = fcEnsureMapper.selectFamTempStudentCount(fcPerson.getPersonID());
                    if (count > 0) {
                        map.put("isHasStuRule", "Y");
                    } else {
                        map.put("isHasStuRule", "N");
                    }
                    if (!"0".equals(fcPerson.getRelation())) {
                        list.add(map);
                    } else {
                        list.add(0, map);
                    }
                }
                resultMap.put("data", list);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "查询信息成功");
                log.info("查询信息成功!");
            }

        } catch (Exception e) {
            log.info("查询家庭成员信息失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return resultMap;
    }

    /**
     * <AUTHOR>
     * @description 查询员工信息及家属信息
     * @date 15:58 15:58
     * @modified
     */
    public String selectFamilyInfo(String token, String pageSource) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            switch (pageSource) {
                case "0":
                    //0--福利查询页面
                    resultMap = selectFamilyInfo_welfareQuery(token, pageSource);
                    break;//0--福利查询页面
                case "1":
                    //1--家庭信息维护页面
                    resultMap = selectFamilyInfo_familyQuery(token, pageSource);
                    break;
                case "2":
                    //2--福利定制页面
                    resultMap = selectFamilyInfo_welfareMake(token, pageSource);
                    break;
            }
        } catch (Exception e) {
            log.info("查询家庭成员信息失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    @Transactional
    public String selectFamliyIdentity(String token, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            //判断前台传的数据是否为空
            if (personId == null || "".equals(personId)) {
                resultMap.put("message", "传递的个人编号为空");
                return JSON.toJSONString(resultMap);
            }
            FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
            fcStaffFamilyRela.setPerNo(globalInput.getCustomNo());
            fcStaffFamilyRela.setPersonID(personId);
            int count = fcStaffFamilyRelaMapper.selectStaffIdentityCount(fcStaffFamilyRela);
            resultMap.put("success", false);
            resultMap.put("code", "200");
            resultMap.put("data", count);
            resultMap.put("message", "查询家庭成员成功!");
        } catch (Exception e) {
            log.info("查询家庭成员身份失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询家庭成员身份失败!");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * <AUTHOR>
     * @description 信息删除
     * @date 10:13 10:13
     * @modified
     */
    @Transactional
    public String delectFamliyInfo(String token, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            //判断前台传的数据是否为空
            if (personId == null || "".equals(personId)) {
                resultMap.put("message", "传递的个人编号为空");
                return JSON.toJSONString(resultMap);
            }
            //查询同一员工的所有的Personid
            Map<String, String> map1 = new HashMap<>();
            map1.put("personid", personId);
            map1.put("perno", globalInput.customNo);
            List<String> personIdlist = empAndFamilyMapper.selectSameStaffPersonid(map1);
            for (String perosnid : personIdlist) {
                //判断该用户是否存在订单
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("personId", perosnid);
                List<FPInsurePlan> orderExist = fpInsurePlanMapper.selectFPInsurePlansByState(params);
                Integer integer = fcOrderInsuredMapper.selectStaffFamilyInsured(personId);
                if (orderExist.size() > 0 || integer > 0) {
                    resultMap.put("message", "该用户已存在订单,不能删除");
                    return JSON.toJSONString(resultMap);
                }
            }
            //查询家属在开放期内是否存有学生的身份
            Integer count = fcEnsureMapper.selectFamTempStudentCount(personId);
            if (count > 0) {
                resultMap.put("message", "该用户拥有学生身份,信息不能删除");
                return JSON.toJSONString(resultMap);
            }
            try {
                for (String personid : personIdlist) {
                    //删除拒保的订单信息
                    Map<String, String> map = fcOrderInsuredMapper.selectStaffFamilyInsuredOrderInfo(personId);
                    if (map != null) {
                        String orderStatus = map.get("orderStatus");
                        String orderitemNo = map.get("orderitemNo");
                        String orderItemDetailNo = map.get("orderItemDetailNo");
                        String orderNo = map.get("orderNo");
                        if (StringUtils.isNotBlank(orderStatus) && orderStatus.equals("011")) {
                            //3. 删除订单表
                            fcOrderItemDetailMapper.deleteByKey(orderItemDetailNo);
                            fcOrderItemMapper.deleteByPrimaryKey(orderitemNo);
                            fcOrderMapper.deleteByPrimaryKey(orderNo);
                            //4.删除受益人表
                            List<FCOrderBnfRela> fcOrderBnfRelas = fcOrderBnfRelaMapper.selectlistByOrderItemNo(orderitemNo);
                            for (FCOrderBnfRela fcOrderBnfRela : fcOrderBnfRelas) {
                                String bnfNo = fcOrderBnfRela.getBnfNo();
                                fcOrderBnfMapper.deleteByPrimaryKey(bnfNo);
                            }
                            fcOrderBnfRelaMapper.deleteByPrimaryKey(orderitemNo);
                            //5. 删除被保人表
                            fcOrderInsuredMapper.deleteByPrimaryKey(orderitemNo);
                        }
                    }
                    //删除个人及家属信息表中数据
                    fcPersonMapper.deleteByPrimaryKey(personid);
                    //删除员工家属信息关联表中数据
                    fcStaffFamilyRelaMapper.deletePersonId(personid);
                }
            } catch (Exception e) {
                log.info("家庭成员删除失败：", e);
                throw new RuntimeException();
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "删除成功");
        } catch (Exception e) {
            log.info("家庭成员删除失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "删除失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description 修改家属信息和个人信息
     * @date 15:13 15:13
     * @modified
     */
    @Transactional
    public String updateFamilyInfo(String personId, FCEmpAndFamilyInfo fcEmpAndFamilyInfo, String token) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "200");
        resultMap.put("message", "修改家属信息成功");
        try {
            String s = "";
            String name = fcEmpAndFamilyInfo.getName();
            fcEmpAndFamilyInfo.setName(name.trim().replaceAll(" +", " "));
            String idType = fcEmpAndFamilyInfo.getIDType();
            if ("8".equals(idType)) {
                return JSON.toJSONString(ResultUtil.error("证件类型不能为其他，请录入正确的证件类型"));
            }
            if (!StringUtil.isEmpty(idType) && idType.equals("1")) {
                s = CheckUtils.checkForeignName(name);
            } else if (!StringUtil.isEmpty(idType) && !idType.equals("1")) {
                s = CheckUtils.checkChineseName(name);
            }
            if (!StringUtil.isEmpty(s)) {
                return JSON.toJSONString(ResultUtil.error(s));
            }
            GlobalInput globalInput = userService.getSession(token);
            log.info("globalInput Json:{}", JsonUtil.toJSON(globalInput));
            String perId = globalInput.getCustomNo();
            if (StringUtils.isNotEmpty(fcEmpAndFamilyInfo.getBirthDay()) && DateTimeUtil.getAgeInt(fcEmpAndFamilyInfo.getBirthDay()) < 18 ){
                //未成年人手机号置空
                fcEmpAndFamilyInfo.setMobilePhone("");
            }

            // 手机不为空正常处理
            if (StringUtils.isNotEmpty(fcEmpAndFamilyInfo.getMobilePhone())) {
                // 查别的员工家属数据是否一致
                if ("0".equals(fcEmpAndFamilyInfo.getRelation())) {
                    int checkint = fdUserMapper.checkByPhone(fcEmpAndFamilyInfo.getMobilePhone(), fcEmpAndFamilyInfo.getIDNo(), "1");
                    if (checkint > 0) {
                        return JSON.toJSONString(ResultUtil.error("该手机号已注册！"));
                    }
                }

                if (!CheckUtils.checkMobilePhone(fcEmpAndFamilyInfo.getMobilePhone())) {
                    resultMap.put("code", "500");
                    resultMap.put("message", "手机号格式错误，请检查");
                    return JSON.toJSONString(resultMap);
                }
                FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perId);
                log.info("fcPerInfo Json:{}", JsonUtil.toJSON(fcPerInfo));
                if (null != fcPerInfo && !"0".equals(fcEmpAndFamilyInfo.getRelation())) {
                    if (fcEmpAndFamilyInfo.getMobilePhone().equals(fcPerInfo.getMobilePhone())) {
                        resultMap.put("code", "500");
                        resultMap.put("message", fcEmpAndFamilyInfo.getMobilePhone() + "手机号和" + fcPerInfo.getName() + "重复，请确保手机号唯一，无法提供时可忽略家属号码录入！");
                        return JSON.toJSONString(resultMap);
                    }
                }

                List<FCStaffFamilyRela> familyRelaList = fcStaffFamilyRelaMapper.getPersonByInfo(fcPerInfo.getPerNo());
                List<String> personIds = familyRelaList.stream().map(FCStaffFamilyRela::getPersonID).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(personIds)) {
                    List<FCPerson> fcPeople = fcPersonMapper.selectByPersonIds(personIds);
                    List<String> names = fcPeople.stream().filter(p -> p.getMobilePhone().equals(fcEmpAndFamilyInfo.getMobilePhone()) && !p.getPersonID().equals(personId)).map(FCPerson::getName).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(names)) {
                        resultMap.put("code", "500");
                        resultMap.put("message", fcEmpAndFamilyInfo.getMobilePhone() + "手机号和" + names + "重复，请确保手机号唯一，无法提供时可忽略家属号码录入！");
                        return JSON.toJSONString(resultMap);
                    }

                    List<String> idNoList = fcPeople.stream().map(FCPerson::getIDNo).collect(Collectors.toList());
                    FCPerson checkFcPerson = fcPersonMapper.checkPhoneFcPerson(idNoList,fcEmpAndFamilyInfo.getMobilePhone());
                    if (checkFcPerson != null &&  fcEmpAndFamilyInfo.getMobilePhone().equals(checkFcPerson.getMobilePhone())) {
                        log.info("与系统已有客户PersonID:{}手机号重复",checkFcPerson.getPersonID());
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        //resultMap.put("message", "[" + MaskUtils.maskPhone(fcPerson.getMobilePhone()) + "] 手机号重复。请确保手机号唯一，无法提供时可忽略家属号码录入");
                        resultMap.put("message", "被保险人【"+fcEmpAndFamilyInfo.getName()+"】与系统已有客户【"+checkFcPerson.getName()+"】手机号重复。请确保手机号唯一，请修改或置为空");
                        return JSON.toJSONString(resultMap);
                    }
                    // 重客逻辑校验
                    List<CoreCustomerInfoResDTO> coreCustomerInfoList = coreCustomerService.sameCustomer(CheckSameCustomerConvert.convertFCEmpAndFamilyInfo(fcEmpAndFamilyInfo));
                    List<CoreCustomerInfoResDTO> errors = coreCustomerInfoList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getErrorList())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(errors) && errors.get(0) != null &&  CollectionUtils.isNotEmpty(errors.get(0).getErrorList())){
                        // 添加错误信息
                        String errorInfo = errors.get(0).getErrorList().get(0).getErrorInfo();
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        resultMap.put("message", errorInfo);
                        return JSON.toJSONString(resultMap);
                    }
                }
            }

            int flag = 0;
            List<FCStaffFamilyRela> checkCount = fcPersonMapper.selectByIdNo2(perId, personId);
            for (FCStaffFamilyRela fcStaffFamilyRela : checkCount) {
                String familyRelaPersonID = fcStaffFamilyRela.getPersonID();
                flag = fcPersonMapper.selectByIdNo3(familyRelaPersonID, fcEmpAndFamilyInfo.getIDNo());
                if (flag > 0) {
                    break;
                }
            }
            //校验添加的用户是否存在
            if (flag > 0) {
                resultMap.put("code", "500");
                resultMap.put("message", "该用户已存在");
                return JSON.toJSONString(resultMap);
            }
            // 校验证件号码和出生日期、性别是否一致
            if (fcEmpAndFamilyInfo.getIDType().equals(IDTypeEnum.IDCARD.getCode()) || fcEmpAndFamilyInfo.getIDType().equals(IDTypeEnum.HOUSEHOLDREGISTER.getCode())) {
                String checkIDCardResult = IDCardUtil.checkIDCard(fcEmpAndFamilyInfo.getIDNo(), fcEmpAndFamilyInfo.getSex(), fcEmpAndFamilyInfo.getBirthDay());
                if (StringUtils.isNotBlank(checkIDCardResult)) {
                    return JSON.toJSONString(ResultUtil.error(checkIDCardResult));
                }
            }

            String iDType = fcEmpAndFamilyInfo.getIDType();
            if (!StringUtil.isEmpty(iDType) && !iDType.equals("1")) {
                if (!StringUtil.isEmpty(CheckUtils.checkChineseName(name))) {
                    return JSON.toJSONString(ResultUtil.error(CheckUtils.checkChineseName(name)));
                }
            } else if (!StringUtil.isEmpty(iDType) && iDType.equals("1")) {
                if (!StringUtil.isEmpty(CheckUtils.checkForeignName(name))) {
                    return JSON.toJSONString(ResultUtil.error(CheckUtils.checkEnglishName(name)));
                }
            }

            if (StringUtils.isBlank(fcEmpAndFamilyInfo.getIdTypeEndDate())) {
                fcEmpAndFamilyInfo.setIdTypeEndDate(null);
            }
            Integer count = empAndFamilyMapper.checkPerTypeByIDNO(fcEmpAndFamilyInfo.getIDNo());
            if (count == 0 && "0".equals(fcEmpAndFamilyInfo.getRelation()) && "undefined".equals(personId)) {
                //修改客户信息表
                FCPerInfo fcPerInfo = updatePerInfo(fcEmpAndFamilyInfo);
                fcPerInfoMapper.updateByPrimaryKeySelective(fcPerInfo);
                resultMap.put("message", "修改个人信息成功");
            } else {
                //校验传来的数据是否为空
                if (!"0".equals(fcEmpAndFamilyInfo.getRelation()) && personId == null || "".equals(personId)) {
                    resultMap.put("message", "传递的个人编号为空");
                    resultMap.put("code", "500");
                    return JSON.toJSONString(resultMap);
                }
                Map<String, Object> params = new HashMap<>();
                params.put("personId", personId);

                //获取关系
                List<FCStaffFamilyRela> personRelalist = fcStaffFamilyRelaMapper.selectPersonIdInfos(personId);
                //确定当前修改的对象
                FCStaffFamilyRela personRela = personRelalist.get(0);


                //不能修改本人关系，本人-本人；本人-家属；家属-本人的情况（传输-存储结构）
                if ("0".equals(fcEmpAndFamilyInfo.getRelation()) && !"0".equals(personRela.getRelation())) {
                    resultMap.put("message", "家属与员工关系不能为本人");
                    resultMap.put("code", "500");
                    return JSON.toJSONString(resultMap);
                }

                /************************************************校验人员信息 start*******************************************************/
                //查询福利信息
                FCEnsure fcEnsure = new FCEnsure();
                if (StringUtils.isNotBlank(userService.getSession(token).ensureCode)) {
                    fcEnsure = fcEnsureMapper.selectByPrimaryKey(userService.getSession(token).ensureCode);
                }
                //校验人员信息
                Map<String, String> map = new HashMap<>();
                if (fcEmpAndFamilyInfo.getRelation().equals("0")) {
                    map.put("sign", "1");
                } else {
                    map.put("sign", "2");
                }
                map.put("idType", fcEmpAndFamilyInfo.getIDType());//证件类型
                map.put("idNo", fcEmpAndFamilyInfo.getIDNo());//证件号
                map.put("birthDay", fcEmpAndFamilyInfo.getBirthDay());//出生日期
                map.put("sex", fcEmpAndFamilyInfo.getSex());//性别
                map.put("nativeplace", fcEmpAndFamilyInfo.getNativeplace());//国籍
                map.put("idTypeEndDate", fcEmpAndFamilyInfo.getIdTypeEndDate());//证件有效期
                map.put("occupationCode", fcEmpAndFamilyInfo.getOccupationCode());//职业代码
                map.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
                String resultMsg = CheckUtils.checkSinglePeople(map);
                if (StringUtils.isNotBlank(resultMsg)) {
                    resultMap.put("message", "被保人 " + fcEmpAndFamilyInfo.getName() + resultMsg);
                    resultMap.put("code", "500");
                    return JSON.toJSONString(resultMap);
                }
                // 校验邮箱
                String email = fcEmpAndFamilyInfo.getEmail();
                if (StringUtils.isNotBlank(email) && !CheckUtils.checkEmail(email)) {
                    return JSON.toJSONString(ResultUtil.error("邮箱地址录入有误，请检查！"));
                }
                List<EvaluationCustomer> customerList = Arrays.asList(EvaluationCustomer.builder()
                        .name(fcEmpAndFamilyInfo.getName())
                        .idType(CoreIdType.getNameByCoreId(fcEmpAndFamilyInfo.getIDType()).name())
                        .idNo(fcEmpAndFamilyInfo.getIDNo())
                        .gender(GenderType.getGenderByCoreId(fcEmpAndFamilyInfo.getSex()).name())
                        .birthday(fcEmpAndFamilyInfo.getBirthDay())
                        .nationality(fcEmpAndFamilyInfo.getNativeplace())
                        .businessNo(StringUtils.isNotEmpty(fcEnsure.getEnsureCode()) ? fcEnsure.getEnsureCode() : null)
                        .build());

                List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
                //校验二要素
                String failVerifies = addressCheckService.checkIdCard(verifies);
                if (StringUtils.isNotEmpty(failVerifies)) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", failVerifies);
                    return JSON.toJSONString(resultMap);
                }

                /************************************************校验人员信息 end*******************************************************/
                //修改员工以及家属的信息
                Map<String, String> map1 = new HashMap<>();
                map1.put("personid", personId);
                map1.put("perno", globalInput.customNo);
                List<String> personIdlist = empAndFamilyMapper.selectSameStaffPersonid(map1);
                for (String personid : personIdlist) {

                    //修改员工家属关联表（家属存于同一个员工下，则同步修改；家属存于其他员工或本员工下则不会同步，只更新所属的家庭下。）
                    FCStaffFamilyRela fcStaffFamilyRela = updateStaffFamilyRela(fcEmpAndFamilyInfo);
                    //创建对象并将参数个人编号存入对象中
                    fcStaffFamilyRela.setPersonID(personid);
                    fcStaffFamilyRela.setPerNo(userService.getSession(token).getCustomNo());
                    //修改个人信息表（家属存于同一个员工下，则同步修改；家属存于其他员工或本员工下则不会同步，只更新所属的家庭下。）
                    FCPerson fcPerson = updatePerson(fcEmpAndFamilyInfo);
                    if (StringUtils.isBlank(fcPerson.getIdTypeEndDate())) {
                        fcPerson.setIdTypeEndDate(null);
                    }
                    //创建对象并将参数个人编号存入对象中
                    if (!"0".equals(fcEmpAndFamilyInfo.getRelation())) {
                        fcPerson.setRelationship("06");
                    }
                    fcPerson.setPersonID(personid);
                    fcPersonMapper.updateByPersonId(fcPerson);
                    //关系证明要更新的,直接更新即可.
                    fcStaffFamilyRelaMapper.updateByPrimaryKeySelective(fcStaffFamilyRela);
                    FCOrderInsured fcOrderInsured = new FCOrderInsured();
                    fcOrderInsured.setIDNo(fcPerson.getIDNo());
                    fcOrderInsured.setMobilePhone(fcPerson.getMobilePhone());
                    fcOrderInsured.setEMail(fcPerson.getEMail());
                    fcOrderInsured.setOperator(globalInput.getUserNo());
                    CommonUtil.initObject(fcOrderInsured, "UPDATE");
                    fcOrderInsuredMapper.updateInPhoneByNoCommitCore(fcOrderInsured);
                }
                //修改员工的信息
                if ("0".equals(personRela.getRelation())) {
                    //修改客户信息表
                    FCPerInfo fcPerInfo = updatePerInfo(fcEmpAndFamilyInfo);
                    fcPerInfo.setRelationship("13");
                    fcPerInfoMapper.updateByPrimaryKeySelective(fcPerInfo);
                    if ("0".equals(personRela.getRelation())) {
                        resultMap.put("message", "修改个人信息成功");
                    }
                    //修改用户表
                    FdUser fdUser = new FdUser();
                    fdUser.setUserName(fcPerInfo.getIDNo());
                    fdUser.setCustomType("1");
                    fdUser.setPhone(fcPerInfo.getMobilePhone());
                    fdUser.setEmail(fcPerInfo.getEmail());
                    fdUser.setOperator(globalInput.getUserNo());
                    fdUser = CommonUtil.initObject(fdUser, "UPDATE");
                    fdUserMapper.updataPwd(fdUser);
                }
            }
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("修改家属信息和个人信息: ", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", e.getMessage());
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description 添加用户信息  Long 应前台要求增加一个标识符
     * @date 15:48 15:48
     * @modified
     */
    @Transactional
    public String addFamilyInfo(FCEmpAndFamilyInfo fcEmpAndFamilyInfo, String token, String Long) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        String msg = "数据保存成功";
        try {
            //检验添加信息是否为空
            if (fcEmpAndFamilyInfo == null) {
                resultMap.put("message", "传递的添加信息不能为空");
                return JSON.toJSONString(resultMap);
            }
            //校验姓名和 电话
            String name = fcEmpAndFamilyInfo.getName();
            String iDType = fcEmpAndFamilyInfo.getIDType();
            if ("8".equals(iDType)) {
                return JSON.toJSONString(ResultUtil.error("证件类型不能为其他，请录入正确的证件类型"));
            }
            if (!StringUtil.isEmpty(iDType) && !iDType.equals("1")) {
                if (!StringUtil.isEmpty(CheckUtils.checkChineseName(name))) {
                    return JSON.toJSONString(ResultUtil.error(CheckUtils.checkChineseName(name)));
                }
            } else if (!StringUtil.isEmpty(iDType) && iDType.equals("1")) {
                if (!StringUtil.isEmpty(CheckUtils.checkForeignName(name))) {
                    return JSON.toJSONString(ResultUtil.error(CheckUtils.checkEnglishName(name)));
                }
            }
            fcEmpAndFamilyInfo.setName(name.trim().replaceAll(" +", " "));
            //****************
            GlobalInput globalInput = userService.getSession(token);
            String idNo = fcEmpAndFamilyInfo.getIDNo();
            String perId = globalInput.getCustomNo();
            int count = fcPersonMapper.selectByIdNo(idNo, perId);
            //校验添加的用户是否存在
            if (count > 0) {
                resultMap.put("message", "该用户已存在");
                return JSON.toJSONString(resultMap);
            }

            if (StringUtils.isNotEmpty(fcEmpAndFamilyInfo.getBirthDay()) && DateTimeUtil.getAgeInt(fcEmpAndFamilyInfo.getBirthDay()) < 18 ){
                //未成年人手机号置空
                fcEmpAndFamilyInfo.setMobilePhone("");
            }

            if (StringUtils.isNotEmpty(fcEmpAndFamilyInfo.getMobilePhone())) {
                if (!CheckUtils.checkMobilePhone(fcEmpAndFamilyInfo.getMobilePhone())) {
                    resultMap.put("message", "手机号格式错误，请检查");
                    return JSON.toJSONString(resultMap);
                }
                FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perId);
                log.info("fcPerInfo Json:{}", JsonUtil.toJSON(fcPerInfo));
                if (!"0".equals(fcEmpAndFamilyInfo.getRelation())) {
                    if (fcEmpAndFamilyInfo.getMobilePhone().equals(fcPerInfo.getMobilePhone())) {
                        resultMap.put("code", "500");
                        resultMap.put("message", fcEmpAndFamilyInfo.getMobilePhone() + "手机号和" + fcPerInfo.getName() + "重复，请确保手机号唯一，无法提供时可忽略家属号码录入！");
                        return JSON.toJSONString(resultMap);
                    }
                }

                List<FCStaffFamilyRela> familyRelaList = fcStaffFamilyRelaMapper.getPersonByInfo(fcPerInfo.getPerNo());
                List<String> personIds = familyRelaList.stream().map(FCStaffFamilyRela::getPersonID).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(personIds)) {
                    List<FCPerson> fcPeople = fcPersonMapper.selectByPersonIds(personIds);
                    List<String> names = fcPeople.stream().filter(s -> s.getMobilePhone().equals(fcEmpAndFamilyInfo.getMobilePhone())).map(FCPerson::getName).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(names)) {
                        resultMap.put("code", "500");
                        resultMap.put("message", fcEmpAndFamilyInfo.getMobilePhone() + "手机号和" + names + "重复，请确保手机号唯一，无法提供时可忽略家属号码录入！");
                        return JSON.toJSONString(resultMap);
                    }
                    List<String> idNoList = fcPeople.stream().map(FCPerson::getIDNo).collect(Collectors.toList());
                    FCPerson checkFcPerson = fcPersonMapper.checkPhoneFcPerson(idNoList,fcEmpAndFamilyInfo.getMobilePhone());
                    if (checkFcPerson != null && fcEmpAndFamilyInfo.getMobilePhone().equals(checkFcPerson.getMobilePhone())) {
                        log.info("与系统已有客户PersonID:{}手机号重复",checkFcPerson.getPersonID());
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        //resultMap.put("message", "[" + MaskUtils.maskPhone(fcPerson.getMobilePhone()) + "] 手机号重复。请确保手机号唯一，无法提供时可忽略家属号码录入");
                        resultMap.put("message", "被保险人【"+fcEmpAndFamilyInfo.getName()+"】与系统已有客户【"+checkFcPerson.getName()+"】手机号重复。请确保手机号唯一，请修改或置为空");
                        return JSON.toJSONString(resultMap);
                    }
                    List<CoreCustomerInfoResDTO> coreCustomerInfoList = coreCustomerService.sameCustomer(CheckSameCustomerConvert.convertFCEmpAndFamilyInfo(fcEmpAndFamilyInfo));
                    List<CoreCustomerInfoResDTO> errors = coreCustomerInfoList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getErrorList())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(errors) && errors.get(0) != null &&  CollectionUtils.isNotEmpty(errors.get(0).getErrorList())){
                        // 添加错误信息
                        String errorInfo = errors.get(0).getErrorList().get(0).getErrorInfo();
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        resultMap.put("message", errorInfo);
                        return JSON.toJSONString(resultMap);
                    }
                }
            }
            //校验添加的用户是否存在(监护人)
            //查询福利信息
            FCEnsure fcEnsure = new FCEnsure();
            if (StringUtils.isNotBlank(userService.getSession(token).ensureCode)) {
                fcEnsure = fcEnsureMapper.selectByPrimaryKey(userService.getSession(token).ensureCode);
            }
            if (StringUtils.isNotBlank(fcEnsure.getEnsureType()) && "1".equals(fcEnsure.getEnsureType())) {
                if (!"3".equals(fcEmpAndFamilyInfo.getRelation()) && !"6".equals(fcEmpAndFamilyInfo.getRelation())) {
                    resultMap.put("message", "学生与监护人关系仅能选择子女或其他！");
                    return JSON.toJSONString(resultMap);
                }
            }

            if (ConstantUtil.relation_0.equals(fcEmpAndFamilyInfo.getRelation())) {
                resultMap.put("message", "家属关系不能选择本人！");
                return JSON.toJSONString(resultMap);
            }
            if (StringUtils.isBlank(fcEmpAndFamilyInfo.getIdTypeEndDate())) {
                fcEmpAndFamilyInfo.setIdTypeEndDate(null);
            }
            //校验人员信息
            Map<String, String> map = new HashMap<>();
            map.put("sign", "2");//1：员工 2：家属
            map.put("idType", fcEmpAndFamilyInfo.getIDType());//证件类型
            map.put("idNo", fcEmpAndFamilyInfo.getIDNo());//证件号
            map.put("birthDay", fcEmpAndFamilyInfo.getBirthDay());//出生日期
            map.put("sex", fcEmpAndFamilyInfo.getSex());//性别
            map.put("nativeplace", fcEmpAndFamilyInfo.getNativeplace());//国籍
            map.put("idTypeEndDate", fcEmpAndFamilyInfo.getIdTypeEndDate());//证件有效期
            map.put("occupationCode", fcEmpAndFamilyInfo.getOccupationCode());//职业代码
            map.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
            String resultMsg = CheckUtils.checkSinglePeople(map);
            if (StringUtils.isNotBlank(resultMsg)) {
                resultMap.put("message", "被保人 " + fcEmpAndFamilyInfo.getName() + resultMsg);
                return JSON.toJSONString(resultMap);
            }
            // 校验证件号码和出生日期、性别是否一致
            if (fcEmpAndFamilyInfo.getIDType().equals(IDTypeEnum.IDCARD.getCode())
                    || fcEmpAndFamilyInfo.getIDType().equals(IDTypeEnum.HOUSEHOLDREGISTER.getCode())) {
                String checkIDCardResult = IDCardUtil.checkIDCard(fcEmpAndFamilyInfo.getIDNo(),
                        fcEmpAndFamilyInfo.getSex(), fcEmpAndFamilyInfo.getBirthDay());
                if (StringUtils.isNotBlank(checkIDCardResult)) {
                    return JSON.toJSONString(ResultUtil.error(checkIDCardResult));
                }
            }
            // 校验邮箱
            String email = fcEmpAndFamilyInfo.getEmail();
            if (StringUtils.isNotBlank(email) && !CheckUtils.checkEmail(email)) {
                resultMap.put("message", "邮箱地址录入有误，请检查！");
                return JSON.toJSONString(resultMap);
            }
            //校验关系数量
            //校验关系
            String flag = checkFamilySum(fcEmpAndFamilyInfo, perId);
            if (StringUtils.isNotBlank(flag)) {
                msg = flag;
            }
            log.info("校验关系数量校验:{}", msg);
            List<EvaluationCustomer> customerList = Collections.singletonList(EvaluationCustomer.builder()
                    .name(fcEmpAndFamilyInfo.getName())
                    .idType(CoreIdType.getNameByCoreId(fcEmpAndFamilyInfo.getIDType()).name())
                    .idNo(fcEmpAndFamilyInfo.getIDNo())
                    .gender(GenderType.getGenderByCoreId(fcEmpAndFamilyInfo.getSex()).name())
                    .birthday(fcEmpAndFamilyInfo.getBirthDay())
                    .nationality(fcEmpAndFamilyInfo.getNativeplace())
                    .businessNo(StringUtils.isNotEmpty(fcEnsure.getEnsureCode()) ? fcEnsure.getEnsureCode() : null)
                    .build());

            List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
            //校验二要素
            String failVerifies = addressCheckService.checkIdCard(verifies);
            if (StringUtils.isNotEmpty(failVerifies)) {
                resultMap.put("message", failVerifies);
                return JSON.toJSONString(resultMap);
            }
            List<String> pernoList = fcPerInfoMapper.getPerNoByFamIdNoList(globalInput.getUserName(), idNo);
            //个人信息表
            for (int i = 0; i < pernoList.size(); i++) {
                FCPerson fcPerson = updatePerson(fcEmpAndFamilyInfo);
                fcPerson.setPersonID(maxNoService.createMaxNo("PersonID", null, 20));
                CommonUtil.initObject(fcPerson, "INSERT");
                //员工家属信息关联表
                FCStaffFamilyRela fcStaffFamilyRela = updateStaffFamilyRela(fcEmpAndFamilyInfo);
                fcStaffFamilyRela.setPerNo(pernoList.get(i));
                fcStaffFamilyRela.setPersonID(fcPerson.getPersonID());
                CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
                try {
                    fcPersonMapper.insert(fcPerson);
                    fcStaffFamilyRelaMapper.insert(fcStaffFamilyRela);
                } catch (Exception e) {
                    log.info("添加家属失败：", e);
                    throw new RuntimeException();
                }
            }
            resultMap.put("Long", Long);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", msg);
        } catch (Exception e) {
            log.info("添加失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "添加失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 3-子女时（即投保人为父母投保），则系统需判断投保人在公司是否已存在作为其父母关系的其它客户信息且为其父母的客户数量达到3人及以上
     * 1-父母时（即投保人为子女投保）,则系统需判断投保人在公司是否已存在作为其子女关系的其它客户信息且为其子女的客户数量达到5人及以上
     * 2-配偶时（即投保人为配偶投保）,则系统需判断投保人在公司是否已存在作为其配偶关系的其它客户信息
     */
    private String checkFamilySum(FCEmpAndFamilyInfo fcEmpAndFamilyInfo, String perNo) {
        String relation = fcEmpAndFamilyInfo.getRelation();

        /**
         * 获取家属的 personid
         */
        FCPerInfo fcperinfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
        // 查询员工下所有家属的证件号
        List<String> idnolist = empAndFamilyMapper.selectFamilyIdNo(perNo);
        // 查询家属证件号的对应的最新的家属personid
        List<String> perosnidlist = new ArrayList<>();
        for (String idno : idnolist) {
            Map<String, String> map1 = new HashMap<>();
            map1.put("IdNo", idno);
            map1.put("perNo", perNo);
            String personid = empAndFamilyMapper.selectNewFamilyPersonid(map1);
            if (StringUtils.isNotBlank(personid)) {
                perosnidlist.add(personid);
            }
        }
        /**
         * 获取员工的 personid
         */
        //根据perno获取员工personid
        List<String> staffPersonIdList = fcPersonMapper.getEmployPersonId(perNo);
        perosnidlist.add(staffPersonIdList.get(0));
        Map<String, Object> paraMap = new HashMap<String, Object>();
        paraMap.put("perosnidlist", perosnidlist);
        paraMap.put("idNo", fcperinfo.getIDNo());

        /**
         * 获取人员信息
         */
        List<FCPerson> familyList = empAndFamilyMapper.selectAllFamilyInfo(paraMap);
        Long childrenCount = 0L;
        Long parentsCount = 0L;
        Long spouseCount = 0L;
        if (CollectionUtils.isNotEmpty(familyList)) {
            childrenCount = familyList.stream().filter(item -> "3".equals(item.getRelation())).count();
            spouseCount = familyList.stream().filter(item -> "2".equals(item.getRelation())).count();
            parentsCount = familyList.stream().filter(item -> "1".equals(item.getRelation())).count();
        }
        switch (relation) {
            case "1":
                if (parentsCount >= 3L) {
                    return "投/被保险人关系信息存疑，需要进一步核实（注：检测异常点为父母数量异常，有虚假保单风险）";
                }
                break;
            case "2":
                if (spouseCount >= 1L) {
                    return "投/被保险人关系信息存疑，需要进一步核实（注：检测异常点为配偶数量异常，有虚假保单风险）";
                }
                break;
            case "3":
                if (childrenCount >= 5L) {
                    return "投/被保险人关系信息存疑，需要进一步核实（注：检测异常点为子女数量异常，有虚假保单风险）";
                }
                break;
        }
        return "";
    }

    /**
     * <AUTHOR>
     * @description 修改个人信息表
     * @date 15:13 15:13
     * @modified
     */
    public FCPerson updatePerson(FCEmpAndFamilyInfo fcEmpAndFamilyInfo) {
        FCPerson fcPerson = new FCPerson();
        fcPerson.setName(fcEmpAndFamilyInfo.getName());
        fcPerson.setBirthDate(fcEmpAndFamilyInfo.getBirthDay());
        fcPerson.setIDNo(fcEmpAndFamilyInfo.getIDNo());
        fcPerson.setIDType(fcEmpAndFamilyInfo.getIDType());
        fcPerson.setNativeplace(fcEmpAndFamilyInfo.getNativeplace());
        fcPerson.setIdTypeEndDate(fcEmpAndFamilyInfo.getIdTypeEndDate());
        fcPerson.setOpenBank(fcEmpAndFamilyInfo.getOpenBank());
        fcPerson.setMobilePhone(fcEmpAndFamilyInfo.getMobilePhone());
        fcPerson.setOccupationCode(fcEmpAndFamilyInfo.getOccupationCode());
        fcPerson.setOpenAccount(fcEmpAndFamilyInfo.getOpenAccount());
        fcPerson.setOccupationType(fcEmpAndFamilyInfo.getOccupationType());
        fcPerson.setOperator(fcEmpAndFamilyInfo.getName());
        fcPerson.setSex(fcEmpAndFamilyInfo.getSex());
        fcPerson.setEMail(fcEmpAndFamilyInfo.getEmail());
        fcPerson.setRelationship("06");
        fcPerson.setJoinMedProtect(fcEmpAndFamilyInfo.getJoinMedProtect());
        CommonUtil.initObject(fcPerson, "UPDATE");
        return fcPerson;
    }

    /**
     * <AUTHOR>
     * @description 修改员工家属信息关联表
     * @date 15:14 15:14
     * @modified
     */
    public FCStaffFamilyRela updateStaffFamilyRela(FCEmpAndFamilyInfo fcEmpAndFamilyInfo) {
        FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
        fcStaffFamilyRela.setRelation(fcEmpAndFamilyInfo.getRelation());
        fcStaffFamilyRela.setOperator(fcEmpAndFamilyInfo.getName());
        fcStaffFamilyRela.setRelationProve(fcEmpAndFamilyInfo.getRelationProve());
        CommonUtil.initObject(fcStaffFamilyRela, "UPDATE");
        return fcStaffFamilyRela;
    }

    /**
     * <AUTHOR>
     * @description修改个人客户信息表
     * @date 15:15 15:15
     * @modified
     */
    public FCPerInfo updatePerInfo(FCEmpAndFamilyInfo fcEmpAndFamilyInfo) {
        FCPerInfo fcPerInfo = new FCPerInfo();
        fcPerInfo.setBirthDay(fcEmpAndFamilyInfo.getBirthDay());
        fcPerInfo.setIDNo(fcEmpAndFamilyInfo.getIDNo());
        fcPerInfo.setIDType(fcEmpAndFamilyInfo.getIDType());
        fcPerInfo.setNativeplace(fcEmpAndFamilyInfo.getNativeplace());
        fcPerInfo.setIdTypeEndDate(fcEmpAndFamilyInfo.getIdTypeEndDate());
        fcPerInfo.setName(fcEmpAndFamilyInfo.getName());
        fcPerInfo.setMobilePhone(fcEmpAndFamilyInfo.getMobilePhone());
        fcPerInfo.setOccupationCode(fcEmpAndFamilyInfo.getOccupationCode());
        fcPerInfo.setOccupationType(fcEmpAndFamilyInfo.getOccupationType());
        fcPerInfo.setOpenAccount(fcEmpAndFamilyInfo.getOpenAccount());
        fcPerInfo.setOpenBank(fcEmpAndFamilyInfo.getOpenBank());
        fcPerInfo.setSex(fcEmpAndFamilyInfo.getSex());
        fcPerInfo.setEmail(fcEmpAndFamilyInfo.getEmail());
        fcPerInfo.setOperator(fcEmpAndFamilyInfo.getName());
        fcPerInfo.setRelationship("06");
        fcPerInfo.setJoinMedProtect(fcEmpAndFamilyInfo.getJoinMedProtect());
        return CommonUtil.initObject(fcPerInfo, "UPDATE");
    }

    /**
     * <AUTHOR>
     * @description家庭福利查询
     * @date 19:28 19:28
     * @modified
     */
    public String getFamilyEnsure(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String perNo = globalInput.getCustomNo();
            FCPerInfo fcperinfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
            List<Map<String, Object>> listMap = new ArrayList<>();
            /**查询要展示的家属信息*/
            List<String> idnolist = empAndFamilyMapper.selectFamilyIDNO(perNo);
            List<String> perosnidlist = new ArrayList<>();
            for (String idno : idnolist) {
                Map<String, String> map1 = new HashMap<>();
                map1.put("IdNo", idno);
                map1.put("perNo", perNo);
                String personid = empAndFamilyMapper.selectNewFamilyPersonid(map1);
                if (StringUtils.isNotBlank(personid)) {
                    perosnidlist.add(personid);
                }
            }
            Map<String, Object> paraMap = new HashMap<String, Object>();
            paraMap.put("perosnidlist", perosnidlist);
            paraMap.put("idNo", fcperinfo.getIDNo());
            List<FCPerson> familyList = empAndFamilyMapper.selectAllFamilyInfo(paraMap);
            /**获取同一员工下证件号相同的家属所属计划数据列表*/
            for (FCPerson fcPerson : familyList) {
                List<Map<String, Object>> planlist = new ArrayList<>();
                Map<String, String> map1 = new HashMap<>();
                map1.put("personid", fcPerson.getPersonID());
                map1.put("perno", perNo);
                List<Map<String, String>> personIdlist = empAndFamilyMapper.selectSameStaffPersonidPerNo(map1);
                /**返回前台信息*/
                Map<String, Object> result = new HashMap<>();
                //不存在已承保订单  isTure--0
                result.put("isTrue", "0");
                //TODO　这块的写的太冗余。
                for (Map<String, String> insureInfo : personIdlist) {
                    List<Map<String, String>> getInsuredOrderInfo = fcOrderInsuredMapper.getInsuredOrderInfo(insureInfo.get("perNo"), insureInfo.get("personID"));
                    if (getInsuredOrderInfo.size() > 0) {
                        for (Map<String, String> insuredOrderInfoMap : getInsuredOrderInfo) {
                            Map<String, Object> map2 = new HashMap<>();
                            String orderSign = insuredOrderInfoMap.get("orderSign");
                            if (StringUtils.isNotBlank(orderSign) && orderSign.matches("^(1|2)")) {
                                if (insuredOrderInfoMap.get("orderStatus").equals("08")) {
                                    result.put("isTrue", "1");
                                    map2.put("ensureName", insuredOrderInfoMap.get("ensureName"));
                                    map2.put("ensureCode", insuredOrderInfoMap.get("ensureCode"));
                                    planlist.add(map2);
                                }
                            } else {
                                //投保成功 即 已承保   isTure--1  前台展示 已投保
                                if ("04".equals(insuredOrderInfoMap.get("grpOrderStatus"))) {
                                    result.put("isTrue", "1");
                                    map2.put("ensureName", insuredOrderInfoMap.get("ensureName"));
                                    map2.put("ensureCode", insuredOrderInfoMap.get("ensureCode"));
                                    planlist.add(map2);
                                }
                            }
                        }
                    }
                }
                //最新的一条数据信息
                result.put("name", fcPerson.getName());
                result.put("relation", fcPerson.getRelation());
                result.put("personId", fcPerson.getPersonID());
                result.put("idno", fcPerson.getIDNo());
                result.put("mapList", planlist);
                listMap.add(result);
            }
            resultMap.put("code", "200");
            resultMap.put("message", "查询成功");
            resultMap.put("data", listMap);
        } catch (Exception e) {
            log.info("查询失败", e);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap, SerializerFeature.DisableCircularReferenceDetect);
    }

    /**
     * 登录成功后异步调用此方法同步员工家属信息
     *
     * @param userNo
     * @return
     */
    @Async
    public void FamilySynation(String userNo) {
        Map<String, String> map = new HashMap<>();
        try {
            map.put("UserNo", userNo);
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByUserNo(map);
            RemoteDelegate rd = RemoteDelegate.getInstance();
            log.info("员工家属信息同步接口调用时间：" + DateTimeUtil.getCurrentDateTime());
            String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
                    "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                    "\t\t<TransType>BF0007</TransType>\n" +
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<body>\n" +
                    "\t\t<Name>" + fcPerInfo.getName() + "</Name>\n" +
                    "\t\t<Sex>" + fcPerInfo.getSex() + "</Sex>\n" +
                    "\t\t<Birthday>" + fcPerInfo.getBirthDay() + "</Birthday>\n" +
                    "\t\t<IDType>" + fcPerInfo.getIDType() + "</IDType>\n" +
                    "\t\t<IDNo>" + fcPerInfo.getIDNo() + "</IDNo>\n" +
                    "\t</body>\n" +
                    "</RequestInfo>";
            log.info("调用接口请求报文：" + reqXml);
            long startStamp = System.currentTimeMillis();
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "BenefitService", reqXml);
            long endStamp = System.currentTimeMillis();
            log.info("调用接口用时：" + (endStamp - startStamp));
            if (success) {
                Map<String, Object> responseXml = rd.getResult();
                Head head = (Head) responseXml.get("Head");
                if ("200".equals(head.getResultCode())) {
                    Body body = (Body) responseXml.get("Body");
                    List<Family> familyList = body.getFamilyList();
                    List<Family> updateFamilyList = new ArrayList<>();
                    if (familyList != null && familyList.size() > 0) {
                        for (Family family : familyList) {
                            if ("00".equals(family.getRelation())) {
                                continue;
                            }
                            List<FCPerson> fcPerson2 = fcPersonMapper.selectByIDNo(family);
                            //判断平台是否存在该家属，存在-更新   不存在-新增
                            if (fcPerson2.size() > 0) {
                                updateFamilyList.add(family);
                            }
                            List<String> pernoList = fcPerInfoMapper.getPerNoByFamIdNoList(fcPerInfo.getIDNo(), family.getIDNo());
                            //个人信息表
                            for (int i = 0; i < pernoList.size(); i++) {
                                //同步fcperson表
                                FCPerson fcPerson3 = getFcPerson(family, userNo);
                                fcPersonMapper.insertSelective(fcPerson3);
                                //同步fcperson表
                                FCStaffFamilyRela fcStaffFamilyRelas = getFcStaffamilyRela(fcPerson3.getPersonID(), pernoList.get(i), family.getRelation(), userNo);
                                fcStaffFamilyRelaMapper.insertSelective(fcStaffFamilyRelas);
                            }
                        }
                        //存在待更新的家属
                        if (updateFamilyList.size() > 0) {
                            fcPersonMapper.updatePersonList(updateFamilyList);
                        }
                    }
                } else {
                    log.info("员工家属信息查询失败");
                }
            }
        } catch (Exception e) {
            log.info("查询失败", e);
        }
    }

    public FCPerson getFcPerson(Family family, String userNo) {
        FCPerson fcPerson = new FCPerson();
        fcPerson.setPersonID(maxNoService.createMaxNo("PersonId", "", 20));
        fcPerson.setName(family.getName());
        fcPerson.setSex(family.getSex());
        fcPerson.setBirthDate(family.getBirthday());
        fcPerson.setIDType(family.getIDType());
        fcPerson.setIDNo(family.getIDNo());
        fcPerson.setOccupationType(family.getOccupationType());
        fcPerson.setOccupationCode(family.getOccupationCode());
        fcPerson.setMobilePhone(family.getMobile());
        fcPerson.setPhone(family.getPhone());
        fcPerson.setEMail(family.getEmail());
        fcPerson.setJoinMedProtect(family.getSocialInsuFlag());
        fcPerson.setZipCode(family.getZipCode());
        fcPerson.setAddress(family.getAddress());
        fcPerson.setOperator(userNo);
        fcPerson = CommonUtil.initObject(fcPerson, "INSERT");
        return fcPerson;
    }

    public FCStaffFamilyRela getFcStaffamilyRela(String personID, String perNo, String relation, String userNo) {
        String relationProve = "";
        switch (relation) {
            case "00":
                relation = "0";
                relationProve = "本人";
                break;
            case "01":
                relation = "1";
                break;
            case "02":
                relation = "2";
                break;
            case "03":
                relation = "3";
                break;
            case "06":
                relation = "6";
                break;
        }
        FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
        fcStaffFamilyRela.setPerNo(perNo);
        fcStaffFamilyRela.setPersonID(personID);
        fcStaffFamilyRela.setRelation(relation);
        fcStaffFamilyRela.setRelationProve(relationProve);
        fcStaffFamilyRela.setOperator(userNo);
        fcStaffFamilyRela = (FCStaffFamilyRela) CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
        return fcStaffFamilyRela;
    }

    public Map checkFamilyPerson(String personid) {
        Map map = empAndFamilyMapper.getByPersonid(personid);
        if (map == null) {
            return ResultUtil.error("未查到被保人信息！");
        }
        //投保检验被保人必录信息是否完整
        if (map.get("name") == null || map.get("name").equals("") ||
                map.get("sex") == null || map.get("sex").equals("") ||
                map.get("birthDate") == null || map.get("birthDate").equals("") ||
                map.get("mobilePhone") == null || map.get("mobilePhone").equals("") ||
                map.get("iDNo") == null || map.get("iDNo").equals("") ||
                map.get("iDType") == null || map.get("iDType").equals("") ||
                map.get("occupationCode") == null || map.get("occupationCode").equals("") ||
                map.get("JoinMedProtect") == null || map.get("JoinMedProtect").equals("")
        ) {
            if (map.get("relation").equals("0")) {
                return ResultUtil.success("请完善您的个人信息", map);
            } else {
                return ResultUtil.success("请完善家属信息", map);
            }
        }
        return ResultUtil.success("校验成功", true);
    }

    public String checkFamilyInfo(String personId, FCEmpAndFamilyInfo fcEmpAndFamilyInfo, String token) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "200");
        resultMap.put("message", "校验成功");
        GlobalInput globalInput = userService.getSession(token);
        FCPerson checkPerson = fcPersonMapper.selectFcpersonByPerNo(globalInput.getCustomNo());
        if (checkPerson != null) {
            Map<String, String> checkMap = new HashMap<>();
            checkMap.put("userSex", checkPerson.getSex());
            checkMap.put("familySex", fcEmpAndFamilyInfo.getSex());
            checkMap.put("userID", checkPerson.getIDNo());
            checkMap.put("familyID", fcEmpAndFamilyInfo.getIDNo());
            checkMap.put("userBirthday", checkPerson.getBirthDate());
            checkMap.put("familyBirthday", fcEmpAndFamilyInfo.getBirthDay());
            checkMap.put("relation", fcEmpAndFamilyInfo.getRelation());

            String result = CheckUtils.checkRelationship(checkMap);
            if ("投&被保险人关系信息异常，请重新确认".equals(result)) {
                resultMap.put("message", result);
                resultMap.put("code", "500");
                return JSON.toJSONString(resultMap);
            } else if (StringUtils.isNotEmpty(result)) {
                resultMap.put("code", "400");
                resultMap.put("message", result);
            }
        }

        return JSON.toJSONString(resultMap);
    }

    public String checkSelection(List<CheckPersonRequest> checkList, String token) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        List<EvaluationCustomer> customerList = new ArrayList<>();

        //非阻断性集合
        List<CheckPersonRequest> arrayList = new ArrayList<>();
        for (CheckPersonRequest fcEmpAndFamilyInfo : checkList) {
            List<CheckPersonRequest> arrayErrorList = new ArrayList<>();
            //校验人员信息
            String flag = "2";
            if (fcEmpAndFamilyInfo.getRelation().equals("0")) {
                flag = "1";
            }
            Map<String, String> map = new HashMap<>();
            map.put("sign", flag);//1：员工 2：家属
            map.put("idType", fcEmpAndFamilyInfo.getIDType());//证件类型
            map.put("idNo", fcEmpAndFamilyInfo.getIDNo());//证件号
            map.put("birthDay", fcEmpAndFamilyInfo.getBirthDate());//出生日期
            map.put("sex", fcEmpAndFamilyInfo.getSex());//性别
            map.put("nativeplace", fcEmpAndFamilyInfo.getNativeplace());//国籍
            map.put("idTypeEndDate", fcEmpAndFamilyInfo.getIdTypeEndDate());//证件有效期
            map.put("occupationCode", fcEmpAndFamilyInfo.getOccupationCode());//职业代码
            //map.put("ensurevaliDate", fcEnsure.getCvaliDate());//福利生效日期
            String resultMsg = CheckUtils.checkSinglePeople(map);
            if (StringUtils.isNotBlank(resultMsg)) {
                fcEmpAndFamilyInfo.setErrorList(Collections.singletonList(resultMsg));
                arrayErrorList.add(fcEmpAndFamilyInfo);
                resultMap.put("data", arrayErrorList);
                //resultMap.put("message",fcEmpAndFamilyInfo.getName()+resultMsg);
                return JSON.toJSONString(resultMap);
            }
            // 校验证件号码和出生日期、性别是否一致
            if (fcEmpAndFamilyInfo.getIDType().equals(IDTypeEnum.IDCARD.getCode())
                    || fcEmpAndFamilyInfo.getIDType().equals(IDTypeEnum.HOUSEHOLDREGISTER.getCode())) {
                String checkIDCardResult = IDCardUtil.checkIDCard(fcEmpAndFamilyInfo.getIDNo(),
                        fcEmpAndFamilyInfo.getSex(), fcEmpAndFamilyInfo.getBirthDate());
                if (StringUtils.isNotBlank(checkIDCardResult)) {
                    fcEmpAndFamilyInfo.setErrorList(Collections.singletonList(checkIDCardResult));
                    arrayErrorList.add(fcEmpAndFamilyInfo);
                    resultMap.put("data", arrayErrorList);
                    return JSON.toJSONString(resultMap);
                }
            }

            String name = fcEmpAndFamilyInfo.getName();
            String iDType = fcEmpAndFamilyInfo.getIDType();
            if (!StringUtil.isEmpty(iDType) && !iDType.equals("1")) {
                if (!StringUtil.isEmpty(CheckUtils.checkChineseName2(name))) {
                    fcEmpAndFamilyInfo.setErrorList(Collections.singletonList(CheckUtils.checkChineseName2(name)));
                    arrayErrorList.add(fcEmpAndFamilyInfo);
                    resultMap.put("data", arrayErrorList);
                    return JSON.toJSONString(resultMap);
                }
            } else if (!StringUtil.isEmpty(iDType) && iDType.equals("1")) {
                if (!StringUtil.isEmpty(CheckUtils.checkForeignName(name))) {
                    fcEmpAndFamilyInfo.setErrorList(Collections.singletonList(CheckUtils.checkChineseName2(name)));
                    arrayErrorList.add(fcEmpAndFamilyInfo);
                    resultMap.put("data", arrayErrorList);
                    return JSON.toJSONString(resultMap);
                }
            }

            if (!fcEmpAndFamilyInfo.getRelation().equals("0")) {
                GlobalInput globalInput = userService.getSession(token);
                FCPerson checkPerson = fcPersonMapper.selectFcpersonByPerNo(globalInput.getCustomNo());
                if (checkPerson != null) {
                    Map<String, String> checkMap = new HashMap<>();
                    checkMap.put("userSex", checkPerson.getSex());
                    checkMap.put("familySex", fcEmpAndFamilyInfo.getSex());
                    checkMap.put("userID", checkPerson.getIDNo());
                    checkMap.put("familyID", fcEmpAndFamilyInfo.getIDNo());
                    checkMap.put("userBirthday", checkPerson.getBirthDate());
                    checkMap.put("familyBirthday", fcEmpAndFamilyInfo.getBirthDate());
                    checkMap.put("relation", fcEmpAndFamilyInfo.getRelation());

                    String result = CheckUtils.checkRelationship(checkMap);
                    if ("投&被保险人关系信息异常，请重新确认".equals(result)) {
                        fcEmpAndFamilyInfo.setErrorList(Collections.singletonList(result));
                        arrayErrorList.add(fcEmpAndFamilyInfo);
                        resultMap.put("data", arrayErrorList);
                        return JSON.toJSONString(resultMap);
                    } else if (StringUtils.isNotEmpty(result)) {
                        fcEmpAndFamilyInfo.setErrorList(Collections.singletonList(result));
                        arrayList.add(fcEmpAndFamilyInfo);
                    }
                }
            }


            customerList.add(EvaluationCustomer.builder()
                    .name(fcEmpAndFamilyInfo.getName())
                    .idType(CoreIdType.getNameByCoreId(fcEmpAndFamilyInfo.getIDType()).name())
                    .idNo(fcEmpAndFamilyInfo.getIDNo())
                    .gender(GenderType.getGenderByCoreId(fcEmpAndFamilyInfo.getSex()).name())
                    .birthday(fcEmpAndFamilyInfo.getBirthDate())
                    .nationality(fcEmpAndFamilyInfo.getNativeplace())
                    .businessNo(null)
                    .build());
            //校验黑名单
            List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
            //校验二要素
            String failVerifies = addressCheckService.checkIdCard(verifies);
            if (StringUtils.isNotEmpty(failVerifies)) {
                fcEmpAndFamilyInfo.setErrorList(Collections.singletonList(" 录入的姓名与证件号码不符"));
                arrayErrorList.add(fcEmpAndFamilyInfo);
                resultMap.put("data", arrayErrorList);
                return JSON.toJSONString(resultMap);
            }
        }

        if (CollectionUtils.isNotEmpty(arrayList)) {
            resultMap.put("code", "400");
            resultMap.put("data", arrayList);
            return JSON.toJSONString(resultMap);
        } else {
            resultMap.put("code", "200");
            resultMap.put("message", "校验通过!!");
            return JSON.toJSONString(resultMap);
        }
    }
}
