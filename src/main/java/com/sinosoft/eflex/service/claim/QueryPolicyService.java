package com.sinosoft.eflex.service.claim;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FCGrpInfoMapper;
import com.sinosoft.eflex.dao.FCGrpOrderMapper;
import com.sinosoft.eflex.dao.FDCodeMapper;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;


import java.util.*;

/**
 * @DESCRIPTION
 * @create 2018-11-06 11:13
 **/
@Service
public class QueryPolicyService {

    /**
     * 日志
     */
    private static Logger Log = LoggerFactory.getLogger(QueryPolicyService.class);

    /**
     * 引入公共资源
     */
    @Autowired
    private UserService userService;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private MyProps myProps;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;


    /**
     *  查询该企业下所有保单列表，包含平台保单和线下保单、包含有效、终止等所有状态的保单。
     *
     * @param token
     * @return
     */
    public String queryPolicyList(String token, Map<String, String> params, Integer pageNum, Integer pageSize) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        if (globalInput == null || globalInput.getGrpNo() == null || "".equals(globalInput.getGrpNo())) {
            Log.info("查不到您的所属企业，请尝试重新登陆");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查不到您的所属企业，请尝试重新登陆！");
            return JSON.toJSONString(resultMap);
        }
        //获取当前企业信息
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(globalInput.getGrpNo());
        if (fcGrpInfo == null) {
            Log.info("查不到您的所属企业，请尝试重新登陆");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查不到您的所属企业，请尝试重新登陆！");
            return JSON.toJSONString(resultMap);
        }
        /********************获取查询条件*****************************/
        // 处理null值
        params.putIfAbsent("GrpContNo", "");
        params.putIfAbsent("EffectDate", "");
        params.putIfAbsent("EndDate", "");
        params.putIfAbsent("Status", "");

        //拼接请求报文
        RemoteDelegate rd = RemoteDelegate.getInstance();
        Map<String, String> myPrpsMap = myProps.getServiceInfo();
        String grpIdTypeName = fdCodeMapper.selectNameByCode("GrpIdType", fcGrpInfo.getGrpIdType());
        String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<RequestInfo>\n" +
                "\t<HEAD>\n" +
                /* 交易流水号 */
                "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                /* 接口交易类型 */
                "\t\t<TransType>BF0004</TransType>\n" +
                /* 交易日期 */
                "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                /* 交易时间 */
                "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                "\t</HEAD>\n" +
                "\t<BODY>\n" +
                "\t\t<GrpContQuery>\n" +
                "\t\t\t<GrpIdType>" + fcGrpInfo.getGrpIdType() + "</GrpIdType>\n" +
                "\t\t\t<GrpIdTypeName>" + grpIdTypeName + "</GrpIdTypeName>\n" +
                "\t\t\t<GrpIdNo>" + fcGrpInfo.getGrpIdNo() + "</GrpIdNo>\t\n" +
                "\t\t\t<GrpContNo>" + params.get("GrpContNo") + "</GrpContNo>\t\n" +
                "\t\t\t<EffectDate>" + params.get("EffectDate") + "</EffectDate>\t\n" +
                "\t\t\t<EndDate>" + params.get("EndDate") + "</EndDate>\t\n" +
                "\t\t\t<Status>" + params.get("Status") + "</Status>\t\n" +
                "\t\t\t<PageNum>" + pageNum + "</PageNum>\t\n" +
                "\t\t\t<PageSize>" + pageSize + "</PageSize>\t\n" +
                "\t\t</GrpContQuery>\n" +
                "\t</BODY>\n" +
                "</RequestInfo>";
        Log.info("企业保单查询接口请求报文：" + reqXml);
        long startStamp = System.currentTimeMillis();
        //将请求报文发送核心
        boolean success = rd.submitData(myPrpsMap.get("serviceUrl"), myPrpsMap.get("operation"), "", reqXml); //BenefitService
        long endStamp = System.currentTimeMillis();
        Log.info("调用核心接口用时：" + (endStamp - startStamp));
        if (success) {
            //接收返回结果
            Map<String, Object> responseXml = rd.getResult();
            List<GrpContInfo> list = (List<GrpContInfo>) responseXml.get("GrpContList");
            /**
             * 查询保单对应的福利编号
             */
            for (GrpContInfo grpContInfo : list) {
                FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByGrpContNo(grpContInfo.getGrpContNo());
                if (!ObjectUtils.isEmpty(fcGrpOrder)) {
                    grpContInfo.setEnsureCode(fcGrpOrder.getEnsureCode());
                    grpContInfo.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
                }
            }

            PageHelperUtil<GrpContInfo> page = new PageHelperUtil<>(list);
            //打印
            Log.info("接收核心数据:" + JSON.toJSONString(responseXml));
            resultMap.put("data", page.getList());
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询保单成功");
            return JSON.toJSONString(resultMap);
        } else {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询保单失败，请联系管理员");
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 	根据保单号查询该保单的理赔记录
     * 可选查询条件：理赔申请日期段、年龄段、被保人类别、性别、险种、被保人姓名、证件类型、证件号
     *
     * @param params
     * @return
     */
    public String queryPolicyClaimList(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();

        //企业页面初始化 查询全部以及模糊查询没有年龄限制时 isTrue为0   有年龄限制  isTrue为2 不查询保单下所有人
        //个人页面查询isTrue为1 不查询全部
        //isTrue为1和2时的区别：为1时去掉重复数据  为2时根据年龄段进行模糊查询
        if ((!"".equals(params.get("ageStart")) && null != params.get("ageStart")) || (!"".equals(params.get("ageEnd")) && null != params.get("ageEnd"))) {
            params.put("isTrue", "2");
        }

        if ("0".equals(params.get("isTrue"))) {
            //员工和家属额度曲线图
            String result = claimPhoto(params);
            return result;
        }
        if (params.get("grpContNo") == null || "".equals(params.get("grpContNo"))) {
            Log.info("queryPolicyClaimList根据保单号查询该保单的理赔记录：团体保单号为必录项");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "团体保单号为必录项！");
            return JSON.toJSONString(resultMap);
        }
        // 处理查询条件
        params.putIfAbsent("ClaimDateS", "");
        params.putIfAbsent("ClaimDateE", "");
        params.putIfAbsent("ageStart", "");
        params.putIfAbsent("ageEnd", "");
        params.putIfAbsent("insuredType", "");
        params.putIfAbsent("sex", "");
        params.putIfAbsent("riskCode", "");
        params.putIfAbsent("insuredName", "");
        params.putIfAbsent("certiType", "");
        params.putIfAbsent("certiCode", "");

        RemoteDelegate rd = RemoteDelegate.getInstance();
        Map<String, String> myPrpsMap = myProps.getServiceInfo();
        String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<RequestInfo>\n" +
                "\t<HEAD>\n" +
                /* 交易流水号 */
                "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                /* 接口交易类型 */
                "\t\t<TransType>BF0003</TransType>\n" +
                /* 交易日期 */
                "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                /* 交易时间 */
                "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                "\t</HEAD>\n" +
                "\t<BODY>\n" +
                "\t\t<ClaimQuery>\n" +
                "\t\t\t<GrpContNo>" + params.get("grpContNo") + "</GrpContNo>\n" +
                "\t\t\t<ClaimDateS></ClaimDateS>\n" +
                "\t\t\t<ClaimDateE></ClaimDateE>\n" +
                "\t\t\t<ageStart>" + params.get("ageStart") + "</ageStart>\n" +
                "\t\t\t<ageEnd>" + params.get("ageEnd") + "</ageEnd>\n" +
                "\t\t\t<insuredType>" + params.get("insuredType") + "</insuredType>\n" +
                "\t\t\t<sex>" + params.get("sex") + "</sex>\n" +
                "\t\t\t<riskCode>" + params.get("riskCode") + "</riskCode>\n" +
                "\t\t\t<insuredName>" + params.get("insuredName") + "</insuredName>\n" +
                "\t\t\t<certiType>" + params.get("certiType") + "</certiType>\n" +
                "\t\t\t<certiCode>" + params.get("certiCode") + "</certiCode>\n" +
                "\t\t</ClaimQuery>\n" +
                "\t</BODY>\t\n" +
                "</RequestInfo>";
        Log.info("调用核心投保接口请求报文：" + reqXml);
        long startStamp = System.currentTimeMillis();
        boolean success = rd.submitData(myPrpsMap.get("serviceUrl"), myPrpsMap.get("operation"), "", reqXml); //BenefitService
        long endStamp = System.currentTimeMillis();
        Log.info("调用核心接口用时：" + (endStamp - startStamp));
        if (success) {
            Map<String, Object> responseXml = rd.getResult();
            List<ClaimInfo> claimInfo = (List<ClaimInfo>) responseXml.get("ClaimInfoList");
            List<ClaimInfo> claimInfoList = new ArrayList<>();
            for (ClaimInfo claiminfo : claimInfo) {
                ClaimInfo claimIn = new ClaimInfo();
                claimIn.setInsuredName(claiminfo.getInsuredName());
                claimIn.setCertiType(claiminfo.getCertiType());
                claimIn.setCertiCode(Base64AndMD5Util.base64SaltEncode(claiminfo.getCertiCode(), "130"));
                claimIn.setClaimType(claiminfo.getClaimType());
                claimIn.setClaimAmount(claiminfo.getClaimAmount());
                claimIn.setIsCheck(claiminfo.getIsCheck());
                claimIn.setStatus(claiminfo.getStatus());
                claimInfoList.add(claimIn);
            }
            PageHelperUtil<ClaimInfo> teamPageInfo = new PageHelperUtil<>(claimInfo);
            if (!"2".equals(params.get("isTrue"))) {
                HashSet hashSet = new HashSet(claimInfoList);
                claimInfoList.clear();
                claimInfoList.addAll(hashSet);
            }
            resultMap.put("data", teamPageInfo.getList());
            for (int i = 0; i < teamPageInfo.getList().size(); i++) {
                Map<String, Object> maps = new HashMap<>();
                ClaimInfo claimInfo1 = teamPageInfo.getList().get(i);
                Double realPayCount = 0.0;
                if ("50".equals(claimInfo1.getStatus()) || "60".equals(claimInfo1.getStatus())) {
                    Double realPay = Double.parseDouble(claimInfo1.getRealPay());
                    realPayCount += realPay;
                }
            }
            resultMap.put("claimInfoList", claimInfoList);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询保单的理赔记录成功");
            return JSON.toJSONString(resultMap);
        } else {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询保单的理赔记录失败，请联系管理员");
            return JSON.toJSONString(resultMap);
        }
    }

    public String claimPhoto(Map<String, Object> params) {
        //封装容器
        List<HashMap<String, Object>> mapList = new ArrayList<>();
        HashMap<String, Object> resultMap = new HashMap();
        Double num = 0.00;
        String age1 = "1";
        String age2 = "20";
        String age3 = "40";
        String age4 = "60";
        String age5 = "80";
        String age6 = "100";
        for (int j = 0; j < 2; j++) {
            //被保人类型0-员工，1-家属
            if ("".equals(params.get("insuredType")) || null == params.get("insuredType")) {
                if (j == 0) {
                    params.put("insuredTypes", "00");
                } else {
                    params.put("insuredTypes", "01");
                }
            } else if (!"".equals(params.get("insuredType")) && null != params.get("insuredType")) {
                params.put("insuredTypes", params.get("insuredType"));
                j++;
            }
            for (int i = 0; i < 5; i++) {
                params.remove("ageStart");
                params.remove("ageEnd");
                params.remove("isTrue");
                //1岁至20岁
                if (i == 0) {
                    params.put("ageStart", age1);
                    params.put("ageEnd", age2);
                    //获取1岁至20岁的额度
                    String getData = getData(params, mapList, age1, age2, num);
                    if (!"".equals(getData)) {
                        resultMap.put("message", getData);
                        return JSON.toJSONString(resultMap);
                    }

                }
                //20岁至40岁
                else if (i == 1) {
                    params.put("ageStart", age2);
                    params.put("ageEnd", age3);
                    String getData = getData(params, mapList, age2, age3, num);
                    if (!"".equals(getData)) {
                        continue;
                    }

                }
                //40岁至60岁
                else if (i == 2) {
                    params.put("ageStart", age3);
                    params.put("ageEnd", age4);
                    String getData = getData(params, mapList, age3, age4, num);
                    if (!"".equals(getData)) {
                        continue;
                    }

                }
                //60岁至80岁
                else if (i == 3) {
                    params.put("ageStart", age4);
                    params.put("ageEnd", age5);
                    String getData = getData(params, mapList, age4, age5, num);
                    if (!"".equals(getData)) {
                        continue;
                    }

                }
                //80岁至100岁
                else if (i == 4) {
                    params.put("ageStart", age5);
                    params.put("ageEnd", age6);
                    String getData = getData(params, mapList, age5, age6, num);
                    if (!"".equals(getData)) {
                        continue;
                    }

                }

            }
        }
        List<ClaimInfo> claimInfoList = new ArrayList<>();
        for (int i = 0; i < mapList.size(); i++) {
            claimInfoList.addAll((List<ClaimInfo>) mapList.get(i).get("claimInfoList"));
        }
        resultMap.put("claimInfoList", claimInfoList);
        resultMap.put("data", mapList);
        resultMap.put("code", "200");
        resultMap.put("message", "查询年龄额度曲线图成功");
        return JSON.toJSONString(resultMap, SerializerFeature.DisableCircularReferenceDetect);
    }

    public String getData(Map<String, Object> params, List<HashMap<String, Object>> mapList, String ageStart, String ageEnd, Double num) {
        //查询理赔记录
        Map<String, Object> map = claimCounmts(params);
        HashMap<String, Object> resultMap = new HashMap<>();
        String strMsg = "";
        if (map.get("data") != null) {
            //获取核心数据
            List<ClaimInfo> claimInfoList = (List<ClaimInfo>) map.get("data");
            for (ClaimInfo claimInfo : claimInfoList) {
                //实际金额
                String realPay = claimInfo.getRealPay();
                if (!"".equals(realPay) && ("50".equals(claimInfo.getStatus()) || "60".equals(claimInfo.getStatus()))) {
                    Double number = Double.parseDouble(realPay);
                    num = CommonUtil.add(num, number);
                }
            }
            resultMap.put("ageGroup", ageStart + "至" + ageEnd);
            resultMap.put("realPay", num);
            resultMap.put("insuredType", params.get("insuredTypes"));
            resultMap.put("claimInfoList", claimInfoList);
            mapList.add(resultMap);
        } else {
            strMsg = "核心返回数据为空";
        }
        return strMsg;
    }


    public Map<String, Object> claimCounmts(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        if (params.get("grpContNo") == null || "".equals(params.get("grpContNo"))) {
            Log.info("queryPolicyClaimList根据保单号查询该保单的理赔记录：团体保单号为必录项");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "团体保单号为必录项！");
            return resultMap;
        }
        RemoteDelegate rd = RemoteDelegate.getInstance();
        Map<String, String> myPrpsMap = myProps.getServiceInfo();
        String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<RequestInfo>\n" +
                "\t<HEAD>\n" +
                /* 交易流水号 */
                "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                /* 接口交易类型 */
                "\t\t<TransType>BF0003</TransType>\n" +
                /* 交易日期 */
                "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                /* 交易时间 */
                "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                "\t</HEAD>\n" +
                "\t<BODY>\n" +
                "\t\t<ClaimQuery>\n" +
                "\t\t\t<GrpContNo>" + params.get("grpContNo") + "</GrpContNo>\n" +
                "\t\t\t<ClaimDateS>" + (params.get("ClaimDateS") == null ? "" : params.get("ClaimDateS")) + "</ClaimDateS>\n" +
                "\t\t\t<ClaimDateE>" + (params.get("ClaimDateE") == null ? "" : params.get("ClaimDateE")) + "</ClaimDateE>\n" +
                "\t\t\t<ageStart>" + (params.get("ageStart") == null ? "" : params.get("ageStart")) + "</ageStart>\n" +
                "\t\t\t<ageEnd>" + (params.get("ageEnd") == null ? "" : params.get("ageEnd")) + "</ageEnd>\n" +
                "\t\t\t<insuredType>" + (params.get("insuredTypes") == null ? "" : params.get("insuredTypes")) + "</insuredType>\n" +
                "\t\t\t<sex>" + (params.get("sex") == null ? "" : params.get("sex")) + "</sex>\n" +
                "\t\t\t<riskCode>" + (params.get("riskCode") == null ? "" : params.get("riskCode")) + "</riskCode>\n" +
                "\t\t\t<insuredName>" + (params.get("insuredName") == null ? "" : params.get("insuredName")) + "</insuredName>\n" +
                "\t\t\t<certiType>" + (params.get("certiType") == null ? "" : params.get("certiType")) + "</certiType>\n" +
                "\t\t\t<certiCode>" + (params.get("certiCode") == null ? "" : params.get("certiCode")) + "</certiCode>\n" +
                "\t\t</ClaimQuery>\n" +
                "\t</BODY>\t\n" +
                "</RequestInfo>";
        Log.info("调用核心理赔清单查询接口请求报文：" + reqXml);
        long startStamp = System.currentTimeMillis();
        boolean success = rd.submitData(myPrpsMap.get("serviceUrl"), myPrpsMap.get("operation"), "", reqXml); //BenefitService
        long endStamp = System.currentTimeMillis();
        Log.info("调用核心接口用时：" + (endStamp - startStamp));
        if (success) {
            Map<String, Object> responseXml = rd.getResult();
            Log.info("调用核心理赔清单查询返回报文：" + JSON.toJSONString(responseXml));
            List<ClaimInfo> claimInfo = (List<ClaimInfo>) responseXml.get("ClaimInfoList");
            if (("".equals(params.get("insuredType")) || params.get("insuredType") == null) && "01".equals(params.get("insuredTypes"))) {
                reqXml = reqXml.replace("<insuredType>01</insuredType>", "<insuredType>02</insuredType>");
                boolean success1 = rd.submitData(myPrpsMap.get("serviceUrl"), myPrpsMap.get("operation"), "", reqXml);
                if (success1) {
                    Map<String, Object> responseXml1 = rd.getResult();
                    claimInfo.addAll((List<ClaimInfo>) responseXml1.get("ClaimInfoList"));
                }
                reqXml = reqXml.replace("<insuredType>02</insuredType>", "<insuredType>03</insuredType>");
                boolean success2 = rd.submitData(myPrpsMap.get("serviceUrl"), myPrpsMap.get("operation"), "", reqXml);
                if (success2) {
                    Map<String, Object> responseXml2 = rd.getResult();
                    claimInfo.addAll((List<ClaimInfo>) responseXml2.get("ClaimInfoList"));
                }
                reqXml = reqXml.replace("<insuredType>03</insuredType>", "<insuredType>06</insuredType>");
                boolean success3 = rd.submitData(myPrpsMap.get("serviceUrl"), myPrpsMap.get("operation"), "", reqXml);
                if (success3) {
                    Map<String, Object> responseXml3 = rd.getResult();
                    claimInfo.addAll((List<ClaimInfo>) responseXml3.get("ClaimInfoList"));
                }
            }
            if (claimInfo != null) {
                resultMap.put("data", claimInfo);
                resultMap.put("code", "200");
                resultMap.put("message", "理赔查询成功");
            } else {
                resultMap.put("message", "该保单没有理赔记录");
            }
            return resultMap;
        } else {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询保单的理赔记录失败，请联系管理员");
            return resultMap;
        }
    }
}
