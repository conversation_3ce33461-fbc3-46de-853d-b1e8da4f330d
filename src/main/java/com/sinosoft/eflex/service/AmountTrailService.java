package com.sinosoft.eflex.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.util.DateTimeUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sinosoft.eflex.dao.AmountTrailMapper;
import com.sinosoft.eflex.model.DailyAmountTrail;


/**
 * 
 * <AUTHOR>
 *
 */
@Service("AmountTrailService")
public class AmountTrailService {

    //日志
    private static Logger Log = LoggerFactory.getLogger(AmountTrailService.class);
    //引入公共资源
    @Autowired
    private PremTrailService premTrailService;
    @Autowired
    private AmountTrailMapper amountTrailMapper;


    /**
     * 年金产品试算保额
     * @param dailyAmountTrail
     * @return
     */
    public Map<String,Object> dailyAmountTrail(DailyAmountTrail dailyAmountTrail) {
        //定义公共返回结果
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        resultMap.put("message", "保额试算不支持此险种！");
        try {
            if(dailyAmountTrail.getRiskCode()==null || dailyAmountTrail.getRiskCode().equals("")){
                resultMap.put("message", "险种编码不能为空");
                return resultMap;
            }
            String riskCode = dailyAmountTrail.getRiskCode();
            switch (riskCode) {
                //横琴安颐无忧年金新产品
                case "14110": resultMap=amountTrial_14110(dailyAmountTrail);
                break;
            }
            String code = resultMap.get("code").toString();
            if("200".equals(code)){
                String amount = resultMap.get("Amount").toString();
                resultMap.put("Amount", amount.replaceAll(",", ""));
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保额测算异常！");
        }
        return resultMap;
    }


    public Map<String,Object> amountTrial_14110(DailyAmountTrail dailyAmountTrail) {
        Log.info("amountTrial_14110 :::: dailyAmountTrail:{}", JsonUtil.toJSON(dailyAmountTrail));
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("保险期间", dailyAmountTrail.getInsurePeriod());
        checkMap.put("缴费期间", dailyAmountTrail.getPayPeriod());
        //checkMap.put("计划编码", dailyAmountTrail.getPlanCode());
        checkMap.put("保费", dailyAmountTrail.getPrem());
        checkMap.put("性别", dailyAmountTrail.getGender());
        String errMsg = premTrailService.checkIsNull(checkMap);
        if(!errMsg.equals("")){
            resultMap.put("message", errMsg);
        }else{
            boolean checkInsureDate = premTrailService.checkDateFormat(dailyAmountTrail.getInsureDate());
            boolean checkBirthDay = premTrailService.checkDateFormat(dailyAmountTrail.getBirthDay());
            boolean checkSex = premTrailService.checkSexAndJoinMedProtectFormat(dailyAmountTrail.getGender());
            if(checkInsureDate && checkBirthDay && checkSex){
                // TODO 因为费率表中的计划编码planCode全部为空，所以现在先把计划编码设置为null add by wuShihao 2021-3-5
                dailyAmountTrail.setPlanCode("");
                int age = DateTimeUtil.getCurrentAge(dailyAmountTrail.getBirthDay(),
                        StringUtils.isEmpty(dailyAmountTrail.getInsureDate()) ? DateTimeUtil.getCurrentDate()
                                : dailyAmountTrail.getInsureDate());
                dailyAmountTrail.setInsureAge(age);
                List<String> amountTrial_14110List = amountTrailMapper.amountTrial_14110(dailyAmountTrail);
                if(amountTrial_14110List==null || amountTrial_14110List.size()<=0){
                    resultMap.put("message", "保额测算失败!");
                }else{
                    String amountTrail = amountTrial_14110List.get(0);
                    if(amountTrail==null || amountTrail.equals("")){
                        resultMap.put("message", "保额测算失败!");
                    }else{
                        resultMap.put("Amount", amountTrail);
                        resultMap.put("message", "保额测算成功!");
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                    }
                }
            }else{
                if(!checkInsureDate)errMsg+=errMsg.equals("")?"保额测算失败，投保日期格式错误":"，投保日期格式错误";
                if(!checkBirthDay)errMsg+=errMsg.equals("")?"保额测算失败，出生日期格式错误":"，出生日期格式错误";
                if(!checkSex)errMsg+=errMsg.equals("")?"保额测算失败，性别格式错误":"，性别格式错误";
                resultMap.put("message", errMsg);
            }
        }
        return resultMap;
    }

}