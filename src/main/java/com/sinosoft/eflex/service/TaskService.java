package com.sinosoft.eflex.service;

import com.alibaba.fastjson.JSON;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.CoreIdType;
import com.sinosoft.eflex.enums.GenderType;
import com.sinosoft.eflex.enums.PaymentTypeEnum;
import com.sinosoft.eflex.enums.SignInsureEnum;
import com.sinosoft.eflex.enums.status.PolicySignStateEnum;
import com.sinosoft.eflex.model.AddressEntity.EvaluationCustomer;
import com.sinosoft.eflex.model.AddressEntity.IdCardVerifyRequest;
import com.sinosoft.eflex.model.BatchInsureInterface.*;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.makeProposalForm.InsuredImpart;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;
import com.thoughtworks.xstream.io.xml.XmlFriendlyNameCoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @DESCRIPTION
 * @create 2018-08-30 10:15
 **/
@Service("TaskService")
@Slf4j
@RequiredArgsConstructor
public class TaskService {

    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FcPrtandCoreRelaMapper fcPrtandCoreRelaMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FDAgentInfoMapper fdAgentInfoMapper;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private FCOrderMapper fcOrderMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private FCOrderItemMapper fcOrderItemMapper;
    @Autowired
    private FCOrderItemDetailMapper fcOrderItemDetailMapper;
    @Autowired
    private FCPlanRiskMapper fcPlanRiskMapper;
    @Autowired
    private FCPlanRiskDutyMapper fcPlanRiskDutyMapper;
    @Autowired
    private FCEnsurePlanMapper fcEnsurePlanMapper;
    @Autowired
    private InsureService insureService;
    @Autowired
    private FDFTPInfoMapper fdftpInfoMapper;
    @Autowired
    private MyProps myProps;
    @Autowired
    private FPInsurePlanMapper fpInsurePlanMapper;
    @Autowired
    private FCDefaultPlanMapper fcDefaultPlanMapper;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private FCPerAppntMapper fcPerAppntMapper;
    @Autowired
    private FCPerRegistDayMapper fcPerRegistDayMapper;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FCOrderLocusMapper fcOrderLocusMapper;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FcEnsureContactMapper fcEnsureContactMapper;
    @Autowired
    private FCAppntImpartInfoMapper fcAppntImpartInfoMapper;
    @Autowired
    private SpanService spanService;
    @Autowired
    private PremTrailService premTrailService;
    @Autowired
    private FPInsureEflexPlanMapper fpInsureEflexPlanMapper;
    @Autowired
    private FPInsureEflexPlanOptionalMapper fpInsureEflexPlanOptionalMapper;
    @Autowired
    private FcInsureEflexPlanMapper fcInsureEflexPlanMapper;
    @Autowired
    private FcInsureEflexPlanOptionalMapper fcInsureEflexPlanOptionalMapper;
    @Autowired
    private FcPlanRiskInfoMapper fcPlanRiskInfoMapper;
    @Autowired
    private EnsureMakeService ensureMakeService;
    @Autowired
    private FcPerImpartResultMapper fcPerImpartResultMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private FCPersonImageMapper fcPersonImageMapper;
    @Autowired
    private AddressCheckService addressCheckService;
    @Autowired
    private FCPerInfoTempMapper fcPerInfoTempMapper;
    @Autowired
    private FCPerinfoFamilyTempMapper fcPerinfoFamilyTempMapper;

    private String localFilePath;


    public String dealGrpOrderList(String token, String ensureCode, String signType) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
//        localFilePath = FileUtil.getLocalPath("0105");
        localFilePath = "/Users/<USER>/work/eflex/2025/8/13/0105";
        // 获取管理机构
        String manageCom = "86";
        if (signType.equals(SignInsureEnum.ARTIFICIALINSURE.getCode())) {
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            manageCom = globalInput.getManageCom();
            if (StringUtils.isEmpty(manageCom)) {
                throw new SystemException("当前用户管理机构为空！");
            }
        }
        List<HashMap<String, Object>> grpOrderList = new ArrayList<>();
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCodeAndManageCom(ensureCode, manageCom);
        if (fcGrpOrder == null) {
            resultMap.put("message", "未查询到该福利相关团单，请确认后福利编码无误后再次签单。");
            return JSON.toJSONString(resultMap);
        }
        // 判断险种是否下架
        String riskStopSale = ensureMakeService.checkRiskStopSale(fcEnsure.getEnsureCode());
        if (!StringUtil.isEmpty(riskStopSale)) {
            resultMap.put("success", Boolean.FALSE);
            resultMap.put("code", "500");
            resultMap.put("message", riskStopSale);
            return JSON.toJSONString(resultMap);
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String newdate = dateFormat.format(new Date());
        int compareTo = fcEnsure.getEndAppntDate().compareTo(newdate);
        if (compareTo > 0) {
            resultMap.put("message", "开放期未结束，不可签单。");
            return JSON.toJSONString(resultMap);
        }
        /**
         * add by PolicyState = 5，承保失败仍可签单
         */
        if (!"1".equals(fcEnsure.getPolicyState()) && !"5".equals(fcEnsure.getPolicyState())) {
            resultMap.put("message", "当前福利已存在签单记录，不可再次签单。");
            return JSON.toJSONString(resultMap);
        }

        // 校验团单至少三人进行投保
        List<FCOrderInsured> fcOrderInsureds = fcOrderInsuredMapper.selectBasePerple(ensureCode);
        if (fcOrderInsureds.size() < 3) {
            resultMap.put("message", "团体订单中至少三人才可以进行签单！");
            return JSON.toJSONString(resultMap);
        }
        List<Map<String, Object>> insuredList = fcOrderInsuredMapper.getOrderItemCustomerInfoList(ensureCode);
        if (CollectionUtils.isNotEmpty(insuredList)) {
            List<EvaluationCustomer> customerList = new ArrayList<>();
            for (Map<String, Object> map : insuredList) {
                customerList.add(EvaluationCustomer.builder()
                        .name(map.get("name").toString())
                        .idType(CoreIdType.getNameByCoreId(map.get("iDType").toString()).name())
                        .idNo(map.get("iDNo").toString())
                        .gender(GenderType.getGenderByCoreId(map.get("sex").toString()).name())
                        .birthday(map.get("birthday").toString())
                        .businessNo(fcEnsure.getEnsureCode())
                        .nationality(map.get("nationality").toString()).build());
            }
            List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(customerList, IdCardVerifyRequest.Verify.class);
            //校验二要素
            String failVerifies = addressCheckService.checkIdCard(verifies);
            if (StringUtils.isNotEmpty(failVerifies)) {
                resultMap.put("message", failVerifies);
                return JSON.toJSONString(resultMap);
            }
        }


        if (grpOrderList == null || grpOrderList.size() < 1) {
            grpOrderList = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            map.put("PrtNo", fcGrpOrder.getPrtNo());
            map.put("GrpOrderNo", fcGrpOrder.getGrpOrderNo());
            grpOrderList.add((HashMap<String, Object>) map);
        }
        if (grpOrderList.size() > 0) {
            RemoteDelegate rd = RemoteDelegate.getInstance();
            Map<String, String> myPrpsMap = myProps.getServiceInfo();
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(),
                    fdftpInfo.getUsername(), fdftpInfo.getPassword());
            for (int i = 0; i < grpOrderList.size(); i++) {
                // 如果在本地生产文件成功则返回文件名
                Map<String, String> grpOrderMap = insureOnePolicy(String.valueOf(grpOrderList.get(i).get("GrpOrderNo")),
                        ensureCode);
                if (null != grpOrderMap) {
                    String fileName;
                    if ("1".equals(fcEnsure.getPlanType())){
                        fileName = grpOrderMap.get("tPartNo") + ".xml";
                    }else {
                        fileName = grpOrderMap.get("tPartNo") + ".json";
                    }
                    String ftpFilePath = FileUtil.getFtpPath("0105", "");
                    // 上传ftp服务器
                    boolean success = Boolean.FALSE;
                    if (sFtp.uploadFile(fdftpInfo.getFtprootpath() + ftpFilePath + fileName, localFilePath + fileName)) {
                        /**
                         * 记录承保（签单）的轨迹
                         */
                        FcPrtandCoreRela fcPrtandCoreRela = new FcPrtandCoreRela();
                        // 主键，流水号
                        String relaSn = maxNoService.createMaxNo("RelaSn", "", 20);
                        fcPrtandCoreRela.setRelaSn(relaSn);
                        // 投保单号
                        fcPrtandCoreRela.setPrtNo(grpOrderList.get(i).get("PrtNo").toString());
                        // 核心投保单号
                        fcPrtandCoreRela.settPrtNo(grpOrderMap.get("tPartNo"));
                        // 承保（签单）状态
                        fcPrtandCoreRela.setStatus(PolicySignStateEnum.SENTCODE.getCode());
                        fcPrtandCoreRela.setDescribe(PolicySignStateEnum.SENTCODE.getValue());
                        fcPrtandCoreRela.setMakeDate(DateTimeUtil.getCurrentDate());
                        fcPrtandCoreRela.setMakeTime(DateTimeUtil.getCurrentTime());
                        fcPrtandCoreRelaMapper.insertSelective(fcPrtandCoreRela);

                        if ("1".equals(fcEnsure.getPlanType())) {
                            // 弹性计划接口
                            String  transType = "BF0008";
                            // 调用核心接口 发送ftp地址
                            String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                                    "<RequestInfo>\n" +
                                    "\t<HEAD>\n" +
                                    "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                                    "\t\t<TransType>" + transType + "</TransType>\n" +
                                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                                    "\t</HEAD>\n" +
                                    "\t<body>\n" +
                                    "\t\t<FileInfo>\n" +
                                    "\t\t\t<filePath>" + ftpFilePath + "</filePath>\n" +
                                    "\t\t\t<fileName>" + fileName + "</fileName>\n" +
                                    "\t\t</FileInfo>\n" +
                                    "\t</body>\n" +
                                    "</RequestInfo>";
                            log.info("调用核心承保接口请求报文：" + reqXml);
                            /**
                             * 请求核心承保接口
                             */
                            long startStamp = System.currentTimeMillis();
                            success = rd.submitData(myPrpsMap.get("serviceUrl"), myPrpsMap.get("operation"), "",
                                    reqXml); // BenefitService
                            long endStamp = System.currentTimeMillis();
                            log.info("调用老核心核心承保接口用时：" + (endStamp - startStamp));

                        }else {
                            // 固定计划接口


                            String s = HttpUtil.postJsonFile(localFilePath +"/"+ fileName, myProps.getNationalityCheck(), myProps.getAppId(), myProps.getAppSecret());


                        }

                        if (success) {
                            // 修改注册期表
                            FCPerRegistDay fcPerRegistDay = new FCPerRegistDay();
                            fcPerRegistDay.setEnsureCode(ensureCode);
                            fcPerRegistDay.setIsValidy("0");
                            fcPerRegistDay = CommonUtil.initObject(fcPerRegistDay, "UPDATE");
                            fcPerRegistDayMapper.updateCloseDayByEnsureCode(fcPerRegistDay);
                            // 返回结果
                            log.info("调用核心承保接口返回报文: {}", JSON.toJSONString(rd.getResult()));
                            resultMap.put("relaSn", relaSn);
                            resultMap.put("success", true);
                            resultMap.put("code", "200");
                            resultMap.put("message", rd.getResult());
                            return JSON.toJSONString(resultMap);

                        } else {
                            // 更新承保（签单）的轨迹，01-承保数据已发送至核心，02-承保成功，03-承保失败, 04-投保成功（待承保）, 05-投保失败
                            spanService.updateFcPrtAndCoreRela(grpOrderList.get(i).get("PrtNo").toString(),
                                    PolicySignStateEnum.UNDERWRITEFAIL.getCode(), "发送失败!");
                            log.info("调用核心承保接口返回报文：失败 {}", JSON.toJSONString(rd.getResult()));
                            // 返回结果
                            resultMap.put("relaSn", relaSn);
                            resultMap.put("message", "承保失败！");
                            return JSON.toJSONString(resultMap);
                        }

                    }
                } else {
                    resultMap.put("message", "数据异常");
                    return JSON.toJSONString(resultMap);
                }
            }
        }
        return null;
    }

    /**
     * 签单完成之后前台调用接口查询承保结果
     * ps：之前写的接口的意思只要核心没有回调员福平台的接口，就每隔1.5S查询下后台数据库的签单记录信息 2022.3.8
     * 但是前后台接口请求等待时间不会超过1分钟。2022.3.8
     *
     * @param relaSn
     * @return
     */
    public String insureCallbackClient(String relaSn) {
        return getFcPrtAndCoreRelaByRelaSn(0, relaSn);
    }

    // 查询承保结果
    public String getFcPrtAndCoreRelaByRelaSn(int number, String relaSn, String describe) {
        Map<String, String> FcPrtAndCoreRelaInfo = fcGrpOrderMapper.getFcPrtAndCoreRelaByRelaSn(relaSn);
        // 承保成功、承保失败、投保失败返回前台结果 已发送、待承保证明核心还未调用承保反馈接口 系统延迟2S再次进行查询
        if ("02".equals(FcPrtAndCoreRelaInfo.get("Status")) || "03".equals(FcPrtAndCoreRelaInfo.get("Status")) || "05".equals(FcPrtAndCoreRelaInfo.get("Status"))) {
            describe = FcPrtAndCoreRelaInfo.get("Describe");
            if ("05".equals(FcPrtAndCoreRelaInfo.get("Status"))) {
                throw new SystemException(describe);
            }
        } else if (number <= 1) {
            number++;
            log.info(DateTimeUtil.getCurrentTime());
            try {
                Thread.sleep(1500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            log.info(DateTimeUtil.getCurrentTime());
            describe = FcPrtAndCoreRelaInfo.get("Describe");
            describe = getFcPrtAndCoreRelaByRelaSn(number, relaSn, describe);
        }
        return describe;
    }

    // 查询承保结果
    public String getFcPrtAndCoreRelaByRelaSn(Integer waitTime, String relaSn) {
        //查询签单记录信息
        FcPrtandCoreRela fcPrtandCoreRela = fcPrtandCoreRelaMapper.selectByPrimaryKey(relaSn);
        if (!fcPrtandCoreRela.getStatus().equals(PolicySignStateEnum.SENTCODE.getCode())) {
            //如果承保或者投保失败则会提示错误信息
            if (fcPrtandCoreRela.getStatus().equals(PolicySignStateEnum.INSUREFAIL.getCode()) || fcPrtandCoreRela.getStatus().equals(PolicySignStateEnum.UNDERWRITEFAIL.getCode())) {
                throw new SystemException(fcPrtandCoreRela.getDescribe());
            } else {
                return JSON.toJSONString(ResponseResultUtil.success(fcPrtandCoreRela.getDescribe()));
            }
        } else {
            //查询时长不可大于1分钟
            if (waitTime >= 1500) {
                return JSON.toJSONString(ResponseResultUtil.success("200", "核心处理中，稍后可在计划信息查询中查看处理结果。", null));
            } else {
                //过1.5S再进行查询
                try {
                    Thread.sleep(1500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                waitTime += 1500;
                log.info("第" + (waitTime / 1500) + "次查询...");
                return getFcPrtAndCoreRelaByRelaSn(waitTime, relaSn);
            }
        }
    }


    /**
     * 投保一个团体订单
     *
     * @param grpOrderNo
     * @return ComType
     */
    public Map<String, String> insureOnePolicy(String grpOrderNo, String ensureCode) {
        // 最终结果
        ContInfo contInfo = new ContInfo();
        try {
            /**************************************
             * 准备数据
             *************************************************/
            // 参数容器
            Map<String, Object> params = new HashMap<>();
            // 获取团体保单信息
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectByPrimaryKey(grpOrderNo);
            // 获取福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(fcGrpOrder.getEnsureCode());
            // 获取代理人信息
            List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
            FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);
            // 获取企业信息
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(fcGrpOrder.getGrpNo());
            // 保障配置表
            params.clear();
            params.put("ensureCode", fcGrpOrder.getEnsureCode());
            params.put("grpNo", fcGrpOrder.getGrpNo());
            List<Map<String, String>> ensureConfigMapList = fcEnsureConfigMapper.selectNoAndValue(params);
            Map<String, String> ensureConfigMap = new HashMap<>();
            for (Map<String, String> map : ensureConfigMapList) {
                ensureConfigMap.put(map.get("ConfigNo"), map.get("ConfigValue"));
            }
            // 是否是股东业务
            params.clear();
            params.put("grpNo", fcGrpOrder.getGrpNo());
            params.put("configNo", "007");
            String gudongContFlag = fcEnsureConfigMapper.selectOnlyValue(params);
            // 股东业务名称
            params.clear();
            params.put("grpNo", fcGrpOrder.getGrpNo());
            params.put("configNo", "009");
            String shareHolder = "";
            if (fcEnsureConfigMapper.selectOnlyValue(params) != null) {
                shareHolder = fcEnsureConfigMapper.selectOnlyValue(params);
            }
            params.clear();
            params.put("ensureCode", fcGrpOrder.getEnsureCode());
            params.put("grpNo", fcGrpOrder.getGrpNo());
            params.put("configNo", "006");
            String fcEnsureConfig_006 = fcEnsureConfigMapper.selectOnlyValue(params);
            if (fcEnsureConfig_006 == null) {
                fcEnsureConfig_006 = "";
            }
            // 同质风险加减人比例上限
            params.clear();
            params.put("ensureCode", fcGrpOrder.getEnsureCode());
            params.put("grpNo", fcGrpOrder.getGrpNo());
            params.put("configNo", "014");
            String fcEnsureConfig_014 = fcEnsureConfigMapper.selectOnlyValue(params);
            if (fcEnsureConfig_014 == null) {
                fcEnsureConfig_014 = "50";
            }
            // 是否续保
            params.clear();
            params.put("ensureCode", fcGrpOrder.getEnsureCode());
            params.put("grpNo", fcGrpOrder.getGrpNo());
            params.put("configNo", "013");
            String fcEnsureConfig_013 = fcEnsureConfigMapper.selectOnlyValue(params);
            // 是否为保全定期结算Y是，N否
            params.put("configNo", "015");
            String fcEnsureConfig_015 = fcEnsureConfigMapper.selectOnlyValue(params);
            // 保全定期结算周期
            params.put("configNo", "016");
            String fcEnsureConfig_016 = fcEnsureConfigMapper.selectOnlyValue(params);
            // 保全定期结算上限
            params.put("configNo", "017");
            String fcEnsureConfig_017 = fcEnsureConfigMapper.selectOnlyValue(params);
            // 是否按照续保单承保
            params.put("configNo", "022");
            String fcEnsureConfig_022 = fcEnsureConfigMapper.selectOnlyValue(params);
            // 企业支付方式
            params.put("configNo", "023");
            String fcEnsureConfig_023 = fcEnsureConfigMapper.selectOnlyValue(params);

            // 获取福利联系人
            params.clear();
            params.put("grpNo", fcGrpOrder.getGrpNo());
            params.put("contactType", "01");
            FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(fcEnsure.getEnsureCode());
            if (StringUtils.isBlank(fcEnsureContact.getIdType())) {
                fcEnsureContact.setIdType("0");
            }
            /************************************
             * 封装数据
             ******************************************/
            // 投保单号(核心)
            String prtNo = maxNoService.createMaxNo("PrtNo", "A511041", 8);
            contInfo.setPrtNo(prtNo);
            // 呈报件号 传空
            contInfo.setReportNo("");
            // 管理机构
            contInfo.setManageCom(fdAgentInfo.getManageCom());
            // 一级销售渠道
            contInfo.setSaleChnl(fdAgentInfo.getBranchType2());
            // 二级销售渠道
            contInfo.setSellType(fcEnsure.getChannel());
            // 三级销售渠道
            contInfo.setAgentType("245");
            //中介机构代码
            contInfo.setZJAgentComCode(fcEnsure.getIntermediaryOrganCode());
            //中介机构名称
            contInfo.setZJAgentComName(StringUtils.isNotBlank(fcEnsure.getIntermediaryOrganName()) ? fcEnsure.getIntermediaryOrganName().split("\\[")[0] : null);
            // 续保业务 核心不支持续保，统一传否 ？？
            contInfo.setRnewFlag(fcEnsureConfig_013);
            // 投保申请日期
            contInfo.setPolApplyDate(DateTimeUtil.getCurrentDate());
            // 保单生效日期
            contInfo.setCvalidate(fcEnsure.getCvaliDate());
            // 财务收费日期 传空
            contInfo.setPayDate("");
            // 保全定期结算 传N
            contInfo.setBalanceFlag(fcEnsureConfig_015);
            // 定期结算周期 传空
            contInfo.setBalanceZQ(fcEnsureConfig_016);
            // 定期结算金额上限 传空
            contInfo.setBalanceMoney(fcEnsureConfig_017);
            // 所属集团投保单号
            contInfo.setRelaPrtNo("");
            // 股东业务标志
            contInfo.setGudongContFlag(gudongContFlag);
            // 股东单位名称 1-珠海铧创 2--亨通集团 3--苏州环亚 4--明珠深投 5--中植集团
            contInfo.setShareHolder(shareHolder);
            // <!--团单类型 1-法人单 2-自然人单-->
            contInfo.setContTypeFlag("1");
            // 日期追溯备注
            // 团单保单打印类型 1-电子保单 2-电子保单+纸质保单，只要不是乐刻的企业就要就传二
            if (fcGrpInfo.getGrpNo().equals("00000000000000000201")) {
                contInfo.setContPrintType("1");
            } else {
                contInfo.setContPrintType("2");
            }
            //日期追溯备注
            contInfo.setBackDataRemark("");
            // 业务员代码
            contInfo.setAgentCode(fdAgentInfo.getAgentCode());
            // 业务员姓名
            contInfo.setAgentName(fdAgentInfo.getName());
            // 所属机构 同管理机构
            contInfo.setAgentManageCom(contInfo.getManageCom());
            // 所属分部 BranchCode代理人组别
            contInfo.setBranchAttr(fdAgentInfo.getBranchCode());
            //客户类别
            contInfo.setPersonType(fcGrpInfo.getGrpCategory());
            // 家庭单标志
            contInfo.setFamilySign("0");
            // 绿色标识字段
            contInfo.setGreenInsureFlag(fcEnsure.getGreenInsurance() ? "Y" : "N");
            //被续保保单编号
            contInfo.setReNewGrpContNo("1".equals(fcEnsureConfig_013) ? fcEnsure.getReNewGrpContNo() : null);
            /*------------------------------------------------------*/
            // CompanyInfo 投保单位信息
            CompanyInfo companyInfo = new CompanyInfo();
            //主营业务
            companyInfo.setMainBussiness(fcGrpInfo.getBusinesses());
            // 投保单位名称
            companyInfo.setGrpName(fcGrpInfo.getGrpName());
            // 证件类型 1-统一社会信用代码， update by wudezhong 支持多种企业证件类型 2021.8.23
            companyInfo.setComType(fcGrpInfo.getGrpIdType());
            // 证件号
            companyInfo.setComNo(fcGrpInfo.getGrpIdNo());
            //证件有效起期
            companyInfo.setBusliceStartDate(fcGrpInfo.getGrpTypeStartDate());
            // 证件有效止期
            companyInfo.setBusliceDate(fcGrpInfo.getGrpTypeEndDate());
            // 企业成立日期
            companyInfo.setFoundDate(fcGrpInfo.getGrpEstablishDate());
            // 企业规模类型
            companyInfo.setBusSizeType(fcGrpInfo.getGrpScaleType());
            // 参加社会统筹标志
            companyInfo.setJoinType(fcGrpInfo.getSociologyPlanSign());
            // 注册资本（元）
            companyInfo.setRgtMoney(fcGrpInfo.getRegisteredCapital());
            // 注册地址
            companyInfo.setRegAddress(fcGrpInfo.getRegaddress());
            // 企业电话号码
            companyInfo.setPhone(fcGrpInfo.getTelphone());
            // 投保单位性质  见码表1核心提供码表
            String grpNature1 = fdCodeMapper.selectOtherSign("GrpNature", fcGrpInfo.getGrpType());
            log.info("insureOnePolicy grpNature1:{}", grpNature1);
            companyInfo.setGrpNature1(grpNature1);
            companyInfo.setGrpNatureType(fcGrpInfo.getGrpNatureType());
            companyInfo.setGrpNature(fcGrpInfo.getGrpType());
            // 行业类别 所属行业
            companyInfo.setBusinessType(fcGrpInfo.getTrade());
            // 营业期限
            companyInfo.setPeriodOfValidity(fcGrpInfo.getBusinessTerm());
            // 单位员工总数
            companyInfo.setPeoples(fcGrpInfo.getPeoples() + "");
            // 投保人数
            companyInfo.setPeoples2(fcEnsure.getInsuredNumber() + "");
            // 单位法人代表
            companyInfo.setCorporation(fcGrpInfo.getCorporationMan());
            companyInfo.setCorporationIDNo(fcGrpInfo.getLegID());
            companyInfo.setCorporationIDType(fcGrpInfo.getLegIDType());
            companyInfo.setCorporationIDStartPeriod(fcGrpInfo.getLegIDStartDate());
            companyInfo.setCorporationIDPeriodOfValidity(fcGrpInfo.getLegIDEndDate());
            companyInfo.setCorporationGender(fcGrpInfo.getLegSex());
            companyInfo.setCorporationBirthday(fcGrpInfo.getLegBirthday());
            companyInfo.setCorporationNationality(fcGrpInfo.getLegNationality());

            // 同质风险减人百分比
            companyInfo.setNZProportion(fcEnsureConfig_014);
            // 单位地址
            companyInfo.setGrpAddress(fcGrpInfo.getGrpAddRess());
            //注册地
            companyInfo.setRegestedPlace(fcGrpInfo.getGrpRegisterAddress());
            // 邮政编码
            companyInfo.setGrpZipCode(fcGrpInfo.getZipCode());
            // 是否按照续保单投保 add by wudezhong 新增字段
            if (StringUtils.isNotBlank(fcEnsureConfig_022)) {
                switch (fcEnsureConfig_022) {
                    case "1":
                        companyInfo.setRnewContFlag("Y");
                        break;
                    case "0":
                        companyInfo.setRnewContFlag("N");
                        break;
                    default:
                        companyInfo.setRnewContFlag("");
                        break;
                }
            }
            // 付款方式 1-现金 update by wudezhong 支持多种企业付款方式 2021.8.23
            companyInfo.setGetFlag(fcEnsureConfig_023);
            if ("1".equals(fcEnsure.getPlanType())){
                companyInfo.setGetFlag("D");
            }
            // 备注
            companyInfo.setRemark("");
            // 特殊险种类别 0-其他
            companyInfo.setRiskflag("0");
            // 特别约定
            companyInfo.setSpecContent(fcEnsureConfig_006);
            // 操作员
            companyInfo.setOperator("uflex");
            // 录入时间
            companyInfo.setMakeDate(DateTimeUtil.getCurrentDateTime());
            /********************** 联系人 *************************************/
            Contacts contacts = new Contacts();
            // 姓名
            contacts.setLinkMan(fcEnsureContact.getName());
            // 证件类型
            contacts.setInsContIDType(fcEnsureContact.getIdType());
            // 证件号码
            contacts.setInsContIDNo(fcEnsureContact.getIdNo());
            // 证件有效期
            contacts.setInsContIDPeriodOfValidityType(fcEnsureContact.getIdTypeEndDate());
            contacts.setInsContIDStartPeriod(fcEnsureContact.getIdTypeStartDate());
            // E-MAIL
            contacts.setE_Mail(fcEnsureContact.getEmail());
            // 固定电话
            contacts.setMobile(fcEnsureContact.getMobilePhone());
            // 所属部门
            contacts.setDepartment(fcEnsureContact.getDepartment());
            // 国籍
            contacts.setNationality1(fcEnsureContact.getNativeplace());
            // 性别
            contacts.setGender1(fcEnsureContact.getSex());
            // 出生日期
            contacts.setBirthday1(fcEnsureContact.getBirthDay());
            companyInfo.setContacts(contacts);
            // ************* 投保人告知 团体告知
            Map<String, String> param = new HashMap<>();
            param.put("ensureCode", fcGrpOrder.getEnsureCode());
            List<AppntImpartInfo> AppntImpartList = fcAppntImpartInfoMapper.selectAppntImpartList(param);
            companyInfo.setAppntImpartList(AppntImpartList);
            // 投保企业信息
            contInfo.setCompanyInfo(companyInfo);
            // 被保人清单
            List<InsuredInfo> insuredInfoList = new ArrayList<>();
            // 获取以员工为单位的子订单（家庭）
            params.clear();
            params.put("grpOrderNo", fcGrpOrder.getGrpOrderNo());
            // 查询出来团体的订单号
            List<FCOrder> fcOrderList = fcOrderMapper.selectList(params);
            for (FCOrder fcOrder : fcOrderList) {
                log.info("签单fcOrder {}", fcOrder.getOrderNo());
                // 1-企业全缴 2-企业代缴 3-混合缴费
                params.put("ensureCode", fcGrpOrder.getEnsureCode());
                params.put("configNo", "008");
                String payMethod = fcEnsureConfigMapper.selectOnlyValue(params);
                // 获取每个家庭的被保人集合
                params.clear();
                params.put("orderNo", fcOrder.getOrderNo());
                // 获取监护人信息
                FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(fcOrder.getPerNo());
                // 获取监护人银行信息
                Map<String, String> signBankInfoMap = fcPerInfoMapper.getSignBankInfo(fcOrder.getPerNo(), ensureCode);
                // 判断是否是固定计划-个人批扣的方式
                if ("0".equals(fcEnsure.getPlanType()) && payMethod.equals("3") && signBankInfoMap == null) {
                    signBankInfoMap = new HashMap<>();
                    signBankInfoMap.put("Name", fcPerInfo.getName());
                    signBankInfoMap.put("PayBankCode", fcPerInfo.getOpenBank());
                    signBankInfoMap.put("BankAccount", fcPerInfo.getOpenAccount());
                }
                log.info("signBankInfoMap:{}", JsonUtil.toJSON(signBankInfoMap));
                List<FCOrderItem> fcOrderItemList = fcOrderItemMapper.selectList(params);
                //投保影像子订单编号(没个被保人人下都要传影像但只有本人子订单号下有影响) imageType 08092 影像阿里云内网地址
                FCPersonImage fcPersonImage = fcPersonImageMapper.selectByOrderItemNoAndImageType(fcOrderItemList.get(0).getOrderItemNo(), "08092");
                for (FCOrderItem fcOrderItem : fcOrderItemList) {
                    // 查询 子订单产品要素详情表 获取 计划编码
                    params.clear();
                    params.put("orderItemDetailNo", fcOrderItem.getOrderItemDetailNo());
                    List<FCOrderItemDetail> fcOrderItemDetailList = fcOrderItemDetailMapper.selectList(params);
                    // 查询被保人信息
                    FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(fcOrderItem.getOrderItemNo());
                    log.info("查询被保人信息fcOrderInsured :: {}", JSON.toJSONString(fcOrderInsured));
                    InsuredInfo insured = new InsuredInfo();
                    Map<String, String> map = new HashMap<>();
                    map.put("perNo", fcOrder.getPerNo());
                    map.put("personID", fcOrderInsured.getPersonID());
                    log.info("查询被保人关系 ::{}", JSON.toJSONString(map));
                    FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(map);
                    String staffPersonId = fcDefaultPlanMapper.getEmployPersonid(fcOrder.getPerNo());
                    if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                        if (staffPersonId == null || "".equals(staffPersonId)) {
                            log.info("fcOrderItem.getOrderItemDetailNo()==" + fcOrderItem.getOrderItemDetailNo() + "的主被保人客户号为空。");
                            return null;
                        }
                    } else if ("1".equals(fcEnsure.getEnsureType())) {
                        staffPersonId = fcOrderInsured.getPersonID();
                    }
                    Integer countUser = fdUserMapper.selectCountByIdNo(fcOrderInsured.getIDNo());
                    FCPerson fcPeople = fcPersonMapper.selectFcPersonByPersonId(fcOrderInsured.getPersonID());
                    if (countUser > 0) {
                        insured.setRelationToAppnt(StringUtils.isNotBlank(fcPeople.getRelationship()) ? fcPeople.getRelationship() : "13");
                    } else {
                        insured.setRelationToAppnt("06");
                    }
                    insured.setInsuredNo(fcOrderInsured.getPersonID());
                    insured.setName(fcOrderInsured.getName());
                    insured.setSex(fcOrderInsured.getSex());
                    insured.setBirthday(fcOrderInsured.getBirthDay());
                    insured.setIDType(fcOrderInsured.getIDType());
                    insured.setIDNo(fcOrderInsured.getIDNo());
                    insured.setNativePlace(fcOrderInsured.getNativeplace());
                    insured.setContNo(fcOrderItem.getContNo());
                    insured.setMainInsuredNo(staffPersonId);
                    FDCodeKey key = new FDCodeKey();
                    key.setCodeType("Relation");
                    key.setCodeKey(fcStaffFamilyRela.getRelation());
                    FDCode fdCode = fdCodeMapper.selectByPrimaryKey(key);
                    if (fdCode != null) {
                        //投保类型为企业投保时取对应核心关系码值  学生投保默认本人 00
                        if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                            insured.setMainRelation(fdCode.getCoreCode());
                        } else if ("1".equals(fcEnsure.getEnsureType())) {
                            insured.setMainRelation("00");
                        }
                    } else {
                        log.info("与员工关系码表映射异常，CodeType：Relation，CodeKey:" + fcStaffFamilyRela.getRelation());
                        return null;
                    }
                    //工作证号
                    insured.setWorkNo("");
                    insured.setOccupationCode(fcOrderInsured.getOccupationCode());
                    insured.setOccupationType(fcOrderInsured.getOccupationType());
                    //入司日期
                    insured.setJoinCompanyDate("");
                    //工资
                    insured.setSalary("");
                    insured.setMobile(fcOrderInsured.getMobilePhone());
                    insured.setInsuredPeoples("1");
                    // 默认否 险种计算保费涉及社保标志时必传 0-无 1-有
                    if (null == fcOrderInsured.getJoinMedProtect() || "".equals(fcOrderInsured.getJoinMedProtect())) {
                        insured.setSocialInsuFlag("0");
                    } else {
                        if ("N".equals(fcOrderInsured.getJoinMedProtect())) {
                            fcOrderInsured.setJoinMedProtect("0");
                        } else if ("Y".equals(fcOrderInsured.getJoinMedProtect())) {
                            fcOrderInsured.setJoinMedProtect("1");
                        }
                        insured.setSocialInsuFlag(fcOrderInsured.getJoinMedProtect());
                    }
                    // 非弹性计划时 传当前计划编码
                    String planCode = "";
                    if (!"1".equals(fcEnsure.getPlanType())) {
                        planCode = fcOrderItemDetailList.get(0).getProductCode();
                        insured.setContPlanCode(planCode);
                    } else {
                        // TODO 弹性福利订单保险计划传值逻辑不明 默认1 (产品要求定义)
                        insured.setContPlanCode("1");
                    }
                    // 1-企业全缴 2-企业代缴 3-混合缴费
                    insured.setGrpPayMode(payMethod);
                    if (signBankInfoMap == null) {
                        insured.setGrpBankCode("");
                        insured.setGrpBankAccName("");
                        insured.setGrpBankAccNo("");
                    } else {
                        insured.setGrpBankCode(signBankInfoMap.get("PayBankCode"));
                        insured.setGrpBankAccName(signBankInfoMap.get("Name"));
                        insured.setGrpBankAccNo(signBankInfoMap.get("BankAccount"));
                    }
                    double companyPrem = fcOrderItem.getGrpPrem();
                    List<RiskInfo> riskInfoList = new ArrayList<>();
                    // 弹性计划
                    if ("1".equals(fcEnsure.getPlanType())) {
                        log.info(insured.getName());
                        // 获取所投险种的保费信息
                        Map<String, Object> resultMap = insureService.efleSplitPremium(fcOrderItem.getOrderItemDetailNo(), companyPrem);
                        // 应缴总保费
                        double tatalPrem = Double.parseDouble(resultMap.get("totalPrem").toString());
                        // 获取拆分后的 公司缴费 和 个人缴费
                        Map<String, Map<String, Map<String, Double>>> splitPremium = (Map<String, Map<String, Map<String, Double>>>) resultMap.get("riskMap");
                        // 所投险种集合
                        List<String> riskCodeList = fcInsureEflexPlanMapper.getRiskCodeByOrderItemDetailNo(fcOrderItem.getOrderItemDetailNo());
                        // 拆分后的保额获取
                        Map<String, Map<String, Map<String, Double>>> splitAmnt = insureService.efleSplitAmnt(fcOrderItem.getOrderItemDetailNo(), companyPrem, tatalPrem, riskCodeList);
                        for (String riskCode : riskCodeList) {
                            FcPlanRiskInfo fcPlanRiskInfos = fcPlanRiskInfoMapper.getFeeRatioByEnsureCodeAndRiskCodeByInsureSign(ensureCode, riskCode);
                            for (int i = 1; i <= 2; i++) {
                                // i = 1 公司缴费 i = 2 个人缴费
                                RiskInfo riskInfo1 = new RiskInfo();
                                riskInfo1.setRiskCode(riskCode);
                                riskInfo1.setMainRiskCode(riskCode);
                                riskInfo1.setPayIntv("0");
                                riskInfo1.setYears("1");
                                riskInfo1.setInsuYear("1");
                                riskInfo1.setInsuYearFlag("Y");
                                riskInfo1.setPayYears("1");
                                riskInfo1.setPayEndYear("1");
                                riskInfo1.setPayEndYearFlag("Y");
                                riskInfo1.setGetIntv("0");
                                riskInfo1.setBonusGetMode("");
                                // 分保标记
                                riskInfo1.setDistriFlag(fcPlanRiskInfos.getReinsuranceMark());
                                // 手续费比率
                                riskInfo1.setChargeFeeRate(fcPlanRiskInfos.getFeeRatio());
                                // 佣金奖励津贴
                                riskInfo1.setCommRate(fcPlanRiskInfos.getCommissionOrAllowanceRatio());
                                riskInfo1.setMult("1");
                                // i = 1 公司缴费 i = 2 个人缴费
                                riskInfo1.setPaySource(i + "");
                                // 责任层数据准备开始
                                log.info("" + splitPremium.get(riskInfo1.getRiskCode()));
                                params.clear();
                                params.put("riskCode", riskCode);
                                params.put("orderItemDetailNo", fcOrderItem.getOrderItemDetailNo());
                                List<Map<String, String>> riskDutyAmntList = fcInsureEflexPlanMapper.getDutyAmntByRiskCode(params);
                                double personPrem = splitPremium.get(riskCode).get(riskDutyAmntList.get(0).get("dutyCode")).get(i + "");
                                log.info("" + riskInfo1.getRiskCode() + "的保费为 " + i + "===" + personPrem);
                                if (i == 2 && personPrem == 0) {
                                    insured.setGrpPayMode("1");
                                    continue;
                                }
                                double riskAmnt = 0.0;
                                double riskPrem = 0.0;
                                List<DutyInfo> dutyInfoList = new ArrayList<>();
                                String floatRate = "";
                                for (Map<String, String> riskDutyInfoMap : riskDutyAmntList) {
                                    log.info(riskCode + insured.getName());
                                    double dutyPrem = splitPremium.get(riskCode).get(riskDutyInfoMap.get("dutyCode")).get(String.valueOf(i));
                                    double dutyAmnt = splitAmnt.get(riskCode).get(riskDutyInfoMap.get("dutyCode")).get(String.valueOf(i));
                                    if (!"".equals(riskDutyInfoMap.get("discountRatio")) && riskDutyInfoMap.get("discountRatio") != null) {
                                        floatRate = String.valueOf(Double.parseDouble(riskDutyInfoMap.get("discountRatio")) / 100);
                                    }
                                    if (!"17050".equals(riskCode)) {
                                        DutyInfo dutyInfo = new DutyInfo();
                                        dutyInfo.setDutyCode(riskDutyInfoMap.get("dutyCode"));
                                        dutyInfo.setAmnt(String.valueOf(dutyAmnt));
                                        dutyInfo.setPrem(String.valueOf(dutyPrem));
                                        riskAmnt = CommonUtil.add(riskAmnt, dutyAmnt);
                                        riskPrem = CommonUtil.add(riskPrem, dutyPrem);
                                        dutyInfo.setGetLimit(("".equals(riskDutyInfoMap.get("deductible"))
                                                || riskDutyInfoMap.get("deductible") == null) ? 0.0
                                                : Double.parseDouble(String.valueOf(riskDutyInfoMap.get("deductible"))));
                                        dutyInfo.setGetLimitType(riskDutyInfoMap.get("deductibleAttr") == null ? ""
                                                : riskDutyInfoMap.get("deductibleAttr"));
                                        dutyInfo.setGetRate(("".equals(riskDutyInfoMap.get("compensationRatio"))
                                                || riskDutyInfoMap.get("compensationRatio") == null)
                                                ? null
                                                : Double.parseDouble(String.valueOf(riskDutyInfoMap.get("compensationRatio")))
                                                / 100);
                                        // 最大赔付天数
                                        if (!ObjectUtils.isEmpty(riskDutyInfoMap.get("maxGetDay"))) {
                                            BigDecimal maxGetDay = new BigDecimal(
                                                    String.valueOf(riskDutyInfoMap.get("maxGetDay")));
                                            dutyInfo.setMaxPayDay(String.valueOf(maxGetDay.intValue()));
                                        }
                                        // 3-约定保额保费
                                        dutyInfo.setCalRule("3");
                                        dutyInfo.setFloatRate(floatRate);
                                        dutyInfoList.add(dutyInfo);
                                    } else {
                                        // 特殊处理17050险种 平台落库责任一条 需要将其拆成两条 保额/2
                                        riskAmnt = CommonUtil.add(riskAmnt, dutyAmnt);
                                        riskPrem = CommonUtil.add(riskPrem, dutyPrem);
                                        for (int j = 1; j <= 2; j++) {
                                            DutyInfo dutyInfo = new DutyInfo();
                                            dutyInfo.setAmnt(String.valueOf(CommonUtil.mul(dutyAmnt, 0.5)));
                                            dutyInfo.setCalRule("3");
                                            dutyInfo.setFloatRate(floatRate);
                                            dutyInfo.setGetRate(("".equals(riskDutyInfoMap.get("compensationRatio"))
                                                    || riskDutyInfoMap.get("compensationRatio") == null)
                                                    ? null
                                                    : Double.parseDouble(String.valueOf(riskDutyInfoMap.get("compensationRatio")))
                                                    / 100);
                                            if (j == 1) {
                                                dutyInfo.setDutyCode("GD0070");
                                                dutyInfo.setPrem(String.valueOf(dutyPrem));
                                                dutyInfo.setGetLimit(("".equals(riskDutyInfoMap.get("deductible"))
                                                        || riskDutyInfoMap.get("deductible") == null) ? 0.0
                                                        : Double.parseDouble(String.valueOf(riskDutyInfoMap.get("deductible"))));
                                                dutyInfo.setGetLimitType(
                                                        riskDutyInfoMap.get("deductibleAttr") == null ? ""
                                                                : riskDutyInfoMap.get("deductibleAttr"));
                                            } else if (j == 2) {
                                                dutyInfo.setDutyCode("GD0071");
                                                dutyInfo.setPrem("0.0");
                                                dutyInfo.setGetLimit(0.0);
                                                dutyInfo.setGetLimitType("");
                                            }
                                            dutyInfoList.add(dutyInfo);
                                        }
                                    }
                                }
                                riskInfo1.setPrem(String.valueOf(riskPrem));
                                riskInfo1.setAmnt(String.valueOf(riskAmnt));
                                riskInfo1.setDutyList(dutyInfoList);
                                riskInfoList.add(riskInfo1);
                            }
                        }
                        // 固定计划
                    } else if (!"1".equals(fcEnsure.getPlanType())) {
                        // 获取拆分后的 公司缴费 和 个人缴费
                        Map<String, Map<String, Map<String, Double>>> splitPremium = insureService.splitPremium(ensureCode, planCode, companyPrem);
                        // 拆分后的保额获取
                        Map<String, Map<String, Map<String, Double>>> splitAmnt = insureService.splitAmnt(ensureCode, planCode, companyPrem);
                        // 根据计划编码获取险种信息
                        List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectPlanDetailList(ensureCode, planCode);
                        for (FCPlanRisk fcPlanRisk : fcPlanRiskList) {
                            for (int i = 1; i <= 2; i++) {
                                // i = 1 公司缴费 i = 2 个人缴费
                                RiskInfo riskInfo1 = new RiskInfo();
                                riskInfo1.setRiskCode(fcPlanRisk.getRiskCode());
                                riskInfo1.setMainRiskCode(fcPlanRisk.getRiskCode());
                                riskInfo1.setPayIntv("0");
                                riskInfo1.setYears("1");
                                // 判断福利是一年期的还是极短期的
                                String policyEndDate = fcEnsure.getPolicyEndDate();
                                String cvaliDate = fcEnsure.getCvaliDate();
                                try {
                                    Long days = DateTimeUtil.getDistanceDays(policyEndDate, cvaliDate);
                                    if (days == 364 || days == 365) {
                                        riskInfo1.setInsuYear("1");
                                        riskInfo1.setInsuYearFlag("Y");
                                    } else {
                                        long daysOne = DateTimeUtil.getDistanceDays(fcEnsure.getCvaliDate(), fcEnsure.getPolicyEndDate());
                                        String daysTwo = String.valueOf(daysOne + 1);
                                        riskInfo1.setInsuYear(daysTwo);
                                        riskInfo1.setInsuYearFlag("D");
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                riskInfo1.setPayYears("1");
                                riskInfo1.setPayEndYear("1");
                                riskInfo1.setPayEndYearFlag("Y");
                                riskInfo1.setGetIntv("0");
                                riskInfo1.setBonusGetMode("");
                                riskInfo1.setDistriFlag(fcPlanRisk.getReinsuranceMark());
                                riskInfo1.setChargeFeeRate(fcPlanRisk.getFeeRatio());
                                riskInfo1.setCommRate(fcPlanRisk.getCommissionOrAllowanceRatio());
                                riskInfo1.setComplimentaryFlag(fcPlanRisk.getGiftInsureSign());
                                riskInfo1.setOldPrem(fcPlanRisk.getOriginalPrem() == null ? null : fcPlanRisk.getOriginalPrem().toString());
                                riskInfo1.setMult("1");
                                // i = 1 公司缴费 i = 2 个人缴费
                                riskInfo1.setPaySource(i + "");

                                // 责任层数据准备开始
                                log.info("" + splitPremium.get(riskInfo1.getRiskCode()));
                                params.clear();
                                params.put("ensureCode", ensureCode);
                                params.put("planCode", planCode);
                                params.put("riskCode", riskInfo1.getRiskCode());
                                List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyList(params);
                                double personPrem = splitPremium.get(fcPlanRisk.getRiskCode()).get(fcPlanRiskDutyList.get(0).getDutyCode()).get(i + "");
                                log.info("" + riskInfo1.getRiskCode() + "的保费为 " + i + "===" + personPrem);
                                if (i == 2 && personPrem == 0) {
                                    insured.setGrpPayMode("1");
                                    continue;
                                }
                                double riskAmnt = 0.0;
                                double riskPrem = 0.0;
                                //用于记录代扣代缴企业缴费金额
                                double GrpAmnt = 0.0;
                                double GrpPrem = 0.0;
                                List<DutyInfo> dutyInfoList = new ArrayList<>();
                                //企业代扣代缴福利type=2
                                if ("2".equals(fcEnsure.getPayType())) {
                                    //企业代扣代缴全部算为企业缴费
                                    if (i == 1) {
                                        //企业
                                        for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                                            double grpPrem = splitPremium.get(fcPlanRisk.getRiskCode()).get(fcPlanRiskDuty.getDutyCode()).get("1");
                                            double dutyPrem = splitPremium.get(fcPlanRisk.getRiskCode()).get(fcPlanRiskDuty.getDutyCode()).get("2");
                                            double grpAmnt = splitAmnt.get(fcPlanRisk.getRiskCode()).get(fcPlanRiskDuty.getDutyCode()).get("1");
                                            double dutyAmnt = splitAmnt.get(fcPlanRisk.getRiskCode()).get(fcPlanRiskDuty.getDutyCode()).get("2");

                                            //计算险种下的总保费保额
                                            Double dutyInfoPrem = CommonUtil.add(grpPrem, dutyPrem);
                                            Double dutyInfoAmt = CommonUtil.add(grpAmnt, dutyAmnt);
                                            //计算险种下的总保费保额
                                            GrpAmnt = CommonUtil.add(GrpAmnt, dutyInfoAmt);
                                            GrpPrem = CommonUtil.add(GrpPrem, dutyInfoPrem);

                                            DutyInfo dutyInfo = new DutyInfo();
                                            dutyInfo.setDutyCode(fcPlanRiskDuty.getDutyCode());
                                            dutyInfo.setAmnt(String.valueOf(dutyInfoAmt));
                                            dutyInfo.setPrem(String.valueOf(dutyInfoPrem));
                                            dutyInfo.setGetLimit(fcPlanRiskDuty.getGetLimit());
                                            dutyInfo.setGetLimitType(fcPlanRiskDuty.getGetLimitType() == null ? ""
                                                    : fcPlanRiskDuty.getGetLimitType());
                                            dutyInfo.setGetRate(fcPlanRiskDuty.getGetRatio());
                                            if (fcPlanRiskDuty.getMaxGetDay() != null) {
                                                dutyInfo.setMaxPayDay(String.valueOf(fcPlanRiskDuty.getMaxGetDay().intValue()));
                                            }
                                            // 3-约定保额保费
                                            dutyInfo.setCalRule("3");
                                            dutyInfoList.add(dutyInfo);
                                        }
                                        riskInfo1.setPrem(String.valueOf(GrpPrem));
                                        riskInfo1.setAmnt(String.valueOf(GrpAmnt));
                                    } else {
                                        //个人
                                        for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                                            DutyInfo dutyInfo = new DutyInfo();
                                            dutyInfo.setDutyCode(fcPlanRiskDuty.getDutyCode());
                                            dutyInfo.setAmnt(String.valueOf(0.0));
                                            dutyInfo.setPrem(String.valueOf(0.0));
                                            dutyInfo.setGetLimit(fcPlanRiskDuty.getGetLimit());
                                            dutyInfo.setGetLimitType(fcPlanRiskDuty.getGetLimitType() == null ? ""
                                                    : fcPlanRiskDuty.getGetLimitType());
                                            dutyInfo.setGetRate(fcPlanRiskDuty.getGetRatio());
                                            if (fcPlanRiskDuty.getMaxGetDay() != null) {
                                                dutyInfo.setMaxPayDay(String.valueOf(fcPlanRiskDuty.getMaxGetDay().intValue()));
                                            }
                                            // 3-约定保额保费
                                            dutyInfo.setCalRule("3");
                                            dutyInfoList.add(dutyInfo);
                                        }
                                        riskInfo1.setPrem(String.valueOf(0.0));
                                        riskInfo1.setAmnt(String.valueOf(0.0));
                                    }

                                } else {
                                    for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                                        double dutyPrem = splitPremium.get(fcPlanRisk.getRiskCode()).get(fcPlanRiskDuty.getDutyCode()).get(String.valueOf(i));
                                        double dutyAmnt = splitAmnt.get(fcPlanRisk.getRiskCode()).get(fcPlanRiskDuty.getDutyCode()).get(String.valueOf(i));
                                        DutyInfo dutyInfo = new DutyInfo();
                                        dutyInfo.setDutyCode(fcPlanRiskDuty.getDutyCode());
                                        dutyInfo.setAmnt(String.valueOf(dutyAmnt));
                                        dutyInfo.setPrem(String.valueOf(dutyPrem));
                                        riskAmnt = CommonUtil.add(riskAmnt, dutyAmnt);
                                        riskPrem = CommonUtil.add(riskPrem, dutyPrem);
                                        dutyInfo.setGetLimit(fcPlanRiskDuty.getGetLimit());
                                        dutyInfo.setGetLimitType(fcPlanRiskDuty.getGetLimitType() == null ? ""
                                                : fcPlanRiskDuty.getGetLimitType());
                                        dutyInfo.setGetRate(fcPlanRiskDuty.getGetRatio());
                                        if (fcPlanRiskDuty.getMaxGetDay() != null) {
                                            dutyInfo.setMaxPayDay(String.valueOf(fcPlanRiskDuty.getMaxGetDay().intValue()));
                                        }
                                        // 3-约定保额保费
                                        dutyInfo.setCalRule("3");
                                        dutyInfoList.add(dutyInfo);
                                    }
                                    riskInfo1.setPrem(String.valueOf(riskPrem));
                                    riskInfo1.setAmnt(String.valueOf(riskAmnt));

                                }
                                riskInfo1.setDutyList(dutyInfoList);
                                riskInfoList.add(riskInfo1);
                            }
                        }
                    }
                    insured.setRiskList(riskInfoList);
                    /**
                     * 个人健康告知信息
                     */
                    List<InsuredImpart> insuredImpartList = fcPerImpartResultMapper.selectByOrderItemNo(fcOrderItem.getOrderItemNo());
                    if (insuredImpartList.size() > 0) {
                        insured.setInsuredImpartList(insuredImpartList);
                    }
                    /**
                     * 电子投保影响节点
                     */
                    if (null != fcPersonImage) {
                        Page page = new Page();
                        List<Page> pageList = new ArrayList<>();
                        List<ESView> esViewList = new ArrayList<>();
                        ESView esView = new ESView();
                        String path = fcPersonImage.getImageUrl();
                        page.setPageCode("1");
                        page.setImageName(path.substring(path.lastIndexOf("/") + 1));
                        page.setImageUrl(path);
                        pageList.add(page);
                        esView.setPageNum("1");
                        esView.setSubType("311013");
                        esView.setPageList(pageList);
                        esViewList.add(esView);
                        contInfo.setESViewList(esViewList);
                        insured.setESViewList(esViewList);
                    }
                    insuredInfoList.add(insured);
                }
            }
            List<FCPerInfoTemp> fcPerInfoTempList = fcPerInfoTempMapper.selectByEnsureCode(ensureCode);
            List<FCPerinfoFamilyTemp> fcPerinfoFamilyTempList = fcPerinfoFamilyTempMapper.selectByEnsureCode(ensureCode);
            int order = 1;

            Map<String, InsuredInfo> idNoToInsuredMap = insuredInfoList.parallelStream()
                    .collect(Collectors.toConcurrentMap(
                            InsuredInfo::getIDNo,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));

            if (CollectionUtils.isNotEmpty(fcPerInfoTempList)) {
                for (FCPerInfoTemp fcPerInfoTemp : fcPerInfoTempList) {
                    InsuredInfo insuredInfo = idNoToInsuredMap.get(fcPerInfoTemp.getIDNo());
                    if (insuredInfo != null && "13".equals(insuredInfo.getRelationToAppnt())) {
                        insuredInfo.setCustomerSeqNo(String.valueOf(order));
                        order++;
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(fcPerinfoFamilyTempList)) {
                for (FCPerinfoFamilyTemp fcPerinfoFamilyTemp : fcPerinfoFamilyTempList) {
                    InsuredInfo insuredInfo = idNoToInsuredMap.get(fcPerinfoFamilyTemp.getIDNo());
                    if (insuredInfo != null && "06".equals(insuredInfo.getRelationToAppnt())) {
                        insuredInfo.setCustomerSeqNo(String.valueOf(order));
                        order++;
                    }
                }
            }

            for (InsuredInfo insuredInfo : insuredInfoList) {
                if ("06".equals(insuredInfo.getRelationToAppnt()) &&
                        StringUtils.isEmpty(insuredInfo.getCustomerSeqNo())) {
                    insuredInfo.setCustomerSeqNo(String.valueOf(order));
                    order++;
                }
            }
            insuredInfoList.sort(Comparator.comparing(
                    insured -> {
                        try {
                            return Integer.parseInt(insured.getCustomerSeqNo());
                        } catch (NumberFormatException e) {
                            return Integer.MAX_VALUE;
                        }
                    }
            ));
            contInfo.setInsuredInfoList(insuredInfoList);

            // 封装影像件
            List<Map<String, String>> imageInfoList = fcGrpInfoMapper.getImageInfo(fcGrpOrder.getGrpNo());
            List<ESView> esViewList = new ArrayList<ESView>();
            if (imageInfoList != null && imageInfoList.size() > 0) {
                Map<String, String> map = imageInfoList.get(0);
                List<Page> pageList = new ArrayList<Page>();
                int i = 0;
                for (String key : map.keySet()) {
                    if (!StringUtil.isEmpty(map.get(key))) {
                        i++;
                        String path = map.get(key);
                        String str1 = path.substring(0, path.indexOf("?"));
                        String str2 = path.substring(str1.length() + 1);
                        Page page = new Page();
                        page.setImageName(path.substring(path.lastIndexOf("/") + 1));
                        page.setImageUrl(myProps.getPolicySignBaseAddress() + str2);
                        page.setPageCode(i + "");
                        pageList.add(page);
                    }
                }
                if (i > 0) {
                    ESView esView = new ESView();
                    esView.setPageNum(i + "");
                    esView.setSubType("311111");
                    esView.setPageList(pageList);
                    esViewList.add(esView);
                    contInfo.setESViewList(esViewList);
                }

            }
            // 传输所有计划到核心--武德仲
            boolean flag;
            if (!"1".equals(fcEnsure.getPlanType())) {
                contInfo.setPlanList(queryEnsurePlans(fcGrpOrder.getEnsureCode()));
                flag = XmlUtil.saveBeanToJsonFile(contInfo, localFilePath + prtNo + ".json");
            }else {
                XStream xStream = new XStream(new DomDriver("UTF-8", new XmlFriendlyNameCoder("-_", "_")));
                xStream.alias("ContInfo", ContInfo.class);
                xStream.alias("CompanyInfo", CompanyInfo.class);
                xStream.alias("AppntImpartInfo", AppntImpartInfo.class);
                xStream.alias("Contacts", Contacts.class);
                xStream.alias("InsuredInfo", InsuredInfo.class);
                xStream.alias("RiskInfo", RiskInfo.class);
                xStream.alias("DutyInfo", DutyInfo.class);
                xStream.alias("PlanInfo", PlanInfo.class);
                xStream.alias("ESView", ESView.class);
                xStream.alias("InsuredImpart", InsuredImpart.class);
                xStream.alias("Page", Page.class);
                flag = XmlUtil.createXml(xStream, contInfo, localFilePath + prtNo + ".xml");
            }
            if (flag) {
                Map<String, String> map = new HashMap<>();
                map.put("tPartNo", prtNo);
                map.put("grpOrderNo", grpOrderNo);
                return map;
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            log.info("系统异常：", e);
            return null;
        }
    }

    /**
     * 获取福利计划信息
     *
     * @param ensureCode
     * @return
     */
    public List<PlanInfo> queryEnsurePlans(String ensureCode) {
        List<PlanInfo> planInfoList = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        params.put("ensureCode", ensureCode);
        List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectFCEnsurePlans(params);
        // 查询福利计划
        if (fcEnsurePlanList != null && fcEnsurePlanList.size() > 0) {
            for (int i = 0; i < fcEnsurePlanList.size(); i++) {
                PlanInfo planInfo = new PlanInfo();
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanList.get(i);
                planInfo.setContPlanCode(fcEnsurePlan.getPlanCode());
                planInfo.setContPlanName(fcEnsurePlan.getPlanName());
                planInfo.setPeoples3(insureService.getPlanPeopleNum(fcEnsurePlan.getEnsureCode(), fcEnsurePlan.getPlanCode()));
                List<RiskInfo> riskList = new ArrayList<RiskInfo>();
                // 查询险种
                List<String> riskCodeList = fcPlanRiskMapper.selectRiskCodeList(ensureCode, fcEnsurePlan.getPlanCode());
                if (riskCodeList.size() > 0) {
                    for (int j = 0; j < riskCodeList.size(); j++) {
                        FCPlanRiskKey fcPlanRiskKey = new FCPlanRiskKey();
                        fcPlanRiskKey.setEnsureCode(ensureCode);
                        fcPlanRiskKey.setPlanCode(fcEnsurePlan.getPlanCode());
                        fcPlanRiskKey.setRiskCode(riskCodeList.get(j));
                        FCPlanRisk fcPlanRisk = fcPlanRiskMapper.selectByPrimaryKey(fcPlanRiskKey);
                        RiskInfo riskInfo = new RiskInfo();
                        riskInfo.setRiskCode(riskCodeList.get(j));
                        riskInfo.setDistriFlag(fcPlanRisk.getReinsuranceMark());
                        // 手续费比率
                        riskInfo.setChargeFeeRate(fcPlanRisk.getFeeRatio());
                        // 佣金奖励津贴
                        riskInfo.setCommRate(fcPlanRisk.getCommissionOrAllowanceRatio());
                        // 赠险标记
                        riskInfo.setComplimentaryFlag(fcPlanRisk.getGiftInsureSign());
                        // 原保费
                        riskInfo.setOldPrem(
                                fcPlanRisk.getOriginalPrem() == null ? null : fcPlanRisk.getOriginalPrem().toString());
                        // 获取 责任列表
                        List<DutyInfo> dutyInfoList = new ArrayList<DutyInfo>();
                        params.clear();
                        params.put("ensureCode", ensureCode);
                        params.put("planCode", fcEnsurePlan.getPlanCode());
                        params.put("riskCode", riskCodeList.get(j));
                        List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyList(params);
                        if (fcPlanRiskDutyList.size() > 0) {
                            for (int k = 0; k < fcPlanRiskDutyList.size(); k++) {
                                DutyInfo dutyInfo = new DutyInfo();
                                FCPlanRiskDuty fcDuty = fcPlanRiskDutyList.get(k);
                                dutyInfo.setDutyCode(fcDuty.getDutyCode());
                                dutyInfo.setCalRule("3");
                                // 险种的保险期间
                                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
                                String policyEndDate = fcEnsure.getPolicyEndDate();
                                String cvaliDate = fcEnsure.getCvaliDate();
                                try {
                                    Long days = DateTimeUtil.getDistanceDays(policyEndDate, cvaliDate);
                                    if (days == 364 || days == 365) {
                                        dutyInfo.setInsuYear("1");
                                        dutyInfo.setInsuYearFlag("Y");
                                    } else {
                                        long daysOne = DateTimeUtil.getDistanceDays(fcEnsure.getCvaliDate(),
                                                fcEnsure.getPolicyEndDate());
                                        String daysTwo = String.valueOf(daysOne + 1);
                                        dutyInfo.setInsuYear(daysTwo);
                                        dutyInfo.setInsuYearFlag("D");
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                dutyInfo.setAmnt(fcDuty.getAmnt() + "");
                                dutyInfo.setPrem(fcDuty.getPrem() + "");
                                dutyInfo.setGetLimit(fcDuty.getGetLimit());
                                dutyInfo.setGetLimitType(fcDuty.getGetLimitType());
                                dutyInfo.setPayDay("0");
                                dutyInfo.setNoGetDay("0");
                                dutyInfo.setGetRate(fcDuty.getGetRatio());
                                if (fcDuty.getMaxGetDay() != null) {
                                    dutyInfo.setMaxPayDay(String.valueOf(fcDuty.getMaxGetDay().intValue()));
                                }
                                dutyInfo.setFloatRate("");
                                dutyInfoList.add(dutyInfo);
                            }
                        }
                        riskInfo.setDutyList(dutyInfoList);
                        riskList.add(riskInfo);
                    }
                }
                planInfo.setRiskList(riskList);
                planInfoList.add(planInfo);
            }
        }
        return planInfoList;
    }

    /**
     * <AUTHOR>
     * @description员工投默认计划
     * @date 19:46 19:46
     * @modified
     */
    @Transactional
    public String makeDefaultPlan(String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            if (StringUtils.isBlank(ensureCode)) {
                resultMap.put("message", "请输入福利编码。");
                return JSON.toJSONString(resultMap);
            }
            //查询福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if (fcEnsure == null) {
                resultMap.put("message", "请输入正确的福利编码。");
                return JSON.toJSONString(resultMap);
            }
            //判断险种是否下架
            String riskStopSale = ensureMakeService.checkRiskStopSale(ensureCode);
            if (!StringUtil.isEmpty(riskStopSale)) {
                resultMap.put("success", Boolean.FALSE);
                resultMap.put("code", "500");
                resultMap.put("message", riskStopSale);
                return JSON.toJSONString(resultMap);
            }
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String newdate = dateFormat.format(new Date());
            int compareTo = fcEnsure.getEndAppntDate().compareTo(newdate);
            if (compareTo > 0) {
                resultMap.put("message", "开放期未结束，不能转默认计划。");
                return JSON.toJSONString(resultMap);
            }
            if (!"1".equals(fcEnsure.getEnsureState())) {
                resultMap.put("message", "当前福利尚未定制完成，不可转默认计划。");
                return JSON.toJSONString(resultMap);
            }
            if (!"1".equals(fcEnsure.getPolicyState())) {
                resultMap.put("message", "当前福利保单状态非投保中，不可转默认计划。");
                return JSON.toJSONString(resultMap);
            }
            if ("0".equals(fcEnsure.getPlanType())) {
                //固定计划转默认计划
                resultMap = fixeMakeDefaultPlan(fcEnsure);
            } else if ("1".equals(fcEnsure.getPlanType())) {
                //弹性计划转默认计划
                resultMap = flexMakeDefaultPlan(fcEnsure);
            }
            // 用于记录本次默认计划投保人数
            int isCheckNumber = (int) resultMap.get("isCheckNumber");
            if (isCheckNumber == 0) {
                resultMap.put("message", "当前福利无需转默认计划。");
                log.info("福利" + ensureCode + "没有人员需要投保默认计划！");
            } else {
                resultMap.put("message", "福利" + ensureCode + "默认计划投保成功。系统共给" + isCheckNumber + "人投保默认计划");
                log.info("福利" + ensureCode + "默认计划投保成功。系统共给" + isCheckNumber + "人投保默认计划");
            }
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("默认计划批处理失败", e);
            throw new SystemException(e.getMessage());
        }
    }

    //整单确认---固定计划---转默认计划
    @Transactional
    public Map<String, Object> fixeMakeDefaultPlan(FCEnsure fcEnsure) {
        String ensureCode = fcEnsure.getEnsureCode();
        Map<String, Object> resultMap = new HashMap<>();
        try {
            int isCheckNumber = 0;   //用于记录本次默认计划投保人数
            List<Map<String, Object>> dutyAmountGradeList = new ArrayList<>();
            //获取该福利下员工信息
            Map<String, Object> dayMap = new HashMap<>();
            dayMap.put("ensureCode", ensureCode);
            dayMap.put("isValidy", "1");
            List<FCPerRegistDay> fcPerRegistDayList = fcPerRegistDayMapper.selectFCPerRegistDayList(dayMap);
            for (FCPerRegistDay fcPerRegistDay : fcPerRegistDayList) {
                log.info("fcPerRegistDay :{}", JsonUtil.toJSON(fcPerRegistDay));
                Map<String, Object> fpMap = new HashMap<>();
                String perNo = fcPerRegistDay.getPerNo();
                if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                    FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
                    log.info("转默认计划fcStaffFamilyRela:{}", JSON.toJSONString(fcStaffFamilyRela));
                    String personId = fcStaffFamilyRela.getPersonID();
                    fpMap.put("personId", personId);
                    fpMap.put("ensureCode", ensureCode);
                    //查询员工默认计划
                    log.info("查询员工默认计划:{}", JSON.toJSONString(fpMap));
                    FCDefaultPlan fcDefaultPlan = fcDefaultPlanMapper.selectDoublePlan(fpMap);
                    String planCode = fcDefaultPlan.getPlanCode();
                    FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
                    //团体投保单号
                    String grpOrderNo = fcGrpOrder.getGrpOrderNo();
                    Map<String, Object> map = new HashMap<>();
                    map.put("perNo", perNo);
                    map.put("grpOrderNo", grpOrderNo);
                    //查询订单是否存在
                    FCOrder fcOrder = fcOrderMapper.selectOrderByperNo(map);
                    //不存在订单
                    if (fcOrder == null) {
                        List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectEnsureCodeByPersonId(fpMap);
                        if (fpInsurePlanList.size() > 0) {
                            String insurePlanNo = fpInsurePlanList.get(0).getInsurePlanNo();
                            //删除计划临时表数据
                            fpInsurePlanMapper.deleteByPrimaryKey(insurePlanNo);
                        }
                        // 存储投保人信息表、订单表、订单轨迹表
                        String orderNo = makeDefaultOrderByInfo(fcEnsure, fcPerRegistDay, "");
                        // 存储子订单表
                        makeDefaultOrderItemByInfo(planCode, fcEnsure, personId, fcPerRegistDay, orderNo, 0.0, dutyAmountGradeList);
                        isCheckNumber++;
                    }
                    //学生投保
                } else if ("1".equals(fcEnsure.getEnsureType())) {
                    Double studentGrpPrem;
                    Map<String, String> insurePlanMap = new HashMap<>();
                    insurePlanMap.put("ensureCode", ensureCode);
                    insurePlanMap.put("perNo", perNo);
                    List<FCStaffFamilyRela> fcStaffFamilyRelaList = fcStaffFamilyRelaMapper.getPersonByInsurePlan(insurePlanMap);
                    // 根据福利编号获取团体保单信息
                    FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(fcEnsure.getEnsureCode());
                    //团体投保单号
                    String grpOrderNo = fcGrpOrder.getGrpOrderNo();
                    Map<String, Object> map = new HashMap<>();
                    map.put("perNo", perNo);
                    map.put("grpOrderNo", grpOrderNo);
                    //查询订单是否存在
                    FCOrder fcOrderInfo = fcOrderMapper.selectOrderByperNo(map);
                    String orderNo = "";
                    int orderItemSum = 0;
                    if (fcOrderInfo == null) {
                        orderNo = makeDefaultOrderByInfo(fcEnsure, fcPerRegistDay, "");
                    } else {
                        orderNo = fcOrderInfo.getOrderNo();
                        orderItemSum = 1;
                    }
                    String totalPrem = fcOrderItemMapper.getSumTotalPrem(perNo, ensureCode);
                    if (StringUtils.isBlank(totalPrem)) {
                        totalPrem = "0.00";
                    }
                    studentGrpPrem = fcPerRegistDay.getStudentGrpPrem() - Double.parseDouble(totalPrem);
                    for (FCStaffFamilyRela fcStaffFamilyRela : fcStaffFamilyRelaList) {
                        String personId = fcStaffFamilyRela.getPersonID();
                        fpMap.put("personId", personId);
                        fpMap.put("ensureCode", ensureCode);
                        //查询学生默认计划
                        FCDefaultPlan fcDefaultPlan = fcDefaultPlanMapper.selectDoublePlan(fpMap);
                        if (fcDefaultPlan == null) {
                            continue;
                        }
                        String planCode = fcDefaultPlan.getPlanCode();
                        List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectEnsureCodeByPersonId(fpMap);
                        if (fpInsurePlanList.size() > 0) {
                            String insurePlanNo = fpInsurePlanList.get(0).getInsurePlanNo();
                            //删除计划临时表数据
                            fpInsurePlanMapper.deleteByPrimaryKey(insurePlanNo);
                        }
                        studentGrpPrem = makeDefaultOrderItemByInfo(planCode, fcEnsure, personId, fcPerRegistDay, orderNo, studentGrpPrem, dutyAmountGradeList);
                        orderItemSum++;
                        isCheckNumber++;
                    }
                    //证明当前订单下并无子订单 删除该订单
                    if (orderItemSum == 0) {
                        fcOrderMapper.deleteByPrimaryKey(orderNo);
                    }
                }
            }
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("isCheckNumber", isCheckNumber);
            return resultMap;
        } catch (Exception e) {
            log.error("固定计划---转默认计划异常:{}", ensureCode, e);
            throw new SystemException(e.toString());
        }
    }


    /**
     * 整单确认--弹性计划--转默认计划
     *
     * @param fcEnsure
     * @return
     */
    @Transactional
    public Map<String, Object> flexMakeDefaultPlan(FCEnsure fcEnsure) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            List<String> errList = new ArrayList<>();
            String ensureCode = fcEnsure.getEnsureCode();
            // 用于记录本次默认计划投保人数
            int isCheckNumber = 0;
            //缴费方式
            Map<String, Object> params = new HashMap<>();
            params.put("ensureCode", ensureCode);
            params.put("configNo", "008");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(params);
            //用于判断是否需要对家属福利额度进行判断  固定计划--不存在企业代缴 不需要进行处理
            boolean payType = !"1".equals(fcEnsureConfig.getConfigValue());
            //获取该福利的员工信息
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            List<Map<String, String>> perInfoMapList = fcPerInfoMapper.getPerInfoByEnsureCode(mapInfo);
            if (perInfoMapList.size() > 0) {
                for (Map<String, String> perInfoMap : perInfoMapList) {
                    String error = "员工" + perInfoMap.get("name") + "的家属：";
                    Map<String, Object> map = new HashMap<>();
                    map.put("EnsureCode", ensureCode);
                    map.put("BirthDay", perInfoMap.get("birthDay"));
                    map.put("CvaliDate", fcEnsure.getCvaliDate());
                    map.put("Sex", perInfoMap.get("sex"));
                    map.put("InsureCount", fcEnsure.getInsuredNumber());
                    map.put("JoinMedProtect", perInfoMap.get("joinMedProtect"));
                    map.put("OccupationType", perInfoMap.get("occupationType"));
                    map.put("LevelCod", perInfoMap.get("levelCode"));
                    map.put("ServiceTerm", perInfoMap.get("serviceTerm"));
                    map.put("Retirement", perInfoMap.get("retirement"));
                    map.put("Relation", "0");
                    //员工是否配置默认计划
                    boolean staRequired = false;
                    //员工是否存在待生效订单
                    boolean staExistence = false;
                    //员工必选保额档次集合
                    log.info(JsonUtil.toJSON(map));
                    List<Map<String, Object>> staDutyAmountGradeList = premTrailService.generateRequest(map);
                    if (staDutyAmountGradeList.size() > 0) {
                        staRequired = true;
                    }
                    String perNo = perInfoMap.get("perNo");
                    List<String> personIDList = fcPersonMapper.getEmployPersonId(perNo);
                    String staPersonID = personIDList.get(0);
                    mapInfo.put("perNo", perNo);
                    List<Map<String, String>> perOrderStatusInfo = fcOrderMapper.getOrderStatusByEnsureCode(mapInfo);
                    String grpOrderNo = "";
                    String orderNo = "";
                    if (perOrderStatusInfo.size() == 1) {
                        grpOrderNo = perOrderStatusInfo.get(0).get("grpOrderNo");
                        orderNo = perOrderStatusInfo.get(0).get("orderNo");
                        //订单状态  待生效
                        if ("08".equals(perOrderStatusInfo.get(0).get("orderStatus"))) {
                            staExistence = true;
                        } else {
                            //存在订单 订单未生效 删除订单下子订单相关信息
                            fpInsureEflexPlanMapper.deletefPInsureEflexPlan("", perNo, ensureCode);
                            fpInsureEflexPlanOptionalMapper.deletefPInsureEflexPlanOptional("", perNo, ensureCode);
                            fcOrderInsuredMapper.deleteByOrderNo(orderNo);
                            fcOrderItemMapper.deleteByOrderNo(orderNo);
                            fcInsureEflexPlanMapper.deleteByOrderNo(orderNo);
                            fcInsureEflexPlanOptionalMapper.deleteByOrderNo(orderNo);
                        }
                    } else if (perOrderStatusInfo.size() > 1) {
                        resultMap.put("code", "500");
                        resultMap.put("success", false);
                        resultMap.put("message", "个人客户号：" + perNo + ",在该福利下存在多个订单，系统有误，请联系平台维护人员。");
                        return resultMap;
                    }
                    Map<String, Object> fcPerRegistDayMap = new HashMap<>();
                    fcPerRegistDayMap.put("ensureCode", ensureCode);
                    fcPerRegistDayMap.put("perNo", perNo);
                    FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(fcPerRegistDayMap);
                    Double staffGrpPrem = 0.00;
                    if (fcPerRegistDay.getStaffGrpPrem() != null) {
                        staffGrpPrem = fcPerRegistDay.getStaffGrpPrem();
                    }
                    Double familyGrpTotalePrem = 0.00;
                    if (fcPerRegistDay.getFamilyGrpPrem() != null) {
                        familyGrpTotalePrem = fcPerRegistDay.getFamilyGrpPrem();
                    }
                    //员工存在待生效订单   员工不需要转默认计划 对家属进行判断
                    if (staExistence) {
                        //获取员工家属信息
                        List<Map<String, String>> fcPersonList = fcPersonMapper.selectPersonByPernoNotPerNo(perNo);
                        boolean check = false;
                        for (Map<String, String> fcPerson : fcPersonList) {
                            Map<String, Object> familyInfoMap = new HashMap<>();
                            familyInfoMap.put("EnsureCode", ensureCode);
                            familyInfoMap.put("BirthDay", fcPerson.get("birthDay"));
                            familyInfoMap.put("CvaliDate", fcEnsure.getCvaliDate());
                            familyInfoMap.put("InsureCount", fcEnsure.getInsuredNumber());
                            familyInfoMap.put("Sex", fcPerson.get("sex"));
                            familyInfoMap.put("JoinMedProtect", fcPerson.get("joinMedProtect"));
                            familyInfoMap.put("OccupationType", fcPerson.get("occupationType"));
                            familyInfoMap.put("Relation", fcPerson.get("realtion"));
                            //家属是否配置默认计划
                            boolean fmaRequired = false;
                            //家属是否存在待生效订单
                            boolean fmaExistence = false;
                            //获取家属默认保额档次
                            List<Map<String, Object>> famDutyAmountGradeList = premTrailService.generateRequest(familyInfoMap);
                            if (famDutyAmountGradeList.size() > 0) {
                                fmaRequired = true;
                            }
                            String personID = fcPerson.get("personID");
                            //判断perNo下是否存在该家属信息 不存在 新增并关联
                            FCPerson fcPersonInfo = fcPersonMapper.getFcPersonInfoByPerNo(perNo, personID);
                            if (fcPersonInfo == null) {
                                fcPersonInfo = fcPersonMapper.selectByPrimaryKey(personID);
                                fcPersonInfo.setPersonID(maxNoService.createMaxNo("PersonId", "", 20));
                                fcPersonInfo.setOperator("admin002");
                                fcPersonMapper.insertSelective(fcPersonInfo);
                                FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
                                fcStaffFamilyRela.setPerNo(perNo);
                                fcStaffFamilyRela.setPersonID(fcPersonInfo.getPersonID());
                                fcStaffFamilyRela.setRelation(fcPerson.get("realtion"));
                                fcStaffFamilyRela.setRelationProve("");
                                fcStaffFamilyRela.setOperator("admin002");
                                fcStaffFamilyRela = CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
                                fcStaffFamilyRelaMapper.insertSelective(fcStaffFamilyRela);
                            }
                            personID = fcPersonInfo.getPersonID();
                            //查询员工是否给家属投保
                            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectOrderItemNo(grpOrderNo, orderNo, personID);
                            if (fcOrderInsured != null) {
                                //员工待生效订单包含家属，家属不需转默认计划
                                fmaExistence = true;
                            }
                            //存在默认保额档次且不存在待生效子订单   家属转默认计划 计算保费
                            if (fmaRequired && !fmaExistence) {
                                Double famTotalPrem = 0.00;
                                List<Map<String, Object>> famdutyAmountGradeList = new ArrayList<>(famDutyAmountGradeList);
                                for (Map<String, Object> map2 : famdutyAmountGradeList) {
                                    Map<String, Object> resultmap = premTrailService.premTrail(map2);
                                    if ((Boolean) resultmap.get("success")) {
                                        if ("17020".equals(map2.get("RiskCode").toString()) || "15060".equals(map2.get("RiskCode").toString()) || "15070".equals(map2.get("RiskCode").toString())) {
                                            Map<String, Object> premMap = (Map<String, Object>) resultmap.get("Prem");
                                            famTotalPrem = CommonUtil.add(famTotalPrem, Double.valueOf(premMap.get("Prem").toString()));
                                        } else {
                                            famTotalPrem = CommonUtil.add(famTotalPrem, Double.valueOf(resultmap.get("Prem").toString()));
                                        }
                                    } else {
                                        famDutyAmountGradeList.remove(map2);
                                    }
                                }
                                //家属自付保费
                                double familySelfPrem = 0.00;
                                //公司补助保费
                                double familyGrpPrem = 0.00;
                                if (payType) {
                                    if (familyGrpTotalePrem > 0 && familyGrpTotalePrem >= famTotalPrem) {
                                        if (famTotalPrem > familyGrpTotalePrem) {
                                            familyGrpPrem = familyGrpTotalePrem;
                                            familySelfPrem = CommonUtil.sub(famTotalPrem, familyGrpTotalePrem);
                                            familyGrpTotalePrem = 0.0;
                                        } else if (famTotalPrem <= familyGrpTotalePrem) {
                                            familyGrpPrem = famTotalPrem;
                                            familyGrpTotalePrem = CommonUtil.sub(familyGrpTotalePrem, famTotalPrem);
                                        }
                                        //家属转默认计划
                                        makeDefaultOrderItemByInfo(familySelfPrem, fcEnsure, personID, fcPerRegistDay, orderNo, familyGrpPrem, famDutyAmountGradeList);
                                        isCheckNumber++;
                                    } else {
                                        error += fcPerson.get("name") + ",";
                                        check = true;
                                    }
                                } else {
                                    familyGrpPrem = famTotalPrem;
                                    //家属转默认计划
                                    makeDefaultOrderItemByInfo(familySelfPrem, fcEnsure, personID, fcPerRegistDay, orderNo, familyGrpPrem, famDutyAmountGradeList);
                                    isCheckNumber++;
                                }
                            }
                        }
                        if (check) {
                            error += "因家属福利额度不足无法转默认计划。";
                            errList.add(error);
                        }
                    } else {
                        if (staRequired) {       //不存在待生效订单 存在默认计划  员工转默认计划
                            double staffprem = 0.00;//个人缴纳计划保费
                            double staTotalPrem = 0.00;//应缴默认计划保费
                            List<Map<String, Object>> dutyAmountGradeList = new ArrayList<>(staDutyAmountGradeList);
                            for (Map<String, Object> map2 : dutyAmountGradeList) {
                                Map<String, Object> resultmap = premTrailService.premTrail(map2);
                                if ((Boolean) resultmap.get("success")) {
                                    if ("17020".equals(map2.get("RiskCode").toString()) || "15060".equals(map2.get("RiskCode").toString()) || "15070".equals(map2.get("RiskCode").toString())) {
                                        Map<String, Object> premMap = (Map<String, Object>) resultmap.get("Prem");
                                        staTotalPrem = CommonUtil.add(staTotalPrem, Double.valueOf(premMap.get("Prem").toString()));
                                    } else {
                                        staTotalPrem = CommonUtil.add(staTotalPrem, Double.valueOf(resultmap.get("Prem").toString()));
                                    }
                                } else {
                                    staDutyAmountGradeList.remove(map2);
                                }
                            }
                            if (payType) {
                                if (staTotalPrem > staffGrpPrem) {
                                    staffprem = CommonUtil.sub(staTotalPrem, staffGrpPrem);
                                } else {
                                    staffGrpPrem = staTotalPrem;
                                }
                            } else {
                                staffGrpPrem = staTotalPrem;
                            }
                            //员工转默认计划
                            orderNo = makeDefaultOrderByInfo(fcEnsure, fcPerRegistDay, orderNo);
                            makeDefaultOrderItemByInfo(staffprem, fcEnsure, staPersonID, fcPerRegistDay, orderNo, staffGrpPrem, staDutyAmountGradeList);
                            isCheckNumber++;
                            //获取员工家属信息
                            List<Map<String, String>> fcPersonList = fcPersonMapper.selectPersonByPernoNotPerNo(perNo);
                            boolean check = false;
                            for (Map<String, String> fcPerson : fcPersonList) {
                                Map<String, Object> familyInfoMap = new HashMap<>();
                                familyInfoMap.put("EnsureCode", ensureCode);
                                familyInfoMap.put("BirthDay", fcPerson.get("birthDay"));
                                familyInfoMap.put("CvaliDate", fcEnsure.getCvaliDate());
                                familyInfoMap.put("InsureCount", fcEnsure.getInsuredNumber());
                                familyInfoMap.put("Sex", fcPerson.get("sex"));
                                familyInfoMap.put("JoinMedProtect", fcPerson.get("joinMedProtect"));
                                familyInfoMap.put("OccupationType", fcPerson.get("occupationType"));
                                familyInfoMap.put("Relation", fcPerson.get("realtion"));
                                //家属是否配置默认计划
                                boolean fmaRequired = false;
                                //家属是否存在待生效订单
                                boolean fmaExistence = false;
                                //获取家属默认保额档次
                                log.info(JSON.toJSONString(familyInfoMap));
                                List<Map<String, Object>> famDutyAmountGradeList = premTrailService.generateRequest(familyInfoMap);
                                if (famDutyAmountGradeList.size() > 0) {
                                    fmaRequired = true;
                                }
                                String personID = fcPerson.get("personID");
                                //判断perNo下是否存在该家属信息 不存在 新增并关联
                                FCPerson fcPersonInfo = fcPersonMapper.getFcPersonInfoByPerNo(perNo, personID);
                                if (fcPersonInfo == null) {
                                    fcPersonInfo = fcPersonMapper.selectByPrimaryKey(personID);
                                    fcPersonInfo.setPersonID(maxNoService.createMaxNo("PersonId", "", 20));
                                    fcPersonInfo.setOperator("admin002");
                                    fcPersonMapper.insertSelective(fcPersonInfo);
                                    FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
                                    fcStaffFamilyRela.setPerNo(perNo);
                                    fcStaffFamilyRela.setPersonID(fcPersonInfo.getPersonID());
                                    fcStaffFamilyRela.setRelation(fcPerson.get("realtion"));
                                    fcStaffFamilyRela.setRelationProve("");
                                    fcStaffFamilyRela.setOperator("admin002");
                                    fcStaffFamilyRela = CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
                                    fcStaffFamilyRelaMapper.insertSelective(fcStaffFamilyRela);
                                }
                                personID = fcPersonInfo.getPersonID();
                                //查询员工是否给家属投保
                                FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectOrderItemNo(grpOrderNo, orderNo, personID);
                                if (fcOrderInsured != null) {
                                    //员工待生效订单包含家属，家属不需转默认计划
                                    fmaExistence = true;
                                }
                                //存在默认保额档次且不存在待生效子订单   家属转默认计划 计算保费
                                if (fmaRequired && !fmaExistence) {
                                    Double famTotalPrem = 0.00;
                                    List<Map<String, Object>> dutyAmountGradeListByfam = new ArrayList<>(famDutyAmountGradeList);
                                    for (Map<String, Object> map2 : dutyAmountGradeListByfam) {
                                        Map<String, Object> resultmap = premTrailService.premTrail(map2);
                                        if ((Boolean) resultmap.get("success")) {
                                            if ("17020".equals(map2.get("RiskCode").toString()) || "15060".equals(map2.get("RiskCode").toString()) || "15070".equals(map2.get("RiskCode").toString())) {
                                                Map<String, Object> premMap = (Map<String, Object>) resultmap.get("Prem");
                                                famTotalPrem = CommonUtil.add(famTotalPrem, Double.valueOf(premMap.get("Prem").toString()));
                                            } else {
                                                famTotalPrem = CommonUtil.add(famTotalPrem, Double.valueOf(resultmap.get("Prem").toString()));
                                            }
                                        } else {
                                            famDutyAmountGradeList.remove(map2);
                                        }
                                    }
                                    //家属自付保费
                                    double familySelfPrem = 0.00;
                                    //公司补助保费
                                    double familyGrpPrem = 0.00;
                                    if (payType) {
                                        if (familyGrpTotalePrem > 0 && familyGrpTotalePrem >= famTotalPrem) {
                                            if (famTotalPrem > familyGrpTotalePrem) {
                                                familyGrpPrem = familyGrpTotalePrem;
                                                familySelfPrem = CommonUtil.sub(famTotalPrem, familyGrpTotalePrem);
                                                familyGrpTotalePrem = 0.0;
                                            } else if (famTotalPrem <= familyGrpTotalePrem) {
                                                familyGrpPrem = famTotalPrem;
                                                familyGrpTotalePrem = CommonUtil.sub(familyGrpTotalePrem, famTotalPrem);
                                            }
                                            //家属转默认计划
                                            makeDefaultOrderItemByInfo(familySelfPrem, fcEnsure, personID, fcPerRegistDay, orderNo, familyGrpPrem, famDutyAmountGradeList);
                                            isCheckNumber++;
                                        } else {
                                            error += fcPerson.get("name") + ",";
                                            check = true;
                                        }
                                    } else {
                                        //家属转默认计划
                                        familyGrpPrem = famTotalPrem;
                                        makeDefaultOrderItemByInfo(familySelfPrem, fcEnsure, personID, fcPerRegistDay, orderNo, familyGrpPrem, famDutyAmountGradeList);
                                        isCheckNumber++;
                                    }
                                }
                            }
                            if (check) {
                                error += "因家属福利额度不足无法转默认计划。";
                                errList.add(error);
                            }
                        } else {
                            //不存在待生效订单 && 不存在默认计划 删除该订单
                            fcOrderMapper.deleteByPrimaryKey(orderNo);
                            errList.add("员工" + perInfoMap.get("name") + "未投保，家属不能转默认计划。");
                        }
                    }
                }
            }
            resultMap.put("data", errList);
            resultMap.put("isCheckNumber", isCheckNumber);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "计划：" + ensureCode + "转默认计划成功");
            return resultMap;
        } catch (Exception e) {
            log.info(e.getMessage());
            throw new RuntimeException();
        }
    }

    /**
     * 转默认计划--订单层级
     *
     * @param fcEnsure
     * @param fcPerRegistDay
     * @param orderNo
     * @return
     */
    @Transactional
    public String makeDefaultOrderByInfo(FCEnsure fcEnsure, FCPerRegistDay fcPerRegistDay, String orderNo) {
        String perNo = fcPerRegistDay.getPerNo();
        // 根据福利编号获取团体保单信息
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(fcEnsure.getEnsureCode());
        FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
        /*
         * 1、投保人表FCPerAppnt 从 员工表FCPerInfo取数据 2、一个员工家庭在订单表FCOrder表创建一条数据
         */
        if (StringUtils.isBlank(orderNo)) {
            // 1、投保人表FCPerAppnt
            FCPerAppnt fcPerAppnt = new FCPerAppnt();
            String perAppNo = maxNoService.createMaxNo("PerAppNo", "", 20);
            fcPerAppnt.setPerAppNo(perAppNo);
            fcPerAppnt.setGrpNo(fcEnsure.getGrpNo());
            fcPerAppnt.setPerNo(perNo);
            fcPerAppnt.setName(fcPerInfo.getName());
            fcPerAppnt.setSex(fcPerInfo.getSex());
            fcPerAppnt.setIDType(fcPerInfo.getIDType());
            fcPerAppnt.setIDNo(fcPerInfo.getIDNo());
            fcPerAppnt.setBirthDay(fcPerInfo.getBirthDay());
            fcPerAppnt.setOperator("admin002");
            fcPerAppnt = CommonUtil.initObject(fcPerAppnt, "INSERT");
            fcPerAppntMapper.insert(fcPerAppnt);
            // 2、订单表FCOrder
            FCOrder fcOrder = new FCOrder();
            orderNo = maxNoService.createMaxNo("OrderNo", DateTimeUtil.getTimeString().substring(0, 4), 8);
            fcOrder.setOrderNo(orderNo);
            fcOrder.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
            fcOrder.setOrderStatus("08");
            fcOrder.setOrderType("01");
            fcOrder.setOpenDay(fcPerRegistDay.getOpenDay());
            fcOrder.setCloseDay(fcPerRegistDay.getCloseDay());
            fcOrder.setGrpNo(fcEnsure.getGrpNo());
            fcOrder.setPerNo(perNo);
            fcOrder.setPerAppNo(perAppNo);
            fcOrder.setCommitDate(DateTimeUtil.getCurrentDate());
            fcOrder.setOperator("admin002");
            fcOrder.setClientNo(fcEnsure.getClientNo());
            CommonUtil.initObject(fcOrder, "INSERT");
            fcOrderMapper.insert(fcOrder);
        } else {
            //订单状态改为--待生效
            FCOrder fcOrder = fcOrderMapper.selectByPrimaryKey(orderNo);
            //固定计划 01 待生效、待修改  弹性计划 08 待生效  -- update by zch 固定、弹性采用统一08 2020.12.17
            if ("0".equals(fcEnsure.getPlanType())) {
                fcOrder.setOrderStatus("08");
            } else {
                fcOrder.setOrderStatus("08");
            }
            CommonUtil.initObject(fcOrder, "UPDATE");
            fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
        }
        // 6、订单轨迹表FCOrderLocus
        FCOrderLocus fcOrderLocus = new FCOrderLocus();
        String orderLocusSN = maxNoService.createMaxNo("orderLocusSN", "", 20);
        fcOrderLocus.setOrderLocusSN(orderLocusSN);
        fcOrderLocus.setOrderNo(orderNo);
        //固定计划 01 待生效、待修改  弹性计划 08 待生效 -- update by zch 固定、弹性采用统一08 2020.12.17
        if ("0".equals(fcEnsure.getPlanType())) {
            fcOrderLocus.setOrderStatus("08");
        } else {
            fcOrderLocus.setOrderStatus("08");
        }
        fcOrderLocus.setOperator("admin001");
        CommonUtil.initObject(fcOrderLocus, "INSERT");
        fcOrderLocusMapper.insert(fcOrderLocus);
        return orderNo;
    }

    /**
     * 转默认计划--转子订单层级
     *
     * @param parameter      因方法被固定计划及弹性计划共用  所以parameter被不同分支调用时代表不同参数。  固定计划--planCode--计划编码  弹性计划--staffprem--自付保费
     * @param fcEnsure
     * @param personId
     * @param fcPerRegistDay
     * @param orderNo
     * @param subsidyPrem    弹性计划--企业代缴保费  固定计划--学生投保--学校补助保费
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public double makeDefaultOrderItemByInfo(Object parameter, FCEnsure fcEnsure, String personId, FCPerRegistDay fcPerRegistDay, String orderNo, Double subsidyPrem, List<Map<String, Object>> dutyAmountGradeList) {
        log.info("转默认计划--转子订单层级:parameter: {}，fcEnsure: {},personId{},fcPerRegistDay{},orderNo{},subsidyPrem{},dutyAmountGradeList{}", parameter, fcEnsure, personId, fcPerRegistDay, orderNo, subsidyPrem, JSON.toJSONString(dutyAmountGradeList));
        //获取员工编号
        String perNo = fcPerRegistDay.getPerNo();
        // 获取代理人信息
        List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
        FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);
        // 根据福利编号获取团体保单信息
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(fcEnsure.getEnsureCode());
        FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
        // 获取支付方式 1-企业全缴 2-企业代缴 3-个人批扣
        Map params1 = new HashMap();
        params1.put("ensureCode", fcEnsure.getEnsureCode());
        params1.put("configNo", "008");
        String payMethod = fcEnsureConfigMapper.selectOnlyValue(params1);

        String orderItemNo = "";
        if ("1".equals(fcEnsure.getPlanType())) {
            // 3、子订单表FCOrderItem
            FCOrderItem fcOrderItem = new FCOrderItem();
            orderItemNo = maxNoService.createMaxNo("OrderItemNo", "", 20);
            String orderItemDetailNo = maxNoService.createMaxNo("OrderItemDetailNo", "", 20);
            fcOrderItem.setOrderItemNo(orderItemNo);
            fcOrderItem.setOrderItemDetailNo(orderItemDetailNo);
            fcOrderItem.setOrderNo(orderNo);
            String rule = "99" + fdAgentInfo.getManageCom().substring(2, 4) + DateTimeUtil.getCurrentYear().substring(2, 4);
            String contNo = maxNoService.createMaxNo("ContNo" + DateTimeUtil.getCurrentYear(), rule, 9);
            contNo = contNo + "8";
            fcOrderItem.setContNo(contNo);
            fcOrderItem.setSelfPrem((double) parameter);
            fcOrderItem.setGrpPrem(subsidyPrem);
            fcOrderItem.setOperator("admin002");
            fcOrderItem = CommonUtil.initObject(fcOrderItem, "INSERT");
            fcOrderItemMapper.insert(fcOrderItem);
            List<FPInsureEflexPlan> fPInsureEflexPlanList = new ArrayList<FPInsureEflexPlan>();
            List<FcInsureEflexPlan> fcInsureEflexPlanList = new ArrayList<>();
            for (Map<String, Object> dutyAmountGradeInfo : dutyAmountGradeList) {
                //个人投保弹性计划正式表
                FcInsureEflexPlan fcInsureEflexPlan = new FcInsureEflexPlan();
                fcInsureEflexPlan.setOrderItemDetailNo(orderItemDetailNo);
                fcInsureEflexPlan.setAmountGrageCode(dutyAmountGradeInfo.get("AmountGrageCode").toString());
                fcInsureEflexPlan.setDeductibleAttr(dutyAmountGradeInfo.get("DeductibleAttr") == null ? "" : dutyAmountGradeInfo.get("DeductibleAttr").toString());
                fcInsureEflexPlan.setDeductible((dutyAmountGradeInfo.get("Deductible") == null || "".equals(dutyAmountGradeInfo.get("Deductible").toString())) ? 0.0 : Double.valueOf(dutyAmountGradeInfo.get("Deductible").toString()));
                fcInsureEflexPlan.setCompensationRatio((dutyAmountGradeInfo.get("CompensationRatio") == null || "".equals(dutyAmountGradeInfo.get("CompensationRatio").toString())) ? 0.0 : Double.valueOf(dutyAmountGradeInfo.get("CompensationRatio").toString()));
                Map<String, Object> resultmap = premTrailService.premTrail(dutyAmountGradeInfo);
                double prem = 0.00;
                if ((Boolean) resultmap.get("success")) {
                    if ("17020".equals(dutyAmountGradeInfo.get("RiskCode").toString()) || "15060".equals(dutyAmountGradeInfo.get("RiskCode").toString()) || "15070".equals(dutyAmountGradeInfo.get("RiskCode").toString())) {
                        Map<String, Object> premMap = (Map<String, Object>) resultmap.get("Prem");
                        prem = CommonUtil.add(prem, Double.valueOf(premMap.get("Prem").toString()));
                    } else {
                        prem = CommonUtil.add(prem, Double.valueOf(resultmap.get("Prem").toString()));
                    }
                }
                fcInsureEflexPlan.setPrem(prem);
                fcInsureEflexPlan.setOperator("admin002");
                fcInsureEflexPlan = CommonUtil.initObject(fcInsureEflexPlan, "INSERT");
                fcInsureEflexPlanList.add(fcInsureEflexPlan);
                //个人投保弹性计划临时表
                String insureElfexPlanNo = maxNoService.createMaxNo("InsureElfexPlanNo", "", 20);
                FPInsureEflexPlan fpInsureEflexPlan = new FPInsureEflexPlan();
                fpInsureEflexPlan.setPerNo(perNo);
                fpInsureEflexPlan.setPersonId(personId);
                fpInsureEflexPlan.setEnsureCode(fcEnsure.getEnsureCode());
                fpInsureEflexPlan.setAmountGrageCode(dutyAmountGradeInfo.get("AmountGrageCode").toString());
                fpInsureEflexPlan.setDeductible((dutyAmountGradeInfo.get("Deductible") == null || "".equals(dutyAmountGradeInfo.get("Deductible").toString())) ? 0.0 : Double.valueOf(dutyAmountGradeInfo.get("Deductible").toString()));
                fpInsureEflexPlan.setCompensationRatio((dutyAmountGradeInfo.get("CompensationRatio") == null || "".equals(dutyAmountGradeInfo.get("CompensationRatio"))) ? 0.0 : Double.valueOf(dutyAmountGradeInfo.get("CompensationRatio").toString()));
                fpInsureEflexPlan.setInsureElfexPlanNo(insureElfexPlanNo);
                fpInsureEflexPlan.setInsureState("1");//0-未提交到订单表
                fpInsureEflexPlan.setRiskCode(dutyAmountGradeInfo.get("RiskCode").toString());
                fpInsureEflexPlan.setRiskType(dutyAmountGradeInfo.get("RiskType").toString());
                fpInsureEflexPlan.setDeductibleAttr(dutyAmountGradeInfo.get("DeductibleAttr") == null ? "" : dutyAmountGradeInfo.get("DeductibleAttr").toString());
                fpInsureEflexPlan.setAppntYear(DateTimeUtil.getCurrentYear());
                fpInsureEflexPlan.setPrem(prem);
                fpInsureEflexPlan.setOperator("admin002");
                fpInsureEflexPlan.setOperatorCom("");
                CommonUtil.initObject(fpInsureEflexPlan, "INSERT");
                fPInsureEflexPlanList.add(fpInsureEflexPlan);
            }
            fcInsureEflexPlanMapper.insertList(fcInsureEflexPlanList);
            fpInsureEflexPlanMapper.insertfPInsureEflexPlan(fPInsureEflexPlanList);
            // 5、被保人表FCOrderInsured
            FCOrderInsured fcOrderInsured = new FCOrderInsured();
            fcOrderInsured.setOrderItemNo(orderItemNo);
            fcOrderInsured.setOrderNo(orderNo);
            fcOrderInsured.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
            FCPerson person = fcPersonMapper.selectByPrimaryKey(personId);
            fcOrderInsured.setPersonID(person.getPersonID());
            fcOrderInsured.setName(person.getName());
            fcOrderInsured.setSex(person.getSex());
            fcOrderInsured.setBirthDay(person.getBirthDate());
            fcOrderInsured.setNativeplace(person.getNativeplace());
            fcOrderInsured.setIDType(person.getIDType());
            fcOrderInsured.setIDNo(person.getIDNo());
            fcOrderInsured.setMobilePhone(person.getMobilePhone());
            fcOrderInsured.setPhone(person.getPhone());
            fcOrderInsured.setDepartment(fcPerInfo.getDepartment());
            fcOrderInsured.setOccupationType(person.getOccupationType());
            fcOrderInsured.setOccupationCode(person.getOccupationCode());
            fcOrderInsured.setJoinMedProtect(person.getJoinMedProtect());
            fcOrderInsured.setMedProtectType(person.getMedProtectType());
            fcOrderInsured.setEMail(person.getEMail());
            fcOrderInsured.setAddress(person.getAddress());
            fcOrderInsured.setOperator("admin002");
            CommonUtil.initObject(fcOrderInsured, "INSERT");
            fcOrderInsuredMapper.insert(fcOrderInsured);
        } else if ("0".equals(fcEnsure.getPlanType())) {
            // 查询人员信息
            FCPerson person = fcPersonMapper.selectByPrimaryKey(personId);
            //查询员工是否给家属投保  存在肯定是待生效订单，无需创建新的订单（非待生效订单已经删除）
            FCOrderInsured orderInsured = fcOrderInsuredMapper.selectOrderItemNo(fcGrpOrder.getGrpOrderNo(), orderNo, personId);
            if (orderInsured == null) {
                // 3、子订单表FCOrderItem
                FCOrderItem fcOrderItem = new FCOrderItem();
                orderItemNo = maxNoService.createMaxNo("OrderItemNo", "", 20);
                String orderItemDetailNo = maxNoService.createMaxNo("OrderItemDetailNo", "", 20);
                fcOrderItem.setOrderItemNo(orderItemNo);
                fcOrderItem.setOrderItemDetailNo(orderItemDetailNo);
                fcOrderItem.setOrderNo(orderNo);
				/* 个人保单号生成规则：
				1、前缀99；
				2、3-4位截取保单管理机构的3、4位，如为总公司则取86（如管理机构为864498取44）；
				3、5-6位截取年份后两位（如2018取18）；
				4、7-15位取每个自然年度流水号；
				5、最后一位固定为8；
				例如弹性平台今年第一张个人保险凭证号应为：9944180000000018。
				*/
                String rule = "99" + fdAgentInfo.getManageCom().substring(2, 4) + DateTimeUtil.getCurrentYear().substring(2, 4);
                String contNo = maxNoService.createMaxNo("ContNo" + DateTimeUtil.getCurrentYear(), rule, 9);
                contNo = contNo + "8";
                fcOrderItem.setContNo(contNo);
                // 应付保费==计划保费
                double planPrem = fcEnsurePlanMapper.selectPlanPrem(fcEnsure.getEnsureCode(), parameter.toString());
                // 公司补助保费
                double grpPrem = 0.0;
                if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                    if (null != fcPerRegistDay.getStaffGrpPrem()) {
                        grpPrem = fcPerRegistDay.getStaffGrpPrem();
                    }
                    if (grpPrem >= planPrem) {
                        fcOrderItem.setSelfPrem(0.00);
                        fcOrderItem.setGrpPrem(planPrem);
                    } else {
                        // 场地险投保仅有个人批扣的方式，且银行卡信息为必录；非场地险个人批扣不能转默认计划。
                        if (!"2".equals(fcEnsure.getEnsureType()) && payMethod.equals(PaymentTypeEnum.PERSONALDEDUCTION.getCode())) {
                            throw new SystemException("被保人" + person.getName() + "，缴费方式为混合缴费不能转默认计划！");
                        }
                        fcOrderItem.setSelfPrem(CommonUtil.sub(planPrem, grpPrem));
                        fcOrderItem.setGrpPrem(grpPrem);
                    }
                } else if ("1".equals(fcEnsure.getEnsureType())) {
                    if (subsidyPrem != null && subsidyPrem > 0) {
                        grpPrem = subsidyPrem;
                    }
                    if (grpPrem >= planPrem) {
                        fcOrderItem.setSelfPrem(0.00);
                        fcOrderItem.setGrpPrem(planPrem);
                        subsidyPrem = subsidyPrem - planPrem;
                    } else {
                        fcOrderItem.setSelfPrem(CommonUtil.sub(planPrem, grpPrem));
                        fcOrderItem.setGrpPrem(grpPrem);
                        subsidyPrem = subsidyPrem - planPrem;
                    }
                }
                fcOrderItem.setOperator("admin002");
                CommonUtil.initObject(fcOrderItem, "INSERT");
                fcOrderItemMapper.insert(fcOrderItem);
                // 4、子订单产品要素详情表FCOrderItemDetail
                FCOrderItemDetail fcOrderItemDetail = new FCOrderItemDetail();
                fcOrderItemDetail.setOrderItemDetailNo(orderItemDetailNo);
                fcOrderItemDetail.setProductCode(parameter.toString());
                fcOrderItemDetail.setProductEleCode("001");
                fcOrderItemDetail.setValue("计划对象");
                fcOrderItemDetail.setEnsureCode(fcEnsure.getEnsureCode());
                fcOrderItemDetail.setOperator("admin002");
                CommonUtil.initObject(fcOrderItemDetail, "INSERT");
                fcOrderItemDetailMapper.insert(fcOrderItemDetail);
                FPInsurePlan fpInsurePlan = new FPInsurePlan();
                String insurePlanNo = maxNoService.createMaxNo("insurePlanNo", "", 20);
                fpInsurePlan.setInsurePlanNo(insurePlanNo);
                fpInsurePlan.setInsureState("1");
                fpInsurePlan.setPerno(perNo);
                fpInsurePlan.setEnsureCode(fcEnsure.getEnsureCode());
                fpInsurePlan.setAppntYear(fcEnsure.getAppntYear());
                fpInsurePlan.setOperator("admin002");
                fpInsurePlan.setPersonId(personId);
                fpInsurePlan.setPlanCode(parameter.toString());
                CommonUtil.initObject(fpInsurePlan, "INSERT");
                fpInsurePlanMapper.insert(fpInsurePlan);
                // 5、被保人表FCOrderInsured
                FCOrderInsured fcOrderInsured = new FCOrderInsured();
                fcOrderInsured.setOrderItemNo(orderItemNo);
                fcOrderInsured.setOrderNo(orderNo);
                fcOrderInsured.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
                fcOrderInsured.setPersonID(person.getPersonID());
                fcOrderInsured.setName(person.getName());
                fcOrderInsured.setSex(person.getSex());
                fcOrderInsured.setBirthDay(person.getBirthDate());
                fcOrderInsured.setNativeplace(person.getNativeplace());
                fcOrderInsured.setIDType(person.getIDType());
                fcOrderInsured.setIDNo(person.getIDNo());
                fcOrderInsured.setMobilePhone(person.getMobilePhone());
                fcOrderInsured.setPhone(person.getPhone());
                fcOrderInsured.setDepartment(fcPerInfo.getDepartment());
                fcOrderInsured.setOccupationType(person.getOccupationType());
                fcOrderInsured.setOccupationCode(person.getOccupationCode());
                fcOrderInsured.setJoinMedProtect(person.getJoinMedProtect());
                fcOrderInsured.setMedProtectType(person.getMedProtectType());
                fcOrderInsured.setEMail(person.getEMail());
                fcOrderInsured.setAddress(person.getAddress());
                fcOrderInsured.setOperator("admin002");
                fcOrderInsured = CommonUtil.initObject(fcOrderInsured, "INSERT");
                fcOrderInsuredMapper.insert(fcOrderInsured);
            }
        }
        return subsidyPrem;
    }
}
