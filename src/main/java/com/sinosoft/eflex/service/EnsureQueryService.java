package com.sinosoft.eflex.service;

import cn.org.bjca.edms.client.message.ChannelMessage;
import cn.org.bjca.edms.client.message.ReqMessage;
import cn.org.bjca.edms.client.tools.EDMSClientTool;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.hqins.common.base.constants.DatePatterns;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.IDTypeNewEnum;
import com.sinosoft.eflex.enums.PayPremTypeEnum;
import com.sinosoft.eflex.enums.SexEnum;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.service.admin.EnsureAuditService;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import org.apache.axis.utils.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 保障信息查询
 *
 * <AUTHOR>
 */
@Service
public class EnsureQueryService {

    private static Logger Log = LoggerFactory.getLogger(EnsureQueryService.class);


    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCEnsurePlanMapper fcEnsurePlanMapper;
    @Autowired
    private FCOrderItemMapper fcOrderItemMapper;
    @Autowired
    private FCOrderMapper fcOrderMapper;
    @Autowired
    private FCPlanRiskMapper fcPlanRiskMapper;
    @Autowired
    private FDRiskDutyInfoMapper fdRiskDutyInfoMapper;
    @Autowired
    private FCPlanRiskDutyMapper fcPlanRiskDutyMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FCDefaultPlanMapper fcDefaultPlanMapper;
    @Autowired
    private WelfareQueryMapper welfareQueryMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private FCFileDocMainMapper fcFileDocMainMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private InsureService insureService;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FCContractMapper fcContractMapper;
    @Autowired
    private FCMailInfoMapper fcMailInfoMapper;
    @Autowired
    private FDPlaceMapper fdPlaceMapper;
    @Autowired
    private MyProps myProps;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;
    @Autowired
    private FcDutyAmountGradeMapper fcDutyAmountGradeMapper;
    @Autowired
    private FPInsureEflexPlanMapper fpInsureEflexPlanMapper;
    @Autowired
    private FCGrpApplicantMapper fcGrpApplicantMapper;
    @Autowired
    private FcPrtandCoreRelaMapper fcPrtandCoreRelaMapper;
    @Autowired
    private EnsureAuditService ensureAuditService;
    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;


    private static final Logger log = LoggerFactory.getLogger(EnsureQueryService.class);

    /**
     * 员工计划编码
     */
    private static final String EMPLOYEE_CODE = "1";

    /**
     * 家属计划编码
     */
    private static final String RELATION_CODE = "2";

    private static final String STUDENT_CODE = "3";

    /**
     * 弹性计划查询
     *
     * @param requestMap
     * @return
     */
    public String selectEnsure(String token, Map<String, Object> requestMap) {
        ResponseMsg<Map<String, Object>> responseInfo = new ResponseMsg<Map<String, Object>>();
        try {
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            String grpNo = globalInput.getGrpNo();
            if (StringUtils.isEmpty(globalInput.getManageCom())
                    && globalInput.getCustomType().matches("^(3|4|5|3,5)")) {
                throw new SystemException("当前用户管理机构不能为空！");
            } else {
                requestMap.put("manageCom", globalInput.getManageCom());
            }
            requestMap.put("grpNo", grpNo);
            // 福利状态 0-待定制，1-已定制
            requestMap.put("ensureState", "1");
            this.pageInit(requestMap);
            List<FCEnsure> hqEnsures = fcEnsureMapper.selectCompanyEnsureInfo(requestMap);
            for (FCEnsure fcEnsure : hqEnsures) {
                String policyState = fcEnsure.getPolicyState() == null ? "" : fcEnsure.getPolicyState();
                FDCodeKey key = new FDCodeKey();
                key.setCodeType("PolicyState");
                key.setCodeKey(policyState);
                FDCode fdCode = fdCodeMapper.selectByPrimaryKey(key);
                fcEnsure.setPolicyStateName(fdCode.getCodeName());
                // 总人数和总保费要实时计算
                fcEnsure.setInsuredNumber(insureService.getEnsureNum(fcEnsure.getEnsureCode()));
                fcEnsure.setTotalPrem(insureService.getEnsurePrem(fcEnsure.getEnsureCode()));
                // 签单状态与签单返回信息
                FcPrtandCoreRela fcPrtandCoreRela = fcPrtandCoreRelaMapper
                        .selectCoreReturnMsg(fcEnsure.getEnsureCode());
                if (!ObjectUtils.isEmpty(fcPrtandCoreRela)) {
                    fcEnsure.setPolicySignState(fcPrtandCoreRela.getStatus());
                    fcEnsure.setPolicySignErrorMsg(fcPrtandCoreRela.getDescribe());
                }
            }
            PageHelperUtil<FCEnsure> teamPageInfo = new PageHelperUtil<FCEnsure>(hqEnsures);
            Map<String, Object> dataMap = new HashMap<String, Object>(16);
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("list", teamPageInfo.getList());
            responseInfo.okStatus().message("查询福利成功").data(dataMap);
        } catch (Exception e) {
            log.error("查询福利失败：", e);
            responseInfo.errorStatus().message("查询福利失败！");
        }
        return JSON.toJSONString(responseInfo);
    }

    /**
     * 保障信息查询折线图
     *
     * @param authorization
     * @return
     */
    public String getEnsureChart(String authorization, String ensureCode) {
        ResponseMsg<List<Map<String, Object>>> responseInfo = new ResponseMsg<>();
        try {
            // 根据token获取企业客户号
            GlobalInput globalInput = userService.getSession(authorization);
            List<Map<String, Object>> fcEnsures = fcEnsureMapper.getEnsureChart(globalInput.getGrpNo(), ensureCode);
            responseInfo.okStatus().message("保障信息查询折线图查询成功").data(fcEnsures);
        } catch (Exception e) {
            log.error("保障信息查询折线图查询失败：", e);
            responseInfo.errorStatus().message("保障信息查询折线图查询失败");
        }
        return JSON.toJSONString(responseInfo);
    }

    /**
     * 企业计划汇总查询
     *
     * @param ensureCode
     * @return
     */
    public String selectPlanCollection(String ensureCode, String token) {
        ResponseMsg<Map<String, Object>> responseInfo = new ResponseMsg<>();
        try {
            List<FCEnsurePlan> fcEnsurePlans = fcEnsurePlanMapper.selectByEnsureCode(ensureCode);
            Map<String, Object> ensureMap = new HashMap<>(16);
            List<Map<String, Object>> employeeList = new LinkedList<>();
            List<Map<String, Object>> relationList = new LinkedList<>();
            List<Map<String, Object>> studentList = new LinkedList<>();
            for (FCEnsurePlan fcEnsurePlan : fcEnsurePlans) {
                Map<String, Object> planMap = new HashMap<String, Object>(16);
                String planObject = fcEnsurePlan.getPlanObject();
                if (EMPLOYEE_CODE.equals(planObject)) {
                    planObject = "员工";
                } else if (RELATION_CODE.equals(planObject)) {
                    planObject = "家属";
                } else if (STUDENT_CODE.equals(planObject)) {
                    planObject = "学生";
                }
                String totalPrem = CommonUtil.mul(Double.valueOf(fcEnsurePlan.getInsuredNumber()), Double.valueOf(fcEnsurePlan.getTotalPrem())) + "";
                planMap.put("PlanObject", planObject);
                planMap.put("PlanCode", fcEnsurePlan.getPlanCode());
                planMap.put("planName", fcEnsurePlan.getPlanName());
                planMap.put("PlanKey", fcEnsurePlan.getPlanKey());
                planMap.put("InsuredNumber", fcEnsurePlan.getInsuredNumber());
                planMap.put("TotalPrem", totalPrem);
                List<FCPlanRisk> fcPlanRisks = fcEnsurePlan.getFcPlanRisks();
                List<Map<String, Object>> riskList = new ArrayList<Map<String, Object>>();
                for (FCPlanRisk fcPlanRisk : fcPlanRisks) {
                    Map<String, Object> riskMap = new HashMap<String, Object>(16);
                    List<FCPlanRiskDuty> fcPlanRiskDuties = fcPlanRisk.getFcPlanRiskDuties();
                    for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDuties) {
                        riskMap.put("RiskName", fcPlanRisk.getRiskName());
                        riskMap.put("Amnt", fcPlanRiskDuty.getAmnt());
                        riskMap.put("Prem", fcPlanRiskDuty.getPrem());
                    }
                    riskList.add(riskMap);
                }
                planMap.put("risk", riskList);
                if (EMPLOYEE_CODE.equals(fcEnsurePlan.getPlanObject())) {
                    employeeList.add(planMap);
                } else if (RELATION_CODE.equals(fcEnsurePlan.getPlanObject())) {
                    relationList.add(planMap);
                } else if (STUDENT_CODE.equals(fcEnsurePlan.getPlanObject())) {
                    studentList.add(planMap);
                }
            }
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            String policyState = fcEnsure.getPolicyState();
            if (!"3".equals(policyState) && !"4".equals(policyState)) {
                ensureMap.put("employee", employeeList);
                ensureMap.put("relation", relationList);
                ensureMap.put("student", studentList);
                responseInfo.okStatus().message("查询弹性计划成功").data(ensureMap);
                return JSON.toJSONString(responseInfo);
            }
            //保单列表查询
            List<HashMap<String, Object>> mapList = ensureRisk(ensureCode, token);
            PageHelperUtil<HashMap<String, Object>> teamPageInfo = new PageHelperUtil<>(mapList);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("list", teamPageInfo.getList());
            ensureMap.put("data", dataMap);
            ensureMap.put("employee", employeeList);
            ensureMap.put("relation", relationList);
            ensureMap.put("student", studentList);
            responseInfo.okStatus().message("查询弹性计划成功").data(ensureMap);
        } catch (Exception e) {
            log.error("企业计划汇总查询失败: ", e);
            responseInfo.errorStatus().message("企业计划汇总查询失败");
        }
        return JSON.toJSONString(responseInfo);
    }

    /**
     * <AUTHOR>
     * @description保单封装数据
     * @date 14:19 14:19
     * @modified
     */
    public List<HashMap<String, Object>> ensureRisk(String ensureCode, String token) {
        List<HashMap<String, Object>> mapList = new ArrayList<>();
        HashMap<String, Object> hashMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        FCEnsure fcEnsureInfo = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        String grpNo = fcEnsureInfo.getGrpNo();
        String customType = globalInput.getCustomType();
        //获取团体保单信息
        Map<String, Object> map = new HashMap<>();
        map.put("grpNo", grpNo);
        map.put("ensureCode", ensureCode);
        Map<String, Object> fcGrpOrderMap = fcGrpOrderMapper.selectGrpOrderInfo(map);
        hashMap.put("grpOrderNo", fcGrpOrderMap.get("GrpContNo"));
        map.clear();
        //获取福利信息
        map.put("ensureCode", ensureCode);
        map.put("grpNo", grpNo);
        List<FCEnsure> fcEnsure = fcEnsureMapper.findEnsureList(map);
        String cvaliDate = fcEnsure.get(0).getCvaliDate();
        String policyEndDate = fcEnsure.get(0).getPolicyEndDate();
        hashMap.put("cvaliDate", cvaliDate);
        String policyEndDateDesc = policyEndDate;
        if ("9999-12-31".equals(policyEndDate)) {
            policyEndDateDesc = "长期";
        }
        hashMap.put("ensureDate", "<span>" + cvaliDate + "</span>" + "&nbsp;<span>" + "至" + "</span>" + "<span>&nbsp;" + policyEndDateDesc + "</span>");
        //福利总缴费
        double ensurePrem = insureService.getEnsurePrem(ensureCode);
        hashMap.put("ensurePrem", ensurePrem);
        //企业总缴费
        double grpPrem = insureService.getGrpPrem(ensureCode, grpNo);
        hashMap.put("grpPrem", grpPrem);
        Map<String, Object> params = new HashMap<>();
        params.put("ensureCode", ensureCode);
        params.put("configNo", "008");
        FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(params);
        if (fcEnsureConfig != null) {
            if ("企业代扣代缴".equals(fcEnsureConfig.getConfigValue())) {
                hashMap.put("payMethod", "2");
            } else {
                hashMap.put("payMethod", fcEnsureConfig.getConfigValue());
            }
        } else {
            if ("0".equals(fcEnsure.get(0).getEnsureType()) || "2".equals(fcEnsure.get(0).getEnsureType())) {
                hashMap.put("payMethod", "2");
            }
        }
        map.put("customType", customType);
        List<FCMailInfo> fcMailInfoList = fcMailInfoMapper.getMailInfoByPersonId(map);
        //是否申请过发票1为申请过，0没有申请过
        if (fcMailInfoList.size() > 0) {
            hashMap.put("isMail", "1");
            mapList.add(hashMap);
            return mapList;
        }
        hashMap.put("isMail", "0");
        map.put("grpNo", grpNo);

        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date cvaliDates = sf.parse(cvaliDate);
            Date policyEndDates = sf.parse(policyEndDate);
            if (cvaliDates.before(new Date()) && (new Date()).before(policyEndDates)) {
                hashMap.put("isFlag", "0");
            } else {
                hashMap.put("isFlag", "1");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        mapList.add(hashMap);
        return mapList;
    }

    /**
     * 保障计划列表查询 用当前日期查询符合条件的福利编码，然后再查询该福利保障的计划列表
     *
     * @return
     * <AUTHOR>
     */
    public String queryPlanList(String token) {
        Map<String, Object> resultMap = new HashMap<String, Object>(16);
        try {
            Map<String, Object> requestMap = new HashMap<String, Object>(1);
            requestMap.put("sysDate", DateTimeUtil.getCurrentDate());
            List<FCEnsure> ensureList = fcEnsureMapper.selectCompanyEnsureInfo(requestMap);
            if (ensureList.size() > 0) {
                String ensureCode = ensureList.get(0).getEnsureCode();
                return selectPlanCollection(ensureCode, token);
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "您没有福利保障");
            }
        } catch (Exception e) {
            log.error("计划列表查询失败: ", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保障计划列表查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 员工计划查询
     *
     * @param requestMap
     * @return
     */
    public String queryEmployPlan(Map<String, Object> requestMap) {
        ResponseMsg<Map<String, Object>> responseInfo = new ResponseMsg<>();
        try {
            // 根据令牌获取企业客户号
            this.getGrpNoByToken(requestMap);
            // 分页参数合理化
            this.pageInit(requestMap);
            List<Map<String, Object>> employPlans = fcOrderItemMapper.selectEmployPlanList(requestMap);
            PageHelperUtil<Map<String, Object>> teamPageInfo = new PageHelperUtil<>(employPlans);
            Map<String, Object> dataMap = new HashMap<String, Object>(16);
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("list", teamPageInfo.getList());
            responseInfo.okStatus().message("员工计划查询成功").data(dataMap);
        } catch (Exception e) {
            log.error("员工计划查询失败：", e);
            responseInfo.errorStatus().message("员工计划查询失败");
        }
        return JSON.toJSONString(responseInfo);
    }

    /**
     * 家属计划查询
     *
     * @param requestMap
     * @return
     */
    public String queryRelationPlan(Map<String, Object> requestMap) {
        ResponseMsg<Map<String, Object>> responseInfo = new ResponseMsg<>();
        try {
            this.getGrpNoByToken(requestMap);
            this.pageInit(requestMap);
            List<Map<String, String>> relationPlans = fcOrderItemMapper.selectRelationPlanList(requestMap);
            PageHelperUtil<Map<String, String>> teamPageInfo = new PageHelperUtil<>(relationPlans);
            Map<String, Object> dataMap = new HashMap<String, Object>(16);
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("list", teamPageInfo.getList());
            responseInfo.okStatus().message("家属计划查询成功").data(dataMap);
        } catch (Exception e) {
            log.error("家属计划查询失败: ", e);
            responseInfo.errorStatus().message("家属计划查询失败");
        }
        return JSON.toJSONString(responseInfo);
    }

    /**
     * 学生计划查询
     *
     * @param requestMap
     * @return
     */
    public String queryStudentPlan(Map<String, Object> requestMap) {
        ResponseMsg<Map<String, Object>> responseInfo = new ResponseMsg<>();
        try {
            this.getGrpNoByToken(requestMap);
            this.pageInit(requestMap);
            List<Map<String, String>> relationPlans = fcOrderItemMapper.queryStudentPlanList(requestMap);
            PageHelperUtil<Map<String, String>> teamPageInfo = new PageHelperUtil<>(relationPlans);
            Map<String, Object> dataMap = new HashMap<String, Object>(16);
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("list", teamPageInfo.getList());
            responseInfo.okStatus().message("学生计划查询成功").data(dataMap);
        } catch (Exception e) {
            log.error("学生计划查询失败: ", e);
            responseInfo.errorStatus().message("学生计划查询失败");
        }
        return JSON.toJSONString(responseInfo);
    }

    /**
     * 投保清单查询(企事业单位投保)
     *
     * @param requestMap
     * @return
     */
    public String queryInsuredDetail(Map<String, Object> requestMap) {
        Map<String, Object> resultMap = new HashMap<String, Object>(16);
        try {
            getGrpNoByToken(requestMap);
            this.pageInit(requestMap);
            List<Map<String, String>> insuredDetails = fcOrderMapper.listInsureDetail(requestMap);
            PageHelperUtil<Map<String, String>> teamPageInfo = new PageHelperUtil<>(insuredDetails);
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("ensureList", teamPageInfo.getList());
            resultMap.put("data", dataMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "投保清单查询成功");
        } catch (Exception e) {
            log.error("投保清单查询失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "投保清单查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 投保清单查询(学生投保)
     *
     * @param requestMap
     * @return
     */
    public String queryStuInsuredDetail(Map<String, Object> requestMap) {
        Map<String, Object> resultMap = new HashMap<String, Object>(16);
        try {
            getGrpNoByToken(requestMap);
            this.pageInit(requestMap);
            List<Map<String, String>> insuredDetails = fcOrderMapper.listStuInsureDetail(requestMap);
            PageHelperUtil<Map<String, String>> teamPageInfo = new PageHelperUtil<>(insuredDetails);
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("ensureList", teamPageInfo.getList());
            resultMap.put("data", dataMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "投保清单查询成功");
        } catch (Exception e) {
            log.error("投保清单查询失败：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "投保清单查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 计划详情查询
     *
     * @param planCode   计划编码
     * @param personId   被保人ID
     * @param ensureCode 福利编码
     * @return
     */
    public String getPlanDetail(String planCode, String personId, String Authorization, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 保障计划表
            Map<String, Object> paramOne = new HashMap<>();
            paramOne.put("ensureCode", ensureCode);
            paramOne.put("planCode", planCode);
            FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByPrimaryKey(paramOne);
            if (fcEnsurePlan == null) {
                log.info("该计划不存在");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "该计划不存在");
                return JSON.toJSONString(resultMap);
            }
//			//获取员工的personid
//			String staffPersonId=fcDefaultPlanMapper.getEmployPersonid(personId);
//			if(personId==null || personId.equals("")){
//				resultMap.put("success", false);
//				resultMap.put("code", "500");
//				resultMap.put("message", "数据异常");
//                return JSON.toJSONString(resultMap);
//			}
//			// 个人投保默认计划保费
//            Map<String, Double> defaultPremMap = fcDefaultPlanMapper.selectPayment(staffPersonId, ensureCode);
//            if (fcEnsurePlan == null || defaultPremMap == null) {
//            	resultMap.put("success", false);
//				resultMap.put("code", "500");
//				resultMap.put("message", "计划详情查询数据为空");
//                return JSON.toJSONString(resultMap);
//            }
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("planCode", planCode);
            // 计划-险种-责任集合
            List<Map<String, Object>> planRiskDutyInfo = new ArrayList<>();
            // 查询险种
            params.put("ensureCode", ensureCode);
            List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectRiskList(params);
            // 循环险种
            List<Map<String, Object>> risklist = new ArrayList<Map<String, Object>>();
            Map<String, String> param = new HashMap<String, String>();
            for (FCPlanRisk risk : fcPlanRiskList) {
                // 查询责任
                params.put("riskCode", risk.getRiskCode());
                List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper
                        .selectDutyList(params);
                // 循环责任
                for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                    Map<String, Object> dutyMap = new HashMap<>();
                    dutyMap.put("planCode", risk.getPlanCode());
                    dutyMap.put("planName", fcEnsurePlan.getPlanName());
                    dutyMap.put("riskCode", risk.getRiskCode());
                    dutyMap.put("amnt", new BigDecimal(fcPlanRiskDuty.getAmnt()).toPlainString());
                    dutyMap.put("dutyCode", fcPlanRiskDuty.getDutyCode());
                    //添加免赔额、免赔额属性、赔付比例字段（若数据库中为空，则返回也为空）
                    //免赔额
                    if (null == fcPlanRiskDuty.getGetLimit()) {
                        dutyMap.put("getLimit", "");
                    } else {
                        dutyMap.put("getLimit", fcPlanRiskDuty.getGetLimit());
                    }
                    //免赔额属性
                    if (null == fcPlanRiskDuty.getGetLimitType()) {
                        dutyMap.put("getLimitType", "");
                    } else {
                        dutyMap.put("getLimitType", fcPlanRiskDuty.getGetLimitType());
                    }
                    //赔付比例
                    if (null == fcPlanRiskDuty.getGetRatio()) {
                        dutyMap.put("getRatio", "");
                    } else {
                        //此数据在数据库中存储的是小数，向前台传输数据得时候要恢复到百分比
                        dutyMap.put("getRatio", fcPlanRiskDuty.getGetRatio() * 100);
                    }
                    //最大赔付天数
                    if (null == fcPlanRiskDuty.getMaxGetDay()) {
                        dutyMap.put("maxGetDay", "");
                    } else {
                        dutyMap.put("maxGetDay", fcPlanRiskDuty.getMaxGetDay());
                    }
                    param.clear();
                    param.put("riskCode", risk.getRiskCode());
                    param.put("dutyCode", fcPlanRiskDuty.getDutyCode());
                    Map<String, Object> dutyinfo = fdRiskDutyInfoMapper.selectByDutyCode(param);
                    dutyMap.put("dutyName", dutyinfo.get("DutyName"));
                    dutyMap.put("riskName", dutyinfo.get("RiskName"));
                    // 保费
                    dutyMap.put("prem", fcPlanRiskDuty.getPrem());
                    planRiskDutyInfo.add(dutyMap);
                }
                Map<String, Object> rMap = new HashMap<>();
                rMap.put("dutyCount", fcPlanRiskDutyList.size());
                risklist.add(rMap);
            }
            // 应前端要求，这里要把fcEnsurePlan放入数组里
            List<FCEnsurePlan> planList = new ArrayList<FCEnsurePlan>();
            planList.add(fcEnsurePlan);
            // 应前台格式要求
            // 总保费
            double totalPrem = fcEnsurePlan.getTotalPrem();
            // 企业缴纳
            double defaultPrem = 0.0;
            // 个人缴纳
            double personPay = 0.00;
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectPersonIdInfo(personId);
            String perNo = fcStaffFamilyRela.getPerNo();
            //获取订单号
            String orderNo = fcOrderMapper.selectOrderList(perNo, ensureCode);
            Map<String, Object> premMap = new HashMap<>();
            premMap.put("orderNo", orderNo);
            premMap.put("personId", personId);
            List<FCOrderInsured> fcOrderInsuredList = fcOrderInsuredMapper.selectListPlan(premMap);
            String orderItemNo = fcOrderInsuredList.get(0).getOrderItemNo();
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            //个人缴费
            personPay = fcOrderItem.getSelfPrem();
            //企业缴费
            defaultPrem = fcOrderItem.getGrpPrem();

            resultMap.put("data", planRiskDutyInfo);
            resultMap.put("companyPay", defaultPrem);
            resultMap.put("personPay", personPay);
            resultMap.put("totalPrem", totalPrem);
            resultMap.put("planFocus", fcEnsurePlan.getPlanKey());
            resultMap.put("riskInfo", risklist);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "计划详情查询成功");
            log.info("计划详情查询成功!");
        } catch (Exception e) {
            log.info("查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "计划详情查询失败");
        }
        return JSON.toJSONString(resultMap);

    }

    /**
     * 员工计划导出excel
     *
     * @param requestMap
     * @param response
     * @return
     */
    public String exportEmployPlan(Map<String, Object> requestMap, HttpServletResponse response) {
        ResponseMsg<Object> responseMsg = new ResponseMsg<>();
        try {
            // 根据token获取企业客户号
            GlobalInput globalInput = null;
            try {
                globalInput = userService.getSession((String) requestMap.get("Authorization"));
                if (globalInput == null) {
                    throw new RuntimeException();
                }
            } catch (Exception e) {
                log.error("获取session失败!", e);
                responseMsg.code("-1").message("获取session失败，请重新登录！");
                return JSON.toJSONString(responseMsg);
            }
            requestMap.put("grpNo", globalInput.getGrpNo());

            List<Map<String, Object>> employPlans = fcOrderItemMapper.selectEmployPlanList(requestMap);
            // excel数组表头
            String[][] headers = {{"department", "部门"}, {"name", "员工姓名"}, {"planName", "选择计划"}};
            // 调用生成excel生成工具类,返回前台下载
            ServletOutputStream outputStream = response.getOutputStream();
            String filename = new String(("EmployeePlan_" + DateTimeUtil.getTimeString()).getBytes(), "ISO-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            String title = "EmployeePlan";
            ExportExcelUtil.exportExcel(title, headers, employPlans, outputStream);
            outputStream.flush();
            responseMsg.okStatus().message("员工计划导出excel成功!");
        } catch (Exception e) {
            log.error("员工计划导出excel失败", e);
            responseMsg.errorStatus().message("员工计划导出excel失败");
        }
        return JSON.toJSONString(responseMsg);
    }

    /**
     * 家属计划导出execel
     *
     * @param requestMap
     * @param response
     */
    public String exportRelationPlan(Map<String, Object> requestMap, HttpServletResponse response) {
        ResponseMsg<Object> responseMsg = new ResponseMsg<>();
        try {
            // 根据token获取企业客户号
            GlobalInput globalInput = null;
            try {
                globalInput = userService.getSession((String) requestMap.get("Authorization"));
                if (globalInput == null) {
                    throw new RuntimeException();
                }
            } catch (Exception e) {
                log.error("获取session失败!", e);
                responseMsg.code("-1").message("获取session失败，请重新登录！");
                return JSON.toJSONString(responseMsg);
            }

            requestMap.put("grpNo", globalInput.getGrpNo());
            List<Map<String, String>> relationPlans = fcOrderItemMapper.selectRelationPlanList(requestMap);
            // excel数组表头
            String[][] headers = {{"employeeName", "员工姓名"}, {"faName", "家属姓名"}, {"planName", "选择计划"},
                    {"department", "部门"}};
            // 调用生成excel生成工具类,返回前台下载
            ServletOutputStream outputStream = response.getOutputStream();
            String filename = new String(("RelationPlan_" + DateTimeUtil.getTimeString()).getBytes(), "ISO-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            String title = "RelationPlan";
            ExportExcelUtil.exportExcel(title, headers, relationPlans, outputStream);
            outputStream.flush();
            responseMsg.okStatus().message("家属计划导出excel成功");
        } catch (Exception e) {
            log.error("家属计划导出excel失败", e);
            responseMsg.errorStatus().message("家属计划导出excel失败");
        }
        return JSON.toJSONString(responseMsg);
    }

    /**
     * 学生计划导出execel
     *
     * @param requestMap
     * @param response
     */
    public String exportStudentPlan(Map<String, Object> requestMap, HttpServletResponse response) {
        ResponseMsg<Object> responseMsg = new ResponseMsg<>();
        try {
            // 根据token获取企业客户号
            GlobalInput globalInput = null;
            try {
                globalInput = userService.getSession((String) requestMap.get("Authorization"));
                if (globalInput == null) {
                    throw new RuntimeException();
                }
            } catch (Exception e) {
                log.error("获取session失败!", e);
                responseMsg.code("-1").message("获取session失败，请重新登录！");
                return JSON.toJSONString(responseMsg);
            }

            requestMap.put("grpNo", globalInput.getGrpNo());
            List<Map<String, String>> relationPlans = fcOrderItemMapper.queryStudentPlanList(requestMap);
            // excel数组表头
            String[][] headers = {{"stuName", "学生姓名"}, {"garName", "监护人姓名"}, {"planName", "选择计划"}};
            // 调用生成excel生成工具类,返回前台下载
            ServletOutputStream outputStream = response.getOutputStream();
            String filename = new String(("StudentPlan" + DateTimeUtil.getTimeString()).getBytes(), "ISO-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            String title = "StudentPlan";
            ExportExcelUtil.exportExcel(title, headers, relationPlans, outputStream);
            outputStream.flush();
            responseMsg.okStatus().message("学生计划导出excel成功");
        } catch (Exception e) {
            log.error("学生计划导出excel失败", e);
            responseMsg.errorStatus().message("学生计划导出excel失败");
        }
        return JSON.toJSONString(responseMsg);
    }

    /**
     * 导出投保清单EXCEL
     *
     * @param authorization
     * @param response
     */
    public String exportInsureList(String authorization, HttpServletResponse response, Map<String, String> map) {
        ResponseMsg<Object> responseMsg = new ResponseMsg<>();
        try {
            // 根据token获取企业客户号
            GlobalInput globalInput;
            String grpNo = "";
            try {
                globalInput = userService.getSession(authorization);
                if (globalInput == null) {
                    throw new RuntimeException();
                }
                grpNo = globalInput.getGrpNo();
            } catch (Exception e) {
                log.error("获取session失败!", e);
                responseMsg.code("-1").message("获取session失败，请重新登录！");
                return JSON.toJSONString(responseMsg);
            }
            FCFileDocMain fcFileDocMain = fcFileDocMainMapper.selectByPrimaryKey("00000003");
            String filePath = fcFileDocMain.getFilePath();
            String filename = fcFileDocMain.getFileSaveName() + fcFileDocMain.getFileSuffix();
            //todo 测试用
            File file = new File(filePath + filename);
            Workbook wb = WorkbookFactory.create(new FileInputStream(file));
            // 调用生成excel生成工具类,返回前台下载
            ServletOutputStream outputStream = response.getOutputStream();
            String filenameF = new String((fcFileDocMain.getFileName() + fcFileDocMain.getFileSuffix()).getBytes("UTF-8"), "iso-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + filenameF);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            // excel表格数据
            wb = insuredPlanSheet(wb, grpNo, map);
            wb.write(outputStream);
            outputStream.flush();
            responseMsg.okStatus().message("导出投保清单EXCEL成功!");
        } catch (Exception e) {
            log.info("导出投保清单EXCEL失败!", e);
            responseMsg.errorStatus().message("导出投保清单EXCEL失败!");
        }
        return JSON.toJSONString(responseMsg);
    }

    /**
     * 导出投保清单EXCEL(学生投保)
     *
     * @param authorization
     * @param response
     */
    public String exportStuInsureList(String authorization, HttpServletResponse response, Map<String, String> map) {
        ResponseMsg<Object> responseMsg = new ResponseMsg<>();
        try {

            // 根据token获取企业客户号
            GlobalInput globalInput = null;
            String grpNo = "";
            try {
                globalInput = userService.getSession(authorization);
                if (globalInput == null) {
                    throw new RuntimeException();
                }
                grpNo = globalInput.getGrpNo();
            } catch (Exception e) {
                log.error("获取session失败!", e);
                responseMsg.code("-1").message("获取session失败，请重新登录！");
                return JSON.toJSONString(responseMsg);
            }
            //获取模板
            FCFileDocMain fcFileDocMain = fcFileDocMainMapper.selectByPrimaryKey("00000014");
            String filePath = fcFileDocMain.getFilePath();
            String filename = fcFileDocMain.getFileSaveName() + fcFileDocMain.getFileSuffix();
            File file = new File(filePath + filename);
            Workbook wb = WorkbookFactory.create(new FileInputStream(file));
            // 调用生成excel生成工具类,返回前台下载
            ServletOutputStream outputStream = response.getOutputStream();
            String filenameF = new String((fcFileDocMain.getFileName() + fcFileDocMain.getFileSuffix()).getBytes("UTF-8"), "iso-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + filenameF);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            // excel表格数据
            wb = insuredStuPlanSheet(wb, grpNo, map);
            wb.write(outputStream);
            outputStream.flush();
            responseMsg.okStatus().message("导出投保清单EXCEL成功!");
        } catch (Exception e) {
            log.info("导出投保清单EXCEL失败!", e);
            responseMsg.errorStatus().message("导出投保清单EXCEL失败!");
        }
        return JSON.toJSONString(responseMsg);
    }

    /**
     * 导出投保人清单excel>[团体被保险人清单]页(企事业单位)
     *
     * @param wb
     */
    public Workbook insuredPlanSheet(Workbook wb, String grpNo, Map<String, String> map) {
        map.put("grpNo", grpNo);
        map.put("ensureType", "0");
        log.info("insuredPlanSheet,开始,grpNo:{}, map: {}", grpNo, map);
        // 团体订单表
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectInsuredDetail(map);
        log.info("insuredPlanSheet,fcGrpOrder:{}", fcGrpOrder);

        if (fcGrpOrder != null) {
            // 保险计划sheet页
            Sheet inPlanSheet = (Sheet) wb.getSheetAt(0);
            inPlanSheet.setColumnWidth(1, 30 * 256);
            // 团体被保人清单sheet页
            Sheet insuredSheet = (Sheet) wb.getSheetAt(1);
            insuredSheet.setColumnWidth(8, 30 * 256);
            // 投保单号
            Row inPlanRow2 = inPlanSheet.getRow(1);
            Row insuredRow2 = insuredSheet.getRow(1);
            inPlanRow2.getCell(1).setCellValue(fcGrpOrder.getPrtNo());
            insuredRow2.getCell(2).setCellValue(fcGrpOrder.getPrtNo());
            // 团体投保人表
            FCGrpApplicant fcGrpApplicant = fcGrpOrder.getFcGrpApplicant();
            if (fcGrpApplicant != null) {
                // 投保企业名(投保人)
                inPlanRow2.getCell(4).setCellValue(fcGrpApplicant.getGrpName());
                insuredRow2.getCell(8).setCellValue(fcGrpApplicant.getGrpName());
            }
            // 订单表
            List<FCOrder> fcOrders = fcGrpOrder.getFcOrders();
            // 投保人数
            Integer ensureNum = 0;
            // 保险计划sheet页序号
            int planIndex = 0;
            // 团体被保人清单sheet页序号
            int insuredIndex = 0;
            int insuredIndexPlan = 0;
            List<String> planCodeList = new ArrayList<>();
            // 获取企业福利对应的支付方式 1-企业全缴 2-企业代缴 3-混合缴费
            Map params1 = new HashMap();
            params1.put("ensureCode", map.get("ensureCode"));
            params1.put("configNo", "008");
            log.info("insuredPlanSheet,params1:{}", params1);
            String payMethod = fcEnsureConfigMapper.selectOnlyValue(params1);
            log.info("insuredPlanSheet,payMethod:{}", payMethod);
            String payMethodName = "企业代扣代缴";
            if (payMethod.equals("3")) {
                payMethodName = "混合缴费";
            }
            for (FCOrder fcOrder : fcOrders) {
                // 子订单表
                List<FCOrderItem> fcOrderItems = fcOrder.getFcOrderItems();
                // 累加投保人数
                ensureNum += fcOrderItems.size();
                // 个人客户信息表
                FCPerInfo fcPerInfo = fcOrder.getFcPerInfo();
                for (FCOrderItem fcOrderItem : fcOrderItems) {
                    Row insuredRow = insuredSheet.createRow(planIndex + 4);
                    insuredRow.setHeightInPoints((float) 24.95);
                    CellStyle inCellStyle = this.cellStyleBorder(wb);
                    /**团体被保人清单*/
                    FCOrderInsured fcOrderInsured = fcOrderItem.getFcOrderInsured();
                    // 子订单产品要素详情表
                    FCOrderItemDetail fcOrderItemDetail = fcOrderItem.getFcOrderItemDetail();
                    if (fcOrderItemDetail != null) {
                        // 福利计划表
                        FCEnsurePlan fcEnsurePlan = fcOrderItemDetail.getFcEnsurePlan();
                        if (fcEnsurePlan == null) {
                            continue;
                        }
                        // 创建团体被保人清单页单元格
                        if (fcOrderInsured != null && fcEnsurePlan != null) {
                            for (int rowNum = 0; rowNum < 26; rowNum++) {
                                Cell cell = insuredRow.createCell(rowNum);
                                cell.setCellStyle(inCellStyle);
                            }
                        }
                        if (!planCodeList.contains(fcEnsurePlan.getPlanCode())) {
                            planCodeList.add(fcEnsurePlan.getPlanCode());
                            // 保险计划编码
                            if (fcEnsurePlan != null) {
                                // 序号
                                insuredIndexPlan = insuredIndexPlan + 1;
                                // 保障计划sheet页
                                wb = insuredDetailSheet(fcEnsurePlan, insuredIndexPlan, wb);
                            }
                        }
                    }
                    if (fcOrderInsured != null) {
                        planIndex = planIndex + 1;
                        insuredRow.getCell(0).setCellValue(planIndex);
                        if (fcPerInfo != null) {
                            // 员工姓名
                            insuredRow.getCell(1).setCellValue(fcPerInfo.getName());
                        }
                        // 被保人姓名
                        insuredRow.getCell(2).setCellValue(fcOrderInsured.getName());
                        // 被保人性别
                        String sex = fdCodeMapper.selectNameByCode("Sex", fcOrderInsured.getSex());
                        insuredRow.getCell(4).setCellValue(sex);
                        // 出生日期
                        insuredRow.getCell(5).setCellValue(fcOrderInsured.getBirthDay());
                        // 证件类型
                        insuredRow.getCell(6).setCellValue(fcOrderInsured.getIDType());
                        // 证件号
                        insuredRow.getCell(7).setCellValue(fcOrderInsured.getIDNo());
                        //计划编码
                        FCEnsurePlan fcEnsurePlan = fcOrderItemDetail.getFcEnsurePlan();
                        insuredRow.getCell(8).setCellValue(fcEnsurePlan.getPlanCode());
                        // 职业类别
                        insuredRow.getCell(9).setCellValue(fcOrderInsured.getOccupationType());
                        // 职业代码
                        insuredRow.getCell(10).setCellValue(fcOrderInsured.getOccupationCode());
                        // 有无医保
                        String joinMedProtect = fcOrderInsured.getJoinMedProtect();
                        if ("0".equals(joinMedProtect)) {
                            joinMedProtect = "否";
                        } else if ("1".equals(joinMedProtect)) {
                            joinMedProtect = "是";
                        } else {
                            joinMedProtect = "";
                        }
                        insuredRow.getCell(11).setCellValue(joinMedProtect);
                        // 手机号
                        insuredRow.getCell(13).setCellValue(fcOrderInsured.getMobilePhone());
                        // email
                        insuredRow.getCell(14).setCellValue(fcOrderInsured.getEMail());
                        //缴费方式
                        insuredRow.getCell(15).setCellValue(payMethodName);
                        //企业缴纳
                        insuredRow.getCell(16).setCellValue(fcOrderItem.getGrpPrem());
                        //个人缴纳
                        insuredRow.getCell(17).setCellValue(fcOrderItem.getSelfPrem());

                        //扣款缴费银行
                        insuredRow.getCell(18).setCellValue(fcPerInfo.getOpenBank());
                        //扣款缴费账户名
                        insuredRow.getCell(19).setCellValue(fcPerInfo.getName());
                        //扣款缴费银行账号
                        insuredRow.getCell(20).setCellValue(fcPerInfo.getOpenAccount());

                        FCStaffFamilyRela fcStaffFamilyRela = fcOrderInsured.getFcStaffFamilyRela();
                        if (fcStaffFamilyRela != null) {
                            // 与员工关系
                            String relation = fdCodeMapper.selectNameByCode("Relation", fcStaffFamilyRela.getRelation());
                            insuredRow.getCell(3).setCellValue(relation);
                        }
                        // 身故受益人姓名
                        insuredRow.getCell(21).setCellValue("法定继承人");
                    }
                }
            }
            // 投保人数
            inPlanRow2.getCell(9).setCellValue(ensureNum);
            insuredRow2.getCell(15).setCellValue(ensureNum);
        }
        return wb;
    }

    /**
     * 导出投保人清单excel>[团体被保险人清单]页(学生投保)
     *
     * @param wb
     */
    public Workbook insuredStuPlanSheet(Workbook wb, String grpNo, Map<String, String> stuMap) {
        String payType = "";
        stuMap.put("grpNo", grpNo);
        stuMap.put("ensureType", "1");
        // 团体订单表
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectInsuredDetail(stuMap);
        //获取缴费方式
        List<Map<String, String>> payTypeList = fcGrpOrderMapper.getPayType(grpNo, stuMap.get("ensureCode"));
        if (payTypeList == null || payTypeList.size() == 0) {
            payType = "企业代扣代缴";
        } else {
            Map<String, String> map = payTypeList.get(0);
            if (map != null) {
                payType = map.get("codeName");
            } else {
                payType = "企业代扣代缴";
            }
        }
        if (fcGrpOrder != null) {
            // 保险计划sheet页
            Sheet inPlanSheet = (Sheet) wb.getSheetAt(0);
            inPlanSheet.setColumnWidth(1, 30 * 256);
            // 团体被保人清单sheet页
            Sheet insuredSheet = (Sheet) wb.getSheetAt(1);
            insuredSheet.setColumnWidth(8, 30 * 256);
            // 投保单号
            Row inPlanRow2 = inPlanSheet.getRow(1);
            Row insuredRow2 = insuredSheet.getRow(1);
            inPlanRow2.getCell(1).setCellValue(fcGrpOrder.getPrtNo());
            insuredRow2.getCell(2).setCellValue(fcGrpOrder.getPrtNo());
            // 团体投保人表
            FCGrpApplicant fcGrpApplicant = fcGrpOrder.getFcGrpApplicant();
            if (fcGrpApplicant != null) {
                // 投保企业名(投保人)
                inPlanRow2.getCell(4).setCellValue(fcGrpApplicant.getGrpName());
                insuredRow2.getCell(8).setCellValue(fcGrpApplicant.getGrpName());
            }
            // 订单表
            List<FCOrder> fcOrders = fcGrpOrder.getFcOrders();
            // 投保人数
            Integer ensureNum = 0;
            // 保险计划sheet页序号
            int planIndex = 0;
            // 团体被保人清单sheet页序号
            int insuredIndex = 0;
            int insuredIndexPlan = 0;
            List<String> planCodeList = new ArrayList<>();
            for (FCOrder fcOrder : fcOrders) {
                // 子订单表
                List<FCOrderItem> fcOrderItems = fcOrder.getFcOrderItems();
                // 累加投保人数
                ensureNum += fcOrderItems.size();
                // 个人客户信息表
                FCPerInfo fcPerInfo = fcOrder.getFcPerInfo();
                for (FCOrderItem fcOrderItem : fcOrderItems) {
                    Row insuredRow = insuredSheet.createRow(planIndex + 4);
                    insuredRow.setHeightInPoints((float) 24.95);
                    CellStyle inCellStyle = this.cellStyleBorder(wb);
                    /**团体被保人清单*/
                    FCOrderInsured fcOrderInsured = fcOrderItem.getFcOrderInsured();
                    // 子订单产品要素详情表
                    FCOrderItemDetail fcOrderItemDetail = fcOrderItem.getFcOrderItemDetail();
                    if (fcOrderItemDetail != null) {
                        // 福利计划表
                        FCEnsurePlan fcEnsurePlan = fcOrderItemDetail.getFcEnsurePlan();
                        if (fcEnsurePlan == null) {
                            continue;
                        }
                        // 创建团体被保人清单页单元格
                        if (fcOrderInsured != null) {
                            for (int rowNum = 0; rowNum < 26; rowNum++) {
                                Cell cell = insuredRow.createCell(rowNum);
                                cell.setCellStyle(inCellStyle);
                            }
                        }
                        insuredRow.getCell(8).setCellValue(fcEnsurePlan.getPlanCode());
                        if (!planCodeList.contains(fcEnsurePlan.getPlanCode())) {
                            planCodeList.add(fcEnsurePlan.getPlanCode());
                            // 保险计划编码
                            if (fcEnsurePlan != null) {
                                // 序号
                                insuredIndexPlan = insuredIndexPlan + 1;
                                // 保障计划sheet页
                                wb = insuredDetailSheet(fcEnsurePlan, insuredIndexPlan, wb);
                            }
                        }
                    }
                    if (fcOrderInsured != null) {
                        planIndex = planIndex + 1;
                        insuredRow.getCell(0).setCellValue(planIndex);
                        if (fcPerInfo != null) {
                            // 员工姓名
                            insuredRow.getCell(1).setCellValue(fcPerInfo.getName());
                        }
                        // 被保人姓名
                        insuredRow.getCell(2).setCellValue(fcOrderInsured.getName());
                        // 被保人性别
                        String sex = fdCodeMapper.selectNameByCode("Sex", fcOrderInsured.getSex());
                        insuredRow.getCell(4).setCellValue(sex);
                        // 出生日期
                        insuredRow.getCell(5).setCellValue(fcOrderInsured.getBirthDay());
                        // 证件类型
                        insuredRow.getCell(6).setCellValue(fcOrderInsured.getIDType());
                        // 证件号
                        insuredRow.getCell(7).setCellValue(fcOrderInsured.getIDNo());
                        // 职业类别
                        insuredRow.getCell(9).setCellValue(fcOrderInsured.getOccupationType());
                        // 职业代码
                        insuredRow.getCell(10).setCellValue(fcOrderInsured.getOccupationCode());
                        // 有无医保
                        String joinMedProtect = fcOrderInsured.getJoinMedProtect();
                        if ("0".equals(joinMedProtect)) {
                            joinMedProtect = "否";
                        } else if ("1".equals(joinMedProtect)) {
                            joinMedProtect = "是";
                        } else {
                            joinMedProtect = "";
                        }
                        insuredRow.getCell(11).setCellValue(joinMedProtect);
                        // 手机号
                        insuredRow.getCell(13).setCellValue(fcOrderInsured.getMobilePhone());
                        // email
                        insuredRow.getCell(14).setCellValue(fcOrderInsured.getEMail());
                        //缴费方式
                        insuredRow.getCell(15).setCellValue(payType);
                        //企业缴纳
                        insuredRow.getCell(16).setCellValue(fcOrderItem.getGrpPrem());
                        //个人缴纳
                        insuredRow.getCell(17).setCellValue(fcOrderItem.getSelfPrem());
                        FCStaffFamilyRela fcStaffFamilyRela = fcOrderInsured.getFcStaffFamilyRela();
                        if (fcStaffFamilyRela != null) {
                            // 与员工关系
                            String relation = fdCodeMapper.selectNameByCode("Relation", fcStaffFamilyRela.getRelation());
                            insuredRow.getCell(3).setCellValue(relation);
                        }
                    }
                    // 身故受益人姓名
                    insuredRow.getCell(21).setCellValue("法定继承人");
                }
            }
            // 投保人数
            inPlanRow2.getCell(9).setCellValue(ensureNum);
            insuredRow2.getCell(15).setCellValue(ensureNum);
        }
        return wb;
    }

    /**
     * 表格样式设置
     *
     * @param wb
     * @return
     */
    private CellStyle cellStyleBorder(Workbook wb) {
        CellStyle inCellStyle = wb.createCellStyle();
        inCellStyle.setBorderBottom(BorderStyle.THIN);
        inCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderLeft(BorderStyle.THIN);
        inCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderRight(BorderStyle.THIN);
        inCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderTop(BorderStyle.THIN);
        inCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        return inCellStyle;
    }


    /**
     * 导出计划清单>[保险计划表]页
     *
     * @param fcEnsurePlan
     * @param insuredIndex
     * @param wb
     * @return
     */
    private Workbook insuredDetailSheet(FCEnsurePlan fcEnsurePlan, int insuredIndex, Workbook wb) {
        Row planRow = wb.getSheetAt(0).createRow(insuredIndex + 4);
        planRow.setHeightInPoints((float) 21.95);
        CellStyle inCellStyle = this.cellStyleBorder(wb);
        // 创建保险计划表单元格
        for (int rowNum = 0; rowNum < 64; rowNum++) {
            Cell cell = planRow.createCell(rowNum);
            cell.setCellStyle(inCellStyle);
        }
        planRow.getCell(0).setCellValue(insuredIndex);
        // 保险计划编码
        planRow.getCell(1).setCellValue(fcEnsurePlan.getPlanCode());
        // 保费合计
        planRow.getCell(63).setCellValue(fcEnsurePlan.getTotalPrem());
        List<FCPlanRisk> fcPlanRisks = fcEnsurePlan.getFcPlanRisks();
        for (FCPlanRisk fcPlanRisk : fcPlanRisks) {
            // 险种编码
            String riskCode = fcPlanRisk.getRiskCode();
            List<FCPlanRiskDuty> fcPlanRiskDuties = fcPlanRisk.getFcPlanRiskDuties();
            if (!fcPlanRiskDuties.isEmpty()) {
                FCPlanRiskDuty fcPlanRiskDuty = fcPlanRiskDuties.get(0);
                // 保额
                String amnt = "" + fcPlanRiskDuty.getAmnt();
                amnt = new BigDecimal(amnt).toPlainString();
                //计划明细导出逻辑需要转化保额单位的险种
                if (riskCode.matches("^(12020|17030|15030|15040|16040|16490|17010|17050|15070)")) {
                    amnt = String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2));
                }
                // 保费
                String prem = "" + fcPlanRiskDuty.getPrem();
                // 免赔额
                String getLimit = "" + fcPlanRiskDuty.getGetLimit();
                // 赔付比例
                String getRatio = "" + fcPlanRiskDuty.getGetRatio();
                switch (riskCode) {
                    // 团体定期寿险（12020）
                    case "12020":
                        planRow.getCell(2).setCellValue(amnt);
                        planRow.getCell(3).setCellValue(prem);
                        break;
                    // 门诊急诊团体医疗保险（17030）
                    case "17030":
                        planRow.getCell(4).setCellValue(amnt);
                        planRow.getCell(5).setCellValue(prem);
                        planRow.getCell(6).setCellValue(getLimit);
                        planRow.getCell(7).setCellValue(getRatio);
                        break;
                    // 团体意外伤害保险
                    case "15030":
                        planRow.getCell(8).setCellValue(amnt);
                        planRow.getCell(9).setCellValue(prem);
                        break;
                    // 意外伤害团体医疗保险
                    case "15040":
                        planRow.getCell(10).setCellValue(amnt);
                        planRow.getCell(11).setCellValue(prem);
                        planRow.getCell(12).setCellValue(getLimit);
                        planRow.getCell(13).setCellValue(getRatio);
                        break;
                    // 意外伤害住院津贴团体医疗保险
                    case "15060":
                        for (FCPlanRiskDuty fcPlanRiskDuty2 : fcPlanRiskDuties) {
                            String dutyCode = fcPlanRiskDuty2.getDutyCode();
                            // 保额
                            String amnt15060 = "" + fcPlanRiskDuty2.getAmnt();
                            // 保费
                            String prem15060 = "" + fcPlanRiskDuty2.getPrem();
                            // 最大免赔天数 -- add by wdz 2020.11.17
                            String maxGetDay15060 = StringUtils.isEmpty(String.valueOf(fcPlanRiskDuty2.getMaxGetDay())) ? "" : String.valueOf(fcPlanRiskDuty2.getMaxGetDay());
                            switch (dutyCode) {
                                // 意外伤害住院津贴保险责任GD0029
                                case "GD0029":
                                    planRow.getCell(14).setCellValue(amnt15060);
                                    planRow.getCell(15).setCellValue(prem15060);
                                    // 最大免赔天数
                                    planRow.getCell(16).setCellValue(maxGetDay15060);
                                    break;
                                // "意外伤害重症住院津贴保险责任（可选责任）GD0030"
                                case "GD0030":
                                    planRow.getCell(17).setCellValue(amnt15060);
                                    planRow.getCell(18).setCellValue(prem15060);
                                    // 最大免赔天数
                                    planRow.getCell(19).setCellValue(maxGetDay15060);
                                    break;
                                default:
                                    break;
                            }
                        }
                        break;
                    // 团体重大疾病保险
                    case "16040":
                        planRow.getCell(20).setCellValue(amnt);
                        planRow.getCell(21).setCellValue(prem);
                        break;
                    // 琴逸团体重大疾病保险
                    case "16490":
                        planRow.getCell(22).setCellValue(amnt);
                        planRow.getCell(23).setCellValue(prem);
                        break;
                    // 住院津贴团体医疗保险
                    case "17020":
                        for (FCPlanRiskDuty fcPlanRiskDuty2 : fcPlanRiskDuties) {
                            String dutyCode = fcPlanRiskDuty2.getDutyCode();
                            // 保额
                            String amnt17020 = "" + fcPlanRiskDuty2.getAmnt();
                            // 保费
                            String prem17020 = "" + fcPlanRiskDuty2.getPrem();
                            // 最大免赔天数
                            String maxGetDay17020 = StringUtils.isEmpty(String.valueOf(fcPlanRiskDuty2.getMaxGetDay())) ? "" : String.valueOf(fcPlanRiskDuty2.getMaxGetDay());
                            switch (dutyCode) {
                                // 一般住院日额津贴保险金GD0032
                                case "GD0032":
                                    planRow.getCell(24).setCellValue(amnt17020);
                                    planRow.getCell(25).setCellValue(prem17020);
                                    planRow.getCell(26).setCellValue(maxGetDay17020);
                                    break;
                                // 癌症住院日额津贴保险金（可选责任）GD0033
                                case "GD0033":
                                    planRow.getCell(27).setCellValue(amnt17020);
                                    planRow.getCell(28).setCellValue(prem17020);
                                    planRow.getCell(29).setCellValue(maxGetDay17020);
                                    break;
                                // 重症监护日额津贴保险金（可选责任）GD0034
                                case "GD0034":
                                    planRow.getCell(30).setCellValue(amnt17020);
                                    planRow.getCell(31).setCellValue(prem17020);
                                    planRow.getCell(32).setCellValue(maxGetDay17020);
                                    break;
                                // 手术医疗津贴保险金（可选责任）GD0035
                                case "GD0035":
                                    Double amount17020 = CommonUtil.div(Double.valueOf(amnt17020), 10000.0, 2);
                                    planRow.getCell(33).setCellValue(String.valueOf(amount17020));
                                    planRow.getCell(34).setCellValue(prem17020);
                                    // update by wudezhong 2020.11.27 EAST需求变更
                                    //planRow.getCell(33).setCellValue(maxGetDay17020);
                                    break;
                                default:
                                    break;
                            }
                        }
                        break;
                    // 住院团体医疗保险（17010）
                    case "17010":
                        planRow.getCell(35).setCellValue(amnt);
                        planRow.getCell(36).setCellValue(prem);
                        // 免赔额
                        planRow.getCell(37).setCellValue(getLimit);
                        // 赔付比例
                        planRow.getCell(38).setCellValue(getRatio);
                        break;
                    // 尊享团体补充医疗保险（17050）
                    case "17050":
                        Double sumAmnt = 0.00;
                        Double sumPrem = 0.00;
                        for (FCPlanRiskDuty fcPlanRiskDuty2 : fcPlanRiskDuties) {
                            // 保额
                            String amnt15070 = "" + fcPlanRiskDuty2.getAmnt();
                            sumAmnt += Double.valueOf(amnt15070);
                            // 保费
                            String prem15070 = "" + fcPlanRiskDuty2.getPrem();
                            sumPrem += Double.valueOf(prem15070);
                        }
                        sumAmnt = CommonUtil.div(Double.valueOf(sumAmnt), 10000.0, 2);
                        planRow.getCell(39).setCellValue(sumAmnt);
                        planRow.getCell(40).setCellValue(sumPrem);
                        // 免赔额
                        planRow.getCell(41).setCellValue(getLimit);
                        // 赔付比例
                        planRow.getCell(42).setCellValue(getRatio);
                        break;
                    // 综合交通团体意外伤害保险（15070）
                    case "15070":
                        for (FCPlanRiskDuty fcPlanRiskDuty2 : fcPlanRiskDuties) {
                            String dutyCode = fcPlanRiskDuty2.getDutyCode();
                            // 保额
                            String amnt15070 = "" + fcPlanRiskDuty2.getAmnt();
                            //单位转换
                            amnt15070 = String.valueOf(CommonUtil.div(Double.valueOf(amnt15070), 10000.0, 2));
                            // 保费
                            String prem15070 = "" + fcPlanRiskDuty2.getPrem();
                            switch (dutyCode) {
                                // 公路公共交通工具保险金GD0050(可选责任)
                                case "GD0050":
                                    planRow.getCell(43).setCellValue(amnt15070);
                                    planRow.getCell(44).setCellValue(prem15070);
                                    break;
                                // 轨道交通工具保险金GD0051(可选责任)
                                case "GD0051":
                                    planRow.getCell(44).setCellValue(amnt15070);
                                    planRow.getCell(45).setCellValue(prem15070);
                                    break;
                                // 水路公共交通工具保险金GD0052(可选责任)
                                case "GD0052":
                                    planRow.getCell(47).setCellValue(amnt15070);
                                    planRow.getCell(48).setCellValue(prem15070);
                                    break;
                                // 民航班机保险金GD0053(可选责任)
                                case "GD0053":
                                    planRow.getCell(49).setCellValue(amnt15070);
                                    planRow.getCell(50).setCellValue(prem15070);
                                    break;
                                // 私家车或公务车保险金GD0054(可选责任)
                                case "GD0054":
                                    planRow.getCell(51).setCellValue(amnt15070);
                                    planRow.getCell(52).setCellValue(prem15070);
                                    break;
                                // 公路公共意外伤害医疗保险金GD0055(可选责任)
                                case "GD0055":
                                    planRow.getCell(53).setCellValue(amnt15070);
                                    planRow.getCell(54).setCellValue(prem15070);
                                    break;
                                // 轨道交通意外伤害医疗保险金GD0056(可选责任)
                                case "GD0056":
                                    planRow.getCell(55).setCellValue(amnt15070);
                                    planRow.getCell(56).setCellValue(prem15070);
                                    break;
                                // 水路公共交通意外伤害医疗保险金GD0057(可选责任)
                                case "GD0057":
                                    planRow.getCell(57).setCellValue(amnt15070);
                                    planRow.getCell(58).setCellValue(prem15070);
                                    break;
                                // 民航班机意外伤害医疗保险金GD0058(可选责任)
                                case "GD0058":
                                    planRow.getCell(59).setCellValue(amnt15070);
                                    planRow.getCell(60).setCellValue(prem15070);
                                    break;
                                // 私家车或公务车意外伤害医疗保险金GD0059(可选责任)
                                case "GD0059":
                                    planRow.getCell(61).setCellValue(amnt15070);
                                    planRow.getCell(62).setCellValue(prem15070);
                                    break;
                                default:
                                    break;
                            }
                        }
                    default:
                        break;
                }
            }
        }
        return wb;
    }

    /**
     * 根据令牌获取企业客户号
     *
     * @param requestMap
     */
    private Map<String, Object> getGrpNoByToken(Map<String, Object> requestMap) {
        // 根据token获取企业客户号
        GlobalInput globalInput = userService.getSession((String) requestMap.get("Authorization"));
        String grpNo = globalInput.getGrpNo();
        requestMap.put("grpNo", grpNo);
        return requestMap;
    }

    /**
     * 分页参数合理化
     *
     * @param requestMap
     */
    private void pageInit(Map<String, Object> requestMap) {
        int pageNum = requestMap.get("pageNum") == null ? 1 : (int) requestMap.get("pageNum");
        int pageSize = requestMap.get("pageSize") == null ? 0 : (int) requestMap.get("pageSize");
        PageHelper.startPage(pageNum, pageSize);
    }

    /**
     * <AUTHOR>
     * @description查询员工信息及家属信息
     * @date 9:52 9:52
     * @modified
     */
    public String familySelect(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        // 判断前台传递的数据是否为空
        if (token == null || "".equals(token)) {
            resultMap.put("message", "用户传入的数据为空");
            return JSON.toJSONString(resultMap);
        }
        GlobalInput globalInput = userService.getSession(token);
        try {
            // 通过客户号来获取联系人及家属性名
            HashMap<String, Object> fcStaffFamilyRela = welfareQueryMapper.selectfamilyInfo(globalInput.getCustomNo());
            if (fcStaffFamilyRela != null) {
                resultMap.put("fcStaffFamilyRela", fcStaffFamilyRela);
                resultMap.put("code", "200");
                resultMap.put("message", "查询成功");
            }
        } catch (Exception e) {
            log.info("查询员工信息及家属信息失败: ", e);
            resultMap.put("false", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description查询员工及家属的福利
     * @date 10:49 10:49
     * @modified
     */
    public String empAndFamilyWelfareSelect(String personId, String Authorization) {
        Map<String, Object> resultMap = new HashMap<>();
        // 判断前台传递的数据是否为空
        if (Authorization == null || "".equals(Authorization)) {
            resultMap.put("message", "用户的数据为空");
            return JSON.toJSONString(resultMap);
        }
        if (personId == null || "".equals(personId)) {
            resultMap.put("message", "传入的个人编号不能为空");
            return JSON.toJSONString(resultMap);
        }
        try {
            GlobalInput globalInput = userService.getSession(Authorization);
            // 获取福利编号
            String EnsureCode = globalInput.getEnsureCode();
            List<Map<String, String>> fcStaffFamilyRelaList = welfareQueryMapper.findFamilyName(personId);
            for (Map<String, String> map : fcStaffFamilyRelaList) {
                String productCode = map.get("ProductCode");
                // 关系
                String relation = map.get("Relation");
                // 姓名
                String name = map.get("Name");
                resultMap.put("name", name);
                resultMap.put("relation", relation);
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectPlanDetail(EnsureCode, productCode);
                // 计划名
                resultMap.put("planName", fcEnsurePlan.getPlanName());
                // 总保费
                resultMap.put("TotalPrem", fcEnsurePlan.getTotalPrem());
                Map<String, Double> defaultPlanMap = fcDefaultPlanMapper.selectPayment(personId, EnsureCode);
                if (defaultPlanMap == null) {
                    resultMap.put("message", "计划查询数据不存在");
                    return JSON.toJSONString(resultMap);
                }
                // 总保费
                Double totalPrem = fcEnsurePlan.getTotalPrem();
                // 企业默认缴费
                Double defaultPrem = defaultPlanMap.get("totalPrem");
                // 个人缴费
                Double perPrem = totalPrem - defaultPrem;
                resultMap.put("defaultPrem", defaultPrem);
                resultMap.put("perPrem", perPrem);
                List<FCPlanRisk> fcPlanRisks = fcEnsurePlan.getFcPlanRisks();
                List<Map<String, Object>> riskList = new ArrayList<Map<String, Object>>();
                for (FCPlanRisk fcPlanRisk : fcPlanRisks) {
                    Map<String, Object> riskMap = new HashMap<String, Object>(16);
                    List<FCPlanRiskDuty> fcPlanRiskDuties = fcPlanRisk.getFcPlanRiskDuties();
                    for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDuties) {
                        riskMap.put("RiskName", fcPlanRisk.getRiskName());
                        riskMap.put("Amnt", fcPlanRiskDuty.getAmnt());
                        riskMap.put("DutyName", fcPlanRiskDuty.getDutyName());
                    }
                    riskList.add(riskMap);
                }
                resultMap.put("risk", riskList);
            }
            resultMap.put("code", "200");
            resultMap.put("message", "查询成功");
        } catch (Exception e) {
            log.info("查询员工及家属的福利失败: ", e);
            resultMap.put("false", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询计划编码是否存在
     *
     * @param planCode
     * @param ensureCode
     * @return
     */
    public String selectdefultPlanCode(String planCode, String ensureCode) {
        ResponseMsg<Boolean> responseMsg = new ResponseMsg<>();
        try {
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("ensureCode", ensureCode);
            param.put("planCode", planCode);
            Integer PlanCodeSum = fcEnsurePlanMapper.existdefultPlanCode(param);
            if (PlanCodeSum == 0) {
                responseMsg.okStatus().message("默认计划编码不存在").data(false);
                return JSON.toJSONString(responseMsg);
            }
            responseMsg.okStatus().message("默认计划编码存在").data(true);
        } catch (Exception e) {
            log.error("默认计划编码查询失败", e);
            responseMsg.errorStatus().message("默认计划编码查询失败");
        }
        return JSON.toJSONString(responseMsg);
    }

    /**
     * <AUTHOR>
     * @description申请合同
     * @date 17:34 17:34
     * @modified
     */
    public String referContract(String token, FCContract fcContract, String grpContNo, String edorAppNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (!CheckUtils.checkMobilePhone(fcContract.getTelPhone())) {
                resultMap.put("code", "400");
                resultMap.put("message", "联系人手机格式错误，请重新录入。");
                return JSON.toJSONString(resultMap);
            }
            GlobalInput globalInput = userService.getSession(token);
            //调用核心接口
            RemoteDelegate rd = RemoteDelegate.getInstance();
            log.info("HR补发纸质合同申请接口调用时间：" + DateTimeUtil.getCurrentDateTime());
            String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
                    "\t\t<TestFlag>test01</TestFlag>\n" +
                    "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                    "\t\t<TransType>YUG011</TransType>\n" +
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                    "\t\t<ClientIp>**************</ClientIp>\n" +
                    "\t\t<FuncFlag>YUG011</FuncFlag>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<GrpContNo>" + grpContNo + "</GrpContNo>\n" +
                    "\t\t<EdorAppNo>" + edorAppNo + "</EdorAppNo>\n" +
                    "\t\t<EdorType>LR</EdorType>\n" +
                    "\t\t<EdorAppDate>" + DateTimeUtil.getCurrentDate() + "</EdorAppDate>\n" +
                    "\t\t<NeedGetMoney>N</NeedGetMoney>\n" +
                    "\t</BODY>\n" +
                    "</RequestInfo>\n";
            log.info("调用接口请求报文：" + reqXml);
            long startStamp = System.currentTimeMillis();
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "BenefitService", reqXml);
            long endStamp = System.currentTimeMillis();
            log.info("调用接口用时：" + (endStamp - startStamp));
            if (success) {
                Map<String, Object> responseXML = rd.getResult();
                Body body = (Body) responseXML.get("Body");
                if ("0".equals(body.getEdorFlag())) {
                    resultMap.put("data", body);
                    //插入数据到合同表
                    String location = fcContract.getProvince() + "-" + fcContract.getCity() + "-" + fcContract.getArea() + "-"
                            + fcContract.getAddress();
                    fcContract.setLocation(location);
                    fcContract.setGrpNo(globalInput.getGrpNo());
                    String contractSN = maxNoService.createMaxNo("fcContract", null, 20);
                    fcContract.setContractSN(contractSN);
                    fcContract.setOperator(globalInput.getUserNo());
                    fcContract.setApplicantName(globalInput.getName());
                    fcContract = (FCContract) CommonUtil.initObject(fcContract, "INSERT");
                    fcContractMapper.insert(fcContract);
                    resultMap.put("code", "200");
                    resultMap.put("message", "补发纸质合同申请成功");
                } else {
                    resultMap.put("code", "500");
                    resultMap.put("message", "补发纸质合同申请失败");
                    return JSON.toJSONString(resultMap);
                }
            } else {
                resultMap.put("code", "500");
                resultMap.put("message", "补发纸质合同申请接口调用失败");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("提交失败：" + e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("message", "提交失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description申请记录
     * @date 17:08 17:08
     * @modified
     */
    public String applicationRecord(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        Map<String, Object> map = new HashMap<>();
        map.put("grpNo", globalInput.getGrpNo());
        map.put("customType", globalInput.getCustomType());
        map.put("ensureCode", ensureCode);
        //合同列表
        List<FCContract> mapList = fcContractMapper.selectContract(map);
        for (FCContract fcContract : mapList) {
            String dateTime = "<p>" + fcContract.getMakeDate() + "</p>" + " " + "<p>" + fcContract.getMakeTime() + "</p>";
            fcContract.setDateTime(dateTime);
        }
        PageHelperUtil<FCContract> teamPageInfo = new PageHelperUtil<>(mapList);
        resultMap.put("contractList", teamPageInfo.getList());
        //发票列表
        List<FCMailInfo> listMap = fcMailInfoMapper.getMailInfoByPersonId(map);
        for (FCMailInfo fcMailInfo : listMap) {
            String dateTime = "<p>" + fcMailInfo.getMakeDate() + "</p>" + " " + "<p>" + fcMailInfo.getMakeTime() + "</p>";
            fcMailInfo.setDateTime(dateTime);
        }
        PageHelperUtil<FCMailInfo> teamPage = new PageHelperUtil<>(listMap);
        resultMap.put("MailList", teamPage.getList());
        resultMap.put("code", "200");
        resultMap.put("message", "查询成功");
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description查询地域
     * @date 11:30 11:30
     * @modified
     */
    public String getLocationMessage(String placeType, String upplaceCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("placeType", placeType);
            map.put("upplaceCode", upplaceCode);
            List<Map<String, Object>> mapList = fdPlaceMapper.selectCodeType(map);
            for (Map<String, Object> codeMap : mapList) {
                List<Object> cityMap = new ArrayList<>();
                codeMap.put("cities", cityMap);
            }
            resultMap.put("mapList", mapList);
            resultMap.put("code", "200");
            resultMap.put("message", "查询地域成功");
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }


    public void downloadContractOrVoucherFile(HttpServletResponse response, String contno, String ruleCode) {
        try {
            String ip = myProps.getCASystem().get("caHost");//服务ip
            int port = Integer.parseInt(myProps.getCASystem().get("caPort"));//服务端口
            EDMSClientTool edmsClientTool = new EDMSClientTool(ip, port);
            log.info("单证编号" + contno);
            log.info("单证规则编号" + ruleCode);
            ReqMessage reqMessage = new ReqMessage();
            reqMessage.setTransRefGUID(CommonUtil.getUUID());
            reqMessage.setDocumentNum(contno);
            reqMessage.setDocumentRuleNum(ruleCode);
            ChannelMessage message = edmsClientTool.downPdfDocument(reqMessage);
            log.info("状态码：" + message.getStatusCode());
            log.info("状态信息：" + message.getStatusInfo());
            if ("200".equals(message.getStatusCode())) {// 成功
                // 清空response
                response.reset();
                // 设置response的Header
                response.addHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode("团体合同（或个人凭证）.pdf", "UTF-8"));
                OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType("application/octet-stream");
                toClient.write(message.getBody());
                toClient.flush();
                toClient.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * HR补发纸质合同预申请
     *
     * @param token
     * @return
     */
    public String ReplacementPreApplication(String token, String grpContNo) {
        Map<String, Object> resultMap = new HashMap<>();

        //判断该福利是否过期
        FCEnsure fcEnsure = fcEnsureMapper.getFcensureByGrpContNo(grpContNo);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date policyEndDates = sf.parse(fcEnsure.getPolicyEndDate());
            if (!(new Date().before(policyEndDates))) {
                resultMap.put("code", "400");
                resultMap.put("message", "保险合同日期已过，无法进行纸质合同补发申请");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("报错信息：" + e.getMessage());
        }

        try {
            RemoteDelegate rd = RemoteDelegate.getInstance();
            log.info("HR补发纸质合同预申请接口调用时间：" + DateTimeUtil.getCurrentDateTime());
            String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
                    "\t\t<TestFlag>test01</TestFlag>\n" +
                    "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                    "\t\t<TransType>YUG010</TransType>\n" +
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                    "\t\t<ClientIp>**************</ClientIp>\n" +
                    "\t\t<FuncFlag>YUG010</FuncFlag>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<GrpContNo>" + grpContNo + "</GrpContNo>\n" +
                    "\t\t<EdorType>LR</EdorType>\n" +
                    "\t\t<EdorAppDate>" + DateTimeUtil.getCurrentDate() + "</EdorAppDate>\n" +
                    "\t\t<NeedGetMoney>N</NeedGetMoney>\n" +
                    "\t</BODY>\n" +
                    "</RequestInfo>";
            ;
            log.info("调用接口请求报文：" + reqXml);
            long startStamp = System.currentTimeMillis();
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "BenefitService", reqXml);
            long endStamp = System.currentTimeMillis();
            log.info("调用接口用时：" + (endStamp - startStamp));
            if (success) {
                Map<String, Object> responseXML = rd.getResult();
                Body body = (Body) responseXML.get("Body");
                if ("0".equals(body.getEdorFlag())) {
                    resultMap.put("data", body);
                    resultMap.put("code", "200");
                    resultMap.put("message", "补发纸质合同预申请成功");
                } else {
                    resultMap.put("code", "400");
                    resultMap.put("message", body.getEdorMark());
                }
            } else {
                resultMap.put("code", "500");
                resultMap.put("message", "补发纸质合同预申请接口调用失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("补发纸质合同预申请失败：" + e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("message", "补发纸质合同预申请失败");
        }
        return JSON.toJSONString(resultMap);
    }

    public String checkDate(String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        //判断该福利是否过期
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date policyEndDates = sf.parse(fcEnsure.getPolicyEndDate());
            if (!(new Date().before(policyEndDates))) {
                resultMap.put("code", "400");
                resultMap.put("message", "保险合同日期已过，无法进行纸质发票申请");
                return JSON.toJSONString(resultMap);
            } else {
                resultMap.put("code", "200");
                resultMap.put("message", "保险合同未过期，可进行纸质发票申请");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("错误信息：" + e.getMessage());
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 企业方案汇总--弹性计划
     *
     * @param ensureCode
     * @return
     */
    public String getEfleRiskCollection(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            //员工所投险种
            List<Map<String, Object>> staRiskMapList = fcDutyAmountGradeMapper.getRiskInfoByEnsureCode(ensureCode);
            if (staRiskMapList.size() > 0) {
                staRiskMapList = reckonRiskDutyInfo("0", ensureCode, staRiskMapList);
            }
            //家属所投险种
            List<Map<String, Object>> famRiskMapList = fcDutyAmountGradeMapper.getRiskInfoByEnsureCodeFamily(ensureCode);
            if (staRiskMapList.size() > 0) {
                famRiskMapList = reckonRiskDutyInfo("1", ensureCode, famRiskMapList);
            }
            Map<String, Object> dataMap = new HashMap<>();
            if ("3".equals(fcEnsure.getPolicyState()) || "4".equals(fcEnsure.getPolicyState())) {
                List<HashMap<String, Object>> mapList = ensureRisk(ensureCode, token);
                PageHelperUtil<HashMap<String, Object>> teamPageInfo = new PageHelperUtil<>(mapList);
                dataMap.put("list", teamPageInfo.getList());
            }
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("data", dataMap);
            resultMap.put("staRiskMapList", staRiskMapList);
            resultMap.put("famRiskMapList", famRiskMapList);
            resultMap.put("message", "企业方案汇总成功。");
        } catch (Exception e) {
            log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "企业方案汇总查询失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 获取每个责任所投总保费及总人数
     *
     * @param check
     * @param ensureCode
     * @param mapList
     * @return
     */
    public List<Map<String, Object>> reckonRiskDutyInfo(String check, String ensureCode, List<Map<String, Object>> mapList) {
        for (Map<String, Object> mapInfo : mapList) {
            String riskCode = mapInfo.get("riskCode").toString();
            String dutyCode = mapInfo.get("dutyCode").toString();
            int countNumber = Integer.parseInt(mapInfo.get("countNumber").toString());
            //当前福利是否有人投保该责任
            if (countNumber > 0) {
                Map<String, String> parmerInfo = new HashMap<>();
                parmerInfo.put("ensureCode", ensureCode);
                parmerInfo.put("riskCode", riskCode);
                parmerInfo.put("dutyCode", dutyCode);
                parmerInfo.put("check", check);
                Map<String, Object> orderItemDutyInfo = new HashMap<>();
                //是否必选 0--比选
                if ("0".equals(mapInfo.get("dutyType"))) {
                    orderItemDutyInfo = fcOrderItemMapper.selectOrderDutyInfoByRelation(parmerInfo);
                } else if ("1".equals(mapInfo.get("dutyType"))) {
                    orderItemDutyInfo = fcOrderItemMapper.selectOrderOptDutyInfoByRelation(parmerInfo);
                }
                mapInfo.put("prem", orderItemDutyInfo.get("prem"));
                mapInfo.put("peoples", orderItemDutyInfo.get("peoples"));
            }
        }
        return mapList;
    }

    /**
     * 弹性计划---投保清单查询
     *
     * @param token
     * @param ensureCode
     * @param isCheck    区分员工/家属  0--员工  1--家属
     * @return
     */
    public String queryEfleInsuredDetail(String token, String ensureCode, String department, String garName, String garIDNo, String gradeLevelCode, String famName, String famIDNo, String isCheck) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("department", department);
            mapInfo.put("garName", garName);
            mapInfo.put("garIDNo", garIDNo);
            mapInfo.put("gradeLevelCode", gradeLevelCode);
            mapInfo.put("famName", famName);
            mapInfo.put("famIDNo", famIDNo);
            mapInfo.put("isCheck", isCheck);
            List<Map<String, String>> insuredMapList = fcOrderInsuredMapper.queryEfleInsuredDetail(mapInfo);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("data", insuredMapList);
            resultMap.put("message", "投保清单查询成功。");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "投保清单查询失败。");
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 弹性计划--投保清单详情查询
     *
     * @param token
     * @param orderItemNo
     * @return
     */
    public String queryInsureRiskInfoByOrderItemNo(String token, String orderItemNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            double sumPrem = 0.00;
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            List<Map<String, String>> fcPlanRiskDutyList = fpInsureEflexPlanMapper.selectDutyListByPersonID(orderItemNo);
            sumPrem = fcOrderItem.getGrpPrem() + fcOrderItem.getSelfPrem();
            resultMap.put("sumPrem", sumPrem);
            resultMap.put("grpPrem", fcOrderItem.getGrpPrem());
            resultMap.put("selfPrem", fcOrderItem.getSelfPrem());
            resultMap.put("data", fcPlanRiskDutyList);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "投保清单详情查询成功。");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info(e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "投保清单详情查询失败。");
            return JSON.toJSONString(resultMap);
        }
    }

    public String exportEfleInsuredInfoExcel(String Authorization, HttpServletResponse response, String ensureCode, String department, String garName, String garIDNo, String gradeLevelCode, String famName, String famIDNo, String isCheck) {
        ResponseMsg<Object> responseMsg = new ResponseMsg<>();
        try {
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("department", department);
            mapInfo.put("garName", garName);
            mapInfo.put("garIDNo", garIDNo);
            mapInfo.put("gradeLevelCode", gradeLevelCode);
            mapInfo.put("famName", famName);
            mapInfo.put("famIDNo", famIDNo);
            //isCheck 区分员工、家属  员工-- 0  家属 != 0
            mapInfo.put("isCheck", isCheck);
            FCFileDocMain fcFileDocMain = fcFileDocMainMapper.selectByPrimaryKey("00000019");
            String filePath = fcFileDocMain.getFilePath();
            String filename = fcFileDocMain.getFileSaveName() + fcFileDocMain.getFileSuffix();
            File file = new File(filePath + filename);
            Workbook wb = WorkbookFactory.create(new FileInputStream(file));
            // 调用生成excel生成工具类,返回前台下载
            ServletOutputStream outputStream = response.getOutputStream();
            String filenameF = new String((fcFileDocMain.getFileName() + fcFileDocMain.getFileSuffix()).getBytes("UTF-8"), "iso-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + filenameF);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            // excel表格数据
            wb = efleInsureInfoSheet(wb, mapInfo);
            wb.write(outputStream);
            outputStream.flush();
            responseMsg.okStatus().message("导出投保清单EXCEL成功!");
        } catch (Exception e) {
            log.info("导出投保清单EXCEL失败!", e);
            responseMsg.errorStatus().message("导出投保清单EXCEL失败!");
        }
        return JSON.toJSONString(responseMsg);
    }

    public Workbook efleInsureInfoSheet(Workbook wb, Map<String, String> mapInfo) {
        try {
            //获取证件类型和证件号码的Value-Key
            List<HashMap<String, Object>> idTypeCodeList = fdCodeMapper.CodeInfo("IDType");
            //团单
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(mapInfo.get("ensureCode"));
            //团体投保人
            FCGrpApplicant fcGrpApplicant = fcGrpApplicantMapper.selectByPrimaryKey(fcGrpOrder.getGrpAppNo());
            //缴费方式
            Map<String, Object> params = new HashMap<>();
            params.put("ensureCode", mapInfo.get("ensureCode"));
            params.put("configNo", "008");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(params);
            //投保清单sheet页
            Sheet staSheet = (Sheet) wb.getSheetAt(0);
            Row insureRow = staSheet.getRow(1);
            //投保单号
            insureRow.getCell(2).setCellValue(fcGrpOrder.getPrtNo());
            //投保人
            insureRow.getCell(4).setCellValue(fcGrpApplicant.getGrpName());
            //投保人数
            int rowIndex;
            List<Map<String, String>> insuredMapList = fcOrderInsuredMapper.queryEfleInsuredDetail(mapInfo);
            int perSum = insuredMapList.size();
            for (int i = 0; i < insuredMapList.size(); i++) {
                rowIndex = i + 5;
                Row row = staSheet.createRow(rowIndex);
                row.setHeightInPoints((float) 20);
                CellStyle inCellStyle = this.cellStyleBorder(wb);
                // 创建投保清单页单元格
                for (int rowNum = 0; rowNum < 84; rowNum++) {
                    Cell cell = row.createCell(rowNum);
                    cell.setCellStyle(inCellStyle);
                }
                row.getCell(0).setCellValue(i + 1);
                row.getCell(1).setCellValue(insuredMapList.get(i).get("garName"));
                row.getCell(2).setCellValue(insuredMapList.get(i).get("insureName"));
                String relation = fdCodeMapper.selectNameByCode("Relation", insuredMapList.get(i).get("relation"));
                row.getCell(3).setCellValue(relation);
                if ("0".equals(insuredMapList.get(i).get("sex"))) {
                    row.getCell(4).setCellValue("男");
                } else if ("1".equals(insuredMapList.get(i).get("sex"))) {
                    row.getCell(4).setCellValue("女");
                }
                row.getCell(5).setCellValue(insuredMapList.get(i).get("birthday"));
                String idTypeName = "";
                for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
                    if ((insuredMapList.get(i).get("idType")).equals(hashMap1.get("CodeKey"))) {
                        idTypeName = hashMap1.get("CodeName").toString();
                        break;
                    }
                }
                row.getCell(6).setCellValue(idTypeName);
                row.getCell(7).setCellValue(insuredMapList.get(i).get("idNo"));
                row.getCell(8).setCellValue(insuredMapList.get(i).get("occupationType"));
                row.getCell(9).setCellValue(insuredMapList.get(i).get("occupationCode"));
                if ("0".equals(insuredMapList.get(i).get("joinMedProtect"))) {
                    row.getCell(10).setCellValue("无");
                } else if ("1".equals(insuredMapList.get(i).get("joinMedProtect"))) {
                    row.getCell(10).setCellValue("有");
                }
                row.getCell(11).setCellValue(insuredMapList.get(i).get("mobilePhone"));
                row.getCell(12).setCellValue(insuredMapList.get(i).get("email"));
                //缴费方式
                String paymentType = fdCodeMapper.selectNameByCode("PaymentType", fcEnsureConfig.getConfigValue());
                row.getCell(13).setCellValue(paymentType);
                row.getCell(14).setCellValue(String.valueOf(insuredMapList.get(i).get("grpPrem")));
                row.getCell(15).setCellValue(String.valueOf(insuredMapList.get(i).get("selfPrem")));
                row.getCell(16).setCellValue(insuredMapList.get(i).get("openBank"));
                row.getCell(17).setCellValue(insuredMapList.get(i).get("openPer"));
                row.getCell(18).setCellValue(insuredMapList.get(i).get("openAccount"));
                //身故受益人姓名、与被保人关系、已投保身故保额总和
                row.getCell(19).setCellValue("法定继承人");
                row.getCell(20).setCellValue("");
                row.getCell(21).setCellValue("");
                Map<String, String> map = new HashMap<>();
                map.put("ensureCode", mapInfo.get("ensureCode"));
                map.put("personID", insuredMapList.get(i).get("personID"));
                FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectInsuredByPersonID(map);
                List<Map<String, String>> fcPlanRiskDutyList = fpInsureEflexPlanMapper.selectDutyListByPersonID(fcOrderInsured.getOrderItemNo());
                double sumsPrem = 0.00;
                sumsPrem = ensureAuditService.createRiskInfoRow(i, row, sumsPrem, fcPlanRiskDutyList);
                row.getCell(83).setCellValue(sumsPrem);
            }
            insureRow.getCell(6).setCellValue(perSum);
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return wb;
    }


    /**
     * 导出被保人清单(详细金额)
     */
    public String personsInsureList(String authorization, HttpServletResponse response, String ensureCode) {

        // 根据token获取企业客户号
        GlobalInput globalInput = userService.getSession(authorization);
        if (globalInput == null) {
            throw new RuntimeException("获取session失败!");
        }

        // 获取福利信息
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectByEnsureCodeKey(ensureCode);
        FcPrtandCoreRela fcPrtandCoreRela = fcPrtandCoreRelaMapper.selectByPrtNo(fcGrpOrder.getPrtNo());
        List<PersonsInsure> personsInsures = concatenationData2(ensureCode, fcEnsure, fcGrpOrder);

        long startTime = System.currentTimeMillis();
        Log.info("导出数据处理准备:>>>>>>>>>>>>>>>>>>>>>>>");
        // 创建数据集合，将数据添加到集合中

        for (PersonsInsure personsInsure : personsInsures) {
            for (PersonsInsure personInsured : personsInsures) {
                if (personInsured.getInsuredNo().equals(personsInsure.getMainInsuredNo())) {
                    personsInsure.setPolicyHolderName(personInsured.getName());
                    personsInsure.setPrintNumber(fcPrtandCoreRela.gettPrtNo());
                }
            }
        }
        long endTime = System.currentTimeMillis();
        Log.info("导出数据处理用时:>>>>>>>>>>>>>>>>>>>>>>>：" + ((endTime - startTime) / 1000.00) + "秒");

        long startTime1 = System.currentTimeMillis();
        Log.info("导出数据生成Excel开始:>>>>>>>>>>>>>>>>>>>>>>>");
        try {
            // 创建 Workbook 和 Sheet
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Sheet1");
            Font font = workbook.createFont();
            font.setFontName("宋体");
            // 设置字体名称
            font.setFontHeightInPoints((short) 16);
            CellStyle style = workbook.createCellStyle();
            style.setFont(font);
            sheet.setDefaultColumnWidth((short) 12);
            // 创建表头行，并设置样式
            Row headerRow = sheet.createRow(0);
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 创建表头单元格
            Cell headerCell = headerRow.createCell(0);
            headerRow.setHeightInPoints(30);
            headerCell.setCellValue("被保人清单");
            headerCell.setCellStyle(headerStyle);
            // 合并表头单元格，并居中
            CellRangeAddress mergedHeaderRegion = new CellRangeAddress(0, 0, 0, 16);
            sheet.addMergedRegion(mergedHeaderRegion);
            Row row1 = sheet.createRow(1);
            Cell cell1 = row1.createCell(1);
            cell1.setCellValue("集体投保单号:");
            Cell cell8 = row1.createCell(8);
            cell8.setCellValue(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS)));
            Cell cell16 = row1.createCell(16);
            cell16.setCellValue("操作员: " + globalInput.getName());
            // 创建单元格样式
            CellStyle cellStyle = workbook.createCellStyle();
            // 设置边框样式
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);

            Row headerRow2 = sheet.createRow(2);
            String[] columnNames = {"序号", "印刷号", "姓名", "性别", "证件类型", "证件号码", "主被保险人姓名", "与主被保险人关系", "险种名称", "保险计划编码", "保险期间", "缴费方式", "保费", "公司缴纳金额", "个人缴纳金额", "保额", "生效日期"};
            int headerRowIndex = 0;
            for (String columnName : columnNames) {
                Cell cell = headerRow2.createCell(headerRowIndex);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(columnName);
                headerRowIndex++;
            }


            // 创建行，将数据添加到行中
            int rowIndex = 3;
            int id = 1;
            for (PersonsInsure insured : personsInsures) {
                Row row = sheet.createRow(rowIndex++);
                row.createCell(0).setCellValue(id++);
                row.createCell(1).setCellValue(insured.getPrintNumber());
                row.createCell(2).setCellValue(insured.getName());
                row.createCell(3).setCellValue(insured.getGender());
                row.createCell(4).setCellValue(insured.getIdType());
                row.createCell(5).setCellValue(insured.getIdNumber());
                row.createCell(6).setCellValue(insured.getPolicyHolderName());
                row.createCell(7).setCellValue(insured.getRelationship());
                row.createCell(8).setCellValue(insured.getInsuranceName());
                row.createCell(9).setCellValue(insured.getPlanCode());
                row.createCell(10).setCellValue(insured.getInsurancePeriod());
                row.createCell(11).setCellValue(insured.getPaymentMethod());
                row.createCell(12).setCellValue(insured.getPremium());
                row.createCell(13).setCellValue(insured.getCompanyPayment());
                row.createCell(14).setCellValue(insured.getPersonalPayment());
                row.createCell(15).setCellValue(insured.getInsuredAmount());
                row.createCell(16).setCellValue(insured.getEffectDate());
            }

            // 获取最后一行和最后一列的索引值
            int lastRowIndex = sheet.getLastRowNum();
            int lastColumnIndex = sheet.getRow(2).getLastCellNum() - 1;

            // 生成筛选范围字符串
            String filterRange = String.format("A3:%s%d", CellReference.convertNumToColString(lastColumnIndex), lastRowIndex + 1);

            // 设置自动筛选
            sheet.setAutoFilter(CellRangeAddress.valueOf(filterRange));

            // 设置响应头信息
            String filename = "投保清单.xlsx";
            String encodedFilename = new String(filename.getBytes("UTF-8"), "ISO-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + encodedFilename);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            // 写入响应体
            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            log.info(e.getMessage());
        }

        long endTime1 = System.currentTimeMillis();
        Log.info("导出数据生成Excel结束用时:>>>>>>>>>>>>>>>>>>>>>>>：" + ((endTime1 - startTime1) / 1000.00) + "秒");
        return null;

    }


    /**
     * 证件号脱敏
     */
    public String maskIDCard(String idCard) {
        if (idCard == null || idCard.length() < 15) {
            return idCard;
        }
        // 将身份证号码最后4位替换为****
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(idCard, 0, idCard.length() - 4).append("****");
        return stringBuffer.toString();
    }

    public List<PersonsInsure> concatenationData2(String ensureCode, FCEnsure fcEnsure, FCGrpOrder fcGrpOrder) {


        List<PersonsInsure> list = new ArrayList<>();

        long startTime = System.currentTimeMillis();
        Log.info("导出数据准备:>>>>>>>>>>>>>>>>>>>>>>>");
        // 参数容器
        Map<String, Object> params = new HashMap<>();

        params.put("grpOrderNo", fcGrpOrder.getGrpOrderNo());
        // 查询出来团体的订单号
        List<FCOrder> fcOrderList = fcOrderMapper.selectList(params);
        // 1-企业全缴 2-企业代缴 3-混合缴费
        params.put("ensureCode", fcGrpOrder.getEnsureCode());
        params.put("configNo", "008");
        // 被保人清单
        for (FCOrder fcOrder : fcOrderList) {
            // 获取每个家庭的被保人集合
            params.clear();
            params.put("orderNo", fcOrder.getOrderNo());
            params.put("perNo", fcOrder.getPerNo());
            List<OrderItemDetailData> fcOrderItemList2 = fcOrderItemMapper.selectByOrderNoAndPerNo(params);
            for (OrderItemDetailData fcOrderItem : fcOrderItemList2) {
                // 查询 子订单产品要素详情表 获取 计划编码
                params.clear();
                // 查询被保人信息
                FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(fcOrderItem.getOrderItemNo());
                Map<String, String> map = new HashMap<>();
                map.put("perNo", fcOrder.getPerNo());
                map.put("personID", fcOrderInsured.getPersonID());
                FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(map);
                String staffPersonId = fcDefaultPlanMapper.getEmployPersonid(fcOrder.getPerNo());
                if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                    if (staffPersonId == null || "".equals(staffPersonId)) {
                        Log.info("fcOrderItem.getOrderItemDetailNo()==" + fcOrderItem.getName() + "的主被保人客户号为空。");
                    }
                } else if ("1".equals(fcEnsure.getEnsureType())) {
                    staffPersonId = fcOrderInsured.getPersonID();
                }

                FDCodeKey key = new FDCodeKey();
                key.setCodeType("Relation");
                key.setCodeKey(fcStaffFamilyRela.getRelation());
                FDCode fdCode = fdCodeMapper.selectByPrimaryKey(key);
                // 非弹性计划时 传当前计划编码
                String planCode = "";
                if (!"1".equals(fcEnsure.getPlanType())) {
                    planCode = fcOrderItem.getProductCode();
                }
                //公司缴纳
                double companyPrem = fcOrderItem.getGrpPrem();
                //个人缴纳
                double selfPrem = fcOrderItem.getSelfPrem();
                // 根据计划编码获取险种信息
                List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectPlanList(ensureCode, planCode);
                int index = 1;
                BigDecimal total = new BigDecimal(0);
                BigDecimal totalCompany = new BigDecimal(0);
                for (FCPlanRisk fcPlanRisk : fcPlanRiskList) {
                    String riskName = fdRiskInfoMapper.selectRiskNameByRiskCode(fcPlanRisk.getRiskCode());

                    PersonsInsure personsInsure = PersonsInsure.builder()
                            .insuredNo(fcOrderInsured.getPersonID())
                            .mainInsuredNo(staffPersonId)
                            .name(fcOrderInsured.getName())
                            .gender(SexEnum.getValueByCode(fcOrderInsured.getSex()))
                            .idType(IDTypeNewEnum.getValueByCode(fcOrderInsured.getIDType()))
                            .idNumber(maskIDCard(fcOrderInsured.getIDNo())).build();

                    if (fdCode != null) {
                        //投保类型为企业投保时取对应核心关系码值  学生投保默认本人 00
                        if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                            personsInsure.setRelationship(fdCode.getCodeName());
                        } else if ("1".equals(fcEnsure.getEnsureType())) {
                            personsInsure.setRelationship("本人");
                        }
                    } else {
                        Log.info("与员工关系码表映射异常，CodeType：Relation，CodeKey:" + fcStaffFamilyRela.getRelation());
                    }
                    String policyEndDate = fcEnsure.getPolicyEndDate();
                    String cvaliDate = fcEnsure.getCvaliDate();
                    try {
                        Long days = DateTimeUtil.getDistanceDays(policyEndDate, cvaliDate);
                        if (days == 364 || days == 365) {
                            personsInsure.setInsurancePeriod("1年");
                        } else {
                            long daysOne = DateTimeUtil.getDistanceDays(fcEnsure.getCvaliDate(),
                                    fcEnsure.getPolicyEndDate());
                            String daysTwo = String.valueOf(daysOne + 1);
                            personsInsure.setInsurancePeriod(daysTwo + "天");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    personsInsure.setPlanCode(planCode);
                    // 1-企业全缴 2-企业代缴 3-混合缴费
                    personsInsure.setPaymentMethod(fcEnsure.getPayType() + "-" + PayPremTypeEnum.getValueByCode(fcEnsure.getPayType()));
                    params.clear();
                    params.put("ensureCode", ensureCode);
                    params.put("planCode", planCode);
                    params.put("riskCode", fcPlanRisk.getRiskCode());
                    //险种总保费
                    FCPlanRiskDuty fcPlanRiskDuty = fcPlanRiskDutyMapper.selectSumPrem(params);
                    double totalPremium = companyPrem + selfPrem;
                    BigDecimal totalPremiumDecimal = BigDecimal.valueOf(totalPremium);
                    BigDecimal companyPremiumDecimal = BigDecimal.valueOf(companyPrem);
                    BigDecimal selfPremiumDecimal = BigDecimal.valueOf(selfPrem);
                    BigDecimal riskPremiumDecimal = BigDecimal.valueOf(fcPlanRiskDuty.getPrem());
                    BigDecimal company = totalPremium == 0 ? BigDecimal.valueOf(0) : riskPremiumDecimal.multiply(companyPremiumDecimal).divide(totalPremiumDecimal, 2, RoundingMode.HALF_UP);
                    BigDecimal prem = totalPremium == 0 ? BigDecimal.valueOf(0) : riskPremiumDecimal.multiply(selfPremiumDecimal).divide(totalPremiumDecimal, 2, RoundingMode.HALF_UP);
                    if (index == fcPlanRiskList.size()) {
                        prem = selfPremiumDecimal.subtract(total);
                        company = companyPremiumDecimal.subtract(totalCompany);
                    }
                    total = total.add(prem);
                    totalCompany = totalCompany.add(company);
                    personsInsure.setPremium(fcPlanRiskDuty.getPrem().toString());
                    personsInsure.setCompanyPayment(company.toString());
                    personsInsure.setPersonalPayment(prem.toString());
                    personsInsure.setInsuranceName(riskName);
                    personsInsure.setEffectDate(fcEnsure.getCvaliDate());
                    personsInsure.setInsuredAmount(fcPlanRiskDuty.getAmnt().toString());
                    list.add(personsInsure);
                    index++;
                }
            }
        }
        long endTime = System.currentTimeMillis();
        Log.info("导出数据准备用时:>>>>>>>>>>>>>>>>>>>>>>>：" + ((endTime - startTime) / 1000.00) + "秒");
        return list;

    }
}
