package com.sinosoft.eflex.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hqins.common.utils.HttpUtils;
import com.hqins.common.utils.JsonUtil;
import com.hqins.job.core.handler.annotation.XxlJob;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.constants.VerifyIdCardProperties;
import com.sinosoft.eflex.dao.FCGrpOrderMapper;
import com.sinosoft.eflex.enums.InterfaceType;
import com.sinosoft.eflex.model.AddressEntity.*;
import com.sinosoft.eflex.model.AddressEntity.convert.IdCardVerifyConvert;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.sinosoft.eflex.constants.EflexConstants.SUS_CODE;

/**
 * <AUTHOR> Li
 * @Date 2023/1/12 17:34
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AddressCheckService {

    private final MyProps myProps;
    private final FCGrpOrderMapper fcGrpOrderMapper;
    private final VerifyIdCardProperties verifyIdCardProperties;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    private static final String USER_CHANNEL = "HQINS-WELFARE";
    private static final String BUSINESS_CODE = "ANTI_MONEY";
    private static final String MANAGE_ORG = "8644989801";
    private static final List<String> CODE_LIST = Arrays.asList("1003", "1004", "1005");

    /**
     * 校验地区编码(不查询本地)
     *
     * @param addressCode
     * @return
     */
    public boolean checkAddressCode(String addressCode) {
        AddressRequestEntity addressRequestEntity = new AddressRequestEntity();
        addressRequestEntity.setAreaCodes(addressCode);
        String s1 = HttpUtil.postHttpRequestJson22("https://hqins-api.e-hqins.com/api/basic/conformity/by-code", JSONObject.toJSONString(addressRequestEntity));
        JSONObject parse = JSON.parseObject(s1);
        String data = parse.getString("data");
        String code = parse.getString("code");
        if (code.equals("000000")) {
            AddressData addressData = JSON.parseObject(data, AddressData.class);
            System.out.println("addressResponseEntity = " + addressData);
            List<AddressDetail> responses = addressData.getResponses();
            if (CollectionUtils.isNotEmpty(responses)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 国籍校验
     */
    public String checkNationalityCode(String nationality) {
        log.info("国籍校验入参,checkNationalityCode::::{}", nationality);
        NationalityRequestEntity build = NationalityRequestEntity.builder().nativePlace(nationality).build();
        String s = HttpUtil.postHttpRequestJson(myProps.getNationalityCheck(), JSON.toJSONString(build), myProps.getAppId(), myProps.getAppSecret());
        NationalityResponseEntity response = JsonUtil.fromJSON(s, NationalityResponseEntity.class);
        if ("1".equals(response.getBody())) {
            //高风险地区
            log.info("国籍校验返回,checkNationalityCode::::投保人；被保险人；受益人国籍为洗钱高风险国家地区，谢绝投保。");
            return "投保人；被保险人；受益人国籍为洗钱高风险国家地区，谢绝投保。";
        }
        log.info("国籍校验返回,checkNationalityCode::::  ''");
        return "";
    }

    /**
     * 客户风险等级
     *
     * @return
     */
    public CheckCustomerVO checkCustomerLevel(String riskManagementTypes, List<EvaluationCustomer> list) {
        log.info("客户风险评估,checkCustomerLevel::::{}", JSON.toJSONString(list));
        List<String> privateRiskLevel = new ArrayList<>();
        List<String> publicRiskLevel = new ArrayList<>();
        try {
            String jsonString = JSON.toJSONString(CustomerAssessmentRequestEntity.builder()
                    .requestId("YF" + System.currentTimeMillis())
                    .userChannel(USER_CHANNEL)
                    .businessCode(BUSINESS_CODE)
                    .riskManagementTypes(riskManagementTypes)
                    .manageOrg(MANAGE_ORG)
                    .evaluationCustomerList(list)
                    .build());
            String s = HttpUtil.postHttpRequestJson(myProps.getCustomerLevelCheck(), jsonString);

            CustomerAssessmentResponseEntity response = JSON.parseObject(s, CustomerAssessmentResponseEntity.class);

            if (response.getCode().equals("000000")) {
                List<AntiMoneyLaunderingAssessmentVO> assessmentVOList = response.getData().getAntiMoneyLaunderingAssessmentList();
                for (AntiMoneyLaunderingAssessmentVO item : assessmentVOList) {
                    if (InterfaceType.NATURAL_PERSONS.name().equals(riskManagementTypes)) {
                        if (CODE_LIST.contains(item.getPrivateRiskLevel())) {
                            //自然人风险
                            privateRiskLevel.add(item.getName());
                        }
                    } else {
                        if (CODE_LIST.contains(item.getPublicRiskLevel())) {
                            //非自然人风险
                            publicRiskLevel.add(item.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.info("客户风险评估,checkCustomerLevel::error::", e);
        }
        return CheckCustomerVO.builder()
                .privateRiskLevel(CollectionUtils.isNotEmpty(privateRiskLevel) ? String.join("、", privateRiskLevel) : null)
                .publicRiskLevel(CollectionUtils.isNotEmpty(publicRiskLevel) ? String.join("、", publicRiskLevel) : null)
                .build();
    }

    /**
     * 疑似重客校验
     *
     * @return https://ishare-gw-uat.e-hqins.com/service/customer-getCustomerNo
     */
    public String checkSameCustomer(CheckSameCustomerRequest request) {
        log.info("checkSameCustomer::::入参{}", JSON.toJSONString(request));
        request.setTransCode("INSURE-OFFLINE-CONTROLLER-CUSTOMER-GETCUSTOMERNO");
        request.setTransNo(String.valueOf(System.currentTimeMillis()));
        request.setTransSN(String.valueOf(System.currentTimeMillis()));
        request.setTransSource("YF");
        request.setTransTime(DateTimeUtil.getCurrentDateTime());
        String s = HttpUtil.postHttpRequestJson(myProps.getCheckSameCustomer(), JSON.toJSONString(request), myProps.getAppId(), myProps.getAppSecret());
        if (StringUtils.isEmpty(s)) {
            return "重复客户校验异常！";
        }
        CheckSameCustomerResponse response = JSON.parseObject(s, CheckSameCustomerResponse.class);
        if (!"0".equals(response.getCode())) {
            return response.getMessage();
        }
        return "";
    }

    /**
     * 公安二要素
     *
     * @return https://ishare-gw-uat.e-hqins.com/service/verify-idcard-batch
     */
    public String checkIdCard(List<IdCardVerifyRequest.Verify> verifies) {
        log.info("checkIdCard request:{}", JsonUtil.toJSON(verifies));
        List<IdCardVerifyRequest.Verify> collect = verifies.stream().filter(x -> StringUtils.isNotEmpty(x.getIdType()) && Arrays.asList(new String[]{"ID", "HOUSEHOLD_REGISTER"}).contains(x.getIdType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            log.debug("collect is null");
            return null;
        }

        List<String> list = new ArrayList<>();
        List<String> synchronizedList = Collections.synchronizedList(list);
        // 最大线程数 * 每组数据量
        List<List<IdCardVerifyRequest.Verify>> partition = Lists.partition(collect, verifyIdCardProperties.getCuntDownSize() * verifyIdCardProperties.getDataVolume());
        for (List<IdCardVerifyRequest.Verify> verifyList : partition) {
            // 同时开启锁定 （线程数）
            final CountDownLatch countDownLatch = new CountDownLatch(verifyIdCardProperties.getCuntDownSize());
            // 每组数据
            List<List<IdCardVerifyRequest.Verify>> verifys = Lists.partition(verifyList, verifyIdCardProperties.getDataVolume());
            int size = verifys.size();
            for (int i = 0; i <= verifyIdCardProperties.getCuntDownSize(); i++) {
                if (i > size - 1) {
                    countDownLatch.countDown();
                    continue;
                }
                // 每组数据
                int finalI = i;
                threadPoolTaskExecutor.execute(() -> {
                    verifyIdCard(verifys.get(finalI), synchronizedList);
                    countDownLatch.countDown();
                });
            }
            // 等待 线程数为0
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("checkIdCard e", e);
            }
        }

        if (CollectionUtil.isNotEmpty(synchronizedList)) {
            StringBuffer failVerifies = new StringBuffer();
            synchronizedList.forEach(s -> failVerifies.append(s).append("、"));
            failVerifies.deleteCharAt(failVerifies.length() - 1);
            return failVerifies + " 录入的姓名与证件号码不符";
        }
        return null;
    }


    /**
     * 公安二要素调用*
     *
     * @param collect
     * @param synchronizedList
     */
    public void verifyIdCard(List<IdCardVerifyRequest.Verify> collect, List<String> synchronizedList) {
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        try {
            HashMap<String, String> headerMap = new HashMap<>(3);
            headerMap.put("Content-Type", "application/json;charset=UTF-8");
            headerMap.put("app_id", myProps.getCoreAppId());
            headerMap.put("app_secret", myProps.getCoreAppSecret());
            IdCardVerifyRequest idCardVerifyRequest = IdCardVerifyConvert.convertReq(collect);
            log.debug("checkIdCard idCardVerifyRequest :{}", JsonUtil.toJSON(idCardVerifyRequest));
            String result = HttpUtils.postJSON(verifyIdCardProperties.getCheckIdCard(), JsonUtil.toJSON(idCardVerifyRequest), headerMap);
            log.info("checkIdCard idCardVerifyRequest  response:{}", result);
            VerificationResponse response = JsonUtil.fromJSON(result, VerificationResponse.class);
            // 失败
            if (!SUS_CODE.equals(response.getCode())) {
                for (IdCardVerifyRequest.Verify verify : collect) {
                    synchronizedList.add(verify.getName());
                }
            }
            List<VerificationResponse.FailVerify> failVerifies = response.getData().getFailVerifies();
            if (CollectionUtils.isNotEmpty(failVerifies)) {
                for (VerificationResponse.FailVerify failVerify : failVerifies) {
                    synchronizedList.add(failVerify.getName());
                }
            }
        } catch (IOException e) {
            for (IdCardVerifyRequest.Verify verify : collect) {
                synchronizedList.add(verify.getName());
            }
            log.error("checkIdCard  http error", e);
        }
    }

    /**
     * 历史保全请求
     * https://ishare-gw-uat.e-hqins.com/service/grp-endorse-send?grpContNo=
     */

    @XxlJob("syncPolicyUpdate")
    public void update() {
        List<String> grpContNoList = fcGrpOrderMapper.selectGrpContNo();
        CoreRequestBody requestBody = CoreRequestBody.builder()
                .transCode("POLICY-MANAGE-SERVICE-GRP-ENDORSE-SEND")
                .transNo(String.valueOf(System.currentTimeMillis()))
                .transSN(String.valueOf(System.currentTimeMillis()))
                .transSource("YF")
                .transTime(DateTimeUtil.getCurrentDateTime()).build();
        for (String contNo : grpContNoList) {
            String url = myProps.getSyncPolicyUpdate() + contNo;
            HttpUtil.postHttpRequestJson(url, JSON.toJSONString(requestBody), myProps.getAppId(), myProps.getAppSecret());

            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

    }


}
