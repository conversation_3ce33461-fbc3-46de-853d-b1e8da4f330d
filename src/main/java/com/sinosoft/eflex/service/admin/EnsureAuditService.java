package com.sinosoft.eflex.service.admin;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.dao.admin.EnsureAuditMapper;
import com.sinosoft.eflex.enums.SMSTemplateNoEnum;
import com.sinosoft.eflex.enums.StateEnum;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.sendmessage.SendMessageResp;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import com.sinosoft.eflex.service.EnsureMakeService;
import com.sinosoft.eflex.service.EnsureQueryService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.encrypt.LisIDEA;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by 苍山 on 2018/11/20.
 */
@Service
@Slf4j
public class EnsureAuditService {

    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private EnsureAuditMapper ensureAuditMapper;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private FcPlanConfigMapper fcPlanConfigMapper;
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private EnsureMakeService ensureMakeService;
    @Autowired
    private FcEnsureContactMapper fcEnsureContactMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FDusertomenugrpMapper fDusertomenugrpMapper;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FcGrpContactMapper fcGrpContactMapper;
    @Autowired
    private FcHrRegistTempMapper fcHrRegistTempMapper;
    @Autowired
    private FCGrpApplicantMapper fcGrpApplicantMapper;
    @Autowired
    private FCGrpApplicantContactMapper fcGrpApplicantContactMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FCEnsurePlanMapper fcEnsurePlanMapper;
    @Autowired
    private FCPerInfoTempMapper fcPerInfoTempMapper;
    @Autowired
    private FCPlanInformMapper fcPlanInformMapper;
    @Autowired
    private FCPlanRiskMapper fcPlanRiskMapper;
    @Autowired
    private FcPlanRiskInfoMapper fcPlanRiskInfoMapper;
    @Autowired
    private FCPerinfoFamilyTempMapper fcPerinfoFamilyTempMapper;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private FDAgentInfoMapper fdAgentInfoMapper;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;
    @Autowired
    private FDRiskDutyInfoMapper fdRiskDutyInfoMapper;
    @Autowired
    private FcDutyAmountGradeMapper fcDutyAmountGradeMapper;
    @Autowired
    private FCOrderMapper fcOrderMapper;
    @Autowired
    private FCOrderItemMapper fcOrderItemMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private FCPlanRiskDutyMapper fcPlanRiskDutyMapper;
    @Autowired
    private FPInsureEflexPlanMapper fpInsureEflexPlanMapper;
    @Autowired
    private FCPerRegistDayMapper fcPerRegistDayMapper;
    @Autowired
    private FCFileDocMainMapper fcFileDocMainMapper;
    @Autowired
    private EnsureQueryService ensureQueryService;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FCPlanHealthDesignRelaMapper fcPlanHealthDesignRelaMapper;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private MyProps myProps;
    @Autowired
    private FCAppntImpartInfoMapper fcAppntImpartInfoMapper;

    private final AuditEnsureService auditEnsureService;

    // HR权限
    private static final String ROLE_TYPE_TWO = "2";

    public EnsureAuditService(AuditEnsureService auditEnsureService) {
        this.auditEnsureService = auditEnsureService;
    }

    /**
     * 计划审核查询
     *
     * @param token
     * @param getEnsureAuditsReq
     * @return
     */
    public String getEnsureAuditList(String token, GetEnsureAuditsReq getEnsureAuditsReq) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            PageHelper.startPage(getEnsureAuditsReq.getPageNo(), getEnsureAuditsReq.getPageSize());
            //isReal  标识符  区分接口调用来源  0-审核管理员  1-复核管理员  2-管理员弹性计划定制
            // String isReal = getEnsureAuditsReq.getIsReal();
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            String manageCom = globalInput.getManageCom();
            if (StringUtils.isEmpty(manageCom)) {
                throw new SystemException("审核用户管理机构为空！");
            } else {
                getEnsureAuditsReq.setManageCom(manageCom);
            }
            getEnsureAuditsReq.setUserNo(globalInput.getUserNo());
            List<HashMap<String, Object>> fcEnsureMap = ensureAuditMapper.getEnsureAuditList1(getEnsureAuditsReq);
            for (HashMap<String, Object> hashMap : fcEnsureMap) {
                if ("0".equals(hashMap.get("planType")) && "011".equals(hashMap.get("ensureState"))) {
                    hashMap.put("ensureStateName", "管理员退回后台审核");
                }
            }
            PageHelperUtil<HashMap<String, Object>> teamPageInfo = new PageHelperUtil<>(fcEnsureMap);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("list", teamPageInfo.getList());
            resultMap.put("data", dataMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "获取计划信息成功！");
        } catch (Exception e) {
            log.info("获取计划信息失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "获取计划信息失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * "福利保障配置信息查询
     *
     * @param token
     * @param params
     * @return
     */
    public String getEnsureConfigList(String token, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtils.isNotBlank(params.get("ensureCode"))) {
                List<HashMap<String, Object>> fcEnsureConfigMap = ensureAuditMapper.getFCEnsureConfigList(params);
                //获取福利保障配置信息
                for (Map<String, Object> ensureConfig : fcEnsureConfigMap) {
                    String configNo = (String) ensureConfig.get("ConfigNo");
                    String configValue = (String) ensureConfig.get("ConfigValue");
                    if (configValue == null || "".equals(configValue)) {
                        continue;
                    } else {
                        switch (configNo) {
                            case "003":
                                // 医疗机构
                                resultMap.put("medicaInstitutions", configValue);
                                break;
                            case "005":
                                //既往症
                                resultMap.put("pastSymptoms", configValue);
                                break;
                            case "006":
                                // 其他约定
                                resultMap.put("appointments", configValue);
                                break;
                            case "008":
                                // 缴费方式
                                resultMap.put("payType", configValue);
                                break;
                            case "013":
                                //是否续保1是，0否
                                resultMap.put("isRenewalInsurance", configValue);
                                break;
                            case "014":
                                //同质风险加减人比例上限
                                resultMap.put("riskAddOrReduceRatio", configValue);
                                break;
                            case "015":
                                //是否为保全定期结算Y是，N否
                                resultMap.put("termSettlementState", configValue);
                                break;
                            case "016":
                                //保全定期结算周期
                                resultMap.put("termSettlementCycle", configValue);
                                break;
                            case "017":
                                //定期结算金额上限
                                resultMap.put("termSettlementToplimit", configValue);
                                break;
                            case "018":
                                //特别约定
                                resultMap.put("specialAgreement", configValue);
                                break;
                            case "019":
                                // 是否需要健告
                                resultMap.put("isNoSetUpHealthInform", configValue);
                                break;
                            case "022":
                                // 是否按照续保单投保
                                resultMap.put("isAccordingRenewalInsurance", configValue);
                                break;
                            case "023":
                                // 企业付款方式 GrpPayType
                                resultMap.put("grpPayType", configValue);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "福利保障配置信息查询成功！");
        } catch (Exception e) {
            log.info("福利保障配置信息查询失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "福利保障配置信息查询失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 福利审核通过
     *
     * @param token
     * @param params
     * @return
     */
    @Transactional
    public String ensureEgis(String token, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();

        // 检查保费信息
        String ensureCode = params.get("ensureCode");
        this.checkPrem(ensureCode);
        try {
            // 获取session
            GlobalInput globalInput = userService.getSession(token);
            //判断险种是否下架
            String riskStopSale = ensureMakeService.checkRiskStopSale(params.get("ensureCode"));
            if (!StringUtil.isEmpty(riskStopSale)) {
                resultMap.put("success", Boolean.FALSE);
                resultMap.put("code", "500");
                resultMap.put("message", riskStopSale);
                return JSON.toJSONString(resultMap);
            }
            if (StringUtil.isEmpty(params.get("clientNo"))) {
                resultMap.put("success", Boolean.FALSE);
                resultMap.put("code", "500");
                resultMap.put("message", "业务员工号必填!,请填写业务员工号");
                return JSON.toJSONString(resultMap);
            }


            String clientNo = auditEnsureService.employeesQuery(params.get("clientNo"));
            if (StringUtils.isEmpty(clientNo)) {
                resultMap.put("success", Boolean.FALSE);
                resultMap.put("code", "500");
                resultMap.put("message", "工号不存在");
                return JSON.toJSONString(resultMap);
            }


            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(params.get("ensureCode"));
            //删除退回原因
            fcEnsureConfigMapper.deleteByEnsureCode(params.get("ensureCode"));
            if (!"1".equals(fcEnsure.getPlanType())) {
                // 必要参数校验
                if ("1".equals(params.get("isRenewalInsurance")) && StringUtil.isEmpty(params.get("reNewGrpContNo"))) {
                    resultMap.put("success", Boolean.FALSE);
                    resultMap.put("code", "500");
                    resultMap.put("message", "请填被续保保单编号!");
                    return JSON.toJSONString(resultMap);
                }
                //配置福利保障
                resultMap = insertEnsureConfig(globalInput, params);
                //查询福利下所有的险种信息
                List<FCPlanRisk> ensureRiskInfolist = fcPlanRiskMapper.getensureRiskInfo(params.get("ensureCode"));
                //存储未设置佣金比率的险种
                List<String> list1 = new ArrayList<>();
                for (FCPlanRisk fcPlanRisk : ensureRiskInfolist) {
                    if (null == fcPlanRisk.getCommissionOrAllowanceRatio()) {
                        list1.add(fcPlanRisk.getRiskCode());
                    }
                }
                if (list1.size() > 0) {
                    resultMap.put("data", list1);
                    resultMap.put("message", "险种中存在佣金/服务津贴率为空！");
                    resultMap.put("code", "500");
                    return JSON.toJSONString(resultMap);
                }
                if ("303".equals(resultMap.get("code"))) {
                    return JSON.toJSONString(resultMap);
                } else {
                    log.info("福利配置完成！");
                }
            } else {
                FDAgentInfo fdagentinfo = fdAgentInfoMapper.getAgentByEnsureCode(params.get("ensureCode"));
                if (fdagentinfo != null) {
                    if (StringUtils.isNotBlank(fdagentinfo.getPhone()) || StringUtils.isNotBlank(fdagentinfo.getMobile())) {
                        FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
                        fcEnsureConfig.setEnsureCode(params.get("ensureCode"));
                        fcEnsureConfig.setGrpNo(params.get("grpNo"));
                        fcEnsureConfig.setOperator(globalInput.getUserNo());
                        fcEnsureConfig.setConfigNo("011");
                        fcEnsureConfig.setConfigValue("".equals(fdagentinfo.getMobile()) ? fdagentinfo.getPhone() : fdagentinfo.getMobile());
                        fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                        fcEnsureConfig = CommonUtil.initObject(fcEnsureConfig, "INSERT");
                        fcEnsureConfigMapper.insertSelective(fcEnsureConfig);
                    } else {
                        resultMap.put("success", false);
                        resultMap.put("code", "303");
                        resultMap.put("message", "代理人联系方式请手动维护！");
                        return JSON.toJSONString(resultMap);
                    }
                }
            }
            FCGrpInfo fcGrpInfo = new FCGrpInfo();
            fcGrpInfo.setGrpNo(params.get("grpNo"));
            fcGrpInfo.setClientno(params.get("clientNo"));
            fcGrpInfo.setClientName(params.get("clientName"));
            if ("true".equals(params.get("flag"))) {
                fcGrpInfo.setGrpScaleType(params.get("grpScaleType"));
            }
            fcGrpInfoMapper.updateByPrimaryKeySelective(fcGrpInfo);
            fcPlanInformMapper.deleteByEnsureCode(params.get("ensureCode"));
            // 同步个告表fcplaninform
            Map<String, String> imform = new HashMap<>();
            imform.put("ensureCode", params.get("ensureCode"));
            imform.put("Operator", globalInput.getUserNo());
            imform.put("currentDate", DateTimeUtil.getCurrentDate());
            imform.put("currentTime", DateTimeUtil.getCurrentTime());
            fcPerInfoTempMapper.insertFcplaninform(imform);
            log.info("同步个告表fcplaninform完成。。。");
            fcEnsure.setEnsureState(ConstantUtil.EnsureState_05);
            fcEnsure.setOperator(globalInput.getUserNo());
            fcEnsure.setClientNo(params.get("clientNo"));
            fcEnsure.setChannel(params.get("channel"));
            fcEnsure.setIntermediaryOrganCode(params.get("intermediaryOrganCode"));
            fcEnsure.setIntermediaryOrganName(params.get("intermediaryOrganName"));
            fcEnsure.setGreenInsurance("false".equals(params.get("greenInsurance")) ? Boolean.FALSE : Boolean.TRUE);
            fcEnsure.setReNewGrpContNo(params.get("reNewGrpContNo"));
            CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKey(fcEnsure);
            log.info("更新福利表fcEnsure投保状态。。。待审核确认");
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "福利审核通过！");
        } catch (Exception e) {
            log.info("福利审核通过失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "福利审核通过失败！");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    private void checkPrem(String ensureCode) {
        List<FCPlanRiskDuty> riskDutyList = fcPlanRiskDutyMapper.selectPlanDetailList(ensureCode, null, null);

        List<FCPlanRisk> fcPlanRisks = fcPlanRiskMapper.selectFcRiskList(new FCPlanRisk(ensureCode));
        double sum = riskDutyList.stream().mapToDouble(FCPlanRiskDuty::getPrem).sum();
        log.info("checkPrem sumPrem:{}", sum);
        for (FCPlanRisk fcPlanRisk : fcPlanRisks) {
            if (StateEnum.INVALID.getCode().equals(fcPlanRisk.getGiftInsureSign()) && sum == (double) 0) {
                throw new EFlexServiceException(EFlexServiceExceptionEnum.RISK_CONFIG_ZERO, "riskCode", fcPlanRisk.getRiskCode());
            }
            if (StateEnum.VALID.getCode().equals(fcPlanRisk.getGiftInsureSign()) && sum != (double) 0) {
                throw new EFlexServiceException(EFlexServiceExceptionEnum.RISK_CONFIG_ERROR, "riskCode", fcPlanRisk.getRiskCode());
            }
        }

    }

    //福利保障配置
    public Map<String, Object> insertEnsureConfig(GlobalInput globalInput, Map<String, String> params) {
        List<FCEnsureConfig> list = new ArrayList<FCEnsureConfig>();
        Map<String, Object> resultMap = new HashMap<>();
        String ensureCode = params.get("ensureCode");
        String grpNo = params.get("grpNo");
        //查询该福利下的医疗机构，其他约定的保障配置
        List<HashMap<String, Object>> fcPlanConfigList = ensureAuditMapper.getFCEnsureConfigList(params);
        if (fcPlanConfigList != null && fcPlanConfigList.size() > 0) {
            // 删除之前存在的福利配置重新插入
            ensureAuditMapper.delete(params.get("ensureCode"));
        }
        String appointments = "";
        String medicaInstitutions = "";

        //校验参数是否合格
        //同质风险加减人比例上限
        if (StringUtils.isNotBlank(params.get("riskAddOrReduceRatio")) && !params.get("riskAddOrReduceRatio").matches("^[1-9]?\\d|100$")) {
            resultMap.put("fcPlanConfigList", null);
            resultMap.put("success", false);
            resultMap.put("code", "303");
            resultMap.put("message", "同质风险加减人比例上限只允许录入大于等于0小于等于100的整数！");
            return resultMap;
        }
        if (StringUtils.isNotBlank(params.get("termSettlementState")) && params.get("termSettlementState").equals("Y")) {
            if (StringUtils.isBlank(params.get("termSettlementCycle")) || StringUtils.isBlank(params.get("termSettlementToplimit"))) {
                resultMap.put("fcPlanConfigList", null);
                resultMap.put("success", false);
                resultMap.put("code", "303");
                resultMap.put("message", "请完整补充保全定期结算项！");
                return resultMap;
            } else {
                if (!params.get("termSettlementToplimit").matches("^[0-9]*[1-9][0-9]*$")) {
                    resultMap.put("fcPlanConfigList", null);
                    resultMap.put("success", false);
                    resultMap.put("code", "303");
                    resultMap.put("message", "定期结算金额上限只允许录入大于0的整数！");
                    return resultMap;
                }
            }
        }

        for (int i = 1; i <= 13; i++) {
            FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
            fcEnsureConfig.setEnsureCode(params.get("ensureCode"));
            fcEnsureConfig.setGrpNo(params.get("grpNo"));
            fcEnsureConfig.setOperator(globalInput.getUserNo());
            if (i == 1 && !"".equals(params.get("medicaInstitutions")) && params.get("medicaInstitutions") != null) {
                fcEnsureConfig.setConfigNo("003");
                medicaInstitutions = params.get("medicaInstitutions");
                fcEnsureConfig.setConfigValue(params.get("medicaInstitutions"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else if (i == 2 && !"".equals(params.get("appointments")) && params.get("appointments") != null) {
                fcEnsureConfig.setConfigNo("006");
                appointments = params.get("appointments");
                fcEnsureConfig.setConfigValue(params.get("appointments"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else if (i == 3 && StringUtils.isNotBlank(params.get("isRenewalInsurance"))) {
                //是否续保
                fcEnsureConfig.setConfigNo("013");
                fcEnsureConfig.setConfigValue(params.get("isRenewalInsurance"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else if (i == 4) {
                //同质风险加减人比例
                fcEnsureConfig.setConfigNo("014");
                fcEnsureConfig.setConfigValue(StringUtils.isBlank(params.get("riskAddOrReduceRatio")) ? "50" : params.get("riskAddOrReduceRatio"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else if (i == 5 && StringUtils.isNotBlank(params.get("termSettlementState"))) {
                //是否为保全定期结算
                fcEnsureConfig.setConfigNo("015");
                fcEnsureConfig.setConfigValue(params.get("termSettlementState"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else if (i == 6 && StringUtils.isNotBlank(params.get("termSettlementCycle"))) {
                //保全定期结算周期
                fcEnsureConfig.setConfigNo("016");
                fcEnsureConfig.setConfigValue(params.get("termSettlementCycle"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else if (i == 7 && StringUtils.isNotBlank(params.get("termSettlementToplimit"))) {
                //保全定期上限金额
                fcEnsureConfig.setConfigNo("017");
                fcEnsureConfig.setConfigValue(params.get("termSettlementToplimit"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else if (i == 8 && StringUtils.isNotBlank(params.get("pastSymptoms"))) {
                //既往症
                fcEnsureConfig.setConfigNo("005");
                fcEnsureConfig.setConfigValue(params.get("pastSymptoms"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else if (i == 9 && StringUtils.isNotBlank(params.get("payType"))) {
                //支付方式
                fcEnsureConfig.setConfigNo("008");
                fcEnsureConfig.setConfigValue(params.get("payType"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else if (i == 10 && StringUtils.isNotBlank(params.get("specialAgreement"))) {
                fcEnsureConfig.setConfigNo("018");
                fcEnsureConfig.setConfigValue(params.get("specialAgreement"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else if (i == 11 && StringUtils.isNotBlank(params.get("isNoSetUpHealthInform"))) {
                fcEnsureConfig.setConfigNo("019");
                fcEnsureConfig.setConfigValue(params.get("isNoSetUpHealthInform"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else if (i == 12 && StringUtils.isNotBlank(params.get("isAccordingRenewalInsurance"))) {
                // 是否按照续保单承保
                fcEnsureConfig.setConfigNo("022");
                fcEnsureConfig.setConfigValue(params.get("isAccordingRenewalInsurance"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else if (i == 13 && StringUtils.isNotBlank(params.get("grpPayType"))) {
                // 企业支付方式
                fcEnsureConfig.setConfigNo("023");
                fcEnsureConfig.setConfigValue(params.get("grpPayType"));
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            }

        }


        if (appointments.length() > 2000) {
            resultMap.put("fcPlanConfigList", null);
            resultMap.put("success", false);
            resultMap.put("code", "303");
            resultMap.put("message", "其他约定不能超过1000个汉字！");
            return resultMap;
        } else if (medicaInstitutions.length() > 1000) {
            resultMap.put("fcPlanConfigList", null);
            resultMap.put("success", false);
            resultMap.put("code", "303");
            resultMap.put("message", "医疗机构不能超过1000个汉字！");
            return resultMap;
        }
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(params.get("ensureCode"));
        FDAgentInfo fdagentinfo = fdAgentInfoMapper.getAgentByEnsureCode(params.get("ensureCode"));
        if (fdagentinfo != null && "0".equals(fcEnsure.getPlanType())) {
            if (StringUtils.isNotBlank(fdagentinfo.getPhone()) || StringUtils.isNotBlank(fdagentinfo.getMobile())) {
                FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
                fcEnsureConfig.setEnsureCode(params.get("ensureCode"));
                fcEnsureConfig.setGrpNo(params.get("grpNo"));
                fcEnsureConfig.setOperator(globalInput.getUserNo());
                fcEnsureConfig.setConfigNo("011");
                fcEnsureConfig.setConfigValue(fdagentinfo.getMobile().equals("") ? fdagentinfo.getPhone() : fdagentinfo.getMobile());
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                list.add(fcEnsureConfig);
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "303");
                resultMap.put("message", "代理人联系方式请手动维护！");
                return resultMap;
            }
        }
        //若已存在告知信息 管理员变更福利企业 Hr再次提交福利时后台会抛异常
        if (("1".equals(fcEnsure.getPlanType()) && fcEnsure.getEnsureState().equals("07"))
                || ("1".equals(fcEnsure.getPlanType()) && fcEnsure.getEnsureState().equals("010"))
                || ("1".equals(fcEnsure.getPlanType()) && fcEnsure.getEnsureState().equals("011"))) {
            params.clear();
            params.put("ensureCode", ensureCode);
            params.put("grpNo", grpNo);
            fcAppntImpartInfoMapper.deleteImpartInfo(params);
        }

        if (list != null && list.size() > 0) {
            fcEnsureConfigMapper.inserts(list);
            resultMap.put("fcPlanConfigList", list);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "福利配置完成！");
        }
        return resultMap;
    }


    /**
     * "计划保障配置信息查询
     *
     * @param token
     * @param params
     * @return
     */
    public String getPlanConfigList(String token, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            List<FcPlanConfig> fcPlanConfigList = fcPlanConfigMapper.getFCPlanConfigList(params);
            Map<String, String> planeAllocation = new HashMap<>();
            for (FcPlanConfig fcPlanConfig : fcPlanConfigList) {
                String configNo = fcPlanConfig.getConfigNo();
                String configValue = fcPlanConfig.getConfigValue();
                if (configValue == null || "".equals(configValue)) {
                    continue;
                } else {
                    switch (configNo) {
                        case "001":
                            // 等待期
                            planeAllocation.put("await", configValue);
                            break;
                        case "002":
                            // 责任说明
                            planeAllocation.put("responsibility", configValue);
                            break;
                        case "003":
                            // 既往症
                            planeAllocation.put("anamnesis", configValue);
                            break;
                        case "004":
                            // 是否个告
                            planeAllocation.put("health", configValue);
                            break;
                        case "005":
                            // 下限
                            planeAllocation.put("lowerValue", configValue);
                            break;
                        case "006":
                            // 下限单位  天，月，周岁
                            planeAllocation.put("ages", configValue);
                            break;
                        case "007":
                            // 下限符号  小于，大于
                            planeAllocation.put("ageGroups", configValue);
                            break;
                        case "008":
                            // 上限
                            planeAllocation.put("upper", configValue);
                            break;
                        case "009":
                            // 上限单位
                            planeAllocation.put("age", configValue);
                            break;
                        case "010":
                            // 上限符号
                            planeAllocation.put("ageGroup", configValue);
                            break;
                        case "011":
                            // 投保年龄段下限
                            planeAllocation.put("minAge", configValue);
                            break;
                        case "012":
                            // 下限单位
                            planeAllocation.put("minAgeUnit", configValue);
                            break;
                        case "013":
                            // 投保年龄段上限
                            planeAllocation.put("maxAge", configValue);
                            break;
                        case "014":
                            // 上限单位
                            planeAllocation.put("maxAgeUnit", configValue);
                            break;
                        case "015":
                            //与员工关系
                            planeAllocation.put("relation", configValue);
                            break;
                        case "016":
                            //职业类别下限
                            planeAllocation.put("minOccupationType", configValue);
                            break;
                        case "017":
                            //职业类别下限
                            planeAllocation.put("maxOccupationType", configValue);
                            break;
                        case "018":
                            //性别
                            planeAllocation.put("planSex", configValue);
                            break;
                        case "019":
                            //有无医保
                            planeAllocation.put("joinMedProtect", configValue);
                            break;
                        case "020":
                            //职别下限
                            planeAllocation.put("gradeLevelLowLimit", configValue);
                            break;
                        case "021":
                            //职别上限
                            planeAllocation.put("gradeLevelTopLimit", configValue);
                            break;
                        default:
                            break;
                    }
                }
            }
            resultMap.put("planeAllocation", planeAllocation);
            resultMap.put("success", true);
            if (planeAllocation.size() > 0 && planeAllocation != null) {
                resultMap.put("code", "200");
                resultMap.put("message", "计划保障配置信息查询成功！");
            } else {
                resultMap.put("code", "204");
                resultMap.put("message", "计划保障配置信息中没有数据！");
            }
        } catch (Exception e) {
            log.info("计划保障配置信息查询失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "计划保障配置信息查询失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * "险种配置信息查询
     *
     * @param token
     * @param
     * @return
     */
    public String getPlanRiskList(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //查询福利下的险种信息
            List<FCPlanRisk> ensureRiskInfolist = fcPlanRiskMapper.getensureRiskInfo(ensureCode);
            resultMap.put("ensureAllRiskInfo", ensureRiskInfolist);
            resultMap.put("success", true);
            if (ensureRiskInfolist.size() > 0 && ensureRiskInfolist != null) {
                resultMap.put("code", "200");
                resultMap.put("message", "险种配置信息查询成功！");
            } else {
                resultMap.put("code", "204");
                resultMap.put("message", "险种配置信息中没有数据！");
            }
        } catch (Exception e) {
            log.info("险种配置信息查询失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "险种配置信息查询失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 计划保障配置
     *
     * @param token
     * @param params
     * @return
     */
    public String insertPlanConfig(String token, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> returnMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            fcPlanConfigMapper.delete(params.get("planCode"), params.get("ensureCode"));
            returnMap = insertPlanConfig(globalInput, params);
            List<FcPlanConfig> insertPlanConfigList = (List<FcPlanConfig>) returnMap.get("planConfigList");
            if (insertPlanConfigList != null && insertPlanConfigList.size() > 0) {
                fcPlanConfigMapper.insert(insertPlanConfigList);
            }
            resultMap.put("insertData", insertPlanConfigList);
            resultMap.put("success", true);
            resultMap.put("code", returnMap.get("code") == null ? "200" : returnMap.get("code"));
            resultMap.put("message", returnMap.get("message"));
        } catch (Exception e) {
            log.info("计划保障配置失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "计划保障配置失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 添加福利配置数据
     *
     * @param globalInput
     * @param params
     * @return
     */
    public Map<String, Object> insertPlanConfig(GlobalInput globalInput, Map<String, String> params) {
        List<FcPlanConfig> planConfigList = new ArrayList<FcPlanConfig>();
        Map<String, Object> returnMap = new HashMap<>();
        Integer lower = 0;
        Integer upper = 0;
        Integer minAge = 0;
        Integer maxAge = 0;
        String lowerUnit = "";
        String upperUnit = "";
        String minAgeUnit = "";
        String maxAgeUnit = "";
        for (int i = 1; i <= 21; i++) {
            FcPlanConfig fcPlanConfig = new FcPlanConfig();
            fcPlanConfig.setEnsureCode(params.get("ensureCode"));
            fcPlanConfig.setPlanCode(params.get("planCode"));
            fcPlanConfig.setGrpNo(params.get("grpNo"));
            fcPlanConfig.setOperator(globalInput.getUserNo());
            if (i == 1 && !"".equals(params.get("await")) && params.get("await") != null) {
                fcPlanConfig.setConfigNo("001");
                fcPlanConfig.setConfigValue(params.get("await"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 2 && !"".equals(params.get("responsibility")) && params.get("responsibility") != null) {
                fcPlanConfig.setConfigNo("002");
                fcPlanConfig.setConfigValue(params.get("responsibility"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 3 && !"".equals(params.get("anamnesis")) && params.get("anamnesis") != null) {
                fcPlanConfig.setConfigNo("003");
                fcPlanConfig.setConfigValue(params.get("anamnesis"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 4 && !"".equals(params.get("health")) && params.get("health") != null) {
                fcPlanConfig.setConfigNo("004");
                fcPlanConfig.setConfigValue(params.get("health"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 5 && !"".equals(params.get("lowerValue")) && params.get("lowerValue") != null) {
                fcPlanConfig.setConfigNo("005");
                lower = Integer.parseInt(params.get("lowerValue"));
                fcPlanConfig.setConfigValue(params.get("lowerValue"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 6 && !"".equals(params.get("ages")) && params.get("ages") != null) {
                fcPlanConfig.setConfigNo("006");
                lowerUnit = params.get("ages");
                fcPlanConfig.setConfigValue(lowerUnit);
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 7 && !"".equals(params.get("ageGroups")) && params.get("ageGroups") != null) {
                fcPlanConfig.setConfigNo("007");
                fcPlanConfig.setConfigValue(params.get("ageGroups"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 8 && !"".equals(params.get("upper")) && params.get("upper") != null) {
                fcPlanConfig.setConfigNo("008");
                upper = Integer.parseInt(params.get("upper"));
                fcPlanConfig.setConfigValue(params.get("upper"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 9 && !"".equals(params.get("age")) && params.get("age") != null) {
                fcPlanConfig.setConfigNo("009");
                upperUnit = params.get("age");
                fcPlanConfig.setConfigValue(upperUnit);
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 10 && !"".equals(params.get("ageGroup")) && params.get("ageGroup") != null) {
                fcPlanConfig.setConfigNo("010");
                fcPlanConfig.setConfigValue(params.get("ageGroup"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 11 && !"".equals(params.get("minAge")) && params.get("minAge") != null) {
                fcPlanConfig.setConfigNo("011");
                fcPlanConfig.setConfigValue(params.get("minAge"));
                minAge = Integer.parseInt(params.get("minAge"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 12 && !"".equals(params.get("minAgeUnit")) && params.get("minAgeUnit") != null) {
                fcPlanConfig.setConfigNo("012");
                fcPlanConfig.setConfigValue(params.get("minAgeUnit"));
                minAgeUnit = params.get("minAgeUnit");
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 13 && !"".equals(params.get("maxAge")) && params.get("maxAge") != null) {
                fcPlanConfig.setConfigNo("013");
                fcPlanConfig.setConfigValue(params.get("maxAge"));
                maxAge = Integer.parseInt(params.get("maxAge"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 14 && !"".equals(params.get("maxAgeUnit")) && params.get("maxAgeUnit") != null) {
                fcPlanConfig.setConfigNo("014");
                fcPlanConfig.setConfigValue(params.get("maxAgeUnit"));
                maxAgeUnit = params.get("maxAgeUnit");
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = (FcPlanConfig) CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 15) {//与员工关系
                if (StringUtils.isNotBlank(params.get("relation"))) {
                    fcPlanConfig.setConfigValue(params.get("relation"));
                } else {
                    fcPlanConfig.setConfigValue("0");
                }
                fcPlanConfig.setConfigNo("015");
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 16 && !"".equals(params.get("minOccupationType")) && params.get("minOccupationType") != null) {//职业类别下限
                fcPlanConfig.setConfigNo("016");
                fcPlanConfig.setConfigValue(params.get("minOccupationType"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 17 && !"".equals(params.get("maxOccupationType")) && params.get("maxOccupationType") != null) {//职业类别上限
                fcPlanConfig.setConfigNo("017");
                fcPlanConfig.setConfigValue(params.get("maxOccupationType"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 18 && !"".equals(params.get("planSex")) && params.get("planSex") != null) {//性别
                fcPlanConfig.setConfigNo("018");
                fcPlanConfig.setConfigValue(params.get("planSex"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 19 && !"".equals(params.get("joinMedProtect")) && params.get("joinMedProtect") != null) {//有无医保
                fcPlanConfig.setConfigNo("019");
                fcPlanConfig.setConfigValue(params.get("joinMedProtect"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 20 && !"".equals(params.get("gradeLevelLowLimit")) && params.get("gradeLevelLowLimit") != null) {//职别下限
                fcPlanConfig.setConfigNo("020");
                fcPlanConfig.setConfigValue(params.get("gradeLevelLowLimit"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            } else if (i == 21 && !"".equals(params.get("gradeLevelTopLimit")) && params.get("gradeLevelTopLimit") != null) {//职别上限
                fcPlanConfig.setConfigNo("021");
                fcPlanConfig.setConfigValue(params.get("gradeLevelTopLimit"));
                fcPlanConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcPlanConfig = CommonUtil.initObject(fcPlanConfig, "INSERT");
                planConfigList.add(fcPlanConfig);
            }
        }
        if (!CommonUtil.isPureDigital(params.get("await")) && !"".equals(params.get("await"))) {
            returnMap.put("planConfigList", null);
            returnMap.put("code", "303");
            returnMap.put("message", "等待期请输入大于等于0的整数!");
            return returnMap;
        }
        if (upper != 0) {
            if ("Y".equals(upperUnit)) {
                if (!CommonUtil.isPureDigital(params.get("upper"))) {
                    returnMap.put("planConfigList", null);
                    returnMap.put("code", "303");
                    returnMap.put("message", "请输入大于等于0的整数!");
                    return returnMap;
                }
                upper = upper * 365;
            } else if ("D".equals(upperUnit)) {
                if (!CommonUtil.isPureDigitals(params.get("upper"))) {
                    returnMap.put("planConfigList", null);
                    returnMap.put("code", "303");
                    returnMap.put("message", " 请输入0-365之间的整数！！");
                    return returnMap;
                }
            }
        }
        if (lower != 0) {
            if ("Y".equals(lowerUnit)) {
                if (!CommonUtil.isPureDigital(params.get("lowerValue"))) {
                    returnMap.put("planConfigList", null);
                    returnMap.put("code", "303");
                    returnMap.put("message", "请输入大于等于0的整数！！");
                    return returnMap;
                }
                lower = lower * 365;
            } else if ("D".equals(lowerUnit)) {
                if (!CommonUtil.isPureDigitals(params.get("lowerValue"))) {
                    returnMap.put("planConfigList", null);
                    returnMap.put("code", "303");
                    returnMap.put("message", " 请输入0-365之间的整数！！");
                    return returnMap;
                }
            }
        }
        if (lower != 0 && upper != 0 && lower > upper) {
            returnMap.put("planConfigList", null);
            returnMap.put("code", "500");
            returnMap.put("message", "个告年龄段下限不可高于年龄段上限！！");
            return returnMap;
        }
        if (minAge != 0) {
            if ("Y".equals(minAgeUnit)) {
                if (!CommonUtil.isPureDigital(params.get("minAge"))) {
                    returnMap.put("planConfigList", null);
                    returnMap.put("code", "303");
                    returnMap.put("message", "请输入大于等于0的整数!");
                    return returnMap;
                }
                minAge = minAge * 365;
            } else if ("D".equals(minAgeUnit)) {
                if (!CommonUtil.isPureDigitals(params.get("minAge"))) {
                    returnMap.put("planConfigList", null);
                    returnMap.put("code", "303");
                    returnMap.put("message", " 请输入0-365之间的整数！！");
                    return returnMap;
                }
            }
        }
        if (maxAge != 0) {
            if ("Y".equals(maxAgeUnit)) {
                if (!CommonUtil.isPureDigital(params.get("maxAge"))) {
                    returnMap.put("planConfigList", null);
                    returnMap.put("code", "303");
                    returnMap.put("message", "请输入大于等于0的整数！！");
                    return returnMap;
                }
                maxAge = maxAge * 365;
            } else if ("D".equals(maxAgeUnit)) {
                if (!CommonUtil.isPureDigitals(params.get("maxAge"))) {
                    returnMap.put("planConfigList", null);
                    returnMap.put("code", "303");
                    returnMap.put("message", " 请输入0-365之间的整数！！");
                    return returnMap;
                }
            }
        }
        if (minAge != 0 && maxAge != 0 && minAge > maxAge) {
            returnMap.put("planConfigList", null);
            returnMap.put("code", "500");
            returnMap.put("message", "投保年龄段下限不可高于年龄段上限！！");
            return returnMap;
        }
        returnMap.put("planConfigList", planConfigList);
        returnMap.put("message", "福利保障配置成功！");
        return returnMap;
    }

    /**
     * 福利审核回退
     *
     * @param token
     * @param params
     * @param grpNo
     * @return
     */
    @Transactional
    public String auditReturn(String token, Map<String, String> params, String grpNo) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        FCEnsure fcEnsure = new FCEnsure();
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            FCEnsure fcEnsures = fcEnsureMapper.selectByPrimaryKey(params.get("ensureCode"));
            if (fcEnsure == null) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "未查询到该福利！");
                return JSON.toJSONString(resultMap);
            }
            fcEnsureConfigMapper.deleteByEnsureCode(params.get("ensureCode"));
            //弹性计划不需要删除判断内的信息
            if (!"1".equals(fcEnsures.getPlanType())) {
                //删除该福利下的计划保障配置
                List<FCEnsurePlan> fcEnsureList = fcEnsurePlanMapper.selectByEnsureCode(params.get("ensureCode"));
                for (FCEnsurePlan fcEnsurePlan : fcEnsureList) {
                    fcPlanConfigMapper.delete(fcEnsurePlan.getPlanCode(), fcEnsurePlan.getEnsureCode());
                }
            }
            List<FCEnsureConfig> fcEnsureConfigList = new ArrayList<>();
            FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
            fcEnsureConfig.setEnsureCode(params.get("ensureCode"));
            fcEnsureConfig.setGrpNo(grpNo);
            fcEnsureConfig.setConfigNo("010");
            fcEnsureConfig.setConfigValue(format.format(new Date()) + "@@" + params.get("configValue"));
            FDusertomenugrpKey fDusertomenugrpKey = fDusertomenugrpMapper.findMenuInfo(globalInput.getUserNo());
            // 3--初审菜单  4--复核菜单
            if ("3".equals(fDusertomenugrpKey.getMenuGrpCode())) {
                fcEnsureConfig.setOperator("0");
            } else {
                fcEnsureConfig.setOperator("1");
            }
            fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
            fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
            fcEnsureConfigList.add(fcEnsureConfig);
            fcEnsureConfigMapper.inserts(fcEnsureConfigList);

            fcEnsures.setEnsureState(ConstantUtil.EnsureState_04);
            fcEnsures.setOperator(globalInput.getUserNo());
            fcEnsures = (FCEnsure) CommonUtil.initObject(fcEnsures, "UPDATE");
            fcEnsureMapper.updateByPrimaryKey(fcEnsures);
            log.info("更新福利表fcEnsure投保状态完成。。。");

            String Phone = fcEnsureContactMapper.selectByPrimaryKey(params.get("ensureCode")).getMobilePhone();

            //短信发送
            SendSMSReq sendSMSReq = new SendSMSReq();
            sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_006.getCode());
            sendSMSReq.setPhones(Phone);
            Map<String, Object> map = new HashMap<>();
            map.put("ensure_name", params.get("ensureName"));
            sendSMSReq.setParam(map);
            sendMessageService.sendSMS(sendSMSReq);


            resultMap.put("fcEnsure", fcEnsure);
            resultMap.put("fcEnsureConfig", fcEnsureConfig);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "福利审核回退完成！");
        } catch (Exception e) {
            log.info("福利审核回退失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "福利审核回退失败！");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 福利复核通过   完成定制
     *
     * @param token
     * @param params
     * @return
     */
    @Transactional
    public String toReviewAdopt(String token, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 获取session
            GlobalInput globalInput = userService.getSession(token);
            // 获取当前企业信息
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(params.get("grpNo"));
            // 获取当前 企业联系人
            FcGrpContact fcGrpContact = fcGrpContactMapper.selectByPrimaryKey(params.get("customNo"));
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(params.get("ensureCode"));
            //判断险种是否下架
            String riskStopSale = ensureMakeService.checkRiskStopSale(fcEnsure.getEnsureCode());
            if (!StringUtil.isEmpty(riskStopSale)) {
                resultMap.put("success", Boolean.FALSE);
                resultMap.put("code", "500");
                resultMap.put("message", riskStopSale);
                return JSON.toJSONString(resultMap);
            }
            int peoples = 0;
            //判断是否是弹性计划 是弹性计划是否配置健康告知 健康告知是否复核通过
            if ("1".equals(fcEnsure.getPlanType())) {
                Map<String, Object> ensureConfigMap = new HashMap<>();
                ensureConfigMap.put("ensureCode", params.get("ensureCode"));
                ensureConfigMap.put("configNo", "019");
                FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(ensureConfigMap);
                if ("1".equals(fcEnsureConfig.getConfigValue())) {//1--配置健康告知方案
                    List<FCPlanHealthDesignRela> fcplanhealthdesignrela = fcPlanHealthDesignRelaMapper.selectByEnsureCode(params.get("ensureCode"));
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    if (fcplanhealthdesignrela.size() > 1) {
                        resultMap.put("message", "数据异常：该福利下存在多个健康告知方案。");
                        return JSON.toJSONString(resultMap);
                    } else if (fcplanhealthdesignrela.size() == 0) {
                        resultMap.put("message", "该福利健康告知方案未复核完成，不能提交复核。");
                        return JSON.toJSONString(resultMap);
                    } else if (!"4".equals(fcplanhealthdesignrela.get(0).getDesignStatus())) {    //4--复核完成
                        resultMap.put("message", "该福利健康告知方案未复核完成，不能提交复核。");
                        return JSON.toJSONString(resultMap);
                    }
                }
                //判断是否是日常计划 是日常计划是否配置健康告知 健康告知是否复核通过
            }
            //PolicyState首次入库即复核通过 ==null 证明是首次提交
            if (fcEnsure.getPolicyState() == null) {
                // 插入 团体投保人表 FCGrpApplicant
                // 团体投保人编号
                String grpAppNo = maxNoService.createMaxNo("GrpAppNo", "", 20);
                FCGrpApplicant fcGrpApplicant = new FCGrpApplicant();
                fcGrpApplicant.setGrpAppNo(grpAppNo);
                BeanUtils.copyProperties(fcGrpInfo, fcGrpApplicant);
                fcGrpApplicant.setOperator(globalInput.getUserNo());
                fcGrpApplicant = CommonUtil.initObject(fcGrpApplicant, "INSERT");
                fcGrpApplicantMapper.insertSelective(fcGrpApplicant);
                log.info("插入团体投保人表 FCGrpApplicant完成。。。");
                FCGrpApplicantContact fcGrpApplicantContact = new FCGrpApplicantContact();
                String serialNo = maxNoService.createMaxNo("GrpAppContact", "", 20);
                try {
                    fcGrpApplicantContact.setSerialNo(serialNo);
                    fcGrpApplicantContact.setGrpAppNo(grpAppNo);
                    fcGrpApplicantContact.setGrpNo(params.get("grpNo"));
                    fcGrpApplicantContact.setName(fcGrpContact.getName());
                    fcGrpApplicantContact.setNativeplace(fcGrpContact.getNativeplace());
                    fcGrpApplicantContact.setIdType(fcGrpContact.getIdType());
                    fcGrpApplicantContact.setIdNo(fcGrpContact.getIdNo());
                    fcGrpApplicantContact.setIdTypeStartDate(fcGrpContact.getIdTypeStartDate());
                    fcGrpApplicantContact.setIdTypeEndDate(fcGrpContact.getIdTypeEndDate());
                    fcGrpApplicantContact.setSex(fcGrpContact.getSex());
                    fcGrpApplicantContact.setBirthDay(fcGrpContact.getBirthDay());
                    fcGrpApplicantContact.setMobilePhone(fcGrpContact.getMobilePhone());
                    fcGrpApplicantContact.setEmail(fcGrpContact.getEmail());
                    fcGrpApplicantContact.setDepartment(fcGrpContact.getDepartment());
                    fcGrpApplicantContact.setIdImage1(fcGrpContact.getIdImage1());
                    fcGrpApplicantContact.setIdImage2(fcGrpContact.getIdImage2());
                    fcGrpApplicantContact.setOperator(globalInput.getUserNo());
                    fcGrpApplicantContact = CommonUtil.initObject(fcGrpApplicantContact, "INSERT");
                    fcGrpApplicantContactMapper.insertSelective(fcGrpApplicantContact);
                    log.info("插入团体投保联系人表 fcGrpApplicantContact完成。。。");

                    // 将团保信息插入团体保单表
                    FCGrpOrder fcGrpOrder = new FCGrpOrder();
                    fcGrpOrder.setGrpOrderNo(maxNoService.createMaxNo("GrpOrderNo", null, 20));
                    fcGrpOrder.setEnsureCode(params.get("ensureCode"));
                    fcGrpOrder.setGrpOrderType("01");
                    fcGrpOrder.setGrpNo(params.get("grpNo"));
                    fcGrpOrder.setGrpAppNo(grpAppNo);
                    fcGrpOrder.setGrpOrderStatus("01");
                    // 用A511041+8位流水号
                    String prtNo = maxNoService.createMaxNo("TPrtNo", "", 20);
                    fcGrpOrder.setPrtNo(prtNo);
                    fcGrpOrder.setOperator(globalInput.getUserNo());
                    fcGrpOrder = CommonUtil.initObject(fcGrpOrder, "INSERT");
                    fcGrpOrderMapper.insert(fcGrpOrder);
                } catch (Exception e) {
                    log.error("福利复核异常", e);
                    throw e;
                }
                log.info("插入团体保单表 fcGrpOrder完成。。。");
            }
            // 同步人员信息
            //企事业单位投保
            if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                List<HashMap<String, String>> listPerIfo = new ArrayList<HashMap<String, String>>();
                Map<String, String> map = new HashMap<String, String>();
                // 获取需要同步的人数
                List<FCPerInfoTemp> needSyncNum = ensureMakeService.getNeedSyncNum(params.get("ensureCode"));
                peoples = peoples + needSyncNum.size();
                if (needSyncNum.size() > 0) {
                    LisIDEA encryPassword = new LisIDEA();
                    for (int i = 0; i < needSyncNum.size(); i++) {
                        Calendar cal = Calendar.getInstance();
                        int year = cal.get(Calendar.YEAR);
                        HashMap<String, String> hashMap = new HashMap<String, String>();
                        hashMap.put("currentDate", DateTimeUtil.getCurrentDate());
                        hashMap.put("currentTime", DateTimeUtil.getCurrentTime());
                        hashMap.put("Operator", globalInput.getUserNo());
                        hashMap.put("relationship", needSyncNum.get(i).getRelationship());
                        hashMap.put("year", year + "");
                        FdUser fdUser = fdUserMapper.selectByPwd(needSyncNum.get(i).getIDNo());
                        if (fdUser == null) {
                            hashMap.put("PassWord", encryPassword.encryptString(needSyncNum.get(i).getIDNo().substring(needSyncNum.get(i).getIDNo().length() - 6)));
                        } else if (fdUser != null) {
                            hashMap.put("PassWord", fdUser.getPassWord());
                        }
                        hashMap.put("ensureCode", params.get("ensureCode"));
                        hashMap.put("idNo", needSyncNum.get(i).getIDNo());
                        hashMap.put("nativeplace", needSyncNum.get(i).getNativeplace());
                        hashMap.put("IDType", needSyncNum.get(i).getIDType());
                        hashMap.put("levelCode", needSyncNum.get(i).getLevelCode());
                        hashMap.put("UserNo", maxNoService.createMaxNo("UserNo", "", 20));
                        hashMap.put("UserRole", maxNoService.createMaxNo("UserRole", "SN", 18));
                        hashMap.put("PwdSN", maxNoService.createMaxNo("PwdSN", "PWDSN", 15));
                        Map<String, Object> perInfoMap = new HashMap<>();
                        perInfoMap.put("IDNo", needSyncNum.get(i).getIDNo());
                        perInfoMap.put("grpNo", fcGrpInfo.getGrpNo());
                        List<FCPerInfo> fcPerInfo = fcPerInfoMapper.selectByExample(perInfoMap);
                        if (fcPerInfo.size() < 1) {
                            hashMap.put("PerNo", maxNoService.createMaxNo("PerNo", "", 20));
                        } else {
                            hashMap.put("PerNo", fcPerInfo.get(0).getPerNo());
                        }
                        Map<String, String> infoMap = new HashMap<>();
                        infoMap.put("grpNo", fcGrpInfo.getGrpNo());
                        infoMap.put("idNo", needSyncNum.get(i).getIDNo());
                        log.info("创建人员信息人员存在 ID::::::::{}", infoMap.get("idNo"));
                        FCPerson fcPerson = fcPersonMapper.getPerPersonID(infoMap);
                        if (fcPerson != null) {
                            log.info("创建人员信息人员存在PersonID::::::::{}", fcPerson.getPersonID());
                            hashMap.put("PersonId", fcPerson.getPersonID());
                        } else {
                            hashMap.put("PersonId", maxNoService.createMaxNo("PersonId", "", 20));
                            log.info("创建人员信息人员不存在PersonID::::::::{}", hashMap.get("PersonId"));
                        }
                        hashMap.put("seriaNo", maxNoService.createMaxNo("DefaultPlan", "", 20));
                        hashMap.put("RegDay", maxNoService.createMaxNo("RegDay", "", 20));
                        //判断该福利是固定计划还是弹性,固定计划，defaultPlan不为空
                        if ("0".equals(fcEnsure.getPlanType())) {
                            hashMap.put("StaffGrpPrem", String.valueOf(fcEnsurePlanMapper.selectPlanPrem(params.get("ensureCode"), needSyncNum.get(i).getDefaultPlan())));
                        }
                        if ("1".equals(fcEnsure.getPlanType())) {
                            hashMap.put("StaffGrpPrem", "");
                        }
                        hashMap.put("grpNo", fcGrpInfo.getGrpNo());
                        log.info("创建人员信息hashMap::::::::{}", JSON.toJSONString(hashMap));
                        listPerIfo.add(hashMap);
                    }
                    log.info("同步人员信息集合listPerIfo准备完毕，开始同步人员。。。");
                    map.put("ensureCode", params.get("ensureCode"));
                    map.put("planType", fcEnsure.getPlanType());
                    map.put("year", Calendar.getInstance().get(Calendar.YEAR) + "");
                    map.put("currentDate", DateTimeUtil.getCurrentDate());
                    map.put("currentTime", DateTimeUtil.getCurrentTime());
                    map.put("grpNo", fcGrpInfo.getGrpNo());
                    // 同步客户表FcPerInfo（更新）
                    //弹性计划 FcPerInfo 表中不存员工职级信息 update-20200417
                    log.info("更新FcPerInfo表开始。。。");
                    int i = fcPerInfoTempMapper.updateFcPerInfo(map);
                    log.info("更新FcPerInfo表完毕。。。");

                    // 同步客户表FcPerSon（更新）
                    log.info("更新FcPerSon表开始。。。");
                    int q = fcPerInfoTempMapper.updateFcPerSon(map);
                    log.info("更新FcPerSon表完毕。。。");

                    // 同步客户表FdUser（更新）
                    log.info("更新FdUser开始。。。");
                    int e = fcPerInfoTempMapper.updateFdUser(map);
                    log.info("更新FdUser完毕。。。");

                    log.info("更新FCPerRegistDay开始。。。");
                    //弹性计划 FCPerRegistDay 表中存员工职级信息 update-20200417
                    int l = fcPerInfoTempMapper.updateFCPerRegistDay(map);//应关闭
                    log.info("更新FCPerRegistDay完毕。。。");
                    log.info("更新人员相关表完成，开始插入人员相关表。。。");

                    // 同步客户表FcPerSon（插入）
                    int r = fcPerInfoTempMapper.insertFcPerSon(listPerIfo);
                    // 同步家庭关系表FCStaffFamilyRela
                    int t = fcPerInfoTempMapper.insertFCStaffFamilyRela(listPerIfo);
                    // 同步员工默认计划表fcDefaultPlan
                    if (!"1".equals(fcEnsure.getPlanType())) {
                        int y = fcPerInfoTempMapper.insertFCDefaultPlan(listPerIfo);
                    }
                    // 注册个人账号 custType : 1-个人账号
                    int u = fcPerInfoTempMapper.insertFdUser(listPerIfo);
                    int o = fcPerInfoTempMapper.insertFdUserRole(listPerIfo);
                    int p = fcPerInfoTempMapper.insertFdPwdHist(listPerIfo);
                    int f = fcPerInfoTempMapper.insertFDusertomenugrp(listPerIfo);
                    // 同步员工注册期表 FCPerRegistDay
                    //弹性计划 FCPerRegistDay 表中存员工职级信息 update-20200417
                    int g = fcPerInfoTempMapper.insertFCPerRegistDay(listPerIfo);
                    // 同步客户表FcPerInfo（插入）
                    //弹性计划 FcPerInfo 表中不存员工职级信息 update-20200417
                    int h = fcPerInfoTempMapper.insertFcPerInfo(listPerIfo);
                    // 同步完成，更新临时表数据为已提交
                    fcPerInfoTempMapper.updateFCPerinfoTemp(map);
                    log.info("同步人员信息完成。。。");
                }
                //同步家属信息
                Map<String, Object> ensureInfoMap = new HashMap<>(params);
                ensureInfoMap.put("subStaus", "01");
                List<FCPerinfoFamilyTemp> fcPerinfoFamilyTempList = fcPerinfoFamilyTempMapper.getFamilyByEnsureCode(ensureInfoMap);
                if (fcPerinfoFamilyTempList.size() > 0) {
                    map.put("ensureCode", params.get("ensureCode"));
                    map.put("year", Calendar.getInstance().get(Calendar.YEAR) + "");
                    map.put("currentDate", DateTimeUtil.getCurrentDate());
                    map.put("currentTime", DateTimeUtil.getCurrentTime());
                    map.put("grpNo", fcGrpInfo.getGrpNo());
                    int j = fcPersonMapper.updateFcPerSonStudent(map);
                    for (FCPerinfoFamilyTemp fcPerinfoFamilyTemp : fcPerinfoFamilyTempList) {
                        //获取员工家属关联表信息
                        FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
                        Map<String, String> familyInfo = new HashMap<>();
                        familyInfo.put("grpNo", fcGrpInfo.getGrpNo());
                        familyInfo.put("idNo", fcPerinfoFamilyTemp.getIDNo());
                        familyInfo.put("perIDNo", fcPerinfoFamilyTemp.getPerIDNo());
                        FCPerson fcPerson = fcPersonMapper.getFamPersonID(familyInfo);
                        if (fcPerson == null) {
                            HashMap<String, String> fcPersonMap = new HashMap<>();
                            fcPersonMap.put("familyTempNo", fcPerinfoFamilyTemp.getFamilyTempNo());
                            fcPersonMap.put("personID", maxNoService.createMaxNo("PersonID", null, 20));
                            fcPersonMap.put("ensureCode", fcPerinfoFamilyTemp.getEnsureCode());
                            fcPersonMap.put("IDType", fcPerinfoFamilyTemp.getIDType());
                            fcPersonMap.put("IDNo", fcPerinfoFamilyTemp.getIDNo());
                            fcPersonMap.put("operator", globalInput.getUserNo());
                            fcPersonMap.put("operatorCom", globalInput.getUserName());
                            fcPersonMap.put("makeDate", DateTimeUtil.getCurrentDate());
                            fcPersonMap.put("makeTime", DateTimeUtil.getCurrentTime());
                            fcPersonMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                            fcPersonMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                            fcPersonMap.put("relationship", "06");
                            int i = fcPersonMapper.insertMap(fcPersonMap);
                            fcStaffFamilyRela.setPersonID(fcPersonMap.get("personID"));
                        } else if (fcPerson != null) {
                            fcStaffFamilyRela.setPersonID(fcPerson.getPersonID());
                        }
                        fcStaffFamilyRela.setRelation(fcPerinfoFamilyTemp.getRelation());
                        fcStaffFamilyRela.setOperator(globalInput.getUserNo());
                        Map<String, Object> isEmptyMap = new HashMap<>();
                        isEmptyMap.put("IDNo", fcPerinfoFamilyTemp.getPerIDNo());
                        isEmptyMap.put("grpNo", fcGrpInfo.getGrpNo());
                        List<FCPerInfo> fcPerInfos = fcPerInfoMapper.isExitPerInfo(isEmptyMap); //调用之前已经写好的方法 返回List  一个企业下fcperinfo不循序重复，所以不会存在多个数据，既取第一条即可
                        if (fcPerInfos.size() > 0) {
                            fcStaffFamilyRela.setPerNo(fcPerInfos.get(0).getPerNo());
                        } else if (fcPerInfos.size() == 0) {
                            for (HashMap hashMap : listPerIfo) {
                                if (fcPerinfoFamilyTemp.getPerIDNo().equals(hashMap.get("idNo")) && fcPerinfoFamilyTemp.getPerIDType().equals(hashMap.get("IDType"))) {
                                    fcStaffFamilyRela.setPerNo(String.valueOf(hashMap.get("PerNo")));
                                }
                            }
                        }
                        fcStaffFamilyRela = (FCStaffFamilyRela) CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
                        //同步员工家属关联表
                        Map<String, String> StaffFamilyMap = new HashMap<>();
                        StaffFamilyMap.put("perNo", fcStaffFamilyRela.getPerNo());
                        StaffFamilyMap.put("personID", fcStaffFamilyRela.getPersonID());
                        FCStaffFamilyRela fcStaffFamilyRela1 = fcStaffFamilyRelaMapper.selectByPrimaryKey(StaffFamilyMap);
                        if (fcStaffFamilyRela1 == null) {
                            int o = fcStaffFamilyRelaMapper.insert(fcStaffFamilyRela);
                        }
                    }
                    fcPerinfoFamilyTempMapper.updateSubStaus(map);
                }
            } else if ("1".equals(fcEnsure.getEnsureType())) {    //在校学生投保
                List<HashMap<String, String>> listPerIfo = new ArrayList<HashMap<String, String>>();
                Map<String, String> map = new HashMap<String, String>();
                // 获取需要同步的监护人总数
                List<Map<String, String>> needSyncNum = ensureMakeService.getNeedSyncGarNum(params.get("ensureCode"));
                if (needSyncNum.size() > 0) {
                    LisIDEA encryPassword = new LisIDEA();
                    for (int i = 0; i < needSyncNum.size(); i++) {
                        Calendar cal = Calendar.getInstance();
                        int year = cal.get(Calendar.YEAR);
                        HashMap<String, String> hashMap = new HashMap<String, String>();
                        hashMap.put("currentDate", DateTimeUtil.getCurrentDate());
                        hashMap.put("currentTime", DateTimeUtil.getCurrentTime());
                        hashMap.put("Operator", globalInput.getUserNo());
                        hashMap.put("year", year + "");
                        hashMap.put("ensureCode", params.get("ensureCode"));
                        hashMap.put("idNo", needSyncNum.get(i).get("IDNo"));
                        hashMap.put("nativeplace", needSyncNum.get(i).get("Nativeplace"));
                        hashMap.put("IDType", needSyncNum.get(i).get("IDType"));
                        hashMap.put("UserNo", maxNoService.createMaxNo("UserNo", "", 20));
                        hashMap.put("UserRole", maxNoService.createMaxNo("UserRole", "SN", 18));
                        hashMap.put("PwdSN", maxNoService.createMaxNo("PwdSN", "PWDSN", 15));
                        hashMap.put("RegDay", maxNoService.createMaxNo("RegDay", "", 20));
                        hashMap.put("StaffGrpPrem", "0.00");
                        hashMap.put("grpNo", fcGrpInfo.getGrpNo());
                        hashMap.put("perTempNo", needSyncNum.get(i).get("PerTempNo"));
                        //过渡Double类型
                        Map<String, String> ss = needSyncNum.get(i);
                        Object s = ss.get("studentGrpPrem");
                        if (s == null) {
                            s = 0.00;
                        }
                        hashMap.put("studentGrpPrem", String.valueOf(s));
                        FdUser fdUser = fdUserMapper.selectByPwd(needSyncNum.get(i).get("IDNo"));
                        if (fdUser == null) {
                            hashMap.put("PassWord", encryPassword.encryptString(needSyncNum.get(i).get("IDNo").substring(needSyncNum.get(i).get("IDNo").length() - 6)));
                        } else if (fdUser != null) {
                            hashMap.put("PassWord", fdUser.getPassWord());
                        }

                        Map<String, Object> perInfoMap = new HashMap<>();
                        perInfoMap.put("IDNo", needSyncNum.get(i).get("IDNo"));
                        perInfoMap.put("grpNo", fcGrpInfo.getGrpNo());
                        List<FCPerInfo> fcPerInfo = fcPerInfoMapper.selectByExample(perInfoMap);
                        if (fcPerInfo.size() < 1) {
                            hashMap.put("PerNo", maxNoService.createMaxNo("PerNo", "", 20));
                        } else {
                            hashMap.put("PerNo", fcPerInfo.get(0).getPerNo());
                        }
                        listPerIfo.add(hashMap);
                    }
                    map.put("ensureCode", params.get("ensureCode"));
                    map.put("year", Calendar.getInstance().get(Calendar.YEAR) + "");
                    map.put("currentDate", DateTimeUtil.getCurrentDate());
                    map.put("currentTime", DateTimeUtil.getCurrentTime());
                    map.put("grpNo", fcGrpInfo.getGrpNo());
                    map.put("ensureType", fcEnsure.getEnsureType());
                    map.put("planType", fcEnsure.getPlanType());
                    // 同步客户表FcPerInfo（更新）
                    log.info("更新FcPerInfo表开始。。。");
                    int i = fcPerInfoTempMapper.updateFcPerInfo(map);
                    log.info("更新FcPerInfo表完毕。。。");

                    // 同步客户表FdUser（更新）
                    log.info("更新FdUser开始。。。");
                    int q = fcPerInfoTempMapper.updateFdUser(map);
                    log.info("更新FdUser完毕。。。");

                    log.info("同步人员信息集合listPerIfo准备完毕，开始同步人员。。。");
                    int w = fcPerInfoTempMapper.insertFdUser(listPerIfo);
                    int e = fcPerInfoTempMapper.insertFdUserRole(listPerIfo);
                    int r = fcPerInfoTempMapper.insertFdPwdHist(listPerIfo);
                    int t = fcPerInfoTempMapper.insertFDusertomenugrp(listPerIfo);
                    // 同步员工注册期表 FCPerRegistDay
                    int y = fcPerInfoTempMapper.insertFCPerRegistDay(listPerIfo);
                    // 同步客户表FcPerInfo（插入）
                    int u = fcPerInfoTempMapper.insertFcPerInfo(listPerIfo);
                    // 同步完成，更新临时表数据为已提交
                    int o = fcPerInfoTempMapper.updateFCPerinfoTemp(map);

                    //同步学生信息
                    Map<String, Object> ensureInfoMap = new HashMap<>(params);
                    ensureInfoMap.put("subStaus", "01");
                    List<FCPerinfoFamilyTemp> fcPerinfoFamilyTempList = fcPerinfoFamilyTempMapper.getFamilyByEnsureCode(ensureInfoMap);
                    peoples = peoples + needSyncNum.size();
                    if (fcPerinfoFamilyTempList.size() > 0) {
                        Calendar cal = Calendar.getInstance();
                        int year = cal.get(Calendar.YEAR);
                        map.put("ensureCode", params.get("ensureCode"));
                        map.put("year", Calendar.getInstance().get(Calendar.YEAR) + "");
                        map.put("currentDate", DateTimeUtil.getCurrentDate());
                        map.put("currentTime", DateTimeUtil.getCurrentTime());
                        map.put("grpNo", fcGrpInfo.getGrpNo());
                        map.put("planType", fcEnsure.getPlanType());
                        int p = fcPersonMapper.updateFcPerSonStudent(map);
                        List<HashMap<String, String>> listStduentIfo = new ArrayList<HashMap<String, String>>();
                        for (FCPerinfoFamilyTemp fcPerinfoFamilyTemp : fcPerinfoFamilyTempList) {
                            //获取监护人学生关联表信息
                            FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
                            Map<String, String> familyInfo = new HashMap<>();
                            familyInfo.put("grpNo", fcGrpInfo.getGrpNo());
                            familyInfo.put("idNo", fcPerinfoFamilyTemp.getIDNo());
                            familyInfo.put("perIDNo", fcPerinfoFamilyTemp.getPerIDNo());
                            FCPerson fcPerson = fcPersonMapper.getFamPersonID(familyInfo);
                            if (fcPerson == null) {
                                HashMap<String, String> fcPersonMap = new HashMap<>();
                                fcPersonMap.put("familyTempNo", fcPerinfoFamilyTemp.getFamilyTempNo());
                                fcPersonMap.put("personID", maxNoService.createMaxNo("PersonID", null, 20));
                                fcPersonMap.put("ensureCode", fcPerinfoFamilyTemp.getEnsureCode());
                                fcPersonMap.put("IDType", fcPerinfoFamilyTemp.getIDType());
                                fcPersonMap.put("IDNo", fcPerinfoFamilyTemp.getIDNo());
                                fcPersonMap.put("operator", globalInput.getUserNo());
                                fcPersonMap.put("operatorCom", globalInput.getUserName());
                                fcPersonMap.put("makeDate", DateTimeUtil.getCurrentDate());
                                fcPersonMap.put("makeTime", DateTimeUtil.getCurrentTime());
                                fcPersonMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                                fcPersonMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                                int b = fcPersonMapper.insertMap(fcPersonMap);
                                fcStaffFamilyRela.setPersonID(fcPersonMap.get("personID"));
                            } else if (fcPerson != null) {
                                fcStaffFamilyRela.setPersonID(fcPerson.getPersonID());
                            }
                            fcStaffFamilyRela.setRelation(fcPerinfoFamilyTemp.getRelation());
                            fcStaffFamilyRela.setOperator(globalInput.getUserNo());
                            Map<String, Object> isEmptyMap = new HashMap<>();
                            isEmptyMap.put("IDNo", fcPerinfoFamilyTemp.getPerIDNo());
                            isEmptyMap.put("grpNo", fcGrpInfo.getGrpNo());
                            List<FCPerInfo> fcPerInfos = fcPerInfoMapper.isExitPerInfo(isEmptyMap); //调用之前已经写好的方法 返回List  一个企业下fcperinfo不循序重复，所以不会存在多个数据，既取第一条即可
                            if (fcPerInfos.size() > 0) {
                                fcStaffFamilyRela.setPerNo(fcPerInfos.get(0).getPerNo());
                            } else if (fcPerInfos.size() == 0) {
                                for (HashMap hashMap : listPerIfo) {
                                    if (fcPerinfoFamilyTemp.getPerIDNo().equals(hashMap.get("idNo"))) {
                                        fcStaffFamilyRela.setPerNo(String.valueOf(hashMap.get("PerNo")));
                                    }
                                }
                            }
                            fcStaffFamilyRela = (FCStaffFamilyRela) CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
                            //同步员工家属关联表
                            Map<String, String> StaffFamilyMap = new HashMap<>();
                            StaffFamilyMap.put("perNo", fcStaffFamilyRela.getPerNo());
                            StaffFamilyMap.put("personID", fcStaffFamilyRela.getPersonID());
                            FCStaffFamilyRela fcStaffFamilyRela1 = fcStaffFamilyRelaMapper.selectByPrimaryKey(StaffFamilyMap);
                            if (fcStaffFamilyRela1 == null) {
                                int a = fcStaffFamilyRelaMapper.insert(fcStaffFamilyRela);
                            }
                            HashMap<String, String> listMap = new HashMap<String, String>();
                            listMap.put("seriaNo", maxNoService.createMaxNo("DefaultPlan", "", 20));
                            listMap.put("grpNo", fcGrpInfo.getGrpNo());
                            listMap.put("personId", fcStaffFamilyRela.getPersonID());
                            listMap.put("ensureCode", fcEnsure.getEnsureCode());
                            listMap.put("year", year + "");
                            listMap.put("Operator", globalInput.getUserNo());
                            listMap.put("currentDate", DateTimeUtil.getCurrentDate());
                            listMap.put("currentTime", DateTimeUtil.getCurrentTime());
                            listMap.put("idNo", fcPerinfoFamilyTemp.getIDNo());
                            listMap.put("perTempNo", fcPerinfoFamilyTemp.getPerTempNo());
                            listStduentIfo.add(listMap);
                        }
                        //同步学生默认计划表fcDefaultPlan
                        fcPersonMapper.insertFCDefaultPlanStudent(listStduentIfo);
                        fcPerinfoFamilyTempMapper.updateSubStaus(map);
                        log.info("同步人员信息完成。。。");
                    }
                }
            }
            //同步企业人数
            peoples = peoples + fcGrpInfo.getPeoples();
            FCGrpInfo grpPeople = new FCGrpInfo();
            grpPeople.setGrpNo(params.get("grpNo"));
            grpPeople.setPeoples(peoples);
            fcGrpInfoMapper.updateByPrimaryKeySelective(grpPeople);

            FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(params.get("ensureCode"));
            String Phone = fcEnsureContact.getMobilePhone();

            //短信发送
            SendSMSReq sendSMSReq = new SendSMSReq();
            sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_005.getCode());
            sendSMSReq.setPhones(Phone);
            Map<String, Object> map = new HashMap<>();
            map.put("ensure_name", params.get("ensureName"));
            map.put("ensure_end_date", fcEnsure.getEndAppntDate());
            sendSMSReq.setParam(map);
            sendMessageService.sendSMS(sendSMSReq);

            fcEnsure.setEnsureState(ConstantUtil.EnsureState_1);
            fcEnsure.setGreenInsurance(StringUtil.isEmpty(params.get("greenInsurance")) ? fcEnsure.getGreenInsurance() : Boolean.valueOf(params.get("greenInsurance")));
            fcEnsure.setPolicyState("1");
            fcEnsure.setOperator(globalInput.getUserNo());
            fcEnsure = (FCEnsure) CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKey(fcEnsure);
            log.info("更新福利表fcEnsure投保状态完成。。。");
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "管理员复核通过");
        } catch (Exception e) {
            log.info("复核通过失败：" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "管理员复核失败");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 复核退回至待后台审核
     *
     * @param token
     * @param
     * @return
     */
    @Transactional
    public String toReviewReturn(String token, String ensureCode, String returnReason) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            FCEnsure fcEnsures = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            List<FCEnsureConfig> fcEnsureConfigList = new ArrayList<>();
            FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
            fcEnsureConfig.setEnsureCode(ensureCode);
            fcEnsureConfig.setGrpNo(fcEnsures.getGrpNo());
            fcEnsureConfig.setConfigNo("010");
            fcEnsureConfig.setConfigValue(format.format(new Date()) + "@@" + returnReason);
            fcEnsureConfig.setOperator(globalInput.getUserNo());
            fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
            fcEnsureConfig = CommonUtil.initObject(fcEnsureConfig, "INSERT");
            fcEnsureConfigList.add(fcEnsureConfig);
            fcEnsureConfigMapper.inserts(fcEnsureConfigList);
            if (fcEnsures.getEnsureState().equals("06")) {
                fcEnsures.setEnsureState(ConstantUtil.EnsureState_011);
                fcEnsures.setOperator(globalInput.getUserNo());
                fcEnsures = CommonUtil.initObject(fcEnsures, "UPDATE");
                fcEnsureMapper.updateByPrimaryKey(fcEnsures);
                log.info("更新福利表fcEnsure投保状态完成。。。");
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "退回待后台审核");
            } else if (fcEnsures.getEnsureState().equals("013")) {
                fcEnsures.setEnsureState(ConstantUtil.EnsureState_017);
                fcEnsures.setOperator(globalInput.getUserNo());
                fcEnsures = CommonUtil.initObject(fcEnsures, "UPDATE");
                fcEnsureMapper.updateByPrimaryKey(fcEnsures);
                log.info("更新福利表fcEnsure投保状态完成。。。");
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "日常计划退回待后台审核");
            } else if (fcEnsures.getEnsureState().equals("016")) {
                fcEnsures.setEnsureState(ConstantUtil.EnsureState_017);
                fcEnsures.setOperator(globalInput.getUserNo());
                fcEnsures = CommonUtil.initObject(fcEnsures, "UPDATE");
                fcEnsureMapper.updateByPrimaryKey(fcEnsures);
                log.info("更新福利表fcEnsure投保状态完成。。。");
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "日常计划退回待后台审核");
            } else {
                log.info("非复核管理员审核确认中不可进行福利回退。。。");
                resultMap.put("code", "500");
                resultMap.put("success", true);
                resultMap.put("message", "非复核管理员审核确认中不可进行福利回退");
            }
        } catch (Exception e) {
            log.info("复核退回失败：" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "复核退回失败");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 修改hr注册临时表
     *
     * @param token
     * @param hrRegist
     * @return
     */
    public String upGrpAndContactInfo(String token, HrRegist hrRegist) {
        Map<String, Object> resultMap = new HashMap<>();
        String message = "";
        try {
            //校验HR信息
            Map<String, String> map1 = new HashMap<>();
            map1.put("sign", "3");//1：员工 2：家属  3：HR
            map1.put("idType", hrRegist.getIdType());//证件类型
            map1.put("idNo", hrRegist.getIdNo());//证件号
            map1.put("birthDay", hrRegist.getBirthday());//出生日期
            map1.put("sex", hrRegist.getSex());//性别
            map1.put("nativeplace", hrRegist.getNativeplace());//国籍
            map1.put("idTypeEndDate", hrRegist.getIdTypeEndDate());//证件有效期
            map1.put("mobilePhone", hrRegist.getMobilePhone());//手机号
            String resultMsg = CheckUtils.checkSinglePeople(map1);
            if (StringUtils.isNotBlank(resultMsg)) {
                message = resultMsg;
                throw new RuntimeException();
            }
            int i1 = 0;
            int i2 = 0;
            int i3 = 0;
            if (StringUtils.isBlank(hrRegist.getIdTypeEndDate())) {
                hrRegist.setIdTypeEndDate(null);
            }

            hrRegist = CommonUtil.initObject(hrRegist, "UPDATE");
            //判断是否存在多条HR信息
            int count = fcGrpContactMapper.selectCountFCHrRegistTemp(hrRegist.getIdNo());
            if (count > 1) {//覆盖更新多条
                //修改临时表（HR+Grp）
                i1 = fcGrpContactMapper.updateHRinfo(hrRegist);
                //修改临时表（HR）
                i2 = fcGrpContactMapper.updateHRInfoByIdno(hrRegist);
            } else if (count <= 1) {//修改一条
                //修改临时表（HR+Grp）
                i3 = fcGrpContactMapper.updateHRinfo(hrRegist);
            }
            if ((count > 1 && (i1 != 1 || i2 <= 1)) || (count <= 1 && i3 != 1)) {
                throw new RuntimeException();
            } else {
                //短信发送
                FcHrRegistTemp fcHrRegistTemp = fcHrRegistTempMapper.selectByPrimaryKey(hrRegist.getRegistSN());
                SendSMSReq sendSMSReq = new SendSMSReq();
                sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_008.getCode());
                sendSMSReq.setPhones(fcHrRegistTemp.getMobilePhone());
                Map<String, Object> map = new HashMap<>();
                map.put("grp_name", hrRegist.getGrpName());
                sendSMSReq.setParam(map);
                sendMessageService.sendSMS(sendSMSReq);
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "企业联系人以及企业相关信息修改成功");
            }
        } catch (Exception e) {
            log.info("HR以及企业信息修改失败" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "企业联系人以及企业相关信息修改失败：" + message);
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 获取平台企业信息
     *
     * @param getGrpListReq
     * @return
     */
    public String getGrpList(String token, GetGrpListReq getGrpListReq) {
        Map<String, Object> resulrMap = new HashMap<>();
        try {
            Map<String, String> paramsMap = new HashMap<>();
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            String manageCom = globalInput.getManageCom();
            if (org.springframework.util.StringUtils.isEmpty(manageCom)) {
                throw new SystemException("当前操作用户管理机构为空！");
            } else {
                paramsMap.put("manageCom", manageCom);
            }
            paramsMap.put("grpName", getGrpListReq.getGrpName());
            paramsMap.put("grpIdType", getGrpListReq.getGrpIdType());
            paramsMap.put("grpIdNo", getGrpListReq.getGrpIdNo());
            PageHelper.startPage(getGrpListReq.getPageNo(), getGrpListReq.getPageSize());
            List<FCGrpInfo> fcGrpInfoList = fcGrpInfoMapper.getGrpListByNameOrCode(paramsMap);
            PageHelperUtil<FCGrpInfo> teamPageInfo = new PageHelperUtil<>(fcGrpInfoList);
            if (fcGrpInfoList.size() > 0) {
                resulrMap.put("message", "企业信息查询成功！");
            } else {
                resulrMap.put("message", "未查询到相关企业信息！");
            }
            resulrMap.put("recordsTotal", teamPageInfo.getTotal());
            resulrMap.put("data", teamPageInfo.getList());
            resulrMap.put("code", "200");
            resulrMap.put("success", true);
        } catch (Exception e) {
            log.info("error", e);
            resulrMap.put("code", "500");
            resulrMap.put("success", false);
            resulrMap.put("message", "企业信息查询失败，请联系运维人员");
        } finally {
            return JSON.toJSONString(resulrMap);
        }
    }

    /**
     * 福利设置页面初始化
     *
     * @param token
     * @param ensureCode
     * @return
     */
    public String getEnsureInfoByCode(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("grpNo", fcEnsure.getGrpNo());
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            String manageCom = globalInput.getManageCom();
            if (StringUtils.isEmpty(manageCom) && !globalInput.getCustomType().matches("^(1|2)")) {
                throw new SystemException("当前用户管理机构为空！");
            } else {
                mapInfo.put("manageCom", manageCom);
            }
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.getGrpListByNameOrCode(mapInfo).get(0);
            resultMap.put("fcEnsureInfo", fcEnsure);
            resultMap.put("fcGrpInfo", fcGrpInfo);
            resultMap.put("code", "200");
            resultMap.put("success", false);
            resultMap.put("message", "福利设置相关信息查询成功");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "福利设置相关信息查询失败，请联系运维人员");
        } finally {
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 获取当前福利未选择险种列表
     *
     * @param ensureCode
     * @return
     */
    public String getRiskInfo(String ensureCode, String riskCode, String riskName) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("riskCode", riskCode);
            mapInfo.put("riskName", riskName);
            List<FDRiskInfo> fdRiskInfoList = fdRiskInfoMapper.selectByEnsureCode(mapInfo);
            List<FDRiskDutyInfo> fdRiskDutyInfoList = new ArrayList<>();
            for (FDRiskInfo riskInfo : fdRiskInfoList) {
                if ("15070".equals(riskInfo.getRiskCode())) {
                    fdRiskDutyInfoList = fdRiskDutyInfoMapper.getRisk_15070ByEnsureCode(ensureCode);
                }
            }
            resultMap.put("data", fdRiskInfoList);
            resultMap.put("_15070DutyList", fdRiskDutyInfoList);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "未添加险种列表查询成功！");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "已添加险种列表信息查询失败！");
        } finally {
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 获取已添加险种列表信息
     *
     * @param token
     * @param ensureCode
     * @return
     */
    public String getAddedRiskInfo(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            List<FcPlanRiskInfo> fcPlanRiskInfoList = fcPlanRiskInfoMapper.getAddedRiskInfo(mapInfo);
            List<FcPlanRiskInfo> fcPlanRiskInfoList_15070 = fcPlanRiskInfoMapper.getAddedRiskInfoBy15070(ensureCode);
            resultMap.put("ensureInfo", fcEnsure);
            resultMap.put("data", fcPlanRiskInfoList);
            resultMap.put("data_15070", fcPlanRiskInfoList_15070);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "已添加险种列表信息查询成功！");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "已添加险种列表信息查询失败！");
        } finally {
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 查询所选险种相关责任信息
     *
     * @param token
     * @param riskCode
     * @param dutyCode
     * @return
     */
    public String getRiskAndDutyInfo(String token, String ensureCode, String riskCode, String dutyCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("riskCode", riskCode);
            mapInfo.put("dutyCode", dutyCode);
            String riskType = "";
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "必要参数缺失，新增险种失败！");
            if (StringUtils.isBlank(ensureCode) || StringUtils.isBlank(riskCode)) {
                return JSON.toJSONString(resultMap);
            } else if ("15070".equals(riskCode)) {
                if (StringUtils.isBlank(dutyCode)) {
                    return JSON.toJSONString(resultMap);
                }
            }
            if (!riskCode.equals("15070") || dutyCode.equals("GD0053")) {
                riskType = "01";
            } else if (dutyCode.equals("GD0050")) {
                riskType = "04";
            } else if (dutyCode.equals("GD0051")) {
                riskType = "02";
            } else if (dutyCode.equals("GD0052")) {
                riskType = "03";
            } else {
                riskType = "05";
            }
            FcPlanRiskInfo fcPlanRiskInfo = fcPlanRiskInfoMapper.selectByPrimaryKey(ensureCode, riskCode, riskType);
            // 删除未保存险种的保额档次
            if (fcPlanRiskInfo == null) {
                int i = fcDutyAmountGradeMapper.delByEnsureCode(mapInfo);
            }
            List<Map<String, String>> dutyAmountGradeInfoMap = fdRiskDutyInfoMapper.getRiskAndDutyInfo(mapInfo);
            resultMap.put("dutyInfoMap", dutyAmountGradeInfoMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "所选险种相关责任查询成功！");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "所选险种相关责任查询失败！");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 查询所选险种相关责任信息
     *
     * @param token
     * @param riskCode
     * @param dutyCode
     * @return
     */
    public String getDutyAmountGradeInfo(String token, String ensureCode, String riskCode, String dutyCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("riskCode", riskCode);
            mapInfo.put("dutyCode", dutyCode);
            List<Map<String, String>> dutyInfoMap = new ArrayList<>();
            List<Map<String, String>> dutyAmountGradeInfoMap = new ArrayList<>();
            dutyInfoMap = fdRiskDutyInfoMapper.getRiskAndDutyInfo(mapInfo);
            dutyAmountGradeInfoMap = fdRiskDutyInfoMapper.getRiskAndDutyInfoByDutyCode(mapInfo);
            resultMap.put("data", dutyAmountGradeInfoMap);
            resultMap.put("dutyInfoMap", dutyInfoMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "所选险种相关责任查询成功！");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "所选险种相关责任查询失败！");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 复核退回HR/后台定制
     *
     * @param token
     * @param ensureCode
     * @param returnNode   0--退回HR定制   1--退回后台定制
     * @param returnReason
     * @return
     */
    @Transactional
    public String efelxEnsureReturn(String token, String ensureCode, String returnNode, String returnReason) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            String error = "";
            GlobalInput globalInput = userService.getSession(token);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if (StringUtils.isBlank(ensureCode)) {
                error = "弹性计划回退失败：福利编号缺失，请联系平台维护人员。";
            } else if (StringUtils.isBlank(returnNode)) {
                error = "弹性计划回退失败：回退节点不可为空。";
            } else if (StringUtils.isBlank(returnReason)) {
                error = "弹性计划回退失败：回退原因不可为空。";
            } else if (!"1".equals(fcEnsure.getPlanType())) {
                error = "所选福利属于固定计划，请联系平台维护人员进行福利退回。";
            }
            if (StringUtils.isNotBlank(error)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", error);
                return JSON.toJSONString(resultMap);
            }
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            List<FCEnsureConfig> fcEnsureConfigList = new ArrayList<>();
            FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
            fcEnsureConfig.setEnsureCode(ensureCode);
            fcEnsureConfig.setGrpNo(fcEnsure.getGrpNo());
            fcEnsureConfig.setConfigNo("010");
            fcEnsureConfig.setConfigValue(format.format(new Date()) + "@@" + returnReason);
            FDusertomenugrpKey fDusertomenugrpKey = fDusertomenugrpMapper.findMenuInfo(globalInput.getUserNo());
            // 3--初审菜单  4--复核菜单
            if ("3".equals(fDusertomenugrpKey.getMenuGrpCode())) {
                fcEnsureConfig.setOperator("0");
            } else {
                fcEnsureConfig.setOperator("1");
            }
            fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
            fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
            fcEnsureConfigList.add(fcEnsureConfig);

            //退回 新增之前，如果已经退回，删除原来数据configNo=10或12的
            int count = fcEnsureConfigMapper.selectEnsureConfigByEnsureCode(ensureCode);
            if (count != 0) {
                fcEnsureConfigMapper.deleteByEnsureCode(ensureCode);
            }

            //复核退回Hr定制
            if ("0".equals(returnNode)) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_04);
                fcEnsureConfig = new FCEnsureConfig();
                fcEnsureConfig.setEnsureCode(ensureCode);
                fcEnsureConfig.setGrpNo(fcEnsure.getGrpNo());
                fcEnsureConfig.setConfigNo("012");
                fcEnsureConfig.setConfigValue("0");
                fcEnsureConfig.setOperator(globalInput.getUserNo());
                fcEnsureConfig.setSerialNo(maxNoService.createMaxNo("EnsureConfig", "", 20));
                fcEnsureConfig = (FCEnsureConfig) CommonUtil.initObject(fcEnsureConfig, "INSERT");
                fcEnsureConfigList.add(fcEnsureConfig);

            }//复核退回后台定制
            else if ("1".equals(returnNode)) {
                fcEnsure.setEnsureState(ConstantUtil.EnsureState_011);
            }
            fcEnsure.setOperator(globalInput.getUserNo());
            fcEnsure = (FCEnsure) CommonUtil.initObject(fcEnsure, "UPDATE");
            fcEnsureMapper.updateByPrimaryKey(fcEnsure);
            log.info("弹性计划复核退回：" + (fcEnsure.getEnsureState().equals("04") ? "HR" : "后台"));
            fcEnsureConfigMapper.inserts(fcEnsureConfigList);
            String Phone = fcEnsureContactMapper.selectByPrimaryKey(ensureCode).getMobilePhone();
            //短信发送
            SendSMSReq sendSMSReq = new SendSMSReq();
            sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_006.getCode());
            sendSMSReq.setPhones(Phone);
            Map<String, Object> map = new HashMap<>();
            map.put("ensure_name", fcEnsure.getEnsureName());
            sendSMSReq.setParam(map);
            SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
            if (sendMessageResp.getSuccess()) {
                resultMap.put("fcEnsure", fcEnsure);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "弹性福利已成功退回。");
            }
        } catch (Exception e) {
            log.info("error", e);
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 整单确认 -- 订单查询
     *
     * @param token
     * @param ensureCode
     * @param orderStatus
     * @return
     */
    public String getOrderPerInfoByEnsureCode(String token, String ensureCode, String orderStatus, String name, int pageNo, int pageSize) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtils.isBlank(ensureCode)) {
                resultMap.put("message", "请录入所要查询的福利编号。");
                throw new RuntimeException();
            }
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            String manageCom = globalInput.getManageCom();
            //兼容 hr 端使用
            if (StringUtils.isEmpty(manageCom) && ROLE_TYPE_TWO.equals(globalInput.getRoleType())) {
                // 赋予复审岗权限
                manageCom = "86";
            }
            if (StringUtils.isEmpty(manageCom)) {
                throw new SystemException("当前用户管理机构为空！");
            }
            // 根据可管理的机构和福利编码查询
            SelectEnsureByManageComReq selectEnsureByManageComReq = new SelectEnsureByManageComReq();
            selectEnsureByManageComReq.setEnsureCode(ensureCode);
            selectEnsureByManageComReq.setManageCom(manageCom);
            FCEnsure fcEnsure = fcEnsureMapper.selectEnsureByManageCom(selectEnsureByManageComReq);
            if (fcEnsure == null) {
                resultMap.put("message", "未查询到该福利，请核实后再次录入。");
                throw new RuntimeException();
            }
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            if (StringUtils.isBlank(orderStatus)) {
                orderStatus = "08";
            }
            mapInfo.put("orderStatus", orderStatus);
            mapInfo.put("name", name);
            PageHelper.startPage(pageNo, pageSize);
            List<Map<String, String>> fcOrderList = fcOrderMapper.getOrderListByEnsureCode(mapInfo);
            Map<String, Object> params = new HashMap<>();
            params.put("ensureCode", ensureCode);
            //该福利下导入员工总数
            Integer ensureCount = fcPerInfoTempMapper.selectHas(ensureCode);
            //员工登录平台投保人数
            Integer platStaCount = fcOrderMapper.selectLoginOrderCountByEnsureCode(ensureCode);
            // 员工投保人数（即订单数，整单确认上部分展示数量）
            Integer orderNumber = fcOrderMapper.selectOrderCountByEnsureCode(ensureCode);
            // 获取总投保人数(员工+家属)
            Integer staFamilyCount = fcOrderItemMapper.selectOrderItemCountByEnsureCode(ensureCode);
            if (fcOrderList.size() > 0) {
                for (Map<String, String> orderInfo : fcOrderList) {
                    List<FCOrderItem> fcOrderItems = fcOrderItemMapper.selectOrderNo(orderInfo.get("orderNo"));
                    double sumPrem = 0.00;
                    double grpPrem = 0.00;
                    double selPrem = 0.00;
                    for (FCOrderItem orderItemInfo : fcOrderItems) {
                        grpPrem = CommonUtil.add(grpPrem, orderItemInfo.getGrpPrem());
                        selPrem = CommonUtil.add(selPrem, orderItemInfo.getSelfPrem());
                    }
                    sumPrem = CommonUtil.add(grpPrem, selPrem);
                    orderInfo.put("sumPrem", String.valueOf(sumPrem));
                    orderInfo.put("grpPrem", String.valueOf(grpPrem));
                    orderInfo.put("selPrem", String.valueOf(selPrem));
                }
                resultMap.put("data", fcOrderList);
                resultMap.put("message", "整单确认订单查询成功。");
            } else {
                resultMap.put("message", "未查询到相关订单。");
            }

            resultMap.put("total", fcOrderList.size());
            resultMap.put("ensureCode", ensureCode);
            // 该福利下导入员工总数
            resultMap.put("ensureCount", ensureCount);
            // 员工登录平台投保人数
            resultMap.put("platStaCount", platStaCount);
            // 员工投保总数
            resultMap.put("orderNumber", orderNumber);
            // 总投保人数
            resultMap.put("staFamilyCount", staFamilyCount);
            resultMap.put("openDay", fcEnsure.getStartAppntDate());
            resultMap.put("closeDay", fcEnsure.getEndAppntDate());
            resultMap.put("success", true);
            resultMap.put("code", "200");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
        } finally {
            return JSON.toJSONString(resultMap);
        }
    }


    /**
     * 订单详情查询
     *
     * @param token
     * @param orderNo
     * @return
     */
    public String queryOrderDetails(String token, String ensureCode, String orderNo, String perNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            List<Map<String, Object>> insuredList = fcOrderInsuredMapper.getOrderItemInfoList(orderNo, perNo);
            //固定计划
            if ("0".equals(fcEnsure.getPlanType())) {
                for (Map<String, Object> insuredInfo : insuredList) {
                    BigDecimal grpPrem = (BigDecimal) insuredInfo.get("grpPrem");
                    BigDecimal selPrem = (BigDecimal) insuredInfo.get("selPrem");
                    double sumPrem = grpPrem.add(selPrem).doubleValue();
                    insuredInfo.put("sumPrem", sumPrem);
                    String planCode = insuredInfo.get("planCode").toString();
                    List<Map<String, String>> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyListByPlanCode(ensureCode, planCode);
                    //只靠上行代码无法显示免赔额，免赔额属性，赔付比例，只有遍历添加 （若数据库中为空，则返回也为空）
                    for (Map<String, String> fcPlanRiskDuty : fcPlanRiskDutyList) {
                        //免赔额
                        if (null == fcPlanRiskDuty.get("getLimit")) {
                            fcPlanRiskDuty.put("getLimit", "");
                        } else {
                            fcPlanRiskDuty.put("getLimit", fcPlanRiskDuty.get("getLimit"));
                        }
                        //免赔额属性
                        if (null == fcPlanRiskDuty.get("getLimitType")) {
                            fcPlanRiskDuty.put("getLimitType", "");
                        } else {
                            fcPlanRiskDuty.put("getLimitType", fcPlanRiskDuty.get("getLimitType"));
                        }
                        //赔付比例
                        if (null == fcPlanRiskDuty.get("getRatio")) {
                            fcPlanRiskDuty.put("getRatio", "");
                        } else {
                            fcPlanRiskDuty.put("getRatio", fcPlanRiskDuty.get("getRatio"));
                        }
                        //最大赔付天数
                        if (null == fcPlanRiskDuty.get("maxGetDay")) {
                            fcPlanRiskDuty.put("maxGetDay", "");
                        } else {
                            fcPlanRiskDuty.put("maxGetDay", fcPlanRiskDuty.get("maxGetDay"));
                        }
                    }
                    insuredInfo.put("dutyList", fcPlanRiskDutyList);
                }
                resultMap.put("data", insuredList);
            }//弹性计划
            else if ("1".equals(fcEnsure.getPlanType())) {
                List<FCStaffFamilyRela> fcStaffFamilyRelaList = fcStaffFamilyRelaMapper.getPersonByInfo(perNo);
                for (FCStaffFamilyRela fcStaffFamilyRela : fcStaffFamilyRelaList) {
                    Map<String, String> mapInfo = new HashMap<>();
                    mapInfo.put("ensureCode", ensureCode);
                    mapInfo.put("personID", fcStaffFamilyRela.getPersonID());
                    FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectInsuredByPersonID(mapInfo);
                    if (fcOrderInsured != null) {
                        List<Map<String, String>> fcPlanRiskDutyList = fpInsureEflexPlanMapper.selectDutyListByPersonID(fcOrderInsured.getOrderItemNo());
                        //上述代码若查询出最大赔付天数为null的话，则无法显示最大赔付天数 （若数据库中为空，则返回也为空）
                        for (Map<String, String> fcPlanRiskDuty : fcPlanRiskDutyList) {
                            //最大赔付天数
                            if (null == fcPlanRiskDuty.get("maxGetDay")) {
                                fcPlanRiskDuty.put("maxGetDay", "");
                            } else {
                                fcPlanRiskDuty.put("maxGetDay", fcPlanRiskDuty.get("maxGetDay"));
                            }
                        }
                        for (Map<String, Object> insuredInfo : insuredList) {
                            if (insuredInfo.get("personID").toString().equals(fcStaffFamilyRela.getPersonID())) {
                                insuredInfo.put("dutyList", fcPlanRiskDutyList);
                                BigDecimal grpPrem = (BigDecimal) insuredInfo.get("grpPrem");
                                BigDecimal selPrem = (BigDecimal) insuredInfo.get("selPrem");
                                double sumPrem = grpPrem.add(selPrem).doubleValue();
                                insuredInfo.put("sumPrem", sumPrem);
                            }
                        }
                    }
                }
                resultMap.put("data", insuredList);
            } else {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "计划类型错误。");
            }
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "订单详情查询成功。");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "查询失败。");
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 重置投保结束日期
     *
     * @param token
     * @param ensureCode
     * @param closeDay
     * @return
     */
    @Transactional
    public String resetInsureDate(String token, String ensureCode, String closeDay) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            GlobalInput globalInput = userService.getSession(token);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            int compareTo = closeDay.compareTo(fcEnsure.getStartAppntDate());
            if (compareTo < 0) {
                resultMap.put("message", "投保结束日期小于投保开始日期。");
                return JSON.toJSONString(resultMap);
            } else if (StringUtils.isNotBlank(fcEnsure.getPolicyState())) {
                String policyState = fcEnsure.getPolicyState();
                if ("2".equals(policyState) || "3".equals(policyState) || "4".equals(policyState)) {
                    resultMap.put("message", "已签单，不能重置投保结束日期。");
                    return JSON.toJSONString(resultMap);
                }
            }
            FCEnsure fcEnsureInfo = new FCEnsure();
            fcEnsureInfo.setEnsureCode(ensureCode);
            fcEnsureInfo.setEndAppntDate(closeDay);
            fcEnsureInfo.setOperator(globalInput.getUserNo());
            fcEnsureInfo = CommonUtil.initObject(fcEnsureInfo, "UPDATE");
            fcEnsureMapper.updateByPrimaryKeySelective(fcEnsureInfo);
            FCPerRegistDay fcPerRegistDay = new FCPerRegistDay();
            fcPerRegistDay.setEnsureCode(ensureCode);
            fcPerRegistDay.setCloseDay(closeDay);
            fcPerRegistDay.setOperator(globalInput.getUserNo());
            fcPerRegistDay = CommonUtil.initObject(fcPerRegistDay, "UPDATE");
            fcPerRegistDayMapper.updateCloseDayByEnsureCode(fcPerRegistDay);
            fcOrderMapper.updateCloseDayByEnsureCode(ensureCode, closeDay);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "投保结束日期重置完成。");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("error", e);
            throw new RuntimeException();
        }
    }

    /**
     * 整单确认--导出投保清单
     *
     * @param Authorization
     * @param response
     * @param ensureCode
     * @param orderStatus
     * @return
     */
    public String eflexExportInsureInfoExcel(String Authorization, HttpServletResponse response, String ensureCode, String orderStatus) {
        ResponseMsg<Object> responseMsg = new ResponseMsg<>();
        try {
            if (StringUtils.isBlank(ensureCode)) {
                responseMsg.errorStatus().message("福利编号为空，导出人员清单EXCEL失败!");
                return JSON.toJSONString(responseMsg);
            }
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if (fcEnsure == null) {
                responseMsg.errorStatus().message("未查询到该福利，导出人员清单EXCEL失败!");
                return JSON.toJSONString(responseMsg);
            }
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("ensureCode", ensureCode);
            mapInfo.put("name", orderStatus);
            FCFileDocMain fcFileDocMain = new FCFileDocMain();
            if ("0".equals(fcEnsure.getPlanType())) {
                fcFileDocMain = fcFileDocMainMapper.selectByPrimaryKey("00000003");
            } else if ("1".equals(fcEnsure.getPlanType())) {
                fcFileDocMain = fcFileDocMainMapper.selectByPrimaryKey("00000019");
            }
            String filePath = fcFileDocMain.getFilePath();
            String filename = fcFileDocMain.getFileSaveName() + fcFileDocMain.getFileSuffix();
            File file = new File(filePath + filename);
            Workbook wb = WorkbookFactory.create(new FileInputStream(file));
            // 调用生成excel生成工具类,返回前台下载
            ServletOutputStream outputStream = response.getOutputStream();
            String filenameF = new String((fcFileDocMain.getFileName() + fcFileDocMain.getFileSuffix()).getBytes("UTF-8"), "iso-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + filenameF);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            // excel表格数据
            if ("0".equals(fcEnsure.getPlanType())) {
                //固定计划
                wb = ensureQueryService.insuredPlanSheet(wb, fcEnsure.getGrpNo(), mapInfo);
            } else if ("1".equals(fcEnsure.getPlanType())) {
                //弹性计划
                wb = efleInsureInfoSheet(wb, mapInfo);
            }
            wb.write(outputStream);
            outputStream.flush();
            responseMsg.okStatus().message("整单确认-导出人员清单EXCEL成功!");
        } catch (Exception e) {
            log.info("整单确认-导出人员清单EXCEL失败!", e);
            responseMsg.errorStatus().message("整单确认-导出人员清单EXCEL失败!");
        }
        return JSON.toJSONString(responseMsg);
    }

    //弹性计划 投保清单导出
    public Workbook efleInsureInfoSheet(Workbook wb, Map<String, String> mapInfo) {
        try {
            //获取证件类型和证件号码的Value-Key
            List<HashMap<String, Object>> idTypeCodeList = fdCodeMapper.CodeInfo("IDType");
            //团单
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(mapInfo.get("ensureCode"));
            //订单
            List<Map<String, String>> fcOrderList = fcOrderMapper.getOrderListByEnsureCode(mapInfo);
            //团体投保人
            FCGrpApplicant fcGrpApplicant = fcGrpApplicantMapper.selectByPrimaryKey(fcGrpOrder.getGrpAppNo());
            //缴费方式
            Map<String, Object> params = new HashMap<>();
            params.put("ensureCode", mapInfo.get("ensureCode"));
            params.put("configNo", "008");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(params);
            //投保清单sheet页
            Sheet staSheet = (Sheet) wb.getSheetAt(0);
            Row insureRow = staSheet.getRow(1);
            //投保单号
            insureRow.getCell(2).setCellValue(fcGrpOrder.getPrtNo());
            //投保人
            insureRow.getCell(4).setCellValue(fcGrpApplicant.getGrpName());
            //投保人数
            int perSum = 0;
            //确定填充第一行行数
            int rowIndex = 5;
            int j = 0;
            for (Map<String, String> orderInfo : fcOrderList) {
                List<Map<String, Object>> insuredList = fcOrderInsuredMapper.getOrderItemInfoList(orderInfo.get("orderNo"), orderInfo.get("perNo"));
                perSum += insuredList.size();
                for (int i = 0; i < insuredList.size(); i++, j++) {
                    Row row = staSheet.createRow(rowIndex);
                    rowIndex++;
                    row.setHeightInPoints((float) 20);
                    CellStyle inCellStyle = this.cellStyleBorder(wb);
                    // 创建投保清单页单元格
                    for (int rowNum = 0; rowNum < 84; rowNum++) {
                        Cell cell = row.createCell(rowNum);
                        cell.setCellStyle(inCellStyle);
                    }
                    row.getCell(0).setCellValue(j + 1);
                    row.getCell(1).setCellValue(orderInfo.get("name"));
                    row.getCell(2).setCellValue(insuredList.get(i).get("name").toString());
                    String relation = fdCodeMapper.selectNameByCode("Relation", insuredList.get(i).get("relation").toString());
                    row.getCell(3).setCellValue(relation);
                    if ("0".equals(insuredList.get(i).get("sex").toString())) {
                        row.getCell(4).setCellValue("男");
                    } else if ("1".equals(insuredList.get(i).get("sex").toString())) {
                        row.getCell(4).setCellValue("女");
                    }
                    row.getCell(5).setCellValue(insuredList.get(i).get("birthday").toString());
                    String idTypeName = "";
                    for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
                        if ((insuredList.get(i).get("iDType").toString()).equals(hashMap1.get("CodeKey").toString())) {
                            idTypeName = hashMap1.get("CodeName").toString();
                            break;
                        }
                    }
                    row.getCell(6).setCellValue(idTypeName);
                    row.getCell(7).setCellValue(insuredList.get(i).get("iDNo").toString());
                    row.getCell(8).setCellValue(insuredList.get(i).get("occupationType").toString());
                    row.getCell(9).setCellValue(insuredList.get(i).get("occupationCode").toString());
                    if ("0".equals(insuredList.get(i).get("joinMedProtect").toString())) {
                        row.getCell(10).setCellValue("无");
                    } else if ("1".equals(insuredList.get(i).get("joinMedProtect").toString())) {
                        row.getCell(10).setCellValue("有");
                    }
                    row.getCell(11).setCellValue(insuredList.get(i).get("mobilePhone").toString());
                    row.getCell(12).setCellValue(insuredList.get(i).get("email") == null ? "" : insuredList.get(i).get("email").toString());
                    //缴费方式
                    String paymentType = fdCodeMapper.selectNameByCode("PaymentType", fcEnsureConfig.getConfigValue());
                    row.getCell(13).setCellValue(paymentType);
                    row.getCell(14).setCellValue(insuredList.get(i).get("grpPrem").toString());
                    row.getCell(15).setCellValue(insuredList.get(i).get("selPrem").toString());
                    row.getCell(16).setCellValue(orderInfo.get("openBank"));
                    row.getCell(17).setCellValue(orderInfo.get("openPer"));
                    row.getCell(18).setCellValue(orderInfo.get("openAccount"));
                    //身故受益人姓名、与被保人关系、已投保身故保额总和
                    row.getCell(19).setCellValue("法定继承人");
                    row.getCell(20).setCellValue("");
                    row.getCell(21).setCellValue("");

                    Map<String, String> map = new HashMap<>();
                    map.put("ensureCode", mapInfo.get("ensureCode"));
                    map.put("personID", insuredList.get(i).get("personID").toString());
                    FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectInsuredByPersonID(map);
                    List<Map<String, String>> fcPlanRiskDutyList = fpInsureEflexPlanMapper.selectDutyListByPersonID(fcOrderInsured.getOrderItemNo());
                    double sumsPrem = 0.00;
                    sumsPrem = createRiskInfoRow(i, row, sumsPrem, fcPlanRiskDutyList);
                    row.getCell(83).setCellValue(sumsPrem);
                }
            }
            insureRow.getCell(6).setCellValue(perSum);
        } catch (Exception e) {
            log.info("error", e);
        }
        return wb;
    }

    /**
     * 启用/禁用员工
     *
     * @param token
     * @param perNo
     * @param lockSign 0-启用  1-禁用
     * @return
     */
    public String updatePerInfoLockState(String token, String ensureCode, List<String> perNo, String lockSign) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "500");
        resultMap.put("success", false);
        String logkSignStr = "";
        try {
            GlobalInput globalInput = userService.getSession(token);
            if (StringUtils.isBlank(ensureCode) || StringUtils.isBlank(lockSign)) {
                resultMap.put("message", "参数缺失。");
                return JSON.toJSONString(resultMap);
            }
            logkSignStr = lockSign.equals("0") ? "加入" : "移出";
            if (perNo.size() < 1) {
                resultMap.put("message", "请勾选要" + logkSignStr + "的员工。");
                return JSON.toJSONString(resultMap);
            }
            for (String pernoInfo : perNo) {
                FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(pernoInfo);
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
                FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(fcEnsure.getGrpNo());
                Map<String, Object> mapInfo = new HashMap<>();
                mapInfo.put("ensureCode", ensureCode);
                mapInfo.put("perNo", pernoInfo);
                FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(mapInfo);
                fcPerRegistDay.setLockState(lockSign);
                fcPerRegistDay.setOperatorCom(globalInput.getUserName());
                fcPerRegistDay = CommonUtil.initObject(fcPerRegistDay, "UPDATE");
                fcPerRegistDayMapper.updateByPrimaryKey(fcPerRegistDay);
                //加入或移除无需发送短信
                //String sendMessage = "";
                //
                //if (lockSign.equals("0")) {
                //    sendMessage = "尊敬的" + fcPerInfo.getName() + "，[" + fcGrpInfo.getGrpName() + "]企业的保险经办人启用了您的信息，建议您在该福利：[" + fcEnsure.getEnsureName() + "]开放期内(投保结束日期)尽快登录中韩弹性福利平台选择保险计划。";
                //} else {
                //    sendMessage = "尊敬的" + fcPerInfo.getName() + "，[" + fcGrpInfo.getGrpName() + "]企业为您定制的福利：[" + fcEnsure.getEnsureName() + "]的开放期结束，" + "因您未在开放期内登录中韩弹性福利平台选择保险计划，企业保险经办人了您的信息，您将无法投保该福利，请知悉。";
                //}
                //sendMessageService.sendMessage(fcPerInfo.getMobilePhone(), sendMessage);
            }
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", logkSignStr + "成功!");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("message", logkSignStr + "失败!");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 获取福利未投保人员信息
     *
     * @return
     */
    public String queryUnInsuredInfo(String ensureCode, String name, String sex,
                                     String nativeplace, String idType, String idNo, int pageNo, int pageSize) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "500");
        resultMap.put("success", false);
        try {
            if (StringUtils.isBlank(ensureCode)) {
                resultMap.put("message", "请输入福利编码。");
                return JSON.toJSONString(resultMap);
            }
            Map<String, String> perInfoMap = new HashMap<>();
            perInfoMap.put("ensureCode", ensureCode);
            perInfoMap.put("name", name);
            perInfoMap.put("sex", sex);
            perInfoMap.put("nativeplace", nativeplace);
            perInfoMap.put("idType", idType);
            perInfoMap.put("idNo", idNo);
            PageHelper.startPage(pageNo, pageSize);
            List<Map<String, String>> perInfoMapList = fcPerInfoMapper.queryUnInsuredList(perInfoMap);
            PageHelperUtil<Map<String, String>> teamPageInfo = new PageHelperUtil<>(perInfoMapList);
            if (perInfoMapList.size() > 0) {
                resultMap.put("message", "未投保人员清单查询成功。");
            } else {
                resultMap.put("message", "不存在未投保人员清单。");
            }
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("unInsuredList", teamPageInfo.getList());
            resultMap.put("dataTotal", teamPageInfo.getTotal());
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("message", "未投保人员清单查询失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 导出未投保及禁用人员清单
     *
     * @param requestMap
     * @param response
     * @return
     */
    public String exportEmployUnInsurdPerInfo(Map<String, String> requestMap, HttpServletResponse response) {
        ResponseMsg<Object> responseMsg = new ResponseMsg<>();
        try {
            List<Map<String, String>> perInfoMapList = fcPerInfoMapper.queryUnInsuredList(requestMap);
            FCFileDocMain fcFileDocMain = fcFileDocMainMapper.selectByPrimaryKey("00000025");
            String filePath = fcFileDocMain.getFilePath();
            String filename = fcFileDocMain.getFileSaveName() + fcFileDocMain.getFileSuffix();
            File file = new File(filePath + filename);
            Workbook wb = WorkbookFactory.create(new FileInputStream(file));
            // 调用生成excel生成工具类,返回前台下载
            ServletOutputStream outputStream = response.getOutputStream();
            String filenameF = new String((fcFileDocMain.getFileName() + fcFileDocMain.getFileSuffix()).getBytes("UTF-8"), "iso-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + filenameF);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            // excel表格数据
            wb = unInsuredPerinfoSheet(wb, perInfoMapList);
            wb.write(outputStream);
            outputStream.flush();
            responseMsg.okStatus().message("未投保人员清单导出excel成功!");
        } catch (Exception e) {
            log.error("未投保人员清单导出excel失败", e);
            responseMsg.errorStatus().message("未投保人员清单导出excel失败");
        }
        return JSON.toJSONString(responseMsg);
    }

    /**
     * 导出未投保及禁用人员清单
     *
     * @param wb
     */
    public Workbook unInsuredPerinfoSheet(Workbook wb, List<Map<String, String>> perInfoList) {
        Sheet unInsuredSheet = (Sheet) wb.getSheetAt(0);
        for (int i = 0; i < perInfoList.size(); i++) {
            Row unInsuredRow = unInsuredSheet.createRow(i + 3);
            unInsuredRow.setHeightInPoints((float) 24.95);
            CellStyle inCellStyle = this.cellStyleBorder(wb);
            // 创建未投保人员清单页单元格
            for (int rowNum = 0; rowNum < 11; rowNum++) {
                Cell cell = unInsuredRow.createCell(rowNum);
                cell.setCellStyle(inCellStyle);
            }
            unInsuredRow.getCell(0).setCellValue(i + 1);
            unInsuredRow.getCell(1).setCellValue(perInfoList.get(i).get("name"));
            unInsuredRow.getCell(2).setCellValue(perInfoList.get(i).get("sexName"));
            unInsuredRow.getCell(3).setCellValue(perInfoList.get(i).get("birthDay"));
            unInsuredRow.getCell(4).setCellValue(perInfoList.get(i).get("iDTypeName"));
            unInsuredRow.getCell(5).setCellValue(perInfoList.get(i).get("idNo"));
            unInsuredRow.getCell(6).setCellValue(perInfoList.get(i).get("occupationType"));
            unInsuredRow.getCell(7).setCellValue(perInfoList.get(i).get("occupationCode"));
            String joinMedProtect = perInfoList.get(i).get("joinMedProtect");
            if ("0".equals(joinMedProtect)) {
                joinMedProtect = "无";
            } else if ("1".equals(joinMedProtect)) {
                joinMedProtect = "是";
            } else {
                joinMedProtect = "";
            }
            unInsuredRow.getCell(8).setCellValue(joinMedProtect);
            unInsuredRow.getCell(9).setCellValue(perInfoList.get(i).get("mobilePhone"));
            unInsuredRow.getCell(10).setCellValue(perInfoList.get(i).get("email"));
        }
        return wb;
    }

    public double createRiskInfoRow(int i, Row row, double sumsPrem, List<Map<String, String>> fcPlanRiskDutyList) {
        for (int j = 0; j < fcPlanRiskDutyList.size(); j++) {
            String riskCode = fcPlanRiskDutyList.get(j).get("riskCode");
            String dutyCode = fcPlanRiskDutyList.get(j).get("dutyCode");
            String amnt = String.valueOf(fcPlanRiskDutyList.get(j).get("amnt"));
            String prem = String.valueOf(fcPlanRiskDutyList.get(j).get("prem"));
            String getLimit = fcPlanRiskDutyList.get(j).get("getLimit");
            String getLimitType = fcPlanRiskDutyList.get(j).get("getLimitType");
            String getRatio = fcPlanRiskDutyList.get(j).get("getRatio");
            //遍历过程中若遇到含有津贴险种则显示最大赔付天数
            String maxGetDay = String.valueOf(fcPlanRiskDutyList.get(j).get("maxGetDay"));
            switch (riskCode) {
                // 团体定期寿险（12020）
                case "12020":
                    row.getCell(22).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                    row.getCell(23).setCellValue(prem);
                    break;
                // 门诊急诊团体医疗保险（17030）
                case "17030":
                    row.getCell(24).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                    row.getCell(25).setCellValue(prem);
                    row.getCell(26).setCellValue(getLimit);
                    row.getCell(27).setCellValue(getRatio);
                    break;
                // 团体意外伤害保险
                case "15030":
                    row.getCell(28).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                    row.getCell(29).setCellValue(prem);
                    break;
                // 意外伤害团体医疗保险
                case "15040":
                    row.getCell(30).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                    row.getCell(31).setCellValue(prem);
                    row.getCell(32).setCellValue(getLimit);
                    row.getCell(33).setCellValue(getRatio);
                    break;
                // 意外伤害住院津贴团体医疗保险
                case "15060":
                    switch (dutyCode) {
                        // 意外伤害住院津贴保险责任GD0029
                        case "GD0029":
                            row.getCell(34).setCellValue(amnt);
                            row.getCell(35).setCellValue(prem);
                            row.getCell(36).setCellValue(maxGetDay);
                            break;
                        // "意外伤害重症住院津贴保险责任（可选责任）GD0030"
                        case "GD0030":
                            row.getCell(37).setCellValue(amnt);
                            row.getCell(38).setCellValue(prem);
                            row.getCell(39).setCellValue(maxGetDay);
                            break;
                        default:
                            break;
                    }
                    break;
                // 团体重大疾病保险
                case "16040":
                    row.getCell(40).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                    row.getCell(41).setCellValue(prem);
                    break;
                //琴逸团体重大疾病保险
                case "16490":
                    row.getCell(42).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                    row.getCell(43).setCellValue(prem);
                    break;
                // 住院津贴团体医疗保险
                case "17020":
                    switch (dutyCode) {
                        // 一般住院日额津贴保险金GD0032
                        case "GD0032":
                            row.getCell(44).setCellValue(amnt);
                            row.getCell(45).setCellValue(prem);
                            row.getCell(46).setCellValue(maxGetDay);
                            break;
                        // 癌症住院日额津贴保险金（可选责任）GD0033
                        case "GD0033":
                            row.getCell(47).setCellValue(amnt);
                            row.getCell(48).setCellValue(prem);
                            row.getCell(49).setCellValue(maxGetDay);
                            break;
                        // 重症监护日额津贴保险金（可选责任）GD0034
                        case "GD0034":
                            row.getCell(50).setCellValue(amnt);
                            row.getCell(51).setCellValue(prem);
                            row.getCell(52).setCellValue(maxGetDay);
                            break;
                        // 手术医疗津贴保险金（可选责任）GD0035
                        case "GD0035":
                            Double amount17020 = CommonUtil.div(Double.valueOf(amnt), 10000.0, 2);
                            row.getCell(53).setCellValue(amount17020);
                            row.getCell(54).setCellValue(prem);
                            // update by wudezhong 2020.11.27 EAST需求变更
                            //row.getCell(53).setCellValue(maxGetDay);
                            break;
                        default:
                            break;
                    }
                    break;
                // 住院团体医疗保险（17010）
                case "17010":
                    row.getCell(55).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                    row.getCell(56).setCellValue(prem);
                    // 免赔额
                    row.getCell(57).setCellValue(getLimit);
                    // 赔付比例
                    row.getCell(58).setCellValue(getRatio);
                    break;
                // 尊享团体补充医疗保险（17050）
                case "17050":
                    row.getCell(59).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                    row.getCell(60).setCellValue(prem);
                    // 免赔额
                    row.getCell(61).setCellValue(getLimit);
                    // 赔付比例
                    row.getCell(62).setCellValue(getRatio);
                    break;
                // 综合交通团体意外伤害保险（15070）
                case "15070":
                    switch (dutyCode) {
                        // 公路公共交通工具保险金GD0050(可选责任)
                        case "GD0050":
                            row.getCell(63).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                            row.getCell(64).setCellValue(prem);
                            break;
                        // 轨道交通工具保险金GD0051(可选责任)
                        case "GD0051":
                            row.getCell(65).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                            row.getCell(66).setCellValue(prem);
                            break;
                        // 水路公共交通工具保险金GD0052(可选责任)
                        case "GD0052":
                            row.getCell(67).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                            row.getCell(68).setCellValue(prem);
                            break;
                        // 民航班机保险金GD0053(可选责任)
                        case "GD0053":
                            row.getCell(69).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                            row.getCell(70).setCellValue(prem);
                            break;
                        // 私家车或公务车保险金GD0054(可选责任)
                        case "GD0054":
                            row.getCell(71).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                            row.getCell(72).setCellValue(prem);
                            break;
                        // 公路公共意外伤害医疗保险金GD0055(可选责任)
                        case "GD0055":
                            row.getCell(73).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                            row.getCell(74).setCellValue(prem);
                            break;
                        // 轨道交通意外伤害医疗保险金GD0056(可选责任)
                        case "GD0056":
                            row.getCell(75).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                            row.getCell(76).setCellValue(prem);
                            break;
                        // 水路公共交通意外伤害医疗保险金GD0057(可选责任)
                        case "GD0057":
                            row.getCell(77).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                            row.getCell(78).setCellValue(prem);
                            break;
                        // 民航班机意外伤害医疗保险金GD0058(可选责任)
                        case "GD0058":
                            row.getCell(79).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                            row.getCell(80).setCellValue(prem);
                            break;
                        // 私家车或公务车意外伤害医疗保险金GD0059(可选责任)
                        case "GD0059":
                            row.getCell(81).setCellValue(String.valueOf(CommonUtil.div(Double.valueOf(amnt), 10000.0, 2)));
                            row.getCell(82).setCellValue(prem);
                            break;
                        default:
                            break;
                    }
                default:
                    break;
            }
            sumsPrem += Double.parseDouble(prem);
        }
        return sumsPrem;
    }


    /**
     * 表格样式设置
     *
     * @param wb
     * @return
     */
    private CellStyle cellStyleBorder(Workbook wb) {
        CellStyle inCellStyle = wb.createCellStyle();
        inCellStyle.setBorderBottom(BorderStyle.THIN);
        inCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderLeft(BorderStyle.THIN);
        inCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderRight(BorderStyle.THIN);
        inCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderTop(BorderStyle.THIN);
        inCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        return inCellStyle;
    }

    /**
     * 得到企业名称和token
     *
     * @param token
     * @return
     */
    public String getGrpInfo(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            List<FCGrpInfo> fcGrpInfos = fcEnsureMapper.selectGrpInfo();
            resultMap.put("fcGrpInfos", fcGrpInfos);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "查询成功！");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "查询失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 重置企业token
     *
     * @param token
     * @param grpNo
     * @return
     */
    public String resetToken(String token, String grpNo) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = new GlobalInput();
        try {
            String userinfo = redisUtil.get(token);
            globalInput = JSON.parseObject(userinfo, GlobalInput.class);
            globalInput.setGrpNo(grpNo);
            Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
            redisUtil.put(token, JSON.toJSONString(globalInput), loginValidity);
            resultMap.put("globalInput", globalInput);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "成功！");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "失败！");
        }
        return JSON.toJSONString(resultMap);
    }
}
