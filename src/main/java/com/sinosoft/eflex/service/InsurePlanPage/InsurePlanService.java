package com.sinosoft.eflex.service.InsurePlanPage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.*;
import com.sinosoft.eflex.enums.status.InsureStateEnum;
import com.sinosoft.eflex.model.BatchInsureInterface.RiskInfo;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.OssEntity.OssEntity;
import com.sinosoft.eflex.model.confirmInsure.*;
import com.sinosoft.eflex.model.confirmInsureEflex.*;
import com.sinosoft.eflex.model.insureEflexPlanPage.*;
import com.sinosoft.eflex.model.insurePlanPage.*;
import com.sinosoft.eflex.model.makeProposalForm.*;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmRequest;
import com.sinosoft.eflex.model.system.ResultResp;
import com.sinosoft.eflex.service.*;
import com.sinosoft.eflex.service.userClient.BankSignService;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.oss.OssUtils;
import com.sinosoft.eflex.util.rest.exception.BusinessException;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import com.sinosoft.eflex.util.rest.util.ResponseResult;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/3/26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class InsurePlanService {


    // 引入公共资源
    @Autowired
    private UserService userService;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private EmpAndFamilyMapper empAndFamilyMapper;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FCPerRegistDayMapper fcPerRegistDayMapper;
    @Autowired
    private FPInsureEflexPlanMapper fPInsureEflexPlanMapper;
    @Autowired
    private FPInsureEflexPlanOptionalMapper fpInsureEflexPlanOptionalMapper;
    @Autowired
    private FcInsureEflexPlanMapper fcInsureEflexPlanMapper;
    @Autowired
    private FcInsureEflexPlanOptionalMapper fcInsureEflexPlanOptionalMapper;
    @Autowired
    private InsureService insureService;
    @Autowired
    private BankSignService bankSignService;
    @Autowired
    private EmpAndFamilyMapper familyMapper;
    @Autowired
    private FPInsurePlanMapper fpInsurePlanMapper;

    @Autowired
    private FCEnsurePlanMapper fcEnsurePlanMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private FCDefaultPlanMapper fcDefaultPlanMapper;
    @Autowired
    private FCPlanRiskMapper fcPlanRiskMapper;
    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;
    @Autowired
    private FCPlanRiskDutyMapper fcPlanRiskDutyMapper;
    @Autowired
    private FDRiskDutyInfoMapper fdRiskDutyInfoMapper;
    @Autowired
    private TBCheckRules tbCheckRules;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private PremTrailService premTrailService;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private EflexTBCheckRules eflexTBCheckRules;
    @Autowired
    private FCPlanHealthDesignRelaMapper fcPlanHealthDesignRelaMapper;
    @Autowired
    private FcDutyAmountGradeMapper fcDutyAmountGradeMapper;
    @Autowired
    private MyProps myProps;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FDAgentInfoMapper fdAgentInfoMapper;
    @Autowired
    private FCOrderMapper fcOrderMapper;
    @Autowired
    private FCOrderLocusMapper fcOrderLocusMapper;
    @Autowired
    private FCBatchPayBankInfoMapper fcBatchPayBankInfoMapper;
    @Autowired
    private FCOrderItemDetailMapper fcOrderItemDetailMapper;
    @Autowired
    private FCOrderItemMapper fcOrderItemMapper;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;
    @Autowired
    private FcPerImpartResultMapper fcPerImpartResultMapper;
    @Autowired
    private FCPerAppntMapper fcPerAppntMapper;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private FCPersonImageMapper fcPersonImageMapper;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FCOrderPayMapper fcOrderPayMapper;


    private final OssUtils ossUtils;

    /**
     * 初始化投保专区页面（固定计划）
     */
    @Transactional
    public InsurePlanPageResp initInsurePlanPage(String token, InsurePlanPageReq insurePlanPageReq) {
        InsurePlanPageResp insurePlanPageResp = new InsurePlanPageResp();
        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);
        String perNo = globalInput.getCustomNo();
        String ensureCode = globalInput.getEnsureCode();
        // 获取福利类型
        String ensureType = "0";
        FCEnsure fcensure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        FCPerInfo fcperinfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
        if (StringUtils.isEmpty(ensureCode) && fcensure == null) {
            throw new SystemException("请先选择福利！");
        }
        if (!ObjectUtils.isEmpty(fcensure)) {
            ensureType = fcensure.getEnsureType();
        }
        // 获取计划类型
        String planType = fcensure.getPlanType();
        if (StringUtils.isEmpty(perNo)) {
            throw new SystemException("个人编号不能为空！");
        } else if (StringUtils.isEmpty(ensureType)) {
            throw new SystemException("福利类型不能为空！");
        }
        // 获取页面来源 01-选择完人进行下一步 02-确认投保后的上一步
        String pageSource = insurePlanPageReq.getPageSource();
        // 选择的需要投保的人员ID
        List<String> perosnidlist = new ArrayList<>();
        if (!ObjectUtils.isEmpty(insurePlanPageReq.getChoiceInsurePeopleNextStepReqs()) && insurePlanPageReq.getChoiceInsurePeopleNextStepReqs().size() > 0) {
            perosnidlist = insurePlanPageReq.getChoiceInsurePeopleNextStepReqs().stream().map(ChoiceInsurePeopleNextStepReq::getPersonid).collect(Collectors.toList());
        }
        // 员工投保专区数据
        List<PeopleZoneData> staffZone = new ArrayList<>();
        // 家属投保专区数据
        List<PeopleZoneData> familyZone = new ArrayList<>();

        /**
         * 获取投保家庭人员数据
         */
        // ensureType:1学校投保和0企事业单位投保
        List<FCPerson> familyList = new ArrayList<>();
        // 学生投保只查询当前福利下的人员信息
        switch (pageSource) {
            case "01":
                if (ensureType.equals(EnsureTypeEnum.STUDENTENSURE.getCode())) {
                    // 企事业单位投保人员信息，在线学生投保
                    Map<String, Object> map = new HashMap<>();
                    map.put("perNo", perNo);
                    map.put("ensureCode", ensureCode);
                    map.put("perosnidlist", perosnidlist);
                    familyList = empAndFamilyMapper.selectFamilyByIsManual1(map);
                } else {
                    // 企事业单位投保人员信息
                    Map<String, Object> paraMap = new HashedMap();
                    paraMap.put("perosnidlist", perosnidlist);
                    paraMap.put("idNo", fcperinfo.getIDNo());
                    familyList = empAndFamilyMapper.selectAllFamilyInfo(paraMap);
                }
                break;
            case "02":
                // 查询家庭投保计划
                Map params = new HashMap<>();
                params.put("ensureCode", ensureCode);
                params.put("perNo", perNo);
                // 获取投保计划
                List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectFPInsurePlans(params);
                if (fpInsurePlanList.size() == 0) {
                    throw new SystemException("错误，未查询家庭投保计划！");
                } else {
                    perosnidlist = fpInsurePlanList.stream().map(FPInsurePlan::getPersonId).collect(Collectors.toList());
                }
                // 企事业单位投保人员信息
                Map<String, Object> paraMap = new HashedMap();
                paraMap.put("perosnidlist", perosnidlist);
                paraMap.put("idNo", fcperinfo.getIDNo());
                familyList = empAndFamilyMapper.selectAllFamilyInfo(paraMap);
                break;
            default:
                throw new SystemException("不支持的页面来源，请重新登录！");
        }

        // 处理投保家庭人员信息
        for (FCPerson fcPerson : familyList) {
            if ("0".equals(fcPerson.getRelation()) && !fcPerson.getIDNo().equals(fcperinfo.getIDNo())) {
                continue;
            }
            if (!"0".equals(fcPerson.getRelation()) && fcPerson.getIDNo().equals(fcperinfo.getIDNo())) {
                continue;
            }
            if (!fcperinfo.getIDNo().equals(fcPerson.getPerIDNo())) {
                continue;
            }
            // 封装投保专区人员信息
            PersonInfo personInfo = new PersonInfo();
            BeanUtils.copyProperties(fcPerson, personInfo);
            // 查询家属在开放期内是否存有学生的身份
            Integer count = fcEnsureMapper.selectFamTempStudentCount(fcPerson.getPersonID());
            personInfo.setIsHasStuRule(count > 0 ? "Y" : "N");
            // 查询家属是否已投保
            if (!"0".equals(fcPerson.getRelation())) {
                List<Map<String, Object>> isplanList = new ArrayList<>();
                // 企事业单位投保
                if (ensureType.equals("0") || ensureType.equals("2")) {
                    // 查询同一个员工下相同证件号的家属的所有计划
                    Map<String, String> map1 = new HashMap<>();
                    map1.put("personid", fcPerson.getPersonID());
                    map1.put("perno", perNo);
                    List<String> personIdlist = empAndFamilyMapper.selectSameStaffPersonid(map1);
                    if (planType == null || "".equals(planType) || planType.equals("0")) {// 固定计划
                        // 循环判断那个人在此福利下投过保
                        for (String personid : personIdlist) {
                            List<Map<String, Object>> planList = empAndFamilyMapper.selectIsPlan(ensureCode, personid);
                            if (planList.size() > 0) {
                                if (pageSource.equals("01")) {
                                    // 福利定制时查询有计划的personid
                                    personInfo.setPersonID(personid);
                                }
                                isplanList = planList;
                            }
                        }
                    } else {// 弹性计划
                        for (String personid : personIdlist) {
                            List<Map<String, Object>> planList = empAndFamilyMapper.selectIsEflexPlan(ensureCode, perNo, personid);
                            if (planList.size() > 0) {
                                if (pageSource.equals("01")) {// 福利定制时查询有计划的personid
                                    personInfo.setPersonID(personid);
                                }
                                isplanList = planList;
                            }
                        }
                    }
                } else {
                    // 学生投保
                    isplanList = empAndFamilyMapper.selectIsPlan(ensureCode, fcPerson.getPersonID());
                }
                if (isplanList != null && isplanList.size() > 0) {
                    personInfo.setIsPlan("1");
                } else {
                    personInfo.setIsPlan("0");
                }
            }

            // 投保专区数据封装
            PeopleZoneData peopleZoneData = new PeopleZoneData();
            // 员工信息需要携带 员工福利额度、家属福利额度
            if ("0".equals(fcPerson.getRelation())) {
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("ensureCode", ensureCode);
                params.put("perNo", perNo);
                FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(params);
                Double staffGrpPrem = fcPerRegistDay.getStaffGrpPrem();
                Double familyGrpPrem = fcPerRegistDay.getFamilyGrpPrem();
                personInfo.setStaffGrpPrem(staffGrpPrem == null ? 0.0 : staffGrpPrem);
                personInfo.setFamilyGrpPrem(familyGrpPrem == null ? 0.0 : familyGrpPrem);
                // 福利额度
                insurePlanPageResp.setStaffGrpPrem(staffGrpPrem == null ? 0.0 : staffGrpPrem);
                insurePlanPageResp.setFamilyGrpPrem(familyGrpPrem == null ? 0.0 : familyGrpPrem);
                peopleZoneData.setPersonInfo(personInfo);
                staffZone.add(peopleZoneData);
            } else {
                peopleZoneData.setPersonInfo(personInfo);
                familyZone.add(peopleZoneData);
            }

        }

        /**
         * 封装投保计划信息
         */
        // 员工的投保计划
        for (PeopleZoneData peopleZoneData : staffZone) {
            // 定义计划集合
            List<InsurePlanInfo> planInfoList = new ArrayList<>();
            // 0企事业投保，1学生投保
            if (ensureType.equals("1")) {
                List<FCStaffFamilyRela> fcStaffFamilyRelaList = familyMapper.empAndFamilySelect(perNo);
                for (FCStaffFamilyRela fcStaffFamilyRela : fcStaffFamilyRelaList) {
                    String relation = fcStaffFamilyRela.getRelation();
                    if (relation != "0" && !relation.equals("0")) {
                        String personId = fcStaffFamilyRela.getFcPersonList().get(0).getPersonID();
                        Map<String, Object> map = new HashMap<>();
                        map.put("ensureCode", ensureCode);
                        map.put("personId", personId);
                        // 查询员工是否投过保
                        List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectEnsureCodeByPersonId(map);
                        // 判断员工是否已投过计划
                        if (fpInsurePlanList.size() > 0) {
                            // 员工已投过保
                            for (FPInsurePlan fpInsurePlan : fpInsurePlanList) {
                                // 获取计划编码
                                String planCode = fpInsurePlan.getPlanCode();
                                // 通过计划编码查询员工投保计划
                                Map<String, String> param = new HashMap<>();
                                param.put("planCode", planCode);
                                param.put("ensureCode", fpInsurePlan.getEnsureCode());
                                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                                InsurePlanInfo planInfo = new InsurePlanInfo();
                                BeanUtils.copyProperties(fcEnsurePlan, planInfo);
                                // 封装险种责任信息
                                getRiskDutyInfo(token, planInfo);
                                planInfoList.add(planInfo);
                                peopleZoneData.setIsPlan("1");
                            }
                        } else {
                            // 员工未投过保
                            List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectPlanInfo(ensureCode, PlanObjectEnum.STAFF.getCode());
                            Map<String, String> mapRaletion = new HashMap<>();
                            mapRaletion.put("perNo", globalInput.getCustomNo());
                            mapRaletion.put("personID", personId);
                            // 查询此人是否在本员工下是否有此家属
                            FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(mapRaletion);
                            List<FCEnsurePlan> insurePlanList = null;
                            try {
                                insurePlanList = insureService.checkPlanAge(fcEnsurePlanList, personId, rela.getRelation(), globalInput.getCustomNo());
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            for (FCEnsurePlan fcEnsurePlan : insurePlanList) {
                                InsurePlanInfo planInfo = new InsurePlanInfo();
                                BeanUtils.copyProperties(fcEnsurePlan, planInfo);
                                // 封装险种责任信息
                                getRiskDutyInfo(token, planInfo);
                                planInfoList.add(planInfo);
                            }
                            peopleZoneData.setIsPlan("0");
                        }
                    }
                }
            } else {
                // 获取所有的人的信息
                List<String> idnolist = familyMapper.selectFamilyIDNO(perNo);
                Map<String, Object> paraMap = new HashMap<>();
                paraMap.put("perosnidlist", perosnidlist);
                paraMap.put("idNo", fcperinfo.getIDNo());
                List<FCPerson> familyList1 = familyMapper.selectAllFamilyInfo(paraMap);
                for (FCPerson fcPerson : familyList1) {
                    if (fcPerson.getRelation().equals("0") && !fcPerson.getIDNo().equals(fcperinfo.getIDNo())) {
                        continue;
                    }
                    if (!fcPerson.getRelation().equals("0") && fcPerson.getIDNo().equals(fcperinfo.getIDNo())) {
                        continue;
                    }
                    if (!fcperinfo.getIDNo().equals(fcPerson.getPerIDNo())) {
                        continue;
                    }
                    String relation = fcPerson.getRelation();
                    if (relation == "0" || relation.equals("0")) {
                        List<FPInsurePlan> fpInsurePlanList = new ArrayList<>();
                        // 查询同一个员工下相同证件号的家属的所有计划
                        Map<String, String> map1 = new HashMap<>();
                        map1.put("personid", fcPerson.getPersonID());
                        map1.put("perno", perNo);
                        List<String> personIdlist = familyMapper.selectSameStaffPersonid(map1);
                        // 判断同一个员工下证件号相同的家属投保计划
                        for (String personid : personIdlist) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("ensureCode", ensureCode);
                            map.put("personId", personid);
                            // 查询员工是否投过保
                            List<FPInsurePlan> InsurePlanList = fpInsurePlanMapper.selectEnsureCodeByPersonId(map);
                            if (InsurePlanList.size() > 0) {
                                for (FPInsurePlan fpInsurePlan : InsurePlanList) {
                                    fpInsurePlanList.add(fpInsurePlan);
                                }
                            }
                        }
                        // 查所有计划
                        Map<String, Object> map = new HashMap<>();
                        map.put("personId", fcPerson.getPersonID());
                        map.put("ensureCode", ensureCode);
                        List<String> personids = fcDefaultPlanMapper.getAllPersonId(String.valueOf(map.get("personId")));
                        List<FCDefaultPlan> fcDefaultPlan = fcDefaultPlanMapper.getAllByList(ensureCode, personids);
                        List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectPlanInfo(ensureCode, PlanObjectEnum.STAFF.getCode());
                        Map<String, String> mapRaletion = new HashMap<>();
                        mapRaletion.put("perNo", globalInput.getCustomNo());
                        mapRaletion.put("personID", fcPerson.getPersonID());
                        // 查询此人是否在本员工下是否有此家属
                        FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(mapRaletion);
                        List<FCEnsurePlan> insurePlanList = null;
                        try {
                            insurePlanList = insureService.checkPlanAge(fcEnsurePlanList, fcPerson.getPersonID(), rela.getRelation(), globalInput.getCustomNo());
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        peopleZoneData.setDefaultPlanCode(fcDefaultPlan.get(0).getPlanCode());
                        for (FCEnsurePlan fcEnsurePlan : insurePlanList) {
                            InsurePlanInfo planInfo = new InsurePlanInfo();
                            BeanUtils.copyProperties(fcEnsurePlan, planInfo);
                            // 封装险种责任信息
                            getRiskDutyInfo(token, planInfo);
                            planInfoList.add(planInfo);
                        }
                        //peopleZoneData.setIsPlan("0");
                        // 判断员工是否已投过计划
                        if (fpInsurePlanList.size() > 0) {
                            // 员工已投过保
                            Map<String, InsurePlanInfo> planInfoMap = planInfoList.stream()
                                    .collect(Collectors.toMap(InsurePlanInfo::getPlanCode, Function.identity()));

                            for (FPInsurePlan fpInsurePlan : fpInsurePlanList) {
                                String planCode = fpInsurePlan.getPlanCode();
                                InsurePlanInfo insurePlanInfo = planInfoMap.get(planCode);
                                if (insurePlanInfo != null) {
                                    insurePlanInfo.setIsPlan("1");
                                    peopleZoneData.setDefaultPlanCode(insurePlanInfo.getPlanCode());
                                }
                            }
                        }
                    }
                }
            }
            planInfoList = planInfoList.stream().filter(i -> i.getPlanCode().equals(peopleZoneData.getDefaultPlanCode())).collect(Collectors.toList());
            peopleZoneData.setPlanInfoList(planInfoList);
        }
        // 家属投保计划
        for (PeopleZoneData peopleZoneData : familyZone) {
            List<InsurePlanInfo> planInfoList = new ArrayList<>();
            String personId = peopleZoneData.getPersonInfo().getPersonID();
            Map<String, Object> ensureCodeMap = new HashMap<>();
            ensureCodeMap.put("ensureCode", ensureCode);
            ensureCodeMap.put("personId", personId);
            // 判断家属是否投保
            List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectEnsureCodeByPersonId(ensureCodeMap);
            Map<String, String> mapRaletion = new HashMap<String, String>();
            // mapRaletion.put("perNo", globalInput.getCustomNo()); //主被保人客户号
            // mapRaletion.put("personID", personId); //被保人id
            // 下面查询关系的话如果只用personid作为参数的话，能查询出
            // 查询此人是否在本员工下是否有此家属
            /*
             * FCStaffFamilyRela rela =
             * fcStaffFamilyRelaMapper.selectRelaSameStaff(mapRaletion); if (rela == null) {
             * //查询家属对应的关系（因为这里有一个大前提，就是相同的员工，所以无需加上相同员工下的条件）不同企业下员工是相同一个人的话，
             * 查询其家属的话只要personid就能查询出所有的家属信息 rela =
             * fcStaffFamilyRelaMapper.selectPersonIdInfo(personId); }
             */
            // 查询此人是否在本员工下是否有此家属
            mapRaletion.put("perNo", globalInput.getCustomNo());
            mapRaletion.put("personid", personId);
            FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaffOne(mapRaletion);

            // 查询家属计划
            List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectEnsureCodeByplanObject(ensureCode, PlanObjectEnum.FAMILY.getCode());
            List<FCEnsurePlan> insurePlanList = null;
            try {
                insurePlanList = insureService.checkPlanAge(fcEnsurePlanList, personId, rela.getRelation(), globalInput.getCustomNo());
            } catch (ParseException e) {
                e.printStackTrace();
            }
            for (FCEnsurePlan fcEnsurePlan : insurePlanList) {
                InsurePlanInfo planInfo = new InsurePlanInfo();
                BeanUtils.copyProperties(fcEnsurePlan, planInfo);
                // 封装险种责任信息
                getRiskDutyInfo(token, planInfo);
                planInfoList.add(planInfo);
            }

            // 判断员工是否已投过计划
            if (fpInsurePlanList.size() > 0) {
                // 家属已投过
                Map<String, InsurePlanInfo> planInfoMap = planInfoList.stream()
                        .collect(Collectors.toMap(InsurePlanInfo::getPlanCode, Function.identity()));

                for (FPInsurePlan fpInsurePlan : fpInsurePlanList) {
                    String planCode = fpInsurePlan.getPlanCode();
                    InsurePlanInfo insurePlanInfo = planInfoMap.get(planCode);
                    if (insurePlanInfo != null) {
                        insurePlanInfo.setIsPlan("1");
                        peopleZoneData.setDefaultPlanCode(insurePlanInfo.getPlanCode());
                    }
                }
            }


            peopleZoneData.setPlanInfoList(planInfoList);
        }
        insurePlanPageResp.setStaffZone(staffZone);
        insurePlanPageResp.setFamilyZone(familyZone);
        return insurePlanPageResp;
    }

    /**
     * @param token
     * @param peopleInsurePlanInfos
     * @return java.lang.String
     * <AUTHOR> Dezhong
     * @date 2021/3/31
     */
    @Transactional
    public ConfirmInsureResp confirmInsure(String token, List<PeopleInsurePlanInfo> peopleInsurePlanInfos, String orderNo) {
        /**
         * 定义返回结果
         */
        ConfirmInsureResp confirmInsureResp = new ConfirmInsureResp();

        /**
         * 订单来源
         */
        String orderSource = "02";

        /**
         * 获取token信息
         */
        GlobalInput globalInput = userService.getSession(token);
        String ensureCode = globalInput.getEnsureCode();
        String perNo = globalInput.getCustomNo();
        /**
         * 校验人员是否投保了计划
         */
        String notInsuredName = "";
        for (PeopleInsurePlanInfo peopleInsurePlanInfo : peopleInsurePlanInfos) {
            if (StringUtils.isEmpty(peopleInsurePlanInfo.getPlanCode())) {
                if (!StringUtils.isEmpty(notInsuredName)) {
                    notInsuredName += "、";
                }
                notInsuredName += peopleInsurePlanInfo.getName();
            }
        }
        if (!StringUtils.isEmpty(notInsuredName)) {
            throw new BusinessException(notInsuredName + "未选择计划，请先选择计划后再进行确认投保！");
        }
        /**
         * 校验投保规则
         */
        for (PeopleInsurePlanInfo peopleInsurePlanInfo : peopleInsurePlanInfos) {
            // 校验基本规则
            PersonInfo personInfo = empAndFamilyMapper.selectPersonInfo(peopleInsurePlanInfo.getPersonId());
            if (ObjectUtils.isEmpty(personInfo)) {
                throw new SystemException("未查到被保人信息！");
            } else if (StringUtils.isEmpty(personInfo.getName()) || StringUtils.isEmpty(personInfo.getSex()) || StringUtils.isEmpty(personInfo.getBirthDate()) || StringUtils.isEmpty(personInfo.getIdNo()) || StringUtils.isEmpty(personInfo.getIdType()) || StringUtils.isEmpty(personInfo.getOccupationCode()) || StringUtils.isEmpty(personInfo.getJoinMedProtect())) {
                throw new SystemException("被保人" + personInfo.getName() + "信息不完整，请前往家庭信息维护页面进行补充！");
            }
            // 校验投保规则
            boolean checkTBRules = tbCheckRules.checkTBRules(peopleInsurePlanInfo.getPersonId(), peopleInsurePlanInfo.getPlanCode(), ensureCode);
            if (!checkTBRules) {
                if (tbCheckRules.getErrorMsg() != null && !"".equals(tbCheckRules.getErrorMsg())) {
                    throw new SystemException(peopleInsurePlanInfo.getName() + "投保规则校验不通过，" + tbCheckRules.getErrorMsg());
                } else {
                    // 自定义返回结果
                    confirmInsureResp.setCheckTBResultInfo(new CheckTBResultInfo(peopleInsurePlanInfo.getName() + "投保规则校验不通过", tbCheckRules.getResult()));
                    return confirmInsureResp;
                }
            }
            log.info("校验投保规则通过::{}", peopleInsurePlanInfo.getName());
        }
        /**
         * 删除之前的投保计划，先删除后新增
         */
        FPInsurePlan fpInsurePlan1 = new FPInsurePlan();
        fpInsurePlan1.setEnsureCode(ensureCode);
        fpInsurePlan1.setPerno(perNo);
        fpInsurePlanMapper.deleteInsurePlan(fpInsurePlan1);

        peopleInsurePlanInfos.forEach((PeopleInsurePlanInfo peopleInsurePlanInfo) -> {
            /**************************** 当前员工不存在当前的家属则同步信息 ************************/
            Map<String, Object> SyncMap = insureService.SynchronizationFamilyinfo(token, peopleInsurePlanInfo.getPersonId());
            if (!SyncMap.get("success").equals(true)) {
                throw new SystemException(JSONObject.toJSONString(SyncMap.get("errmsg")));
            }
            String personId = String.valueOf(SyncMap.get("personId"));
            String planCode = peopleInsurePlanInfo.getPlanCode();
            /**
             * 校验员工的投保计划保费与默认计划保费
             */
            Map<String, Object> map = new HashMap<>();
            Map<String, String> maps = new HashMap<>();
            maps.put("personID", personId);
            maps.put("perNo", perNo);
            log.info("校验员工的投保计划保费与默认计划保费::{}", JSON.toJSONString(maps));
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(maps);
            String relation = fcStaffFamilyRela.getRelation();
            if (relation.equals("0")) {
                Map<String, Object> defaultPlanMap = new HashMap<>();
                defaultPlanMap.put("ensureCode", ensureCode);
                defaultPlanMap.put("personId", personId);
                // 获取默认计划编号
                log.info("获取默认计划编号::{}", JSON.toJSONString(defaultPlanMap));
                List<FCDefaultPlan> fcDefaultPlan = fcDefaultPlanMapper.selectDefaultPlans(defaultPlanMap);
                String defaultPlanCode = fcDefaultPlan.get(0).getPlanCode();
                Map<String, String> param = new HashMap<>();
                param.put("planCode", defaultPlanCode);
                param.put("ensureCode", ensureCode);
                FCEnsurePlan defaultEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                // 默认计划保费
                Double defaultPrem = defaultEnsurePlan.getTotalPrem();
                // 计划保费
                map.clear();
                param.put("planCode", planCode);
                param.put("ensureCode", ensureCode);
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                Double planPrem = fcEnsurePlan.getTotalPrem();
                if (planPrem < defaultPrem) {
                    throw new SystemException("投选的计划保费不能低于默认计划保费");
                }
            }
            // 福利编号
            map.put("ensureCode", ensureCode);
            map.put("personId", personId);
            /**
             * 存储投保计划信息表  主表
             */
            String savePlanInsure = insureService.saveInsureInfo(token, personId, planCode, ensureCode, perNo);
            ResponseResult responseResult = JSONObject.parseObject(savePlanInsure, ResponseResult.class);
            if (responseResult.getSuccess().equals(Boolean.FALSE)) {
                throw new SystemException(responseResult.getMessage());
            }
        });

        /******************************************订单存储逻辑 start*******************************************/
        //删除之前订单
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        List<FCOrder> fcOrderList = fcOrderMapper.selectOrder(perNo, ensureCode);
        if (fcOrderList.size() > 0) {
            //删除之前的订单
            String deletConfirmInfo = deleteConfirmInfo(fcOrderList);
            if (!("".equals(deletConfirmInfo))) {
                throw new SystemException("删除订单失败！");
            }
        }
        //获取代理人信息
        List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
        FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);
        // 根据福利编号获取团体保单信息
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(fcEnsure.getEnsureCode());
        /*
         * 1、投保人表FCPerAppnt 从 员工表FCPerInfo取数据
         * 2、一个员工家庭在订单表FCOrder表创建一条数据
         */
        // 1、投保人表FCPerAppnt
        FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
        FCPerAppnt fcPerAppnt = new FCPerAppnt();
        String perAppNo = maxNoService.createMaxNo("PerAppNo", "", 20);
        fcPerAppnt.setPerAppNo(perAppNo);
        fcPerAppnt.setGrpNo(globalInput.getGrpNo());
        fcPerAppnt.setPerNo(globalInput.getCustomNo());
        fcPerAppnt.setName(fcPerInfo.getName());
        fcPerAppnt.setSex(fcPerInfo.getSex());
        fcPerAppnt.setIDType(fcPerInfo.getIDType());
        fcPerAppnt.setIDNo(fcPerInfo.getIDNo());
        fcPerAppnt.setBirthDay(fcPerInfo.getBirthDay());
        fcPerAppnt.setOperator(globalInput.getUserNo());
        fcPerAppnt = CommonUtil.initObject(fcPerAppnt, "INSERT");
        fcPerAppntMapper.insert(fcPerAppnt);
        // 2、订单表FCOrder
        Map param = new HashMap();
        param.put("ensureCode", globalInput.getEnsureCode());
        param.put("perNo", globalInput.getCustomNo());
        param.put("personType", "1");
        param.put("isValidy", "1");
        List<FCPerRegistDay> fcPerRegistDayList = fcPerRegistDayMapper.selectFCPerRegistDayList(param);
        if (fcPerRegistDayList.size() != 1) {
            // 员工开放投保信息表数据异常，不能创建订单
            throw new SystemException("员工开放投保信息表数据异常，不能创建订单！");
        }
        FCPerRegistDay fcPerRegistDay = fcPerRegistDayList.get(0);
        FCOrder fcOrder = new FCOrder();

        if (StringUtils.isEmpty(orderNo)) {
            orderNo = maxNoService.createMaxNo("OrderNo", DateTimeUtil.getTimeString().substring(0, 4), 8);
        }

        fcOrder.setOrderNo(orderNo);
        fcOrder.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
        //固定，弹性计划，个人投保之后状态修改为待生效，日常计划为待提交核心 01-待提交核心 08-待生效
        //if (fcEnsure.getPlanType().equals(PlanTypeEnum.DAILYPLAN.getCode())){
        //    fcOrder.setOrderStatus("01");
        //}else{
        //    fcOrder.setOrderStatus("08");
        //}
        fcOrder.setOrderStatus("03");//02-待支付，03-待确认投保信息
        fcOrder.setOrderType("01");
        fcOrder.setOrderSource(orderSource);
        fcOrder.setOpenDay(fcPerRegistDay.getOpenDay());
        fcOrder.setCloseDay(fcPerRegistDay.getCloseDay());
        fcOrder.setGrpNo(globalInput.getGrpNo());
        fcOrder.setPerNo(globalInput.getCustomNo());
        fcOrder.setPerAppNo(perAppNo);
        fcOrder.setCommitDate(DateTimeUtil.getCurrentDate());
        fcOrder.setClientNo(fcEnsure.getClientNo());
        fcOrder.setOperator(globalInput.getUserNo());
        fcOrder = CommonUtil.initObject(fcOrder, "INSERT");
        fcOrderMapper.insert(fcOrder);
        // 3、订单轨迹表FCOrderLocus
        FCOrderLocus fcOrderLocus = new FCOrderLocus();
        String orderLocusSN = maxNoService.createMaxNo("orderLocusSN", "", 20);
        fcOrderLocus.setOrderLocusSN(orderLocusSN);
        fcOrderLocus.setOrderNo(orderNo);
        fcOrderLocus.setOrderStatus("03");//02-待支付，03-待确认投保信息
        fcOrderLocus.setOperator(globalInput.getUserNo());
        fcOrderLocus = CommonUtil.initObject(fcOrderLocus, "INSERT");
        fcOrderLocusMapper.insert(fcOrderLocus);
        param.clear();
        param.put("perNo", globalInput.getCustomNo());
        param.put("ensureCode", globalInput.getEnsureCode());
        //修改成查询个人投保记录临时表（因为只有在最终确认的时候才会在主表表中添加信息）
        List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectFPInsurePlans(param);
        if (fpInsurePlanList.size() < 1) {
            // 没有被保人
            log.info("系统异常，没有被保人");
            throw new RuntimeException();
        }
        //获取拆分保费：个人缴费+公司缴费
        Map<String, Object> dataMap = insureService.calStaffPolicyPremDetail(globalInput.getEnsureCode(), globalInput.getCustomNo(), fcEnsure.getEnsureType());
        if (dataMap == null) {
            log.info("系统异常，缴费明细计算失败");
            throw new RuntimeException();
        }
        Map<String, Object> staffMap = (HashMap) dataMap.get("staffMap");
        List<Map<String, Object>> familyList = (ArrayList<Map<String, Object>>) dataMap.get("familyMap");
        Map<String, Map<String, Object>> familyMaps = new HashMap<>();
        if (familyList != null && familyList.size() > 0) {
            for (Map<String, Object> map : familyList) {
                familyMaps.put((String) map.get("personId"), map);
            }
        }
        for (FPInsurePlan fpInsurePlan : fpInsurePlanList) {
            // 3、子订单表FCOrderItem
            FCOrderItem fcOrderItem = new FCOrderItem();
            String orderItemNo = maxNoService.createMaxNo("OrderItemNo", "", 20);
            String orderItemDetailNo = maxNoService.createMaxNo("OrderItemDetailNo", "", 20);
            fcOrderItem.setOrderItemNo(orderItemNo);
            fcOrderItem.setOrderItemDetailNo(orderItemDetailNo);
            fcOrderItem.setOrderNo(orderNo);
            /* 个人保单号生成规则：
			   1、前缀99；
			   2、3-4位截取保单管理机构的3、4位，如为总公司则取86（如管理机构为864498取44）；
			   3、5-6位截取年份后两位（如2018取18）；
			   4、7-15位取每个自然年度流水号；
			   5、最后一位固定为8；
			   例如弹性平台今年第一张个人保险凭证号应为：9944180000000018。
			*/
            String rule = "99" + fdAgentInfo.getManageCom().substring(2, 4) + DateTimeUtil.getCurrentYear().substring(2, 4);
            String contNo = maxNoService.createMaxNo("ContNo" + DateTimeUtil.getCurrentYear(), rule, 9);
            contNo = contNo + "8";
            fcOrderItem.setContNo(contNo);
            if (!staffMap.isEmpty()) {
                if (fpInsurePlan.getPersonId().equals(staffMap.get("personId"))) {
                    fcOrderItem.setSelfPrem((double) staffMap.get("staffPrem"));
                    fcOrderItem.setGrpPrem((double) staffMap.get("staffDefaultPrem"));
                }
            }
            if (!familyMaps.isEmpty()) {
                if (familyMaps.get(fpInsurePlan.getPersonId()) != null) {
                    Map<String, Object> familyMap = familyMaps.get(fpInsurePlan.getPersonId());
                    fcOrderItem.setSelfPrem((double) familyMap.get("familyPrem"));
                    fcOrderItem.setGrpPrem((double) familyMap.get("familyDefaultPrem"));
                }
            }

            fcOrderItem.setOperator(globalInput.getUserNo());
            fcOrderItem = CommonUtil.initObject(fcOrderItem, "INSERT");
            fcOrderItemMapper.insert(fcOrderItem);
            // 4、子订单产品要素详情表FCOrderItemDetail
            FCOrderItemDetail fcOrderItemDetail = new FCOrderItemDetail();
            fcOrderItemDetail.setOrderItemDetailNo(orderItemDetailNo);
            fcOrderItemDetail.setProductCode(fpInsurePlan.getPlanCode());
            fcOrderItemDetail.setEnsureCode(ensureCode);
            fcOrderItemDetail.setProductEleCode("001");
            fcOrderItemDetail.setOperator(globalInput.getUserNo());
            fcOrderItemDetail = CommonUtil.initObject(fcOrderItemDetail, "INSERT");
            fcOrderItemDetailMapper.insert(fcOrderItemDetail);
            // 5、被保人表FCOrderInsured
            FCOrderInsured fcOrderInsured = new FCOrderInsured();
            fcOrderInsured.setOrderItemNo(orderItemNo);
            fcOrderInsured.setOrderNo(orderNo);
            fcOrderInsured.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
            FCPerson person = fcPersonMapper.selectByPrimaryKey(fpInsurePlan.getPersonId());
            fcOrderInsured.setPersonID(person.getPersonID());
            fcOrderInsured.setName(person.getName());
            fcOrderInsured.setSex(person.getSex());
            fcOrderInsured.setBirthDay(person.getBirthDate());
            fcOrderInsured.setNativeplace(person.getNativeplace());
            fcOrderInsured.setIDType(person.getIDType());
            fcOrderInsured.setIDNo(person.getIDNo());
            fcOrderInsured.setMobilePhone(person.getMobilePhone());
            fcOrderInsured.setPhone(person.getPhone());
            fcOrderInsured.setDepartment(fcPerInfo.getDepartment());
            fcOrderInsured.setOccupationType(person.getOccupationType());
            fcOrderInsured.setOccupationCode(person.getOccupationCode());
            fcOrderInsured.setJoinMedProtect(person.getJoinMedProtect());
            fcOrderInsured.setMedProtectType(person.getMedProtectType());
            fcOrderInsured.setEMail(person.getEMail());
            fcOrderInsured.setAddress(person.getAddress());
            fcOrderInsured.setOperator(globalInput.getUserNo());
            fcOrderInsured = CommonUtil.initObject(fcOrderInsured, "INSERT");
            fcOrderInsuredMapper.insert(fcOrderInsured);

            // 6、个人健康告知信息表（记录需要健康告知的人员信息）
            long age = Integer.valueOf(DateTimeUtil.getCurrentAge(person.getBirthDate(), fcEnsure.getCvaliDate()));
            if (age > 0) {
                age = age * 365;
            } else {
                age = DateTimeUtil.getCurrentAge(person.getBirthDate(), DateTimeUtil.getCurrentDate());
            }
            if (insureService.ageValidate(age, fpInsurePlan.getPlanCode(), ensureCode)) {
                FcPerImpartResult fcPerImpartResult = new FcPerImpartResult();
                fcPerImpartResult.setOrderItemNo(orderItemNo);
                fcPerImpartResultMapper.insertSameResultImpartList(fcPerImpartResult);
            }

            //修改投保状态，0-未提交订单表 1-已提交订单表 2-核心承保成功
            fpInsurePlan.setInsureState(InsureStateEnum.SUBMITTED.getCode());
            //修改投保临时表订单状态
            fpInsurePlanMapper.updateByPrimaryKey(fpInsurePlan);
        }

        confirmInsureResp.setOrderNo(orderNo);
        /******************************************订单存储逻辑 end*******************************************/

        return confirmInsureResp;
    }

    // 获取计划下险种责任信息
    public List<RiskDutyInfo> getRiskDutyInfo(String ensureCode, String planCode) {
        Map<String, String> param = new HashMap<>();
        param.put("planCode", planCode);
        param.put("ensureCode", ensureCode);
        List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectRiskInfo(ensureCode, planCode);// 计划编码查险种表
        List<RiskDutyInfo> riskList = new ArrayList<>();
        // 查询一个险种
        for (FCPlanRisk fcPlanRisk : fcPlanRiskList) {
            String riskCode = fcPlanRisk.getRiskCode();
            String riskName = fdRiskInfoMapper.selectRiskNameByRiskCode(riskCode);
            List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyInfo(ensureCode, planCode, riskCode);
            for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                RiskDutyInfo riskDutyInfo = new RiskDutyInfo();
                riskDutyInfo.setPrem(fcPlanRiskDuty.getPrem());
                riskDutyInfo.setRiskCode(fcPlanRisk.getRiskCode());
                String dutyCode = fcPlanRiskDuty.getDutyCode();
                Map<String, String> dutyMap = new HashMap<>();
                dutyMap.put("dutyCode", dutyCode);
                dutyMap.put("riskCode", riskCode);
                Map<String, Object> fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                riskDutyInfo.setRiskCode(riskCode);
                riskDutyInfo.setDutyCode(dutyCode);
                riskDutyInfo.setRiskName(riskName);
                riskDutyInfo.setAmount(fcPlanRiskDuty.getAmnt());
                riskDutyInfo.setDutyName(fdRiskDutyInfo.get("DutyName").toString());
                riskList.add(riskDutyInfo);
            }
        }
        return riskList;
    }

    // 获取计划下险种责任信息
    public void getRiskDutyInfo(String token, InsurePlanInfo planInfo) {
        GlobalInput globalInput = userService.getSession(token);
        String ensureCode = planInfo.getEnsureCode();
        String planCode = planInfo.getPlanCode();
        List<RiskDutyInfo> riskDutyInfoList = getRiskDutyInfo(ensureCode, planCode);
        // 获取计划保障说明
        PlanConfigExplain configExplain1 = insureService.getConfigExplain1(globalInput.getGrpNo(), ensureCode, planCode);
        planInfo.setPlanConfigExplain(configExplain1);
        planInfo.setRiskDutyInfoList(riskDutyInfoList);
    }

    /**
     * 点击修改查询符合的投保计划
     */
    @Transactional
    public PeopleZoneData getInsurePlan(String token, ChoiceInsurePeopleNextStepReq choiceInsurePeopleNextStepReq) {
        // 定义返回参数
        PeopleZoneData peopleZoneData = new PeopleZoneData();
        // 获取token的信息
        GlobalInput globalInput = userService.getSession(token);
        String perNo = globalInput.getCustomNo();
        String ensureCode = globalInput.getEnsureCode();
        // 获取请求参数
        String personId = choiceInsurePeopleNextStepReq.getPersonid();
        String planObject = choiceInsurePeopleNextStepReq.getPlanObject();
        // 查询符合条件的投保计划
        List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectPlanInfo(ensureCode, planObject);
        Map<String, String> mapRaletion = new HashMap<>();
        mapRaletion.put("perNo", perNo);
        mapRaletion.put("personID", personId);
        FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(mapRaletion);
        //人员信息
        PersonInfo personInfo = empAndFamilyMapper.selectPersonInfo(personId);
        peopleZoneData.setPersonInfo(personInfo);
        try {
            // 获取投保计划
            List<FCEnsurePlan> insurePlanList = insureService.checkPlanAge(fcEnsurePlanList, personId, rela.getRelation(), globalInput.getCustomNo());
            List<InsurePlanInfo> planInfoList = new ArrayList();
            for (FCEnsurePlan fcEnsurePlan : insurePlanList) {
                InsurePlanInfo planInfo = new InsurePlanInfo();
                BeanUtils.copyProperties(fcEnsurePlan, planInfo);
                // 封装险种责任信息
                getRiskDutyInfo(token, planInfo);
                planInfoList.add(planInfo);
                peopleZoneData.setPlanInfoList(planInfoList);
            }
            // 查询是否存在默认计划
            Map<String, Object> map = new HashMap<>();
            map.put("personId", personId);
            map.put("ensureCode", ensureCode);
            List<String> personids = fcDefaultPlanMapper.getAllPersonId(String.valueOf(map.get("personId")));
            List<FCDefaultPlan> fcDefaultPlan = fcDefaultPlanMapper.getAllByList(ensureCode, personids);
            if (fcDefaultPlan.size() != 0) {
                peopleZoneData.setDefaultPlanCode(fcDefaultPlan.get(0).getPlanCode());
            }
            peopleZoneData.setIsPlan("0");
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return peopleZoneData;
    }

    /**
     * @param token
     * @param selectOrderDetailInfoReq
     * @return
     */
    public ConfirmInsureResp selectOrderDetailInfo(String token, SelectOrderDetailInfoReq selectOrderDetailInfoReq) {
        /**
         * 定义返回参数
         */
        ConfirmInsureResp confirmInsureResp = new ConfirmInsureResp();
        List<HealthNoticeInfo> healthNoticeInfoList = new ArrayList<>();
        List<JuvenilesConfirmationInfo> juvenilesConfirmationInfoList = new ArrayList<>();
        /**
         * 获取token信息
         */
        GlobalInput globalInput = userService.getSession(token);
        /**
         * 获取请求参数
         */
        String orderNo = selectOrderDetailInfoReq.getOrderNo();
        /**
         * 订单详情页面信息
         */
        FCEnsure fcEnsure = fcEnsureMapper.selectEnsureByOrderNo(orderNo);
        String ensureCode = fcEnsure.getEnsureCode();
        List<PeopleInsureInfo> peopleInsureInfoList = fcOrderInsuredMapper.selectPeopleInsureInfo(orderNo);
        for (PeopleInsureInfo peopleInsureInfo : peopleInsureInfoList) {
            // 封装责任信息
            peopleInsureInfo.setRiskDutyInfoList(getRiskDutyInfo(ensureCode, peopleInsureInfo.getPlanCode()));
            if (fcEnsure.getPlanType().equals(PlanTypeEnum.IMMOBILIZATIONPLAN.getCode())) {
                try {
                    // 记录需要健康告知的人员信息
                    long age = Integer.valueOf(DateTimeUtil.getCurrentAge(peopleInsureInfo.getBirthDate(), fcEnsure.getCvaliDate()));
                    if (age > 0) {
                        age = age * 365;
                    } else {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        Date fDate = sdf.parse(peopleInsureInfo.getBirthDate());
                        Date oDate = sdf.parse(sdf.format(new Date()));
                        age = (oDate.getTime() - fDate.getTime()) / (1000 * 3600 * 24);
                    }
                    if (insureService.ageValidate(age, peopleInsureInfo.getPlanCode(), ensureCode)) {
                        healthNoticeInfoList.add(new HealthNoticeInfo(peopleInsureInfo.getPersonId(), peopleInsureInfo.getName()));
                    }
                    // 记录需要投保确认函的未成年人信息
                    long age1 = Integer.valueOf(DateTimeUtil.getCurrentAge(peopleInsureInfo.getBirthDate(), fcEnsure.getCvaliDate()));
                    if (age1 < 18) {
                        JuvenilesConfirmationInfo juvenilesConfirmationInfo = new JuvenilesConfirmationInfo();
                        // 投保确认书
                        FCPerInfo staffInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
                        juvenilesConfirmationInfo.setStaffName(staffInfo.getName());
                        juvenilesConfirmationInfo.setStaffIDNo(staffInfo.getIDNo());
                        juvenilesConfirmationInfo.setInsuredName(peopleInsureInfo.getName());
                        Map<String, String> map = new HashMap<String, String>();
                        map.put("perNo", globalInput.getCustomNo());
                        map.put("personID", peopleInsureInfo.getPersonId());
                        // 查询此人是否在本员工下是否有此家属
                        FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(map);
                        if (rela == null) {
                            // 查询家属对应的关系（因为这里有一个大前提，就是相同的员工，所以无需加上相同员工下的条件）
                            rela = fcStaffFamilyRelaMapper.selectPersonIdInfo(peopleInsureInfo.getPersonId());
                        }
                        if (rela != null) {
                            if ("3".equals(rela.getRelation())) {
                                // 3为子女。 员工是被保人的父母。这里需要拆分成父亲或母亲
                                if ("0".equals(staffInfo.getSex())) {
                                    juvenilesConfirmationInfo.setRelation("父亲");
                                } else {
                                    juvenilesConfirmationInfo.setRelation("母亲");
                                }
                            } else {
                                String relationName = fdCodeMapper.selectNameByCode("Relation", rela.getRelation());
                                juvenilesConfirmationInfo.setRelation(relationName);
                            }
                        } else {
                            juvenilesConfirmationInfo.setRelation("");
                        }
                        Map<String, Object> params1 = new HashMap<>();
                        params1.put("planCode", peopleInsureInfo.getPlanCode());
                        params1.put("ensureCode", ensureCode);
                        List<FCPlanRiskDuty> dutyList = fcPlanRiskDutyMapper.selectDutyList(params1);
                        // 在这里判断固定计划中是否含有身故保险
                        List<String> dutyCodeList = Arrays.asList("GD0017", "GD0037", "GD0050", "GD0051", "GD0052", "GD0054");
                        int count = 0;
                        for (FCPlanRiskDuty dutyInfo : dutyList) {
                            if (dutyCodeList.contains(dutyInfo.getDutyCode())) {
                                count++;
                            }
                        }
                        if (0 == count) {
                            log.info("被保人：" + peopleInsureInfo.getName() + "，投保计划：" + peopleInsureInfo.getPlanName() + "，该计划中不包含身故险种，故不弹出确认函!");
                        } else {
                            double deathAmnt = 0.00;
                            for (FCPlanRiskDuty dutyInfo : dutyList) {
                                if ("GD0017".equals(dutyInfo.getDutyCode())) {
                                    deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                                } else if ("GD0037".equals(dutyInfo.getDutyCode())) {
                                    deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                                }
                            }
                            double Amnt15070 = 0.00;
                            for (FCPlanRiskDuty dutyInfo : dutyList) {
                                if ("GD0050".equals(dutyInfo.getDutyCode())) {
                                    Amnt15070 = dutyInfo.getAmnt();
                                }
                                if ("GD0051".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                                    Amnt15070 = dutyInfo.getAmnt();
                                }
                                if ("GD0052".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                                    Amnt15070 = dutyInfo.getAmnt();
                                }
                                if ("GD0054".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                                    Amnt15070 = dutyInfo.getAmnt();
                                }
                            }
                            deathAmnt = CommonUtil.add(deathAmnt, Amnt15070);
                            if (0.00 == deathAmnt) {
                                log.info("被保人：" + peopleInsureInfo.getName() + "，投保计划：" + peopleInsureInfo.getPlanName() + "，本次投保身故保险金额为0，故不弹出确认函!");
                            } else {
                                String riskAmnt = insureService.getRiskAmnt(peopleInsureInfo.getPersonId());
                                if (riskAmnt == null || "".equals(riskAmnt)) {
                                    log.info("调用核心接口查询风险保额失败，请联系管理员！");
                                    throw new SystemException("调用核心接口查询风险保额失败！");

                                }
                                juvenilesConfirmationInfo.setPersonId(peopleInsureInfo.getPersonId());
                                juvenilesConfirmationInfo.setPlanCode(peopleInsureInfo.getPlanCode());
                                juvenilesConfirmationInfo.setDeathAmnt(deathAmnt);
                                Double riskAmntTotal = CommonUtil.add(Double.valueOf(riskAmnt), deathAmnt);
                                juvenilesConfirmationInfo.setDeathAmntCount(riskAmntTotal);
                                juvenilesConfirmationInfo.setYear(DateTimeUtil.getCurrentYear());
                                juvenilesConfirmationInfo.setMonth(DateTimeUtil.getCurrentMonth());
                                juvenilesConfirmationInfo.setDay(DateTimeUtil.getCurrentDay());
                                juvenilesConfirmationInfoList.add(juvenilesConfirmationInfo);
                            }

                        }
                    }

                } catch (Exception e) {
                    throw new SystemException("程序有误！");
                }

            }
        }
        confirmInsureResp.setPeopleInsureInfoList(peopleInsureInfoList);
        /**
         * 封装需要健康告知的人员信息
         */
        confirmInsureResp.setHealthNoticeList(healthNoticeInfoList);

        /**
         * 封装需要未成年人确认函的人员信息
         */
        confirmInsureResp.setJuvenilesConfirmationInfoList(juvenilesConfirmationInfoList);

        return confirmInsureResp;
    }

    /**
     * 初始化投保专区页面（弹性计划）
     */
    @Transactional
    public InsureEflexPlanPageResp insureEflexPlanPage(String token, InsurePlanPageReq insurePlanPageReq) {
        /**
         * 定义返回对象
         */
        InsureEflexPlanPageResp insureEflexPlanPageResp = new InsureEflexPlanPageResp();

        /**
         * 获取基础信息
         */
        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);
        String ensureCode = globalInput.getEnsureCode();
        String perNo = globalInput.getCustomNo();
        String grpNo = globalInput.getGrpNo();
        // 获取福利信息
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        if (StringUtils.isEmpty(fcEnsure.getEnsureType())) {
            throw new SystemException("福利类型不能为空！");
        }
        String ensureType = fcEnsure.getEnsureType();
        String planType = fcEnsure.getPlanType();
        // 获取个人信息
        FCPerInfo fcperinfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
        // 获取个人注册表信息
        Map<String, Object> params = new HashMap<>();
        params.put("ensureCode", ensureCode);
        params.put("perNo", perNo);
        FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(params);
        String levelCode = fcPerRegistDay.getLevelCode();
        if (!StringUtils.isEmpty(ensureCode) && fcEnsure == null) {
            throw new SystemException("请先选择福利！");
        }
        if (StringUtils.isEmpty(levelCode)) {
            throw new SystemException("员工职级不存在！");
        }
        if (StringUtils.isEmpty(perNo)) {
            throw new SystemException("个人编号不能为空！");
        }
        if (insurePlanPageReq.getChoiceInsurePeopleNextStepReqs().size() == 0) {
            throw new SystemException("请先选择需要投保的人员信息！");
        }
        /**
         * 封装投保专区信息
         */
        // 员工投保专区数据
        List<EflexPeopleZoneData> staffZone = new ArrayList<>();
        // 家属投保专区数据
        List<EflexPeopleZoneData> familyZone = new ArrayList<>();

        /**
         * 循环所有人的信息
         */
        for (ChoiceInsurePeopleNextStepReq choiceInsurePeopleNextStepReq : insurePlanPageReq.getChoiceInsurePeopleNextStepReqs()) {
            // 获取单个最新的员工或者家属数据
            Map<String, Object> selectSingleFamilyInfoMap = new HashedMap();
            selectSingleFamilyInfoMap.put("personid", choiceInsurePeopleNextStepReq.getPersonid());
            selectSingleFamilyInfoMap.put("idNo", fcperinfo.getIDNo());
            FCPerson fcPerson = empAndFamilyMapper.selectSingleFamilyInfo(selectSingleFamilyInfoMap);
            // 排除错误数据
            if ((fcPerson.getRelation().equals("0") && !fcPerson.getIDNo().equals(fcperinfo.getIDNo())) || (!fcPerson.getRelation().equals("0") && fcPerson.getIDNo().equals(fcperinfo.getIDNo())) || (!fcperinfo.getIDNo().equals(fcPerson.getPerIDNo()))) {
                continue;
            }
            // 封装投保专区人员信息
            PersonInfo personInfo = new PersonInfo();
            personInfo.setIdType(fcPerson.getIDType());
            personInfo.setIdTypeName(fcPerson.getIDTypeName());
            personInfo.setIdNo(fcPerson.getIDNo());
            personInfo.setOccupationTypeName(fcPerson.getOccupationTypeName());
            personInfo.setMobilePhone(fcPerson.getMobilePhone());
            BeanUtils.copyProperties(fcPerson, personInfo);
            // 查询家属在开放期内是否存有学生的身份
            Integer count = fcEnsureMapper.selectFamTempStudentCount(fcPerson.getPersonID());
            personInfo.setIsHasStuRule(count > 0 ? "Y" : "N");
            // 查询家属是否已投保
            if (!fcPerson.getRelation().equals(RelationEnum.SELF.getCode())) {
                List<Map<String, Object>> isplanList = new ArrayList<>();
                // 企事业单位投保
                if (ensureType.equals("0") || ensureType.equals("2")) {
                    // 查询同一个员工下相同证件号的家属的所有计划
                    Map<String, String> map1 = new HashMap<>();
                    map1.put("personid", fcPerson.getPersonID());
                    map1.put("perno", perNo);
                    List<String> personIdlist = empAndFamilyMapper.selectSameStaffPersonid(map1);
                    // 弹性计划
                    if (planType.equals(PlanTypeEnum.EFLEXPLAN.getCode())) {
                        for (String personid : personIdlist) {
                            List<Map<String, Object>> planList = empAndFamilyMapper.selectIsEflexPlan(ensureCode, perNo, personid);
                            if (planList.size() > 0) {
                                personInfo.setPersonID(personid);
                                isplanList = planList;
                            }
                        }
                    } else {
                        throw new SystemException("福利类型类型应为弹性计划，接口调用有误！");
                    }
                }
                if (isplanList != null && isplanList.size() > 0) {
                    personInfo.setIsPlan("1");
                } else {
                    personInfo.setIsPlan("0");
                }
            }

            /**
             * 投保专区数据封装
             */
            EflexPeopleZoneData eflexPeopleZoneData = new EflexPeopleZoneData();

            /**
             * 个人信息封装
             */
            // 个人编码
            String personId = choiceInsurePeopleNextStepReq.getPersonid();
            // 福利额度
            Double staffGrpPrem = fcPerRegistDay.getStaffGrpPrem();
            Double familyGrpPrem = fcPerRegistDay.getFamilyGrpPrem();
            personInfo.setStaffGrpPrem(staffGrpPrem == null ? 0.0 : staffGrpPrem);
            personInfo.setFamilyGrpPrem(familyGrpPrem == null ? 0.0 : familyGrpPrem);
            insureEflexPlanPageResp.setStaffGrpPrem(staffGrpPrem == null ? 0.0 : staffGrpPrem);
            insureEflexPlanPageResp.setFamilyGrpPrem(familyGrpPrem == null ? 0.0 : familyGrpPrem);
            eflexPeopleZoneData.setPersonInfo(personInfo);
            /**
             * 员工、家属投保信息加载
             */
            List<GetInsureRiskInfoResp> insuredRiskInfos = fPInsureEflexPlanMapper.getInsureRiskInfo(GetInsureRiskInfoReq.builder().ensureCode(ensureCode).perNo(perNo).personId(personId).build());
            if (insuredRiskInfos.size() > 1) {
                // 已投保
                eflexPeopleZoneData.setIsExists("1");
            } else {
                // 是否保存过保额档次信息
                eflexPeopleZoneData.setIsExists("0");
            }

            // 未投保
            SelectMinAmntDefaultReq selectMinAmntDefaultReq = new SelectMinAmntDefaultReq();
            selectMinAmntDefaultReq.setEnsureCode(ensureCode);
            selectMinAmntDefaultReq.setGrpNo(grpNo);
            selectMinAmntDefaultReq.setBirthDay(fcperinfo.getBirthDay());
            selectMinAmntDefaultReq.setLevelCode(levelCode);
            selectMinAmntDefaultReq.setOccupationType(fcperinfo.getOccupationType());
            selectMinAmntDefaultReq.setSex(fcperinfo.getSex());
            selectMinAmntDefaultReq.setServiceTerm(fcperinfo.getServiceTerm());
            selectMinAmntDefaultReq.setRetirement(fcperinfo.getRetirement());
            selectMinAmntDefaultReq.setRelation(fcPerson.getRelation());
            selectMinAmntDefaultReq.setJoinMedProtect(fcperinfo.getJoinMedProtect());
            selectMinAmntDefaultReq.setInsureCount(fcEnsure.getInsuredNumber());
            selectMinAmntDefaultReq.setInsuredType(fcPerson.getRelation().equals(RelationEnum.SELF.getCode()) ? InsuredTypeEnum.STAFF.getCode() : InsuredTypeEnum.RELATION.getCode());

            // 查询最小保额的默认档次
            // List<EflexEmployInfo> minAmntDefaultGradeList =
            // fcEnsureMapper.selectMinAmntDefault1(selectMinAmntDefaultReq);
            // 获取员工、家属符合条件的所有保额档次
            List<EflexEmployInfo> amountGradeInfoList = fcEnsureMapper.selectEflexEmployList1(selectMinAmntDefaultReq);
            // 获取所有的险种名称以及对应险种信息 的集合
            // List<String> riskNameList = new
            // ArrayList<>(amountGradeInfoList.stream().map(eflexEmployInfo ->
            // eflexEmployInfo.getRiskName()).collect(Collectors.toSet()));
            // 所有的档次编码以及是否为默认档次 的集合
            Map<String, Object> amountGradeCodeAndDefaultFlagMap = new HashMap<>();
            amountGradeInfoList.forEach((EflexEmployInfo eflexEmployInfo) -> amountGradeCodeAndDefaultFlagMap.put(eflexEmployInfo.getAmountGrageCode(), eflexEmployInfo.getIsDefaultFlag()));

            /**
             * 封装险种信息
             */
            // 查询符合员工的投保险种信息，即投保专区的最外层结构
            List<InsureRiskInfo> insureRiskInfoList = fcEnsureMapper.selectInsureRiskInfoList(selectMinAmntDefaultReq);
            for (InsureRiskInfo insureRiskInfo : insureRiskInfoList) {
                // 保额档次集合
                List<AmountGrageInfo> insureAmountGradeInfoList = new ArrayList<>();
                // 保额档次信息
                for (EflexEmployInfo eflexEmployInfo : amountGradeInfoList) {
                    String amountGrageCode = eflexEmployInfo.getAmountGrageCode();
                    String riskCode = eflexEmployInfo.getRiskCode();
                    String riskName = eflexEmployInfo.getRiskName();
                    String riskType = eflexEmployInfo.getRiskType();
                    String dutyCode = eflexEmployInfo.getDutyCode();
                    String dutyName = eflexEmployInfo.getDutyName();
                    String annualTimeDeduction = eflexEmployInfo.getAnnualTimeDeduction();
                    String amnt = eflexEmployInfo.getAmnt();
                    String isDefaultFlag = eflexEmployInfo.getIsDefaultFlag();
                    // 获取同一个险种名称的保额档次，前台页面也是根据险种名称用来区分的档次选择模块
                    if (insureRiskInfo.getRiskName().equals(eflexEmployInfo.getRiskName())) {
                        AmountGrageInfo amountGrageInfo = new AmountGrageInfo();
                        amountGrageInfo.setAmountGrageCode(amountGrageCode);
                        amountGrageInfo.setRiskCode(riskCode);
                        amountGrageInfo.setRiskName(riskName);
                        amountGrageInfo.setRiskType(riskType);
                        amountGrageInfo.setDutyCode(dutyCode);
                        amountGrageInfo.setDutyName(dutyName);
                        // 保额
                        amountGrageInfo.setAmnt(amnt);
                        // 保费
                        Map<String, Object> paraMap = new HashMap<>();
                        String defaultDeductible = eflexEmployInfo.getDefaultDeductible() == null ? "" : eflexEmployInfo.getDefaultDeductible();
                        String defaultCompensationRatio = eflexEmployInfo.getDefaultCompensationRatio() == null ? "" : eflexEmployInfo.getDefaultCompensationRatio();
                        paraMap.put("EnsureCode", ensureCode);
                        paraMap.put("grpNo", grpNo);
                        paraMap.put("BirthDay", fcperinfo.getBirthDay());
                        paraMap.put("LevelCode", levelCode);
                        paraMap.put("OccupationType", fcperinfo.getOccupationType());
                        paraMap.put("Sex", fcperinfo.getSex());
                        paraMap.put("ServiceTerm", fcperinfo.getServiceTerm());
                        paraMap.put("Retirement", fcperinfo.getRetirement());
                        paraMap.put("Relation", "0");
                        paraMap.put("JoinMedProtect", fcperinfo.getJoinMedProtect());
                        paraMap.put("InsureCount", fcEnsure.getInsuredNumber());
                        paraMap.put("CvaliDate", eflexEmployInfo.getCvaliDate());
                        paraMap.put("RiskCode", riskCode);
                        paraMap.put("AmountGrageCode", amountGrageCode);
                        paraMap.put("DefaultDeductible", defaultDeductible);
                        paraMap.put("DefaultCompensationRatio", defaultCompensationRatio);
                        Map<String, Object> generateRequest = premTrailService.getDefaultPlanPrem(paraMap);
                        if (generateRequest != null) {
                            Map<String, Object> resultmap = premTrailService.premTrail(generateRequest);
                            if (Boolean.valueOf(resultmap.get("success").toString())) {
                                if (riskCode.equals("17020") || riskCode.equals("15060") || riskCode.equals("15070")) {
                                    Map<String, Object> mapPrem = (Map<String, Object>) resultmap.get("Prem");
                                    amountGrageInfo.setPrem((String) mapPrem.get("Prem"));
                                } else {
                                    amountGrageInfo.setPrem(resultmap.get("Prem").toString());
                                }
                            }
                        }
                        // 免赔方式
                        amountGrageInfo.setAnnualTimeDeduction(annualTimeDeduction);
                        // 免赔额列表
                        List<String> deductibleList = fcEnsureMapper.selectDeductibleList(amountGrageCode);
                        if (deductibleList != null && deductibleList.size() > 0) {
                            List<DeductibleInfo> deductibleInfoList = new ArrayList<>();
                            for (String deductible : deductibleList) {
                                DeductibleInfo deductibleInfo = new DeductibleInfo();
                                deductibleInfo.setDeductible(deductible);
                                // 判断是否已经投保选中
                                for (GetInsureRiskInfoResp insuredRiskInfo : insuredRiskInfos) {
                                    if (insuredRiskInfo.getRiskName().equals(riskName) && insuredRiskInfo.getAmountGrageCode().equals(amountGrageCode) && !StringUtils.isEmpty(insuredRiskInfo.getDeductible()) && insuredRiskInfo.getDeductible().equals(deductible)) {
                                        // 选中
                                        deductibleInfo.setIsChecked("1");
                                    }
                                }
                                // 判断是否为默认档次
                                if (deductible.equals(defaultDeductible)) {
                                    if (amountGrageInfo.getPrem() == null) {
                                        deductibleInfo.setIsDefaultFlag("0");
                                    } else {
                                        deductibleInfo.setIsDefaultFlag("1");
                                    }
                                } else {
                                    deductibleInfo.setIsDefaultFlag("0");
                                }
                                deductibleInfoList.add(deductibleInfo);
                            }
                            amountGrageInfo.setDeductibleList(deductibleInfoList);
                        }
                        // 赔付比例列表
                        List<String> compensationRatioList = fcEnsureMapper.selectCompensationRatioList(amountGrageCode);
                        if (compensationRatioList != null && compensationRatioList.size() > 0) {
                            List<CompensationRatioInfo> compensationRatioInfoList = new ArrayList<>();

                            for (String compensationRatio : compensationRatioList) {
                                CompensationRatioInfo compensationRatioInfo = new CompensationRatioInfo();
                                compensationRatioInfo.setCompensationRatio(compensationRatio);
                                // 判断是否已经投保选中
                                for (GetInsureRiskInfoResp insuredRiskInfo : insuredRiskInfos) {
                                    if (insuredRiskInfo.getRiskName().equals(riskName) && insuredRiskInfo.getAmountGrageCode().equals(amountGrageCode) && !StringUtils.isEmpty(insuredRiskInfo.getCompensationRatio()) && insuredRiskInfo.getCompensationRatio().equals(compensationRatio)) {
                                        // 选中
                                        compensationRatioInfo.setIsChecked("1");
                                    }
                                }
                                // 判断是否为默认档次
                                if (compensationRatio.equals(defaultCompensationRatio) && !StringUtils.isEmpty(amountGrageInfo.getPrem())) {
                                    compensationRatioInfo.setIsDefaultFlag("1");
                                } else {
                                    compensationRatioInfo.setIsDefaultFlag("0");
                                }
                                compensationRatioInfoList.add(compensationRatioInfo);
                            }
                            amountGrageInfo.setCompensationRatioList(compensationRatioInfoList);
                        }
                        // 可选责任列表
                        List<OptDutyCodeInfo> optDutyCodeInfoList = fcEnsureMapper.selectOptDutyCodeInfoList(amountGrageCode, riskCode);
                        if (optDutyCodeInfoList != null && optDutyCodeInfoList.size() > 0) {
                            for (OptDutyCodeInfo optDutyCodeInfo : optDutyCodeInfoList) {
                                // 判断是否已经投保选中
                                for (GetInsureRiskInfoResp insuredRiskInfo : insuredRiskInfos) {
                                    if (insuredRiskInfo.getAmountGrageCode().equals(amountGrageCode) && insuredRiskInfo.getIsChoice().equals("2") && insuredRiskInfo.getDutyCode().equals(optDutyCodeInfo.getOptDutyCode())) {
                                        // 选中
                                        optDutyCodeInfo.setIsChecked("1");
                                    }
                                }
                            }
                            amountGrageInfo.setOptDutyCodeList(optDutyCodeInfoList);
                        }
                        // 是否为默认档次
                        amountGrageInfo.setIsDefaultFlag(isDefaultFlag);
                        // 是否已经投保选中
                        for (GetInsureRiskInfoResp insuredRiskInfo : insuredRiskInfos) {
                            if (insuredRiskInfo.getRiskName().equals(riskName) && insuredRiskInfo.getAmountGrageCode().equals(amountGrageCode) && insuredRiskInfo.getIsChoice().equals("1")) {
                                // 选中
                                amountGrageInfo.setIsChecked("1");
                            }
                        }

                        // 封装赋值
                        insureAmountGradeInfoList.add(amountGrageInfo);
                    }
                }
                // 保额档次信息
                insureRiskInfo.setInsureAmountGradeInfoList(insureAmountGradeInfoList);
            }
            // 险种层级信息
            eflexPeopleZoneData.setRiskList(insureRiskInfoList);
            // 员工
            if (fcPerson.getRelation().equals(RelationEnum.SELF.getCode()) && choiceInsurePeopleNextStepReq.getPlanObject().equals(PlanObjectEnum.STAFF.getCode())) {
                staffZone.add(eflexPeopleZoneData);
                log.info("====员工投保专区信息\n" + JSONObject.toJSONString(staffZone));
                insureEflexPlanPageResp.setStaffZone(staffZone);
            } else {
                familyZone.add(eflexPeopleZoneData);
                log.info("====家属投保专区信息\n" + JSONObject.toJSONString(familyZone));
                insureEflexPlanPageResp.setFamilyZone(familyZone);
            }

        }
        log.info("====投保专区页面信息\n" + JSONObject.toJSONString(insureEflexPlanPageResp, SerializerFeature.DisableCircularReferenceDetect));

        return insureEflexPlanPageResp;
    }

    /**
     * 确认投保，1、校验投保规则 2、存储保额档次信息 3、生成订单信息
     */
    @Transactional
    public EflexConfirmInsureResp confirmInsureEflex(String token, List<PeopleInsureEflexPlanInfo> peopleInsureEflexPlanInfos) {
        // 定义返回对象
        EflexConfirmInsureResp eflexConfirmInsureResp = new EflexConfirmInsureResp();
        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);
        String perNo = globalInput.getCustomNo();
        String ensureCode = globalInput.getEnsureCode();
        //删除之前家庭下所有人员保存的保额档次临时表信息
        fpInsureEflexPlanOptionalMapper.deletefPInsureEflexPlanOptional("", perNo, ensureCode);
        fPInsureEflexPlanMapper.deletefPInsureEflexPlan("", perNo, ensureCode);
        fpInsureEflexPlanOptionalMapper.deletefPfPEflexCheckRuleEflexPlanOptional("", perNo, ensureCode);
        fPInsureEflexPlanMapper.deletefPEflexCheckRuleEflexPlan("", perNo, ensureCode);
        /**
         * 循环人员的投保选中档次信息
         */
        for (PeopleInsureEflexPlanInfo peopleInsureEflexPlanInfo : peopleInsureEflexPlanInfos) {
            String personId = peopleInsureEflexPlanInfo.getPersonId();
            /**
             * 校验人员基本信息
             */
            PersonInfo personInfo = empAndFamilyMapper.selectPersonInfo(personId);
            if (ObjectUtils.isEmpty(personInfo)) {
                throw new SystemException("未查到被保人信息！");
            } else if (StringUtils.isEmpty(personInfo.getName()) || StringUtils.isEmpty(personInfo.getSex()) || StringUtils.isEmpty(personInfo.getBirthDate()) || StringUtils.isEmpty(personInfo.getIdNo()) || StringUtils.isEmpty(personInfo.getIdType()) || StringUtils.isEmpty(personInfo.getOccupationCode()) || StringUtils.isEmpty(personInfo.getJoinMedProtect())) {
                throw new SystemException("被保人" + personInfo.getName() + "信息不完整，请前往家庭信息维护页面进行补充！");
            }
            /**
             * 判断是否选择了计划
             */
            if (peopleInsureEflexPlanInfo.getAmountGradeCodeList().size() == 0) {
                throw new SystemException("被保人" + personInfo.getName() + "弹性计划保额档次不能为空！");
            }
            /**
             * todo 校验是否存在未投保险种，之前交由前端统计进行展示提示
             */

            /**
             * 校验投保规则，并存储人员投保信息到临时表
             */
            ResultResp resultResp = eflexCheckTBRules(token, peopleInsureEflexPlanInfo);
            if (resultResp.getCode().equals(ResultCodeEnum.INSURERULE_EXCEPTION.getCode())) {
                // 自定义返回结果
                CheckTBResultInfo checkTBResultInfo = new CheckTBResultInfo(peopleInsureEflexPlanInfo.getName() + "投保规则校验不通过", (List<Map<String, Object>>) resultResp.getData());
                eflexConfirmInsureResp.setCheckTBResultInfo(checkTBResultInfo);
                return eflexConfirmInsureResp;
            }
        }
        // 弹性计划个人投保确认接口
        eflexPersonInsureConfirm(token, "02");

        return eflexConfirmInsureResp;
    }

    /**
     * 校验投保规则
     *
     * @param token
     * @param peopleInsureEflexPlanInfo
     * @return
     */
    public ResultResp eflexCheckTBRules(String token, PeopleInsureEflexPlanInfo peopleInsureEflexPlanInfo) {
        // 定义返回对象
        ResultResp resultResp = new ResultResp();
        try {
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            String perNo = globalInput.getCustomNo();
            String ensureCode = globalInput.getEnsureCode();
            String personId = peopleInsureEflexPlanInfo.getPersonId();
            // 存储投保保额档次信息，目的是为了下面的投保规则校验
            Map<String, Object> eflexPersonInsureSave = checkRuleInsureSave(token, peopleInsureEflexPlanInfo);
            if (eflexPersonInsureSave.get("code").toString().equals("200")) {
                // 校验投保规则
                boolean eflexCheckTBRules = eflexTBCheckRules.eflexCheckTBRules(personId, ensureCode, perNo, "01");
                if (eflexCheckTBRules) {
                    resultResp.setCode(ResultCodeEnum.SUCCESS.getCode());
                    resultResp.setMessage("投保规则校验通过!");
                } else if (eflexTBCheckRules.getErrorMsg() != null && !"".equals(eflexTBCheckRules.getErrorMsg())) {
                    throw new SystemException(eflexTBCheckRules.getErrorMsg());
                } else {
                    // 校验没有通过，则需要把保存的信息进行删除
                    fpInsureEflexPlanOptionalMapper.deletefPInsureEflexPlanOptional(personId, perNo, ensureCode);
                    fPInsureEflexPlanMapper.deletefPInsureEflexPlan(personId, perNo, ensureCode);
                    fpInsureEflexPlanOptionalMapper.deletefPfPEflexCheckRuleEflexPlanOptional(personId, perNo, ensureCode);
                    fPInsureEflexPlanMapper.deletefPEflexCheckRuleEflexPlan(personId, perNo, ensureCode);
                    resultResp.setCode(ResultCodeEnum.INSURERULE_EXCEPTION.getCode());
                    resultResp.setData(eflexTBCheckRules.getResult());
                }
                return resultResp;
            } else {
                throw new SystemException("投保规则校验未通过!");
            }
        } catch (Exception e) {
            log.info("投保规则校验失败：", e);
            throw new SystemException("投保规则校验失败，" + e.getMessage());
        }
    }

    /**
     * 弹性福利投保信息暂存
     *
     * @param token
     * @param peopleInsureEflexPlanInfo
     * @return
     */
    public Map<String, Object> checkRuleInsureSave(String token, PeopleInsureEflexPlanInfo peopleInsureEflexPlanInfo) {
        // 获取人员ID
        String personId = peopleInsureEflexPlanInfo.getPersonId();
        // 定义返回结果
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            /**
             * 当前员工不存在当前的家属则同步信息
             */
            Map<String, Object> SyncMap = SynchronizationFamilyinfo(token, personId);
            if (!SyncMap.get("success").equals(true)) {
                resultMap.put("message", SyncMap.get("errmsg"));
                resultMap.put("success", false);
                resultMap.put("code", "500");
                return resultMap;
            }
            personId = String.valueOf(SyncMap.get("personId"));
            String perNo = String.valueOf(SyncMap.get("perNo"));
            String ensureCode = String.valueOf(SyncMap.get("ensureCode"));
            // 获取投保的险种信息
            List<AmountGrageInfo> amountGradeCodeList = peopleInsureEflexPlanInfo.getAmountGradeCodeList();
            // 存储必选保额档次信息表
            List<FPInsureEflexPlan> fPInsureEflexPlanList = new ArrayList<>();
            for (AmountGrageInfo amountGrageInfo : amountGradeCodeList) {
                FPInsureEflexPlan fpInsureEflexPlan = new FPInsureEflexPlan();
                String insureElfexPlanNo = maxNoService.createMaxNo("CheckRuleElfexPlanNo", "", 20);
                fpInsureEflexPlan.setInsureElfexPlanNo(insureElfexPlanNo);
                // 员工编码
                fpInsureEflexPlan.setPerNo(perNo);
                // 人员ID
                fpInsureEflexPlan.setPersonId(personId);
                // 福利编码
                fpInsureEflexPlan.setEnsureCode(ensureCode);
                // 保额档次编码
                fpInsureEflexPlan.setAmountGrageCode(amountGrageInfo.getAmountGrageCode());
                // 个单状态
                fpInsureEflexPlan.setInsureState("0");
                // 险种编码
                fpInsureEflexPlan.setRiskCode(amountGrageInfo.getRiskCode());
                // 险种类型
                fpInsureEflexPlan.setRiskType(amountGrageInfo.getRiskType());
                // 投保年度
                fpInsureEflexPlan.setAppntYear(DateTimeUtil.getCurrentYear());
                // 保费
                // 是否需要重新计算下保费
                FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
                PremTrailReq premTrailReq = new PremTrailReq();
                premTrailReq.setEnsureCode(ensureCode);
                premTrailReq.setRiskCode(amountGrageInfo.getRiskCode());
                premTrailReq.setAmountGrageCode(amountGrageInfo.getAmountGrageCode());
                premTrailReq.setBirthDay(fcPerson.getBirthDate());
                premTrailReq.setSex(fcPerson.getSex());
                premTrailReq.setJoinMedProtect(fcPerson.getJoinMedProtect());
                if (amountGrageInfo.getCompensationRatioList().size() > 0) {
                    premTrailReq.setCompensationRatio(amountGrageInfo.getCompensationRatioList().get(0).getCompensationRatio());
                }
                if (amountGrageInfo.getDeductibleList().size() > 0) {
                    premTrailReq.setDeductible(amountGrageInfo.getDeductibleList().get(0).getDeductible());
                }
                FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
                premTrailReq.setCvaliDate(fcGrpOrder.getEffectDate());

                fpInsureEflexPlan.setPrem(Double.valueOf(amountGrageInfo.getPrem()));
                fpInsureEflexPlan.setOperator(globalInput.getCustomNo());
                fpInsureEflexPlan.setOperatorCom("");
                fpInsureEflexPlan = CommonUtil.initObject(fpInsureEflexPlan, "INSERT");
                // 可选保额档次信息
                List<OptDutyCodeInfo> optDutyCodeList = amountGrageInfo.getOptDutyCodeList();
                if (!ObjectUtils.isEmpty(optDutyCodeList) && optDutyCodeList.size() > 0) {
                    // 免赔额属性
                    if (!ObjectUtils.isEmpty(amountGradeCodeList) && amountGradeCodeList.size() > 1) {
                        fpInsureEflexPlan.setDeductibleAttr(StringUtils.isEmpty(amountGradeCodeList.get(0).getAnnualTimeDeduction()) ? "" : amountGradeCodeList.get(0).getAnnualTimeDeduction());
                    }
                    // 免赔额
                    if (!ObjectUtils.isEmpty(amountGrageInfo.getDeductibleList()) && amountGrageInfo.getDeductibleList().size() > 1) {
                        fpInsureEflexPlan.setDeductible(StringUtils.isEmpty(amountGrageInfo.getDeductibleList().get(0)) ? null : Double.valueOf(amountGrageInfo.getDeductibleList().get(0).toString().replaceAll(",", "")));
                    }
                    // 赔付比例
                    if (!ObjectUtils.isEmpty(amountGrageInfo.getCompensationRatioList()) && amountGrageInfo.getCompensationRatioList().size() > 1) {
                        fpInsureEflexPlan.setCompensationRatio(StringUtils.isEmpty(amountGrageInfo.getCompensationRatioList().get(0)) ? null : Double.valueOf(amountGrageInfo.getCompensationRatioList().get(0).toString()));
                    }
                    // 只有一条
                    List<FPInsureEflexPlanOptional> list = new ArrayList<>();
                    for (OptDutyCodeInfo optDutyCodeInfo : optDutyCodeList) {
                        FPInsureEflexPlanOptional fPInsureEflexPlanOptional = new FPInsureEflexPlanOptional();
                        fPInsureEflexPlanOptional.setInsureElfexPlanNo(insureElfexPlanNo);
                        // 可选责任编码
                        fPInsureEflexPlanOptional.setOptDutyCode(optDutyCodeInfo.getOptDutyCode());
                        // 保额档次编码
                        fPInsureEflexPlanOptional.setAmountGrageCode(amountGrageInfo.getAmountGrageCode());
                        // 可选责任保费
                        fPInsureEflexPlanOptional.setPrem(StringUtils.isEmpty(optDutyCodeInfo.getPrem()) ? 0.0 : Double.valueOf(optDutyCodeInfo.getPrem().replaceAll(",", "")));
                        // 个单状态
                        fPInsureEflexPlanOptional.setInsureState("0");
                        fPInsureEflexPlanOptional.setOperator(globalInput.getCustomNo());
                        fPInsureEflexPlanOptional.setOperatorCom("");
                        fPInsureEflexPlanOptional = CommonUtil.initObject(fPInsureEflexPlanOptional, "INSERT");
                        list.add(fPInsureEflexPlanOptional);
                    }
                    // 可选保额档次信息表
                    fpInsureEflexPlanOptionalMapper.insertfPfPEflexCheckRuleEflexPlanOptional(list);
                    // 投保可选保额档次信息
                    fpInsureEflexPlanOptionalMapper.insertfPInsureEflexPlanOptional(list);
                }
                fPInsureEflexPlanList.add(fpInsureEflexPlan);
            }
            // 必选保额档次信息表
            fPInsureEflexPlanMapper.insertfPEflexCheckRuleEflexPlan(fPInsureEflexPlanList);
            // 投保必选保额档次信息
            fPInsureEflexPlanMapper.insertfPInsureEflexPlan(fPInsureEflexPlanList);
            resultMap.put("code", "200");
            resultMap.put("isSuccessSave", true);
            resultMap.put("success", true);
            resultMap.put("message", "投保暂存成功");
        } catch (Exception e) {
            log.info("判断失败", e);
            resultMap.put("code", "500");
            resultMap.put("isSuccessSave", false);
            resultMap.put("success", false);
            resultMap.put("message", "投保暂存失败");
        }
        return resultMap;
    }

    // 同步信息的操作
    @Transactional
    public Map<String, Object> SynchronizationFamilyinfo(String token, String personid) {
        // 公共变量
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", true);
        GlobalInput globalInput = userService.getSession(token);
        Map<String, String> getPerNoParm = new HashMap<>();
        Map<String, String> getStaffCountParm = new HashMap<>();
        String errmsg = "";
        try {
            // 获取当前福利
            String ensureCode = globalInput.getEnsureCode();
            if (StringUtils.isEmpty(ensureCode)) {
                errmsg += "福利编号为空！";
                throw new RuntimeException();
            }
            // 获取员工所属企业
            String grpno = globalInput.getGrpNo();
            if (StringUtils.isEmpty(grpno)) {
                errmsg += "企业号为空！";
                throw new RuntimeException();
            }
            // 获取当前的员工
            String perno = globalInput.customNo;
            if (!globalInput.getGrpNo().equals(grpno)) {
                getPerNoParm.put("grpNo", grpno);
                getPerNoParm.put("perNo", perno);
                perno = fcPerInfoMapper.selectSinglePerNo(getPerNoParm);
                if (StringUtils.isEmpty(perno)) {
                    errmsg += "员工号为空！";
                    throw new RuntimeException();
                }
            }
            // 获取操作的家属
            String personId = personid;

            // 判断家属是否在员工下,同时判断存在相同家属。
            getStaffCountParm.put("perNo", perno);
            getStaffCountParm.put("personId", personid);
            List<String> list = fcStaffFamilyRelaMapper.selectStaffCount(getStaffCountParm);
            if (list.size() > 1) {
                errmsg += "员工存在多条家属！";
                throw new RuntimeException();
            } else if (list.size() == 1) {
                personId = list.get(0);
            } else {
                //
                FCPerson fcperson = initFcperson(personid);
                personId = fcperson.getPersonID();
                fcPersonMapper.insert(fcperson);
                // 同步FCpersonStaffFamilyRela
                FCStaffFamilyRela fcStaffFamilyRela = initFCStaffFamilyRela(personid, perno, personId);
                fcStaffFamilyRelaMapper.insert(fcStaffFamilyRela);
            }
            if (!StringUtils.isEmpty(errmsg)) {
                resultMap.put("success", false);
                resultMap.put("errmsg", errmsg);
            }
            // 刷新参数
            resultMap.put("personId", personId);
            resultMap.put("grpNo", grpno);
            resultMap.put("perNo", perno);
            resultMap.put("ensureCode", ensureCode);
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("errmsg", StringUtils.isEmpty(errmsg) ? "同步人员信息失败！" : errmsg);
        }

        return resultMap;
    }

    private FCPerson initFcperson(String personid) {
        FCPerson fcperson = fcPersonMapper.selectByPrimaryKey(personid);
        fcperson.setPersonID(maxNoService.createMaxNo("PersonID", null, 20));
        fcperson = CommonUtil.initObject(fcperson, "INSERT");
        return fcperson;
    }

    private FCStaffFamilyRela initFCStaffFamilyRela(String oldpersonid, String perno, String newoldpersonid) {
        FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectPersonIdInfo(oldpersonid);
        fcStaffFamilyRela.setPerNo(perno);
        fcStaffFamilyRela.setPersonID(newoldpersonid);
        fcStaffFamilyRela.setRelation(fcStaffFamilyRela.getRelation());
        fcStaffFamilyRela = CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
        return fcStaffFamilyRela;
    }

    public Boolean eflexHealthyInform(String token, PeopleInsureEflexPlanInfo peopleInsureEflexPlanInfo) {
        try {
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            String grpNo = globalInput.getGrpNo();
            String perNo = globalInput.getCustomNo();
            // 从个人注册期表查询员工职级
            String levelCode = fcPerRegistDayMapper.selectLevelCodeByPerNo(perNo, ensureCode);
            FCEnsure fCEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
            List<AmountGrageInfo> amountGradeCodeList = peopleInsureEflexPlanInfo.getAmountGradeCodeList();
            if (amountGradeCodeList != null && amountGradeCodeList.size() > 0) {
                boolean flag = false;
                String relation = peopleInsureEflexPlanInfo.getRelation();
                if (relation.equals("0")) {
                    double amnt15070 = 0.0;
                    for (AmountGrageInfo amountGrageInfo : amountGradeCodeList) {
                        double amntOther = 0.0;
                        String riskCode = amountGrageInfo.getRiskCode();
                        double defaultAmnt = Double.valueOf(amountGrageInfo.getAmnt());
                        if (riskCode.equals("15070")) {
                            amnt15070 = CommonUtil.add(amnt15070, defaultAmnt);
                            List<OptDutyCodeInfo> optDutyCodeList = amountGrageInfo.getOptDutyCodeList();
                            if (optDutyCodeList != null && optDutyCodeList.size() > 0) {
                                for (OptDutyCodeInfo optDutyCodeInfo : optDutyCodeList) {
                                    double optionalAmnt = Double.valueOf(optDutyCodeInfo.getAmount());
                                    amnt15070 = CommonUtil.add(amnt15070, optionalAmnt);
                                }
                            }
                        } else {
                            amntOther = CommonUtil.add(amntOther, defaultAmnt);
                            List<OptDutyCodeInfo> optDutyCodeList = amountGrageInfo.getOptDutyCodeList();
                            if (optDutyCodeList != null && optDutyCodeList.size() > 0) {
                                for (OptDutyCodeInfo optDutyCodeInfo : optDutyCodeList) {
                                    double optionalAmnt = Double.valueOf(optDutyCodeInfo.getAmount());
                                    amntOther = CommonUtil.add(amntOther, optionalAmnt);
                                }
                            }

                            // 查询是否需要健康告知
                            CheckIsNeedInformReq checkIsNeedInformReq = new CheckIsNeedInformReq();
                            checkIsNeedInformReq.setRiskCode(riskCode);
                            checkIsNeedInformReq.setEnsureCode(ensureCode);
                            checkIsNeedInformReq.setGrpNo(grpNo);
                            checkIsNeedInformReq.setBirthDay(fcPerInfo.getBirthDay());
                            checkIsNeedInformReq.setSex(fcPerInfo.getSex());
                            checkIsNeedInformReq.setLevelCode(levelCode);
                            checkIsNeedInformReq.setCvaliDate(fCEnsure.getCvaliDate());
                            checkIsNeedInformReq.setRelation(relation);
                            checkIsNeedInformReq.setAmount(amntOther);
                            int checkIsNeedInform = fcPlanHealthDesignRelaMapper.checkIsNeedHealthNotice(checkIsNeedInformReq);
                            if (checkIsNeedInform > 0) {
                                flag = true;
                                break;
                            }
                        }
                    }
                    if (amnt15070 > 0) {
                        // 查询是否需要健康告知
                        CheckIsNeedInformReq checkIsNeedInformReq = new CheckIsNeedInformReq();
                        checkIsNeedInformReq.setRiskCode("15070");
                        checkIsNeedInformReq.setEnsureCode(ensureCode);
                        checkIsNeedInformReq.setGrpNo(grpNo);
                        checkIsNeedInformReq.setBirthDay(fcPerInfo.getBirthDay());
                        checkIsNeedInformReq.setSex(fcPerInfo.getSex());
                        checkIsNeedInformReq.setLevelCode(levelCode);
                        checkIsNeedInformReq.setCvaliDate(fCEnsure.getCvaliDate());
                        checkIsNeedInformReq.setRelation(relation);
                        checkIsNeedInformReq.setAmount(amnt15070);
                        int checkIsNeedInform = fcPlanHealthDesignRelaMapper.checkIsNeedHealthNotice(checkIsNeedInformReq);
                        if (checkIsNeedInform > 0) {
                            flag = true;
                        }
                    }
                } else {
                    double amnt15070 = 0.0;
                    for (AmountGrageInfo amountGrageInfo : amountGradeCodeList) {
                        double amntOther = 0.0;
                        String riskCode = amountGrageInfo.getRiskCode();
                        double defaultAmnt = Double.valueOf(amountGrageInfo.getAmnt());
                        if (riskCode.equals("15070")) {
                            amnt15070 = CommonUtil.add(amnt15070, defaultAmnt);
                            List<OptDutyCodeInfo> optDutyCodeList = amountGrageInfo.getOptDutyCodeList();
                            if (optDutyCodeList != null && optDutyCodeList.size() > 0) {
                                for (OptDutyCodeInfo optDutyCodeInfo : optDutyCodeList) {
                                    double optionalAmnt = Double.valueOf(optDutyCodeInfo.getAmount());
                                    amnt15070 = CommonUtil.add(amnt15070, optionalAmnt);
                                }
                            }
                        } else {
                            amntOther = CommonUtil.add(amntOther, defaultAmnt);
                            List<OptDutyCodeInfo> optDutyCodeList = amountGrageInfo.getOptDutyCodeList();
                            if (optDutyCodeList != null && optDutyCodeList.size() > 0) {
                                for (OptDutyCodeInfo optDutyCodeInfo : optDutyCodeList) {
                                    double optionalAmnt = Double.valueOf(optDutyCodeInfo.getAmount());
                                    amntOther = CommonUtil.add(amntOther, optionalAmnt);
                                }
                            }
                            // 查询是否需要健康告知
                            CheckIsNeedInformReq checkIsNeedInformReq = new CheckIsNeedInformReq();
                            checkIsNeedInformReq.setRiskCode(riskCode);
                            checkIsNeedInformReq.setEnsureCode(ensureCode);
                            checkIsNeedInformReq.setGrpNo(grpNo);
                            checkIsNeedInformReq.setBirthDay(fcPerInfo.getBirthDay());
                            checkIsNeedInformReq.setSex(fcPerInfo.getSex());
                            checkIsNeedInformReq.setLevelCode(levelCode);
                            checkIsNeedInformReq.setCvaliDate(fCEnsure.getCvaliDate());
                            checkIsNeedInformReq.setRelation(relation);
                            checkIsNeedInformReq.setAmount(amntOther);
                            int checkIsNeedInform = fcPlanHealthDesignRelaMapper.checkIsNeedHealthNotice(checkIsNeedInformReq);
                            if (checkIsNeedInform > 0) {
                                flag = true;
                                break;
                            }
                        }
                    }
                    if (amnt15070 > 0) {
                        // 查询是否需要健康告知
                        CheckIsNeedInformReq checkIsNeedInformReq = new CheckIsNeedInformReq();
                        checkIsNeedInformReq.setRiskCode("15070");
                        checkIsNeedInformReq.setEnsureCode(ensureCode);
                        checkIsNeedInformReq.setGrpNo(grpNo);
                        checkIsNeedInformReq.setBirthDay(fcPerInfo.getBirthDay());
                        checkIsNeedInformReq.setSex(fcPerInfo.getSex());
                        checkIsNeedInformReq.setLevelCode(levelCode);
                        checkIsNeedInformReq.setCvaliDate(fCEnsure.getCvaliDate());
                        checkIsNeedInformReq.setRelation(relation);
                        checkIsNeedInformReq.setAmount(amnt15070);
                        int checkIsNeedInform = fcPlanHealthDesignRelaMapper.checkIsNeedHealthNotice(checkIsNeedInformReq);
                        if (checkIsNeedInform > 0) {
                            flag = true;
                        }
                    }
                }
                // flag=true则需要弹出健康告知，flag=false则不需要弹出健康告知。
                return flag;
            } else {
                throw new SystemException("保额档次不能为空！");
            }
        } catch (Exception e) {
            log.info("确认失败", e);
            throw new SystemException("个人健康告知接口异常！");
        }
    }

    // 判断是否需要健康告知
    public Boolean eflexHealthyInform(String token, String personId, String relation) {
        try {
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            String grpNo = globalInput.getGrpNo();
            String perNo = globalInput.getCustomNo();
            // 从个人注册期表查询员工职级
            String levelCode = fcPerRegistDayMapper.selectLevelCodeByPerNo(perNo, ensureCode);
            FCEnsure fCEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
            // 循环人员投保的保额档次
            List<InsureEflexOrderDetailInfo> insureEflexOrderDetailInfos = fcOrderMapper.selfOrderDetailDesInfo1(ensureCode, perNo, personId);
            if (insureEflexOrderDetailInfos != null && insureEflexOrderDetailInfos.size() > 0) {
                boolean flag = false;
                double amnt15070 = 0.0;
                for (InsureEflexOrderDetailInfo insureEflexOrderDetailInfo : insureEflexOrderDetailInfos) {
                    double amntOther = 0.0;
                    String riskCode = insureEflexOrderDetailInfo.getRiskCode();
                    double defaultAmnt = Double.valueOf(insureEflexOrderDetailInfo.getAmount());
                    if (riskCode.equals("15070")) {
                        amnt15070 = CommonUtil.add(amnt15070, defaultAmnt);
                    } else {
                        amntOther = CommonUtil.add(amntOther, defaultAmnt);

                        // 查询是否需要健康告知
                        CheckIsNeedInformReq checkIsNeedInformReq = new CheckIsNeedInformReq();
                        checkIsNeedInformReq.setRiskCode(riskCode);
                        checkIsNeedInformReq.setEnsureCode(ensureCode);
                        checkIsNeedInformReq.setGrpNo(grpNo);
                        checkIsNeedInformReq.setBirthDay(fcPerInfo.getBirthDay());
                        checkIsNeedInformReq.setSex(fcPerInfo.getSex());
                        checkIsNeedInformReq.setLevelCode(levelCode);
                        checkIsNeedInformReq.setCvaliDate(fCEnsure.getCvaliDate());
                        checkIsNeedInformReq.setRelation(relation);
                        checkIsNeedInformReq.setAmount(amntOther);
                        int checkIsNeedInform = fcPlanHealthDesignRelaMapper.checkIsNeedHealthNotice(checkIsNeedInformReq);
                        if (checkIsNeedInform > 0) {
                            flag = true;
                            break;
                        }
                    }
                }
                if (amnt15070 > 0) {
                    // 查询是否需要健康告知
                    CheckIsNeedInformReq checkIsNeedInformReq = new CheckIsNeedInformReq();
                    checkIsNeedInformReq.setRiskCode("15070");
                    checkIsNeedInformReq.setEnsureCode(ensureCode);
                    checkIsNeedInformReq.setGrpNo(grpNo);
                    checkIsNeedInformReq.setBirthDay(fcPerInfo.getBirthDay());
                    checkIsNeedInformReq.setSex(fcPerInfo.getSex());
                    checkIsNeedInformReq.setLevelCode(levelCode);
                    checkIsNeedInformReq.setCvaliDate(fCEnsure.getCvaliDate());
                    checkIsNeedInformReq.setRelation(relation);
                    checkIsNeedInformReq.setAmount(amnt15070);
                    int checkIsNeedInform = fcPlanHealthDesignRelaMapper.checkIsNeedHealthNotice(checkIsNeedInformReq);
                    if (checkIsNeedInform > 0) {
                        flag = true;
                    }
                }
                // flag=true则需要弹出健康告知，flag=false则不需要弹出健康告知。
                return flag;
            } else {
                throw new SystemException("保额档次不能为空！");
            }
        } catch (Exception e) {
            log.info("确认失败", e);
            throw new SystemException("个人健康告知接口异常！");
        }
    }

    // 弹性计划-未成年人投保确认函
    public ResultResp eflexJuvenilesConfirmation(String token, PeopleInsureEflexPlanInfo peopleInsureEflexPlanInfo) {
        // 定义返回结果
        ResultResp resultResp = new ResultResp();
        try {
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            String personId = peopleInsureEflexPlanInfo.getPersonId();
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            int age = Integer.valueOf(DateTimeUtil.getCurrentAge(fcPerson.getBirthDate(), fcEnsure.getCvaliDate()));
            if (age < 18) {
                // 投保确认书
                Map<String, Object> dataMap = new HashMap<>();
                FCPerInfo staffInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
                dataMap.put("staffName", staffInfo.getName());
                dataMap.put("staffIDNo", staffInfo.getIDNo());
                dataMap.put("insuredName", fcPerson.getName());
                Map<String, String> map = new HashMap<String, String>();
                map.put("perNo", globalInput.getCustomNo());
                map.put("personID", personId);
                // 查询此人是否在本员工下是否有此家属
                FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(map);
                if (rela == null) {
                    // 查询家属对应的关系（因为这里有一个大前提，就是相同的员工，所以无需加上相同员工下的条件）
                    rela = fcStaffFamilyRelaMapper.selectPersonIdInfo(personId);
                }
                if (rela != null) {
                    if ("3".equals(rela.getRelation())) {
                        // 3为子女。 员工是被保人的父母。这里需要拆分成父亲或母亲
                        if ("0".equals(staffInfo.getSex())) {
                            dataMap.put("relation", "父亲");
                        } else {
                            dataMap.put("relation", "母亲");
                        }
                    } else {
                        String relationName = fdCodeMapper.selectNameByCode("Relation", rela.getRelation());
                        dataMap.put("relation", relationName);
                    }
                } else {
                    dataMap.put("relation", "");
                }
                List<FCPlanRiskDuty> dutyList = new ArrayList<FCPlanRiskDuty>();

                List<AmountGrageInfo> amountGradeCodeList = peopleInsureEflexPlanInfo.getAmountGradeCodeList();
                for (AmountGrageInfo amountGrageInfo : amountGradeCodeList) {
                    FCPlanRiskDuty fcPlanRiskDuty = new FCPlanRiskDuty();
                    String amountGrageCode = amountGrageInfo.getAmountGrageCode();
                    FcDutyAmountGrade fcDutyAmountGrade = fcDutyAmountGradeMapper.selectByPrimaryKey(amountGrageCode);
                    fcPlanRiskDuty.setDutyCode(fcDutyAmountGrade.getDutyCode());
                    fcPlanRiskDuty.setAmnt(fcDutyAmountGrade.getAmnt());
                    dutyList.add(fcPlanRiskDuty);
                }
                // 在这里判断弹性福利中是否含有身故保险
                List<String> dutyCodeList = Arrays.asList("GD0017", "GD0037", "GD0050", "GD0051", "GD0052", "GD0054");
                int count = 0;
                for (FCPlanRiskDuty dutyInfo : dutyList) {
                    if (dutyCodeList.contains(dutyInfo.getDutyCode())) {
                        count++;
                    }
                }
                if (0 == count) {
                    resultResp.setCode(ResultCodeEnum.SUCCESS.getCode());
                    resultResp.setMessage("该计划中不包含身故险种，故不弹出确认函！");
                    return resultResp;
                }
                double deathAmnt = 0.00;
                for (FCPlanRiskDuty dutyInfo : dutyList) {
                    if ("GD0017".equals(dutyInfo.getDutyCode())) {
                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                    } else if ("GD0037".equals(dutyInfo.getDutyCode())) {
                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                    }
                }
                double Amnt15070 = 0.00;
                for (FCPlanRiskDuty dutyInfo : dutyList) {
                    if ("GD0050".equals(dutyInfo.getDutyCode())) {
                        Amnt15070 = dutyInfo.getAmnt();
                    }
                    if ("GD0051".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                        Amnt15070 = dutyInfo.getAmnt();
                    }
                    if ("GD0052".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                        Amnt15070 = dutyInfo.getAmnt();
                    }
                    if ("GD0054".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                        Amnt15070 = dutyInfo.getAmnt();
                    }
                }
                deathAmnt = CommonUtil.add(deathAmnt, Amnt15070);
                if (0.00 == deathAmnt) {
                    resultResp.setCode(ResultCodeEnum.SUCCESS.getCode());
                    resultResp.setMessage("本次投保身故保险金额为0，故不弹出确认函！");
                    return resultResp;
                }
                // 调用核心查询累计风险保额
                String riskAmnt = getRiskAmnt(personId);
                if (riskAmnt == null || "".equals(riskAmnt)) {
                    log.info("调用核心接口查询风险保额失败，请联系管理员！");
                    throw new BusinessException("调用核心接口查询风险保额失败，请联系管理员！");
                }
                // 本次身故保险金
                dataMap.put("deathAmnt", deathAmnt);
                // 累计身故保险金
                dataMap.put("deathAmntCount", riskAmnt);
                dataMap.put("month", DateTimeUtil.getCurrentMonth());
                dataMap.put("year", DateTimeUtil.getCurrentYear());
                dataMap.put("month", DateTimeUtil.getCurrentMonth());
                dataMap.put("day", DateTimeUtil.getCurrentDay());
                resultResp.setCode(ResultCodeEnum.INSURERULE_EXCEPTION.getCode());
                resultResp.setMessage("未成年人投保确认书！");
                resultResp.setData(dataMap);
                return resultResp;
            } else {
                resultResp.setCode(ResultCodeEnum.SUCCESS.getCode());
                resultResp.setMessage("年龄校验通过！");
                return resultResp;
            }
        } catch (Exception e) {
            throw new SystemException("系统繁忙！");
        }
    }

    // 弹性计划-未成年人投保确认函
    public ResultResp eflexJuvenilesConfirmation(String token, String personId) {
        // 定义返回结果
        ResultResp resultResp = new ResultResp();
        try {
            // 获取token信息
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            String perNo = globalInput.getCustomNo();
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            int age = Integer.valueOf(DateTimeUtil.getCurrentAge(fcPerson.getBirthDate(), fcEnsure.getCvaliDate()));
            if (age < 18) {
                JuvenilesConfirmationInfo juvenilesConfirmationInfo = new JuvenilesConfirmationInfo();
                // 投保确认书
                FCPerInfo staffInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
                juvenilesConfirmationInfo.setStaffName(staffInfo.getName());
                juvenilesConfirmationInfo.setStaffIDNo(staffInfo.getIDNo());
                juvenilesConfirmationInfo.setInsuredName(fcPerson.getName());
                Map<String, String> map = new HashMap<>();
                map.put("perNo", globalInput.getCustomNo());
                map.put("personID", personId);
                // 查询此人是否在本员工下是否有此家属
                FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(map);
                if (rela == null) {
                    // 查询家属对应的关系（因为这里有一个大前提，就是相同的员工，所以无需加上相同员工下的条件）
                    rela = fcStaffFamilyRelaMapper.selectPersonIdInfo(personId);
                }
                if (rela != null) {
                    if ("3".equals(rela.getRelation())) {
                        // 3为子女。 员工是被保人的父母。这里需要拆分成父亲或母亲
                        if ("0".equals(staffInfo.getSex())) {
                            juvenilesConfirmationInfo.setRelation("父亲");
                        } else {
                            juvenilesConfirmationInfo.setRelation("母亲");
                        }
                    } else {
                        String relationName = fdCodeMapper.selectNameByCode("Relation", rela.getRelation());
                        juvenilesConfirmationInfo.setRelation(relationName);
                    }
                } else {
                    juvenilesConfirmationInfo.setRelation("");
                }
                List<InsureEflexOrderDetailInfo> insureEflexOrderDetailInfos = fcOrderMapper.selfOrderDetailDesInfo1(ensureCode, perNo, personId);
                // 在这里判断弹性福利中是否含有身故保险
                List<String> dutyCodeList = Arrays.asList("GD0017", "GD0037", "GD0050", "GD0051", "GD0052", "GD0054");
                int count = 0;
                for (InsureEflexOrderDetailInfo dutyInfo : insureEflexOrderDetailInfos) {
                    if (dutyCodeList.contains(dutyInfo.getDutyCode())) {
                        count++;
                    }
                }
                if (0 == count) {
                    resultResp.setCode(ResultCodeEnum.SUCCESS.getCode());
                    resultResp.setMessage("该计划中不包含身故险种，故不弹出确认函！");
                    return resultResp;
                }
                double deathAmnt = 0.00;
                for (InsureEflexOrderDetailInfo dutyInfo : insureEflexOrderDetailInfos) {
                    if ("GD0017".equals(dutyInfo.getDutyCode())) {
                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmount());
                    } else if ("GD0037".equals(dutyInfo.getDutyCode())) {
                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmount());
                    }
                }
                double Amnt15070 = 0.00;
                for (InsureEflexOrderDetailInfo dutyInfo : insureEflexOrderDetailInfos) {
                    if ("GD0050".equals(dutyInfo.getDutyCode())) {
                        Amnt15070 = dutyInfo.getAmount();
                    }
                    if ("GD0051".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmount()) {
                        Amnt15070 = dutyInfo.getAmount();
                    }
                    if ("GD0052".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmount()) {
                        Amnt15070 = dutyInfo.getAmount();
                    }
                    if ("GD0054".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmount()) {
                        Amnt15070 = dutyInfo.getAmount();
                    }
                }
                deathAmnt = CommonUtil.add(deathAmnt, Amnt15070);
                if (0.00 == deathAmnt) {
                    resultResp.setCode(ResultCodeEnum.SUCCESS.getCode());
                    resultResp.setMessage("本次投保身故保险金额为0，故不弹出确认函！");
                    return resultResp;
                }
                // 调用核心查询累计风险保额
                String riskAmnt = getRiskAmnt(personId);
                if (riskAmnt == null || "".equals(riskAmnt)) {
                    log.info("调用核心接口查询风险保额失败，请联系管理员！");
                    throw new BusinessException("调用核心接口查询风险保额失败，请联系管理员！");
                }
                // 本次身故保险金
                juvenilesConfirmationInfo.setDeathAmnt(deathAmnt);
                // 累计身故保险金
                Double riskAmntTotal = CommonUtil.add(Double.valueOf(riskAmnt), deathAmnt);
                juvenilesConfirmationInfo.setDeathAmntCount(riskAmntTotal);
                juvenilesConfirmationInfo.setYear(DateTimeUtil.getCurrentYear());
                juvenilesConfirmationInfo.setMonth(DateTimeUtil.getCurrentMonth());
                juvenilesConfirmationInfo.setDay(DateTimeUtil.getCurrentDay());
                resultResp.setCode(ResultCodeEnum.INSURERULE_EXCEPTION.getCode());
                resultResp.setMessage("未成年人投保确认书！");
                resultResp.setData(juvenilesConfirmationInfo);
                return resultResp;
            } else {
                resultResp.setCode(ResultCodeEnum.SUCCESS.getCode());
                resultResp.setMessage("年龄校验通过！");
                return resultResp;
            }
        } catch (Exception e) {
            throw new SystemException("系统繁忙！");
        }
    }

    /**
     * 调用核心累计风险保额
     *
     * @param personId
     * @return
     */
    public String getRiskAmnt(String personId) {
        try {
            // 获取个人信息
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
            if (fcPerson == null) {
                log.info("风险保额接口personId==" + personId + "不存在。");
                return "";
            }
            String requestXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + "<RequestInfo>\n" + "\t<HEAD>\n" +
                    // 交易流水号
                    "\t\t<TransRefGUID>" + CommonUtil.getUUID() + "</TransRefGUID>\n" +
                    // 接口交易类型
                    "\t\t<TransType>BF0002</TransType>\n" +
                    // 交易日期
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    // 交易时间
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" + "\t</HEAD>\n" + "\t<InsuredInfo>\n" + "\t\t<InsuredName>" + fcPerson.getName() + "</InsuredName>\n" + "\t\t<Sex>" + fcPerson.getSex() + "</Sex>\n" + "\t\t<CertiType>" + fcPerson.getIDType() + "</CertiType>\n" + "\t\t<CertiCode>" + fcPerson.getIDNo() + "</CertiCode>\n" + "\t\t<Birthday>" + fcPerson.getBirthDate() + "</Birthday>\n" +
                    // 核心固定值 5-表示未成年人身故风险保额
                    "\t\t<AmountType>5</AmountType>\t\n" + "\t</InsuredInfo>\n" + "</RequestInfo>";
            log.info("累计风险保额请求报文：" + requestXml);
            long a = System.currentTimeMillis();
            // 调用核心接口
            RemoteDelegate remoteDelegate = RemoteDelegate.getInstance();
            boolean success = remoteDelegate.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "", requestXml);
            long b = System.currentTimeMillis();
            log.info("连接核心接口所用时间" + ((a - b) / 1000.0) + "秒");
            if (success) {
                Map<String, Object> responseXml = remoteDelegate.getResult();
                log.info("调用核心接口查询风险保额返回报文：" + JSON.toJSONString(responseXml));
                return ((RiskInfo) responseXml.get("RiskInfo")).getAmount();
            }
            return "";
        } catch (Exception e) {
            log.info("", e);
            return "";
        }
    }

    /**
     * 确认投保
     *
     * @param token
     * @param orderSource
     * @return
     */
    public ResultResp eflexPersonInsureConfirm(String token, String orderSource) {
        // 定义返回结果
        ResultResp resultResp = new ResultResp();
        GlobalInput globalInput = userService.getSession(token);
        String payType = "";
        try {
            /*************************************
             * 准备数据
             *********************************************/
            String perNo = globalInput.getCustomNo();
            String ensureCode = globalInput.getEnsureCode();
            String grpNo = globalInput.getGrpNo();
            Map<String, Object> fpMap = new HashMap<>();
            fpMap.put("perNo", perNo);
            fpMap.put("ensureCode", ensureCode);
            // 获取福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(globalInput.getEnsureCode());
            /*****************************
             * 确认所有被保人所投计划是否符合投保规则
             ***********************************/
            // 弹性计划-获取员工下所有被保人的personid
            List<Map<String, String>> getPersonIdList = fPInsureEflexPlanMapper.getPersonIdList(ensureCode, perNo);
            if (getPersonIdList == null || getPersonIdList.size() < 1) {// 没有被保人
                FCPerInfo fCPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
                log.info("系统异常，没有被保人");
                throw new SystemException(fCPerInfo.getName() + "未投保，请投保");
            }
            // 获取支付方式
            List<Map<String, String>> payTypeList = fcGrpOrderMapper.getPayType(grpNo, ensureCode);
            if (payTypeList != null && payTypeList.size() > 0 && !ObjectUtils.isEmpty(payTypeList.get(0))) {
                Map<String, String> payTypeMap = payTypeList.get(0);
                payType = payTypeMap.get("configValue");
            } else {
                throw new SystemException("订单提交失败，获取支付方式失败！");
            }
            // 获取代理人信息
            List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
            FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);
            // 根据福利编号获取团体保单信息
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(fcEnsure.getEnsureCode());
            // 获取员工信息
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
            // 判断订单是否存在
            List<FCOrder> fcOrderList = fcOrderMapper.selectOrder(perNo, ensureCode);
            int size = fcOrderList.size();
            if (size > 0) {
                for (FCOrder fcOrder : fcOrderList) {
                    String orderNo = fcOrder.getOrderNo();
                    // 删除个人投保弹性计划正式表
                    int i = fcInsureEflexPlanMapper.deleteByOrderNo(orderNo);
                    // 删除个人投保弹性计划可选责任正式表
                    int i1 = fcInsureEflexPlanOptionalMapper.deleteByOrderNo(orderNo);

                    int i4 = fcOrderItemDetailMapper.deleteByOrderNo(orderNo);
                    // 删除子订单表
                    int i5 = fcOrderItemMapper.deleteByOrderNo(orderNo);
                    // 删除被保人表
                    int i6 = fcOrderInsuredMapper.deleteByOrderNo(orderNo);

                    // 第二步，更新订单信息
                    insureService.updateOrderInfo(fcPerInfo, globalInput, fcGrpOrder, fcEnsure, payType, fpMap, fdAgentInfo, orderNo, orderSource);
                    // 第三步，插入子订单信息
                    insureService.insertOrderItemInfo(fcPerInfo, globalInput, fcGrpOrder, fcEnsure, payType, fpMap, fdAgentInfo, orderNo);
                }
            } else {
                insureService.insertOrderInfo(fcPerInfo, globalInput, fcGrpOrder, fcEnsure, payType, fpMap, fdAgentInfo, orderSource);
            }
            resultResp.setSuccess(true);
            resultResp.setData(payType);
            resultResp.setCode(ResultCodeEnum.SUCCESS.getCode());
            resultResp.setMessage("订单提交成功!");
            return resultResp;
        } catch (Exception e) {
            throw new SystemException("订单提交失败，" + e.getMessage());
        }
    }

    // 投保确认页面信息加载
    public EflexConfirmInsureResp insuredConfirmPageEflex(String token) {
        try {
            /**
             * 获取基础信息
             */
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            String perNo = globalInput.getCustomNo();
            // 获取员工信息
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
            // 查询订单是否已经生成
            List<FCOrder> fcOrders = fcOrderMapper.selectOrder(perNo, ensureCode);
            if (fcOrders.size() == 1) {
                FcBatchPayBankInfo fcBatchPayBankInfo = fcBatchPayBankInfoMapper
                        .selectFcBatchPayBankInfoByOrderNo(fcOrders.get(0).getOrderNo());
                if (!ObjectUtils.isEmpty(fcBatchPayBankInfo)) {
                    fcPerInfo.setOpenBank(fcBatchPayBankInfo.getPayBankCode());
                    fcPerInfo.setOpenAccount(fcBatchPayBankInfo.getBankAccount());
                }
            }
            Map<String, Object> param = new HashMap<>();
            param.put("ensureCode", ensureCode);
            param.put("perNo", perNo);
            List<Map<String, Object>> familyPremList = fPInsureEflexPlanMapper.getTotlePrem(param);
            if (familyPremList.size() < 1) {
                log.info("系统异常，没有被保人");
                throw new RuntimeException();
            }
            /**
             * 封装弹性计划投保确认信息
             */
            EflexConfirmInsureResp eflexConfirmInsureResp = calStaffPolicyPremDetail(token, ensureCode, perNo, fcPerInfo, familyPremList);
            return eflexConfirmInsureResp;
        } catch (Exception e) {
            throw new SystemException("投保确认页面信息加载失败，" + e.getMessage());
        }
    }

    /**
     * 封装弹性计划投保确认信息
     *
     * @param token
     * @param ensureCode
     * @param perNo
     * @param fcPerInfo
     * @param familyPremList
     * @return
     */
    public EflexConfirmInsureResp calStaffPolicyPremDetail(String token, String ensureCode, String perNo, FCPerInfo fcPerInfo, List<Map<String, Object>> familyPremList) {

        /**
         * 封装返回结果
         */
        EflexConfirmInsureResp eflexConfirmInsureResp = new EflexConfirmInsureResp();
        // 个人健康告知确认信息
        List<HealthNoticeInfo> healthNoticeList = new ArrayList<>();
        // 未成年人投保确认信息
        List<JuvenilesConfirmationInfo> juvenilesConfirmationInfoList = new ArrayList<>();
        // 支付方式
        String payType = "";
        try {
            // 获取员工的personId
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
            String staffPersonId = "";
            if (fcStaffFamilyRela != null) {
                staffPersonId = fcStaffFamilyRela.getPersonID();
            }
            // 获取员工注册期表数据
            Map<String, Object> params = new HashMap<>();
            params.put("ensureCode", ensureCode);
            params.put("perNo", perNo);
            FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(params);
            // 获取支付方式
            Map<String, Object> ensureMap = new HashMap<>();
            ensureMap.put("ensureCode", ensureCode);
            ensureMap.put("configNo", "008");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(ensureMap);
            if (fcEnsureConfig != null) {
                payType = fcEnsureConfig.getConfigValue();
            }
            /**
             * 投保汇总信息
             */
            EflexTotalInfo eflexTotalInfo = new EflexTotalInfo();
            // 家属福利额度
            double familyGrpTotalPrem = fcPerRegistDay.getFamilyGrpPrem() == null ? 0.0 : fcPerRegistDay.getFamilyGrpPrem();
            eflexTotalInfo.setFamilyGrpPrem(familyGrpTotalPrem);
            // 家属自付保费
            double familyTotalSelfPrem = 0.0;
            // 员工自付保费
            double staffSelfPrem = 0.00;
            // 员工投保保费
            double staffInsurePrem = 0.0;
            // 家属累计投保保费
            double familyTotalInsurePrem = 0.0;
            // 员工福利额度（初始福利额度，非使用的福利额度）
            double staffGrpPrem = 0.0;


            /**
             * 循环人员的信息
             */
            if (familyPremList != null && familyPremList.size() > 0) {
                List<EflexPeopleInsureInfo> peopleInsureInfoList = new ArrayList<>();
                for (Map<String, Object> map : familyPremList) {
                    EflexPeopleInsureInfo eflexPeopleInsureInfo = new EflexPeopleInsureInfo();
                    String personId = map.get("personId").toString();
                    Double totalPrem = (Double) map.get("prem");
                    // 查询人员信息
                    // FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
                    SelectFcPersonInfoByPerNoAndPersonIdReq selectFcPersonInfoByPerNoAndPersonIdReq = new SelectFcPersonInfoByPerNoAndPersonIdReq(perNo, personId);
                    PeopleInsureInfo peopleInsureInfo = fcPersonMapper.selectFcPersonInfoByPernoAndPersonId(selectFcPersonInfoByPerNoAndPersonIdReq);
                    eflexPeopleInsureInfo.setPersonId(personId);
                    eflexPeopleInsureInfo.setName(peopleInsureInfo.getName());
                    eflexPeopleInsureInfo.setRelation(peopleInsureInfo.getRelation());
                    eflexPeopleInsureInfo.setRelationName(peopleInsureInfo.getRelationName());
                    if (!StringUtils.isEmpty(staffPersonId)) {
                        if (staffPersonId.equals(personId)) {
                            // 员工企业缴费
                            if (payType.equals("1")) {// 企业缴纳
                                staffGrpPrem = totalPrem;
                            } else {
                                if (fcPerRegistDay.getStaffGrpPrem() != null)
                                    staffGrpPrem = fcPerRegistDay.getStaffGrpPrem();
                                if (totalPrem > staffGrpPrem) {
                                    staffSelfPrem = CommonUtil.sub(totalPrem, staffGrpPrem);
                                } else {
                                    staffGrpPrem = totalPrem;
                                }
                            }
                            // 员工投保保费
                            eflexPeopleInsureInfo.setInsurePrem(totalPrem);
                            staffInsurePrem = totalPrem;
                            // 员工企业缴费
                            eflexPeopleInsureInfo.setGrpPrem(staffGrpPrem);
                            // 员工个人缴费
                            eflexPeopleInsureInfo.setSelfPrem(staffSelfPrem);
                            peopleInsureInfoList.add(eflexPeopleInsureInfo);
                        } else {
                            // 家属企业缴费
                            double familyGrpPrem = 0.0;
                            // 家属个人缴费
                            double familySelfPrem = 0.00;
                            if (payType.equals("1")) {// 企业缴纳
                                familyGrpPrem = totalPrem;
                            } else {
                                if (totalPrem > familyGrpTotalPrem) {
                                    familyGrpPrem = familyGrpTotalPrem;
                                    familySelfPrem = CommonUtil.sub(totalPrem, familyGrpTotalPrem);
                                    familyGrpTotalPrem = 0.0;
                                } else if (totalPrem <= familyGrpTotalPrem) {
                                    familyGrpPrem = totalPrem;
                                    familyGrpTotalPrem = CommonUtil.sub(familyGrpTotalPrem, totalPrem);
                                }
                            }
                            familyTotalSelfPrem += familySelfPrem;
                            // 家属投保险种总保费
                            eflexPeopleInsureInfo.setInsurePrem(totalPrem);
                            familyTotalInsurePrem += totalPrem;
                            // 家属企业缴费
                            eflexPeopleInsureInfo.setGrpPrem(familyGrpPrem);
                            // 家属个人缴费
                            eflexPeopleInsureInfo.setSelfPrem(familySelfPrem);
                            peopleInsureInfoList.add(eflexPeopleInsureInfo);
                        }
                    }

                    /**
                     * 健康告知信息
                     */
                    Boolean isHealthNotice = eflexHealthyInform(token, personId, peopleInsureInfo.getRelation());
                    if (isHealthNotice) {
                        HealthNoticeInfo healthNoticeInfo = new HealthNoticeInfo(personId, peopleInsureInfo.getName());
                        healthNoticeList.add(healthNoticeInfo);
                    }
                    /**
                     * 未成年附带被保险人投保确认函
                     */
                    ResultResp eflexJuvenilesConfirmationResult = eflexJuvenilesConfirmation(token, personId);
                    if (eflexJuvenilesConfirmationResult.getCode().equals(ResultCodeEnum.INSURERULE_EXCEPTION.getCode())) {
                        JuvenilesConfirmationInfo juvenilesConfirmationInfo = (JuvenilesConfirmationInfo) eflexJuvenilesConfirmationResult.getData();
                        juvenilesConfirmationInfoList.add(juvenilesConfirmationInfo);
                    }

                }
                // 汇总信息
                eflexConfirmInsureResp.setPeopleInsureInfoList(peopleInsureInfoList);
                eflexConfirmInsureResp.setHealthNoticeList(healthNoticeList);
                eflexConfirmInsureResp.setJuvenilesConfirmationInfoList(juvenilesConfirmationInfoList);
            }

            /**
             * 投保汇总信息
             */
            // 员工福利额度（初始福利额度，非使用的福利额度）
            eflexTotalInfo.setStaffGrpPrem(staffGrpPrem);
            // 员工投保保费
            eflexTotalInfo.setStaffInsurePrem(staffInsurePrem);
            // 家属累计投保保费
            eflexTotalInfo.setFamilyTotalInsurePrem(familyTotalInsurePrem);
            // 员工自付保费
            eflexTotalInfo.setStaffSelfPrem(staffSelfPrem);
            // 家属累计自付保费
            eflexTotalInfo.setFamilyTotalSelfPrem(familyTotalSelfPrem);
            // 个人实际支付保费
            eflexTotalInfo.setTotalSelfPrem(CommonUtil.add(staffSelfPrem, familyTotalSelfPrem));
            eflexConfirmInsureResp.setTotalInfo(eflexTotalInfo);
            // 校验是否需要显示员工付款信息
            if (fcEnsureConfig != null) {
                if ("3".equals(fcEnsureConfig.getConfigValue())) {
                    // 获取未投保家属默认档次总保费
                    Double prem = insureService.getNoInsuredFamilyPrem(ensureCode, perNo);
                    if (familyTotalSelfPrem + staffSelfPrem > 0) {
                        eflexConfirmInsureResp.setIsCheck("1");
                        eflexConfirmInsureResp.setPerInfo(fcPerInfo);
                    }
                    Double familyGrpPrem = fcPerRegistDay.getFamilyGrpPrem();
                    familyGrpPrem = familyGrpPrem == null ? 0.0 : familyGrpPrem;
                    if (prem > (familyGrpPrem - familyTotalSelfPrem)) {
                        eflexConfirmInsureResp.setIsCheck("1");
                        eflexConfirmInsureResp.setPerInfo(fcPerInfo);
                    }
                } else {
                    eflexConfirmInsureResp.setIsCheck("0");
                    eflexConfirmInsureResp.setPerInfo(fcPerInfo);
                }
            } else {
                eflexConfirmInsureResp.setIsCheck("0");
                eflexConfirmInsureResp.setPerInfo(fcPerInfo);
            }
            if (familyTotalSelfPrem + staffSelfPrem > 0) {
                // 需要支付
                eflexConfirmInsureResp.setIsSelfPay("1");
            } else {
                // 不需要支付
                eflexConfirmInsureResp.setIsSelfPay("0");
            }
        } catch (Exception e) {
            log.info("计算缴费明细失败：", e);
            return null;
        }
        return eflexConfirmInsureResp;
    }

    /**
     * 保费试算接口
     *
     * @param premTrailReq
     */
    public Object premTrail(PremTrailReq premTrailReq) {
        // 对象转map
        Map<String, Object> map = new HashMap<>();
        map.put("ensureCode", premTrailReq.getEnsureCode());
        map.put("AmountGrageCode", premTrailReq.getAmountGrageCode());
        map.put("RiskCode", premTrailReq.getRiskCode());
        map.put("BirthDay", premTrailReq.getBirthDay());
        map.put("Sex", premTrailReq.getSex());
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(premTrailReq.getEnsureCode());
        map.put("CvaliDate", fcEnsure.getCvaliDate());
        map.put("InsureCount", fcEnsure.getInsuredNumber());
        map.put("CompensationRatio", premTrailReq.getCompensationRatio());
        map.put("Deductible", premTrailReq.getDeductible());
        map.put("JoinMedProtect", premTrailReq.getJoinMedProtect());
        map.put("OccupationType", premTrailReq.getOccupationType());
        map.put("optDutyCode", ObjectUtils.isEmpty(premTrailReq.getOptDutyCode()) ? null : Arrays.asList(premTrailReq.getOptDutyCode()));
        map.put("isAmount", premTrailReq.getIsAmount());


        // 保费试算
        Map<String, Object> objectMap = premTrailService.premTrail(map);
        if (objectMap.get("code").equals(ResultCodeEnum.CODE_EXCEPTION.getCode())) {
            throw new SystemException(objectMap.get("message").toString());
        } else {
            return objectMap.get("Prem");
        }

    }

    /**
     * <AUTHOR>
     * @description删除订单及子订单
     * @date 16:25 16:25
     * @modified
     */
    public String deleteConfirmInfo(List<FCOrder> fcOrderList) {
        for (int i = 0; i < fcOrderList.size(); i++) {
            //删除子订单详情表
            fcOrderItemDetailMapper.deleteByOrderNo(fcOrderList.get(i).getOrderNo());
            // 删除子订单个人健康告知信息表
            fcPerImpartResultMapper.deleteByOrderNo(fcOrderList.get(i).getOrderNo());
            //删除子订单表
            fcOrderItemMapper.deleteByOrderNo(fcOrderList.get(i).getOrderNo());
            //删除被保人表
            fcOrderInsuredMapper.deleteByOrderNo(fcOrderList.get(i).getOrderNo());
            //删除个人被保人表
            fcPerAppntMapper.deleteByPrimaryKey(fcOrderList.get(i).getPerAppNo());
            //删除订单表
            fcOrderMapper.deleteByPrimaryKey(fcOrderList.get(i).getOrderNo());
        }
        return "";
    }

    /**
     * 投保确认页面信息加载
     *
     * @param token
     * @param insuredConfirmPageReq
     * @return
     */
    @Transactional
    public ConfirmInsureResp insuredConfirmPage(String token, InsuredConfirmPageReq insuredConfirmPageReq) {
        try {
            /**
             * 定义返回结果
             */
            ConfirmInsureResp confirmInsureResp = new ConfirmInsureResp();
            /**
             * 获取基础信息
             */
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            // 获取员工信息
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());

            List<PeopleInsurePlanInfo> peopleInsurePlanInfos = insuredConfirmPageReq.getPeopleInsurePlanInfos();

            PeopleInsurePlanInfo peopleInsurePlanInfo = peopleInsurePlanInfos.get(0);
            String perNo = peopleInsurePlanInfo.getPerNo();
            String grpNo = peopleInsurePlanInfo.getGrpNo();


            // 查询之前的付款信息 根据福利编号 和perNo查询
            FcBatchPayBankInfo fcBatchPayBankInfo = fcBatchPayBankInfoMapper.selectByPrimaryKey(ensureCode, perNo);
            if (!ObjectUtils.isEmpty(fcBatchPayBankInfo)) {
                fcPerInfo.setOpenBank(fcBatchPayBankInfo.getPayBankCode());
                fcPerInfo.setOpenAccount(fcBatchPayBankInfo.getBankAccount());
            }
            // 福利编码
            ensureCode = peopleInsurePlanInfo.getEnsureCode();
            // 更新token信息
            globalInput.setCustomNo(perNo);
            globalInput.setGrpNo(grpNo);
            globalInput.setEnsureCode(ensureCode);
            Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
            redisUtil.put(token, JSON.toJSONString(globalInput), loginValidity);
            Map<String, Object> param = new HashMap<>();
            param.put("ensureCode", ensureCode);
            param.put("perNo", perNo);


            /**
             * 封装弹性计划投保确认信息
             */
            // 获取支付方式
            Map<String, Object> ensureMap = new HashMap<>();
            ensureMap.put("ensureCode", ensureCode);
            ensureMap.put("configNo", "008");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(ensureMap);
            String payType = "";
            if (fcEnsureConfig != null) {
                payType = fcEnsureConfig.getConfigValue();
            }

            /**
             * 投保确认页面信息加载
             */
            //健康告知信息表
            List<HealthNoticeInfo> healthNoticeInfoList = new ArrayList<>();
            //未成年人投保确认信息
            List<JuvenilesConfirmationInfo> juvenilesConfirmationInfoList = new ArrayList<>();
            List<PeopleInsureInfo> peopleInsureInfoList = new ArrayList<>();
            // 获取监护人信息
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
            // 获取福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            String ensureType = fcEnsure.getEnsureType();
            // 获取员工的personId
            String staffPersonId = "";
            if (fcStaffFamilyRela != null) {
                staffPersonId = fcStaffFamilyRela.getPersonID();
            }
            // 获取员工注册期表数据
            Map params = new HashMap<>();
            params.put("ensureCode", ensureCode);
            params.put("perNo", perNo);
            FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(params);

            /*** 统计投保信息 ***/
            EflexTotalInfo eflexTotalInfo = new EflexTotalInfo();
            // 家属福利额度(学生：学生的福利额度)
            double familyGrpTotalPrem = 0.0;
            if (ensureType == null || ensureType.equals("")) {
                if (fcPerRegistDay.getFamilyGrpPrem() != null) {
                    familyGrpTotalPrem = fcPerRegistDay.getFamilyGrpPrem();
                }
            } else {
                // 企事业单位投保和场地险投保
                if (ensureType.equals("0") || ensureType.equals("2")) {
                    if (fcPerRegistDay.getFamilyGrpPrem() != null) {
                        familyGrpTotalPrem = fcPerRegistDay.getFamilyGrpPrem();
                    }
                    // 学生投保
                } else if (ensureType.equals("1")) {
                    if (fcPerRegistDay.getStudentGrpPrem() != null) {
                        familyGrpTotalPrem = fcPerRegistDay.getStudentGrpPrem();
                    }
                }
            }
            eflexTotalInfo.setFamilyGrpPrem(familyGrpTotalPrem);
            // 员工自付保费
            double staffSelfPrem = 0.00;
            // 家属自付保费
            double familyTotalSelfPrem = 0.0;
            // 员工投保保费
            double staffInsurePrem = 0.0;
            // 家属累计投保保费
            double familyTotalInsurePrem = 0.0;
            // 员工福利额度（初始福利额度，非使用的福利额度）
            double staffGrpPrem = 0.0;


            /**
             * 获取投保计划
             */
            for (PeopleInsurePlanInfo PlanInfo : peopleInsurePlanInfos) {

                String planCode = PlanInfo.getPlanCode();
                String personId = PlanInfo.getPersonId();


                /**************************** 当前员工不存在当前的家属则同步信息 ************************/
                Map<String, Object> SyncMap = insureService.SynchronizationFamilyinfo(token, PlanInfo.getPersonId());
                if (!SyncMap.get("success").equals(true)) {
                    throw new SystemException(JSONObject.toJSONString(SyncMap.get("errmsg")));
                }

                Map<String, String> param1 = new HashMap<>();
                param1.put("ensureCode", ensureCode);
                param1.put("planCode", planCode);
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param1);

                // 查询人员信息
                SelectFcPersonInfoByPerNoAndPersonIdReq selectFcPersonInfoByPerNoAndPersonIdReq = new SelectFcPersonInfoByPerNoAndPersonIdReq(perNo, personId);
                PeopleInsureInfo peopleInsureInfo = fcPersonMapper.selectFcPersonInfoByPernoAndPersonId(selectFcPersonInfoByPerNoAndPersonIdReq);
                peopleInsureInfo.setPlanCode(fcEnsurePlan.getPlanCode());
                peopleInsureInfo.setPlanName(fcEnsurePlan.getPlanName());
                /******** 判断是否为员工 *******************/
                if (!StringUtils.isEmpty(staffPersonId) && fcEnsure.getEnsureType().matches("^(0|2)")) {
                    if (staffPersonId.equals(personId)) {
                        /*** 员工 ***/
                        // 1、员工福利额度
                        if (fcPerRegistDay.getStaffGrpPrem() != null) {
                            staffGrpPrem = fcPerRegistDay.getStaffGrpPrem();
                        }
                        // 2、员工投保保费
                        staffInsurePrem = fcEnsurePlanMapper.selectPlanPrem(PlanInfo.getEnsureCode(), planCode);
                        // 3、员工自付保费
                        if (staffInsurePrem > staffGrpPrem) {
                            staffSelfPrem = CommonUtil.sub(staffInsurePrem, staffGrpPrem);
                        } else {
                            staffGrpPrem = staffInsurePrem;
                        }
                        // 封装员工数据
                        peopleInsureInfo.setTotalPrem(staffInsurePrem);
                        peopleInsureInfo.setGrpPrem(staffGrpPrem);
                        peopleInsureInfo.setSelfPrem(staffSelfPrem);
                    } else {
                        /****** 家属 ****/
                        // 1、家属企业福利额度（分摊到每个家属的福利额度）
                        double familyGrpPrem = 0.0;
                        // 2、家属投保保费
                        double familyInsurePrem = fcEnsurePlanMapper.selectPlanPrem(PlanInfo.getEnsureCode(), planCode);
                        // 3、家属自付保费
                        double familySelfPrem = 0.00;
                        if (familyInsurePrem > familyGrpTotalPrem) {
                            familyGrpPrem = familyGrpTotalPrem;
                            familySelfPrem = CommonUtil.sub(familyInsurePrem, familyGrpTotalPrem);
                            familyGrpTotalPrem = 0.0;
                        } else if (familyInsurePrem <= familyGrpTotalPrem) {
                            familyGrpPrem = familyInsurePrem;
                            familyGrpTotalPrem = CommonUtil.sub(familyGrpTotalPrem, familyInsurePrem);
                        }
                        // 统计家属累计自付保费
                        familyTotalSelfPrem = CommonUtil.add(familyTotalSelfPrem, familySelfPrem);
                        // 统计家属累计投保保费
                        familyTotalInsurePrem = CommonUtil.add(familyTotalInsurePrem, familyInsurePrem);
                        // 封装单个家属数据
                        peopleInsureInfo.setTotalPrem(familyInsurePrem);
                        peopleInsureInfo.setGrpPrem(familyGrpPrem);
                        peopleInsureInfo.setSelfPrem(familySelfPrem);
                    }
                } else {
                    /*** 学生投保 ***/
                    // 1、学生福利额度
                    double familyGrpPrem = 0.0;
                    // 2、学生投保保费
                    double familyInsurePrem = fcEnsurePlanMapper.selectPlanPrem(PlanInfo.getEnsureCode(), planCode);
                    // 3、学生自付保费
                    double familySelfPrem = 0.00;
                    if (familyInsurePrem > familyGrpTotalPrem) {
                        familyGrpPrem = familyGrpTotalPrem;
                        familySelfPrem = CommonUtil.sub(familyInsurePrem, familyGrpTotalPrem);
                        familyGrpTotalPrem = 0.0;
                    } else if (familyInsurePrem <= familyGrpTotalPrem) {
                        familyGrpPrem = familyInsurePrem;
                        familyGrpTotalPrem = CommonUtil.sub(familyGrpTotalPrem, familyInsurePrem);
                    }
                    //统计家属（学生）累计自付保费
                    familyTotalSelfPrem = CommonUtil.add(familyTotalSelfPrem, familySelfPrem);
                    // 统计家属累计投保保费
                    familyTotalInsurePrem = CommonUtil.add(familyTotalInsurePrem, familyInsurePrem);
                    // 封装单个家属数据
                    peopleInsureInfo.setPersonId(staffPersonId);
                    peopleInsureInfo.setTotalPrem(familyInsurePrem);
                    peopleInsureInfo.setGrpPrem(familyGrpPrem);
                    peopleInsureInfo.setSelfPrem(familySelfPrem);
                }

                /**
                 * 封装责任信息
                 */
                peopleInsureInfo.setRiskDutyInfoList(getRiskDutyInfo(ensureCode, planCode));
                peopleInsureInfoList.add(peopleInsureInfo);
                if (fcEnsure.getPlanType().equals(PlanTypeEnum.IMMOBILIZATIONPLAN.getCode())) {
                    try {
                        // 记录需要健康告知的人员信息
                        long age = Integer.valueOf(DateTimeUtil.getCurrentAge(peopleInsureInfo.getBirthDate(), fcEnsure.getCvaliDate()));
                        if (age > 0) {
                            age = age * 365;
                        } else {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            Date fDate = sdf.parse(peopleInsureInfo.getBirthDate());
                            Date oDate = sdf.parse(sdf.format(new Date()));
                            age = (oDate.getTime() - fDate.getTime()) / (1000 * 3600 * 24);
                        }
                        if (insureService.ageValidate(age, planCode, ensureCode)) {
                            healthNoticeInfoList.add(new HealthNoticeInfo(peopleInsureInfo.getPersonId(), peopleInsureInfo.getName()));
                        }
                        // 记录需要投保确认函的未成年人信息
                        long age1 = Integer.valueOf(DateTimeUtil.getCurrentAge(peopleInsureInfo.getBirthDate(), fcEnsure.getCvaliDate()));
                        if (age1 < 18) {
                            JuvenilesConfirmationInfo juvenilesConfirmationInfo = new JuvenilesConfirmationInfo();
                            // 投保确认书
                            FCPerInfo staffInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
                            juvenilesConfirmationInfo.setStaffName(staffInfo.getName());
                            juvenilesConfirmationInfo.setStaffIDNo(staffInfo.getIDNo());
                            juvenilesConfirmationInfo.setInsuredName(peopleInsureInfo.getName());
                            Map<String, String> map = new HashMap<String, String>();
                            map.put("perNo", globalInput.getCustomNo());
                            map.put("personID", peopleInsureInfo.getPersonId());
                            // 查询此人是否在本员工下是否有此家属
                            FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(map);
                            if (rela == null) {
                                // 查询家属对应的关系（因为这里有一个大前提，就是相同的员工，所以无需加上相同员工下的条件）
                                rela = fcStaffFamilyRelaMapper.selectPersonIdInfo(peopleInsureInfo.getPersonId());
                            }
                            if (rela != null) {
                                if ("3".equals(rela.getRelation())) {
                                    // 3为子女。 员工是被保人的父母。这里需要拆分成父亲或母亲
                                    if ("0".equals(staffInfo.getSex())) {
                                        juvenilesConfirmationInfo.setRelation("父亲");
                                    } else {
                                        juvenilesConfirmationInfo.setRelation("母亲");
                                    }
                                } else {
                                    String relationName = fdCodeMapper.selectNameByCode("Relation", rela.getRelation());
                                    juvenilesConfirmationInfo.setRelation(relationName);
                                }
                            } else {
                                juvenilesConfirmationInfo.setRelation("");
                            }
                            Map<String, Object> params1 = new HashMap<>();
                            params1.put("planCode", planCode);
                            params1.put("ensureCode", ensureCode);
                            List<FCPlanRiskDuty> dutyList = fcPlanRiskDutyMapper.selectDutyList(params1);
                            // 在这里判断固定计划中是否含有身故保险
                            List<String> dutyCodeList = Arrays.asList("GD0017", "GD0037", "GD0050", "GD0051", "GD0052", "GD0054");
                            int count = 0;
                            for (FCPlanRiskDuty dutyInfo : dutyList) {
                                if (dutyCodeList.contains(dutyInfo.getDutyCode())) {
                                    count++;
                                }
                            }
                            if (0 == count) {
                                log.info("被保人：" + peopleInsureInfo.getName() + "，投保计划：" + peopleInsureInfo.getPlanName() + "，该计划中不包含身故险种，故不弹出确认函!");
                            } else {
                                double deathAmnt = 0.00;
                                for (FCPlanRiskDuty dutyInfo : dutyList) {
                                    if ("GD0017".equals(dutyInfo.getDutyCode())) {
                                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                                    } else if ("GD0037".equals(dutyInfo.getDutyCode())) {
                                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                                    }
                                }
                                double Amnt15070 = 0.00;
                                for (FCPlanRiskDuty dutyInfo : dutyList) {
                                    if ("GD0050".equals(dutyInfo.getDutyCode())) {
                                        Amnt15070 = dutyInfo.getAmnt();
                                    }
                                    if ("GD0051".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                                        Amnt15070 = dutyInfo.getAmnt();
                                    }
                                    if ("GD0052".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                                        Amnt15070 = dutyInfo.getAmnt();
                                    }
                                    if ("GD0054".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                                        Amnt15070 = dutyInfo.getAmnt();
                                    }
                                }
                                deathAmnt = CommonUtil.add(deathAmnt, Amnt15070);
                                if (0.00 == deathAmnt) {
                                    log.info("被保人：" + peopleInsureInfo.getName() + "，投保计划：" + peopleInsureInfo.getPlanName() + "，本次投保身故保险金额为0，故不弹出确认函!");
                                } else {
                                    String riskAmnt = insureService.getRiskAmnt(peopleInsureInfo.getPersonId());
                                    if (riskAmnt == null || "".equals(riskAmnt)) {
                                        log.info("调用核心接口查询风险保额失败，请联系管理员！");
                                        throw new SystemException("调用核心接口查询风险保额失败！");

                                    }
                                    juvenilesConfirmationInfo.setPersonId(peopleInsureInfo.getPersonId());
                                    juvenilesConfirmationInfo.setPlanCode(planCode);
                                    juvenilesConfirmationInfo.setDeathAmnt(deathAmnt);
                                    Double riskAmntTotal = CommonUtil.add(Double.valueOf(riskAmnt), deathAmnt);
                                    juvenilesConfirmationInfo.setDeathAmntCount(riskAmntTotal);
                                    juvenilesConfirmationInfo.setYear(DateTimeUtil.getCurrentYear());
                                    juvenilesConfirmationInfo.setMonth(DateTimeUtil.getCurrentMonth());
                                    juvenilesConfirmationInfo.setDay(DateTimeUtil.getCurrentDay());
                                    juvenilesConfirmationInfoList.add(juvenilesConfirmationInfo);
                                }

                            }
                        }

                    } catch (Exception e) {
                        throw new SystemException("程序有误！");
                    }

                }

            }

            /**
             * 封装投保汇总信息
             */
            // 员工福利额度（初始福利额度，非使用的福利额度）
            eflexTotalInfo.setStaffGrpPrem(staffGrpPrem);
            // 员工投保保费
            eflexTotalInfo.setStaffInsurePrem(staffInsurePrem);
            // 家属累计投保保费
            eflexTotalInfo.setFamilyTotalInsurePrem(familyTotalInsurePrem);
            // 员工自付保费
            eflexTotalInfo.setStaffSelfPrem(staffSelfPrem);
            // 家属累计自付保费
            eflexTotalInfo.setFamilyTotalSelfPrem(familyTotalSelfPrem);
            // 个人实际支付保费
            eflexTotalInfo.setTotalSelfPrem(CommonUtil.add(staffSelfPrem, familyTotalSelfPrem));
            confirmInsureResp.setTotalInfo(eflexTotalInfo);
            // 校验是否需要显示员工付款信息
            if (fcEnsureConfig != null && payType.equals(PaymentTypeEnum.PERSONALDEDUCTION.getCode()) && eflexTotalInfo.getTotalSelfPrem() > 0) {
                confirmInsureResp.setIsCheck("1");
                confirmInsureResp.setPerInfo(fcPerInfo);
            } else {
                confirmInsureResp.setIsCheck("0");
                confirmInsureResp.setPerInfo(fcPerInfo);
            }
            if (eflexTotalInfo.getTotalSelfPrem() > 0) {
                // 需要支付
                confirmInsureResp.setIsSelfPay("1");
            } else {
                // 不需要支付
                confirmInsureResp.setIsSelfPay("0");
            }

            /**
             * 封装已投保的人员信息
             */
            confirmInsureResp.setPeopleInsureInfoList(peopleInsureInfoList);

            /**
             * 封装需要健康告知的人员信息
             */
            confirmInsureResp.setHealthNoticeList(healthNoticeInfoList);

            /**
             * 封装需要未成年人确认函的人员信息
             */
            confirmInsureResp.setJuvenilesConfirmationInfoList(juvenilesConfirmationInfoList);

            return confirmInsureResp;
        } catch (Exception e) {
            log.error("投保确认页面信息加载失败", e);
            throw new SystemException("投保确认页面信息加载失败，" + e.getMessage());
        }
    }

    /**
     * 投保确认页面信息加载
     *
     * @param token
     * @param insuredConfirmPageReq
     * @return
     */
    @Transactional
    public ConfirmInsureResp insuredConfirmPage2(String token, InsuredConfirmPageReq insuredConfirmPageReq) {
        try {
            /**
             * 定义返回结果
             */
            ConfirmInsureResp confirmInsureResp = new ConfirmInsureResp();
            /**
             * 获取基础信息
             */
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            //String perNo = globalInput.getCustomNo();
            // 获取员工信息
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
            // 获取订单号
            String orderNo = insuredConfirmPageReq.getOrderNo();
            if (StringUtils.isEmpty(orderNo)) {
                throw new SystemException("订单号不能为空！");
            }
            confirmInsureResp.setOrderNo(orderNo);
            // 查询之前的付款信息
            FcBatchPayBankInfo fcBatchPayBankInfo = fcBatchPayBankInfoMapper.selectFcBatchPayBankInfoByOrderNo(orderNo);
            if (!ObjectUtils.isEmpty(fcBatchPayBankInfo)) {
                fcPerInfo.setOpenBank(fcBatchPayBankInfo.getPayBankCode());
                fcPerInfo.setOpenAccount(fcBatchPayBankInfo.getBankAccount());
            }
            // 福利编码
            GrpOrderInfo grpOrderInfo = fcOrderMapper.selectGrpOrderInfo(orderNo);
            ensureCode = grpOrderInfo.getEnsureCode();
            // 更新token信息
            globalInput.setCustomNo(grpOrderInfo.getPerNo());
            globalInput.setGrpNo(grpOrderInfo.getGrpNo());
            globalInput.setEnsureCode(ensureCode);
            Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
            redisUtil.put(token, JSON.toJSONString(globalInput), loginValidity);
            Map<String, Object> param = new HashMap<>();
            param.put("ensureCode", ensureCode);
            param.put("perNo", grpOrderInfo.getPerNo());
            List<FPInsurePlan> fpInsurePlans = fpInsurePlanMapper.selectFlansInfo(param);
            if (fpInsurePlans.size() < 1) {
                log.info("系统异常，没有被保人");
                throw new SystemException("系统异常，没有被保人");
            }

            /**
             * 封装弹性计划投保确认信息
             */
            // 获取支付方式
            Map<String, Object> ensureMap = new HashMap<>();
            ensureMap.put("ensureCode", ensureCode);
            ensureMap.put("configNo", "008");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(ensureMap);
            String payType = "";
            if (fcEnsureConfig != null) {
                payType = fcEnsureConfig.getConfigValue();
            }

            /**
             * 投保确认页面信息加载
             */
            //健康告知信息表
            List<HealthNoticeInfo> healthNoticeInfoList = new ArrayList<>();
            //未成年人投保确认信息
            List<JuvenilesConfirmationInfo> juvenilesConfirmationInfoList = new ArrayList<>();
            List<PeopleInsureInfo> peopleInsureInfoList = new ArrayList<>();
            // 获取监护人信息
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPerNo(grpOrderInfo.getPerNo());
            // 获取福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            String ensureType = fcEnsure.getEnsureType();
            // 获取员工的personId
            String staffPersonId = "";
            if (fcStaffFamilyRela != null) {
                staffPersonId = fcStaffFamilyRela.getPersonID();
            }
            // 获取员工注册期表数据
            Map params = new HashMap<>();
            params.put("ensureCode", ensureCode);
            params.put("perNo", grpOrderInfo.getPerNo());
            FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(params);

            /*** 统计投保信息 ***/
            EflexTotalInfo eflexTotalInfo = new EflexTotalInfo();
            // 家属福利额度(学生：学生的福利额度)
            double familyGrpTotalPrem = 0.0;
            if (ensureType == null || ensureType.equals("")) {
                if (fcPerRegistDay.getFamilyGrpPrem() != null) {
                    familyGrpTotalPrem = fcPerRegistDay.getFamilyGrpPrem();
                }
            } else {
                // 企事业单位投保和场地险投保
                if (ensureType.equals("0") || ensureType.equals("2")) {
                    if (fcPerRegistDay.getFamilyGrpPrem() != null) {
                        familyGrpTotalPrem = fcPerRegistDay.getFamilyGrpPrem();
                    }
                    // 学生投保
                } else if (ensureType.equals("1")) {
                    if (fcPerRegistDay.getStudentGrpPrem() != null) {
                        familyGrpTotalPrem = fcPerRegistDay.getStudentGrpPrem();
                    }
                }
            }
            eflexTotalInfo.setFamilyGrpPrem(familyGrpTotalPrem);
            // 员工自付保费
            double staffSelfPrem = 0.00;
            // 家属自付保费
            double familyTotalSelfPrem = 0.0;
            // 员工投保保费
            double staffInsurePrem = 0.0;
            // 家属累计投保保费
            double familyTotalInsurePrem = 0.0;
            // 员工福利额度（初始福利额度，非使用的福利额度）
            double staffGrpPrem = 0.0;


            /**
             * 获取投保计划
             */
            List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectFPInsurePlans(params);
            for (FPInsurePlan fpInsurePlan : fpInsurePlanList) {
                Map<String, String> param1 = new HashMap<>();
                param1.put("ensureCode", ensureCode);
                param1.put("planCode", fpInsurePlan.getPlanCode());
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param1);

                // 查询人员信息
                SelectFcPersonInfoByPerNoAndPersonIdReq selectFcPersonInfoByPerNoAndPersonIdReq = new SelectFcPersonInfoByPerNoAndPersonIdReq(grpOrderInfo.getPerNo(), fpInsurePlan.getPersonId());
                PeopleInsureInfo peopleInsureInfo = fcPersonMapper.selectFcPersonInfoByPernoAndPersonId(selectFcPersonInfoByPerNoAndPersonIdReq);
                peopleInsureInfo.setPlanCode(fcEnsurePlan.getPlanCode());
                peopleInsureInfo.setPlanName(fcEnsurePlan.getPlanName());
                /******** 判断是否为员工 *******************/
                if (!StringUtils.isEmpty(staffPersonId) && fcEnsure.getEnsureType().matches("^(0|2)")) {
                    if (staffPersonId.equals(fpInsurePlan.getPersonId())) {
                        /*** 员工 ***/
                        // 1、员工福利额度
                        if (fcPerRegistDay.getStaffGrpPrem() != null) {
                            staffGrpPrem = fcPerRegistDay.getStaffGrpPrem();
                        }
                        // 2、员工投保保费
                        staffInsurePrem = fcEnsurePlanMapper.selectPlanPrem(fpInsurePlan.getEnsureCode(), fpInsurePlan.getPlanCode());
                        // 3、员工自付保费
                        if (staffInsurePrem > staffGrpPrem) {
                            staffSelfPrem = CommonUtil.sub(staffInsurePrem, staffGrpPrem);
                        } else {
                            staffGrpPrem = staffInsurePrem;
                        }
                        // 封装员工数据
                        peopleInsureInfo.setTotalPrem(staffInsurePrem);
                        peopleInsureInfo.setGrpPrem(staffGrpPrem);
                        peopleInsureInfo.setSelfPrem(staffSelfPrem);
                    } else {
                        /****** 家属 ****/
                        // 1、家属企业福利额度（分摊到每个家属的福利额度）
                        double familyGrpPrem = 0.0;
                        // 2、家属投保保费
                        double familyInsurePrem = fcEnsurePlanMapper.selectPlanPrem(fpInsurePlan.getEnsureCode(), fpInsurePlan.getPlanCode());
                        // 3、家属自付保费
                        double familySelfPrem = 0.00;
                        if (familyInsurePrem > familyGrpTotalPrem) {
                            familyGrpPrem = familyGrpTotalPrem;
                            familySelfPrem = CommonUtil.sub(familyInsurePrem, familyGrpTotalPrem);
                            familyGrpTotalPrem = 0.0;
                        } else if (familyInsurePrem <= familyGrpTotalPrem) {
                            familyGrpPrem = familyInsurePrem;
                            familyGrpTotalPrem = CommonUtil.sub(familyGrpTotalPrem, familyInsurePrem);
                        }
                        // 统计家属累计自付保费
                        familyTotalSelfPrem = CommonUtil.add(familyTotalSelfPrem, familySelfPrem);
                        // 统计家属累计投保保费
                        familyTotalInsurePrem = CommonUtil.add(familyTotalInsurePrem, familyInsurePrem);
                        // 封装单个家属数据
                        peopleInsureInfo.setTotalPrem(familyInsurePrem);
                        peopleInsureInfo.setGrpPrem(familyGrpPrem);
                        peopleInsureInfo.setSelfPrem(familySelfPrem);
                    }
                } else {
                    /*** 学生投保 ***/
                    // 1、学生福利额度
                    double familyGrpPrem = 0.0;
                    // 2、学生投保保费
                    double familyInsurePrem = fcEnsurePlanMapper.selectPlanPrem(fpInsurePlan.getEnsureCode(), fpInsurePlan.getPlanCode());
                    // 3、学生自付保费
                    double familySelfPrem = 0.00;
                    if (familyInsurePrem > familyGrpTotalPrem) {
                        familyGrpPrem = familyGrpTotalPrem;
                        familySelfPrem = CommonUtil.sub(familyInsurePrem, familyGrpTotalPrem);
                        familyGrpTotalPrem = 0.0;
                    } else if (familyInsurePrem <= familyGrpTotalPrem) {
                        familyGrpPrem = familyInsurePrem;
                        familyGrpTotalPrem = CommonUtil.sub(familyGrpTotalPrem, familyInsurePrem);
                    }
                    //统计家属（学生）累计自付保费
                    familyTotalSelfPrem = CommonUtil.add(familyTotalSelfPrem, familySelfPrem);
                    // 统计家属累计投保保费
                    familyTotalInsurePrem = CommonUtil.add(familyTotalInsurePrem, familyInsurePrem);
                    // 封装单个家属数据
                    peopleInsureInfo.setPersonId(staffPersonId);
                    peopleInsureInfo.setTotalPrem(familyInsurePrem);
                    peopleInsureInfo.setGrpPrem(familyGrpPrem);
                    peopleInsureInfo.setSelfPrem(familySelfPrem);
                }

                /**
                 * 封装责任信息
                 */
                peopleInsureInfo.setRiskDutyInfoList(getRiskDutyInfo(ensureCode, fpInsurePlan.getPlanCode()));
                peopleInsureInfoList.add(peopleInsureInfo);
                if (fcEnsure.getPlanType().equals(PlanTypeEnum.IMMOBILIZATIONPLAN.getCode())) {
                    try {
                        // 记录需要健康告知的人员信息
                        long age = Integer.valueOf(DateTimeUtil.getCurrentAge(peopleInsureInfo.getBirthDate(), fcEnsure.getCvaliDate()));
                        if (age > 0) {
                            age = age * 365;
                        } else {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            Date fDate = sdf.parse(peopleInsureInfo.getBirthDate());
                            Date oDate = sdf.parse(sdf.format(new Date()));
                            age = (oDate.getTime() - fDate.getTime()) / (1000 * 3600 * 24);
                        }
                        if (insureService.ageValidate(age, fpInsurePlan.getPlanCode(), ensureCode)) {
                            healthNoticeInfoList.add(new HealthNoticeInfo(peopleInsureInfo.getPersonId(), peopleInsureInfo.getName()));
                        }
                        // 记录需要投保确认函的未成年人信息
                        long age1 = Integer.valueOf(DateTimeUtil.getCurrentAge(peopleInsureInfo.getBirthDate(), fcEnsure.getCvaliDate()));
                        if (age1 < 18) {
                            JuvenilesConfirmationInfo juvenilesConfirmationInfo = new JuvenilesConfirmationInfo();
                            // 投保确认书
                            FCPerInfo staffInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
                            juvenilesConfirmationInfo.setStaffName(staffInfo.getName());
                            juvenilesConfirmationInfo.setStaffIDNo(staffInfo.getIDNo());
                            juvenilesConfirmationInfo.setInsuredName(peopleInsureInfo.getName());
                            Map<String, String> map = new HashMap<String, String>();
                            map.put("perNo", globalInput.getCustomNo());
                            map.put("personID", peopleInsureInfo.getPersonId());
                            // 查询此人是否在本员工下是否有此家属
                            FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(map);
                            if (rela == null) {
                                // 查询家属对应的关系（因为这里有一个大前提，就是相同的员工，所以无需加上相同员工下的条件）
                                rela = fcStaffFamilyRelaMapper.selectPersonIdInfo(peopleInsureInfo.getPersonId());
                            }
                            if (rela != null) {
                                if ("3".equals(rela.getRelation())) {
                                    // 3为子女。 员工是被保人的父母。这里需要拆分成父亲或母亲
                                    if ("0".equals(staffInfo.getSex())) {
                                        juvenilesConfirmationInfo.setRelation("父亲");
                                    } else {
                                        juvenilesConfirmationInfo.setRelation("母亲");
                                    }
                                } else {
                                    String relationName = fdCodeMapper.selectNameByCode("Relation", rela.getRelation());
                                    juvenilesConfirmationInfo.setRelation(relationName);
                                }
                            } else {
                                juvenilesConfirmationInfo.setRelation("");
                            }
                            Map<String, Object> params1 = new HashMap<>();
                            params1.put("planCode", fpInsurePlan.getPlanCode());
                            params1.put("ensureCode", ensureCode);
                            List<FCPlanRiskDuty> dutyList = fcPlanRiskDutyMapper.selectDutyList(params1);
                            // 在这里判断固定计划中是否含有身故保险
                            List<String> dutyCodeList = Arrays.asList("GD0017", "GD0037", "GD0050", "GD0051", "GD0052", "GD0054");
                            int count = 0;
                            for (FCPlanRiskDuty dutyInfo : dutyList) {
                                if (dutyCodeList.contains(dutyInfo.getDutyCode())) {
                                    count++;
                                }
                            }
                            if (0 == count) {
                                log.info("被保人：" + peopleInsureInfo.getName() + "，投保计划：" + peopleInsureInfo.getPlanName() + "，该计划中不包含身故险种，故不弹出确认函!");
                            } else {
                                double deathAmnt = 0.00;
                                for (FCPlanRiskDuty dutyInfo : dutyList) {
                                    if ("GD0017".equals(dutyInfo.getDutyCode())) {
                                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                                    } else if ("GD0037".equals(dutyInfo.getDutyCode())) {
                                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                                    }
                                }
                                double Amnt15070 = 0.00;
                                for (FCPlanRiskDuty dutyInfo : dutyList) {
                                    if ("GD0050".equals(dutyInfo.getDutyCode())) {
                                        Amnt15070 = dutyInfo.getAmnt();
                                    }
                                    if ("GD0051".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                                        Amnt15070 = dutyInfo.getAmnt();
                                    }
                                    if ("GD0052".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                                        Amnt15070 = dutyInfo.getAmnt();
                                    }
                                    if ("GD0054".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                                        Amnt15070 = dutyInfo.getAmnt();
                                    }
                                }
                                deathAmnt = CommonUtil.add(deathAmnt, Amnt15070);
                                if (0.00 == deathAmnt) {
                                    log.info("被保人：" + peopleInsureInfo.getName() + "，投保计划：" + peopleInsureInfo.getPlanName() + "，本次投保身故保险金额为0，故不弹出确认函!");
                                } else {
                                    String riskAmnt = insureService.getRiskAmnt(peopleInsureInfo.getPersonId());
                                    if (riskAmnt == null || "".equals(riskAmnt)) {
                                        log.info("调用核心接口查询风险保额失败，请联系管理员！");
                                        throw new SystemException("调用核心接口查询风险保额失败！");

                                    }
                                    juvenilesConfirmationInfo.setPersonId(peopleInsureInfo.getPersonId());
                                    juvenilesConfirmationInfo.setPlanCode(fpInsurePlan.getPlanCode());
                                    juvenilesConfirmationInfo.setDeathAmnt(deathAmnt);
                                    Double riskAmntTotal = CommonUtil.add(Double.valueOf(riskAmnt), deathAmnt);
                                    juvenilesConfirmationInfo.setDeathAmntCount(riskAmntTotal);
                                    juvenilesConfirmationInfo.setYear(DateTimeUtil.getCurrentYear());
                                    juvenilesConfirmationInfo.setMonth(DateTimeUtil.getCurrentMonth());
                                    juvenilesConfirmationInfo.setDay(DateTimeUtil.getCurrentDay());
                                    juvenilesConfirmationInfoList.add(juvenilesConfirmationInfo);
                                }

                            }
                        }

                    } catch (Exception e) {
                        throw new SystemException("程序有误！");
                    }

                }

            }

            /**
             * 封装投保汇总信息
             */
            // 员工福利额度（初始福利额度，非使用的福利额度）
            eflexTotalInfo.setStaffGrpPrem(staffGrpPrem);
            // 员工投保保费
            eflexTotalInfo.setStaffInsurePrem(staffInsurePrem);
            // 家属累计投保保费
            eflexTotalInfo.setFamilyTotalInsurePrem(familyTotalInsurePrem);
            // 员工自付保费
            eflexTotalInfo.setStaffSelfPrem(staffSelfPrem);
            // 家属累计自付保费
            eflexTotalInfo.setFamilyTotalSelfPrem(familyTotalSelfPrem);
            // 个人实际支付保费
            eflexTotalInfo.setTotalSelfPrem(CommonUtil.add(staffSelfPrem, familyTotalSelfPrem));
            confirmInsureResp.setTotalInfo(eflexTotalInfo);
            // 校验是否需要显示员工付款信息
            if (fcEnsureConfig != null && payType.equals(PaymentTypeEnum.PERSONALDEDUCTION.getCode()) && eflexTotalInfo.getTotalSelfPrem() > 0) {
                confirmInsureResp.setIsCheck("1");
                confirmInsureResp.setPerInfo(fcPerInfo);
            } else {
                confirmInsureResp.setIsCheck("0");
                confirmInsureResp.setPerInfo(fcPerInfo);
            }
            if (eflexTotalInfo.getTotalSelfPrem() > 0) {
                // 需要支付
                confirmInsureResp.setIsSelfPay("1");
            } else {
                // 不需要支付
                confirmInsureResp.setIsSelfPay("0");
            }

            // 封装已投保的人员信息
            confirmInsureResp.setPeopleInsureInfoList(peopleInsureInfoList);

            // 封装需要健康告知的人员信息
            confirmInsureResp.setHealthNoticeList(healthNoticeInfoList);

            // 封装需要未成年人确认函的人员信息
            confirmInsureResp.setJuvenilesConfirmationInfoList(juvenilesConfirmationInfoList);

            return confirmInsureResp;
        } catch (Exception e) {
            log.info("投保确认页面信息加载失败，", e);
            throw new SystemException("投保确认页面信息加载失败，" + e.getMessage());
        }
    }


    /**
     * 用来查询之前保存签名未生成法人团体保险个人电子投保书 查询信息
     *
     * @param insuredConfirmPageReq
     * @return
     */
    @Transactional
    public ConfirmInsureResp insuredConfirmPage2(InsuredConfirmPageReq insuredConfirmPageReq) {
        try {
            ConfirmInsureResp confirmInsureResp = new ConfirmInsureResp();
            List<PeopleInsurePlanInfo> peopleInsurePlanInfos = insuredConfirmPageReq.getPeopleInsurePlanInfos();

            PeopleInsurePlanInfo peopleInsurePlanInfo = peopleInsurePlanInfos.get(0);
            String perNo = peopleInsurePlanInfo.getPerNo();
            String grpNo = peopleInsurePlanInfo.getGrpNo();
            String ensureCode = peopleInsurePlanInfo.getEnsureCode();
            // 获取员工信息
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);


            // 查询之前的付款信息 根据福利编号 和perNo查询
            FcBatchPayBankInfo fcBatchPayBankInfo = fcBatchPayBankInfoMapper.selectByPrimaryKey(ensureCode, perNo);
            if (!ObjectUtils.isEmpty(fcBatchPayBankInfo)) {
                fcPerInfo.setOpenBank(fcBatchPayBankInfo.getPayBankCode());
                fcPerInfo.setOpenAccount(fcBatchPayBankInfo.getBankAccount());
            }

            Map<String, Object> param = new HashMap<>();
            param.put("ensureCode", ensureCode);
            param.put("perNo", perNo);


            /**
             * 封装弹性计划投保确认信息
             */
            // 获取支付方式
            Map<String, Object> ensureMap = new HashMap<>();
            ensureMap.put("ensureCode", ensureCode);
            ensureMap.put("configNo", "008");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(ensureMap);
            String payType = "";
            if (fcEnsureConfig != null) {
                payType = fcEnsureConfig.getConfigValue();
            }

            /**
             * 投保确认页面信息加载
             */
            //健康告知信息表
            List<HealthNoticeInfo> healthNoticeInfoList = new ArrayList<>();
            //未成年人投保确认信息
            List<JuvenilesConfirmationInfo> juvenilesConfirmationInfoList = new ArrayList<>();
            List<PeopleInsureInfo> peopleInsureInfoList = new ArrayList<>();
            // 获取监护人信息
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
            // 获取福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            String ensureType = fcEnsure.getEnsureType();
            // 获取员工的personId
            String staffPersonId = "";
            if (fcStaffFamilyRela != null) {
                staffPersonId = fcStaffFamilyRela.getPersonID();
            }
            // 获取员工注册期表数据
            Map params = new HashMap<>();
            params.put("ensureCode", ensureCode);
            params.put("perNo", perNo);
            FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(params);

            /*** 统计投保信息 ***/
            EflexTotalInfo eflexTotalInfo = new EflexTotalInfo();
            // 家属福利额度(学生：学生的福利额度)
            double familyGrpTotalPrem = 0.0;
            if (ensureType == null || ensureType.equals("")) {
                if (fcPerRegistDay.getFamilyGrpPrem() != null) {
                    familyGrpTotalPrem = fcPerRegistDay.getFamilyGrpPrem();
                }
            } else {
                // 企事业单位投保和场地险投保
                if (ensureType.equals("0") || ensureType.equals("2")) {
                    if (fcPerRegistDay.getFamilyGrpPrem() != null) {
                        familyGrpTotalPrem = fcPerRegistDay.getFamilyGrpPrem();
                    }
                    // 学生投保
                } else if (ensureType.equals("1")) {
                    if (fcPerRegistDay.getStudentGrpPrem() != null) {
                        familyGrpTotalPrem = fcPerRegistDay.getStudentGrpPrem();
                    }
                }
            }
            eflexTotalInfo.setFamilyGrpPrem(familyGrpTotalPrem);
            // 员工自付保费
            double staffSelfPrem = 0.00;
            // 家属自付保费
            double familyTotalSelfPrem = 0.0;
            // 员工投保保费
            double staffInsurePrem = 0.0;
            // 家属累计投保保费
            double familyTotalInsurePrem = 0.0;
            // 员工福利额度（初始福利额度，非使用的福利额度）
            double staffGrpPrem = 0.0;


            /**
             * 获取投保计划
             */
            for (PeopleInsurePlanInfo PlanInfo : peopleInsurePlanInfos) {

                String planCode = PlanInfo.getPlanCode();
                String personId = PlanInfo.getPersonId();
                /**************************** 当前员工不存在当前的家属则同步信息 ************************/
                Map<String, String> param1 = new HashMap<>();
                param1.put("ensureCode", ensureCode);
                param1.put("planCode", planCode);
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param1);

                // 查询人员信息
                SelectFcPersonInfoByPerNoAndPersonIdReq selectFcPersonInfoByPerNoAndPersonIdReq = new SelectFcPersonInfoByPerNoAndPersonIdReq(perNo, personId);
                PeopleInsureInfo peopleInsureInfo = fcPersonMapper.selectFcPersonInfoByPernoAndPersonId(selectFcPersonInfoByPerNoAndPersonIdReq);
                peopleInsureInfo.setPlanCode(fcEnsurePlan.getPlanCode());
                peopleInsureInfo.setPlanName(fcEnsurePlan.getPlanName());
                /******** 判断是否为员工 *******************/
                if (!StringUtils.isEmpty(staffPersonId) && fcEnsure.getEnsureType().matches("^(0|2)")) {
                    if (staffPersonId.equals(personId)) {
                        /*** 员工 ***/
                        // 1、员工福利额度
                        if (fcPerRegistDay.getStaffGrpPrem() != null) {
                            staffGrpPrem = fcPerRegistDay.getStaffGrpPrem();
                        }
                        // 2、员工投保保费
                        staffInsurePrem = fcEnsurePlanMapper.selectPlanPrem(PlanInfo.getEnsureCode(), planCode);
                        // 3、员工自付保费
                        if (staffInsurePrem > staffGrpPrem) {
                            staffSelfPrem = CommonUtil.sub(staffInsurePrem, staffGrpPrem);
                        } else {
                            staffGrpPrem = staffInsurePrem;
                        }
                        // 封装员工数据
                        peopleInsureInfo.setTotalPrem(staffInsurePrem);
                        peopleInsureInfo.setGrpPrem(staffGrpPrem);
                        peopleInsureInfo.setSelfPrem(staffSelfPrem);
                    } else {
                        /****** 家属 ****/
                        // 1、家属企业福利额度（分摊到每个家属的福利额度）
                        double familyGrpPrem = 0.0;
                        // 2、家属投保保费
                        double familyInsurePrem = fcEnsurePlanMapper.selectPlanPrem(PlanInfo.getEnsureCode(), planCode);
                        // 3、家属自付保费
                        double familySelfPrem = 0.00;
                        if (familyInsurePrem > familyGrpTotalPrem) {
                            familyGrpPrem = familyGrpTotalPrem;
                            familySelfPrem = CommonUtil.sub(familyInsurePrem, familyGrpTotalPrem);
                            familyGrpTotalPrem = 0.0;
                        } else if (familyInsurePrem <= familyGrpTotalPrem) {
                            familyGrpPrem = familyInsurePrem;
                            familyGrpTotalPrem = CommonUtil.sub(familyGrpTotalPrem, familyInsurePrem);
                        }
                        // 统计家属累计自付保费
                        familyTotalSelfPrem = CommonUtil.add(familyTotalSelfPrem, familySelfPrem);
                        // 统计家属累计投保保费
                        familyTotalInsurePrem = CommonUtil.add(familyTotalInsurePrem, familyInsurePrem);
                        // 封装单个家属数据
                        peopleInsureInfo.setTotalPrem(familyInsurePrem);
                        peopleInsureInfo.setGrpPrem(familyGrpPrem);
                        peopleInsureInfo.setSelfPrem(familySelfPrem);
                    }
                } else {
                    /*** 学生投保 ***/
                    // 1、学生福利额度
                    double familyGrpPrem = 0.0;
                    // 2、学生投保保费
                    double familyInsurePrem = fcEnsurePlanMapper.selectPlanPrem(PlanInfo.getEnsureCode(), planCode);
                    // 3、学生自付保费
                    double familySelfPrem = 0.00;
                    if (familyInsurePrem > familyGrpTotalPrem) {
                        familyGrpPrem = familyGrpTotalPrem;
                        familySelfPrem = CommonUtil.sub(familyInsurePrem, familyGrpTotalPrem);
                        familyGrpTotalPrem = 0.0;
                    } else if (familyInsurePrem <= familyGrpTotalPrem) {
                        familyGrpPrem = familyInsurePrem;
                        familyGrpTotalPrem = CommonUtil.sub(familyGrpTotalPrem, familyInsurePrem);
                    }
                    //统计家属（学生）累计自付保费
                    familyTotalSelfPrem = CommonUtil.add(familyTotalSelfPrem, familySelfPrem);
                    // 统计家属累计投保保费
                    familyTotalInsurePrem = CommonUtil.add(familyTotalInsurePrem, familyInsurePrem);
                    // 封装单个家属数据
                    peopleInsureInfo.setPersonId(staffPersonId);
                    peopleInsureInfo.setTotalPrem(familyInsurePrem);
                    peopleInsureInfo.setGrpPrem(familyGrpPrem);
                    peopleInsureInfo.setSelfPrem(familySelfPrem);
                }

                /**
                 * 封装责任信息
                 */
                peopleInsureInfo.setRiskDutyInfoList(getRiskDutyInfo(ensureCode, planCode));
                peopleInsureInfoList.add(peopleInsureInfo);
                if (fcEnsure.getPlanType().equals(PlanTypeEnum.IMMOBILIZATIONPLAN.getCode())) {
                    try {
                        // 记录需要健康告知的人员信息
                        long age = Integer.valueOf(DateTimeUtil.getCurrentAge(peopleInsureInfo.getBirthDate(), fcEnsure.getCvaliDate()));
                        if (age > 0) {
                            age = age * 365;
                        } else {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            Date fDate = sdf.parse(peopleInsureInfo.getBirthDate());
                            Date oDate = sdf.parse(sdf.format(new Date()));
                            age = (oDate.getTime() - fDate.getTime()) / (1000 * 3600 * 24);
                        }
                        if (insureService.ageValidate(age, planCode, ensureCode)) {
                            healthNoticeInfoList.add(new HealthNoticeInfo(peopleInsureInfo.getPersonId(), peopleInsureInfo.getName()));
                        }
                        // 记录需要投保确认函的未成年人信息
                        long age1 = Integer.valueOf(DateTimeUtil.getCurrentAge(peopleInsureInfo.getBirthDate(), fcEnsure.getCvaliDate()));
                        if (age1 < 18) {
                            JuvenilesConfirmationInfo juvenilesConfirmationInfo = new JuvenilesConfirmationInfo();
                            // 投保确认书
                            FCPerInfo staffInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
                            juvenilesConfirmationInfo.setStaffName(staffInfo.getName());
                            juvenilesConfirmationInfo.setStaffIDNo(staffInfo.getIDNo());
                            juvenilesConfirmationInfo.setInsuredName(peopleInsureInfo.getName());
                            Map<String, String> map = new HashMap<String, String>();
                            map.put("perNo", perNo);
                            map.put("personID", peopleInsureInfo.getPersonId());
                            // 查询此人是否在本员工下是否有此家属
                            FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(map);
                            if (rela == null) {
                                // 查询家属对应的关系（因为这里有一个大前提，就是相同的员工，所以无需加上相同员工下的条件）
                                rela = fcStaffFamilyRelaMapper.selectPersonIdInfo(peopleInsureInfo.getPersonId());
                            }
                            if (rela != null) {
                                if ("3".equals(rela.getRelation())) {
                                    // 3为子女。 员工是被保人的父母。这里需要拆分成父亲或母亲
                                    if ("0".equals(staffInfo.getSex())) {
                                        juvenilesConfirmationInfo.setRelation("父亲");
                                    } else {
                                        juvenilesConfirmationInfo.setRelation("母亲");
                                    }
                                } else {
                                    String relationName = fdCodeMapper.selectNameByCode("Relation", rela.getRelation());
                                    juvenilesConfirmationInfo.setRelation(relationName);
                                }
                            } else {
                                juvenilesConfirmationInfo.setRelation("");
                            }
                            Map<String, Object> params1 = new HashMap<>();
                            params1.put("planCode", planCode);
                            params1.put("ensureCode", ensureCode);
                            List<FCPlanRiskDuty> dutyList = fcPlanRiskDutyMapper.selectDutyList(params1);
                            // 在这里判断固定计划中是否含有身故保险
                            List<String> dutyCodeList = Arrays.asList("GD0017", "GD0037", "GD0050", "GD0051", "GD0052", "GD0054");
                            int count = 0;
                            for (FCPlanRiskDuty dutyInfo : dutyList) {
                                if (dutyCodeList.contains(dutyInfo.getDutyCode())) {
                                    count++;
                                }
                            }
                            if (0 == count) {
                                log.info("被保人：" + peopleInsureInfo.getName() + "，投保计划：" + peopleInsureInfo.getPlanName() + "，该计划中不包含身故险种，故不弹出确认函!");
                            } else {
                                double deathAmnt = 0.00;
                                for (FCPlanRiskDuty dutyInfo : dutyList) {
                                    if ("GD0017".equals(dutyInfo.getDutyCode())) {
                                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                                    } else if ("GD0037".equals(dutyInfo.getDutyCode())) {
                                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                                    }
                                }
                                double Amnt15070 = 0.00;
                                for (FCPlanRiskDuty dutyInfo : dutyList) {
                                    if ("GD0050".equals(dutyInfo.getDutyCode())) {
                                        Amnt15070 = dutyInfo.getAmnt();
                                    }
                                    if ("GD0051".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                                        Amnt15070 = dutyInfo.getAmnt();
                                    }
                                    if ("GD0052".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                                        Amnt15070 = dutyInfo.getAmnt();
                                    }
                                    if ("GD0054".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                                        Amnt15070 = dutyInfo.getAmnt();
                                    }
                                }
                                deathAmnt = CommonUtil.add(deathAmnt, Amnt15070);
                                if (0.00 == deathAmnt) {
                                    log.info("被保人：" + peopleInsureInfo.getName() + "，投保计划：" + peopleInsureInfo.getPlanName() + "，本次投保身故保险金额为0，故不弹出确认函!");
                                } else {
                                    String riskAmnt = insureService.getRiskAmnt(peopleInsureInfo.getPersonId());
                                    if (riskAmnt == null || "".equals(riskAmnt)) {
                                        log.info("调用核心接口查询风险保额失败，请联系管理员！");
                                        throw new SystemException("调用核心接口查询风险保额失败！");

                                    }
                                    juvenilesConfirmationInfo.setPersonId(peopleInsureInfo.getPersonId());
                                    juvenilesConfirmationInfo.setPlanCode(planCode);
                                    juvenilesConfirmationInfo.setDeathAmnt(deathAmnt);
                                    Double riskAmntTotal = CommonUtil.add(Double.valueOf(riskAmnt), deathAmnt);
                                    juvenilesConfirmationInfo.setDeathAmntCount(riskAmntTotal);
                                    juvenilesConfirmationInfo.setYear(DateTimeUtil.getCurrentYear());
                                    juvenilesConfirmationInfo.setMonth(DateTimeUtil.getCurrentMonth());
                                    juvenilesConfirmationInfo.setDay(DateTimeUtil.getCurrentDay());
                                    juvenilesConfirmationInfoList.add(juvenilesConfirmationInfo);
                                }

                            }
                        }

                    } catch (Exception e) {
                        throw new SystemException("程序有误！");
                    }

                }

            }

            /**
             * 封装投保汇总信息
             */
            // 员工福利额度（初始福利额度，非使用的福利额度）
            eflexTotalInfo.setStaffGrpPrem(staffGrpPrem);
            // 员工投保保费
            eflexTotalInfo.setStaffInsurePrem(staffInsurePrem);
            // 家属累计投保保费
            eflexTotalInfo.setFamilyTotalInsurePrem(familyTotalInsurePrem);
            // 员工自付保费
            eflexTotalInfo.setStaffSelfPrem(staffSelfPrem);
            // 家属累计自付保费
            eflexTotalInfo.setFamilyTotalSelfPrem(familyTotalSelfPrem);
            // 个人实际支付保费
            eflexTotalInfo.setTotalSelfPrem(CommonUtil.add(staffSelfPrem, familyTotalSelfPrem));
            confirmInsureResp.setTotalInfo(eflexTotalInfo);
            // 校验是否需要显示员工付款信息
            if (fcEnsureConfig != null && payType.equals(PaymentTypeEnum.PERSONALDEDUCTION.getCode()) && eflexTotalInfo.getTotalSelfPrem() > 0) {
                confirmInsureResp.setIsCheck("1");
                confirmInsureResp.setPerInfo(fcPerInfo);
            } else {
                confirmInsureResp.setIsCheck("0");
                confirmInsureResp.setPerInfo(fcPerInfo);
            }
            if (eflexTotalInfo.getTotalSelfPrem() > 0) {
                // 需要支付
                confirmInsureResp.setIsSelfPay("1");
            } else {
                // 不需要支付
                confirmInsureResp.setIsSelfPay("0");
            }

            /**
             * 封装已投保的人员信息
             */
            confirmInsureResp.setPeopleInsureInfoList(peopleInsureInfoList);

            /**
             * 封装需要健康告知的人员信息
             */
            confirmInsureResp.setHealthNoticeList(healthNoticeInfoList);

            /**
             * 封装需要未成年人确认函的人员信息
             */
            confirmInsureResp.setJuvenilesConfirmationInfoList(juvenilesConfirmationInfoList);

            return confirmInsureResp;
        } catch (Exception e) {
            log.error("投保确认页面信息加载失败:{}", e);
            throw new SystemException("投保确认页面信息加载失败，" + e.getMessage());
        }
    }

    /**
     * @param token
     * @param insureConfirmSubmitReq
     * @return
     */
    @Transactional
    public String insureConfirmSubmit(String token, InsureConfirmSubmitReq insureConfirmSubmitReq) {

        Map<String, Object> resultMap = new HashMap<>();
        // 签约短信验证码
        String verifyCode = insureConfirmSubmitReq.getVerifyCode();
        // 是否校验付款信息
        String isCheck = insureConfirmSubmitReq.getIsCheck();

        //支付方式
        String payType = "";
        GlobalInput globalInput = userService.getSession(token);
        String grpNo = globalInput.getGrpNo();
        String ensureCode = globalInput.getEnsureCode();
        String perNo = globalInput.getCustomNo();
        String signatureFile = insureConfirmSubmitReq.getSignatureFile();


        //获取支付方式
        List<Map<String, String>> payTypeList = fcGrpOrderMapper.getPayType(grpNo, ensureCode);
        if (payTypeList != null && payTypeList.size() > 0) {
            Map<String, String> payTypeMap = payTypeList.get(0);
            if (payTypeMap != null) {
                payType = payTypeMap.get("configValue");
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "订单提交失败,获取支付方式失败");
                return JSON.toJSONString(resultMap);
            }
        } else {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "订单提交失败,获取支付方式失败");
            return JSON.toJSONString(resultMap);
        }
        ConfirmInsureResp confirmInsureResp = confirmInsure(token, insureConfirmSubmitReq.getPeopleInsurePlanInfos(), insureConfirmSubmitReq.getOrderNo());
        log.info("订单提交返回:{}", JSON.toJSONString(confirmInsureResp));
        CheckTBResultInfo checkTBResultInfo = confirmInsureResp.getCheckTBResultInfo();
        if (null != checkTBResultInfo) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", checkTBResultInfo.getCheckTBResultMsg() + "--" + checkTBResultInfo.getCheckTBResultList().get(0).get("data"));
            return JSON.toJSONString(resultMap);
        }
        String orderNo = confirmInsureResp.getOrderNo();

        if (payType.equals("1") || payType.equals("2")) {//1-企业缴纳，2-企业代扣代缴时，订单状态置为待生效
            insureService.updateFcorder("08", orderNo);
        } else if (payType.equals("0")) {//0-实时支付
            insureService.updateFcorder("08", orderNo);
        } else if (payType.equals("3")) {//3-个人批扣
            //判断是否需要提供付款信息
            if (org.apache.commons.lang3.StringUtils.isNotBlank(isCheck) && isCheck.equals("1")) {
                if (verifyCode == null || verifyCode.equals("")) {
                    resultMap.put("code", "500");
                    resultMap.put("message", "请输入短信验证码");
                    return JSON.toJSONString(resultMap);
                } else {
                    // 调用签约确认接口
                    bankSignService.signConfirm(new SignConfirmRequest(perNo, ensureCode, orderNo, verifyCode));
                }
            }

            // 更新团体订单的信息
            insureService.updateFcorder("08", orderNo);
        } else {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "支付方式数据异常");
            return JSON.toJSONString(resultMap);
        }
        //记录订单轨迹表
        FCOrderLocus fcOrderLocus = new FCOrderLocus();
        String orderLocusSN = maxNoService.createMaxNo("orderLocusSN", "", 20);
        fcOrderLocus.setOrderLocusSN(orderLocusSN);
        fcOrderLocus.setOrderNo(orderNo);
        fcOrderLocus.setOrderStatus("08");
        fcOrderLocus.setOperator(globalInput.getUserNo());
        fcOrderLocus = CommonUtil.initObject(fcOrderLocus, "INSERT");
        fcOrderLocusMapper.insert(fcOrderLocus);

        //生成影像PDF
        try {
            MultipartFile file = BASE64DecodedMultipartFile.base64ToMultipart(signatureFile);
            InsuredConfirmPageReq insuredConfirmPageReq = new InsuredConfirmPageReq();
            insuredConfirmPageReq.setPeopleInsurePlanInfos(insureConfirmSubmitReq.getPeopleInsurePlanInfos());
            String resultString = MakeProposalForm2(token, ensureCode, perNo, orderNo, insuredConfirmPageReq, file);
            if ("500".equals(resultString)) {
                resultMap.put("success", false);
                resultMap.put("orderNo", orderNo);
                resultMap.put("code", "500");
                resultMap.put("message", "电子投保单影像件生成失败,请联系管理员");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            log.error("调用电子投保单影像件生成失败:{},{}", e.getMessage(), e);
            e.printStackTrace();
            resultMap.put("success", false);
            resultMap.put("orderNo", orderNo);
            resultMap.put("code", "500");
            resultMap.put("message", "电子投保单影像件生成失败,请联系管理员");
            return JSON.toJSONString(resultMap);
        }
        resultMap.put("success", true);
        resultMap.put("orderNo", orderNo);
        resultMap.put("code", "200");
        resultMap.put("message", "确认成功");
        return JSON.toJSONString(resultMap);
    }

    /**
     * 正常流程的生成影像
     *
     * @param ensureCode
     * @param perNo
     * @param orderNo
     * @return, String token ,InsuredConfirmPageReq insuredConfirmPageReq
     */
    public String MakeProposalForm2(String token, String ensureCode, String perNo, String orderNo, InsuredConfirmPageReq insuredConfirmPageReq, MultipartFile file) {
        log.info("团险员福平台新增电子签名及电子投保书影像生成开始》》》》》》》》》》》》》》");
        long startMakeProposalTime = System.currentTimeMillis();
        GlobalInput globalInput = userService.getSession(token);
        String resultString = "500";
        try {
            /** 请求报文拼接 */
            XStream xStream = new XStream(new DomDriver());
            DATASETS datasets = new DATASETS();
            datasets.setControl("01");
            /**
             * 影像类型编码集合
             */
            ImageTypes imageTypes = new ImageTypes();
            imageTypes.setImageType("311013");
            datasets.setImageTypes(imageTypes);
            /**
             * 影像报文集合
             */
            DATASET dataset = new DATASET();
            DecimalFormat df = new DecimalFormat("#.00");
            // 福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            // 获取代理人信息
            List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
            FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);
            // 团体订单信息
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
            // 获取企业信息
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(fcGrpOrder.getGrpNo());
            // 子订单信息
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectImagesByorderNo(orderNo);
            // 获取付款人银行信息
            Map<String, String> orderPayInfo = fcOrderPayMapper.selectByOrderNo(orderNo);
            //获取员工和家属投保金额
            ConfirmInsureResp confirmInsureResp = insuredConfirmPage(token, insuredConfirmPageReq);
            List<PeopleInsureInfo> peopleInsureInfoList = confirmInsureResp.getPeopleInsureInfoList();
            FCPerInfo perInfo = confirmInsureResp.getPerInfo();
            // 获取缴费方式;
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("ensureCode", fcGrpOrder.getEnsureCode());
            param.put("grpNo", fcGrpOrder.getGrpNo());
            param.put("configNo", "008");
            // 交费方式 0企业 1个人
            String orderPayType = fcEnsureConfigMapper.selectOnlyValue(param);
            // 交费方式 0企业 1个人
            String payMoney = String.valueOf(confirmInsureResp.getTotalInfo().getFamilyTotalInsurePrem() + confirmInsureResp.getTotalInfo().getStaffInsurePrem());//订单金额总和(员工和家属)
            String PayMode = "";
            String PayModeCon = "";
            if ("2".equals(orderPayType)) {
                PayMode = "企业代扣代缴-企业代缴";
            }
            if ("3".equals(orderPayType)) {
                PayMode = "个人批扣-混合缴费";
            }
            if ("1".equals(orderPayType)) {
                PayMode = "企业缴纳";
            }
            dataset.setContNo(fcGrpOrder.getGrpContNo());
            String ImageNo = maxNoService.createMaxNo("ImageNo", "A311013", 8);
            dataset.setPrtNo(ImageNo);
            dataset.setOrderNo(orderNo);
            dataset.setManageComCode(fdAgentInfo.getManageCom());
            dataset.setManageComName("");
            dataset.setAgentCode(fdAgentInfo.getAgentCode());
            dataset.setAgentName(fdAgentInfo.getName());
            dataset.setAgentPhone(fdAgentInfo.getMobile());
            dataset.setCurrencyType("");
            dataset.setPayMoneySumFigures(df.format(Double.valueOf(payMoney)));
            // 金额大写
            dataset.setPayMoneySumWrods(NumberToCN.number2CNMontrayUnit(new BigDecimal(payMoney)));
            // 续期缴费方式
            dataset.setPayMode(PayModeCon);
            // 首期缴费方式 默认自交
            dataset.setNewPayMode(PayMode);
            //固定计划没有缴费频次
            dataset.setPayIntv("");
            // 一年期产品是否自动续保
            dataset.setRNewFlag("");
            // 是否按照续保单承保
			/*Map<String,Object> params = new HashMap<>();
			params.put("ensureCode", fcGrpOrder.getEnsureCode());
			params.put("grpNo", fcGrpOrder.getGrpNo());
			params.put("configNo", "022");
			String fcEnsureConfig_022 = fcEnsureConfigMapper.selectOnlyValue(params);*/
            //是否有社保
            dataset.setSocialInsuFlag("");
            //红利领取方式
            dataset.setBonusGetMode("");
            dataset.setAnnuityGetMode("");
            dataset.setAnnuityGetIntv("");
            //团险种类??
            dataset.setGrpType("05");
            //电子投保单种类
            dataset.setEPolicyType("02");
            /************* 银行信息 ***********************/
            String openBank = perInfo.getOpenBank();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(openBank)) {
                String bankName = fdCodeMapper.queryNameByCode(openBank);
                dataset.setBankName(bankName);
                dataset.setBankAccNo(perInfo.getOpenAccount());
                dataset.setBankAccName(perInfo.getName());
                dataset.setBankAccType("");
            } else {
                dataset.setBankName("");
                dataset.setBankAccNo("");
                dataset.setBankAccName("");
                dataset.setBankAccType("");
            }
            dataset.setSignDate(DateTimeUtil.getCurrentDate());
            dataset.setAgentSignDate("");
            dataset.setLoanType("");
            dataset.setLoanOrg("");
            dataset.setLoanContNo("");
            dataset.setLoanMoney("");
            /************* 投保人信息 *************************/
            // 员工信息
            String relation = "0";
            Map<String, String> params = new HashMap<>();
            params.put("perNo", perNo);
            params.put("relation", relation);
            List<Map<String, String>> fcPersonList = fcPersonMapper.selectFcPersonInfoByParams(params);
            Map<String, String> staffInfo = fcPersonList.get(0);
            dataset.setAppntName(staffInfo.get("name"));
            dataset.setAppntBirthday(staffInfo.get("birthDate"));
            dataset.setAppntSex(staffInfo.get("sexName"));
            dataset.setAppntNationality(staffInfo.get("nativeplaceName"));
            dataset.setAppntIdTypeName(staffInfo.get("idTypeName"));
            dataset.setAppntIdNo(staffInfo.get("idNo"));
            dataset.setAppntIdExpDate(staffInfo.get("idTypeEndDate"));
            dataset.setAppntOccupationName(staffInfo.get("occupationCodeName"));
            dataset.setAppntOccupationCode(staffInfo.get("occupationCode"));
            dataset.setAppntWorkPlace(fcGrpInfo.getGrpName());
            dataset.setAppntAddress(
                    staffInfo.get("provinceName") == null ? "" : staffInfo.get("provinceName")
                            + staffInfo.get("cityName") == null ? "" : staffInfo.get("cityName")
                            + staffInfo.get("countyName") == null ? "" : staffInfo.get("countyName")
                            + staffInfo.get("detaileAddress") == null ? "" : staffInfo.get("detaileAddress")
            );
            dataset.setAppntZipCode(staffInfo.get("zipCode"));
            dataset.setAppntPhone(staffInfo.get("mobilePhone"));
            dataset.setAppntHomePhone("");
            dataset.setAppntEmail(staffInfo.get("eMail"));
            /**************************** 被保人节点 ********************************/
            // Insured第二层
            List<InsuredsItem> Insureds = new ArrayList<>();
            // 被保人信息
            // 查询被保人信息peopleInsureInfoList
            for (PeopleInsureInfo peopleInsureInfo : peopleInsureInfoList) {
                // TODO  这里条件能换成personID 循环peopleInsureInfoList 里面的
                Map<String, String> fcOrderInsured = fcOrderInsuredMapper.selectPersonByOrderItemNo(peopleInsureInfo.getPersonId());
                // 根据perNo和personId查询被保人信息
                params.clear();
                params.put("perNo", perNo);
                params.put("personId", fcOrderInsured.get("personId"));
                List<Map<String, String>> orderInsuredInfos = fcPersonMapper.selectFcPersonInfoByParams(params);
                Map<String, String> orderInsuredInfo = orderInsuredInfos.get(0);
                // Insured第三层
                InsuredsItem insuredsItem = new InsuredsItem();
                // TODO 被保险人类型
                insuredsItem.setInsuredType("被保险人");
                insuredsItem.setInsuredName(fcOrderInsured.get("name"));
                insuredsItem.setInsuredBirthday(fcOrderInsured.get("birthday"));
                insuredsItem.setInsuredSex(fcOrderInsured.get("sexName"));
                insuredsItem.setInsuredNationality(orderInsuredInfo.get("nativeplaceName"));
                insuredsItem.setInsuredIdTypeName(fcOrderInsured.get("idTypeName"));
                insuredsItem.setInsuredIdNo(fcOrderInsured.get("idNo"));
                insuredsItem.setInsuredIdExpDate(orderInsuredInfo.get("idTypeEndDate"));
                insuredsItem.setInsuredOccupationName(fcOrderInsured.get("occupationCodeName"));
                insuredsItem.setInsuredOccupationCode(fcOrderInsured.get("occupationCode"));
                insuredsItem.setInsuredWorkPlace(fcOrderInsured.get("relation").equals("00") ? fcGrpInfo.getGrpName() : "");
                dataset.setAppntAnnualIncome(StringUtil.isEmpty(fcOrderInsured.get("mainYearSalary")) ? "0.0"
                        : CommonUtil.mul(Double.parseDouble(fcOrderInsured.get("mainYearSalary")), 10000.0) + "");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(fcOrderInsured.get("provinceName")) &&
                        org.apache.commons.lang3.StringUtils.isNotBlank(fcOrderInsured.get("cityName")) &&
                        org.apache.commons.lang3.StringUtils.isNotBlank(fcOrderInsured.get("countyName")) &&
                        org.apache.commons.lang3.StringUtils.isNotBlank(fcOrderInsured.get("detaileAddress"))) {
                    insuredsItem.setInsuredAddress(fcOrderInsured.get("provinceName") + fcOrderInsured.get("cityName")
                            + fcOrderInsured.get("countyName") + fcOrderInsured.get("detaileAddress"));
                } else {
                    insuredsItem.setInsuredAddress("");
                }
                insuredsItem.setInsuredZipCode(fcOrderInsured.get("zipCode"));
                insuredsItem.setInsuredPhone(fcOrderInsured.get("mobilePhone"));
                insuredsItem.setInsuredHomePhone("");
                insuredsItem.setInsuredEmail(fcOrderInsured.get("eMail"));
                insuredsItem.setNeedConfirmationFlag("是");
                insuredsItem.setAppntRelationToInsured(fcOrderInsured.get("relation"));
                // Insured->BnfInfo第一层没有身故受益人
                List<BnfInfosItem> bnfInfos = new ArrayList<>();
                insuredsItem.setBnfInfos(bnfInfos);
                /************** 险种列表 *************************/
                List<RiskInfosItem> riskInfos = new ArrayList<>();
                /**
                 * 需要peopleInsureInfoList 里面的每条数据拼接riskinfo
                 */
                List<RiskDutyInfo> riskDutyInfoList = peopleInsureInfo.getRiskDutyInfoList();
                for (RiskDutyInfo riskDutyInfo : riskDutyInfoList) {
                    RiskInfosItem riskInfosItem = new RiskInfosItem();
                    riskInfosItem.setRiskCode(riskDutyInfo.getRiskCode());
                    riskInfosItem.setRiskName(riskDutyInfo.getRiskName());
                    riskInfosItem.setSubRiskFlag("M");
                    riskInfosItem.setInsuredObjectName(fcOrderInsured.get("name"));
                    riskInfosItem.setAmnt(riskDutyInfo.getAmount().toString());
                    //TODO !!!!!!!!固定计划好像没有保险期间(不确定是不是计划的福利保险期间  感觉应该是)
                    // 设置保障期间字段
                    String policyEndDate = fcEnsure.getPolicyEndDate();
                    String cvaliDate = fcEnsure.getCvaliDate();
                    Long days = DateTimeUtil.getDistanceDays(policyEndDate, cvaliDate);
                    if (days == 364 || days == 365) {
                        fcEnsure.setInsuredPeriodType(InsuredPeriodTypeEnum.YEARINSURED.getValue());
                        fcEnsure.setInsuredPeriod("1年");
                    } else {
                        fcEnsure.setInsuredPeriodType(InsuredPeriodTypeEnum.SHORTINSURED.getValue());
                        days = days + 1;
                        fcEnsure.setInsuredPeriod(days + "天");
                    }
                    riskInfosItem.setInsuYear(fcEnsure.getInsuredPeriodType() + fcEnsure.getInsuredPeriod());
                    riskInfosItem.setPayEndYear("");
                    /**
                     * 需要peopleInsureInfoList totalprem
                     */
                    riskInfosItem.setPrem(riskDutyInfo.getPrem().toString());
                    List<DutyInfoListItem> dutyInfoList = new ArrayList<>();
                    DutyInfoListItem dutyInfoListItem = new DutyInfoListItem();
                    /**
                     * 直接取 dutycode dutyname
                     */
                    dutyInfoListItem.setDutyName(riskDutyInfo.getDutyName());
                    dutyInfoListItem.setDutyCode(riskDutyInfo.getDutyCode());
                    dutyInfoListItem.setAmnt(riskDutyInfo.getAmount().toString());
                    //TODO !!!!!!!!固定计划好像没有保险期间(不确定是不是计划的福利保险期间  感觉应该是)
                    dutyInfoListItem.setInsuYear(fcEnsure.getInsuredPeriodType() + fcEnsure.getInsuredPeriod());
                    dutyInfoListItem.setPayEndYear("");
                    dutyInfoListItem.setPrem(riskDutyInfo.getPrem().toString());
                    dutyInfoList.add(dutyInfoListItem);
                    riskInfosItem.setDutyInfoList(dutyInfoList);
                    riskInfos.add(riskInfosItem);
                    /**
                     * peopleInsureInfoList这里循环结束
                     */
                }
                insuredsItem.setRiskInfos(riskInfos);
                Insureds.add(insuredsItem);
            }
            dataset.setInsureds(Insureds);
            Map<String, Object> paramss = new HashMap<String, Object>();
            paramss.put("ensureCode", fcGrpOrder.getEnsureCode());
            paramss.put("grpNo", fcGrpOrder.getGrpNo());
            paramss.put("configNo", "006");
            String fcEnsureConfig_006 = fcEnsureConfigMapper.selectOnlyValue(paramss);
            if (fcEnsureConfig_006 == null) {
                fcEnsureConfig_006 = "";
            }
            dataset.setSpecInfoSet(fcEnsureConfig_006);
            /******************* 健告 **********************/
            List<CustomerImpart> customerImparts = new ArrayList<>();
            // TODO 少一层for循环吧
            /**********************************************************************************************************/
            String healthNotice = "是否有在投保或复效时因身体疾病原因被拒保、延期、加费或除外责任承保？或被解除过保险合同？或申请过重大疾病理赔？。" +
                    "被保险人是否存在智力发育障碍、失明、聋哑、咀嚼或身体任何部位缺失、畸形及功能障碍、重听、高度近视（1000度以上）？。" +
                    "是否目前或过去1年内曾有过下列症状或异常？反复头痛，晕厥，胸痛，咯血，呼吸困难，进行性吞咽困难，黄疸（不包括已治愈无脑损伤和后遗症的新生儿黄疸），便血，持续血尿或蛋白尿，阴道异常出血，宫颈重度不典型增生，原因不明的粘膜及齿龈出血，持续发热，半年内体重下降超过5公斤（主动减肥除外），甲状腺结节（医学分类3级或3级以上），乳房结节（医学分类3级或3级以上），肺毛玻璃样结节？。" +
                    "被保险人最近2年内是否做过以下一项或几项检查并且检查结果有异常：运动心电图、超声心动图、血管造影、CT、核磁共振、内镜、病理检查、肿瘤标志物检查、TCT等？。" +
                    "过去或现在是否有患下列严重疾病：恶性肿瘤（包括白血病、淋巴瘤），原位癌，癌前病变；脑肿瘤，脑中风（脑梗塞/脑梗死/脑出血），帕金森氏病，阿尔茨海默病，癫痫，瘫痪，重症肌无力，精神疾病（精神或行为障碍）；2级或以上高血压（收缩压≥160mmHg和或舒张压≥100mmHg），冠心病，冠状动脉狭窄，心肌梗死/心肌梗塞，风湿性心脏病，心肌病，心功能不全二级或以上，主动脉疾病；慢性阻塞性肺病，肺心病，呼吸衰竭；慢性肾炎，尿毒症，肾功能不全；慢性肝炎，丙肝，肝硬化，克隆病，溃疡性结肠炎，慢性胰腺炎；糖尿病（不包括妊娠糖尿病但产后恢复正常的情形），再生障碍性贫血，血友病，系统性红斑狼疮，类风湿性关节炎，强直性脊柱炎，艾滋病或艾滋病毒携带者；重大器官（肝、肺、心、肾）移植，以及其他造成生活不能自理、需要长期治疗的疾病？。" +
                    "过去或现在是否患有酒精或药物滥用成瘾？是否存在以下习惯？" +
                    "a.吸烟，且平均每日吸烟2包以上，" +
                    "b.饮酒，且平均每日饮酒白酒1斤以上，或啤酒8瓶，或红酒2瓶以上。" +
                    "2周岁及以下被保险人告知：是否为出生低体重儿（出生时体重低于 2.5 公斤）或早产儿（出生孕周小于37周）？是否有发育迟缓、惊厥、抽搐、脑瘫、先天性和遗传性疾病？。";
            String answerHealthNotice = "否/否/否/。否/。否/否/。否/。否/。否/否/。否/否/。";
            List<String> NoticeList = Arrays.stream(healthNotice.split("。")).collect(Collectors.toList());
            List<String> answerNoticeList = Arrays.stream(answerHealthNotice.split("。")).collect(Collectors.toList());
            List<HealthNoticeInfo> healthNoticeList = confirmInsureResp.getHealthNoticeList();
            if (CollectionUtils.isNotEmpty(healthNoticeList)) {
                for (HealthNoticeInfo healthNoticeInfo : healthNoticeList) {
                    List<InsuredImpart> InsuredImpartList = new ArrayList<>();
                    CustomerImpart customerImpart = new CustomerImpart();
                    customerImpart.setInsuredObjectName(healthNoticeInfo.getName());
                    for (int i = 0; i < NoticeList.size(); i++) {
                        InsuredImpart insuredImpart = new InsuredImpart();
                        insuredImpart.setImpver("D008");
                        insuredImpart.setImpartCode("4180" + (i + 1));
                        insuredImpart.setImpartContent(NoticeList.get(i));
                        insuredImpart.setImpartReply(answerNoticeList.get(i));
                        insuredImpart.setIntroDuctions("");
                        InsuredImpartList.add(insuredImpart);
                    }
                    customerImpart.setInsuredImpartList(InsuredImpartList);
                    customerImparts.add(customerImpart);
                }
            }
            /**********************************************************************************************************/
            dataset.setCustomerImparts(customerImparts);
            // 插入签名地址
            List<PicFile> picFileList = new ArrayList<PicFile>();
            params.clear();
            log.info("图片文件:{}", file);
            //签名在ftp上传到阿里云
            OssEntity sign = ossUtils.uploadFile(file, "sign");
            PicFile picFileKid = new PicFile();
            picFileKid.setPicType("0205");
            picFileKid.setFileName("被保险人签名");
            picFileKid.setHttpUrl(sign.getOutUrl());
            picFileList.add(picFileKid);
            /**
             * 把Oss签名保存到DB 签名fileType 0808
             */
            params.put("orderItemNo", fcOrderItem.getOrderItemNo());
            params.put("relation", relation);
            // fileType(imageType) 08081 Oss签名类型  外网地址
            params.put("fileType", "08081");
            saveImageAddress(fcOrderItem.getOrderItemNo(), "08081", relation, params, globalInput.customNo, sign.getOutUrl(), "");
            // fileType(imageType) 08082 Oss签名类型  内网地址
            params.put("fileType", "08082");
            saveImageAddress(fcOrderItem.getOrderItemNo(), "08082", relation, params, globalInput.customNo, sign.getInUrl(), "");

            if (picFileList != null && picFileList.size() > 0) {
                dataset.setPicFiles(picFileList);
            }
            datasets.setDATASET(dataset);

            // XML请求报文生成
            xStream.alias("DATASETS", DATASETS.class);
            xStream.alias("Insured", InsuredsItem.class);
            xStream.alias("BnfInfo", BnfInfosItem.class);
            xStream.alias("RiskInfo", RiskInfosItem.class);
            xStream.alias("DutyInfo", DutyInfoListItem.class);
            xStream.alias("CustomerImpart", CustomerImpart.class);
            xStream.alias("InsuredImpart", InsuredImpart.class);
            xStream.alias("LCCustomerImpart", LCCustomerImpart.class);
            xStream.alias("LCCustomerImparts", LCCustomerImparts.class);
            xStream.alias("PicFile", PicFile.class);
            String requestBodyXml = xStream.toXML(datasets);
            String reqXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
            requestBodyXml = reqXml + requestBodyXml;
            // 调用接口
            log.info("调用电子投保单影像件生成请求报文：\n{}", requestBodyXml);
            long startTime = System.currentTimeMillis();
            String responseJson = HttpUtil.postHttpRequestXMl(myProps.getPolicyGenerateImageUrl(), requestBodyXml);
            long endTime = System.currentTimeMillis();
            log.info("电子投保单影像件生成接口用时：" + ((endTime - startTime) / 1000.00) + "秒");
            log.info("调用电子投保单影像件生成返回参数:{}", responseJson);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(responseJson)) {
                GenerateImageResult generateImageResult = JSONObject.parseObject(responseJson,
                        GenerateImageResult.class);
                GenerateImageResultRowResp result = generateImageResult.getResult();
                List<GenerateImageResultRowItemResp> row = result.getRow();
                if (row != null && row.size() > 0) {
                    GenerateImageResultRowItemResp generateImageResultRowItemResp = row.get(0);
                    String rescode = generateImageResultRowItemResp.getRescode();
                    if (rescode.equals("0000")) {
                        /**
                         * 把电子签名影响保存到DB 签名fileType 0809 08091 外网地址  08092 内网地址
                         */
                        params.put("fileType", "08091");
                        saveImageAddress(fcOrderItem.getOrderItemNo(), "08091", relation, params, globalInput.customNo, generateImageResultRowItemResp.getOuterNetUrl(), ImageNo);
                        params.put("fileType", "08092");
                        saveImageAddress(fcOrderItem.getOrderItemNo(), "08092", relation, params, globalInput.customNo, generateImageResultRowItemResp.getInnerNetUrl(), ImageNo);
                        resultString = "200";
                    } else {
                        resultString = "500";
                    }
                }
                long endMakeProposalTime = System.currentTimeMillis();
                log.info("团险员福平台新增电子签名及电子投保书影像生成生成用时：" + ((endMakeProposalTime - startMakeProposalTime) / 1000.00) + "秒");
            } else {
                log.info("调用电子投保单影像件生成返回结果为空！");
            }
        } catch (Exception e) {
            log.error("调用电子投保单影像件生成失败:{},{}", e.getMessage(), e);
            TransactionAspectSupport.currentTransactionStatus()
                    .setRollbackOnly();
            e.printStackTrace();
        }
        return resultString;
    }

    /**
     * 保存 签名以及电子投保影像
     *
     * @param orderItemNo
     * @param imageType   0808签名Oss   0809 电子投保单影像
     * @param relation
     * @param params
     * @param operator
     * @param sign        地址
     */
    private void saveImageAddress(String orderItemNo, String imageType, String relation, Map<String, String> params, String operator, String sign, String imageNumber) {
        //删除历史记录
        List<FCPersonImage> selectImages = fcPersonImageMapper.selectImages(params);
        selectImages.forEach((FCPersonImage fcPersonImage) -> {
            fcPersonImageMapper.deleteByPrimaryKey(fcPersonImage.getImageNo());
        });
        Integer imageOrder = fcPersonImageMapper.getImageOrderInfo(orderItemNo, imageType);
        FCPersonImage fcPersonImage = new FCPersonImage();
        String imageNo = maxNoService.createMaxNo("ImageNo", "", 20);
        fcPersonImage.setImageNo(imageNo);
        fcPersonImage.setOrderItemNo(orderItemNo);
        fcPersonImage.setRelation(relation);
        fcPersonImage.setImageType(imageType);
        fcPersonImage.setImageOrder(StringUtil.isEmpty(imageOrder) ? "1" : imageOrder + "");
        fcPersonImage.setOperator(operator);
        fcPersonImage.setImageUrl(sign);
        fcPersonImage.setImageNumber(imageNumber);
        fcPersonImage = CommonUtil.initObject(fcPersonImage, "INSERT");
        fcPersonImageMapper.insertSelective(fcPersonImage);
    }


    public String insertImage(String ftprootpath, String orderItemNo, String relation, String fileType, String operator, String fileName) {
        Integer imageOrder = fcPersonImageMapper.getImageOrderInfo(orderItemNo, fileType);
        FCPersonImage fcPersonImage = new FCPersonImage();
        String imageNo = maxNoService.createMaxNo("ImageNo", "", 20);
        fcPersonImage.setImageNo(imageNo);
        fcPersonImage.setOrderItemNo(orderItemNo);
        fcPersonImage.setRelation(relation);
        fcPersonImage.setImageType(fileType);
        fcPersonImage.setImageOrder(StringUtil.isEmpty(imageOrder) ? "1" : imageOrder + "");
        fcPersonImage.setOperator(operator);
        fcPersonImage.setImageUrl(FileUtil.getFtpPath(ftprootpath, fileType, orderItemNo, relation) + fileName);
        fcPersonImage = CommonUtil.initObject(fcPersonImage, "INSERT");
        fcPersonImageMapper.insertSelective(fcPersonImage);
        return imageNo;
    }
}