package com.sinosoft.eflex.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.PlanObjectEnum;
import com.sinosoft.eflex.enums.PlanTypeEnum;
import com.sinosoft.eflex.model.AddressEntity.convert.CheckSameCustomerConvert;
import com.sinosoft.eflex.model.BatchInsureInterface.Head;
import com.sinosoft.eflex.model.BatchInsureInterface.RiskInfo;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.confirmInsure.PeopleInsurePlanInfo;
import com.sinosoft.eflex.model.confirmInsureEflex.SelectSelfOrderDetailInfoReq;
import com.sinosoft.eflex.model.insureEflexPlanPage.WaitingPeriodInfo;
import com.sinosoft.eflex.model.sign.apply.SignApply;
import com.sinosoft.eflex.model.sign.apply.SignApplyResponse;
import com.sinosoft.eflex.model.sign.confirm.SignConfirm;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmRequest;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmResponse;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmReturnData;
import com.sinosoft.eflex.rpc.model.CoreCustomerInfoResDTO;
import com.sinosoft.eflex.rpc.service.CoreCustomerService;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.service.userClient.BankSignService;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @DESCRIPTION
 * @create 2018-08-23 15:40
 **/
@Service
@Slf4j
public class InsureService {

    @Autowired
    private UserService userService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FPInsurePlanMapper fpInsurePlanMapper;
    @Autowired
    private FCDefaultPlanMapper fcDefaultPlanMapper;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private FCPerAppntMapper fcPerAppntMapper;
    @Autowired
    private FCPerRegistDayMapper fcPerRegistDayMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCOrderMapper fcOrderMapper;
    @Autowired
    private FCOrderItemMapper fcOrderItemMapper;
    @Autowired
    private FcPerImpartResultMapper fcPerImpartResultMapper;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private FCOrderItemDetailMapper fcOrderItemDetailMapper;
    @Autowired
    private FCOrderLocusMapper fcOrderLocusMapper;
    @Autowired
    private FCEnsurePlanMapper fcEnsurePlanMapper;
    @Autowired
    private FCPlanRiskMapper fcPlanRiskMapper;
    @Autowired
    private FCPlanRiskDutyMapper fcPlanRiskDutyMapper;
    @Autowired
    private MyProps myProps;
    @Autowired
    private FDAgentInfoMapper fdAgentInfoMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private EmpAndFamilyMapper familyMapper;
    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;
    @Autowired
    private FCPlanInformMapper fcPlanInformMapper;
    @Autowired
    private FDRiskDutyInfoMapper fdRiskDutyInfoMapper;
    @Autowired
    private TBCheckRules tbCheckRules;
    @Autowired
    private EflexTBCheckRules eflexTBCheckRules;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FCMailInfoMapper fcMailInfoMapper;
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private FcPlanConfigMapper fcPlanConfigMapper;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;
    @Autowired
    private FcEnsureContactMapper fcEnsureContactMapper;
    @Autowired
    private PremTrailService premTrailService;
    @Autowired
    private FPInsureEflexPlanMapper fPInsureEflexPlanMapper;
    @Autowired
    private FPInsureEflexPlanOptionalMapper fPInsureEflexPlanOptionalMapper;
    @Autowired
    private FCPlanHealthDesignRelaMapper fCPlanHealthDesignRelaMapper;
    @Autowired
    private FcDutyAmountGradeMapper fcDutyAmountGradeMapper;
    @Autowired
    private FcInsureEflexPlanMapper fcInsureEflexPlanMapper;
    @Autowired
    private FCBusPersonTypeMapper fcBusPersonTypeMapper;
    @Autowired
    private EnsureMakeService ensureMakeService;
    @Autowired
    private BankSignService bankSignService;
    @Autowired
    private FCBatchPayBankInfoMapper fcBatchPayBankInfoMapper;
    @Autowired
    private FcBusinessProDutyGrpObjectMapper fcBusinessProDutyGrpObjectMapper;
    @Autowired
    private AddressCheckService addressCheckService;
    @Autowired
    private CoreCustomerService coreCustomerService;


    /**
     * 个人投保计划暂存保存接口
     *
     * @param token
     * @param personId
     * @param planCode
     * @return
     */
    public String saveInsureInfoYUAN(String token, String personId, String planCode) {
        GlobalInput globalInput = userService.getSession(token);
        return saveInsureInfo(token, personId, planCode, globalInput.getEnsureCode(), globalInput.getCustomNo());
    }

    /**
     * 存储投保计划信息表
     *
     * @param token
     * @param personId
     * @param planCode
     * @param ensureCode
     * @param perNo
     * @return
     */
    public String saveInsureInfo(String token, String personId, String planCode, String ensureCode, String perNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            log.info("计划" + planCode);
            GlobalInput globalInput = userService.getSession(token);
            FPInsurePlan insurePlan = new FPInsurePlan();
            String insurePlanNo = maxNoService.createMaxNo("InsurePlanNo", "", 20);
            insurePlan.setInsurePlanNo(insurePlanNo);
            insurePlan.setEnsureCode(ensureCode);
            insurePlan.setAppntYear(DateTimeUtil.getCurrentYear());
            insurePlan.setPlanCode(planCode);
            // 0:订单未提交 1.订单已提交
            insurePlan.setInsureState("0");
            insurePlan.setPerno(perNo);
            insurePlan.setPersonId(personId);
            insurePlan.setOperator(globalInput.getName());
            insurePlan = (FPInsurePlan) CommonUtil.initObject(insurePlan, "INSERT");
            //个人投保计划临时表 用于用户最后确认投保的数据
            fpInsurePlanMapper.insertSubtables(insurePlan);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "投保计划保存成功");
        } catch (Exception e) {
            log.info("计划投保失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "投保计划保存失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 个人投保计划暂存修改接口
     *
     * @param token
     * @param insurePlanNo
     * @param planCode
     * @return
     */
    public String updateInsureInfo(String token, String insurePlanNo, String planCode) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            FPInsurePlan insurePlan = fpInsurePlanMapper.selectByPrimaryKey(insurePlanNo);
            if (insurePlan != null) {
                insurePlan.setAppntYear(DateTimeUtil.getCurrentYear());
                insurePlan.setPlanCode(planCode);
                insurePlan.setInsureState("0");
                insurePlan = (FPInsurePlan) CommonUtil.initObject(insurePlan, "UPDATE");
                fpInsurePlanMapper.updateByPrimaryKey(insurePlan);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "投保计划修改成功");
            } else {
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "该被保人不存在投保计划");
            }
        } catch (Exception e) {
            log.info("投保计划修改失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "投保计划修改失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 个人投保计划暂存删除接口
     *
     * @param token
     * @param
     * @return
     */
    @Transactional
    public String deleteInsureInfo(String token, String planCode, String personId) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        GlobalInput globalInput = userService.getSession(token);
        try {
            Map<String, Object> selectmap = new HashMap<>();
            selectmap.put("ensureCode", globalInput.getEnsureCode());
            selectmap.put("personId", personId);
            List<FPInsurePlan> insurePlan = fpInsurePlanMapper.selectEnsureCodeByPersonId(selectmap);
            for (FPInsurePlan fpInsurePlan : insurePlan) {
                String insurePlanNo = fpInsurePlan.getInsurePlanNo();
                Map<String, Object> map = new HashMap<>();
                //获取团单号
                FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(globalInput.getEnsureCode());
                map.put("grpOrderNo", fcGrpOrder.getGrpOrderNo());
                map.put("personID", personId);
                List<FCOrderInsured> fcOrderInsuredList = fcOrderInsuredMapper.selectList(map);
                if (fcOrderInsuredList.size() > 0) {
                    for (FCOrderInsured fcOrderInsured : fcOrderInsuredList) {
                        //订单项编号
                        String orderItemNo = fcOrderInsured.getOrderItemNo();
                        Map<String, Object> map1 = new HashMap<>();
                        map1.put("orderItemNo", orderItemNo);
                        //订单详情编号
                        List<FCOrderItem> fcOrderItemList = fcOrderItemMapper.selectList(map1);
                        for (FCOrderItem fcOrderItem : fcOrderItemList) {
                            String orderItemDetailNo = fcOrderItem.getOrderItemDetailNo();
                            //删除子订单详情表
                            fcOrderItemDetailMapper.deleteByKey(orderItemDetailNo);
                        }
                        //删除子订单表
                        fcOrderItemMapper.deleteByPrimaryKey(orderItemNo);
                        // 删除个人健康告知信息
                        fcPerImpartResultMapper.deleteByOrderItemNo(orderItemNo);
                        //删除被保人表
                        fcOrderInsuredMapper.deleteByPrimaryKey(orderItemNo);
                    }
                }
                fpInsurePlanMapper.deleteByPrimaryKey(insurePlanNo);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "投保计划删除成功");
            }
        } catch (Exception e) {
            log.info("投保计划删除失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "投保计划删除失败");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description个人保费汇总接口
     * @date 20:50 20:50
     * @modified
     */
    public String personPermTotal(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            // 获取福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(globalInput.getEnsureCode());
            Map<String, Object> dataMap = calStaffPolicyPremDetail(globalInput.getEnsureCode(), globalInput.getCustomNo(), fcEnsure.getEnsureType());
            if (dataMap != null) {
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "保费汇总查询成功");
                resultMap.put("data", dataMap);
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "保费汇总查询失败");
            }
        } catch (Exception e) {
            log.info("保费汇总查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保费汇总查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 选择计划页面保费汇总接口
     *
     * @param token
     * @param peopleInsurePlanInfos
     * @return
     */
    public String insurePremTrail(String token, List<PeopleInsurePlanInfo> peopleInsurePlanInfos) {
        // 结果数据容器
        Map<String, Object> dataMap = new HashMap<String, Object>();
        // 员工数据容器
        Map<String, Object> staffMap = new HashMap<>();
        // 家属数据容器
        List<Map<String, Object>> familyList = new ArrayList<Map<String, Object>>();
        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);
        String ensureCode = globalInput.getEnsureCode();
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        String ensureType = fcEnsure.getEnsureType();
        String perNo = globalInput.getCustomNo();
        FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
        // 获取员工的personId
        String staffPersonId = fcStaffFamilyRelaMapper.selectStaffPersonid(perNo);
        log.info("保费汇总信息：ensureCode-{},perNo-{}", ensureCode, perNo);

        // 获取注册表中的员工以及家属的福利额度
        Map<String, Object> params = new HashedMap();
        params.put("ensureCode", ensureCode);
        params.put("perNo", perNo);
        FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(params);
        // 获取福利缴费方式
        Map<String, Object> ensureMap = new HashMap<>();
        ensureMap.put("ensureCode", ensureCode);
        ensureMap.put("configNo", "008");
        FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(ensureMap);

        // 获取家属的公司补助总保费/学生则为学校补助总保费
        double familyGrpTotalePrem = 0.0;
        // 学生实际自付保费之和
        double totalSelPrem = 0.00;
        // 学校实际补助保费之和
        double totalGrpPrem = 0.00;

        if (StringUtils.isNotBlank(ensureType) && ensureType.equals("1")) {
            if (fcPerRegistDay.getStudentGrpPrem() != null) {
                familyGrpTotalePrem = fcPerRegistDay.getStudentGrpPrem();
            }
        } else {
            if (fcPerRegistDay.getFamilyGrpPrem() != null) {
                familyGrpTotalePrem = fcPerRegistDay.getFamilyGrpPrem();
            }
        }

        // 设置家属投保总保费
        double familyInsurePremSum = 0.00;

        for (PeopleInsurePlanInfo peopleInsurePlanInfo : peopleInsurePlanInfos) {
            Map<String, String> param = new HashMap<>();
            param.put("ensureCode", ensureCode);
            param.put("planCode", peopleInsurePlanInfo.getPlanCode());
            //修改为查询临时表（主表只会在确认投保时才会有数据）
            FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
            // 判断是否为员工
            if (StringUtils.isNotBlank(staffPersonId) && fcEnsure.getEnsureType().matches("^(0|2)")) { // 不为空则是员工投保
                if (staffPersonId.equals(peopleInsurePlanInfo.getPersonId())) {
                    /****** 员工 ****/
                    // 1、员工的公司补助保费
                    double staffGrpPrem = 0.0;
                    if (fcPerRegistDay.getStaffGrpPrem() != null) {
                        staffGrpPrem = fcPerRegistDay.getStaffGrpPrem();
                    }
                    // 2、员工的投保计划保费 也修改为查询临时表
                    double staffInsurePrem = fcEnsurePlanMapper.selectPlanPrem(ensureCode, peopleInsurePlanInfo.getPlanCode());
                    // 3、计算员工自付保费
                    double staffSelfPrem = 0.00;
                    if (staffInsurePrem > staffGrpPrem) {
                        staffSelfPrem = CommonUtil.sub(staffInsurePrem, staffGrpPrem);
                    } else {
                        staffGrpPrem = staffInsurePrem;
                    }
                    // 封装员工数据
                    staffMap.put("personId", staffPersonId);
                    staffMap.put("name", peopleInsurePlanInfo.getName());
                    staffMap.put("planName", fcEnsurePlan.getPlanName());
                    staffMap.put("staffInsurePrem", staffInsurePrem);
                    staffMap.put("staffDefaultPrem", staffGrpPrem);
                    staffMap.put("staffPrem", staffSelfPrem);
                    dataMap.put("staffMap", staffMap);
                } else {
                    /****** 家属 ****/
                    // 1、家属-公司补助保费
                    double familyGrpPrem = 0.0;
                    // 2、家属-选择计划保费
                    double familyInsurePrem = fcEnsurePlanMapper.selectPlanPrem(ensureCode,
                            peopleInsurePlanInfo.getPlanCode());
                    familyInsurePremSum = CommonUtil.add(familyInsurePremSum, familyInsurePrem);
                    // 3、计算家属自付保费
                    double familySelfPrem = 0.00;
                    // familyGrpTotalePrem是注册期表中的家属福利额度
                    if (familyInsurePrem > familyGrpTotalePrem) {
                        familyGrpPrem = familyGrpTotalePrem;
                        familySelfPrem = CommonUtil.sub(familyInsurePrem, familyGrpTotalePrem);
                        familyGrpTotalePrem = 0.0;
                    } else if (familyInsurePrem <= familyGrpTotalePrem) {
                        familyGrpPrem = familyInsurePrem;
                        familyGrpTotalePrem = CommonUtil.sub(familyGrpTotalePrem, familyInsurePrem);
                    }
                    // 封装单个家属数据
                    Map<String, Object> familyPremMap = new HashMap<>();
                    familyPremMap.put("name", peopleInsurePlanInfo.getName());
                    familyPremMap.put("personId", peopleInsurePlanInfo.getPersonId());
                    familyPremMap.put("familyInsurePrem", familyInsurePrem);
                    familyPremMap.put("familyDefaultPrem", familyGrpPrem);
                    familyPremMap.put("familyPrem", familySelfPrem);
                    familyPremMap.put("planName", fcEnsurePlan.getPlanName());
                    familyList.add(familyPremMap);
                }
            } else {
                /****** 学生 ****/
                // 1、学生-补助保费
                double familyGrpPrem = 0.0;
                // 2、学生-选择计划保费
                double familyInsurePrem = fcEnsurePlanMapper.selectPlanPrem(ensureCode,
                        peopleInsurePlanInfo.getPlanCode());
                // 3、计算学生自付保费
                double familySelfPrem = 0.00;
                if (familyInsurePrem > familyGrpTotalePrem) {
                    familyGrpPrem = familyGrpTotalePrem;
                    familySelfPrem = CommonUtil.sub(familyInsurePrem, familyGrpTotalePrem);
                    familyGrpTotalePrem = 0.0;
                } else if (familyInsurePrem <= familyGrpTotalePrem) {
                    familyGrpPrem = familyInsurePrem;
                    familyGrpTotalePrem = CommonUtil.sub(familyGrpTotalePrem, familyInsurePrem);
                }
                totalSelPrem += familySelfPrem;
                totalGrpPrem += familyGrpPrem;
                // 封装单个家属数据
                Map<String, Object> familyPremMap = new HashMap<>();
                familyPremMap.put("name", peopleInsurePlanInfo.getName());
                familyPremMap.put("personId", peopleInsurePlanInfo.getPersonId());
                familyPremMap.put("familyInsurePrem", familyInsurePrem);
                familyPremMap.put("familyDefaultPrem", familyGrpPrem);
                familyPremMap.put("familyPrem", familySelfPrem);
                familyPremMap.put("planName", fcEnsurePlan.getPlanName());
                familyList.add(familyPremMap);
            }
        }
        // 校验是否需要显示监护人付款信息
        if (fcEnsureConfig != null) {
            if ("3".equals(fcEnsureConfig.getConfigValue())) {
                String prem = fcEnsurePlanMapper.getSumTotalPrem(perNo, ensureCode); // 未付
                if (totalSelPrem > 0) {
                    dataMap.put("isCheck", "1");
                    dataMap.put("perInfo", fcPerInfo);
                }
                Double studentGrpPrem = fcPerRegistDay.getStudentGrpPrem();
                studentGrpPrem = studentGrpPrem == null ? 0.0 : studentGrpPrem;
                if (Double.parseDouble(prem) > (studentGrpPrem - totalGrpPrem)) {
                    dataMap.put("isCheck", "1");
                    dataMap.put("perInfo", fcPerInfo);
                }
            } else {
                dataMap.put("isCheck", "0");
                dataMap.put("perInfo", fcPerInfo);
            }
        } else {
            dataMap.put("isCheck", "0");
            dataMap.put("perInfo", fcPerInfo);
        }
        // 封装返回数据
        dataMap.put("payType", fcEnsureConfig.getConfigValue());
        dataMap.put("staffMap", staffMap);
        if (familyList.size() > 0) {
            dataMap.put("familyMap", familyList);
        }

        return JSONObject.toJSONString(dataMap);
    }

    public String personEflexPermTotal(String token) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            // 获取福利信息
            String ensureCode = globalInput.getEnsureCode();
            String perNo = globalInput.getCustomNo();
            //获取员工信息
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
            Map<String, Object> param = new HashMap<String, Object>();
            param.clear();
            param.put("perNo", perNo);
            param.put("ensureCode", ensureCode);
            List<Map<String, Object>> familyPremList = fPInsureEflexPlanMapper.getTotlePrem(param);
            if (familyPremList.size() < 1) {
                log.info("系统异常，没有被保人");// 没有被保人
                throw new RuntimeException();
            }
            Map<String, Object> dataMap = calStaffPolicyPremDetail(ensureCode, perNo, fcPerInfo, familyPremList);
            if (dataMap != null) {
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "保费汇总查询成功");
                resultMap.put("data", dataMap);
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "保费汇总查询失败");
            }
        } catch (Exception e) {
            log.info("保费汇总查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保费汇总查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 计算员工及其家属保费明细
     * 每个被保人都区分个人缴费和公司缴费
     *
     * @param ensureCode
     * @param perNo
     * @param ensureType 福利投保类型：0-企事业单位投保  1-在校学生投保 2-场地险投保
     * @return
     * <AUTHOR>
     */
    public Map<String, Object> calStaffPolicyPremDetail(String ensureCode, String perNo, String ensureType) {
        /*******************************数据容器**********************************/
        // 查询条件
        Map<String, Object> params = new HashMap<String, Object>();
        // 结果数据容器
        Map<String, Object> dataMap = new HashMap<String, Object>();
        // 员工数据容器
        Map<String, Object> staffMap = new HashMap<String, Object>();
        // 家属数据容器
        List<Map<String, Object>> familyList = new ArrayList<Map<String, Object>>();
        //家属投保个数
        int i = 0;
        try {
            /********************************准备数据*********************************/
            //获取监护人信息
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);

            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
            // 获取福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            // 获取员工的personId
            String staffPersonId = "";
            if (fcStaffFamilyRela != null) {
                staffPersonId = fcStaffFamilyRela.getPersonID();
            }
            // 获取员工注册期表数据
            params.clear();
            params.put("ensureCode", ensureCode);
            params.put("perNo", perNo);
            FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(params);
            //获取福利缴费方式
            Map<String, Object> ensureMap = new HashMap<>();
            ensureMap.put("ensureCode", ensureCode);
            ensureMap.put("configNo", "008");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(ensureMap);
            /*****************************************业务处理*********************************/
            params.clear();
            params.put("perNo", perNo);
            params.put("ensureCode", ensureCode);
            List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectFPInsurePlans(params);
            //获取家属的公司补助总保费(学生-学校补助总保费)
            double familyGrpTotalePrem = 0.0;
            //学生实际自付保费之和
            double totalSelPrem = 0.00;
            //学校实际补助保费之和
            double totalGrpPrem = 0.00;
            if (ensureType == null || ensureType.equals("")) {
                if (fcPerRegistDay.getFamilyGrpPrem() != null) {
                    familyGrpTotalePrem = fcPerRegistDay.getFamilyGrpPrem();
                }
            } else {
                if (ensureType.equals("0") || ensureType.equals("2")) {
                    if (fcPerRegistDay.getFamilyGrpPrem() != null) {
                        familyGrpTotalePrem = fcPerRegistDay.getFamilyGrpPrem();
                    }
                } else if (ensureType.equals("1")) {
                    if (fcPerRegistDay.getStudentGrpPrem() != null) {
                        familyGrpTotalePrem = fcPerRegistDay.getStudentGrpPrem();
                    }
                }
            }
            //设置家属投保总保费
            double familyInsurePremSum = 0.00;
            for (FPInsurePlan fpInsurePlan : fpInsurePlanList) {
                Map<String, String> param = new HashMap<>();
                param.put("ensureCode", ensureCode);
                param.put("planCode", fpInsurePlan.getPlanCode());
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(fpInsurePlan.getPersonId());
                /******** 判断是否为员工*******************/
                if (StringUtils.isNotBlank(staffPersonId) && ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType()))) { //不为空则是员工投保
                    if (staffPersonId.equals(fpInsurePlan.getPersonId())) {
                        // 1、员工的公司补助保费
                        double staffGrpPrem = 0.0;
                        if (fcPerRegistDay.getStaffGrpPrem() != null) {
                            staffGrpPrem = fcPerRegistDay.getStaffGrpPrem();
                        }
                        // 2、员工的投保计划保费
                        double staffInsurePrem = fcEnsurePlanMapper.selectPlanPrem(fpInsurePlan.getEnsureCode(), fpInsurePlan.getPlanCode());
                        // 3、计算员工自付保费
                        double staffSelfPrem = 0.00;
                        if (staffInsurePrem > staffGrpPrem) {
                            staffSelfPrem = CommonUtil.sub(staffInsurePrem, staffGrpPrem);
                        } else {
                            staffGrpPrem = staffInsurePrem;
                        }
                        // 封装员工数据
                        staffMap.put("personId", staffPersonId);
                        staffMap.put("name", fcPerson.getName());
                        staffMap.put("planName", fcEnsurePlan.getPlanName());
                        staffMap.put("staffInsurePrem", staffInsurePrem);
                        staffMap.put("staffDefaultPrem", staffGrpPrem);
                        staffMap.put("staffPrem", staffSelfPrem);
                        dataMap.put("staffMap", staffMap);
                    } else {
                        /****** 家属 ****/
                        // 1、家属-公司补助保费
                        double familyGrpPrem = 0.0;
                        // 2、家属-选择计划保费
                        double familyInsurePrem = fcEnsurePlanMapper.selectPlanPrem(fpInsurePlan.getEnsureCode(), fpInsurePlan.getPlanCode());
                        familyInsurePremSum = CommonUtil.add(familyInsurePremSum, familyInsurePrem);
                        // 3、计算家属自付保费
                        double familySelfPrem = 0.00;
                        //familyGrpTotalePrem是注册期表中的家属福利额度
                        if (familyInsurePrem > familyGrpTotalePrem) {
                            familyGrpPrem = familyGrpTotalePrem;
                            familySelfPrem = CommonUtil.sub(familyInsurePrem, familyGrpTotalePrem);
                            familyGrpTotalePrem = 0.0;
                        } else if (familyInsurePrem <= familyGrpTotalePrem) {
                            familyGrpPrem = familyInsurePrem;
                            familyGrpTotalePrem = CommonUtil.sub(familyGrpTotalePrem, familyInsurePrem);
                        }
                        // 封装单个家属数据
                        Map<String, Object> familyPremMap = new HashMap<>();
                        familyPremMap.put("name", fcPerson.getName());
                        familyPremMap.put("personId", fpInsurePlan.getPersonId());
                        familyPremMap.put("familyInsurePrem", familyInsurePrem);
                        familyPremMap.put("familyDefaultPrem", familyGrpPrem);
                        familyPremMap.put("familyPrem", familySelfPrem);
                        familyPremMap.put("planName", fcEnsurePlan.getPlanName());
                        familyList.add(familyPremMap);
                        //判断家属是否投保
                        i += fpInsurePlanMapper.selectStaffFamilyInsured(perNo, fcPerson.getPersonID());
                    }
                } else {//学生投保
                    // 1、学生-补助保费
                    double familyGrpPrem = 0.0;
                    // 2、学生-选择计划保费
                    double familyInsurePrem = fcEnsurePlanMapper.selectPlanPrem(fpInsurePlan.getEnsureCode(), fpInsurePlan.getPlanCode());
                    // 3、计算学生自付保费
                    double familySelfPrem = 0.00;
                    if (familyInsurePrem > familyGrpTotalePrem) {
                        familyGrpPrem = familyGrpTotalePrem;
                        familySelfPrem = CommonUtil.sub(familyInsurePrem, familyGrpTotalePrem);
                        familyGrpTotalePrem = 0.0;
                    } else if (familyInsurePrem <= familyGrpTotalePrem) {
                        familyGrpPrem = familyInsurePrem;
                        familyGrpTotalePrem = CommonUtil.sub(familyGrpTotalePrem, familyInsurePrem);
                    }

                    totalSelPrem += familySelfPrem;
                    totalGrpPrem += familyGrpPrem;

                    // 封装单个家属数据
                    Map<String, Object> familyPremMap = new HashMap<>();
                    familyPremMap.put("name", fcPerson.getName());
                    familyPremMap.put("personId", fpInsurePlan.getPersonId());
                    familyPremMap.put("familyInsurePrem", familyInsurePrem);
                    familyPremMap.put("familyDefaultPrem", familyGrpPrem);
                    familyPremMap.put("familyPrem", familySelfPrem);
                    familyPremMap.put("planName", fcEnsurePlan.getPlanName());
                    familyList.add(familyPremMap);
                }
            }
            //如果是员工投保，则判断
            //判断家属是否投过保，若未投保则不展示
            if (i != 0) {
                if ("0".equals(ensureType) && null != fcPerRegistDay.getFamilyGrpPrem()) {
                    if (familyInsurePremSum >= fcPerRegistDay.getFamilyGrpPrem()) {
                        dataMap.put("familyInsurePremResult", fcPerRegistDay.getFamilyGrpPrem());
                    } else {
                        dataMap.put("familyInsurePremResult", familyInsurePremSum);
                    }
                } else if ("0".equals(ensureType) && null == fcPerRegistDay.getFamilyGrpPrem()) {
                    dataMap.put("familyInsurePremResult", 0);
                }
            }
            //校验是否需要显示监护人付款信息
            if (fcEnsureConfig != null) {
                if ("3".equals(fcEnsureConfig.getConfigValue())) {
                    String prem = fcEnsurePlanMapper.getSumTotalPrem(perNo, ensureCode); //未付
                    if (totalSelPrem > 0) {
                        dataMap.put("isCheck", "1");
                        dataMap.put("perInfo", fcPerInfo);
                    }
                    Double studentGrpPrem = fcPerRegistDay.getStudentGrpPrem();
                    studentGrpPrem = studentGrpPrem == null ? 0.0 : studentGrpPrem;
                    if (Double.parseDouble(prem) > (studentGrpPrem - totalGrpPrem)) {
                        dataMap.put("isCheck", "1");
                        dataMap.put("perInfo", fcPerInfo);
                    }
                } else {
                    dataMap.put("isCheck", "0");
                    dataMap.put("perInfo", fcPerInfo);
                }
            } else {
                dataMap.put("isCheck", "0");
                dataMap.put("perInfo", fcPerInfo);
            }
            // 封装返回数据
            dataMap.put("payType", fcEnsureConfig.getConfigValue());
            dataMap.put("staffMap", staffMap);
            if (familyList.size() > 0) {
                dataMap.put("familyMap", familyList);
            }
        } catch (Exception e) {
            log.info("计算缴费明细失败：", e);
            return null;
        }
        return dataMap;
    }

    /**
     * 个人投保确认 完成个人投保信息从暂存表到订单表的一系列操作 涉及表--按存储顺序依次为：
     * 投保人表FCPerAppnt、订单表FCOrder、子订单表FCOrderItem、子订单产品要素详情表FCOrderItemDetail、
     * 被保人表FCOrderInsured、订单轨迹表FCOrderLocus
     *
     * @param token
     * @return
     */
    @Transactional
    public String personInsureConfirm(String token, String verifyCode, String isCheck, String orderSource) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        GlobalInput globalInput = userService.getSession(token);
        Map<String, Object> param = new HashMap<String, Object>();
        boolean flag = false;
        try {
            /************************************* 准备数据 *********************************************/
            String perNo = globalInput.getCustomNo();
            String ensureCode = globalInput.getEnsureCode();
            String grpNo = globalInput.getGrpNo();
            Map<String, Object> fpMap = new HashMap<>();
            fpMap.put("perNo", perNo);
            fpMap.put("ensureCode", ensureCode);
            log.info("perNo:" + perNo + "=====ensureCode" + ensureCode);
            //判断是否需要提供付款信息
            if (StringUtils.isNotBlank(isCheck) && isCheck.equals("1")) {
                //获取缴费方式(判断支付方式是否为批扣)
                List<Map<String, String>> payTypeList = fcGrpOrderMapper.getPayType(grpNo, ensureCode);
                log.info("payTypeList.Size()" + payTypeList.size());
                if (payTypeList != null && payTypeList.size() > 0) {
                    Map<String, String> payTypeMap = payTypeList.get(0);
                    if (payTypeMap != null) {
                        log.info("payTypeMap.get(configValue)" + payTypeMap.get("configValue"));
                        String payType = payTypeMap.get("configValue");
                        if (payType.equals("3")) {//批扣
                            flag = true;
                            //调用签约确认接口
                            if (verifyCode == null || verifyCode.equals("")) {
                                resultMap.put("code", "500");
                                resultMap.put("message", "请输入短信验证码");
                                return JSON.toJSONString(resultMap);
                            } else {
                                List<FcBatchPayBankInfo> selectFcBatchPayBankInfo = fcEnsureMapper.selectFcBatchPayBankInfo(fpMap);
                                if (selectFcBatchPayBankInfo == null || selectFcBatchPayBankInfo.size() == 0) {
                                    resultMap.put("code", "500");
                                    resultMap.put("success", false);
                                    resultMap.put("message", "请发送短信验证码");
                                    return JSON.toJSONString(resultMap);
                                } else {
                                    FcBatchPayBankInfo fcBatchPayBankInfo = selectFcBatchPayBankInfo.get(0);
                                    SignConfirm signConfirm = new SignConfirm();
                                    signConfirm.setSignNo(fcBatchPayBankInfo.getSignSN());
                                    signConfirm.setSource(myProps.getSourse());
                                    signConfirm.setVerifyCode(verifyCode);
                                    String confirmParam = JSONObject.toJSONString(signConfirm);
                                    String confirmResponseDate = HttpUtil.postHttpRequestJson(myProps.getSignConfirmSererUrl(), confirmParam);
                                    log.info("confirmResponseDate：" + confirmResponseDate);
                                    if (!confirmResponseDate.equals("")) {
                                        JSONObject jsStr = JSONObject.parseObject(confirmResponseDate);
                                        SignConfirmResponse signConfirmResponse = JSONObject.toJavaObject(jsStr, SignConfirmResponse.class);
                                        if (signConfirmResponse.getCode().equals("200") && signConfirmResponse.isSuccess()) {
                                            SignConfirmReturnData confirmData = signConfirmResponse.getData();
                                            if (confirmData == null) {
                                                resultMap.put("code", "500");
                                                resultMap.put("success", false);
                                                resultMap.put("message", "签约确认失败");
                                                return JSON.toJSONString(resultMap);
                                            } else {
                                                String signState = confirmData.getSignState();
                                                if (signState == null || signState.equals("")) {
                                                    resultMap.put("code", "500");
                                                    resultMap.put("success", false);
                                                    resultMap.put("message", "签约确认失败");
                                                    return JSON.toJSONString(resultMap);
                                                } else {
                                                    if (!signState.equals("2")) {
                                                        resultMap.put("code", "500");
                                                        resultMap.put("success", false);
                                                        resultMap.put("message", "签约确认失败");
                                                        return JSON.toJSONString(resultMap);
                                                    } else {
                                                        //调用签约确认成功，保存信息
                                                        updateFcBatchPayBankInfo(fpMap, "Y");
                                                        log.info("签约成功 ！！");
                                                    }
                                                }
                                            }
                                        } else {
                                            resultMap.put("code", "500");
                                            resultMap.put("success", false);
                                            resultMap.put("message", "失败：" + signConfirmResponse);
                                            return JSON.toJSONString(resultMap);
                                        }
                                    } else {
                                        resultMap.put("code", "500");
                                        resultMap.put("success", false);
                                        resultMap.put("message", "签约确认失败");
                                        return JSON.toJSONString(resultMap);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            // 获取福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(globalInput.getEnsureCode());
            /***************************** 确认所有被保人所投计划是否符合投保规则***********************************/
            String ensureType = fcEnsure.getEnsureType();
            //获取员工(监护人)下所有被保人（学生）的personid和plancode
            List<Map<String, String>> personIdAndPlanCode = fcEnsureMapper.getPersonIdAndPlanCode(ensureCode, perNo);
            if (personIdAndPlanCode == null || personIdAndPlanCode.size() < 1) {// 没有被保人
                log.info("系统异常，没有被保人");
                throw new RuntimeException();
            } else {
                for (Map<String, String> map : personIdAndPlanCode) {
                    String personId = map.get("personId");
                    String planCode = map.get("planCode");
                    String personName = map.get("personName");
                    Map<String, Object> checkTBRules = checkTBRulesPlan(globalInput.getEnsureCode(), personId, planCode);
                    String code = checkTBRules.get("code").toString();
                    if (!code.equals("200")) {//投保规则校验未通过
                        resultMap.put("code", checkTBRules.get("code").toString());
                        resultMap.put("message", "被保人" + personName + "，" + checkTBRules.get("message"));
                        return JSON.toJSONString(resultMap);
                    }
                }
            }
            //固定，弹性计划，个人投保之后状态修改为待生效，日常计划为待提交核心 01-待提交核心 08-待生效
            //删除之前订单涉及到订单状态，故而添加判断,日常计划 订单状态-01,固定弹性计划 订单状态-08
            String OrderStatus = "";
            if (fcEnsure.getPlanType().equals(PlanTypeEnum.DAILYPLAN.getCode())) {
                OrderStatus = "01";
            } else {
                OrderStatus = "08";
            }
            List<FCOrder> fcOrderList = fcOrderMapper.selectPerNo(perNo, OrderStatus, globalInput.getEnsureCode());
            if (fcOrderList.size() > 0) {
                //删除之前的订单
                String deletConfirmInfo = deleteConfirmInfo(fcOrderList);
                if (!("".equals(deletConfirmInfo))) {
                    resultMap.put("code", "0");
                    resultMap.put("message", "删除订单失败");
                    return JSON.toJSONString(resultMap);
                }
            }
            //获取代理人信息
            List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
            FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);
            // 根据福利编号获取团体保单信息
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(fcEnsure.getEnsureCode());
            /*
             * 1、投保人表FCPerAppnt 从 员工表FCPerInfo取数据 2、一个员工家庭在订单表FCOrder表创建一条数据
             */
            // 1、投保人表FCPerAppnt
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
            FCPerAppnt fcPerAppnt = new FCPerAppnt();
            String perAppNo = maxNoService.createMaxNo("PerAppNo", "", 20);
            fcPerAppnt.setPerAppNo(perAppNo);
            fcPerAppnt.setGrpNo(globalInput.getGrpNo());
            fcPerAppnt.setPerNo(globalInput.getCustomNo());
            fcPerAppnt.setName(fcPerInfo.getName());
            fcPerAppnt.setSex(fcPerInfo.getSex());
            fcPerAppnt.setIDType(fcPerInfo.getIDType());
            fcPerAppnt.setIDNo(fcPerInfo.getIDNo());
            fcPerAppnt.setBirthDay(fcPerInfo.getBirthDay());
            fcPerAppnt.setOperator(globalInput.getUserNo());
            fcPerAppnt = (FCPerAppnt) CommonUtil.initObject(fcPerAppnt, "INSERT");
            fcPerAppntMapper.insert(fcPerAppnt);
            // 2、订单表FCOrder
            param.clear();
            param.put("ensureCode", globalInput.getEnsureCode());
            param.put("perNo", globalInput.getCustomNo());
            param.put("personType", "1");
            param.put("isValidy", "1");
            List<FCPerRegistDay> fcPerRegistDayList = fcPerRegistDayMapper.selectFCPerRegistDayList(param);
            if (fcPerRegistDayList.size() != 1) {
                // 员工开放投保信息表数据异常，不能创建订单
                log.info("员工开放投保信息表数据异常，不能创建订单");
                throw new RuntimeException();
            }
            FCPerRegistDay fcPerRegistDay = fcPerRegistDayList.get(0);
            FCOrder fcOrder = new FCOrder();
            //System.out.println("===============" + DateTimeUtil.getTimeString().substring(1, 5));
            String orderNo = maxNoService.createMaxNo("OrderNo", DateTimeUtil.getTimeString().substring(0, 4), 8);
            fcOrder.setOrderNo(orderNo);
            fcOrder.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
            //固定，弹性计划，个人投保之后状态修改为待生效，日常计划为待提交核心 01-待提交核心 08-待生效
            if (fcEnsure.getPlanType().equals(PlanTypeEnum.DAILYPLAN.getCode())) {
                fcOrder.setOrderStatus("01");
            } else {
                fcOrder.setOrderStatus("08");
            }
            fcOrder.setOrderType("01");
            fcOrder.setOrderSource(orderSource);
            fcOrder.setOpenDay(fcPerRegistDay.getOpenDay());
            fcOrder.setCloseDay(fcPerRegistDay.getCloseDay());
            fcOrder.setGrpNo(globalInput.getGrpNo());
            fcOrder.setPerNo(globalInput.getCustomNo());
            fcOrder.setPerAppNo(perAppNo);
            fcOrder.setCommitDate(DateTimeUtil.getCurrentDate());
            fcOrder.setClientNo(fcEnsure.getClientNo());
            fcOrder.setOperator(globalInput.getUserNo());
            fcOrder = (FCOrder) CommonUtil.initObject(fcOrder, "INSERT");
            fcOrderMapper.insert(fcOrder);
            //更新fcbatchpaybankinfo的订单号
            if (flag) {//缴费方式为批扣
                updateOrderNoFcBatchPayBankInfo(null, fpMap, orderNo);
            }
            // 6、订单轨迹表FCOrderLocus
            FCOrderLocus fcOrderLocus = new FCOrderLocus();
            String orderLocusSN = maxNoService.createMaxNo("orderLocusSN", "", 20);
            fcOrderLocus.setOrderLocusSN(orderLocusSN);
            fcOrderLocus.setOrderNo(orderNo);
            fcOrderLocus.setOrderStatus("01");
            fcOrderLocus.setOperator(globalInput.getUserNo());
            fcOrderLocus = (FCOrderLocus) CommonUtil.initObject(fcOrderLocus, "INSERT");
            fcOrderLocusMapper.insert(fcOrderLocus);
            param.clear();
            param.put("perNo", globalInput.getCustomNo());
            param.put("ensureCode", globalInput.getEnsureCode());
            List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectFPInsurePlans(param);
            if (fpInsurePlanList.size() < 1) {
                // 没有被保人
                log.info("系统异常，没有被保人");
                throw new RuntimeException();
            }
            //获取拆分保费：个人缴费+公司缴费
            Map<String, Object> dataMap = calStaffPolicyPremDetail(globalInput.getEnsureCode(),
                    globalInput.getCustomNo(),
                    ensureType);
            if (dataMap == null) {
                log.info("系统异常，缴费明细计算失败");
                throw new RuntimeException();
            }

            Map<String, Object> staffMap = (HashMap) dataMap.get("staffMap");
            List<Map<String, Object>> familyList = (ArrayList<Map<String, Object>>) dataMap.get("familyMap");
            Map<String, Map<String, Object>> familyMaps = new HashMap<String, Map<String, Object>>();
            if (familyList != null && familyList.size() > 0) {
                for (Map<String, Object> map : familyList) {
                    familyMaps.put((String) map.get("personId"), map);
                }
            }
            for (FPInsurePlan fpInsurePlan : fpInsurePlanList) {
                // 3、子订单表FCOrderItem
                FCOrderItem fcOrderItem = new FCOrderItem();
                String orderItemNo = maxNoService.createMaxNo("OrderItemNo", "", 20);
                String orderItemDetailNo = maxNoService.createMaxNo("OrderItemDetailNo", "", 20);
                fcOrderItem.setOrderItemNo(orderItemNo);
                fcOrderItem.setOrderItemDetailNo(orderItemDetailNo);
                fcOrderItem.setOrderNo(orderNo);
				/* 个人保单号生成规则：
				1、前缀99；
				2、3-4位截取保单管理机构的3、4位，如为总公司则取86（如管理机构为864498取44）；
				3、5-6位截取年份后两位（如2018取18）；
				4、7-15位取每个自然年度流水号；
				5、最后一位固定为8；
				例如弹性平台今年第一张个人保险凭证号应为：9944180000000018。
				*/
                String rule = "99" + fdAgentInfo.getManageCom().substring(2, 4) + DateTimeUtil.getCurrentYear().substring(2, 4);
                String contNo = maxNoService.createMaxNo("ContNo" + DateTimeUtil.getCurrentYear(), rule, 9);
                contNo = contNo + "8";
                fcOrderItem.setContNo(contNo);
                if (!staffMap.isEmpty()) {
                    if (fpInsurePlan.getPersonId().equals(staffMap.get("personId"))) {
                        fcOrderItem.setSelfPrem((double) staffMap.get("staffPrem"));
                        fcOrderItem.setGrpPrem((double) staffMap.get("staffDefaultPrem"));
                    }
                }
                if (!familyMaps.isEmpty()) {
                    if (familyMaps.get(fpInsurePlan.getPersonId()) != null) {
                        Map<String, Object> familyMap = familyMaps.get(fpInsurePlan.getPersonId());
                        fcOrderItem.setSelfPrem((double) familyMap.get("familyPrem"));
                        fcOrderItem.setGrpPrem((double) familyMap.get("familyDefaultPrem"));
                    }
                }

                fcOrderItem.setOperator(globalInput.getUserNo());
                fcOrderItem = (FCOrderItem) CommonUtil.initObject(fcOrderItem, "INSERT");
                fcOrderItemMapper.insert(fcOrderItem);
                // 4、子订单产品要素详情表FCOrderItemDetail
                FCOrderItemDetail fcOrderItemDetail = new FCOrderItemDetail();
                fcOrderItemDetail.setOrderItemDetailNo(orderItemDetailNo);
                fcOrderItemDetail.setProductCode(fpInsurePlan.getPlanCode());
                fcOrderItemDetail.setEnsureCode(ensureCode);
                fcOrderItemDetail.setProductEleCode("001");
                // fcOrderItemDetail.setValue("计划对象"); 在 mapper.xml里已处理
                fcOrderItemDetail.setOperator(globalInput.getUserNo());
                fcOrderItemDetail = (FCOrderItemDetail) CommonUtil.initObject(fcOrderItemDetail, "INSERT");
                fcOrderItemDetailMapper.insert(fcOrderItemDetail);
                // 5、被保人表FCOrderInsured
                FCOrderInsured fcOrderInsured = new FCOrderInsured();
                fcOrderInsured.setOrderItemNo(orderItemNo);
                fcOrderInsured.setOrderNo(orderNo);
                fcOrderInsured.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
                FCPerson person = fcPersonMapper.selectByPrimaryKey(fpInsurePlan.getPersonId());
                fcOrderInsured.setPersonID(person.getPersonID());
                fcOrderInsured.setName(person.getName());
                fcOrderInsured.setSex(person.getSex());
                fcOrderInsured.setBirthDay(person.getBirthDate());
                fcOrderInsured.setIDType(person.getIDType());
                fcOrderInsured.setIDNo(person.getIDNo());
                fcOrderInsured.setMobilePhone(person.getMobilePhone());
                fcOrderInsured.setPhone(person.getPhone());
                fcOrderInsured.setDepartment(fcPerInfo.getDepartment());
                fcOrderInsured.setOccupationType(person.getOccupationType());
                fcOrderInsured.setOccupationCode(person.getOccupationCode());
                fcOrderInsured.setJoinMedProtect(person.getJoinMedProtect());
                fcOrderInsured.setMedProtectType(person.getMedProtectType());
                fcOrderInsured.setEMail(person.getEMail());
                fcOrderInsured.setAddress(person.getAddress());
                fcOrderInsured.setOperator(globalInput.getUserNo());
                fcOrderInsured = (FCOrderInsured) CommonUtil.initObject(fcOrderInsured, "INSERT");
                fcOrderInsuredMapper.insert(fcOrderInsured);
                // 6、个人健康告知信息表（记录需要健康告知的人员信息）
                long age = Integer.valueOf(DateTimeUtil.getCurrentAge(person.getBirthDate(), fcEnsure.getCvaliDate()));
                if (age > 0) {
                    age = age * 365;
                } else {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date fDate = sdf.parse(person.getBirthDate());
                    Date oDate = sdf.parse(sdf.format(new Date()));
                    age = (oDate.getTime() - fDate.getTime()) / (1000 * 3600 * 24);
                }
                if (ageValidate(age, fpInsurePlan.getPlanCode(), ensureCode)) {
                    FcPerImpartResult fcPerImpartResult = new FcPerImpartResult();
                    fcPerImpartResult.setOrderItemNo(orderItemNo);
                    fcPerImpartResultMapper.insertSameResultImpartList(fcPerImpartResult);
                }

                //修改投保状态，0-未提交订单表 1-已提交订单表 2-核心承保成功
                fpInsurePlan.setInsureState("1");
                //修改投保临时表订单状态
                fpInsurePlanMapper.updateByPrimaryKey(fpInsurePlan);
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "订单提交成功");
        } catch (Exception e) {
            log.info("订单提交失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "订单提交失败");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * <AUTHOR>
     * @description累计风险保额
     * @date 20:28 20:28
     * @modified
     */
    public String getRiskAmnt(String personId) {
        try {
            // 获取个人信息
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
            if (fcPerson == null) {
                log.info("风险保额接口personId==" + personId + "不存在。");
                return "";
            }
            String requestXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
                    //交易流水号
                    "\t\t<TransRefGUID>" + CommonUtil.getUUID() + "</TransRefGUID>\n" +
                    // 接口交易类型
                    "\t\t<TransType>BF0002</TransType>\n" +
                    // 交易日期
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    // 交易时间
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<InsuredInfo>\n" +
                    "\t\t<InsuredName>" + fcPerson.getName() + "</InsuredName>\n" +
                    "\t\t<Sex>" + fcPerson.getSex() + "</Sex>\n" +
                    "\t\t<CertiType>" + fcPerson.getIDType() + "</CertiType>\n" +
                    "\t\t<CertiCode>" + fcPerson.getIDNo() + "</CertiCode>\n" +
                    "\t\t<Birthday>" + fcPerson.getBirthDate() + "</Birthday>\n" +
                    // 核心固定值 5-表示未成年人身故风险保额
                    "\t\t<AmountType>5</AmountType>\t\n" +
                    "\t</InsuredInfo>\n" +
                    "</RequestInfo>";
            log.info("累计风险保额请求报文：" + requestXml);
            long a = System.currentTimeMillis();
            // 调用核心接口
            RemoteDelegate remoteDelegate = RemoteDelegate.getInstance();
            boolean success = remoteDelegate.submitData(myProps.getServiceInfo().get("serviceUrl"),
                    myProps.getServiceInfo().get("operation"), "", requestXml);
            long b = System.currentTimeMillis();
            log.info("连接核心接口所用时间" + ((a - b) / 1000.0) + "秒");
            if (success) {
                Map<String, Object> responseXml = remoteDelegate.getResult();
                log.info("调用核心接口查询风险保额返回报文：" + JSON.toJSONString(responseXml));
                return ((RiskInfo) responseXml.get("RiskInfo")).getAmount();
            }
            return "";
        } catch (Exception e) {
            log.info("", e);
            return "";
        }
    }

    /**
     * 拆分保费
     *
     * @param planCode
     * @param companyPrem 公司缴费金额
     * @return 举例说明：
     * 某被保人，选择的保险计划下，存在A、B、C三个险种（险种编码顺序按A、B、C排列），总保费为3000元，其中A险种保费为1800元，B险种保费为1000元，C险种保费为200元；公司缴费金额为1000元，个人缴费金额为2000元；
     * <p>
     * 按险种编码顺序：
     * A险种，保费占比为：1800/3000=60%；
     * B险种，保费占比为：1000/3000=33.333333%；（保留6位小数，注意是百分号后6位）
     * C险种，保费占比为：1 – 60% - 33.333333% = 6.666667%；（最后一个险种，采用差额计算）
     * <p>
     * 所以A险种对应的公司缴费金额为：1000 × 60% = 600元；个人缴费金额为：1800 - 600 = 1200元；
     * B险种对应的公司缴费金额为：1000 × 33.333333% = 333.33元（金额保留两位小数）；个人缴费金额为：1000 - 333.33 = 666.67元；
     * C险种对应的公司缴费金额为：1000 – 600 – 333.33 = 66.67元（金额保留两位小数）；个人缴费金额为：2000 – 1200 – 666.67 = 133.33元；
     */
    public Map<String, Map<String, Map<String, Double>>> splitPremium(String ensureCode, String planCode, double companyPrem) {
        // 先判断是不是默认计划，如果是，则公司缴费为默认计划保费， 个人缴费为0 。 不需要计算
        // 1、 获取计划险种列表 ,按险种编码排序
        // 2、 查询公司缴费，即默认计划保费
        // 3、 查询个人缴费。 计划缴费- 公司缴费
        // 4、 计算险种公司缴费比例
        // 5、 计算单个险种公司缴费金额
        // 6、 计算单个险种个人缴费金额
        // 7、 计算最后一个险种的公司缴费和个人缴费
        Map<String, Map<String, Map<String, Double>>> riskMap = new HashMap<String, Map<String, Map<String, Double>>>();
        Map<String, Map<String, Double>> dutyMap = new HashMap<String, Map<String, Double>>();
        // 参数容器
        Map<String, Object> params = new HashMap<String, Object>();
        // 获取险种名称集合，查询时按顺序排序
        List<String> riskCodeList = fcPlanRiskMapper.selectRiskCodeList(ensureCode, planCode);

        // 计划总保费
        double totalPrem = fcPlanRiskDutyMapper.selectTotalPremPlan(ensureCode, planCode);
        //需要缴费<=实际缴费  个人不需要缴费
        if (totalPrem <= companyPrem) {
            return planPrem(ensureCode, planCode);
        }
        // 个人缴费金额
        double personPrem = CommonUtil.sub(totalPrem, companyPrem);
        // 获取 计划下 每个险种的保费 存入riskPremMap
        List<Map<String, Object>> temp_riskPremMap = fcPlanRiskDutyMapper.selectRiskPremMap(ensureCode, planCode);
        Map<String, Double> riskPremMap = new HashMap<String, Double>();
        for (int i = 0; i < temp_riskPremMap.size(); i++) {
            riskPremMap.put(String.valueOf(temp_riskPremMap.get(i).get("RiskCode")), Double.parseDouble(temp_riskPremMap.get(i).get("Prem").toString()));
        }
        // 存储 除最后一款险种外的保费占比
        double premRatioCount = 0.0;
        // 存储 个人缴费
        double personRiskPremCount = 0.0;
        // 储存 公司缴费
        double companyRiskPremCount = 0.0;
        for (int i = 0; i < riskCodeList.size() - 1; i++) {
            String riskCode = riskCodeList.get(i);
            // 险种，保费占比为：1800/3000=60%； 小数点后精确8位数  ROUND_HALF_UP 四舍五入
            double premRatio = CommonUtil.div(riskPremMap.get(riskCode), totalPrem, 8);
            premRatioCount = CommonUtil.add(premRatioCount, premRatio);
            // 公司缴费 = 公司保费 * 险种保费占比
            double companyRiskPrem = CommonUtil.mul(companyPrem, premRatio);
            companyRiskPrem = new BigDecimal(companyRiskPrem).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            companyRiskPremCount = CommonUtil.add(companyRiskPremCount, companyRiskPrem);
            // 个人缴费 = 险种保费 - 公司缴费
            double personRiskPrem = CommonUtil.sub(riskPremMap.get(riskCode), companyRiskPrem);
            personRiskPremCount = CommonUtil.add(personRiskPremCount, personRiskPrem);
            params.clear();
            params.put("planCode", planCode);
            params.put("riskCode", riskCode);
            params.put("ensureCode", ensureCode);
            List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyList(params);
            if (fcPlanRiskDutyList.size() <= 1) {
                // 存最终结果 责任层级的 公司缴费 和 个人缴费
                Map<String, Double> premMap = new HashMap<String, Double>();
                premMap.put("1", companyRiskPrem);
                premMap.put("2", personRiskPrem);
                dutyMap.put(fcPlanRiskDutyList.get(0).getDutyCode(), premMap);
                riskMap.put(riskCode, dutyMap);
            } else {
                // 多责任的险种则需要计算责任层保费(最后一个责任单独计算)
                // 记录责任保费
                double companyDutyPremCount = 0.0;
                double personDutyPremCount = 0.0;
                for (int j = 0; j < fcPlanRiskDutyList.size() - 1; j++) {
                    // 存最终结果 责任层级的 公司缴费 和 个人缴费
                    Map<String, Double> premMap = new HashMap<String, Double>();
                    FCPlanRiskDuty fcPlanRiskDuty = fcPlanRiskDutyList.get(j);
                    // 计算责任保费占比
                    double dutyPremRatio = CommonUtil.div(fcPlanRiskDuty.getPrem(), riskPremMap.get(riskCode), 8);
                    // 责任层公司缴费 = 险种层公司缴费 * 责任保费占比
                    double companyDutyPrem = CommonUtil.mul(companyRiskPrem, dutyPremRatio);
                    companyDutyPrem = new BigDecimal(companyDutyPrem).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                    double personDutyPrem = CommonUtil.sub(fcPlanRiskDuty.getPrem(), companyDutyPrem);
                    companyDutyPremCount = CommonUtil.add(companyDutyPremCount, companyDutyPrem);
                    personDutyPremCount = CommonUtil.add(personDutyPremCount, personDutyPrem);
                    premMap.put("1", companyDutyPrem);
                    premMap.put("2", personDutyPrem);
                    dutyMap.put(fcPlanRiskDuty.getDutyCode(), premMap);
                }

                // 单独计算最后一个责任
                FCPlanRiskDuty fcPlanRiskDuty = fcPlanRiskDutyList.get(fcPlanRiskDutyList.size() - 1);
                // 存最终结果 责任层级的 公司缴费 和 个人缴费
                Map<String, Double> premMap = new HashMap<String, Double>();
                // 公司缴费
                premMap.put("1", CommonUtil.sub(companyRiskPrem, companyDutyPremCount));
                // 个人缴费
                premMap.put("2", CommonUtil.sub(personRiskPrem, personDutyPremCount));
                dutyMap.put(fcPlanRiskDuty.getDutyCode(), premMap);
                riskMap.put(riskCode, dutyMap);
            }

        }
        // 计算最后一个险种
        String riskCode = riskCodeList.get(riskCodeList.size() - 1);
        double premRatio = CommonUtil.sub(1.0, premRatioCount);
        double companyRiskPrem = CommonUtil.sub(companyPrem, companyRiskPremCount);
        double personRiskPrem = CommonUtil.sub(personPrem, personRiskPremCount);
        params.clear();
        params.put("planCode", planCode);
        params.put("riskCode", riskCode);
        params.put("ensureCode", ensureCode);
        List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyList(params);

        if (fcPlanRiskDutyList.size() <= 1) {
            Map<String, Double> premMap = new HashMap<String, Double>();
            premMap.put("1", companyRiskPrem);
            premMap.put("2", personRiskPrem);
            dutyMap.put(fcPlanRiskDutyList.get(0).getDutyCode(), premMap);
            riskMap.put(riskCode, dutyMap);
        } else {
            // 多责任的险种则需要计算责任层保费(最后一个责任单独计算)
            // 记录责任保费
            double companyDutyPremCount = 0.0;
            double personDutyPremCount = 0.0;
            for (int j = 0; j < fcPlanRiskDutyList.size() - 1; j++) {
                Map<String, Double> premMap = new HashMap<String, Double>();
                FCPlanRiskDuty fcPlanRiskDuty = fcPlanRiskDutyList.get(j);
                // 计算责任保费占比
                double dutyPremRatio = CommonUtil.div(fcPlanRiskDuty.getPrem(), riskPremMap.get(riskCode), 8);
                // 责任层公司缴费 = 险种层公司缴费 * 责任保费占比
                double companyDutyPrem = CommonUtil.mul(companyRiskPrem, dutyPremRatio);
                companyDutyPrem = new BigDecimal(companyDutyPrem).setScale(2, RoundingMode.HALF_UP).doubleValue();
                double personDutyPrem = CommonUtil.sub(fcPlanRiskDuty.getPrem(), companyDutyPrem);
                companyDutyPremCount = CommonUtil.add(companyDutyPremCount, companyDutyPrem);
                personDutyPremCount = CommonUtil.add(personDutyPremCount, personDutyPrem);
                premMap.put("1", companyDutyPrem);
                premMap.put("2", personDutyPrem);
                dutyMap.put(fcPlanRiskDuty.getDutyCode(), premMap);
            }
            // 单独计算最后一个责任
            FCPlanRiskDuty fcPlanRiskDuty = fcPlanRiskDutyList.get(fcPlanRiskDutyList.size() - 1);
            Map<String, Double> premMap = new HashMap<String, Double>();
            // 公司缴费
            premMap.put("1", CommonUtil.sub(companyRiskPrem, companyDutyPremCount));
            // 个人缴费
            premMap.put("2", CommonUtil.sub(personRiskPrem, personDutyPremCount));
            dutyMap.put(fcPlanRiskDuty.getDutyCode(), premMap);
            riskMap.put(riskCode, dutyMap);
        }
        return riskMap;
    }

    /**
     * 拆分保额
     *
     * @param planCode
     * @param companyPrem 公司缴费金额
     * @return
     */
    public Map<String, Map<String, Map<String, Double>>> splitAmnt(String ensureCode, String planCode, double companyPrem) {
        Map<String, Map<String, Map<String, Double>>> riskMap = new HashMap<String, Map<String, Map<String, Double>>>();
        Map<String, Map<String, Double>> dutyMap = new HashMap<String, Map<String, Double>>();
        // 参数容器
        Map<String, Object> params = new HashMap<String, Object>();
        // 获取险种名称集合，查询时按顺序排序
        List<String> riskCodeList = fcPlanRiskMapper.selectRiskCodeList(ensureCode, planCode);
        // 计划总保费
        double totalPrem = fcPlanRiskDutyMapper.selectTotalPremPlan(ensureCode, planCode);
        if (totalPrem <= companyPrem) {
            return planAmnt(ensureCode, planCode);
        }
        // 公司缴费占比 = 公司缴费 / (公司缴费 + 个人缴费)
        double companyPremRatio = CommonUtil.div(companyPrem, totalPrem, 8);
		/*// 个人缴费金额
		double personPrem = CommonUtil.sub(totalPrem, companyPrem);
		// 个人缴费占比 = 个人缴费 / (公司缴费 + 个人缴费)
		double personPremRatio = CommonUtil.div(personPrem, totalPrem, 8);*/

        for (int i = 0; i < riskCodeList.size(); i++) {
            String riskCode = riskCodeList.get(i);
            params.clear();
            params.put("planCode", planCode);
            params.put("riskCode", riskCode);
            params.put("ensureCode", ensureCode);
            List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyList(params);
            if (fcPlanRiskDutyList == null || fcPlanRiskDutyList.size() == 0) {
                // 存最终结果 责任层级的 公司缴费 和 个人缴费
                Map<String, Double> amntMap = new HashMap<String, Double>();
                log.info("数据异常：计划" + planCode + "下险种" + riskCode + "的责任数据异常。");
                amntMap.put("1", 0.00);
                amntMap.put("2", 0.00);
                dutyMap.put("000000", amntMap);
                riskMap.put(riskCode, dutyMap);
            } else {
                for (int j = 0; j < fcPlanRiskDutyList.size(); j++) {
                    // 存最终结果 责任层级的 公司缴费 和 个人缴费
                    Map<String, Double> amntMap = new HashMap<String, Double>();
                    // 责任保额   拆成 公司保额 + 个人保额
                    double amnt = fcPlanRiskDutyList.get(j).getAmnt();
                    // 公司保额
                    double companyAmnt = CommonUtil.mul(amnt, companyPremRatio);
                    companyAmnt = new BigDecimal(companyAmnt).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                    // 个人保额 = 责任保额 - 公司保额
                    double personAmnt = CommonUtil.sub(amnt, companyAmnt);
                    amntMap.put("1", companyAmnt);
                    amntMap.put("2", personAmnt);
                    dutyMap.put(fcPlanRiskDutyList.get(j).getDutyCode(), amntMap);
                }
                riskMap.put(riskCode, dutyMap);
            }
        }
        return riskMap;
    }


    public Map<String, Map<String, Map<String, Double>>> planPrem(String ensureCode, String planCode) {
        // 返回值结果
        Map<String, Map<String, Map<String, Double>>> riskMap = new HashMap<String, Map<String, Map<String, Double>>>();
        Map<String, Map<String, Double>> dutyMap = new HashMap<String, Map<String, Double>>();
        List<String> riskCodeList = fcPlanRiskMapper.selectRiskCodeList(ensureCode, planCode);
        // 参数容器
        Map<String, Object> params = new HashMap<String, Object>();
        for (int i = 0; i < riskCodeList.size(); i++) {
            String riskCode = riskCodeList.get(i);
            params.clear();
            params.put("planCode", planCode);
            params.put("riskCode", riskCode);
            params.put("ensureCode", ensureCode);
            List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyList(params);
            for (int j = 0; j < fcPlanRiskDutyList.size(); j++) {
                // 存最终结果 责任层级的 公司缴费 和 个人缴费
                Map<String, Double> premMap = new HashMap<String, Double>();
                FCPlanRiskDuty fcPlanRiskDuty = fcPlanRiskDutyList.get(j);
                // 责任层公司缴费 = 险种层公司缴费 * 责任保费占比
                premMap.put("1", fcPlanRiskDuty.getPrem());
                premMap.put("2", 0.0);
                dutyMap.put(fcPlanRiskDuty.getDutyCode(), premMap);
            }
            riskMap.put(riskCode, dutyMap);
        }
        return riskMap;
    }

    // 保额获取
    public Map<String, Map<String, Map<String, Double>>> planAmnt(String ensureCode, String planCode) {
        // 返回值结果
        Map<String, Map<String, Map<String, Double>>> riskMap = new HashMap<String, Map<String, Map<String, Double>>>();
        Map<String, Map<String, Double>> dutyMap = new HashMap<String, Map<String, Double>>();
        List<String> riskCodeList = fcPlanRiskMapper.selectRiskCodeList(ensureCode, planCode);
        // 参数容器
        Map<String, Object> params = new HashMap<String, Object>();
        for (int i = 0; i < riskCodeList.size(); i++) {
            String riskCode = riskCodeList.get(i);
            params.clear();
            params.put("planCode", planCode);
            params.put("riskCode", riskCode);
            params.put("ensureCode", ensureCode);
            List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyList(params);
            for (int j = 0; j < fcPlanRiskDutyList.size(); j++) {
                FCPlanRiskDuty fcPlanRiskDuty = fcPlanRiskDutyList.get(j);
                Map<String, Double> amntMap = new HashMap<String, Double>();
                // 责任层 保额
                amntMap.put("1", fcPlanRiskDuty.getAmnt());
                amntMap.put("2", 0.0);
                dutyMap.put(fcPlanRiskDuty.getDutyCode(), amntMap);
            }
            riskMap.put(riskCode, dutyMap);
        }
        return riskMap;
    }

    /**
     * 弹性计划拆分保费
     *
     * @return
     */
    public Map<String, Object> efleSplitPremium(String orderItemDetailNo, double companyPrem) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Map<String, Map<String, Double>>> riskMap = new HashMap<String, Map<String, Map<String, Double>>>();
        Map<String, Map<String, Double>> dutyMap = new HashMap<String, Map<String, Double>>();
        //参数容器
        Map<String, Object> params = new HashMap<>();
        //所需总保费
        double totalPrem = 0.00;
        //存储每个险种应缴保费
        Map<String, Double> riskTotalPremMap = new HashMap<String, Double>();
        //存储险种责任保费信息
        Map<String, Map<String, Double>> riskInfoMap = new HashMap<>();
        //所投险种集合
        List<String> riskCodeList = fcInsureEflexPlanMapper.getRiskCodeByOrderItemDetailNo(orderItemDetailNo);
        for (String riskCode : riskCodeList) {
            log.info("险种：" + riskCode);
            params.put("riskCode", riskCode);
            params.put("orderItemDetailNo", orderItemDetailNo);
            List<Map<String, String>> riskDutyAmntList = fcInsureEflexPlanMapper.getDutyAmntByRiskCode(params);
            //险种保费
            double riskTotalPrem = 0.00;
            Map<String, Double> riskPremMap = new HashMap<>();
            for (Map<String, String> riskDutyInfo : riskDutyAmntList) {
                //责任保费
                Double dutyTotalPrem = Double.parseDouble(String.valueOf(riskDutyInfo.get("prem")));
                riskPremMap.put(riskDutyInfo.get("dutyCode"), dutyTotalPrem);
                //险种保费
                riskTotalPrem = CommonUtil.add(riskTotalPrem, dutyTotalPrem);
            }
            riskInfoMap.put(riskCode, riskPremMap);
            riskTotalPremMap.put(riskCode, riskTotalPrem);
            //总保费
            totalPrem = CommonUtil.add(totalPrem, riskTotalPrem);
        }
        if (totalPrem <= companyPrem) {
            for (Map.Entry<String, Map<String, Double>> riskInfo : riskInfoMap.entrySet()) {
                Map<String, Double> dutyPremMap = riskInfo.getValue();
                Map<String, Map<String, Double>> returnDutyPremMap = new HashMap<>();
                for (Map.Entry<String, Double> dutyPremInfo : dutyPremMap.entrySet()) {
                    Map<String, Double> premMap = new HashMap<String, Double>();
                    premMap.put("1", dutyPremInfo.getValue());
                    premMap.put("2", 0.0);
                    returnDutyPremMap.put(dutyPremInfo.getKey(), premMap);
                }
                riskMap.put(riskInfo.getKey(), returnDutyPremMap);
            }
            resultMap.put("riskMap", riskMap);
            resultMap.put("totalPrem", totalPrem);
            return resultMap;
        } else {
            // 存储 除最后一款险种外的保费占比
            double premRatioCount = 0.0;
            // 存储 个人缴费
            double personRiskPremCount = 0.0;
            // 储存 公司缴费
            double companyRiskPremCount = 0.0;
            // 个人缴费金额
            double personPrem = CommonUtil.sub(totalPrem, companyPrem);
            for (int i = 0; i < riskCodeList.size() - 1; i++) {
                String riskCode = riskCodeList.get(i);
                // 险种，保费占比为：当前险种保费/总保费； 小数点后精确8位数  ROUND_HALF_UP 四舍五入
                double premRatio = CommonUtil.div(riskTotalPremMap.get(riskCode), totalPrem, 8);
                premRatioCount = CommonUtil.add(premRatioCount, premRatio);
                // 公司缴费 = 公司保费 * 险种保费占比
                double companyRiskPrem = CommonUtil.mul(companyPrem, premRatio);
                companyRiskPrem = new BigDecimal(companyRiskPrem).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                companyRiskPremCount = CommonUtil.add(companyRiskPremCount, companyRiskPrem);
                // 个人缴费 = 险种保费 - 公司缴费
                double personRiskPrem = CommonUtil.sub(riskTotalPremMap.get(riskCode), companyRiskPrem);
                personRiskPremCount = CommonUtil.add(personRiskPremCount, personRiskPrem);
                //所投责任编码集合
                List<String> dutyCodeList = new ArrayList<>();
                //该险种下所投责任集合
                Map<String, Double> dutyPremMap = new HashMap<>();
                for (Map.Entry<String, Map<String, Double>> riskInfo : riskInfoMap.entrySet()) {
                    if (riskCode.equals(riskInfo.getKey())) {
                        dutyPremMap = riskInfo.getValue();
                        Iterator it = dutyPremMap.keySet().iterator();
                        while (it.hasNext()) {
                            String key = it.next().toString();
                            dutyCodeList.add(key);
                        }
                    }
                }
                if (dutyCodeList.size() <= 1) {
                    // 存最终结果 责任层级的 公司缴费 和 个人缴费
                    String dutyCode = dutyCodeList.get(0);
                    Map<String, Double> premMap = new HashMap<String, Double>();
                    premMap.put("1", companyRiskPrem);
                    premMap.put("2", personRiskPrem);
                    dutyMap.put(dutyCode, premMap);
                    riskMap.put(riskCode, dutyMap);
                } else {
                    // 多责任的险种则需要计算责任层保费(最后一个责任单独计算)
                    // 记录责任保费
                    double companyDutyPremCount = 0.0;
                    double personDutyPremCount = 0.0;
                    for (int j = 0; j < dutyCodeList.size() - 1; j++) {
                        // 存最终结果 责任层级的 公司缴费 和 个人缴费
                        Map<String, Double> premMap = new HashMap<String, Double>();
                        double dutyPrem = dutyPremMap.get(dutyCodeList.get(j));
                        // 计算责任保费占比
                        double dutyPremRatio = CommonUtil.div(dutyPrem, riskTotalPremMap.get(riskCode), 8);
                        // 责任层公司缴费 = 险种层公司缴费 * 责任保费占比
                        double companyDutyPrem = CommonUtil.mul(companyRiskPrem, dutyPremRatio);
                        companyDutyPrem = new BigDecimal(companyDutyPrem).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        // 个人缴费 = 责任保费 - 公司缴费
                        double personDutyPrem = CommonUtil.sub(dutyPrem, companyDutyPrem);
                        companyDutyPremCount = CommonUtil.add(companyDutyPremCount, companyDutyPrem);
                        personDutyPremCount = CommonUtil.add(personDutyPremCount, personDutyPrem);
                        premMap.put("1", companyDutyPrem);
                        premMap.put("2", personDutyPrem);
                        dutyMap.put(dutyCodeList.get(j), premMap);
                    }
                    // 单独计算最后一个责任
                    // 存最终结果 责任层级的 公司缴费 和 个人缴费
                    Map<String, Double> premMap = new HashMap<String, Double>();
                    // 公司缴费
                    premMap.put("1", CommonUtil.sub(companyRiskPrem, companyDutyPremCount));
                    // 个人缴费
                    premMap.put("2", CommonUtil.sub(personRiskPrem, personDutyPremCount));
                    dutyMap.put(dutyCodeList.get(dutyCodeList.size() - 1), premMap);
                    riskMap.put(riskCode, dutyMap);
                }
            }
            // 计算最后一个险种
            String riskCode = riskCodeList.get(riskCodeList.size() - 1);
            log.info("金额测试  险种" + riskCode);
            double companyRiskPrem = CommonUtil.sub(companyPrem, companyRiskPremCount);
            double personRiskPrem = CommonUtil.sub(personPrem, personRiskPremCount);
            //所投责任编码集合
            List<String> dutyCodeList = new ArrayList<>();
            //该险种下所投责任集合
            Map<String, Double> dutyPremMap = new HashMap<>();
            for (Map.Entry<String, Map<String, Double>> riskInfo : riskInfoMap.entrySet()) {
                if (riskCode.equals(riskInfo.getKey())) {
                    dutyPremMap = riskInfo.getValue();
                    Iterator it = dutyPremMap.keySet().iterator();
                    while (it.hasNext()) {
                        String key = it.next().toString();
                        dutyCodeList.add(key);
                    }
                }
            }
            if (dutyCodeList.size() <= 1) {
                String dutyCode = dutyCodeList.get(0);
                Map<String, Double> premMap = new HashMap<String, Double>();
                premMap.put("1", companyRiskPrem);
                premMap.put("2", personRiskPrem);
                dutyMap.put(dutyCode, premMap);
                riskMap.put(riskCode, dutyMap);
            } else {
                // 多责任的险种则需要计算责任层保费(最后一个责任单独计算)
                // 记录责任保费
                double companyDutyPremCount = 0.0;
                double personDutyPremCount = 0.0;
                for (int j = 0; j < dutyCodeList.size() - 1; j++) {
                    Map<String, Double> premMap = new HashMap<String, Double>();
                    double dutyPrem = dutyPremMap.get(dutyCodeList.get(j));
                    // 计算责任保费占比
                    double dutyPremRatio = CommonUtil.div(dutyPrem, riskTotalPremMap.get(riskCode), 8);
                    // 责任层公司缴费 = 险种层公司缴费 * 责任保费占比
                    double companyDutyPrem = CommonUtil.mul(companyRiskPrem, dutyPremRatio);
                    companyDutyPrem = new BigDecimal(companyDutyPrem).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                    double personDutyPrem = CommonUtil.sub(dutyPrem, companyDutyPrem);
                    companyDutyPremCount = CommonUtil.add(companyDutyPremCount, companyDutyPrem);
                    personDutyPremCount = CommonUtil.add(personDutyPremCount, personDutyPrem);
                    premMap.put("1", companyDutyPrem);
                    premMap.put("2", personDutyPrem);
                    dutyMap.put(dutyCodeList.get(j), premMap);
                }
                // 单独计算最后一个责任
                Map<String, Double> premMap = new HashMap<String, Double>();
                // 公司缴费
                premMap.put("1", CommonUtil.sub(companyRiskPrem, companyDutyPremCount));
                // 个人缴费
                premMap.put("2", CommonUtil.sub(personRiskPrem, personDutyPremCount));
                dutyMap.put(dutyCodeList.get(dutyCodeList.size() - 1), premMap);
                riskMap.put(riskCode, dutyMap);
            }
        }
        resultMap.put("riskMap", riskMap);
        resultMap.put("totalPrem", totalPrem);
        return resultMap;
    }


    /**
     * 弹性计划--拆分保额
     *
     * @param orderItemDetailNo
     * @param companyPrem       公司缴费金额
     * @return
     */
    public Map<String, Map<String, Map<String, Double>>> efleSplitAmnt(String orderItemDetailNo, double companyPrem, double totalPrem, List<String> riskCodeList) {
        Map<String, Map<String, Map<String, Double>>> riskMap = new HashMap<String, Map<String, Map<String, Double>>>();
        Map<String, Map<String, Double>> dutyMap = new HashMap<String, Map<String, Double>>();
        // 参数容器
        Map<String, Object> params = new HashMap<String, Object>();
        if (totalPrem <= companyPrem) {
            for (int i = 0; i < riskCodeList.size(); i++) {
                // 存最终结果 责任层级的 公司缴费 和 个人缴费
                Map<String, Double> amntMap = new HashMap<String, Double>();
                String riskCode = riskCodeList.get(i);
                params.clear();
                params.put("riskCode", riskCode);
                params.put("orderItemDetailNo", orderItemDetailNo);
                List<Map<String, String>> riskDutyAmntList = fcInsureEflexPlanMapper.getDutyAmntByRiskCode(params);
                log.info("保额拆分测试：" + riskCode);
                for (int j = 0; j < riskDutyAmntList.size(); j++) {
                    Map<String, String> dutyAmntInfoMap = riskDutyAmntList.get(j);
                    // 责任层公司缴费 = 险种层公司缴费 * 责任保费占比
                    amntMap.put("1", Double.valueOf(String.valueOf(dutyAmntInfoMap.get("amnt"))));
                    amntMap.put("2", 0.0);
                    dutyMap.put(dutyAmntInfoMap.get("dutyCode"), amntMap);
                }
                riskMap.put(riskCode, dutyMap);
            }
            return riskMap;
        }
        // 公司缴费占比 = 公司缴费 / (公司缴费 + 个人缴费)
        double companyPremRatio = CommonUtil.div(companyPrem, totalPrem, 8);
        for (int i = 0; i < riskCodeList.size(); i++) {
            String riskCode = riskCodeList.get(i);
            params.clear();
            params.put("riskCode", riskCode);
            params.put("orderItemDetailNo", orderItemDetailNo);
            List<Map<String, String>> riskDutyAmntList = fcInsureEflexPlanMapper.getDutyAmntByRiskCode(params);
            log.info("保额拆分测试：" + riskCode);
            if (riskDutyAmntList == null || riskDutyAmntList.size() == 0) {
                // 存最终结果 责任层级的 公司缴费 和 个人缴费
                Map<String, Double> amntMap = new HashMap<String, Double>();
                log.info("保额拆分数据异常：险种" + riskCode + "的责任数据异常。");
                amntMap.put("1", 0.00);
                amntMap.put("2", 0.00);
                dutyMap.put("000000", amntMap);
                riskMap.put(riskCode, dutyMap);
            } else {
                for (int j = 0; j < riskDutyAmntList.size(); j++) {
                    // 存最终结果 责任层级的 公司缴费 和 个人缴费
                    Map<String, Double> amntMap = new HashMap<String, Double>();
                    // 责任保额   拆成 公司保额 + 个人保额
                    double amnt = Double.valueOf(String.valueOf(riskDutyAmntList.get(j).get("amnt")));
                    // 公司保额
                    double companyAmnt = CommonUtil.mul(amnt, companyPremRatio);
                    companyAmnt = new BigDecimal(companyAmnt).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                    // 个人保额 = 责任保额 - 公司保额
                    double personAmnt = CommonUtil.sub(amnt, companyAmnt);
                    amntMap.put("1", companyAmnt);
                    amntMap.put("2", personAmnt);
                    dutyMap.put(riskDutyAmntList.get(j).get("dutyCode"), amntMap);
                }
                riskMap.put(riskCode, dutyMap);
            }
        }
        return riskMap;
    }


    public double calPercentage(String a, String b) {
        double result = Double.parseDouble(a) / Double.parseDouble(b);
        BigDecimal bigDecimal = new BigDecimal(result);
        result = bigDecimal.setScale(8, BigDecimal.ROUND_HALF_UP).doubleValue();
        return result;
    }


    /**
     * <AUTHOR>
     * @description家属计划详情
     * @date 10:31 10:31
     * @modified
     */
    public String familyInsure(String token, String planObject, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            Map<String, Object> ensureCodeMap = new HashMap<>();
            ensureCodeMap.put("ensureCode", ensureCode);
            ensureCodeMap.put("personId", personId);
            //判断家属是否投保
            List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectEnsureCodeByPersonId(ensureCodeMap);
            Map<String, String> raletion = new HashMap<>();
            // 查询此人是否在本员工下是否有此家属
            raletion.put("perNo", globalInput.getCustomNo());
            raletion.put("personid", personId);
            FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaffOne(raletion);
            // 判断员工是否已投过计划
            if (fpInsurePlanList.size() > 0) {
                List<Map<String, Object>> mapList = new ArrayList<>();
                // 家属已投过
                for (FPInsurePlan fpInsurePlan : fpInsurePlanList) {
                    // 获取计划编码
                    String planCode = fpInsurePlan.getPlanCode();
                    // 通过计划编码查询员工投保计划
                    Map<String, String> param = new HashMap<>();
                    param.put("planCode", planCode);
                    param.put("ensureCode", fpInsurePlan.getEnsureCode());
                    FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                    Map<String, Object> map = new HashMap<>();
                    map.put("planCode", fcEnsurePlan.getPlanCode());
                    // 获取家属计划名称
                    map.put("planName", fcEnsurePlan.getPlanName());
                    // 获取家属计划总费
                    map.put("totalPrem", fcEnsurePlan.getTotalPrem());
                    // 获取家属重点
                    map.put("planKey", fcEnsurePlan.getPlanKey());
                    mapList.add(map);
                    resultMap.put("isPlan", "1");
                    resultMap.put("fcInsurePlan", fcEnsurePlan);
                    resultMap.put("mapList", mapList);
                    resultMap.put("code", "200");
                    resultMap.put("message", "查询家属计划成功");
                    return JSON.toJSONString(resultMap);
                }
            } else {
                // 查询家属计划
                List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectEnsureCodeByplanObject(ensureCode, planObject);
                if (fcEnsurePlanList.size() < 1) {
                    resultMap.put("isPlan", "0");
                    resultMap.put("mapList", "");
                    resultMap.put("code", "200");
                    resultMap.put("message", "未查询到符合该家属的相关计划。");
                    return JSON.toJSONString(resultMap);
                }
                List<FCEnsurePlan> insurePlanList = checkPlanAge(fcEnsurePlanList, personId, rela.getRelation(), globalInput.getCustomNo());
                List<Map<String, Object>> mapList = new ArrayList<>();
                for (FCEnsurePlan fcEnsurePlan : insurePlanList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("planCode", fcEnsurePlan.getPlanCode());
                    //获取家属计划名称
                    map.put("planName", fcEnsurePlan.getPlanName());
                    //获取家属计划总费
                    map.put("totalPrem", fcEnsurePlan.getTotalPrem());
                    //获取家属重点
                    map.put("planKey", fcEnsurePlan.getPlanKey());
                    mapList.add(map);
                }
                resultMap.put("isPlan", "0");
                resultMap.put("mapList", mapList);
                resultMap.put("code", "200");
                resultMap.put("message", "查询家属计划成功");
            }
        } catch (Exception e) {
            log.info("查询家属计划失败", e);
            resultMap.put("code", "500");
            resultMap.put("message", "查询家属计划失败");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * <AUTHOR>
     * @description查询保障计划
     * @date 9:10 9:10
     * @modified
     */
    public String selectPlanInfo(String token, String planObject) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (planObject == null || "".equals(planObject)) {
                resultMap.put("message", "使用人不能为空");
                JSON.toJSONString(resultMap);
            }
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            String perNo = globalInput.getCustomNo();
            Map<String, Object> map = new HashMap<>();
            //获取福利类型
            FCEnsure fcensure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if (fcensure == null) {
                resultMap.put("message", "请先选择福利！");
                resultMap.put("code", "500");
                return JSON.toJSONString(resultMap);
            }
            String ensureType = fcensure.getEnsureType();
            //ensureType：0企事业投保，1学生投保
            if (ensureType.equals("1")) {
                List<FCStaffFamilyRela> fcStaffFamilyRelaList = familyMapper.empAndFamilySelect(perNo);
                for (FCStaffFamilyRela fcStaffFamilyRela : fcStaffFamilyRelaList) {
                    String relation = fcStaffFamilyRela.getRelation();
                    if (relation != "0" && !relation.equals("0")) {
                        String personId = fcStaffFamilyRela.getFcPersonList().get(0).getPersonID();
                        map.put("ensureCode", ensureCode);
                        map.put("personId", personId);
                        //查询员工是否投过保
                        List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectEnsureCodeByPersonId(map);
                        //判断员工是否已投过计划
                        if (fpInsurePlanList.size() > 0) {
                            //员工已投过保
                            for (FPInsurePlan fpInsurePlan : fpInsurePlanList) {
                                //获取计划编码
                                String planCode = fpInsurePlan.getPlanCode();
                                //通过计划编码查询员工投保计划
                                Map<String, String> param = new HashMap<>();
                                param.put("planCode", planCode);
                                param.put("ensureCode", fpInsurePlan.getEnsureCode());
                                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                                resultMap.put("isPlan", "1");
                                List<FCEnsurePlan> insurePlanList = new ArrayList<>();
                                insurePlanList.add(fcEnsurePlan);
                                resultMap.put("data", insurePlanList);
                                resultMap.put("code", "200");
                                resultMap.put("message", "查询员工计划成功");
                                return JSON.toJSONString(resultMap);
                            }
                        }
                        //员工未投过保
                        List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectPlanInfo(ensureCode, planObject);
                        Map<String, String> mapRaletion = new HashMap<>();
                        mapRaletion.put("perNo", globalInput.getCustomNo());
                        mapRaletion.put("personID", personId);
                        //查询此人是否在本员工下是否有此家属
                        FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(mapRaletion);
                        List<FCEnsurePlan> insurePlanList = checkPlanAge(fcEnsurePlanList, personId, rela.getRelation(), globalInput.getCustomNo());
                        resultMap.put("isPlan", "0");
                        resultMap.put("data", insurePlanList);
                        resultMap.put("message", "查询计划信息成功");
                        resultMap.put("code", "200");
                    }
                }
            } else {
                //获取所有的人的信息
                log.info("获取所有的人的信息！");
                List<String> idnolist = familyMapper.selectFamilyIDNO(perNo);
                List<String> perosnidlist = new ArrayList<>();
                for (String idno : idnolist) {
                    Map<String, String> map1 = new HashMap<>();
                    map1.put("IdNo", idno);
                    map1.put("perNo", perNo);
                    String personid = familyMapper.selectNewFamilyPersonidEflex(map1);
                    if (StringUtils.isNotBlank(personid)) {
                        perosnidlist.add(personid);
                    }
                }
                FCPerInfo fcperinfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
                Map<String, Object> paraMap = new HashMap<String, Object>();
                paraMap.put("perosnidlist", perosnidlist);
                paraMap.put("idNo", fcperinfo.getIDNo());
                List<FCPerson> familyList = familyMapper.selectAllFamilyInfo(paraMap);
                for (FCPerson fcPerson : familyList) {
                    if (fcPerson.getRelation().equals("0") && !fcPerson.getIDNo().equals(fcperinfo.getIDNo())) continue;
                    if (!fcPerson.getRelation().equals("0") && fcPerson.getIDNo().equals(fcperinfo.getIDNo())) continue;
                    if (!fcperinfo.getIDNo().equals(fcPerson.getPerIDNo())) continue;
                    String relation = fcPerson.getRelation();
                    if (relation == "0" || relation.equals("0")) {
                        List<FPInsurePlan> fpInsurePlanList = new ArrayList<>();
                        //查询同一个员工下相同证件号的家属的所有计划
                        Map<String, String> map1 = new HashMap<>();
                        map1.put("personid", fcPerson.getPersonID());
                        map1.put("perno", perNo);
                        List<String> personIdlist = familyMapper.selectSameStaffPersonid(map1);
                        //判断同一个员工下证件号相同的家属投保计划
                        for (String personid : personIdlist) {
                            map.put("ensureCode", ensureCode);
                            map.put("personId", personid);
                            //查询员工是否投过保
                            List<FPInsurePlan> InsurePlanList = fpInsurePlanMapper.selectEnsureCodeByPersonId(map);
                            if (InsurePlanList.size() > 0) {
                                for (FPInsurePlan fpInsurePlan : InsurePlanList) {
                                    fpInsurePlanList.add(fpInsurePlan);
                                }
                            }
                        }
                        //判断员工是否已投过计划
                        if (fpInsurePlanList.size() > 0) {
                            log.info("员工已投过保！");
                            //员工已投过保
                            for (FPInsurePlan fpInsurePlan : fpInsurePlanList) {
                                //获取计划编码
                                String planCode = fpInsurePlan.getPlanCode();
                                //通过计划编码查询员工投保计划
                                Map<String, String> param = new HashMap<>();
                                param.put("planCode", planCode);
                                param.put("ensureCode", fpInsurePlan.getEnsureCode());
                                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                                resultMap.put("isPlan", "1");
                                List<FCEnsurePlan> insurePlanList = new ArrayList<>();
                                insurePlanList.add(fcEnsurePlan);
                                resultMap.put("data", insurePlanList);
                                resultMap.put("code", "200");
                                resultMap.put("message", "查询员工计划成功");
                                return JSON.toJSONString(resultMap);
                            }
                        } else {
                            log.info("员工未投过保！");
                            //员工未投过保
                            Map<String, Object> params = new HashMap<>();
                            map.put("personId", fcPerson.getPersonID());
                            map.put("ensureCode", ensureCode);
                            List<String> personids = fcDefaultPlanMapper.getAllPersonId(String.valueOf(map.get("personId")));
                            List<FCDefaultPlan> fcDefaultPlan = fcDefaultPlanMapper.getAllByList(ensureCode, personids);
                            List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectPlanInfo(ensureCode, planObject);
                            Map<String, String> mapRaletion = new HashMap<>();
                            mapRaletion.put("perNo", globalInput.getCustomNo());
                            mapRaletion.put("personID", fcPerson.getPersonID());
                            //查询此人是否在本员工下是否有此家属
                            FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(mapRaletion);
                            List<FCEnsurePlan> insurePlanList = checkPlanAge(fcEnsurePlanList, fcPerson.getPersonID(), rela.getRelation(), globalInput.getCustomNo());
                            if (fcEnsurePlanList.size() < 1) {
                                log.info("未查询到符合该家属的相关计划。");
                                resultMap.put("isPlan", "0");
                                resultMap.put("mapList", null);
                                resultMap.put("code", "200");
                                resultMap.put("message", "未查询到符合该家属的相关计划。");
                                return JSON.toJSONString(resultMap);
                            }
                            String planCode = fcDefaultPlan.get(0).getPlanCode();
                            insurePlanList = insurePlanList.stream().filter(i -> i.getPlanCode().equals(planCode)).collect(Collectors.toList());
                            resultMap.put("isPlan", "0");
                            resultMap.put("defaultPlanCode", planCode);
                            resultMap.put("data", insurePlanList);
                            resultMap.put("message", "查询计划信息成功");
                            resultMap.put("code", "200");
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.info("查询计划失败", e);
            resultMap.put("message", "查询计划失败");
            resultMap.put("code", "500");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description校验用户是否有福利计划
     * @date 9:12 9:12
     * @modified
     */
    public String checkEnsure(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            String perNo = globalInput.getCustomNo();
            if (ensureCode == null || "".equals(ensureCode)) {
                resultMap.put("message", "该公司没有为该用户制定福利或该福利不在开放期内无法投保");
                resultMap.put("isEnsure", "0");
                return JSON.toJSONString(resultMap);
            }

            List<FCOrder> fcOrder = fcOrderMapper.selectOrder(perNo, ensureCode);
            if (fcOrder.size() > 0) {
                resultMap.put("message", "该用户已投过该福利");
                resultMap.put("isPlan", "1");
            } else {
                resultMap.put("message", "该用户没有投过该福利");
                resultMap.put("isPlan", "0");
            }
            resultMap.put("message", "公司为该用户制订了福利");
            resultMap.put("isEnsure", "1");
        } catch (Exception e) {
            log.info("校验福利计划失败", e);
            resultMap.put("message", "校验福利计划失败");
            resultMap.put("code", "500");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * <AUTHOR>
     * @description查询险种详情信息
     * @date 19:44 19:44
     * @modified
     */
    public String getRiskinfo(String token, String planCode, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            Map<String, String> param = new HashMap<>();
            param.put("planCode", planCode);
            param.put("ensureCode", ensureCode);
            FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
            List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectRiskInfo(ensureCode, planCode);//计划编码查险种表
            List<Map<String, Object>> riskList = new ArrayList<>();
            //查询一个险种
            for (FCPlanRisk fcPlanRisk : fcPlanRiskList) {
                String riskCode = fcPlanRisk.getRiskCode();
                String riskName = fdRiskInfoMapper.selectRiskNameByRiskCode(riskCode);
                List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyInfo(ensureCode, planCode, riskCode);
                for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("Prem", fcPlanRiskDuty.getPrem());
                    map.put("RiskCode", fcPlanRisk.getRiskCode());
                    String dutyCode = fcPlanRiskDuty.getDutyCode();
                    Map<String, String> dutyMap = new HashMap<>();
                    dutyMap.put("dutyCode", dutyCode);
                    dutyMap.put("riskCode", riskCode);
                    Map<String, Object> fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                    map.put("RiskName", riskName);
                    map.put("Amnt", fcPlanRiskDuty.getAmnt());
                    map.put("DutyName", fdRiskDutyInfo.get("DutyName"));
                    riskList.add(map);
                }
            }
            // 获取计划保障说明
            PlanConfigExplain configExplain1 = getConfigExplain1(globalInput.getGrpNo(), ensureCode, planCode);
            resultMap.put("TotalPrem", fcEnsurePlan.getTotalPrem());
            resultMap.put("planConfigExplain", configExplain1);
            resultMap.put("data", riskList);
            resultMap.put("message", "查询险种信息成功");
            resultMap.put("code", "0");
        } catch (Exception e) {
            log.info("查询险种信息失败", e);
            resultMap.put("messag", "查询险种信息失败");
            resultMap.put("code", "500");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description移動端查询险种详情信息
     * @date 19:44 19:44
     * @modified
     */
    public String getYDRiskinfo(String token, String planCode, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            Map<String, Object> planMap = new HashMap<>();
            planMap.put("ensureCode", ensureCode);
            planMap.put("personId", personId);
            List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectEnsureCodeByPersonId(planMap);
            //是否投过保
            if (fpInsurePlanList.size() > 0) {
                String plan = fpInsurePlanList.get(0).getPlanCode();
                if (plan == planCode || plan.equals(planCode)) {
                    resultMap.put("isPlan", "1");
                } else {
                    resultMap.put("isPlan", "0");
                }
            }
            Map<String, String> param = new HashMap<>();
            param.put("planCode", planCode);
            param.put("ensureCode", ensureCode);
            FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
            List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectRiskInfo(ensureCode, planCode);
            List<Map<String, Object>> riskList = new ArrayList<>();

            //查询一个险种
            for (FCPlanRisk fcPlanRisk : fcPlanRiskList) {
                String riskCode = fcPlanRisk.getRiskCode();
                String riskName = fdRiskInfoMapper.selectRiskNameByRiskCode(riskCode);
                List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyInfo(ensureCode, planCode, riskCode);
                for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("Prem", fcPlanRiskDuty.getPrem());
                    map.put("RiskCode", fcPlanRisk.getRiskCode());
                    String dutyCode = fcPlanRiskDuty.getDutyCode();
                    Map<String, String> dutyMap = new HashMap<>();
                    dutyMap.put("dutyCode", dutyCode);
                    dutyMap.put("riskCode", riskCode);
                    Map<String, Object> fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                    map.put("RiskName", riskName);
                    map.put("Amnt", fcPlanRiskDuty.getAmnt());
                    map.put("DutyName", fdRiskDutyInfo.get("DutyName"));
                    riskList.add(map);
                }
            }
            resultMap.put("TotalPrem", fcEnsurePlan.getTotalPrem());
            resultMap.put("data", riskList);
            resultMap.put("message", "查询险种信息成功");
            resultMap.put("code", "0");
        } catch (Exception e) {
            log.info("查询险种信息失败", e);
            resultMap.put("messag", "查询险种信息失败");
            resultMap.put("code", "500");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * <AUTHOR>
     * @description修改后的查询保障列表
     * @date 20:26 20:26
     * @modified
     */
    public String getPSPlanInfo(String token, String planObject, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (planObject == null || "".equals(planObject)) {
                resultMap.put("message", "使用人不能为空");
                JSON.toJSONString(resultMap);
            }
            GlobalInput globalInput = userService.getSession(token);
            String perNo = globalInput.getCustomNo();
            String ensureCode = globalInput.getEnsureCode();
            List<FCStaffFamilyRela> fcStaffFamilyRelaList = familyMapper.empAndFamilySelect(perNo);
            for (FCStaffFamilyRela fcStaffFamilyRela : fcStaffFamilyRelaList) {
                String relation = fcStaffFamilyRela.getRelation();
                if (relation == "0" || relation.equals("0")) {
                    List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectPlanInfo(ensureCode, planObject);
                    Map<String, String> mapRaletion = new HashMap<>();
                    mapRaletion.put("perNo", globalInput.getCustomNo());
                    mapRaletion.put("personID", personId);
                    //查询此人是否在本员工下有此家属
                    FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(mapRaletion);
                    List<FCEnsurePlan> insurePlanList = checkPlanAge(fcEnsurePlanList, personId, rela.getRelation(), globalInput.getCustomNo());
                    for (int i = 0; i < insurePlanList.size(); i++) {
                        List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectRiskInfoList(insurePlanList.get(i).getEnsureCode(), insurePlanList.get(i).getPlanCode());//计划编码查险种表
                        //查询一个险种
                        for (FCPlanRisk fcPlanRisk : fcPlanRiskList) {
                            String riskCode = fcPlanRisk.getRiskCode();
                            List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyInfo(insurePlanList.get(i).getEnsureCode(), insurePlanList.get(i).getPlanCode(), riskCode);//查询投的责任
                            if (fcPlanRiskDutyList.size() == 0) {
                                insurePlanList.remove(i);
                            }
                        }
                    }
                    resultMap.put("isPlan", "0");
                    resultMap.put("data", insurePlanList);
                    resultMap.put("message", "查询计划信息成功");
                    resultMap.put("code", "200");
                    return JSON.toJSONString(resultMap);
                }
                List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectPlanInfo(ensureCode, planObject);
                if (fcEnsurePlanList.size() < 1) {
                    resultMap.put("isPlan", "0");
                    resultMap.put("mapList", null);
                    resultMap.put("code", "200");
                    resultMap.put("message", "未查询到符合该家属的相关计划。");
                    return JSON.toJSONString(resultMap);
                }
                Map<String, String> mapRaletion = new HashMap<>();
                mapRaletion.put("perNo", globalInput.getCustomNo());
                mapRaletion.put("personID", personId);
                //查询此人是否在本员工下有此家属
                FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(mapRaletion);
                List<FCEnsurePlan> insurePlanList = checkPlanAge(fcEnsurePlanList, personId, rela.getRelation(), globalInput.getCustomNo());
                resultMap.put("isPlan", "0");
                resultMap.put("data", insurePlanList);
                resultMap.put("message", "查询计划信息成功");
                resultMap.put("code", "200");
            }
        } catch (Exception e) {
            log.info("查询员工计划失败", e);
            resultMap.put("message", "查询员工计划失败");
            resultMap.put("code", "500");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 投保规则校验接口返回200后调用此接口。
     * 符合计划保障配置中的个告配置  返回200 可以直接投保
     * 不符合 返回 400 - 前端调健康告知接口
     *
     * @param personId
     * @param planCode
     * @return
     */
    public String healthyInform(String token, String personId, String planCode, String ensureCode) throws ParseException {
        GlobalInput globalInput = userService.getSession(token);
        Map<String, Object> resultMap = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
        FCEnsure fcEnsure = fcEnsureMapper.getFCEnsureByPlanCode(globalInput.getEnsureCode(), planCode);
        if (fcPerson == null) {
            resultMap.put("code", "500");
            resultMap.put("message", "被保人信息不存在");
            return JSON.toJSONString(resultMap);
        }
        DateTimeUtil.getAge(fcPerson.getBirthDate());
        long age = Integer.valueOf(DateTimeUtil.getCurrentAge(fcPerson.getBirthDate(), fcEnsure.getCvaliDate()));
        if (age > 0) {
            age = age * 365;
        } else {
            Date fDate = sdf.parse(fcPerson.getBirthDate());
            Date oDate = sdf.parse(sdf.format(new Date()));
            age = (oDate.getTime() - fDate.getTime()) / (1000 * 3600 * 24);
        }
        if (ageValidate(age, planCode, ensureCode)) {
            resultMap.put("code", "400");
            resultMap.put("message", "调用健康告知接口");
            return JSON.toJSONString(resultMap);
        } else {
            resultMap.put("code", "200");
            resultMap.put("message", "健康告知校验通过");
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 健康告知接口调用后调用此接口进行未成年人投保确认书校验
     * 小于18周岁 返回 300 - 未成年人投保确认书
     *
     * @param token
     * @param personId
     * @param planCode
     * @return
     */
    public String juvenilesConfirmation(String token, String personId, String planCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
            FCEnsure fcEnsure = fcEnsureMapper.getFCEnsureByPlanCode(globalInput.getEnsureCode(), planCode);
            int age = Integer.valueOf(DateTimeUtil.getCurrentAge(fcPerson.getBirthDate(), fcEnsure.getCvaliDate()));

            if (age < 18) {
                // 投保确认书
                //FCPlanRiskDuty fcPlanRiskDuty = fcPlanRiskDutyMapper.selectTotalPremPlan(planCode);
                Map<String, Object> paramOne = new HashMap<>();
                paramOne.put("ensureCode", fcEnsure.getEnsureCode());
                paramOne.put("planCode", planCode);
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByPrimaryKey(paramOne);
                if (fcEnsurePlan == null) {
                    resultMap.put("code", "500");
                    resultMap.put("message", "计划编码不存在");
                    return JSON.toJSONString(resultMap);
                }
                Map<String, Object> dataMap = new HashMap<>();
                FCPerInfo staffInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
                dataMap.put("staffName", staffInfo.getName());
                dataMap.put("staffIDNo", staffInfo.getIDNo());
                dataMap.put("insuredName", fcPerson.getName());
                Map<String, String> map = new HashMap<String, String>();
                map.put("perNo", globalInput.getCustomNo());
                map.put("personID", personId);
                //查询此人是否在本员工下是否有此家属
                FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(map);
                if (rela == null) {
                    //查询家属对应的关系（因为这里有一个大前提，就是相同的员工，所以无需加上相同员工下的条件）
                    rela = fcStaffFamilyRelaMapper.selectPersonIdInfo(personId);
                }
                if (rela != null) {
                    if ("3".equals(rela.getRelation())) {
                        // 3为子女。 员工是被保人的父母。这里需要拆分成父亲或母亲
                        if ("0".equals(staffInfo.getSex())) {
                            dataMap.put("relation", "父亲");
                        } else {
                            dataMap.put("relation", "母亲");
                        }
                    } else {
                        String relationName = fdCodeMapper.selectNameByCode("Relation", rela.getRelation());
                        dataMap.put("relation", relationName);
                    }
                } else {
                    dataMap.put("relation", "");
                }

                Map<String, Object> params = new HashMap<String, Object>();
                params.put("planCode", planCode);
                params.put("ensureCode", globalInput.getEnsureCode());
                List<FCPlanRiskDuty> dutyList = fcPlanRiskDutyMapper.selectDutyList(params);
                //在这里判断固定计划中是否含有身故保险
                List<String> dutyCodeList = Arrays.asList("GD0017", "GD0037", "GD0050", "GD0051", "GD0052", "GD0054");
                int count = 0;
                for (FCPlanRiskDuty dutyInfo : dutyList) {
                    if (dutyCodeList.contains(dutyInfo.getDutyCode())) {
                        count++;
                    }
                }
                if (0 == count) {
                    resultMap.put("code", "200");
                    resultMap.put("message", "该计划中不包含身故险种，故不弹出确认函");
                    return JSON.toJSONString(resultMap);
                }
                double deathAmnt = 0.00;
                for (FCPlanRiskDuty dutyInfo : dutyList) {
                    if ("GD0017".equals(dutyInfo.getDutyCode())) {
                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                    } else if ("GD0037".equals(dutyInfo.getDutyCode())) {
                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                    }
                }
                double Amnt15070 = 0.00;
                for (FCPlanRiskDuty dutyInfo : dutyList) {
                    if ("GD0050".equals(dutyInfo.getDutyCode())) {
                        Amnt15070 = dutyInfo.getAmnt();
                    }
                    if ("GD0051".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                        Amnt15070 = dutyInfo.getAmnt();
                    }
                    if ("GD0052".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                        Amnt15070 = dutyInfo.getAmnt();
                    }
                    if ("GD0054".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                        Amnt15070 = dutyInfo.getAmnt();
                    }
                }
                deathAmnt = CommonUtil.add(deathAmnt, Amnt15070);
                if (0.00 == deathAmnt) {
                    resultMap.put("code", "200");
                    resultMap.put("message", "本次投保身故保险金额为0，故不弹出确认函");
                    return JSON.toJSONString(resultMap);
                }

                String riskAmnt = getRiskAmnt(personId);
                if (riskAmnt == null || "".equals(riskAmnt)) {
                    log.info("调用核心接口查询风险保额失败，请联系管理员！");
                    resultMap.put("code", "500");
                    resultMap.put("message", "系统繁忙");
                    return JSON.toJSONString(resultMap);
                }
                dataMap.put("deathAmnt", deathAmnt);
                dataMap.put("deathAmntCount", riskAmnt);
                dataMap.put("month", DateTimeUtil.getCurrentMonth());
                dataMap.put("year", DateTimeUtil.getCurrentYear());
                dataMap.put("month", DateTimeUtil.getCurrentMonth());
                dataMap.put("day", DateTimeUtil.getCurrentDay());

                resultMap.put("code", "300");
                resultMap.put("message", "未成年人投保确认书");
                resultMap.put("data", dataMap);
                return JSON.toJSONString(resultMap);
            } else {
                resultMap.put("code", "200");
                resultMap.put("message", "年龄校验通过");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            resultMap.put("code", "500");
            resultMap.put("message", "系统繁忙");
            return JSON.toJSONString(resultMap);
        }
    }

    //弹性计划-未成年人投保确认函
    public String eflexJuvenilesConfirmation(String token, Map<String, Object> paramMap) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            String personId = paramMap.get("personId").toString();
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            int age = Integer.valueOf(DateTimeUtil.getCurrentAge(fcPerson.getBirthDate(), fcEnsure.getCvaliDate()));
            if (age < 18) {
                // 投保确认书
                Map<String, Object> dataMap = new HashMap<>();
                FCPerInfo staffInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
                dataMap.put("staffName", staffInfo.getName());
                dataMap.put("staffIDNo", staffInfo.getIDNo());
                dataMap.put("insuredName", fcPerson.getName());
                Map<String, String> map = new HashMap<String, String>();
                map.put("perNo", globalInput.getCustomNo());
                map.put("personID", personId);
                //查询此人是否在本员工下是否有此家属
                FCStaffFamilyRela rela = fcStaffFamilyRelaMapper.selectRelaSameStaff(map);
                if (rela == null) {
                    //查询家属对应的关系（因为这里有一个大前提，就是相同的员工，所以无需加上相同员工下的条件）
                    rela = fcStaffFamilyRelaMapper.selectPersonIdInfo(personId);
                }
                if (rela != null) {
                    if ("3".equals(rela.getRelation())) {
                        // 3为子女。 员工是被保人的父母。这里需要拆分成父亲或母亲
                        if ("0".equals(staffInfo.getSex())) {
                            dataMap.put("relation", "父亲");
                        } else {
                            dataMap.put("relation", "母亲");
                        }
                    } else {
                        String relationName = fdCodeMapper.selectNameByCode("Relation", rela.getRelation());
                        dataMap.put("relation", relationName);
                    }
                } else {
                    dataMap.put("relation", "");
                }
                List<FCPlanRiskDuty> dutyList = new ArrayList<FCPlanRiskDuty>();
                List<Map<String, Object>> riskList = (List<Map<String, Object>>) paramMap.get("RiskList");
                for (Map<String, Object> riskMap : riskList) {
                    FCPlanRiskDuty fcPlanRiskDuty = new FCPlanRiskDuty();
                    String amountGrageCode = riskMap.get("AmountGrageCode").toString();
                    FcDutyAmountGrade fcDutyAmountGrade = fcDutyAmountGradeMapper.selectByPrimaryKey(amountGrageCode);
                    fcPlanRiskDuty.setDutyCode(fcDutyAmountGrade.getDutyCode());
                    fcPlanRiskDuty.setAmnt(fcDutyAmountGrade.getAmnt());
                    dutyList.add(fcPlanRiskDuty);
                }
                //在这里判断弹性福利中是否含有身故保险
                List<String> dutyCodeList = Arrays.asList("GD0017", "GD0037", "GD0050", "GD0051", "GD0052", "GD0054");
                int count = 0;
                for (FCPlanRiskDuty dutyInfo : dutyList) {
                    if (dutyCodeList.contains(dutyInfo.getDutyCode())) {
                        count++;
                    }
                }
                if (0 == count) {
                    resultMap.put("code", "200");
                    resultMap.put("message", "该计划中不包含身故险种，故不弹出确认函");
                    return JSON.toJSONString(resultMap);
                }
                double deathAmnt = 0.00;
                for (FCPlanRiskDuty dutyInfo : dutyList) {
                    if ("GD0017".equals(dutyInfo.getDutyCode())) {
                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                    } else if ("GD0037".equals(dutyInfo.getDutyCode())) {
                        deathAmnt = CommonUtil.add(deathAmnt, dutyInfo.getAmnt());
                    }
                }
                double Amnt15070 = 0.00;
                for (FCPlanRiskDuty dutyInfo : dutyList) {
                    if ("GD0050".equals(dutyInfo.getDutyCode())) {
                        Amnt15070 = dutyInfo.getAmnt();
                    }
                    if ("GD0051".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                        Amnt15070 = dutyInfo.getAmnt();
                    }
                    if ("GD0052".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                        Amnt15070 = dutyInfo.getAmnt();
                    }
                    if ("GD0054".equals(dutyInfo.getDutyCode()) && Amnt15070 < dutyInfo.getAmnt()) {
                        Amnt15070 = dutyInfo.getAmnt();
                    }
                }
                deathAmnt = CommonUtil.add(deathAmnt, Amnt15070);
                if (0.00 == deathAmnt) {
                    resultMap.put("code", "200");
                    resultMap.put("message", "本次投保身故保险金额为0，故不弹出确认函");
                    return JSON.toJSONString(resultMap);
                }
                String riskAmnt = getRiskAmnt(personId);
                if (riskAmnt == null || "".equals(riskAmnt)) {
                    log.info("调用核心接口查询风险保额失败，请联系管理员！");
                    resultMap.put("code", "500");
                    resultMap.put("message", "系统繁忙");
                    return JSON.toJSONString(resultMap);
                }
                dataMap.put("deathAmnt", deathAmnt);            //本次身故保险金
                dataMap.put("deathAmntCount", riskAmnt);        //累计身故保险金
                dataMap.put("month", DateTimeUtil.getCurrentMonth());
                dataMap.put("year", DateTimeUtil.getCurrentYear());
                dataMap.put("month", DateTimeUtil.getCurrentMonth());
                dataMap.put("day", DateTimeUtil.getCurrentDay());

                resultMap.put("code", "300");
                resultMap.put("message", "未成年人投保确认书");
                resultMap.put("data", dataMap);
                return JSON.toJSONString(resultMap);
            } else {
                resultMap.put("code", "200");
                resultMap.put("message", "年龄校验通过");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            resultMap.put("code", "500");
            resultMap.put("message", "系统繁忙");
            return JSON.toJSONString(resultMap);
        }
    }

    public boolean ageValidate(long age, String planCode, String ensureCode) {
        List<FcPlanConfig> fcPlanConfigList = fcPlanConfigMapper.getFCPlanConfigByPlanCode(planCode, ensureCode);
        boolean bool = false;
        Double upper = null;  //上限
        Double lower = null;    //下限
        String upperUnit = "";  //上限单位
        String lowerUnit = "";    //下限单位
        String upperSymbol = "";    //上限符号
        String lowerSymbol = "";    //下限符号
        for (FcPlanConfig fcPlanConfig : fcPlanConfigList) {
            String configNo = fcPlanConfig.getConfigNo();
            switch (configNo) {
                case "005":
                    lower = Double.parseDouble(fcPlanConfig.getConfigValue());
                    break;
                case "006":
                    lowerUnit = fcPlanConfig.getConfigValue();
                    break;
                case "007":
                    lowerSymbol = fcPlanConfig.getConfigValue();
                    break;
                case "008":
                    upper = Double.parseDouble(fcPlanConfig.getConfigValue());
                    break;
                case "009":
                    upperUnit = fcPlanConfig.getConfigValue();
                    break;
                case "010":
                    upperSymbol = fcPlanConfig.getConfigValue();
                    break;
            }
        }
        if (upper != null && lower != null) {
            if ("Y".equals(lowerUnit)) {
                lower = lower * 365;
            }
            if ("Y".equals(upperUnit)) {
                upper = upper * 365;
            }

            if ("<".equals(upperSymbol) && ("<".equals(lowerSymbol) || "<=".equals(lowerSymbol))) {
                if (age < upper) {
                    bool = true;
                }
            } else if ("<=".equals(upperSymbol) && ("<".equals(lowerSymbol) || "<=".equals(lowerSymbol))) {
                if (age <= upper) {
                    bool = true;
                }
            } else if ("<".equals(upperSymbol) && (">".equals(lowerSymbol))) {
                if (age < upper && age > lower) {
                    bool = true;
                }
            } else if ("<".equals(upperSymbol) && (">=".equals(lowerSymbol))) {
                if (age < upper && age >= lower) {
                    bool = true;
                }
            } else if ("<=".equals(upperSymbol) && (">".equals(lowerSymbol))) {
                if (age <= upper && age > lower) {
                    bool = true;
                }
            } else if ("<=".equals(upperSymbol) && (">=".equals(lowerSymbol))) {
                if (age <= upper && age >= lower) {
                    bool = true;
                }
            } else if (">".equals(upperSymbol) && ("<".equals(lowerSymbol))) {
                if (age > upper || age < lower) {
                    bool = true;
                }
            } else if (">".equals(upperSymbol) && ("<=".equals(lowerSymbol))) {
                if (age > upper || age <= lower) {
                    bool = true;
                }
            } else if (">=".equals(upperSymbol) && ("<".equals(lowerSymbol))) {
                if (age >= upper || age < lower) {
                    bool = true;
                }
            } else if (">=".equals(upperSymbol) && ("<=".equals(lowerSymbol))) {
                if (age >= upper || age <= lower) {
                    bool = true;
                }
            } else if ((">".equals(upperSymbol) || ">=".equals(upperSymbol)) && (">".equals(lowerSymbol))) {
                if (age > lower) {
                    bool = true;
                }
            } else if ((">".equals(upperSymbol) || ">=".equals(upperSymbol)) && (">=".equals(lowerSymbol))) {
                if (age >= lower) {
                    bool = true;
                }
            }
        } else if (upper != null && lower == null) {
            if ("Y".equals(upperUnit)) {
                upper = upper * 365;
            }

            if ("<".equals(upperSymbol)) {
                if (age < upper) {
                    bool = true;
                }
            } else if ("<=".equals(upperSymbol)) {
                if (age <= upper) {
                    bool = true;
                }
            } else if (">=".equals(upperSymbol)) {
                if (age >= upper) {
                    bool = true;
                }
            } else if (">".equals(upperSymbol)) {
                if (age > upper) {
                    bool = true;
                }
            }
        } else if (lower != null && upper == null) {
            if ("Y".equals(lowerUnit)) {
                lower = lower * 365;
            }
            if ("<".equals(lowerSymbol)) {
                if (age < lower) {
                    bool = true;
                }
            } else if ("<=".equals(lowerSymbol)) {
                if (age <= lower) {
                    bool = true;
                }
            } else if (">=".equals(lowerSymbol)) {
                if (age >= lower) {
                    bool = true;
                }
            } else if (">".equals(lowerSymbol)) {
                if (age > lower) {
                    bool = true;
                }
            }
        }
        return bool;
    }


    /**
     * <AUTHOR>
     * @description健康须知
     * @date 20:25 20:25
     * @modified
     */
    public String selectflaninform(String token, String planCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //查询健康问题
            List<Map<String, String>> fcPlanInformList = fcPlanInformMapper.selectPlanInform(planCode);
            List<Map<String, String>> mapList = new ArrayList<>();
            for (int i = 1; i < fcPlanInformList.size(); i++) {
                Map<String, String> planInfoMap = new HashMap<>();
                //判断详细描述是否为空
                if (fcPlanInformList.get(i).get("InformDescribe") == null || "".equals(fcPlanInformList.get(i).get("InformDescribe"))) {
                    String informContent = fcPlanInformList.get(i).get("InformContent");
                    planInfoMap.put("InformContent", i + "、" + informContent);
                } else {
                    String informContent = fcPlanInformList.get(i).get("InformContent") + fcPlanInformList.get(i).get("InformDescribe");
                    planInfoMap.put("InformContent", i + "、" + informContent);
                }
                mapList.add(planInfoMap);
            }
            resultMap.put("data", mapList);
            resultMap.put("message", "查询健康问题成功");
            resultMap.put("code", "200");
        } catch (Exception e) {
            log.info("查询健康失败", e);
            resultMap.put("message", "查询健康失败");
            resultMap.put("code", "500");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description个人保存及修改判断
     * @date 19:56 19:56
     * @modified
     */
    public String getInsureSaveByUpdate(String token, String personId, String planCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            /****************************当前员工不存在当前的家属则同步信息************************/
            Map<String, Object> SyncMap = SynchronizationFamilyinfo(token, personId);
            if (!SyncMap.get("success").equals(true)) {
                resultMap.put("message", SyncMap.get("errmsg"));
                resultMap.put("code", "500");
                return JSON.toJSONString(resultMap);
            }
            personId = String.valueOf(SyncMap.get("personId"));
            String grpNo = String.valueOf(SyncMap.get("grpNo"));
            String perNo = String.valueOf(SyncMap.get("perNo"));
            String ensureCode = String.valueOf(SyncMap.get("ensureCode"));
            /***************************************end*******************************************/

            //获取正式的表
            Map<String, Object> map = new HashMap<>();
            Map<String, String> maps = new HashMap<>();
            maps.put("personID", personId);
            maps.put("perNo", perNo);
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(maps);
            String relation = fcStaffFamilyRela.getRelation();
            if (relation.equals("0")) {
                Map<String, Object> defaultPlanMap = new HashMap<>();
                defaultPlanMap.put("ensureCode", ensureCode);
                defaultPlanMap.put("personId", personId);
                //获取默认计划编号
                List<FCDefaultPlan> fcDefaultPlan = fcDefaultPlanMapper.selectDefaultPlans(defaultPlanMap);
                String defaultPlanCode = fcDefaultPlan.get(0).getPlanCode();
                Map<String, String> param = new HashMap<>();
                param.put("planCode", defaultPlanCode);
                param.put("ensureCode", ensureCode);
                FCEnsurePlan defaultEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                //默认计划保费
                Double defaultPrem = defaultEnsurePlan.getTotalPrem();
                //计划保费
                map.clear();
                param.put("planCode", planCode);
                param.put("ensureCode", ensureCode);
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
                Double planPrem = fcEnsurePlan.getTotalPrem();
                if (planPrem < defaultPrem) {
                    resultMap.put("message", "投选的计划保费不能低于默认计划保费");
                    resultMap.put("code", "0");
                    return JSON.toJSONString(resultMap);
                }
            }
            //福利编号
            map.put("ensureCode", ensureCode);
            map.put("personId", personId);
            List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectEnsureCodeByPersonId(map);
            //判断是否投过保
            if (fpInsurePlanList.size() < 1) {
                log.info("修改人的个人编号" + personId);
                //投保
                String savePlanInsure = saveInsureInfo(token, personId, planCode, ensureCode, perNo);
                return savePlanInsure;
            }
            //修改投保
            for (FPInsurePlan fpInsurePlan : fpInsurePlanList) {
                log.info("投保人的个人编号" + fpInsurePlan);
                String insurePlanNo = fpInsurePlan.getInsurePlanNo();
                String updatePlanInsure = updateInsureInfo(token, insurePlanNo, planCode);
                return updatePlanInsure;
            }
        } catch (Exception e) {
            log.info("判断失败", e);
            resultMap.put("code", "500");
            resultMap.put("message", "判断失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @descriptionp判断家属是否存在
     * @date 20:47 20:47
     * @modified
     */
    public String isPlanExist(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String perNo = globalInput.getCustomNo();
            String ensureCode = globalInput.getEnsureCode();
            Map<String, Object> map = new HashMap<>();
            List<FCStaffFamilyRela> fcStaffFamilyRelaList = fcStaffFamilyRelaMapper.getPersonByInfo(perNo);
            for (FCStaffFamilyRela fcStaffFamilyRela : fcStaffFamilyRelaList) {
                if (!fcStaffFamilyRela.getRelation().equals("0")) {
                    String personId = fcStaffFamilyRela.getPersonID();
                    map.put("ensureCode", ensureCode);
                    map.put("personId", personId);
                    List<FPInsurePlan> fpInsurePlanList = fpInsurePlanMapper.selectEnsureCodeByPersonId(map);
                    if (fpInsurePlanList.size() > 0) {
                        resultMap.put("isPlan", "1");
                        resultMap.put("message", "该家属以投过计划");
                        return JSON.toJSONString(resultMap);
                    }
                    resultMap.put("message", "家属没有投保");
                    resultMap.put("isPlan", "0");
                }
            }
        } catch (Exception e) {
            log.info("判断失败", e);
            resultMap.put("code", "500");
            resultMap.put("message", "判断失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description查询雷达图
     * @date 17:41 17:41
     * @modified
     */
    public String getRiskType(String token, String planCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            List<Map<String, Object>> mapList = riskType(token, planCode);
            resultMap.put("code", "200");
            resultMap.put("message", "查询雷达成功");
            resultMap.put("data", mapList);
        } catch (Exception e) {
            log.info("查询雷达图失败", e);
            resultMap.put("code", "500");
            resultMap.put("messag", "查询雷达图失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description封装参数
     * @date 10:04 10:04
     * @modified
     */
    public List<Map<String, Object>> riskType(String token, String planCode) {
        GlobalInput globalInput = userService.getSession(token);
        String ensureCode = globalInput.getEnsureCode();
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("riskType", "1");
        map1.put("Prem", 0.0);
        Map<String, Object> map2 = new HashMap<>();
        map2.put("riskType", "2");
        map2.put("Prem", 0.0);
        Map<String, Object> map3 = new HashMap<>();
        map3.put("riskType", "3");
        map3.put("Prem", 0.0);
        Map<String, Object> map4 = new HashMap<>();
        map4.put("riskType", "4");
        map4.put("Prem", 0.0);
        Map<String, Object> map5 = new HashMap<>();
        map5.put("riskType", "5");
        map5.put("Prem", 0.0);
        Map<String, Object> map6 = new HashMap<>();
        map6.put("riskType", "6");
        map6.put("Prem", 0.0);
        List<FCPlanRisk> rickList = fcPlanRiskMapper.selectRiskInfo(ensureCode, planCode);
        Double prem1 = 0.0;
        Double prem2 = 0.0;
        Double prem3 = 0.0;
        Double prem4 = 0.0;
        Double prem5 = 0.0;
        Double prem6 = 0.0;
        for (int i = 0; i < rickList.size(); i++) {
            //每个险种的编码
            String rickCode = rickList.get(i).getRiskCode();
            List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyInfo(ensureCode, planCode, rickCode);
            Double amnt = 0.0;
            for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                //获取该险种下的总保额
                amnt += fcPlanRiskDuty.getAmnt();
            }
            FDRiskInfo fdRiskInfo = fdRiskInfoMapper.selectByPrimaryKey(rickCode);
            String riskType = fdRiskInfo.getRiskType();
            switch (riskType) {
                case "1":
                    map1.put("Prem", prem1 += amnt);
                    break;
                case "2":
                    map2.put("Prem", prem2 += amnt);
                    break;
                case "3":
                    map3.put("Prem", prem3 += amnt);
                    break;
                case "4":
                    map4.put("Prem", prem4 += amnt);
                    break;
                case "5":
                    map5.put("Prem", prem5 += amnt);
                    break;
                case "6":
                    map6.put("Prem", prem6 += amnt);
                    break;
                default:
                    System.out.println("没有这个险");
            }
        }
        mapList.add(map1);
        mapList.add(map2);
        mapList.add(map3);
        mapList.add(map4);
        mapList.add(map5);
        mapList.add(map6);
        return mapList;
    }

    /**
     * 查询所投计划
     *
     * @param personId
     * @param choiceRisk
     * @return
     */
    public String getPlanList(String personId, String choiceRisk) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, String>> insurePlanList = new ArrayList<>();
        try {
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
            //姓名
            String name = fcPerson.getName();
            resultMap.put("name", name);
            //获取关系
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectPersonIdInfo(personId);
            //关系
            String relation = fcStaffFamilyRela.getRelation();
            resultMap.put("relation", relation);
            Map<String, Object> personIDMap = new HashMap<>();
            personIDMap.put("personId", personId);
            List<Map<String, String>> fpInsurePlan = fpInsurePlanMapper.selectPlanCodeByPersonId(personIDMap);
            if (fpInsurePlan.size() < 1) {
                resultMap.put("isPlan", "0");
                resultMap.put("code", "200");
                resultMap.put("message", "未查询到相关投保计划");
                return JSON.toJSONString(resultMap);
            }
            //判断员工及家属是否投过保
            for (Map<String, String> map : fpInsurePlan) {
                insurePlanList.add(map);
                resultMap.put("isPlan", "1");
            }
            if (insurePlanList.size() < 1) {
                resultMap.put("isPlan", "0");
                resultMap.put("code", "200");
                resultMap.put("message", "未查询到相关投保计划");
                return JSON.toJSONString(resultMap);
            }
            //
            List<Object> list = new ArrayList<>();
            for (Map<String, String> map : insurePlanList) {
                List<Object> planList = new ArrayList<>();
                String planCode = map.get("PlanCode");
                String personID = map.get("PersonId");
                String ensureCode = map.get("EnsureCode");
                FCEnsure fcEnsure = fcEnsureMapper.getFCEnsureByPlanCode(ensureCode, planCode);
                //查询以投保的计划
                Map<String, Object> paramOne = new HashMap<>();
                paramOne.put("ensureCode", fcEnsure.getEnsureCode());
                paramOne.put("planCode", planCode);
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanMapper.selectByPrimaryKey(paramOne);
                //获取计划个人缴费以及企业缴费
                FCOrderItem fcOrderItem = fcOrderItemMapper.getByPersonId(ensureCode, planCode, personID);
                //获取计划名称
                String planName = fcEnsurePlan.getPlanName();
                //查询该计划下所有险种
                List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectRiskInfo(ensureCode, planCode);

                //查询险种的信息及员工缴纳的费用
                if (choiceRisk == "0" || "0".equals(choiceRisk)) {
                    //移动端
                    for (FCPlanRisk fcPlanRisk : fcPlanRiskList) {
                        double count = 0;
                        String riskCode = fcPlanRisk.getRiskCode();
                        //查询险种名称
                        String riskName = fdRiskInfoMapper.selectRiskNameByRiskCode(riskCode);
                        Map<String, Object> riskMap = new HashMap<>();
                        riskMap.put("riskName", riskName);
                        riskMap.put("RiskCode", fcPlanRisk.getRiskCode());
                        //查询责任信息
                        List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyInfo(ensureCode, planCode, riskCode);
                        List<Map<String, Object>> mapArrayList = new ArrayList<>();
                        for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                            Map<String, Object> map8 = new HashMap<>();
                            count = count + fcPlanRiskDuty.getPrem();
                            map8.put("DutyCode", fcPlanRiskDuty.getDutyCode());
                            Double amnt = fcPlanRiskDuty.getAmnt();
                            //四舍五入
                            DecimalFormat df = new DecimalFormat("#.00");
                            map8.put("Amnt", df.format(amnt));
                            //获取责任名称
                            Map<String, String> dutyMap = new HashMap<>();
                            dutyMap.put("dutyCode", fcPlanRiskDuty.getDutyCode());
                            dutyMap.put("riskCode", riskCode);
                            Map<String, Object> fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                            map8.put("DutyName", fdRiskDutyInfo.get("DutyName"));
                            mapArrayList.add(map8);
                        }
                        DecimalFormat ds = new DecimalFormat("#.00");
                        riskMap.put("Prem", ds.format(count));
                        riskMap.put("dutyArr", mapArrayList);
                        //获取计划总保费
                        Double totalPrem = fcEnsurePlan.getTotalPrem();
                        riskMap.put("insurePrem", fcOrderItem.getSelfPrem());
                        riskMap.put("defaultPrem", fcOrderItem.getGrpPrem());
                        riskMap.put("totalPrem", totalPrem);
                        riskMap.put("planName", planName);
                        riskMap.put("planCode", planCode);
                        FDCodeKey key = new FDCodeKey();
                        key.setCodeType("PolicyState");
                        key.setCodeKey(fcEnsure.getPolicyState());
                        FDCode fdCode = fdCodeMapper.selectByPrimaryKey(key);
                        riskMap.put("policyState", fdCode.getCodeName());
                        riskMap.put("ensureCode", fcEnsure.getEnsureCode());
                        planList.add(riskMap);
                    }
                    //pc端
                } else {
                    //查询一个险种
                    for (FCPlanRisk fcPlanRisk : fcPlanRiskList) {
                        String riskCode = fcPlanRisk.getRiskCode();
                        String riskName = fdRiskInfoMapper.selectRiskNameByRiskCode(riskCode);
                        List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyInfo(ensureCode, planCode, riskCode);
                        for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                            Map<String, Object> map7 = new HashMap<>();
                            map7.put("Prem", fcPlanRiskDuty.getPrem());
                            map7.put("RiskCode", fcPlanRisk.getRiskCode());
                            map7.put("DutyCode", fcPlanRiskDuty.getDutyCode());
                            map7.put("RiskName", riskName);
                            map7.put("planName", planName);
                            map7.put("planCode", planCode);
                            Double amnt = fcPlanRiskDuty.getAmnt();
                            //四舍五入
                            DecimalFormat df = new DecimalFormat("#.00");
                            map7.put("Amnt", df.format(amnt));
                            //获取责任名称
                            Map<String, String> dutyMap = new HashMap<>();
                            dutyMap.put("dutyCode", fcPlanRiskDuty.getDutyCode());
                            dutyMap.put("riskCode", riskCode);
                            Map<String, Object> fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                            map7.put("DutyName", fdRiskDutyInfo.get("DutyName"));
                            //获取计划总保费
                            Double totalPrem = fcEnsurePlan.getTotalPrem();
                            map7.put("insurePrem", fcOrderItem.getSelfPrem());
                            map7.put("defaultPrem", fcOrderItem.getGrpPrem());
                            map7.put("totalPrem", totalPrem);
                            map7.put("planName", planName);
                            map7.put("planCode", planCode);
                            FDCodeKey key = new FDCodeKey();
                            key.setCodeType("PolicyState");
                            key.setCodeKey(fcEnsure.getPolicyState());
                            FDCode fdCode = fdCodeMapper.selectByPrimaryKey(key);
                            map7.put("policyState", fdCode.getCodeName());
                            map7.put("ensureCode", fcEnsure.getEnsureCode());
                            planList.add(map7);
                        }
                    }
                }
                Map<String, Object> newmap = new HashMap<>();
                newmap.put("plan", planList);
                newmap.put("planCode", planCode);
                list.add(newmap);
            }
            if (list.size() > 0) {
                resultMap.put("code", "200");
                resultMap.put("message", "个人所投计划查询成功");
                resultMap.put("planList", list);
            } else {
                resultMap.put("code", "203");
                resultMap.put("message", "个人未投计划！");
                resultMap.put("planList", list);
            }
        } catch (Exception e) {
            log.info("个人所投计划查询失败", e);
            resultMap.put("code", "500");
            resultMap.put("message", "个人所投计划查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询所投计划险种
     *
     * @param planCodeInfo
     * @return
     */
    public String checkRisk(List<Map<String, String>> planCodeInfo) {
        Map<String, Object> resultMap = new HashMap<>();
        double sumAmnt1 = 0.00;
        double sumAmnt2 = 0.00;
        double sumAmnt3 = 0.00;
        double sumAmnt4 = 0.00;
        double sumAmnt5 = 0.00;
        double sumAmnt6 = 0.00;
        try {
            Map<String, Object> map1 = new HashMap<>();
            map1.put("riskType", "1");
            map1.put("isRisk", "0");
            map1.put("Amnt", 0);
            Map<String, Object> map2 = new HashMap<>();
            map2.put("riskType", "2");
            map2.put("isRisk", "0");
            map2.put("Amnt", 0);
            Map<String, Object> map3 = new HashMap<>();
            map3.put("riskType", "3");
            map3.put("isRisk", "0");
            map3.put("Amnt", 0);
            Map<String, Object> map4 = new HashMap<>();
            map4.put("riskType", "4");
            map4.put("isRisk", "0");
            map4.put("Amnt", 0);
            Map<String, Object> map5 = new HashMap<>();
            map5.put("riskType", "5");
            map5.put("isRisk", "0");
            map5.put("Amnt", 0);
            Map<String, Object> map6 = new HashMap<>();
            map6.put("riskType", "6");
            map6.put("isRisk", "0");
            map6.put("Amnt", 0);
            //四舍五入
//			DecimalFormat df = new DecimalFormat("#.00");
            for (int j = 0; j < planCodeInfo.size(); j++) {
                Map<String, String> map = planCodeInfo.get(j);
                String ensureCode = map.get("ensureCode");
                String planCode = map.get("planCode");
                String number = fcEnsureMapper.getEffEctiveEnsure(ensureCode); //查询该计划所在福利是否失效  返回 1-有效
                if ("1".equals(number)) {
                    //查询该计划下所有险种
                    List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectRiskInfo(ensureCode, planCode);
                    //查询每一个险种
                    for (int i = 0; i < fcPlanRiskList.size(); i++) {
                        //获取每一个险种编号
                        String riskCode = fcPlanRiskList.get(i).getRiskCode();
                        List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyInfo(ensureCode, planCode, riskCode);
                        Double Amnt = 0.00;
                        for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                            Double amnt = fcPlanRiskDuty.getAmnt();
                            Amnt += amnt;        //获取该险种下所有责任的总计保额
                        }
                        //查询险种对应的险种类型
                        FDRiskInfo fdRiskInfo = fdRiskInfoMapper.selectByPrimaryKey(riskCode);
                        String riskType = fdRiskInfo.getRiskType();
                        //获取已投保险种总称
                        switch (riskType) {
                            case "1":
                                sumAmnt1 += Amnt;                //获取该用户下的所投保福利下同一险种下的保额总计
                                map1.put("Amnt", sumAmnt1);
                                map1.put("isRisk", "1");
                                break;
                            case "2":
                                sumAmnt2 += Amnt;
                                map2.put("Amnt", sumAmnt2);
                                map2.put("isRisk", "1");
                                break;
                            case "3":
                                sumAmnt3 += Amnt;
                                map3.put("Amnt", sumAmnt3);
                                map3.put("isRisk", "1");
                                break;
                            case "4":
                                sumAmnt4 += Amnt;
                                map4.put("Amnt", sumAmnt4);
                                map4.put("isRisk", "1");
                                break;
                            case "5":
                                sumAmnt5 += Amnt;
                                map5.put("Amnt", sumAmnt5);
                                map5.put("isRisk", "1");
                                break;
                            case "6":
                                sumAmnt6 += Amnt;
                                map6.put("Amnt", sumAmnt6);
                                map6.put("isRisk", "1");
                                break;
                        }
                    }
                }
            }
            List<Map<String, Object>> list = new ArrayList<>();
            list.add(map1);
            list.add(map2);
            list.add(map3);
            list.add(map4);
            list.add(map5);
            list.add(map6);
            resultMap.put("isInsure", list);
            resultMap.put("code", "200");
            resultMap.put("message", "所投计划险种信息查询成功");
        } catch (Exception e) {
            log.info("所投计划险种信息查询失败", e);
            resultMap.put("code", "500");
            resultMap.put("message", "所投计划险种信息查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description查询福利计划
     * @date 9:11 9:11
     * @modified
     */
    public String getEnsureInfo(String token, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String perNo = globalInput.getCustomNo();
            List<String> personList = fcPersonMapper.getAllPerson(personId);
            List<HashMap<String, Object>> mapList = new ArrayList<>();
            for (String personID : personList) {
                List<HashMap<String, Object>> mapLists = personalRisk(perNo, personID);
                if (mapLists.size() > 0) {
                    mapList.addAll(mapLists);
                }
            }
            if (mapList.size() > 0) {
                PageHelperUtil<HashMap<String, Object>> teamPageInfo = new PageHelperUtil<>(mapList);
                resultMap.put("data", teamPageInfo.getList());
            } else {
                resultMap.put("data", "");
            }
            resultMap.put("code", "200");
            resultMap.put("message", "查询成功");
        } catch (Exception e) {
            log.info("查询失败", e);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    public List<HashMap<String, Object>> personalRisk(String perNo, String personId) {
        List<HashMap<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("personId", personId);
        List<Map<String, Object>> fcGrpOrderList = fcGrpOrderMapper.selectGrpOrderNoList(map);
        for (Map<String, Object> fcGrpOrderMap : fcGrpOrderList) {
            personId = String.valueOf(fcGrpOrderMap.get("PersonID"));
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("grpContNo", fcGrpOrderMap.get("GrpContNo"));
            hashMap.put("contNo", fcGrpOrderMap.get("ContNo"));
            map.clear();
            map.put("personId", personId);
            map.put("grpOrderNo", fcGrpOrderMap.get("GrpOrderNo"));
            FCEnsure fcEnsure = fcOrderInsuredMapper.getFcensureByPersonID(map);
            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectOrderItemNo((String) fcGrpOrderMap.get("GrpOrderNo"), (String) fcGrpOrderMap.get("orderNo"), personId);
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(fcOrderInsured.getOrderItemNo());
            String cvaliDate = fcEnsure.getCvaliDate();
            String policyEndDate = fcEnsure.getPolicyEndDate();
            double invoiceAmount = 0.0;
            List<FCOrderItem> fcOrderItemList = fcOrderItemMapper.selectOrderNo(fcOrderItem.getOrderNo());
            for (FCOrderItem fcOrderItem1 : fcOrderItemList) {
                invoiceAmount = CommonUtil.add(invoiceAmount, fcOrderItem1.getSelfPrem());
            }
            map.put("personId", personId);
            map.put("customType", ConstantUtil.EnsureState_1);
            List<FCMailInfo> fcMailInfoList = fcMailInfoMapper.getMailInfoByPersonId(map);
            hashMap.put("ensureCode", fcEnsure.getEnsureCode());
            hashMap.put("ensureDate", "<p>" + cvaliDate + "</p>" + "<p>" + "至" + "</p>" + "<p>" + policyEndDate + "</p>");
            hashMap.put("cvaliDate", cvaliDate);
            hashMap.put("personelPrem", fcOrderItem.getSelfPrem());
            hashMap.put("ensurePrem", fcOrderItem.getSelfPrem() + fcOrderItem.getGrpPrem());
            hashMap.put("invoiceAmount", invoiceAmount);
            hashMap.put("PolicyState", fcEnsure.getPolicyState());
            hashMap.put("insurePrem", fcOrderItem.getSelfPrem());
            hashMap.put("defaultPrem", fcOrderItem.getGrpPrem());
            hashMap.put("personId", personId);
            if (fcMailInfoList.size() > 0) {
                hashMap.put("isMail", "1");
                mapList.add(hashMap);
                return mapList;
            }
            hashMap.put("isMail", "0");
            mapList.add(hashMap);
        }
        return mapList;
    }

    /**
     * 投保须知
     *
     * @param authorization
     * @return
     */
    public String getInsureNotes(String authorization, String ensureCode) {
        ResponseMsg<Map<String, Object>> responseMsg = new ResponseMsg<>();
        try {
            GlobalInput globalInput = userService.getSession(authorization);
            Map<String, Object> insureNoteMap = new HashMap<>();
            if (StringUtils.isEmpty(ensureCode) || ensureCode.equals(Boolean.TRUE.toString())) {
                ensureCode = globalInput.getEnsureCode();
            }
            String grpNo = globalInput.getGrpNo();
            String perNo = globalInput.getCustomNo();
            log.info("" + ensureCode);
            if ("".equals(ensureCode) || ensureCode == null) {
                return "该员工不存在福利";
            }
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
            fcEnsureConfig.setEnsureCode(ensureCode);
            fcEnsureConfig.setGrpNo(grpNo);
            List<FCEnsureConfig> fcEnsureConfigList = fcEnsureConfigMapper.getInsureNote(fcEnsureConfig);
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPerNo(globalInput.getCustomNo());
            // 获取员工的personId
            String personId = "";
            if (fcStaffFamilyRela != null) {
                personId = fcStaffFamilyRela.getPersonID();
            }
            //List<Map<String, Object>> insureNotesList = fcEnsureMapper.selectInsureNotes(ensureCode,grpNo);
            if (fcEnsureConfigList.size() > 0 && fcEnsureConfigList != null) {
                for (FCEnsureConfig insureNote : fcEnsureConfigList) {
                    String configNo = insureNote.getConfigNo();
                    String configValue = insureNote.getConfigValue();
                    if (configValue == null || "".equals(configValue)) {
                        continue;
                    } else {
                        switch (configNo) {
                            case "003":
                                // 医疗机构
                                if (!configValue.substring(configValue.length() - 1).matches("^[。.]+$")) {
                                    configValue += "。";
                                }
                                insureNoteMap.put("medicalInstitution", "医疗机构说明:" + configValue);
                                break;
                            case "005":
                                // 既往症
                                if (!configValue.substring(configValue.length() - 1).matches("^[。.]+$")) {
                                    configValue += "。";
                                }
                                insureNoteMap.put("anamnesis", "既往症:" + configValue);
                                break;
                            case "006":
                                // 其他约定
                                if (!configValue.substring(configValue.length() - 1).matches("^[。.]+$")) {
                                    configValue += "。";
                                }
                                insureNoteMap.put("otherConventions", "其他约定:" + configValue);
                                break;
                            case "018":
                                // 特别约定
                                if (!configValue.substring(configValue.length() - 1).matches("^[。.]+$")) {
                                    configValue += "。";
                                }
                                insureNoteMap.put("specialAgreement", "特别约定:" + configValue);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            String startAppntDate = fcEnsure.getStartAppntDate() == null ? "" : fcEnsure.getStartAppntDate();
            String endAppntDate = fcEnsure.getEndAppntDate() == null ? "" : fcEnsure.getEndAppntDate();
            String cvaliDate = fcEnsure.getCvaliDate() == null ? "" : fcEnsure.getCvaliDate();
            String policyEndDate = fcEnsure.getPolicyEndDate() == null ? "" : fcEnsure.getPolicyEndDate();
            Map<String, Object> dateMap = new HashMap<>(4);
            Map<String, Object> map = new HashMap<>();
            log.info("" + globalInput.getCustomNo());
            map.put("perNo", globalInput.getCustomNo());
            map.put("ensureCode", ensureCode);
            FCDefaultPlan fcDefaultPlan = fcDefaultPlanMapper.selectDoublePlanByPerNo(map);
            FCEnsurePlan fcEnsurePlan = null;
            if (fcDefaultPlan != null) {
                Map<String, String> param = new HashMap<>();
                param.put("planCode", fcDefaultPlan.getPlanCode());
                param.put("ensureCode", fcDefaultPlan.getEnsureCode());
                fcEnsurePlan = fcEnsurePlanMapper.selectByKey(param);
            }
            String planType = fcEnsure.getPlanType();
            if (planType != null && !"".equals(planType) && planType.equals("1")) {
                //获取员工信息
                FCPerInfo fcperinfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
                //从个人注册期表查询员工职级
                String levelCode = fcPerRegistDayMapper.selectLevelCodeByPerNo(perNo, ensureCode);
                Map<String, Object> paraMap = new HashMap<String, Object>();
                paraMap.put("EnsureCode", ensureCode);
                paraMap.put("grpNo", grpNo);
                paraMap.put("BirthDay", fcperinfo.getBirthDay());
                if (StringUtil.isEmpty(levelCode)) {
                    return JSON.toJSONString(ResultUtil.error("员工职级查询有误！"));
                }
                paraMap.put("LevelCode", levelCode);
                paraMap.put("OccupationType", fcperinfo.getOccupationType());
                paraMap.put("Sex", fcperinfo.getSex());
                paraMap.put("ServiceTerm", fcperinfo.getServiceTerm());
                paraMap.put("Retirement", fcperinfo.getRetirement());
                paraMap.put("Relation", "0");
                paraMap.put("JoinMedProtect", fcperinfo.getJoinMedProtect());
                paraMap.put("InsureCount", fcEnsure.getInsuredNumber());
                //查询最小保额的默认档次
                List<Map<String, Object>> minAmntDefaultList = fcEnsureMapper.selectMinAmntDefault(paraMap);
                dateMap.put("includDefault", minAmntDefaultList != null && minAmntDefaultList.size() != 0);
            }
            dateMap.put("personId", personId);
            dateMap.put("totalPrem", fcEnsurePlan == null ? "" : fcEnsurePlan.getTotalPrem());
            dateMap.put("ensureCode", ensureCode);
            dateMap.put("PlanCode", fcDefaultPlan == null ? "" : fcDefaultPlan.getPlanCode());
            dateMap.put("planName", fcEnsurePlan == null ? "" : fcEnsurePlan.getPlanName());
            dateMap.put("startAppntDate", startAppntDate);
            dateMap.put("endAppntDate", endAppntDate);
            dateMap.put("cvaliDate", cvaliDate);
            dateMap.put("policyEndDate", policyEndDate);
            insureNoteMap.put("date", dateMap);
            responseMsg.okStatus().message("投保须知信息查询成功").data(insureNoteMap);
        } catch (Exception e) {
            log.info("投保须知查询失败", e);
            responseMsg.errorStatus().message("投保须知查询失败");
        }
        return JSON.toJSONString(responseMsg);
    }

    /**
     * <AUTHOR>
     * @description删除订单及子订单
     * @date 16:25 16:25
     * @modified
     */
    public String deleteConfirmInfo(List<FCOrder> fcOrderList) {
        for (int i = 0; i < fcOrderList.size(); i++) {
            //删除子订单详情表
            fcOrderItemDetailMapper.deleteByOrderNo(fcOrderList.get(i).getOrderNo());
            // 删除子订单个人健康告知信息表
            fcPerImpartResultMapper.deleteByOrderNo(fcOrderList.get(i).getOrderNo());
            //删除子订单表
            fcOrderItemMapper.deleteByOrderNo(fcOrderList.get(i).getOrderNo());
            //删除被保人表
            fcOrderInsuredMapper.deleteByOrderNo(fcOrderList.get(i).getOrderNo());
            //删除个人被保人表
            fcPerAppntMapper.deleteByPrimaryKey(fcOrderList.get(i).getPerAppNo());
            //删除订单表
            fcOrderMapper.deleteByPrimaryKey(fcOrderList.get(i).getOrderNo());
        }
        return "";
    }

    /**
     * 投保规则校验
     *
     * @param token
     * @param personId
     * @param planCode
     * @return
     */
    public String checkTBRules(String token, String personId, String planCode) {
        Map<String, Object> resultMap = new HashMap<>();
        //校验请求参数
        if (StringUtils.isBlank(personId)) {
            resultMap.put("code", "500");
            resultMap.put("message", "请您先选择人员信息！");
            return JSON.toJSONString(resultMap);
        }
        if (StringUtils.isBlank(planCode)) {
            resultMap.put("code", "500");
            resultMap.put("message", "请您先选择计划后，再进行投保确认！");
            return JSON.toJSONString(resultMap);
        }
        try {
            GlobalInput globalInput = userService.getSession(token);
            //校验投保规则
            if (tbCheckRules.checkTBRules(personId, planCode, globalInput.getEnsureCode())) {
                resultMap.put("code", "200");
                resultMap.put("message", "投保规则校验通过");
            } else if (tbCheckRules.getErrorMsg() != null && !"".equals(tbCheckRules.getErrorMsg())) {
                resultMap.put("code", "500");
                resultMap.put("message", tbCheckRules.getErrorMsg());
            } else {
                resultMap.put("code", "300");
                resultMap.put("message", tbCheckRules.getResult());
            }
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("投保规则校验失败：", e);
            resultMap.put("code", "500");
            resultMap.put("message", "投保规则校验未通过");
            return JSON.toJSONString(resultMap);
        }
    }

    public Map<String, Object> checkEflexTBRules(String token, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            if (eflexTBCheckRules.eflexCheckTBRules(personId, globalInput.getEnsureCode(), globalInput.getCustomNo(), "02")) {
                resultMap.put("code", "200");
                resultMap.put("message", "投保规则校验通过");
            } else if (eflexTBCheckRules.getErrorMsg() != null && !"".equals(eflexTBCheckRules.getErrorMsg())) {
                resultMap.put("code", "500");
                resultMap.put("message", eflexTBCheckRules.getErrorMsg());
            } else {
                resultMap.put("code", "300");
                resultMap.put("message", eflexTBCheckRules.getResult());
            }
            return resultMap;
        } catch (Exception e) {
            log.info("投保规则校验失败：", e);
            resultMap.put("code", "500");
            resultMap.put("message", "投保规则校验未通过");
            return resultMap;
        }
    }

    public Map<String, Object> checkTBRulesPlan(String ensureCode, String personId, String planCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (tbCheckRules.checkTBRules(personId, planCode, ensureCode)) {
                resultMap.put("code", "200");
                resultMap.put("message", "投保规则校验通过");
            } else if (tbCheckRules.getErrorMsg() != null && !"".equals(tbCheckRules.getErrorMsg())) {
                resultMap.put("code", "500");
                resultMap.put("message", tbCheckRules.getErrorMsg());
            } else {
                resultMap.put("code", "300");
                resultMap.put("message", tbCheckRules.getResult());
            }
            return resultMap;
        } catch (Exception e) {
            log.info("投保规则校验失败：", e);
            resultMap.put("code", "500");
            resultMap.put("message", "投保规则校验未通过");
            return resultMap;
        }
    }

    public String eflexCheckTBRules(String token, Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, Object> eflexPersonInsureSave = checkRuleInsureSave(token, map);
            GlobalInput globalInput = userService.getSession(token);
            String personId = map.get("personId").toString();
            String perNo = globalInput.getCustomNo();
            String ensureCode = globalInput.getEnsureCode();
            if (eflexPersonInsureSave.get("code").toString().equals("200")) {
                if (eflexTBCheckRules.eflexCheckTBRules(personId, globalInput.getEnsureCode(), globalInput.getCustomNo(), "01")) {
                    resultMap.put("code", "200");
                    resultMap.put("message", "投保规则校验通过");
                } else if (eflexTBCheckRules.getErrorMsg() != null && !"".equals(eflexTBCheckRules.getErrorMsg())) {
                    resultMap.put("code", "500");
                    resultMap.put("message", eflexTBCheckRules.getErrorMsg());
                } else {
                    resultMap.put("code", "300");
                    resultMap.put("message", eflexTBCheckRules.getResult());
                }
                fPInsureEflexPlanOptionalMapper.deletefPfPEflexCheckRuleEflexPlanOptional(personId, perNo, ensureCode);
                fPInsureEflexPlanMapper.deletefPEflexCheckRuleEflexPlan(personId, perNo, ensureCode);
                return JSON.toJSONString(resultMap);
            } else {
                resultMap.put("code", "500");
                resultMap.put("message", "投保规则校验未通过");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            log.info("投保规则校验失败：", e);
            resultMap.put("code", "500");
            resultMap.put("message", "投保规则校验未通过");
            return JSON.toJSONString(resultMap);
        }
    }


    /**
     * 计算 福利总保费
     *
     * @param ensureCode
     * @return
     */
    public double getEnsurePrem(String ensureCode) {
        double ensurePrem = 0.00;
        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("ensureCode", ensureCode);
            if ("0".equals(fcEnsure.getPlanType())) {
                List<FCEnsurePlan> planList = fcEnsurePlanMapper.selectFCEnsurePlans(params);
                for (FCEnsurePlan fcEnsurePlan : planList) {
                    double planTotalPrem = CommonUtil.mul(fcEnsurePlan.getTotalPrem(), Double.valueOf(getPlanPeopleNum(ensureCode, fcEnsurePlan.getPlanCode())));
                    ensurePrem = CommonUtil.add(ensurePrem, planTotalPrem);
                }
            } else {
                List<String> premList = fcOrderItemMapper.getPremByEnsureCode(ensureCode);
                for (int i = 0; i < premList.size(); i++)
                    ensurePrem = CommonUtil.add(ensurePrem, StringUtils.isBlank(premList.get(i)) ? 0.00 : Double.parseDouble(premList.get(i).replaceAll(",", "")));
            }
        } catch (Exception e) {
            log.info("福利" + ensureCode + "计算总保费异常：", e);
            return ensurePrem;
        }
        return ensurePrem;
    }

    /**
     * <AUTHOR>
     * @description企业总缴费
     * @date 14:32 14:32
     * @modified
     */
    public double getGrpPrem(String ensureCode, String grpNo) {
        double grpPrem = 0.0;
        try {
            List<Map<String, Object>> mapList = fcGrpOrderMapper.selectGrpPrem(ensureCode, grpNo);
            for (Map<String, Object> map : mapList) {
                grpPrem = CommonUtil.add(grpPrem, Double.valueOf(map.get("GrpPrem").toString()));
            }
        } catch (Exception e) {
            log.info("企业" + ensureCode + "计算总保费异常：", e);
            return grpPrem;
        }
        return grpPrem;
    }

    /**
     * 计算 福利总人数
     *
     * @param ensureCode
     * @return
     */
    public int getEnsureNum(String ensureCode) {
        int ensureNum = 0;
        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("ensureCode", ensureCode);
            if ("0".equals(fcEnsure.getPlanType())) {
                List<FCEnsurePlan> planList = fcEnsurePlanMapper.selectFCEnsurePlans(params);
                for (FCEnsurePlan fcEnsurePlan : planList) {
                    ensureNum += getPlanPeopleNum(ensureCode, fcEnsurePlan.getPlanCode());
                }
            } else {
                ensureNum = fcOrderItemMapper.getEnsureNum(ensureCode);
            }

        } catch (Exception e) {
            log.info("福利" + ensureCode + "计算总人数异常：", e);
            return ensureNum;
        }
        return ensureNum;
    }

    /**
     * 查询单个计划总人数
     * add by hhw 20181031
     *
     * @param planCode
     * @return
     */
    public int getPlanPeopleNum(String ensureCode, String planCode) {
        int num = 0;
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("productCode", planCode);
            map.put("ensureCode", ensureCode);
            List<FCOrderItemDetail> fcOrderItemDetailList = fcOrderItemDetailMapper.selectList(map);
            if (fcOrderItemDetailList != null) {
                num = fcOrderItemDetailList.size();
            }
        } catch (Exception e) {
            log.info("计划" + planCode + "计算总人数异常：", e);
            return num;
        }
        return num;
    }

    /**
     * <AUTHOR>
     * @description判断该福利是否有家属计划
     * @date 19:10 19:10
     * @modified
     */
    public String isExistFamilyPlan(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectEnsureCodeByplanObject(ensureCode, "2");
            if (fcEnsurePlanList.size() == 0) {
                resultMap.put("isTrue", "0");
                resultMap.put("message", "该用户没有家属计划");
                return JSON.toJSONString(resultMap);
            }
            resultMap.put("isTrue", "1");
            resultMap.put("message", "该用户有家属计划");
        } catch (Exception e) {

            resultMap.put("code", "500");
            resultMap.put("message", "判断失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description申请发票
     * @date 10:08 10:08
     * @modified
     */
    public String referMailInfo(String token, FCMailInfo fcMailInfo, String grpOrderNo) {
        Map<String, Object> resultMap = new HashMap<>();
        if (StringUtils.isEmpty(fcMailInfo.getEnsureCode()) || StringUtils.isEmpty(grpOrderNo)) {
            return JSON.toJSONString(ResultUtil.error("请求参数缺失！"));
        }
        //判断该福利是否过期
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(fcMailInfo.getEnsureCode());
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date policyEndDates = sf.parse(fcEnsure.getPolicyEndDate());
            if (!(new Date().before(policyEndDates))) {
                resultMap.put("code", "400");
                resultMap.put("message", "保险合同日期已过，无法进行纸质发票申请");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {

            log.info("错误信息：" + e);
        }
        try {
            if (!CheckUtils.checkMobilePhone(fcMailInfo.getTelPhone())) {
                resultMap.put("code", "400");
                resultMap.put("message", "联系人手机格式错误，请重新录入。");
                return JSON.toJSONString(resultMap);
            }
            GlobalInput globalInput = userService.getSession(token);
            String ensurecode = fcMailInfo.getEnsureCode();
            if (ensurecode == null || "".equals(ensurecode)) {
                ensurecode = globalInput.getEnsureCode();
            }
            //根据福利编号查询是否申请过发票
            int i = fcMailInfoMapper.selectByEnsureCode(ensurecode);
            if (i > 0) {
                return JSON.toJSONString(ResultUtil.error("发票已申请，请勿重复提交！"));
            }
            resultMap.put("ensureCode", ensurecode);
            resultMap.put("configNo", "011");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(resultMap);
            if (ObjectUtils.isEmpty(fcEnsureConfig)) {
                return JSON.toJSONString(ResultUtil.error("未查询到财务的联系方式。发票申请失败！"));
            }
            resultMap.clear();
            //将发票信息发送核心，核心发送密码或邮件（待定）
            //获取发票流水号
            String customType = fcMailInfo.getCustomType();
            String invoiceType = fcMailInfo.getInvoiceType();
            if (customType == ConstantUtil.CustomType_2 || ConstantUtil.CustomType_2.equals(customType)) {
                if (invoiceType == ConstantUtil.CustomType_1 || invoiceType.equals(ConstantUtil.CustomType_1)) {
                    //处理日期为当前日期加3天
                    /*String dealDate = DateTimeUtil.getdateYMD(3);
                    //发送短信
                    SendSMSReq sendSMSReq = new SendSMSReq();
                    sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_001.getCode());
                    sendSMSReq.setPhones(fcEnsureConfig.getConfigValue());
                    Map<String, Object> map = new HashMap<>();
                    map.put("grp_cont_no", FormatUtils.formatPolicyNo(grpOrderNo));
                    map.put("get_address", fcMailInfo.getProvince() + "-" + fcMailInfo.getCity() + "-" + fcMailInfo.getArea() + "-" + fcMailInfo.getAddress());
                    map.put("zipcode", fcMailInfo.getZipcode());
                    map.put("receiver", fcMailInfo.getReceiver());
                    map.put("tel_phone", FormatUtils.formatPhone(fcMailInfo.getTelPhone()));
                    map.put("deal_date", dealDate);
                    sendSMSReq.setParam(map);
                    SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
                    if (!sendMessageResp.getSuccess().equals("true")) {
                        resultMap.put("phoneMessage", sendMessageResp.getMsg());
                    }*/
                    resultMap.put("phoneMessage", "");
                }
            }
            String invoiceInfoSN = maxNoService.createMaxNo("fcMailInfo", null, 20);
            String location = fcMailInfo.getProvince() + "-" + fcMailInfo.getCity() + "-" + fcMailInfo.getArea() + "-" + fcMailInfo.getAddress();
            fcMailInfo.setLocation(location);
            fcMailInfo.setEnsureCode(ensurecode);
            fcMailInfo.setInvoiceInfoSN(invoiceInfoSN);
            fcMailInfo.setCustomType(globalInput.getCustomType());
            fcMailInfo.setApplicantName(globalInput.getName());
            fcMailInfo.setGrpNo(globalInput.getGrpNo());
            fcMailInfo.setOperator(globalInput.getUserNo());
            fcMailInfo = (FCMailInfo) CommonUtil.initObject(fcMailInfo, "INSERT");
            fcMailInfoMapper.insert(fcMailInfo);
            resultMap.put("code", "200");
            resultMap.put("message", "提交成功");
        } catch (Exception e) {

            log.info("错误信息：" + e);
            resultMap.put("code", "500");
            resultMap.put("message", "申请发票失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description查看申请记录
     * @date 10:12 10:12
     * @modified
     */
    public String getMailInfo(String personId, String token) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (personId == null || "".equals(personId)) {
                resultMap.put("code", "300");
                resultMap.put("message", "个人编号为空");
                return JSON.toJSONString(resultMap);
            }
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            Map<String, Object> map = new HashMap<>();
            map.put("personId", personId);
            map.put("ensureCode", ensureCode);
            map.put("customType", ConstantUtil.EnsureState_1);
            map.put("grpNo", globalInput.getGrpNo());
            //员工姓名
            String name = globalInput.getName();
            //查询发票信息
            List<FCMailInfo> fcMailInfoList = fcMailInfoMapper.getMailInfoByPersonId(map);
            //封装参数
            List<Map<String, Object>> mapList = new ArrayList<>();
            for (FCMailInfo fcMailInfo : fcMailInfoList) {
                Map<String, Object> emailInfo = emailInfo(name, fcMailInfo);
                mapList.add(emailInfo);
            }
            PageHelperUtil<Map<String, Object>> teamPageInfo = new PageHelperUtil<>(mapList);
            resultMap.put("list", teamPageInfo.getList());
            resultMap.put("code", "200");
            resultMap.put("message", "查询成功");
        } catch (Exception e) {

            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }

        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description封装发票信息
     * @date 11:13 11:13
     * @modified
     */
    public Map<String, Object> emailInfo(String name, FCMailInfo fcMailInfo) {
        Map<String, Object> map = new HashMap<>();
        map.put("payName", fcMailInfo.getPayName());
        map.put("invoiceType", fcMailInfo.getInvoiceType());
        map.put("name", name);
        map.put("invoiceAmount", fcMailInfo.getInvoiceAmount());
        map.put("email", fcMailInfo.getEmail());
        String dateTime = "<p>" + fcMailInfo.getMakeDate() + "</p>" + " " + "<p>" + fcMailInfo.getMakeTime() + "</p>";
        map.put("dateTime", dateTime);
        return map;
    }

    /**
     * <AUTHOR>
     * @description计划保障说明
     * @date 17:14 17:14
     * @modified
     */
    public String getConfigExplain(String token, String planCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            String grpNo = globalInput.getGrpNo();
            Map<String, Object> ensureMap = new HashMap<>();
            ensureMap.put("ensureCode", ensureCode);
            ensureMap.put("grpNo", grpNo);
            ensureMap.put("planCode", planCode);
            List<FcPlanConfig> listMap = fcPlanConfigMapper.getConfigExplain(ensureMap);
            Map<String, String> map = new HashMap<>();
            if (listMap == null || listMap.size() == 0) {
                resultMap.put("code", "500");
                resultMap.put("meaasge", "该计划没有配置保障说明。");
            } else {
                for (FcPlanConfig fcPlanConfig : listMap) {
                    String configNo = fcPlanConfig.getConfigNo();
                    String configValue = fcPlanConfig.getConfigValue();
                    switch (configNo) {
                        case "001":
                            map.put("waitTime", "等待期:" + configValue + "天");
                            break;
                        case "002":
                            map.put("responsibility", "责任说明:" + configValue);
                            break;
                        case "003":
                            map.put("anamnesis", "既往症:" + configValue);
                            break;
                    }
                }
                resultMap.put("data", map);
                resultMap.put("code", "200");
                resultMap.put("meaasge", "查询计划配置成功");
            }
        } catch (Exception e) {

            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 获取固定计划保障配置
     *
     * @param grpNo
     * @param ensureCode
     * @param planCode
     * @return
     */
    public PlanConfigExplain getConfigExplain1(String grpNo, String ensureCode, String planCode) {
        // 定义获取计划保障配置对象
        PlanConfigExplain planConfigExplain = new PlanConfigExplain();
        Map<String, Object> ensureMap = new HashMap<>();
        ensureMap.put("grpNo", grpNo);
        ensureMap.put("ensureCode", ensureCode);
        ensureMap.put("planCode", planCode);
        List<FcPlanConfig> listMap = fcPlanConfigMapper.getConfigExplain(ensureMap);
        if (listMap.size() > 0) {
            for (FcPlanConfig fcPlanConfig : listMap) {
                String configNo = fcPlanConfig.getConfigNo();
                String configValue = fcPlanConfig.getConfigValue();
                switch (configNo) {
                    case "001":
                        // 等待期
                        planConfigExplain.setWaitTime(configValue + "天");
                        break;
                    case "002":
                        // 责任说明
                        planConfigExplain.setResponsibility(configValue);
                        break;
                    case "003":
                        // 既往症
                        planConfigExplain.setAnamnesis(configValue);
                        break;
                }
            }
        }
        return planConfigExplain;
    }

    /**
     * 获取弹性福利配置
     *
     * @param token
     * @param amountGradeCodeList
     * @return
     */
    public EflexConfigExplain getEflexConfigExplain(String token, List<String> amountGradeCodeList) {
        // 校验数据
        if (ObjectUtils.isEmpty(amountGradeCodeList) || amountGradeCodeList.size() == 0) {
            throw new SystemException("保额档次编码不能为空！");
        }
        // 定义返回对象
        EflexConfigExplain eflexConfigExplain = new EflexConfigExplain();
        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);
        String ensureCode = globalInput.getEnsureCode();
        String grpNo = globalInput.getGrpNo();

        // 获取弹性计划的福利配置信息
        FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
        fcEnsureConfig.setEnsureCode(ensureCode);
        fcEnsureConfig.setGrpNo(grpNo);
        List<FCEnsureConfig> fcEnsureConfigList = fcEnsureConfigMapper.getInsureNote(fcEnsureConfig);
        if (fcEnsureConfigList.size() > 0) {
            for (FCEnsureConfig insureNote : fcEnsureConfigList) {
                String configNo = insureNote.getConfigNo();
                String configValue = insureNote.getConfigValue();
                if (configValue == null || "".equals(configValue)) {
                    continue;
                } else {
                    switch (configNo) {
                        case "003":
                            // 医疗机构
                            eflexConfigExplain.setMedicalInstitution(configValue);
                            break;
                        case "005":
                            // 既往症
                            eflexConfigExplain.setAnamnesis(configValue);
                            break;
                        case "006":
                            // 其他约定
                            eflexConfigExplain.setOtherConventions(configValue);
                            break;
                        case "018":
                            // 特别约定
                            eflexConfigExplain.setSpecialAgreement(configValue);
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        // 查询等待期
        if (amountGradeCodeList != null && amountGradeCodeList.size() > 0) {
            List<WaitingPeriodInfo> waitingPeriodListInfo = fcDutyAmountGradeMapper.getWaitingPeriodInfoList(amountGradeCodeList);
            eflexConfigExplain.setWaitingPeriodInfoList(waitingPeriodListInfo);
        }

        return eflexConfigExplain;
    }

    /**
     * todo 这块的逻辑太冗余，需要弃用、重写
     *
     * @param fcEnsurePlanList
     * @param personId
     * @param relation
     * @param perNo
     * @return
     * @throws ParseException
     */
    public List<FCEnsurePlan> checkPlanAge(List<FCEnsurePlan> fcEnsurePlanList, String personId, String relation, String perNo) throws ParseException {
        log.info("checkPlanAge校验入参fcEnsurePlanList:{}personId:{}relation{}perNo{}", JSONObject.toJSONString(fcEnsurePlanList), personId, relation, perNo);
        List<FCEnsurePlan> insurePlanList = new ArrayList<>();
        FCEnsure fcEnsure = fcEnsureMapper.getFCEnsureByPlanCode(fcEnsurePlanList.get(0).getEnsureCode(), fcEnsurePlanList.get(0).getPlanCode());
        for (FCEnsurePlan insurePlan : fcEnsurePlanList) {
            //查询符合被保险人年龄段的已投保计划
            int minAge = 0;
            int maxAge = 0;
            String relationConfig = "";
            String minAgeUnit = "";
            String maxAgeUnit = "";
            int minOccupationType = 0;
            int maxOccupationType = 0;
            String planSex = "";
            String joinMedProtect = "";
            int gradeLowLevelCode = 0;
            int gradeTopLevelCode = 0;
            List<FcPlanConfig> fcPlanConfigList = fcPlanConfigMapper.getFCPlanConfigMaxMinAge(insurePlan.getEnsureCode(), insurePlan.getPlanCode());
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);

            long ages = 0;
            ages = Integer.valueOf(DateTimeUtil.getCurrentAge(fcPerson.getBirthDate(), fcEnsure.getCvaliDate()));
            if (ages > 0) {
                ages = ages * 365;
            } else {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date fDate = sdf.parse(fcPerson.getBirthDate());
                Date oDate = sdf.parse(fcEnsure.getCvaliDate());
                ages = ((oDate.getTime() - fDate.getTime()) / (1000 * 3600 * 24));
            }
            log.info("计划校验年龄:{},fcPlanConfigList{}", ages, JSONObject.toJSONString(fcPlanConfigList));
            for (FcPlanConfig fcPlanConfig : fcPlanConfigList) {
                switch (fcPlanConfig.getConfigNo()) {
                    case "011":
                        minAge = Integer.parseInt(fcPlanConfig.getConfigValue());
                        break;
                    case "012":
                        minAgeUnit = fcPlanConfig.getConfigValue();
                        break;
                    case "013":
                        maxAge = Integer.parseInt(fcPlanConfig.getConfigValue());
                        break;
                    case "014":
                        maxAgeUnit = fcPlanConfig.getConfigValue();
                        break;
                    case "015":
                        //与员工关系
                        relationConfig = fcPlanConfig.getConfigValue();
                        break;
                    case "016":
                        //职业类别下限
                        minOccupationType = Integer.parseInt(fcPlanConfig.getConfigValue());
                        break;
                    case "017":
                        //职业类别下限
                        maxOccupationType = Integer.parseInt(fcPlanConfig.getConfigValue());
                        break;
                    case "018":
                        //性别
                        planSex = fcPlanConfig.getConfigValue();
                        break;
                    case "019":
                        //有无医保
                        joinMedProtect = fcPlanConfig.getConfigValue();
                        break;
                    case "020":
                        // 职级下限
                        List<String> listOne = fcBusPersonTypeMapper.selectOrderNum(fcEnsure.getGrpNo(), fcPlanConfig.getConfigValue());
                        String orderNumLow = listOne.get(0);
                        gradeLowLevelCode = Integer.parseInt(orderNumLow);
                        break;
                    case "021":
                        // 职级上限
                        List<String> listTwo = fcBusPersonTypeMapper.selectOrderNum(fcEnsure.getGrpNo(), fcPlanConfig.getConfigValue());
                        String orderNumTop = listTwo.get(0);
                        gradeTopLevelCode = Integer.parseInt(orderNumTop);
                        break;
                    default:
                        break;
                }
            }
            log.info("计划校验:minAge{},minAgeUnit{},maxAge{},maxAgeUnit{},relationConfig{},minOccupationType{},maxOccupationType{},planSex{},joinMedProtect{},gradeLowLevelCode{},gradeTopLevelCode{}", minAge, minAgeUnit, maxAge, maxAgeUnit, relationConfig, minOccupationType, maxOccupationType, planSex, joinMedProtect, gradeLowLevelCode, gradeTopLevelCode);
            //职业 判断职业区间范围(若是学生的话也不判断职级)
            if (!"3".equals(insurePlan.getPlanObject())) {
                if (minOccupationType < maxOccupationType) {
                    if (!(minOccupationType <= Integer.parseInt(fcPerson.getOccupationType()) && Integer.parseInt(fcPerson.getOccupationType()) <= maxOccupationType)) {
                        continue;
                    }
                } else if (maxOccupationType < minOccupationType) {
                    if (!(maxOccupationType <= Integer.parseInt(fcPerson.getOccupationType()) && Integer.parseInt(fcPerson.getOccupationType()) <= minOccupationType)) {
                        continue;
                    }
                } else if (maxOccupationType == minOccupationType) {
                    if (maxOccupationType != Integer.parseInt(fcPerson.getOccupationType())) {
                        continue;
                    }
                }
            }

            //职级 判断职级区间范围
            //如果使用人是员工的话，进行职级区间的判断，若使用人为家属，则不用判断职级
            if ("1".equals(insurePlan.getPlanObject())) {
                String levelCode = fcPerRegistDayMapper.selectLevelCodeByPerNo(perNo, fcEnsure.getEnsureCode());
                log.info("判断职级区间范围,{}", levelCode);
                List<String> listOne = fcBusPersonTypeMapper.selectOrderNum(fcEnsure.getGrpNo(), levelCode);
                String orderNum = listOne.get(0);
                log.info("员工进行职级区间的判断:levelCode:{},listOne{}", levelCode, JSONObject.toJSONString(listOne));
                if (gradeLowLevelCode < gradeTopLevelCode) {
                    if (!(gradeLowLevelCode <= Integer.parseInt(orderNum) && Integer.parseInt(orderNum) <= gradeTopLevelCode)) {
                        continue;
                    }
                } else if (gradeTopLevelCode < gradeLowLevelCode) {
                    if (!(gradeTopLevelCode <= Integer.parseInt(orderNum) && Integer.parseInt(orderNum) <= gradeLowLevelCode)) {
                        continue;
                    }
                } else if (gradeTopLevelCode == gradeLowLevelCode) {
                    if (gradeTopLevelCode != Integer.parseInt(orderNum)) {
                        continue;
                    }
                }
            }
            //若是学生投保得话不校验医保和性别
            if (!"3".equals(insurePlan.getPlanObject())) {
                //有无医保
                joinMedProtect = joinMedProtect.replace('有', '1');
                joinMedProtect = joinMedProtect.replace('无', '0');
                joinMedProtect = joinMedProtect.replace('Y', '1');
                joinMedProtect = joinMedProtect.replace('N', '0');
                if (!Arrays.asList(joinMedProtect.split("\\@")).contains(fcPerson.getJoinMedProtect())) {
                    continue;
                }
                //性别
                planSex = planSex.replace('男', '0');
                planSex = planSex.replace('女', '1');
                if (!Arrays.asList(planSex.split("\\@")).contains(fcPerson.getSex())) {
                    continue;
                }
            }
            //计划类型为家属时判断关系
            if ("2".equals(insurePlan.getPlanObject())) {
                relationConfig = relationConfig.replace("父母", "1");
                relationConfig = relationConfig.replace("配偶", "2");
                relationConfig = relationConfig.replace("子女", "3");
                if (!Arrays.asList(relationConfig.split("\\@")).contains(relation)) {
                    continue;
                }
            }
            if ("Y".equals(minAgeUnit)) {
                minAge = minAge * 365;
            }
            if ("Y".equals(maxAgeUnit)) {
                maxAge = maxAge * 365;
            }
            if (minAge <= ages && ages <= maxAge) {
                insurePlanList.add(insurePlan);
            }
        }
        return insurePlanList;
    }

    /**
     * 电子发票短连接查询接口
     *
     * @param token
     * @param ContNo 个人保单号
     * @return
     */
    public String invoiceQuery(String token, String grpContNo, String ContNo) {
        Map<String, Object> resultMap = new HashMap<>();
        FCEnsure fcEnsure = fcEnsureMapper.getFcensureByGrpContNo(grpContNo);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date policyEndDates = sf.parse(fcEnsure.getPolicyEndDate());
            if (!(new Date().before(policyEndDates))) {
                resultMap.put("code", "400");
                resultMap.put("message", "保险合同日期已过，无法进行电子发票申请");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {

            log.info("错误信息：" + e);
        }

        try {
            RemoteDelegate rd = RemoteDelegate.getInstance();
            log.info("电子发票短连接查询接口调用时间：" + DateTimeUtil.getCurrentDateTime());
            String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
                    "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                    "\t\t<TransType>YUG007</TransType>\n" +
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<ContNo>" + ContNo + "</ContNo>\n" +
                    "\t</BODY>\n" +
                    "</RequestInfo>\n";
            log.info("请求报文：" + reqXml);
            long startStamp = System.currentTimeMillis();
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "BenefitService", reqXml);
            long endStamp = System.currentTimeMillis();
            log.info("调用接口用时：" + (endStamp - startStamp));
            if (success) {
                Map<String, Object> responseXml = rd.getResult();
                Head head = (Head) responseXml.get("Head");
                if ("0".equals(head.getFlag())) {
                    Body body = (Body) responseXml.get("Body");
                    resultMap.put("InvoiceList", body.getInvoiceList());
                    resultMap.put("code", "200");
                    resultMap.put("meaasge", "电子发票查询成功");
                } else if ("1".equals(head.getFlag())) {
                    resultMap.put("InvoiceList", new ArrayList<FpList>());
                    resultMap.put("code", "200");
                    resultMap.put("meaasge", "暂无开票信息");
                }
            } else {
                resultMap.put("code", "500");
                resultMap.put("meaasge", "电子发票查询失败");
            }
        } catch (Exception e) {

            log.info("电子发票查询接口调用失败：" + e);
            resultMap.put("code", "500");
            resultMap.put("message", "电子发票查询接口调用失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 电子发票申请接口
     *
     * @param token
     * @param ContNo
     * @param PayNo
     * @return
     */
    public String invoiceApply(String token, String ContNo, String PayNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            RemoteDelegate rd = RemoteDelegate.getInstance();
            log.info("电子发票申请接口调用时间：" + DateTimeUtil.getCurrentDateTime());
            String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
                    "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                    "\t\t<TransType>YUG006</TransType>\n" +
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<ContNo>" + ContNo + "</ContNo>\n" +
                    "\t\t<FpList>\n" +
                    "\t\t\t<PayNo>" + PayNo + "</PayNo>\n" +
                    "\t\t</FpList>\n" +
                    "\t</BODY>\n" +
                    "</RequestInfo>";
            log.info("调用接口请求报文：" + reqXml);
            long startStamp = System.currentTimeMillis();
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "BenefitService", reqXml);
            long endStamp = System.currentTimeMillis();
            log.info("调用接口用时：" + (endStamp - startStamp));
            if (success) {
                Map<String, Object> responseXml = rd.getResult();
                Body body = (Body) responseXml.get("Body");
                Head head = (Head) responseXml.get("Head");
                if ("0".equals(head.getFlag())) {
                    if ("Y".equals(body.getInvoiceList().get(0).getDealFlag())) {
                        resultMap.put("Invoice", body.getInvoiceList().get(0));
                        resultMap.put("code", "200");
                        resultMap.put("meaasge", "电子发票申请成功");
                    } else {
                        log.info("电子发票申请接口调用失败：" + body.getFpList().getErrorReason());
                        resultMap.put("code", "500");
                        resultMap.put("meaasge", "电子发票申请失败");
                    }
                } else if ("1".equals(head.getFlag())) {
                    resultMap.put("Invoice", new FpList());
                    resultMap.put("code", "200");
                    resultMap.put("meaasge", "暂无开票信息");
                }
            }
        } catch (Exception e) {

            log.info("电子发票申请失败：" + e);
            resultMap.put("code", "500");
            resultMap.put("message", "电子发票申请失败");
        }
        return JSON.toJSONString(resultMap);
    }

    //对自然人客户职业信息判断控制--personid为null：确认投保 ,反之投保
    public String checkPeople(String token, String personid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "200");
        resultMap.put("message", "校验成功！");
        GlobalInput globalInput = userService.getSession(token);
        //查询福利信息
        FCEnsure fcEnsure = new FCEnsure();
        if (StringUtils.isNotBlank(globalInput.getEnsureCode())) {
            fcEnsure = fcEnsureMapper.selectByPrimaryKey(globalInput.getEnsureCode());
        }
        //除学生福利外，校验员工是否已经投保
        if (fcEnsure.getEnsureType().equals("0")) {
            String StaffPersonid = fcStaffFamilyRelaMapper.selectStaffPersonid(globalInput.getCustomNo());
            Integer count = fpInsurePlanMapper.selectStaffInsured(globalInput.getCustomNo());
            if (count == 0 && !personid.equals(StaffPersonid)) {
                resultMap.put("code", "500");
                resultMap.put("message", "请先为员工进行投保！");
                return JSON.toJSONString(resultMap);
            }
        } else if (fcEnsure.getEnsureType().equals("1") && StringUtils.isBlank(personid)) {
            //校验学生福利，是否已经投保
            Map<String, Object> selectmap = new HashMap<>();
            selectmap.put("ensureCode", globalInput.getEnsureCode());
            selectmap.put("perNo", globalInput.getCustomNo());
            List<FPInsurePlan> fpInsurePlans = fpInsurePlanMapper.selectEnsureCodeByPersonId(selectmap);
            if (fpInsurePlans.size() == 0) {
                resultMap.put("code", "500");
                resultMap.put("message", "至少为一个学生投保哦！");
                return JSON.toJSONString(resultMap);
            }
        }


        //校验单个人
        if (StringUtils.isNotBlank(personid)) {
            FCPerson fcperson = fcPersonMapper.selectByPrimaryKey1(personid);
            if (fcperson != null) {
                Map<String, Object> map = checkRules(fcperson, fcEnsure.getCvaliDate());
                if (map.get("code").equals("500")) {
                    resultMap.put("code", map.get("code"));
                    resultMap.put("message", map.get("message"));
                }
            } else {
                resultMap.put("code", "500");
                resultMap.put("message", "至少为一个家庭成员投保哦！");
            }
        } else {
            //判断家属是否投保
            Map<String, Object> smap = new HashMap<>();
            smap.put("perNo", globalInput.getCustomNo());
            smap.put("ensureCode", globalInput.getEnsureCode());
            List<FCPerson> fcpersonlist = fcPersonMapper.selectInsuredPerson(smap);
            if (fcpersonlist.size() > 0) {
                for (FCPerson fcPerson : fcpersonlist) {
                    Map<String, Object> map = checkRules(fcPerson, fcEnsure.getCvaliDate());
                    if (map.get("code").equals("500")) {
                        resultMap.put("code", map.get("code"));
                        resultMap.put("message", map.get("message"));
                        break;
                    }
                }
            } else {
                resultMap.put("code", "500");
                if (fcEnsure.getEnsureType().equals("1")) {
                    resultMap.put("message", "至少为一个学生投保哦！");
                } else {
                    resultMap.put("message", "至少为一个家庭成员投保哦！");
                }
            }

        }
        return JSON.toJSONString(resultMap);
    }

    //校验规则
    public Map<String, Object> checkRules(FCPerson fcperson, String ensurevaliDate) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "200");
        resultMap.put("message", "校验成功！");
        //校验人员信息
        Map<String, String> map = new HashMap<>();
        if (fcperson.getRelation().equals("0")) {
            map.put("sign", "1");
        } else {
            map.put("sign", "2");
        }
        map.put("idType", fcperson.getIDType());//证件类型
        map.put("idNo", fcperson.getIDNo());//证件号
        map.put("birthDay", fcperson.getBirthDate());//出生日期
        map.put("sex", fcperson.getSex());//性别
        map.put("nativeplace", fcperson.getNativeplace());//国籍
        map.put("idTypeEndDate", fcperson.getIdTypeEndDate());//证件有效期
        map.put("occupationCode", fcperson.getOccupationCode());//职业代码
        map.put("ensurevaliDate", ensurevaliDate);//福利生效日期
        String resultMsg = CheckUtils.checkSinglePeople(map);
        if (StringUtils.isNotBlank(resultMsg)) {
            resultMap.put("message", fcperson.getName() + "错误信息：" + resultMsg);
            resultMap.put("code", "500");
            return resultMap;
        }
        return resultMap;
    }


    //同步信息的操作
    @Transactional
    public Map<String, Object> SynchronizationFamilyinfo(String token, String personid) {
        //公共变量
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", true);
        GlobalInput globalInput = userService.getSession(token);
        Map<String, String> getPerNoParm = new HashMap<>();
        Map<String, String> getStaffCountParm = new HashMap<>();
        String errmsg = "";
        try {
            //获取当前福利
            String ensureCode = globalInput.getEnsureCode();
            if (StringUtils.isBlank(ensureCode)) {
                errmsg += "福利编号为空！";
                throw new RuntimeException();
            }
            //获取员工所属企业
            String grpno = globalInput.getGrpNo();
            if (StringUtils.isBlank(grpno)) {
                errmsg += "企业号为空！";
                throw new RuntimeException();
            }
            //获取当前的员工
            String perno = globalInput.customNo;
            if (!globalInput.getGrpNo().equals(grpno)) {
                getPerNoParm.put("grpNo", grpno);
                getPerNoParm.put("perNo", perno);
                perno = fcPerInfoMapper.selectSinglePerNo(getPerNoParm);
                if (StringUtils.isBlank(perno)) {
                    errmsg += "员工号为空！";
                    throw new RuntimeException();
                }
            }
            //获取操作的家属
            String personId = personid;

            //判断家属是否在员工下,同时判断存在相同家属。
            getStaffCountParm.put("perNo", perno);
            getStaffCountParm.put("personId", personid);
            List<String> list = fcStaffFamilyRelaMapper.selectStaffCount(getStaffCountParm);
            if (list.size() > 1) {
                errmsg += "员工存在多条家属！";
                throw new RuntimeException();
            } else if (list.size() == 1) {
                personId = list.get(0);
            } else {
                //同步FCperson
                FCPerson fcperson = initFcperson(personid);
                personId = fcperson.getPersonID();
                fcPersonMapper.insert(fcperson);
                //同步FCpersonStaffFamilyRela
                FCStaffFamilyRela fcStaffFamilyRela = initFCStaffFamilyRela(personid, perno, personId);
                fcStaffFamilyRelaMapper.insert(fcStaffFamilyRela);
            }
            if (StringUtils.isNotBlank(errmsg)) {
                resultMap.put("success", false);
                resultMap.put("errmsg", errmsg);
            }
            //刷新参数
            resultMap.put("personId", personId);
            resultMap.put("grpNo", grpno);
            resultMap.put("perNo", perno);
            resultMap.put("ensureCode", ensureCode);
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("errmsg", StringUtils.isBlank(errmsg) ? "同步人员信息失败！" : errmsg);
        }

        return resultMap;
    }

    private FCPerson initFcperson(String personid) {
        FCPerson fcperson = fcPersonMapper.selectByPrimaryKey(personid);
        fcperson.setPersonID(maxNoService.createMaxNo("PersonID", null, 20));
        fcperson = CommonUtil.initObject(fcperson, "INSERT");
        return fcperson;
    }

    private FCStaffFamilyRela initFCStaffFamilyRela(String oldpersonid, String perno, String newoldpersonid) {
        FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectPersonIdInfo(oldpersonid);
        fcStaffFamilyRela.setPerNo(perno);
        fcStaffFamilyRela.setPersonID(newoldpersonid);
        fcStaffFamilyRela.setRelation(fcStaffFamilyRela.getRelation());
        fcStaffFamilyRela = (FCStaffFamilyRela) CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
        return fcStaffFamilyRela;
    }


    /**
     * 查询所有的福利
     */
    public String selctAllEnsure(String token, String grpNo, int page, int rows) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "200");
        resultMap.put("message", "校验成功！");
        GlobalInput globalInput = userService.getSession(token);
        PageHelper.startPage(page, rows);
        try {
            //查询所有的福利
            Map params = new HashMap();
            params.put("perNo", globalInput.getCustomNo());
            params.put("grpNo", grpNo);
            List<FCEnsureGrpInfo> EnsureInfolist = fcEnsureMapper.selectAllEnsureandGrpNo(params);
            List<FCEnsureGrpInfo> EnsureInfolist1 = new ArrayList<>();
            for (FCEnsureGrpInfo fcEnsureGrpInfo : EnsureInfolist) {
                if (!fcEnsureGrpInfo.getPlanType().equals(PlanTypeEnum.DAILYPLAN.getCode())) {
                    // 保险期间
                    Long days = DateTimeUtil.getDistanceDays(fcEnsureGrpInfo.getPolicyEndDate(), fcEnsureGrpInfo.getCvaliDate());
                    if (days == 364 || days == 365) {
                        fcEnsureGrpInfo.setInsuredPeriod("1");
                        fcEnsureGrpInfo.setInsuredPeriodUnit("Y");
                        fcEnsureGrpInfo.setInsuredPeriodMsg("保1年");
                    } else {
                        long daysOne = DateTimeUtil.getDistanceDays(fcEnsureGrpInfo.getCvaliDate(), fcEnsureGrpInfo.getPolicyEndDate());
                        String daysTwo = String.valueOf(daysOne + 1);
                        fcEnsureGrpInfo.setInsuredPeriod(daysTwo);
                        fcEnsureGrpInfo.setInsuredPeriodUnit("D");
                        fcEnsureGrpInfo.setInsuredPeriodMsg("保" + daysTwo + "天");
                    }
                    // 计划投保对象
                    if (fcEnsureGrpInfo.getPlanType().equals(PlanTypeEnum.IMMOBILIZATIONPLAN.getCode())) {
                        List<String> planObjectList = fcEnsurePlanMapper.selectPlanObject(fcEnsureGrpInfo.getEnsureCode());
                        fcEnsureGrpInfo.setInsureObject(JSONObject.toJSONString(planObjectList));
                        Set planObjectSet = new HashSet(planObjectList);
                        String insureObjectMsg = "";
                        if (planObjectSet.contains("1") && planObjectSet.contains("2")) {
                            insureObjectMsg = "员工家属均可投保";
                        }
                        if (planObjectSet.contains("1") && planObjectSet.size() == 1) {
                            insureObjectMsg = "员工可投保";
                        }
                        if (planObjectSet.contains("2") && !planObjectSet.contains("1")) {
                            insureObjectMsg = "家属可投保";
                        }
                        fcEnsureGrpInfo.setInsureObjectMsg(insureObjectMsg);
                    }
                    if (fcEnsureGrpInfo.getPlanType().equals(PlanTypeEnum.EFLEXPLAN.getCode())) {
                        List<String> insuredTypeList = fcBusinessProDutyGrpObjectMapper.selectInsuredType(fcEnsureGrpInfo.getEnsureCode());
                        fcEnsureGrpInfo.setInsureObject(JSONObject.toJSONString(insuredTypeList));
                        Set insuredTypeSet = new HashSet(insuredTypeList);
                        String insureObjectMsg = "";
                        if (insuredTypeSet.contains("0") && insuredTypeSet.size() == 1) {
                            insureObjectMsg = "员工可投保";
                        }
                        if (!insuredTypeSet.contains("0") && insuredTypeSet.size() >= 1) {
                            insureObjectMsg = "家属可投保";
                        }
                        if (insuredTypeSet.contains("0") && insuredTypeSet.size() > 1) {
                            insureObjectMsg = "员工家属均可投保";
                        }
                        fcEnsureGrpInfo.setInsureObjectMsg(insureObjectMsg);
                    }
                } else {
                    fcEnsureGrpInfo.setInsuredPeriodMsg("保终身");
                    fcEnsureGrpInfo.setInsureObjectMsg("员工家属均可投保");
                }
                //去除下架产品的福利
                String checkRiskStopSale = ensureMakeService.checkRiskStopSale(fcEnsureGrpInfo.getEnsureCode());
                if (StringUtil.isEmpty(checkRiskStopSale)) {
                    EnsureInfolist1.add(fcEnsureGrpInfo);
                } else {
                    log.info("福利{}中含有下架的产品。", fcEnsureGrpInfo.getEnsureCode());
                }

            }


            PageHelperUtil<FCEnsureGrpInfo> teamPageInfo = new PageHelperUtil<>(EnsureInfolist1);
            resultMap.put("recordsTotal", teamPageInfo.getTotal());
            resultMap.put("ensureList", teamPageInfo.getList());
            if (teamPageInfo.getTotal() == 0) {
                resultMap.put("message", "您的员福计划还在酝酿中，请耐心等待……");
                resultMap.put("isEnsure", "0");
                resultMap.put("code", "500");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("message", e);
            resultMap.put("code", "500");
            return JSON.toJSONString(resultMap);
        }

        return JSON.toJSONString(resultMap);
    }

    /**
     * 选中福利
     *
     * @param token
     * @param grpNo
     * @param ensureCode
     * @param perNo
     * @return
     */
    public String selectionEnsure(String token, String grpNo, String ensureCode, String perNo) {
        //
        Map<String, Object> resultMap = new HashMap<>();
        String planType = "";
        try {
            if (StringUtils.isBlank(perNo) || perNo.equals("undefined")) {
                GlobalInput globalInput = userService.getSession(token);
                perNo = fcPerInfoMapper.selectperNobyPerNo(globalInput.getCustomNo(), grpNo);
            }
            //判断计划类型是否为弹性计划
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            //获取员工personId
            List<String> employPersonId = fcPersonMapper.getEmployPersonId(perNo);
            if (fcEnsure != null && fcEnsure.getPlanType() != null
                    && fcEnsure.getPlanType().equals(PlanTypeEnum.EFLEXPLAN.getCode())) {
                //获取员工信息
                FCPerInfo fcperinfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
                //从个人注册期表查询员工职级
                String levelCode = fcPerRegistDayMapper.selectLevelCodeByPerNo(perNo, ensureCode);
                //查询员工是否含有可以投保的险种档次
                Map<String, Object> paraMap = new HashMap<String, Object>();
                paraMap.put("EnsureCode", ensureCode);
                paraMap.put("grpNo", grpNo);
                paraMap.put("BirthDay", fcperinfo.getBirthDay());
                if (StringUtil.isEmpty(levelCode)) {
                    return JSON.toJSONString(ResultUtil.error("员工职级查询有误！"));
                }
                paraMap.put("LevelCode", levelCode);
                paraMap.put("OccupationType", fcperinfo.getOccupationType());
                paraMap.put("Sex", fcperinfo.getSex());
                paraMap.put("ServiceTerm", fcperinfo.getServiceTerm());
                paraMap.put("Retirement", fcperinfo.getRetirement());
                paraMap.put("Relation", "0");
                paraMap.put("JoinMedProtect", fcperinfo.getJoinMedProtect());
                paraMap.put("InsureCount", fcEnsure.getInsuredNumber());
                List<Map<String, Object>> resultList = fcEnsureMapper.selectEflexEmployList(paraMap);
                if (resultList == null || resultList.size() < 1) {
                    throw new SystemException("您的员福计划还在酝酿中，请耐心等待……！");
                }
                planType = "1";
            } else {
                planType = "0";
            }
            resultMap.put("planType", planType);
            //重置globalInput信息
            GlobalInput globalInput = new GlobalInput();
            String userinfo = redisUtil.get(token);
            globalInput = JSON.parseObject(userinfo, GlobalInput.class);
            globalInput.setCustomNo(perNo);
            globalInput.setGrpNo(grpNo);
            globalInput.setEnsureCode(ensureCode);
            Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
            redisUtil.put(token, JSON.toJSONString(globalInput), loginValidity);
            if (JSON.parseObject(redisUtil.get(token), GlobalInput.class).getGrpNo().equals(grpNo)) {
                //判断是否已经生成订单，返回订单状态
                if (fcEnsure != null && fcEnsure.getPlanType() != null) {
                    List<FCOrder> fcorderlist = fcOrderMapper.selectOrder(perNo, ensureCode);
                    if (fcorderlist != null && fcorderlist.size() > 0) {
                        FCOrder fcOrder = fcorderlist.get(0);
                        resultMap.put("orderNo", fcOrder.getOrderNo());
                        resultMap.put("orderStatus", fcOrder.getOrderStatus());
                    } else {
                        resultMap.put("orderNo", "");
                        resultMap.put("orderStatus", "");
                    }
                    resultMap.put("personId", employPersonId.get(0));
                }
                //获取支付方式
                Map<String, Object> ensureMap = new HashMap<>();
                ensureMap.put("ensureCode", ensureCode);
                ensureMap.put("configNo", "008");
                FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(ensureMap);
                if (fcEnsureConfig != null) {
                    resultMap.put("payType", fcEnsureConfig.getConfigValue());
                }
                return JSONObject.toJSONString(ResponseResultUtil.success(resultMap));
            } else {
                throw new SystemException("福利选择失败！");
            }
        } catch (Exception e) {
            log.info("福利选择失败", e);
            throw new SystemException("福利选择失败！");

        }
    }

    //健康告知选择部分是的操作
    public String choicePartYes(String token, String personName) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        resultMap.put("code", "200");
        resultMap.put("success", true);
        resultMap.put("message", "请联系人力资源部姜凯文协助处理！");
        try {
            if (StringUtils.isNotBlank(personName)) {
                String ensureCode = globalInput.getEnsureCode();
                FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(ensureCode);
                if (fcEnsureContact != null) {
                    resultMap.put("message", "尊敬的客户：您的健康告知不满足投保要求，系统不支持您进行线上投保！");
                }
                resultMap.put("data", fcEnsureContact);

            } else {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "被保人姓名不能为空！");
            }
        } catch (Exception e) {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "查询联系人失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    public String signApplyMainPerInfo(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        String perNo = globalInput.getCustomNo();
        FCPerInfo perInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
        // 查询订单是否已经生成
        List<FCOrder> fcOrders = fcOrderMapper.selectOrder(perNo, globalInput.getEnsureCode());
        if (fcOrders.size() == 1) {
            FcBatchPayBankInfo fcBatchPayBankInfo = fcBatchPayBankInfoMapper
                    .selectFcBatchPayBankInfoByOrderNo(fcOrders.get(0).getOrderNo());
            if (!ObjectUtils.isEmpty(fcBatchPayBankInfo)) {
                perInfo.setOpenBank(fcBatchPayBankInfo.getPayBankCode());
                perInfo.setOpenAccount(fcBatchPayBankInfo.getBankAccount());
            }
        }
        if (perInfo == null) {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "数据异常");
        } else {
            resultMap.put("data", perInfo);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "查询成功！");
        }
        return JSON.toJSONString(resultMap);
    }

    //签约申请
    public String signApply(String token, SignApply signApply) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        String errorMsg = checkSignApplyParamsIsNull(signApply);//判断参数是否为空
        if (!errorMsg.equals("")) {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", errorMsg);
        } else {
            //更新fcbatchpaybankinfo为未签约
            Map<String, Object> fpMap = new HashMap<>();
            fpMap.put("perNo", globalInput.getCustomNo());
            fpMap.put("ensureCode", globalInput.getEnsureCode());
            updateFcBatchPayBankInfo(fpMap, "N");
            //调用签约申请接口
            signApply.setSource(myProps.getSourse());//H08 员福平台
            signApply.setAccountType("01");//01 借记卡
            signApply.setCorpEntity("440498");//默认440498
            signApply.setBatchFlag("1");//默认1-单笔
            String applyParam = JSONObject.toJSONString(signApply);
            log.info("调用签约申请接口请求报文: {}", JSONObject.toJSONString(applyParam));
            String applyResponseDate = HttpUtil.postHttpRequestJson(myProps.getSignApplySererUrl(), applyParam);
            log.info("调用签约申请接口返回报文: {}", JSONObject.toJSONString(applyResponseDate));
            if (!applyResponseDate.equals("")) {
                JSONObject jsStr = JSONObject.parseObject(applyResponseDate);
                SignApplyResponse signApplyResponse = JSONObject.toJavaObject(jsStr, SignApplyResponse.class);
                if (signApplyResponse.getCode().equals("200") && signApplyResponse.isSuccess()) {
                    //调用签约申请接口成功，已发送验证码，保存信息
                    log.info("调用签约申请接口保存请求信息: {}", signApplyResponse);
                    saveFcBatchPayBankInfo(globalInput, signApply, signApplyResponse.getData().getSignNo());
                    resultMap.put("globalInput", globalInput);
                    resultMap.put("code", "200");
                    resultMap.put("success", true);
                    resultMap.put("message", "验证码发送成功！");
                } else {
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", signApplyResponse);
                }
            } else {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "验证码发送失败");
            }
        }
        return JSON.toJSONString(resultMap);
    }

    public void saveFcBatchPayBankInfo(GlobalInput globalInput, SignApply signApply, String signSN) {
        FcBatchPayBankInfo fcBatchPayBankInfo = new FcBatchPayBankInfo();
        fcBatchPayBankInfo.setEnsureCode(globalInput.getEnsureCode());
        fcBatchPayBankInfo.setPerNo(globalInput.getCustomNo());
        fcBatchPayBankInfo.setSignSN(signSN);
        fcBatchPayBankInfo.setOrderNo("");
        fcBatchPayBankInfo.setName(signApply.getAccountName());
        fcBatchPayBankInfo.setIdType(signApply.getCertType());
        fcBatchPayBankInfo.setIdNo(signApply.getCertNo());
        fcBatchPayBankInfo.setPayBankCode(signApply.getBankCode());
        fcBatchPayBankInfo.setReservePhone(signApply.getCellPhone());
        fcBatchPayBankInfo.setIsSidned("N");
        fcBatchPayBankInfo.setOperator(globalInput.getUserNo());
        fcBatchPayBankInfo = CommonUtil.initObject(fcBatchPayBankInfo, "INSERT");
        log.info("调用签约申请接口保存信息: {}", fcBatchPayBankInfo);
        fcEnsureMapper.saveFcBatchPayBankInfo(fcBatchPayBankInfo);
    }

    public void updateFcBatchPayBankInfo(Map<String, Object> fpMap, String isSidned) {
        FcBatchPayBankInfo fcBatchPayBankInfo = new FcBatchPayBankInfo();
        fcBatchPayBankInfo.setEnsureCode(fpMap.get("ensureCode").toString());
        fcBatchPayBankInfo.setPerNo(fpMap.get("perNo").toString());
        fcBatchPayBankInfo.setOrderNo(fpMap.get("orderNo").toString());
        fcBatchPayBankInfo.setIsSidned(isSidned);
        fcBatchPayBankInfo = CommonUtil.initObject(fcBatchPayBankInfo, "INSERT");
        fcEnsureMapper.saveFcBatchPayBankInfo(fcBatchPayBankInfo);
    }

    public void updateOrderNoFcBatchPayBankInfo(String bankAccNo, Map<String, Object> fpMap, String orderNo) {
        FcBatchPayBankInfo fcBatchPayBankInfo = new FcBatchPayBankInfo();
        fcBatchPayBankInfo.setEnsureCode(fpMap.get("ensureCode").toString());
        fcBatchPayBankInfo.setPerNo(fpMap.get("perNo").toString());
        fcBatchPayBankInfo.setBankAccount(fpMap.get("perNo").toString());
        fcBatchPayBankInfo.setOrderNo(orderNo);
        if (null != bankAccNo) {
            fcBatchPayBankInfo.setBankAccount(bankAccNo);
        }
        fcEnsureMapper.saveFcBatchPayBankInfo(CommonUtil.initObject(fcBatchPayBankInfo, "INSERT"));
    }

    public String checkSignApplyParamsIsNull(SignApply signApply) {
        String errorMsg = "";
        if (signApply == null) {
            errorMsg = "付款信息不能为空";
        } else {
            if (signApply.getAccountName() == null || "".equals(signApply.getAccountName())) {
                errorMsg += "".equals(errorMsg) ? "持卡人姓名不能为空" : ",持卡人姓名不能为空";
            }
            if (signApply.getBankCode() == null || "".equals(signApply.getBankCode())) {
                errorMsg += "".equals(errorMsg) ? "付款银行不能为空" : ",付款银行不能为空";
            }
            if (signApply.getCertType() == null || "".equals(signApply.getCertType())) {
                errorMsg += "".equals(errorMsg) ? "持卡人证件类型不能为空" : ",持卡人证件类型不能为空";
            }
            if (signApply.getCertNo() == null || "".equals(signApply.getCertNo())) {
                errorMsg += "".equals(errorMsg) ? "持卡人证件号码不能为空" : ",持卡人证件号码不能为空";
            }
            if (signApply.getAccountNo() == null || "".equals(signApply.getAccountNo())) {
                errorMsg += "".equals(errorMsg) ? "银行账号不能为空" : ",银行账号不能为空";
            }
            if (!CheckUtils.checkMobilePhone(signApply.getCellPhone())) {
                errorMsg += "".equals(errorMsg) ? "预留手机号格式错误" : ",预留手机号格式错误";
            }
        }
        return errorMsg;
    }

    public String eflexEmployList(String token, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String perNo = globalInput.getCustomNo();
            String ensureCode = globalInput.getEnsureCode();
            String grpNo = globalInput.getGrpNo();
            //获取福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            //获取员工信息
            FCPerInfo fcperinfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
            //从个人注册期表查询员工职级
            String levelCode = fcPerRegistDayMapper.selectLevelCodeByPerNo(perNo, ensureCode);
            //首先判断暂存表是否有数据
            Map<String, Object> params = new HashMap<>();
            params.put("perNo", perNo);
            params.put("ensureCode", ensureCode);
            params.put("personId", personId);
            List<Map<String, String>> checkInfoIsExist = fPInsureEflexPlanMapper.checkInfoIsExist(params);
            if (checkInfoIsExist != null && checkInfoIsExist.size() > 0) {
                Map<String, Object> paraMap = new HashMap<>();
                paraMap.put("EnsureCode", ensureCode);
                paraMap.put("grpNo", grpNo);
                paraMap.put("BirthDay", fcperinfo.getBirthDay());
                if (StringUtil.isEmpty(levelCode)) {
                    return JSON.toJSONString(ResultUtil.error("员工职级查询有误！"));
                }
                paraMap.put("LevelCode", levelCode);
                paraMap.put("OccupationType", fcperinfo.getOccupationType());
                paraMap.put("Sex", fcperinfo.getSex());
                paraMap.put("ServiceTerm", fcperinfo.getServiceTerm());
                paraMap.put("Retirement", fcperinfo.getRetirement());
                paraMap.put("Relation", "0");
                paraMap.put("JoinMedProtect", fcperinfo.getJoinMedProtect());
                paraMap.put("InsureCount", fcEnsure.getInsuredNumber());
                //获取员工符合条件的保额档次
                List<Map<String, Object>> resultList = fcEnsureMapper.selectEflexEmployList(paraMap);
                if (resultList != null && resultList.size() > 0) {
                    List<String> riskList = new ArrayList<>();
                    Map<String, Object> amountGrageCodeAndFlaggMap = new HashMap<>();
                    List<String> amountGrageCodeAndFalgList = new ArrayList<>();
                    for (Map<String, Object> map : resultList) {
                        String riskName = map.get("RiskName").toString();
                        if (!riskList.contains(riskName)) {
                            riskList.add(riskName);
                        }
                        String amountGrageCode = map.get("AmountGrageCode").toString();
                        String isDefaultFlag = map.get("IsDefaultFlag").toString();
                        if (amountGrageCodeAndFlaggMap.containsKey(amountGrageCode)) {
                            amountGrageCodeAndFalgList.add(amountGrageCode);
                        } else {
                            amountGrageCodeAndFlaggMap.put(amountGrageCode, isDefaultFlag);
                        }
                    }
                    if (amountGrageCodeAndFalgList != null && amountGrageCodeAndFalgList.size() > 0) {
                        for (int i = 0; i < resultList.size(); i++) {
                            Map<String, Object> map = resultList.get(i);
                            String amountGrageCode = map.get("AmountGrageCode").toString();
                            String isDefaultFlag = map.get("IsDefaultFlag").toString();
                            for (String amountGrageCodeIs : amountGrageCodeAndFalgList) {
                                if (amountGrageCodeIs.equals(amountGrageCode) && isDefaultFlag.equals("0")) {
                                    resultList.remove(i);
                                }
                            }
                        }
                    }
                    List<Map<String, Object>> tList = new ArrayList<Map<String, Object>>();
                    for (String riskName : riskList) {
                        Map<String, Object> riskMap = new HashMap<String, Object>();
                        List<Map<String, Object>> amountGrageCodeList = new ArrayList<Map<String, Object>>();
                        for (Map<String, Object> map : resultList) {
                            String riskCode = map.get("RiskCode").toString();
                            String riskNameC = map.get("RiskName").toString();
                            String riskType = map.get("RiskType").toString();
                            String amountGrageCode = map.get("AmountGrageCode").toString();
                            String annualTimeDeduction = map.get("AnnualTimeDeduction").toString();
                            String dutyName = map.get("DutyName").toString();
                            String Amnt = map.get("Amnt").toString();
                            String isDefaultFlag = map.get("IsDefaultFlag").toString();
                            if (riskName.equals(riskNameC)) {
                                riskMap.put("RiskCode", riskCode);
                                riskMap.put("RiskType", riskType);
                                riskMap.put("RiskName", riskName);
                                riskMap.put("BirthDay", fcperinfo.getBirthDay());
                                riskMap.put("Sex", fcperinfo.getSex());
                                riskMap.put("JoinMedProtect", fcperinfo.getJoinMedProtect());
                                riskMap.put("OccupationType", fcperinfo.getOccupationType());
                                riskMap.put("CvaliDate", map.get("CvaliDate").toString());
                                riskMap.put("InsureCount", map.get("InsuredNumber").toString());
                                riskMap.put("DutyName", dutyName);
                                Map<String, Object> amountGrageCodeMap = new HashMap<String, Object>();
                                amountGrageCodeMap.put("RiskCode", riskCode);
                                amountGrageCodeMap.put("RiskType", riskType);
                                amountGrageCodeMap.put("RiskName", riskName);
                                amountGrageCodeMap.put("AnnualTimeDeduction", annualTimeDeduction);
                                String deductibleM = "";
                                String compensationRatioM = "";
                                for (Map<String, String> mapM : checkInfoIsExist) {
                                    String riskNameM = mapM.get("riskName");
                                    String amnt = mapM.get("amnt");
                                    String prem = mapM.get("prem");
                                    String amountGrageCodeM = mapM.get("AmountGrageCode");
                                    String isChoice = mapM.get("isChoice");//1-必选保额档次;2-可选保额档次
                                    if (riskNameM.equals(riskName) && amountGrageCodeM.equals(amountGrageCode) && isChoice.equals("1")) {
                                        amountGrageCodeMap.put("isChecked", "1");//选中
                                        amountGrageCodeMap.put("Prem", prem);
                                        deductibleM = mapM.get("Deductible");//免赔额
                                        compensationRatioM = mapM.get("CompensationRatio");//赔付比例
                                        riskMap.put("DefaultAmnt", amnt);
                                        riskMap.put("DefaultDeductible", deductibleM);
                                        riskMap.put("DefaultCompensationRatio", compensationRatioM);
                                        break;
                                    } else {
                                        amountGrageCodeMap.put("Prem", "0");
                                        amountGrageCodeMap.put("isChecked", "0");//非选中
                                    }
                                }
                                amountGrageCodeMap.put("AmountGrageCode", amountGrageCode);
                                amountGrageCodeMap.put("Amnt", Amnt);
                                //查询当前保额档次是否含有免赔额、赔付比例、可选责任
                                List<String> deductibleList = fcEnsureMapper.selectDeductibleList(amountGrageCode);
                                List<String> compensationRatioList = fcEnsureMapper.selectCompensationRatioList(amountGrageCode);
                                List<Map<String, Object>> optDutyCodeList = fcEnsureMapper.selectOptDutyCodeList(amountGrageCode, riskCode);
                                if (deductibleList != null && deductibleList.size() > 0) {
                                    List<Map<String, Object>> deductibleMapList = new ArrayList<Map<String, Object>>();
                                    for (String deductible : deductibleList) {
                                        Map<String, Object> deductiblemap = new HashMap<String, Object>();
                                        deductiblemap.put("Deductible", deductible);
                                        if (deductible.equals(deductibleM)) {
                                            deductiblemap.put("isChecked", "1");
                                        } else {
                                            deductiblemap.put("isChecked", "0");
                                        }
                                        deductibleMapList.add(deductiblemap);
                                    }
                                    amountGrageCodeMap.put("DeductibleList", deductibleMapList);
                                }
                                if (compensationRatioList != null && compensationRatioList.size() > 0) {
                                    List<Map<String, Object>> compensationRatioMapList = new ArrayList<Map<String, Object>>();
                                    for (String compensationRatio : compensationRatioList) {
                                        Map<String, Object> compensationRatioMmap = new HashMap<String, Object>();
                                        compensationRatioMmap.put("CompensationRatio", compensationRatio);
                                        if (compensationRatio.equals(compensationRatioM)) {
                                            compensationRatioMmap.put("isChecked", "1");
                                        } else {
                                            compensationRatioMmap.put("isChecked", "0");
                                        }
                                        compensationRatioMapList.add(compensationRatioMmap);
                                    }
                                    amountGrageCodeMap.put("CompensationRatioList", compensationRatioMapList);
                                }
                                if (optDutyCodeList != null && optDutyCodeList.size() > 0) {
                                    for (Map<String, Object> map2 : optDutyCodeList) {
                                        String optDutyCode = map2.get("OptDutyCode").toString();
                                        for (Map<String, String> mapM : checkInfoIsExist) {
                                            String dutyCodeM = mapM.get("dutyCode");
                                            String prem = mapM.get("prem");
                                            String amountGrageCodeM = mapM.get("AmountGrageCode");
                                            String isChoice = mapM.get("isChoice");//1-必选保额档次;2-可选保额档次
                                            if (amountGrageCodeM.equals(amountGrageCode) && isChoice.equals("2") && optDutyCode.equals(dutyCodeM)) {
                                                map2.put("isChecked", "1");
                                                map2.put("Prem", prem);
                                                break;
                                            } else {
                                                map2.put("Prem", "0");
                                                map2.put("isChecked", "0");
                                            }
                                        }
                                    }
                                    amountGrageCodeMap.put("OptDutyCodeList", optDutyCodeList);
                                }
                                amountGrageCodeMap.put("IsDefaultFlag", isDefaultFlag);
                                amountGrageCodeList.add(amountGrageCodeMap);
                            }
                        }
                        riskMap.put("AmountGrageCodeList", amountGrageCodeList);
                        tList.add(riskMap);
                    }
                    Map<String, Object> rMap = new HashMap<String, Object>();
                    rMap.put("riskList", tList);
                    rMap.put("isExists", "1");//保存过保额档次信息
                    resultMap.put("data", rMap);
                    resultMap.put("code", "200");
                    resultMap.put("success", true);
                    resultMap.put("message", "查询成功");
                } else {
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", "没有查询到符合当前员工的责任档次");
                }
            } else {
                Map<String, Object> paraMap = new HashMap<>();
                paraMap.put("EnsureCode", ensureCode);
                paraMap.put("grpNo", grpNo);
                paraMap.put("BirthDay", fcperinfo.getBirthDay());
                if (StringUtil.isEmpty(levelCode)) {
                    return JSON.toJSONString(ResultUtil.error("员工职级查询有误！"));
                }
                paraMap.put("LevelCode", levelCode);
                paraMap.put("OccupationType", fcperinfo.getOccupationType());
                paraMap.put("Sex", fcperinfo.getSex());
                paraMap.put("ServiceTerm", fcperinfo.getServiceTerm());
                paraMap.put("Retirement", fcperinfo.getRetirement());
                paraMap.put("Relation", "0");
                paraMap.put("JoinMedProtect", fcperinfo.getJoinMedProtect());
                paraMap.put("InsureCount", fcEnsure.getInsuredNumber());
                //查询最小保额的默认档次
                List<Map<String, Object>> minAmntDefaultList = fcEnsureMapper.selectMinAmntDefault(paraMap);
                //获取员工符合条件的保额档次
                List<Map<String, Object>> resultList = fcEnsureMapper.selectEflexEmployList(paraMap);
                if (resultList != null && resultList.size() > 0) {
                    List<String> riskList = new ArrayList<>();
                    Map<String, Object> amountGrageCodeAndFlaggMap = new HashMap<>();
                    List<String> amountGrageCodeAndFalgList = new ArrayList<>();
                    for (Map<String, Object> map : resultList) {
                        String riskName = map.get("RiskName").toString();
                        if (!riskList.contains(riskName)) riskList.add(riskName);
                        String amountGrageCode = map.get("AmountGrageCode").toString();
                        String isDefaultFlag = map.get("IsDefaultFlag").toString();
                        if (amountGrageCodeAndFlaggMap.containsKey(amountGrageCode)) {
                            amountGrageCodeAndFalgList.add(amountGrageCode);
                        } else {
                            amountGrageCodeAndFlaggMap.put(amountGrageCode, isDefaultFlag);
                        }
                    }
                    if (amountGrageCodeAndFalgList != null && amountGrageCodeAndFalgList.size() > 0) {
                        for (int i = 0; i < resultList.size(); i++) {
                            Map<String, Object> map = resultList.get(i);
                            String amountGrageCode = map.get("AmountGrageCode").toString();
                            String isDefaultFlag = map.get("IsDefaultFlag").toString();
                            for (String amountGrageCodeIs : amountGrageCodeAndFalgList) {
                                if (amountGrageCodeIs.equals(amountGrageCode) && isDefaultFlag.equals("0")) {
                                    resultList.remove(i);
                                }
                            }
                        }
                    }
                    List<Map<String, Object>> tList = new ArrayList<>();
                    for (String riskName : riskList) {
                        Map<String, Object> riskMap = new HashMap<>();
                        List<Map<String, Object>> amountGrageCodeList = new ArrayList<>();
                        for (Map<String, Object> map : resultList) {
                            String riskCode = map.get("RiskCode").toString();
                            String riskNameC = map.get("RiskName").toString();
                            String riskType = map.get("RiskType").toString();
                            String amountGrageCode = map.get("AmountGrageCode").toString();
                            String annualTimeDeduction = map.get("AnnualTimeDeduction").toString();
                            String dutyName = map.get("DutyName").toString();
                            String Amnt = map.get("Amnt").toString();
                            String isDefaultFlag = map.get("IsDefaultFlag").toString();
                            if (riskName.equals(riskNameC)) {
                                riskMap.put("RiskCode", riskCode);
                                riskMap.put("RiskType", riskType);
                                riskMap.put("RiskName", riskName);
                                riskMap.put("BirthDay", fcperinfo.getBirthDay());
                                riskMap.put("Sex", fcperinfo.getSex());
                                riskMap.put("JoinMedProtect", fcperinfo.getJoinMedProtect());
                                riskMap.put("OccupationType", fcperinfo.getOccupationType());
                                riskMap.put("CvaliDate", map.get("CvaliDate").toString());
                                riskMap.put("InsureCount", map.get("InsuredNumber").toString());
                                riskMap.put("DutyName", dutyName);
                                Map<String, Object> amountGrageCodeMap = new HashMap<>();
                                amountGrageCodeMap.put("RiskCode", riskCode);
                                amountGrageCodeMap.put("RiskType", riskType);
                                amountGrageCodeMap.put("RiskName", riskName);
                                amountGrageCodeMap.put("AnnualTimeDeduction", annualTimeDeduction);
                                if (minAmntDefaultList != null && minAmntDefaultList.size() > 0) {
                                    for (Map<String, Object> map2 : minAmntDefaultList) {
                                        String defAmountGrageCode = map2.get("AmountGrageCode").toString();
                                        String amnt = map2.get("Amnt").toString();
                                        if (defAmountGrageCode.equals(amountGrageCode)) {
                                            //查询当前保额档次是否含有免赔额、赔付比例、可选责任
                                            String defaultDeductible = map.get("DefaultDeductible") == null ? "" : map.get("DefaultDeductible").toString();
                                            String defaultCompensationRatio = map.get("DefaultCompensationRatio") == null ? "" : map.get("DefaultCompensationRatio").toString();
                                            paraMap.put("CvaliDate", map.get("CvaliDate").toString());
                                            paraMap.put("RiskCode", riskCode);
                                            paraMap.put("AmountGrageCode", amountGrageCode);
                                            paraMap.put("DefaultDeductible", defaultDeductible);
                                            paraMap.put("DefaultCompensationRatio", defaultCompensationRatio);
                                            Map<String, Object> generateRequest = premTrailService.getDefaultPlanPrem(paraMap);
                                            if (generateRequest != null) {
                                                Map<String, Object> resultmap = premTrailService.premTrail(generateRequest);
                                                if (Boolean.valueOf(resultmap.get("success").toString())) {
                                                    if (riskCode.equals("17020") || riskCode.equals("15060") || riskCode.equals("15070")) {
                                                        Map<String, Object> mapPrem = (Map<String, Object>) resultmap.get("Prem");
                                                        amountGrageCodeMap.put("Prem", mapPrem.get("Prem"));
                                                    } else {
                                                        amountGrageCodeMap.put("Prem", resultmap.get("Prem").toString());
                                                    }
                                                }
                                            }
                                            if (amountGrageCodeMap.get("Prem") != null)
                                                riskMap.put("DefaultAmnt", amnt);
                                            amountGrageCodeMap.put("AmountGrageCode", amountGrageCode);
                                            amountGrageCodeMap.put("Amnt", Amnt);
                                            List<String> deductibleList = fcEnsureMapper.selectDeductibleList(amountGrageCode);
                                            List<String> compensationRatioList = fcEnsureMapper.selectCompensationRatioList(amountGrageCode);
                                            List<Map<String, Object>> optDutyCodeList = fcEnsureMapper.selectOptDutyCodeList(amountGrageCode, riskCode);
                                            if (deductibleList != null && deductibleList.size() > 0) {
                                                List<Map<String, Object>> deductibleMapList = new ArrayList<Map<String, Object>>();
                                                for (String deductible : deductibleList) {
                                                    Map<String, Object> deductiblemap = new HashMap<String, Object>();
                                                    deductiblemap.put("Deductible", deductible);
                                                    if (deductible.equals(defaultDeductible)) {
                                                        if (amountGrageCodeMap.get("Prem") == null) {
                                                            deductiblemap.put("isChecked", "0");
                                                        } else {
                                                            deductiblemap.put("isChecked", "1");
                                                            riskMap.put("DefaultDeductible", deductible);
                                                        }
                                                    } else {
                                                        deductiblemap.put("isChecked", "0");
                                                    }
                                                    deductibleMapList.add(deductiblemap);
                                                }
                                                amountGrageCodeMap.put("DeductibleList", deductibleMapList);
                                            }
                                            if (compensationRatioList != null && compensationRatioList.size() > 0) {
                                                List<Map<String, Object>> compensationRatioMapList = new ArrayList<Map<String, Object>>();
                                                for (String compensationRatio : compensationRatioList) {
                                                    Map<String, Object> compensationRatioMmap = new HashMap<String, Object>();
                                                    compensationRatioMmap.put("CompensationRatio", compensationRatio);
                                                    if (compensationRatio.equals(defaultCompensationRatio)) {
                                                        if (amountGrageCodeMap.get("Prem") == null) {
                                                            compensationRatioMmap.put("isChecked", "0");
                                                        } else {
                                                            compensationRatioMmap.put("isChecked", "1");
                                                            riskMap.put("DefaultCompensationRatio", compensationRatio);
                                                        }
                                                    } else {
                                                        compensationRatioMmap.put("isChecked", "0");
                                                    }
                                                    compensationRatioMapList.add(compensationRatioMmap);
                                                }
                                                amountGrageCodeMap.put("CompensationRatioList", compensationRatioMapList);
                                            }
                                            if (optDutyCodeList != null && optDutyCodeList.size() > 0)
                                                amountGrageCodeMap.put("OptDutyCodeList", optDutyCodeList);
                                            amountGrageCodeMap.put("IsDefaultFlag", amountGrageCodeMap.get("Prem") == null ? "0" : isDefaultFlag);
                                            if (amountGrageCodeMap.get("Prem") == null)
                                                amountGrageCodeMap.put("Prem", "0");
                                            break;
                                        }
                                    }
                                }
                                if (amountGrageCodeMap == null || amountGrageCodeMap.get("AmountGrageCode") == null) {
                                    amountGrageCodeMap.put("AmountGrageCode", amountGrageCode);
                                    amountGrageCodeMap.put("Amnt", Amnt);
                                    amountGrageCodeMap.put("Prem", "0");
                                    //查询当前保额档次是否含有免赔额、赔付比例、可选责任
                                    List<String> deductibleList = fcEnsureMapper.selectDeductibleList(amountGrageCode);
                                    List<String> compensationRatioList = fcEnsureMapper.selectCompensationRatioList(amountGrageCode);
                                    List<Map<String, Object>> optDutyCodeList = fcEnsureMapper.selectOptDutyCodeList(amountGrageCode, riskCode);
                                    if (deductibleList != null && deductibleList.size() > 0) {
                                        List<Map<String, Object>> deductibleMapList = new ArrayList<Map<String, Object>>();
                                        for (String deductible : deductibleList) {
                                            Map<String, Object> deductiblemap = new HashMap<String, Object>();
                                            deductiblemap.put("Deductible", deductible);
                                            deductiblemap.put("isChecked", "0");
                                            deductibleMapList.add(deductiblemap);
                                        }
                                        amountGrageCodeMap.put("DeductibleList", deductibleMapList);
                                    }
                                    if (compensationRatioList != null && compensationRatioList.size() > 0) {
                                        List<Map<String, Object>> compensationRatioMapList = new ArrayList<Map<String, Object>>();
                                        for (String compensationRatio : compensationRatioList) {
                                            Map<String, Object> compensationRatioMmap = new HashMap<String, Object>();
                                            compensationRatioMmap.put("CompensationRatio", compensationRatio);
                                            compensationRatioMmap.put("isChecked", "0");
                                            compensationRatioMapList.add(compensationRatioMmap);
                                        }
                                        amountGrageCodeMap.put("CompensationRatioList", compensationRatioMapList);
                                    }
                                    if (optDutyCodeList != null && optDutyCodeList.size() > 0)
                                        amountGrageCodeMap.put("OptDutyCodeList", optDutyCodeList);
                                    amountGrageCodeMap.put("IsDefaultFlag", "0");
                                }
                                amountGrageCodeList.add(amountGrageCodeMap);
                            }
                        }
                        riskMap.put("AmountGrageCodeList", amountGrageCodeList);
                        tList.add(riskMap);
                    }
                    Map<String, Object> rMap = new HashMap<>();
                    rMap.put("riskList", tList);
                    rMap.put("isExists", "0");//没有保存过保额档次信息
                    resultMap.put("data", rMap);
                    resultMap.put("code", "200");
                    resultMap.put("success", true);
                    resultMap.put("message", "查询成功");
                } else {
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", "没有查询到符合当前员工的责任档次");
                }
            }
        } catch (Exception e) {

            log.info("查询失败>" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    public String eflexEmployFamilyList(String token, String personId, String relation) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (personId == null || "".equals(personId)) {
                GlobalInput globalInput = userService.getSession(token);
                String ensureCode = globalInput.getEnsureCode();
                String perNo = globalInput.getCustomNo();
                //查询员工下所有家属
                List<Map<String, Object>> getAllSaveFamilyList = fPInsureEflexPlanMapper.getAllSaveFamily(ensureCode, perNo);
                if (getAllSaveFamilyList != null && getAllSaveFamilyList.size() > 0) {
                    List<Map<String, Object>> famList = new ArrayList<>();
                    getAllSaveFamilyList.forEach((Map<String, Object> famMap) -> {
                        String personID = famMap.get("personId").toString();
                        String relaTion = famMap.get("relation").toString();
                        Map<String, Object> resultFamMap = eflexEmployFamilyDetailList(token, personID, relaTion);
                        Map<String, Object> dataFamMap = (Map<String, Object>) resultFamMap.get("data");
                        famList.add(dataFamMap);
                    });
                    resultMap.put("data", famList);
                    resultMap.put("code", "200");
                    resultMap.put("success", true);
                    resultMap.put("message", "查询成功");
                }
            } else {
                resultMap = eflexEmployFamilyDetailList(token, personId, relation);
            }
        } catch (Exception e) {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    public Map<String, Object> eflexEmployFamilyDetailList(String token, String personId, String relation) {
        Map<String, Object> resultMap = new HashMap<>();
        Double totalPrem = 0.0;
        try {
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            String perNo = globalInput.getCustomNo();
            //获取福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            //获取家属信息
            FCPerson fcperson = fcPersonMapper.selectByPrimaryKey1(personId);
            //首先判断暂存表是否有数据
            Map<String, Object> params = new HashMap<>();
            params.put("perNo", perNo);
            params.put("ensureCode", ensureCode);
            params.put("personId", personId);
            List<Map<String, String>> checkInfoIsExist = fPInsureEflexPlanMapper.checkInfoIsExist(params);
            if (checkInfoIsExist != null && checkInfoIsExist.size() > 0) {
                Map<String, Object> paraMap = new HashMap<>();
                paraMap.put("EnsureCode", ensureCode);
                paraMap.put("BirthDay", fcperson.getBirthDate());
                paraMap.put("OccupationType", fcperson.getOccupationType());
                paraMap.put("Sex", fcperson.getSex());
                paraMap.put("Relation", relation);
                paraMap.put("JoinMedProtect", fcperson.getJoinMedProtect());
                paraMap.put("InsureCount", fcEnsure.getInsuredNumber());
                //获取家属符合条件的保额档次
                List<Map<String, Object>> resultList = fcEnsureMapper.selectEflexEmployFamilyList(paraMap);
                if (resultList != null && resultList.size() > 0) {
                    List<String> riskList = new ArrayList<>();
                    Map<String, Object> amountGrageCodeAndFlaggMap = new HashMap<>();
                    List<String> amountGrageCodeAndFalgList = new ArrayList<>();
                    for (Map<String, Object> map : resultList) {
                        String riskName = map.get("RiskName").toString();
                        if (!riskList.contains(riskName)) {
                            riskList.add(riskName);
                        }
                        String amountGrageCode = map.get("AmountGrageCode").toString();
                        String isDefaultFlag = map.get("IsDefaultFlag").toString();
                        if (amountGrageCodeAndFlaggMap.containsKey(amountGrageCode)) {
                            amountGrageCodeAndFalgList.add(amountGrageCode);
                        } else {
                            amountGrageCodeAndFlaggMap.put(amountGrageCode, isDefaultFlag);
                        }
                    }
                    if (amountGrageCodeAndFalgList.size() > 0) {
                        for (int i = 0; i < resultList.size(); i++) {
                            Map<String, Object> map = resultList.get(i);
                            String amountGrageCode = map.get("AmountGrageCode").toString();
                            String isDefaultFlag = map.get("IsDefaultFlag").toString();
                            for (String amountGrageCodeIs : amountGrageCodeAndFalgList) {
                                if (amountGrageCodeIs.equals(amountGrageCode) && "0".equals(isDefaultFlag)) {
                                    resultList.remove(i);
                                }
                            }
                        }
                    }
                    List<Map<String, Object>> tList = new ArrayList<>();
                    for (String riskName : riskList) {
                        Map<String, Object> riskMap = new HashMap<>();
                        List<Map<String, Object>> amountGrageCodeList = new ArrayList<>();
                        for (Map<String, Object> map : resultList) {
                            String riskCode = map.get("RiskCode").toString();
                            String riskNameC = map.get("RiskName").toString();
                            String riskType = map.get("RiskType").toString();
                            String amountGrageCode = map.get("AmountGrageCode").toString();
                            String annualTimeDeduction = map.get("AnnualTimeDeduction").toString();
                            String dutyName = map.get("DutyName").toString();
                            String Amnt = map.get("Amnt").toString();
                            String isDefaultFlag = map.get("IsDefaultFlag").toString();
                            if (riskName.equals(riskNameC)) {
                                riskMap.put("RiskCode", riskCode);
                                riskMap.put("RiskName", riskName);
                                riskMap.put("RiskType", riskType);
                                riskMap.put("BirthDay", fcperson.getBirthDate());
                                riskMap.put("Sex", fcperson.getSex());
                                riskMap.put("JoinMedProtect", fcperson.getJoinMedProtect());
                                riskMap.put("OccupationType", fcperson.getOccupationType());
                                riskMap.put("CvaliDate", map.get("CvaliDate").toString());
                                riskMap.put("InsureCount", map.get("InsuredNumber").toString());
                                riskMap.put("DutyName", dutyName);
                                Map<String, Object> amountGrageCodeMap = new HashMap<>();
                                amountGrageCodeMap.put("RiskCode", riskCode);
                                amountGrageCodeMap.put("RiskType", riskType);
                                amountGrageCodeMap.put("RiskName", riskName);
                                amountGrageCodeMap.put("AnnualTimeDeduction", annualTimeDeduction);
                                String deductibleM = "";
                                String compensationRatioM = "";
                                for (Map<String, String> mapM : checkInfoIsExist) {
                                    String riskNameM = mapM.get("riskName");
                                    String prem = mapM.get("prem");
                                    String amnt = mapM.get("amnt");
                                    String amountGrageCodeM = mapM.get("AmountGrageCode");
                                    String isChoice = mapM.get("isChoice");//1-必选保额档次;2-可选保额档次
                                    if (riskNameM.equals(riskName) && amountGrageCodeM.equals(amountGrageCode) && "1".equals(isChoice)) {
                                        amountGrageCodeMap.put("isChecked", "1");//选中
                                        amountGrageCodeMap.put("Prem", prem);
                                        deductibleM = mapM.get("Deductible");//免赔额
                                        compensationRatioM = mapM.get("CompensationRatio");//赔付比例
                                        totalPrem = CommonUtil.add(totalPrem, Double.valueOf(prem == null || "".equals(prem) ? "0.0" : prem));
                                        riskMap.put("DefaultAmnt", amnt);
                                        riskMap.put("DefaultDeductible", deductibleM);
                                        riskMap.put("DefaultCompensationRatio", compensationRatioM);
                                        break;
                                    } else {
                                        amountGrageCodeMap.put("Prem", "0");
                                        amountGrageCodeMap.put("isChecked", "0");//非选中
                                    }
                                }
                                amountGrageCodeMap.put("AmountGrageCode", amountGrageCode);
                                amountGrageCodeMap.put("Amnt", Amnt);
                                //查询当前保额档次是否含有免赔额、赔付比例、可选责任
                                List<String> deductibleList = fcEnsureMapper.selectDeductibleList(amountGrageCode);
                                List<String> compensationRatioList = fcEnsureMapper.selectCompensationRatioList(amountGrageCode);
                                List<Map<String, Object>> optDutyCodeList = fcEnsureMapper.selectOptDutyCodeList(amountGrageCode, riskCode);
                                if (deductibleList != null && deductibleList.size() > 0) {
                                    List<Map<String, Object>> deductibleMapList = new ArrayList<>();
                                    for (String deductible : deductibleList) {
                                        Map<String, Object> deductiblemap = new HashMap<>();
                                        deductiblemap.put("Deductible", deductible);
                                        if (deductible.equals(deductibleM)) {
                                            deductiblemap.put("isChecked", "1");
                                        } else {
                                            deductiblemap.put("isChecked", "0");
                                        }
                                        deductibleMapList.add(deductiblemap);
                                    }
                                    amountGrageCodeMap.put("DeductibleList", deductibleMapList);
                                }
                                if (compensationRatioList != null && compensationRatioList.size() > 0) {
                                    List<Map<String, Object>> compensationRatioMapList = new ArrayList<Map<String, Object>>();
                                    for (String compensationRatio : compensationRatioList) {
                                        Map<String, Object> compensationRatioMmap = new HashMap<String, Object>();
                                        compensationRatioMmap.put("CompensationRatio", compensationRatio);
                                        if (compensationRatio.equals(compensationRatioM)) {
                                            compensationRatioMmap.put("isChecked", "1");
                                        } else {
                                            compensationRatioMmap.put("isChecked", "0");
                                        }
                                        compensationRatioMapList.add(compensationRatioMmap);
                                    }
                                    amountGrageCodeMap.put("CompensationRatioList", compensationRatioMapList);
                                }
                                if (optDutyCodeList != null && optDutyCodeList.size() > 0) {
                                    for (Map<String, Object> map2 : optDutyCodeList) {
                                        String optDutyCode = map2.get("OptDutyCode").toString();
                                        for (Map<String, String> mapM : checkInfoIsExist) {
                                            String dutyCodeM = mapM.get("dutyCode");
                                            String prem = mapM.get("prem");
                                            String amountGrageCodeM = mapM.get("AmountGrageCode");
                                            String isChoice = mapM.get("isChoice");//1-必选保额档次;2-可选保额档次
                                            if (amountGrageCodeM.equals(amountGrageCode) && "2".equals(isChoice) && optDutyCode.equals(dutyCodeM)) {
                                                map2.put("isChecked", "1");
                                                map2.put("Prem", prem);
                                                totalPrem = CommonUtil.add(totalPrem, Double.valueOf(prem == null || "".equals(prem) ? "0.0" : prem));
                                                break;
                                            } else {
                                                map2.put("Prem", "0");
                                                map2.put("isChecked", "0");
                                            }
                                        }
                                    }
                                    amountGrageCodeMap.put("OptDutyCodeList", optDutyCodeList);
                                }
                                amountGrageCodeMap.put("IsDefaultFlag", isDefaultFlag);
                                amountGrageCodeList.add(amountGrageCodeMap);
                            }
                        }
                        riskMap.put("AmountGrageCodeList", amountGrageCodeList);
                        tList.add(riskMap);
                    }
                    Map<String, Object> rMap = new HashMap<>();
                    rMap.put("riskList", tList);
                    rMap.put("isExists", "1");//保存过保额档次信息
                    rMap.put("totalPrem", totalPrem);//保费合计
                    rMap.put("personId", personId);
                    rMap.put("personName", fcperson.getName());
                    rMap.put("relation", fcperson.getRelation());
                    resultMap.put("data", rMap);
                    resultMap.put("code", "200");
                    resultMap.put("success", true);
                    resultMap.put("message", "查询成功");
                } else {
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", "没有查询到符合当前家属的责任档次");
                }
            } else {
                Map<String, Object> paraMap = new HashMap<>();
                paraMap.put("EnsureCode", ensureCode);
                paraMap.put("BirthDay", fcperson.getBirthDate());
                paraMap.put("OccupationType", fcperson.getOccupationType());
                paraMap.put("Sex", fcperson.getSex());
                paraMap.put("Relation", relation);
                paraMap.put("JoinMedProtect", fcperson.getJoinMedProtect());
                paraMap.put("InsureCount", fcEnsure.getInsuredNumber());
                //查询家属最小保额的默认档次
                List<Map<String, Object>> minAmntDefaultList = fcEnsureMapper.selectFamMinAmntDefault(paraMap);
                //获取家属符合条件的保额档次
                List<Map<String, Object>> resultList = fcEnsureMapper.selectEflexEmployFamilyList(paraMap);
                if (resultList != null && resultList.size() > 0) {
                    List<String> riskList = new ArrayList<String>();
                    Map<String, Object> amountGrageCodeAndFlaggMap = new HashMap<String, Object>();
                    List<String> amountGrageCodeAndFalgList = new ArrayList<>();
                    for (Map<String, Object> map : resultList) {
                        String riskName = map.get("RiskName").toString();
                        if (!riskList.contains(riskName)) riskList.add(riskName);
                        String amountGrageCode = map.get("AmountGrageCode").toString();
                        String isDefaultFlag = map.get("IsDefaultFlag").toString();
                        if (amountGrageCodeAndFlaggMap.containsKey(amountGrageCode)) {
                            amountGrageCodeAndFalgList.add(amountGrageCode);
                        } else {
                            amountGrageCodeAndFlaggMap.put(amountGrageCode, isDefaultFlag);
                        }
                    }
                    if (amountGrageCodeAndFalgList.size() > 0) {
                        for (int i = 0; i < resultList.size(); i++) {
                            Map<String, Object> map = resultList.get(i);
                            String amountGrageCode = map.get("AmountGrageCode").toString();
                            String isDefaultFlag = map.get("IsDefaultFlag").toString();
                            for (String amountGrageCodeIs : amountGrageCodeAndFalgList) {
                                if (amountGrageCodeIs.equals(amountGrageCode) && "0".equals(isDefaultFlag)) {
                                    resultList.remove(i);
                                }
                            }
                        }
                    }
                    List<Map<String, Object>> tList = new ArrayList<>();
                    for (String riskName : riskList) {
                        Map<String, Object> riskMap = new HashMap<>();
                        List<Map<String, Object>> amountGrageCodeList = new ArrayList<>();
                        for (Map<String, Object> map : resultList) {
                            String riskCode = map.get("RiskCode").toString();
                            String riskNameC = map.get("RiskName").toString();
                            String riskType = map.get("RiskType").toString();
                            String amountGrageCode = map.get("AmountGrageCode").toString();
                            String annualTimeDeduction = map.get("AnnualTimeDeduction").toString();
                            String dutyName = map.get("DutyName").toString();
                            String Amnt = map.get("Amnt").toString();
                            String isDefaultFlag = map.get("IsDefaultFlag").toString();
                            if (riskName.equals(riskNameC)) {
                                riskMap.put("RiskCode", riskCode);
                                riskMap.put("RiskName", riskName);
                                riskMap.put("RiskType", riskType);
                                riskMap.put("BirthDay", fcperson.getBirthDate());
                                riskMap.put("Sex", fcperson.getSex());
                                riskMap.put("JoinMedProtect", fcperson.getJoinMedProtect());
                                riskMap.put("OccupationType", fcperson.getOccupationType());
                                riskMap.put("CvaliDate", map.get("CvaliDate").toString());
                                riskMap.put("InsureCount", map.get("InsuredNumber").toString());
                                riskMap.put("DutyName", dutyName);
                                Map<String, Object> amountGrageCodeMap = new HashMap<String, Object>();
                                amountGrageCodeMap.put("RiskCode", riskCode);
                                amountGrageCodeMap.put("RiskType", riskType);
                                amountGrageCodeMap.put("RiskName", riskName);
                                amountGrageCodeMap.put("AnnualTimeDeduction", annualTimeDeduction);
                                if (minAmntDefaultList != null && minAmntDefaultList.size() > 0) {
                                    for (Map<String, Object> map2 : minAmntDefaultList) {
                                        String defAmountGrageCode = map2.get("AmountGrageCode").toString();
                                        if (defAmountGrageCode.equals(amountGrageCode)) {
                                            //查询当前保额档次是否含有免赔额、赔付比例、可选责任
                                            String defaultDeductible = map.get("DefaultDeductible") == null ? "" : map.get("DefaultDeductible").toString();
                                            String defaultCompensationRatio = map.get("DefaultCompensationRatio") == null ? "" : map.get("DefaultCompensationRatio").toString();
                                            paraMap.put("CvaliDate", map.get("CvaliDate").toString());
                                            paraMap.put("RiskCode", riskCode);
                                            paraMap.put("AmountGrageCode", amountGrageCode);
                                            paraMap.put("DefaultDeductible", defaultDeductible);
                                            paraMap.put("DefaultCompensationRatio", defaultCompensationRatio);
                                            Map<String, Object> generateRequest = premTrailService.getDefaultPlanPrem(paraMap);
                                            if (generateRequest != null) {
                                                Map<String, Object> resultmap = premTrailService.premTrail(generateRequest);
                                                if (Boolean.valueOf(resultmap.get("success").toString())) {
                                                    if ("17020".equals(riskCode) || "15060".equals(riskCode) || "15070".equals(riskCode)) {
                                                        Map<String, Object> mapPrem = (Map<String, Object>) resultmap.get("Prem");
                                                        String prem = (String) mapPrem.get("Prem");
                                                        amountGrageCodeMap.put("Prem", prem);
                                                        totalPrem = CommonUtil.add(totalPrem, Double.valueOf(prem == null || "".equals(prem) ? "0.0" : prem));
                                                    } else {
                                                        String prem = resultmap.get("Prem").toString();
                                                        amountGrageCodeMap.put("Prem", resultmap.get("Prem").toString());
                                                        totalPrem = CommonUtil.add(totalPrem, Double.valueOf(prem == null || "".equals(prem) ? "0.0" : prem));
                                                    }
                                                }
                                            }
                                            amountGrageCodeMap.put("AmountGrageCode", amountGrageCode);
                                            amountGrageCodeMap.put("Amnt", Amnt);
                                            if (amountGrageCodeMap.get("Prem") != null)
                                                riskMap.put("DefaultAmnt", Amnt);
                                            List<String> deductibleList = fcEnsureMapper.selectDeductibleList(amountGrageCode);
                                            List<String> compensationRatioList = fcEnsureMapper.selectCompensationRatioList(amountGrageCode);
                                            List<Map<String, Object>> optDutyCodeList = fcEnsureMapper.selectOptDutyCodeList(amountGrageCode, riskCode);
                                            if (deductibleList != null && deductibleList.size() > 0) {
                                                List<Map<String, Object>> deductibleMapList = new ArrayList<>();
                                                for (String deductible : deductibleList) {
                                                    Map<String, Object> deductiblemap = new HashMap<String, Object>();
                                                    deductiblemap.put("Deductible", deductible);
                                                    if (deductible.equals(defaultDeductible)) {
                                                        if (amountGrageCodeMap.get("Prem") == null) {
                                                            deductiblemap.put("isChecked", "0");
                                                        } else {
                                                            deductiblemap.put("isChecked", "1");
                                                            riskMap.put("DefaultDeductible", deductible);
                                                        }
                                                    } else {
                                                        deductiblemap.put("isChecked", "0");
                                                    }
                                                    deductibleMapList.add(deductiblemap);
                                                }
                                                amountGrageCodeMap.put("DeductibleList", deductibleMapList);
                                            }
                                            if (compensationRatioList != null && compensationRatioList.size() > 0) {
                                                List<Map<String, Object>> compensationRatioMapList = new ArrayList<Map<String, Object>>();
                                                for (String compensationRatio : compensationRatioList) {
                                                    Map<String, Object> compensationRatioMmap = new HashMap<String, Object>();
                                                    compensationRatioMmap.put("CompensationRatio", compensationRatio);
                                                    if (compensationRatio.equals(defaultCompensationRatio)) {
                                                        if (amountGrageCodeMap.get("Prem") == null) {
                                                            compensationRatioMmap.put("isChecked", "0");
                                                        } else {
                                                            compensationRatioMmap.put("isChecked", "1");
                                                            riskMap.put("DefaultCompensationRatio", compensationRatio);
                                                        }
                                                    } else {
                                                        compensationRatioMmap.put("isChecked", "0");
                                                    }
                                                    compensationRatioMapList.add(compensationRatioMmap);
                                                }
                                                amountGrageCodeMap.put("CompensationRatioList", compensationRatioMapList);
                                            }
                                            if (optDutyCodeList != null && optDutyCodeList.size() > 0)
                                                amountGrageCodeMap.put("OptDutyCodeList", optDutyCodeList);
                                            amountGrageCodeMap.put("IsDefaultFlag", amountGrageCodeMap.get("Prem") == null ? "0" : isDefaultFlag);
                                            if (amountGrageCodeMap.get("Prem") == null)
                                                amountGrageCodeMap.put("Prem", "0");
                                        }
                                    }
                                }
                                if (amountGrageCodeMap.get("AmountGrageCode") == null) {
                                    amountGrageCodeMap.put("AmountGrageCode", amountGrageCode);
                                    amountGrageCodeMap.put("Amnt", Amnt);
                                    amountGrageCodeMap.put("Prem", "0");
                                    //查询当前保额档次是否含有免赔额、赔付比例、可选责任
                                    List<String> deductibleList = fcEnsureMapper.selectDeductibleList(amountGrageCode);
                                    List<String> compensationRatioList = fcEnsureMapper.selectCompensationRatioList(amountGrageCode);
                                    List<Map<String, Object>> optDutyCodeList = fcEnsureMapper.selectOptDutyCodeList(amountGrageCode, riskCode);
                                    if (deductibleList != null && deductibleList.size() > 0) {
                                        List<Map<String, Object>> deductibleMapList = new ArrayList<>();
                                        for (String deductible : deductibleList) {
                                            Map<String, Object> deductiblemap = new HashMap<>();
                                            deductiblemap.put("Deductible", deductible);
                                            deductiblemap.put("isChecked", "0");
                                            deductibleMapList.add(deductiblemap);
                                        }
                                        amountGrageCodeMap.put("DeductibleList", deductibleMapList);
                                    }
                                    if (compensationRatioList != null && compensationRatioList.size() > 0) {
                                        List<Map<String, Object>> compensationRatioMapList = new ArrayList<>();
                                        for (String compensationRatio : compensationRatioList) {
                                            Map<String, Object> compensationRatioMmap = new HashMap<>();
                                            compensationRatioMmap.put("CompensationRatio", compensationRatio);
                                            compensationRatioMmap.put("isChecked", "0");
                                            compensationRatioMapList.add(compensationRatioMmap);
                                        }
                                        amountGrageCodeMap.put("CompensationRatioList", compensationRatioMapList);
                                    }
                                    if (optDutyCodeList != null && optDutyCodeList.size() > 0)
                                        amountGrageCodeMap.put("OptDutyCodeList", optDutyCodeList);
                                    amountGrageCodeMap.put("IsDefaultFlag", "0");
                                }
                                amountGrageCodeList.add(amountGrageCodeMap);
                            }
                        }
                        riskMap.put("AmountGrageCodeList", amountGrageCodeList);
                        tList.add(riskMap);
                    }
                    Map<String, Object> rMap = new HashMap<String, Object>();
                    rMap.put("riskList", tList);
                    rMap.put("isExists", "0");//没有保存过保额档次信息
                    rMap.put("totalPrem", totalPrem);//保费合计
                    rMap.put("personId", personId);
                    rMap.put("personName", fcperson.getName());
                    rMap.put("relation", fcperson.getRelation());
                    resultMap.put("data", rMap);
                    resultMap.put("code", "200");
                    resultMap.put("success", true);
                    resultMap.put("message", "查询成功");
                } else {
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", "没有查询到符合当前家属的责任档次");
                }
            }
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "查询失败");
        }
        return resultMap;
    }

    public Map<String, Object> eflexPersonInsureSave(String token, Map<String, Object> paramMap) {
        String personId = paramMap.get("personId").toString();
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            /****************************当前员工不存在当前的家属则同步信息************************/
            Map<String, Object> SyncMap = SynchronizationFamilyinfo(token, personId);
            if (!SyncMap.get("success").equals(true)) {
                resultMap.put("message", SyncMap.get("errmsg"));
                resultMap.put("success", false);
                resultMap.put("code", "500");
                return resultMap;
            }
            personId = String.valueOf(SyncMap.get("personId"));
            String grpNo = String.valueOf(SyncMap.get("grpNo"));
            String perNo = String.valueOf(SyncMap.get("perNo"));
            String ensureCode = String.valueOf(SyncMap.get("ensureCode"));
            /***************************************end*******************************************/
            fPInsureEflexPlanOptionalMapper.deletefPInsureEflexPlanOptional(personId, perNo, ensureCode);
            fPInsureEflexPlanMapper.deletefPInsureEflexPlan(personId, perNo, ensureCode);
            List<Map<String, Object>> totleList = (List<Map<String, Object>>) paramMap.get("RiskList");
            List<FPInsureEflexPlan> fPInsureEflexPlanList = new ArrayList<FPInsureEflexPlan>();
            for (Map<String, Object> map : totleList) {
                //必选保额档次
                String insureElfexPlanNo = maxNoService.createMaxNo("InsureElfexPlanNo", "", 20);
                FPInsureEflexPlan fpInsureEflexPlan = new FPInsureEflexPlan();
                fpInsureEflexPlan.setPerNo(perNo);
                fpInsureEflexPlan.setPersonId(personId);
                fpInsureEflexPlan.setEnsureCode(ensureCode);
                fpInsureEflexPlan.setAmountGrageCode(map.get("AmountGrageCode").toString());
                fpInsureEflexPlan.setDeductible((map.get("Deductible") == null || "".equals(map.get("Deductible"))) ? null : Double.valueOf(map.get("Deductible").toString().replaceAll(",", "")));
                fpInsureEflexPlan.setCompensationRatio((map.get("CompensationRatio") == null || "".equals(map.get("CompensationRatio"))) ? null : Double.valueOf(map.get("CompensationRatio").toString()));
                fpInsureEflexPlan.setInsureElfexPlanNo(insureElfexPlanNo);
                fpInsureEflexPlan.setInsureState("0");//0-未提交到订单表
                fpInsureEflexPlan.setRiskCode(map.get("RiskCode").toString());
                fpInsureEflexPlan.setRiskType(map.get("RiskType").toString());
                Object object = map.get("AmountGrageCodeList");
                List<Map<String, String>> annualTimeDeductionList = (List<Map<String, String>>) object;
                fpInsureEflexPlan.setDeductibleAttr((annualTimeDeductionList.get(0).get("AnnualTimeDeduction") == null || "".equals(annualTimeDeductionList.get(0).get("AnnualTimeDeduction"))) ? "" : annualTimeDeductionList.get(0).get("AnnualTimeDeduction"));
                fpInsureEflexPlan.setAppntYear(DateTimeUtil.getCurrentYear());
                fpInsureEflexPlan.setPrem(Double.valueOf(map.get("Prem").toString()));
                fpInsureEflexPlan.setOperator(globalInput.getCustomNo());
                fpInsureEflexPlan.setOperatorCom("");
                fpInsureEflexPlan = (FPInsureEflexPlan) CommonUtil.initObject(fpInsureEflexPlan, "INSERT");
                fPInsureEflexPlanList.add(fpInsureEflexPlan);
                //可选保额档次
                Object amObj = map.get("AmountGrageCodeList");
                if (amObj != null) {
                    List<Map<String, Object>> amountGrageCodeList = (List<Map<String, Object>>) amObj;
                    if (amountGrageCodeList != null && amountGrageCodeList.size() > 0) {
                        Map<String, Object> amountGrageCodeMap = amountGrageCodeList.get(0);
                        Object optObj = amountGrageCodeMap.get("optDutyCode");
                        if (optObj != null) {
                            List<Map<String, Object>> fPInsureEflexPlanOptionalList = (List<Map<String, Object>>) optObj;
                            if (fPInsureEflexPlanOptionalList != null && fPInsureEflexPlanOptionalList.size() > 0) {
                                List<FPInsureEflexPlanOptional> list = new ArrayList<FPInsureEflexPlanOptional>();
                                fPInsureEflexPlanOptionalList.forEach((Map<String, Object> optMap) -> {
                                    FPInsureEflexPlanOptional fPInsureEflexPlanOptional = new FPInsureEflexPlanOptional();
                                    fPInsureEflexPlanOptional.setOptDutyCode(optMap.get("OptDutyCode").toString());
                                    fPInsureEflexPlanOptional.setPrem((optMap.get("Prem") == null || "".equals(optMap.get("Prem"))) ? 0.0 : Double.valueOf(optMap.get("Prem").toString().replaceAll(",", "")));
                                    fPInsureEflexPlanOptional.setInsureElfexPlanNo(insureElfexPlanNo);
                                    fPInsureEflexPlanOptional.setAmountGrageCode(map.get("AmountGrageCode").toString());
                                    fPInsureEflexPlanOptional.setInsureState("0");
                                    fPInsureEflexPlanOptional.setOperator(globalInput.getCustomNo());
                                    fPInsureEflexPlanOptional.setOperatorCom("");
                                    fPInsureEflexPlanOptional = (FPInsureEflexPlanOptional) CommonUtil.initObject(fPInsureEflexPlanOptional, "INSERT");
                                    list.add(fPInsureEflexPlanOptional);
                                });
                                fPInsureEflexPlanOptionalMapper.insertfPInsureEflexPlanOptional(list);
                            }
                        }
                    }
                }
            }
            fPInsureEflexPlanMapper.insertfPInsureEflexPlan(fPInsureEflexPlanList);
            resultMap.put("code", "200");
            resultMap.put("isSuccessSave", true);
            resultMap.put("success", true);
            resultMap.put("message", "投保暂存成功");
        } catch (Exception e) {
            log.info("判断失败", e);
            resultMap.put("code", "500");
            resultMap.put("isSuccessSave", false);
            resultMap.put("success", false);
            resultMap.put("message", "投保暂存失败");
        }
        return resultMap;
    }

    public Map<String, Object> checkRuleInsureSave(String token, Map<String, Object> paramMap) {
        String personId = paramMap.get("personId").toString();
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            /****************************当前员工不存在当前的家属则同步信息************************/
            Map<String, Object> SyncMap = SynchronizationFamilyinfo(token, personId);
            if (!SyncMap.get("success").equals(true)) {
                resultMap.put("message", SyncMap.get("errmsg"));
                resultMap.put("success", false);
                resultMap.put("code", "500");
                return resultMap;
            }
            personId = String.valueOf(SyncMap.get("personId"));
            String grpNo = String.valueOf(SyncMap.get("grpNo"));
            String perNo = String.valueOf(SyncMap.get("perNo"));
            String ensureCode = String.valueOf(SyncMap.get("ensureCode"));
            /***************************************end*******************************************/
            fPInsureEflexPlanOptionalMapper.deletefPfPEflexCheckRuleEflexPlanOptional(personId, perNo, ensureCode);
            fPInsureEflexPlanMapper.deletefPEflexCheckRuleEflexPlan(personId, perNo, ensureCode);
            List<Map<String, Object>> totleList = (List<Map<String, Object>>) paramMap.get("RiskList");
            List<FPInsureEflexPlan> fPInsureEflexPlanList = new ArrayList<FPInsureEflexPlan>();
            for (Map<String, Object> map : totleList) {
                //必选保额档次
                String insureElfexPlanNo = maxNoService.createMaxNo("CheckRuleElfexPlanNo", "", 20);
                FPInsureEflexPlan fpInsureEflexPlan = new FPInsureEflexPlan();
                fpInsureEflexPlan.setPerNo(perNo);
                fpInsureEflexPlan.setPersonId(personId);
                fpInsureEflexPlan.setEnsureCode(ensureCode);
                fpInsureEflexPlan.setAmountGrageCode(map.get("AmountGrageCode").toString());
                fpInsureEflexPlan.setDeductible((map.get("Deductible") == null || "".equals(map.get("Deductible"))) ? null : Double.valueOf(map.get("Deductible").toString().replaceAll(",", "")));
                fpInsureEflexPlan.setCompensationRatio((map.get("CompensationRatio") == null || "".equals(map.get("CompensationRatio"))) ? null : Double.valueOf(map.get("CompensationRatio").toString()));
                fpInsureEflexPlan.setInsureElfexPlanNo(insureElfexPlanNo);
                fpInsureEflexPlan.setInsureState("0");//0-未提交到订单表
                fpInsureEflexPlan.setRiskCode(map.get("RiskCode").toString());
                fpInsureEflexPlan.setRiskType(map.get("RiskType").toString());
                Object object = map.get("AmountGrageCodeList");
                List<Map<String, String>> annualTimeDeductionList = (List<Map<String, String>>) object;
                fpInsureEflexPlan.setDeductibleAttr((annualTimeDeductionList.get(0).get("AnnualTimeDeduction") == null || "".equals(annualTimeDeductionList.get(0).get("AnnualTimeDeduction"))) ? "" : annualTimeDeductionList.get(0).get("AnnualTimeDeduction"));
                fpInsureEflexPlan.setAppntYear(DateTimeUtil.getCurrentYear());
                fpInsureEflexPlan.setPrem(Double.valueOf(map.get("Prem").toString()));
                fpInsureEflexPlan.setOperator(globalInput.getCustomNo());
                fpInsureEflexPlan.setOperatorCom("");
                fpInsureEflexPlan = (FPInsureEflexPlan) CommonUtil.initObject(fpInsureEflexPlan, "INSERT");
                fPInsureEflexPlanList.add(fpInsureEflexPlan);
                //可选保额档次
                Object amObj = map.get("AmountGrageCodeList");
                if (amObj != null) {
                    List<Map<String, Object>> amountGrageCodeList = (List<Map<String, Object>>) amObj;
                    if (amountGrageCodeList != null && amountGrageCodeList.size() > 0) {
                        Map<String, Object> amountGrageCodeMap = amountGrageCodeList.get(0);
                        Object optObj = amountGrageCodeMap.get("optDutyCode");
                        if (optObj != null) {
                            List<Map<String, Object>> fPInsureEflexPlanOptionalList = (List<Map<String, Object>>) optObj;
                            if (fPInsureEflexPlanOptionalList != null && fPInsureEflexPlanOptionalList.size() > 0) {
                                List<FPInsureEflexPlanOptional> list = new ArrayList<FPInsureEflexPlanOptional>();
                                fPInsureEflexPlanOptionalList.forEach((Map<String, Object> optMap) -> {
                                    FPInsureEflexPlanOptional fPInsureEflexPlanOptional = new FPInsureEflexPlanOptional();
                                    fPInsureEflexPlanOptional.setOptDutyCode(optMap.get("OptDutyCode").toString());
                                    fPInsureEflexPlanOptional.setPrem((optMap.get("Prem") == null || "".equals(optMap.get("Prem"))) ? 0.0 : Double.valueOf(optMap.get("Prem").toString().replaceAll(",", "")));
                                    fPInsureEflexPlanOptional.setInsureElfexPlanNo(insureElfexPlanNo);
                                    fPInsureEflexPlanOptional.setAmountGrageCode(map.get("AmountGrageCode").toString());
                                    fPInsureEflexPlanOptional.setInsureState("0");
                                    fPInsureEflexPlanOptional.setOperator(globalInput.getCustomNo());
                                    fPInsureEflexPlanOptional.setOperatorCom("");
                                    fPInsureEflexPlanOptional = (FPInsureEflexPlanOptional) CommonUtil.initObject(fPInsureEflexPlanOptional, "INSERT");
                                    list.add(fPInsureEflexPlanOptional);
                                });
                                fPInsureEflexPlanOptionalMapper.insertfPfPEflexCheckRuleEflexPlanOptional(list);
                            }
                        }
                    }
                }
            }
            fPInsureEflexPlanMapper.insertfPEflexCheckRuleEflexPlan(fPInsureEflexPlanList);
            resultMap.put("code", "200");
            resultMap.put("isSuccessSave", true);
            resultMap.put("success", true);
            resultMap.put("message", "投保暂存成功");
        } catch (Exception e) {
            log.info("判断失败", e);
            resultMap.put("code", "500");
            resultMap.put("isSuccessSave", false);
            resultMap.put("success", false);
            resultMap.put("message", "投保暂存失败");
        }
        return resultMap;
    }

    @Transactional
    public String eflexPersonInsureConfirm(String token, String orderSource) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        GlobalInput globalInput = userService.getSession(token);
        String payType = "";
        try {
            /************************************* 准备数据 *********************************************/
            String perNo = globalInput.getCustomNo();
            String ensureCode = globalInput.getEnsureCode();
            String grpNo = globalInput.getGrpNo();
            Map<String, Object> fpMap = new HashMap<>();
            fpMap.put("perNo", perNo);
            fpMap.put("ensureCode", ensureCode);
            // 获取福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(globalInput.getEnsureCode());
            /***************************** 确认所有被保人所投计划是否符合投保规则***********************************/
            //弹性计划-获取员工下所有被保人的personid
            List<Map<String, String>> getPersonIdList = fPInsureEflexPlanMapper.getPersonIdList(ensureCode, perNo);
            if (getPersonIdList == null || getPersonIdList.size() < 1) {// 没有被保人
                FCPerInfo fCPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
                log.info("系统异常，没有被保人");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", fCPerInfo.getName() + "未投保，请投保");
                return JSON.toJSONString(resultMap);
            } else {
                for (Map<String, String> map : getPersonIdList) {
                    String personId = map.get("personId");
                    String personName = map.get("personName");
                    Map<String, Object> checkTBRules = checkEflexTBRules(token, personId);
                    String code = checkTBRules.get("code").toString();
                    if (!code.equals("200")) {//投保规则校验未通过
                        resultMap.put("code", checkTBRules.get("code").toString());
                        resultMap.put("message", "被保人" + personName + "，" + checkTBRules.get("message"));
                        return JSON.toJSONString(resultMap);
                    }
                }
            }
            //获取支付方式
            List<Map<String, String>> payTypeList = fcGrpOrderMapper.getPayType(grpNo, ensureCode);
            if (payTypeList != null && payTypeList.size() > 0) {
                Map<String, String> payTypeMap = payTypeList.get(0);
                if (payTypeMap != null) {
                    payType = payTypeMap.get("configValue");
                } else {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "订单提交失败,获取支付方式失败");
                    return JSON.toJSONString(resultMap);
                }
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "订单提交失败,获取支付方式失败");
                return JSON.toJSONString(resultMap);
            }
            //获取代理人信息
            List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
            FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);
            // 根据福利编号获取团体保单信息
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(fcEnsure.getEnsureCode());
            //获取员工信息
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo());
            //判断订单是否存在
            List<FCOrder> fcOrderList = fcOrderMapper.selectOrder(perNo, ensureCode);
            if (fcOrderList.size() > 0) {
                for (FCOrder fcOrder : fcOrderList) {
                    String orderNo = fcOrder.getOrderNo();
                    //删除保额档次正式表
                    fPInsureEflexPlanMapper.deleteAllFcInsureEflexPlan(orderNo);
                    fPInsureEflexPlanMapper.deleteAllFcInsureEflexPlanOptional(orderNo);
                    //删除子订单详情表
                    fcOrderItemDetailMapper.deleteByOrderNo(orderNo);
                    //删除子订单表
                    fcOrderItemMapper.deleteByOrderNo(orderNo);
                    //删除被保人表
                    fcOrderInsuredMapper.deleteByOrderNo(orderNo);
                    //第二步，更新订单信息
                    updateOrderInfo(fcPerInfo, globalInput, fcGrpOrder, fcEnsure, payType, fpMap, fdAgentInfo, orderNo, orderSource);
                    //第三步，插入子订单信息
                    insertOrderItemInfo(fcPerInfo, globalInput, fcGrpOrder, fcEnsure, payType, fpMap, fdAgentInfo, orderNo);
                }
            } else {
                insertOrderInfo(fcPerInfo, globalInput, fcGrpOrder, fcEnsure, payType, fpMap, fdAgentInfo, orderSource);
            }
            resultMap.put("success", true);
            resultMap.put("payType", payType);
            resultMap.put("code", "200");
            resultMap.put("message", "订单提交成功");
        } catch (Exception e) {
            log.info("订单提交失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "订单提交失败");
        }
        return JSON.toJSONString(resultMap);
    }

    @Transactional
    public void insertOrderItemInfo(FCPerInfo fcPerInfo, GlobalInput globalInput, FCGrpOrder fcGrpOrder,
                                    FCEnsure fcEnsure, String payType, Map<String, Object> fpMap, FDAgentInfo fdAgentInfo, String orderNo) {
        Map<String, Object> param = new HashMap<String, Object>();
        //查询临时表数据
        param.clear();
        param.put("perNo", globalInput.getCustomNo());
        param.put("ensureCode", globalInput.getEnsureCode());
        List<Map<String, Object>> familyPremList = fPInsureEflexPlanMapper.getTotlePrem(param);
        if (familyPremList.size() < 1) {
            log.info("系统异常，没有被保人");// 没有被保人
            throw new RuntimeException();
        }
        //获取拆分保费：个人缴费+公司缴费
        Map<String, Object> dataMap = calStaffPolicyPremDetail(globalInput.getEnsureCode(), globalInput.getCustomNo(), fcPerInfo, familyPremList);
        if (dataMap == null) {
            log.info("系统异常，缴费明细计算失败");
            throw new RuntimeException();
        }
        Map<String, Object> staffMap = (Map<String, Object>) dataMap.get("staffMap");
        List<Map<String, Object>> familyList = (List<Map<String, Object>>) dataMap.get("familyMap");
        Map<String, Map<String, Object>> familyMaps = new HashMap<String, Map<String, Object>>();
        if (familyList != null && familyList.size() > 0) {
            for (Map<String, Object> map : familyList) {
                familyMaps.put((String) map.get("personId"), map);
            }
        }
        for (Map<String, Object> map : familyPremList) {
            String personId = map.get("personId").toString();
            // 3、子订单表FCOrderItem
            FCOrderItem fcOrderItem = new FCOrderItem();
            String orderItemNo = maxNoService.createMaxNo("OrderItemNo", "", 20);
            String orderItemDetailNo = maxNoService.createMaxNo("OrderItemDetailNo", "", 20);
            fcOrderItem.setOrderItemNo(orderItemNo);
            fcOrderItem.setOrderItemDetailNo(orderItemDetailNo);
            fcOrderItem.setOrderNo(orderNo);
			/* 个人保单号生成规则：
			1、前缀99；
			2、3-4位截取保单管理机构的3、4位，如为总公司则取86（如管理机构为864498取44）；
			3、5-6位截取年份后两位（如2018取18）；
			4、7-15位取每个自然年度流水号；
			5、最后一位固定为8；
			例如弹性平台今年第一张个人保险凭证号应为：9944180000000018。
			*/
            String rule = "99" + fdAgentInfo.getManageCom().substring(2, 4) + DateTimeUtil.getCurrentYear().substring(2, 4);
            String contNo = maxNoService.createMaxNo("ContNo" + DateTimeUtil.getCurrentYear(), rule, 9);
            contNo = contNo + "8";
            fcOrderItem.setContNo(contNo);
            if (!staffMap.isEmpty()) {
                if (personId.equals(staffMap.get("personId"))) {
                    fcOrderItem.setSelfPrem((double) staffMap.get("staffPrem"));
                    fcOrderItem.setGrpPrem((double) staffMap.get("staffDefaultPrem"));
                }
            }
            if (!familyMaps.isEmpty()) {
                if (familyMaps.get(personId) != null) {
                    Map<String, Object> familyMap = familyMaps.get(personId);
                    fcOrderItem.setSelfPrem((double) familyMap.get("familyPrem"));
                    fcOrderItem.setGrpPrem((double) familyMap.get("familyDefaultPrem"));
                }
            }

            fcOrderItem.setOperator(globalInput.getUserNo());
            fcOrderItem = (FCOrderItem) CommonUtil.initObject(fcOrderItem, "INSERT");
            fcOrderItemMapper.insert(fcOrderItem);
            param.clear();
            param.put("ensureCode", globalInput.getEnsureCode());
            param.put("perNo", globalInput.getCustomNo());
            param.put("personId", personId);
            List<String> amountGrageCodeList = fPInsureEflexPlanMapper.getPersonAmountGrageCode(param);
            for (String amountGrageCode : amountGrageCodeList) {
                //子订单产品要素详情表FCOrderItemDetail
                FCOrderItemDetail fcOrderItemDetail = new FCOrderItemDetail();
                fcOrderItemDetail.setOrderItemDetailNo(orderItemDetailNo);
                fcOrderItemDetail.setProductCode(amountGrageCode);
                fcOrderItemDetail.setProductEleCode("001");
                fcOrderItemDetail.setEnsureCode(globalInput.getEnsureCode());
                fcOrderItemDetail.setOperator(globalInput.getUserNo());
                fcOrderItemDetail = (FCOrderItemDetail) CommonUtil.initObject(fcOrderItemDetail, "INSERT");
                fcOrderItemDetailMapper.insert(fcOrderItemDetail);
            }
            //投保必选档次正式表FcInsureEflexPlan
            param.clear();
            param.put("perNo", globalInput.getCustomNo());
            param.put("ensureCode", globalInput.getEnsureCode());
            param.put("personId", personId);
            param.put("orderItemDetailNo", orderItemDetailNo);
            param.put("operator", globalInput.getCustomNo());
            param.put("date", DateTimeUtil.getCurrentDate());
            param.put("time", DateTimeUtil.getCurrentTime());
            fPInsureEflexPlanMapper.saveFcInsureEflexPlan(param);
            fPInsureEflexPlanMapper.saveFcInsureEflexPlanOptional(param);
            //被保人表FCOrderInsured
            FCOrderInsured fcOrderInsured = new FCOrderInsured();
            fcOrderInsured.setOrderItemNo(orderItemNo);
            fcOrderInsured.setOrderNo(orderNo);
            fcOrderInsured.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
            FCPerson person = fcPersonMapper.selectByPrimaryKey(personId);
            fcOrderInsured.setPersonID(person.getPersonID());
            fcOrderInsured.setName(person.getName());
            fcOrderInsured.setSex(person.getSex());
            fcOrderInsured.setBirthDay(person.getBirthDate());
            fcOrderInsured.setNativeplace(person.getNativeplace());
            fcOrderInsured.setIDType(person.getIDType());
            fcOrderInsured.setIDNo(person.getIDNo());
            fcOrderInsured.setMobilePhone(person.getMobilePhone());
            fcOrderInsured.setPhone(person.getPhone());
            fcOrderInsured.setDepartment(fcPerInfo.getDepartment());
            fcOrderInsured.setOccupationType(person.getOccupationType());
            fcOrderInsured.setOccupationCode(person.getOccupationCode());
            fcOrderInsured.setJoinMedProtect(person.getJoinMedProtect());
            fcOrderInsured.setMedProtectType(person.getMedProtectType());
            fcOrderInsured.setEMail(person.getEMail());
            fcOrderInsured.setAddress(person.getAddress());
            fcOrderInsured.setOperator(globalInput.getUserNo());
            fcOrderInsured = (FCOrderInsured) CommonUtil.initObject(fcOrderInsured, "INSERT");
            fcOrderInsuredMapper.insert(fcOrderInsured);

            //修改投保状态，0-未提交订单表 1-已提交订单表 2-核心承保成功
            param.clear();
            param.put("ensureCode", globalInput.getEnsureCode());
            param.put("perNo", globalInput.getCustomNo());
            param.put("personId", personId);
            param.put("insureState", "1");
            fPInsureEflexPlanMapper.updateInsureState(param);
            fPInsureEflexPlanOptionalMapper.updateInsureState(param);
        }
    }

    public void insertOrderInfo(FCPerInfo fcPerInfo, GlobalInput globalInput, FCGrpOrder fcGrpOrder,
                                FCEnsure fcEnsure, String payType, Map<String, Object> fpMap,
                                FDAgentInfo fdAgentInfo, String orderSource) {
        Map<String, Object> param = new HashMap<String, Object>();
        //个人投保人表
        FCPerAppnt fcPerAppnt = new FCPerAppnt();
        String perAppNo = maxNoService.createMaxNo("PerAppNo", "", 20);
        fcPerAppnt.setPerAppNo(perAppNo);
        fcPerAppnt.setGrpNo(globalInput.getGrpNo());
        fcPerAppnt.setPerNo(globalInput.getCustomNo());
        fcPerAppnt.setName(fcPerInfo.getName());
        fcPerAppnt.setSex(fcPerInfo.getSex());
        fcPerAppnt.setIDType(fcPerInfo.getIDType());
        fcPerAppnt.setIDNo(fcPerInfo.getIDNo());
        fcPerAppnt.setBirthDay(fcPerInfo.getBirthDay());
        fcPerAppnt.setOperator(globalInput.getUserNo());
        fcPerAppnt = (FCPerAppnt) CommonUtil.initObject(fcPerAppnt, "INSERT");
        fcPerAppntMapper.insert(fcPerAppnt);
        //订单表
        param.clear();
        param.put("ensureCode", globalInput.getEnsureCode());
        param.put("perNo", globalInput.getCustomNo());
        param.put("personType", "1");
        param.put("isValidy", "1");
        List<FCPerRegistDay> fcPerRegistDayList = fcPerRegistDayMapper.selectFCPerRegistDayList(param);
        if (fcPerRegistDayList.size() != 1) {
            // 员工开放投保信息表数据异常，不能创建订单
            log.info("员工开放投保信息表数据异常，不能创建订单");
            throw new RuntimeException();
        }
        FCPerRegistDay fcPerRegistDay = fcPerRegistDayList.get(0);
        FCOrder fcOrder = new FCOrder();
        String orderNo = maxNoService.createMaxNo("OrderNo", DateTimeUtil.getTimeString().substring(0, 4), 8);
        fcOrder.setOrderNo(orderNo);
        fcOrder.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
        fcOrder.setOrderStatus(payType.equals("0") ? "02" : "03");//02-待支付，03-待确认投保信息
        fcOrder.setOrderType("01");
        fcOrder.setOrderSource(orderSource);
        fcOrder.setOpenDay(fcPerRegistDay.getOpenDay());
        fcOrder.setCloseDay(fcPerRegistDay.getCloseDay());
        fcOrder.setGrpNo(globalInput.getGrpNo());
        fcOrder.setPerNo(globalInput.getCustomNo());
        fcOrder.setPerAppNo(perAppNo);
        fcOrder.setCommitDate(DateTimeUtil.getCurrentDate());
        fcOrder.setClientNo(fcEnsure.getClientNo());
        fcOrder.setOperator(globalInput.getUserNo());
        fcOrder = (FCOrder) CommonUtil.initObject(fcOrder, "INSERT");
        fcOrderMapper.insert(fcOrder);
        //更新fcbatchpaybankinfo的订单号
        if (payType.equals("3")) {//缴费方式为批扣
            updateOrderNoFcBatchPayBankInfo(null, fpMap, orderNo);
        }
        //订单轨迹表FCOrderLocus
        FCOrderLocus fcOrderLocus = new FCOrderLocus();
        String orderLocusSN = maxNoService.createMaxNo("orderLocusSN", "", 20);
        fcOrderLocus.setOrderLocusSN(orderLocusSN);
        fcOrderLocus.setOrderNo(orderNo);
        fcOrderLocus.setOrderStatus(payType.equals("0") ? "02" : "03");//02-待支付，03-待确认投保信息
        fcOrderLocus.setOperator(globalInput.getUserNo());
        fcOrderLocus = (FCOrderLocus) CommonUtil.initObject(fcOrderLocus, "INSERT");
        fcOrderLocusMapper.insert(fcOrderLocus);
        //查询临时表数据
        param.clear();
        param.put("perNo", globalInput.getCustomNo());
        param.put("ensureCode", globalInput.getEnsureCode());
        List<Map<String, Object>> familyPremList = fPInsureEflexPlanMapper.getTotlePrem(param);
        if (familyPremList.size() < 1) {
            log.info("系统异常，没有被保人");// 没有被保人
            throw new RuntimeException();
        }
        //获取拆分保费：个人缴费+公司缴费
        Map<String, Object> dataMap = calStaffPolicyPremDetail(globalInput.getEnsureCode(), globalInput.getCustomNo(), fcPerInfo, familyPremList);
        if (dataMap == null) {
            log.info("系统异常，缴费明细计算失败");
            throw new RuntimeException();
        }
        Map<String, Object> staffMap = (Map<String, Object>) dataMap.get("staffMap");
        List<Map<String, Object>> familyList = (List<Map<String, Object>>) dataMap.get("familyMap");
        Map<String, Map<String, Object>> familyMaps = new HashMap<String, Map<String, Object>>();
        if (familyList != null && familyList.size() > 0) {
            for (Map<String, Object> map : familyList) {
                familyMaps.put((String) map.get("personId"), map);
            }
        }
        for (Map<String, Object> map : familyPremList) {
            String personId = map.get("personId").toString();
            // 3、子订单表FCOrderItem
            FCOrderItem fcOrderItem = new FCOrderItem();
            String orderItemNo = maxNoService.createMaxNo("OrderItemNo", "", 20);
            String orderItemDetailNo = maxNoService.createMaxNo("OrderItemDetailNo", "", 20);
            fcOrderItem.setOrderItemNo(orderItemNo);
            fcOrderItem.setOrderItemDetailNo(orderItemDetailNo);
            fcOrderItem.setOrderNo(orderNo);
			/* 个人保单号生成规则：
			1、前缀99；
			2、3-4位截取保单管理机构的3、4位，如为总公司则取86（如管理机构为864498取44）；
			3、5-6位截取年份后两位（如2018取18）；
			4、7-15位取每个自然年度流水号；
			5、最后一位固定为8；
			例如弹性平台今年第一张个人保险凭证号应为：9944180000000018。
			 */
            String rule = "99" + fdAgentInfo.getManageCom().substring(2, 4) + DateTimeUtil.getCurrentYear().substring(2, 4);
            String contNo = maxNoService.createMaxNo("ContNo" + DateTimeUtil.getCurrentYear(), rule, 9);
            contNo = contNo + "8";
            fcOrderItem.setContNo(contNo);
            if (!staffMap.isEmpty()) {
                if (personId.equals(staffMap.get("personId"))) {
                    fcOrderItem.setSelfPrem((double) staffMap.get("staffPrem"));
                    fcOrderItem.setGrpPrem((double) staffMap.get("staffDefaultPrem"));
                }
            }
            if (!familyMaps.isEmpty()) {
                if (familyMaps.get(personId) != null) {
                    Map<String, Object> familyMap = familyMaps.get(personId);
                    fcOrderItem.setSelfPrem((double) familyMap.get("familyPrem"));
                    fcOrderItem.setGrpPrem((double) familyMap.get("familyDefaultPrem"));
                }
            }

            fcOrderItem.setOperator(globalInput.getUserNo());
            fcOrderItem = (FCOrderItem) CommonUtil.initObject(fcOrderItem, "INSERT");
            fcOrderItemMapper.insert(fcOrderItem);
            param.clear();
            param.put("ensureCode", globalInput.getEnsureCode());
            param.put("perNo", globalInput.getCustomNo());
            param.put("personId", personId);
            List<String> amountGrageCodeList = fPInsureEflexPlanMapper.getPersonAmountGrageCode(param);
            for (String amountGrageCode : amountGrageCodeList) {
                //子订单产品要素详情表FCOrderItemDetail
                FCOrderItemDetail fcOrderItemDetail = new FCOrderItemDetail();
                fcOrderItemDetail.setOrderItemDetailNo(orderItemDetailNo);
                fcOrderItemDetail.setProductCode(amountGrageCode);
                fcOrderItemDetail.setProductEleCode("001");
                fcOrderItemDetail.setEnsureCode(globalInput.getEnsureCode());
                fcOrderItemDetail.setOperator(globalInput.getUserNo());
                fcOrderItemDetail = (FCOrderItemDetail) CommonUtil.initObject(fcOrderItemDetail, "INSERT");
                fcOrderItemDetailMapper.insert(fcOrderItemDetail);
            }
            //投保必选档次正式表FcInsureEflexPlan
            param.clear();
            param.put("perNo", globalInput.getCustomNo());
            param.put("ensureCode", globalInput.getEnsureCode());
            param.put("personId", personId);
            param.put("orderItemDetailNo", orderItemDetailNo);
            param.put("operator", globalInput.getCustomNo());
            param.put("date", DateTimeUtil.getCurrentDate());
            param.put("time", DateTimeUtil.getCurrentTime());
            fPInsureEflexPlanMapper.saveFcInsureEflexPlan(param);
            fPInsureEflexPlanMapper.saveFcInsureEflexPlanOptional(param);
            //被保人表FCOrderInsured
            FCOrderInsured fcOrderInsured = new FCOrderInsured();
            fcOrderInsured.setOrderItemNo(orderItemNo);
            fcOrderInsured.setOrderNo(orderNo);
            fcOrderInsured.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
            FCPerson person = fcPersonMapper.selectByPrimaryKey(personId);
            fcOrderInsured.setPersonID(person.getPersonID());
            fcOrderInsured.setNativeplace(person.getNativeplace());
            fcOrderInsured.setName(person.getName());
            fcOrderInsured.setSex(person.getSex());
            fcOrderInsured.setBirthDay(person.getBirthDate());
            fcOrderInsured.setIDType(person.getIDType());
            fcOrderInsured.setIDNo(person.getIDNo());
            fcOrderInsured.setMobilePhone(person.getMobilePhone());
            fcOrderInsured.setPhone(person.getPhone());
            fcOrderInsured.setDepartment(fcPerInfo.getDepartment());
            fcOrderInsured.setOccupationType(person.getOccupationType());
            fcOrderInsured.setOccupationCode(person.getOccupationCode());
            fcOrderInsured.setJoinMedProtect(person.getJoinMedProtect());
            fcOrderInsured.setMedProtectType(person.getMedProtectType());
            fcOrderInsured.setEMail(person.getEMail());
            fcOrderInsured.setAddress(person.getAddress());
            fcOrderInsured.setOperator(globalInput.getUserNo());
            fcOrderInsured = (FCOrderInsured) CommonUtil.initObject(fcOrderInsured, "INSERT");
            fcOrderInsuredMapper.insert(fcOrderInsured);

            //修改投保状态，0-未提交订单表 1-已提交订单表 2-核心承保成功
            param.clear();
            param.put("ensureCode", globalInput.getEnsureCode());
            param.put("perNo", globalInput.getCustomNo());
            param.put("personId", personId);
            param.put("insureState", "1");
            fPInsureEflexPlanMapper.updateInsureState(param);
            fPInsureEflexPlanOptionalMapper.updateInsureState(param);
        }
    }

    public void updateOrderInfo(FCPerInfo fcPerInfo, GlobalInput globalInput, FCGrpOrder fcGrpOrder,
                                FCEnsure fcEnsure, String payType, Map<String, Object> fpMap,
                                FDAgentInfo fdAgentInfo, String orderNo, String orderSource) {
        Map<String, Object> param = new HashMap<String, Object>();
        //订单表
        param.clear();
        param.put("ensureCode", globalInput.getEnsureCode());
        param.put("perNo", globalInput.getCustomNo());
        param.put("personType", "1");
        param.put("isValidy", "1");
        List<FCPerRegistDay> fcPerRegistDayList = fcPerRegistDayMapper.selectFCPerRegistDayList(param);
        if (fcPerRegistDayList.size() != 1) {
            // 员工开放投保信息表数据异常，不能创建订单
            log.info("员工开放投保信息表数据异常，不能创建订单");
            throw new RuntimeException();
        }
        FCPerRegistDay fcPerRegistDay = fcPerRegistDayList.get(0);
        FCOrder fcOrder = new FCOrder();
        fcOrder.setOrderNo(orderNo);
        fcOrder.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
        fcOrder.setOrderStatus(payType.equals("0") ? "02" : "03");//02-待支付，03-待确认投保信息
        fcOrder.setOrderType("01");
        fcOrder.setOrderSource(orderSource);
        fcOrder.setOpenDay(fcPerRegistDay.getOpenDay());
        fcOrder.setCloseDay(fcPerRegistDay.getCloseDay());
        fcOrder.setGrpNo(globalInput.getGrpNo());
        fcOrder.setPerNo(globalInput.getCustomNo());
        fcOrder.setCommitDate(DateTimeUtil.getCurrentDate());
        fcOrder.setClientNo(fcEnsure.getClientNo());
        fcOrder.setOperator(globalInput.getUserNo());
        fcOrder = (FCOrder) CommonUtil.initObject(fcOrder, "UPDATE");
        fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
        //更新fcbatchpaybankinfo的订单号
        if (payType.equals("3")) {//缴费方式为批扣
            updateOrderNoFcBatchPayBankInfo(null, fpMap, orderNo);
        }
        //订单轨迹表FCOrderLocus
        FCOrderLocus fcOrderLocus = new FCOrderLocus();
        String orderLocusSN = maxNoService.createMaxNo("orderLocusSN", "", 20);
        fcOrderLocus.setOrderLocusSN(orderLocusSN);
        fcOrderLocus.setOrderNo(orderNo);
        fcOrderLocus.setOrderStatus(payType.equals("0") ? "02" : "03");//02-待支付，03-待确认投保信息
        fcOrderLocus.setOperator(globalInput.getUserNo());
        fcOrderLocus = (FCOrderLocus) CommonUtil.initObject(fcOrderLocus, "INSERT");
        fcOrderLocusMapper.insert(fcOrderLocus);
    }

    public Map<String, Object> calStaffPolicyPremDetail(String ensureCode, String perNo, FCPerInfo fcPerInfo, List<Map<String, Object>> familyPremList) {
        // 查询条件
        Map<String, Object> params = new HashMap<String, Object>();
        // 结果数据容器
        Map<String, Object> dataMap = new HashMap<String, Object>();
        // 员工数据容器
        Map<String, Object> staffMap = new HashMap<String, Object>();
        // 员工及家属合计数据容器
        Map<String, Object> totalMap = new HashMap<String, Object>();
        // 家属数据容器
        List<Map<String, Object>> familyList = new ArrayList<Map<String, Object>>();
        String payType = "";
        try {
            //获取支付方式
            Map<String, Object> ensureMap = new HashMap<>();
            ensureMap.put("ensureCode", ensureCode);
            ensureMap.put("configNo", "008");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(ensureMap);
            if (fcEnsureConfig != null) {
                payType = fcEnsureConfig.getConfigValue();
            }
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
            // 获取员工的personId
            String staffPersonId = "";
            if (fcStaffFamilyRela != null) staffPersonId = fcStaffFamilyRela.getPersonID();
            // 获取员工注册期表数据
            params.clear();
            params.put("ensureCode", ensureCode);
            params.put("perNo", perNo);
            FCPerRegistDay fcPerRegistDay = fcPerRegistDayMapper.selectFCPerRegistDayByKey(params);
            //获取家属的公司补助总保费
            double familyGrpTotalePrem = 0.0;
            //家属一共需要的自付保费
            double familyTotleSelfPrem = 0.0;
            //员工个人缴费
            double staffSelfPrem = 0.00;
            familyGrpTotalePrem = fcPerRegistDay.getFamilyGrpPrem() == null ? 0.0 : fcPerRegistDay.getFamilyGrpPrem();
            //员工家属总保费、员工家属企业总保费、员工家属自付总保费
            double totleStaffAndFamPrem = 0.0, totleGrpPrem = 0.0, totleSelfPrem = 0.0;
            if (familyPremList != null && familyPremList.size() > 0) {
                for (Map<String, Object> map : familyPremList) {
                    String personId = map.get("personId").toString();
                    Double totlePrem = (Double) map.get("prem");
                    FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
                    if (StringUtils.isNotBlank(staffPersonId)) {
                        if (staffPersonId.equals(personId)) {
                            //员工企业缴费
                            double staffGrpPrem = 0.0;
                            if (payType.equals("1")) {//企业缴纳
                                staffGrpPrem = totlePrem;
                            } else {
                                if (fcPerRegistDay.getStaffGrpPrem() != null)
                                    staffGrpPrem = fcPerRegistDay.getStaffGrpPrem();
                                if (totlePrem > staffGrpPrem) {
                                    staffSelfPrem = CommonUtil.sub(totlePrem, staffGrpPrem);
                                } else {
                                    staffGrpPrem = totlePrem;
                                }
                            }
                            staffMap.put("personId", staffPersonId);
                            staffMap.put("name", fcPerson.getName());
                            staffMap.put("staffInsurePrem", totlePrem);//员工投保险种总保费
                            staffMap.put("staffDefaultPrem", staffGrpPrem);//员工企业缴费
                            staffMap.put("staffPrem", staffSelfPrem);//员工个人缴费
                            totleStaffAndFamPrem = CommonUtil.add(totleStaffAndFamPrem, totlePrem);
                            totleGrpPrem = CommonUtil.add(totleGrpPrem, staffGrpPrem);
                            totleSelfPrem = CommonUtil.add(totleSelfPrem, staffSelfPrem);
                            dataMap.put("staffMap", staffMap);
                        } else {
                            //家属企业缴费
                            double familyGrpPrem = 0.0;
                            //家属个人缴费
                            double familySelfPrem = 0.00;
                            if (payType.equals("1")) {//企业缴纳
                                familyGrpPrem = totlePrem;
                            } else {
                                if (totlePrem > familyGrpTotalePrem) {
                                    familyGrpPrem = familyGrpTotalePrem;
                                    familySelfPrem = CommonUtil.sub(totlePrem, familyGrpTotalePrem);
                                    familyGrpTotalePrem = 0.0;
                                } else if (totlePrem <= familyGrpTotalePrem) {
                                    familyGrpPrem = totlePrem;
                                    familyGrpTotalePrem = CommonUtil.sub(familyGrpTotalePrem, totlePrem);
                                }
                            }
                            familyTotleSelfPrem += familySelfPrem;
                            // 封装单个家属数据
                            Map<String, Object> familyPremMap = new HashMap<>();
                            familyPremMap.put("name", fcPerson.getName());
                            familyPremMap.put("personId", personId);
                            familyPremMap.put("familyInsurePrem", totlePrem);//家属投保险种总保费
                            familyPremMap.put("familyDefaultPrem", familyGrpPrem);//家属企业缴费
                            familyPremMap.put("familyPrem", familySelfPrem);//家属个人缴费
                            totleStaffAndFamPrem = CommonUtil.add(totleStaffAndFamPrem, totlePrem);
                            totleGrpPrem = CommonUtil.add(totleGrpPrem, familyGrpPrem);
                            totleSelfPrem = CommonUtil.add(totleSelfPrem, familySelfPrem);
                            familyList.add(familyPremMap);
                        }
                    }
                }
            }
            // 封装返回数据
            dataMap.put("staffMap", staffMap);
            totalMap.put("totleStaffAndFamPrem", totleStaffAndFamPrem);
            totalMap.put("totleGrpPrem", totleGrpPrem);
            totalMap.put("totleSelfPrem", totleSelfPrem);
            dataMap.put("totalMap", totalMap);
            if (familyList.size() > 0) {
                dataMap.put("familyMap", familyList);
            }
            //校验是否需要显示员工付款信息
            if (fcEnsureConfig != null) {
                if ("3".equals(fcEnsureConfig.getConfigValue())) {
                    //获取未投保家属默认档次总保费
                    Double prem = getNoInsuredFamilyPrem(ensureCode, perNo);
                    if (familyTotleSelfPrem + staffSelfPrem > 0) {
                        dataMap.put("isCheck", "1");
                        dataMap.put("perInfo", fcPerInfo);
                    }
                    Double familyGrpPrem = fcPerRegistDay.getFamilyGrpPrem();
                    familyGrpPrem = familyGrpPrem == null ? 0.0 : familyGrpPrem;
                    if (prem > (familyGrpPrem - familyTotleSelfPrem)) {
                        dataMap.put("isCheck", "1");
                        dataMap.put("perInfo", fcPerInfo);
                    }
                } else {
                    dataMap.put("isCheck", "0");
                    dataMap.put("perInfo", fcPerInfo);
                }
            } else {
                dataMap.put("isCheck", "0");
                dataMap.put("perInfo", fcPerInfo);
            }
            if (familyTotleSelfPrem + staffSelfPrem > 0) {
                dataMap.put("isSelfPay", "1");//需要支付
            } else {
                dataMap.put("isSelfPay", "0");//不需要支付
            }
        } catch (Exception e) {
            log.info("计算缴费明细失败：", e);
            return null;
        }
        return dataMap;
    }

    public Double getNoInsuredFamilyPrem(String ensureCode, String perNo) {
        Double totalPrem = 0.0;
        //获取未投保的家属信息
        List<Map<String, Object>> noInsuredFamilyInfoList = fPInsureEflexPlanMapper.getNoInsuredFamilyInfo(ensureCode, perNo);
        if (noInsuredFamilyInfoList != null && noInsuredFamilyInfoList.size() > 0) {
            for (Map<String, Object> map : noInsuredFamilyInfoList) {
                List<Map<String, Object>> generateRequestList = premTrailService.generateRequest(map);
                for (Map<String, Object> map2 : generateRequestList) {
                    Map<String, Object> resultmap = premTrailService.premTrail(map2);
                    if (Boolean.valueOf(resultmap.get("success").toString())) {
                        if (map2.get("RiskCode").toString().equals("17020")) {
                            Map<String, Object> premMap = (Map<String, Object>) resultmap.get("Prem");
                            totalPrem += Double.valueOf(premMap.get("Prem").toString());
                        } else {
                            totalPrem += Double.valueOf(resultmap.get("Prem").toString());
                        }
                    }
                }
            }
        }
        return totalPrem;
    }

    public String selfOrderInfo(String token) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String payType = "";
        try {
            GlobalInput globalInput = userService.getSession(token);
            String grpNo = globalInput.getGrpNo();
            String ensureCode = globalInput.getEnsureCode();
            String perNo = globalInput.getCustomNo();
            //获取支付方式
            List<Map<String, String>> payTypeList = fcGrpOrderMapper.getPayType(grpNo, ensureCode);
            if (payTypeList != null && payTypeList.size() > 0) {
                Map<String, String> payTypeMap = payTypeList.get(0);
                if (payTypeMap != null) {
                    payType = payTypeMap.get("configValue");
                } else {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "获取支付方式失败");
                    return JSON.toJSONString(resultMap);
                }
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "获取支付方式失败");
                return JSON.toJSONString(resultMap);
            }
            // 获取订单信息
            List<Map<String, Object>> dataMap = fcOrderMapper.getSelfOrderInfo(perNo, ensureCode);
            if (dataMap != null) {
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("payType", payType);
                resultMap.put("message", "我的订单信息查询成功");
                resultMap.put("data", dataMap.get(0));
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "我的订单信息查询失败");
            }
        } catch (Exception e) {
            log.info("我的订单信息查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "我的订单信息查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    public String selfOrderDetailInfo(String token) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            // 获取订单信息
            String perNo = globalInput.getCustomNo();
            String ensureCode = globalInput.getEnsureCode();
            List<Map<String, Object>> empDataMap = fcOrderMapper.selfEmpOrderInfo(perNo, ensureCode);
            List<Map<String, Object>> famDataMap = fcOrderMapper.selfFamilyOrderInfo(perNo, ensureCode);
            if (empDataMap != null) {
                Map<String, Object> dataMap = new HashMap<String, Object>();
                dataMap.put("empDataMap", empDataMap.get(0));
                if (famDataMap != null && famDataMap.size() > 0) {
                    dataMap.put("famDataMap", famDataMap);
                } else {
                    dataMap.put("famDataMap", new ArrayList<String>());
                }
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "我的订单信息查询成功");
                resultMap.put("data", dataMap);
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "我的订单信息查询失败");
            }
        } catch (Exception e) {
            log.info("我的订单信息查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "我的订单信息查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    public String selfOrderDetailDesInfo(String token, String personId) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            // 获取订单信息
            String perNo = globalInput.getCustomNo();
            String ensureCode = globalInput.getEnsureCode();
            List<Map<String, Object>> dataMap = fcOrderMapper.selfOrderDetailDesInfo(perNo, ensureCode, personId);
            if (dataMap != null) {
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "我的订单信息查询成功");
                resultMap.put("data", dataMap);
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "我的订单信息查询失败");
            }
        } catch (Exception e) {
            log.info("我的订单信息查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "我的订单信息查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    public String selectSelfOrderDetailInfo(String token, SelectSelfOrderDetailInfoReq selectSelfOrderDetailInfoReq) {
        // 定义返回结果
        Map<String, Object> resultMap = new HashMap<>();
        // 人员ID
        String personId = selectSelfOrderDetailInfoReq.getPersonId();
        if (StringUtils.isBlank(personId)) {
            throw new SystemException("人员ID不能为空！");
        }
        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);
        // 获取订单信息
        String perNo = globalInput.getCustomNo();
        String ensureCode = globalInput.getEnsureCode();
        List<Map<String, Object>> dataMap = fcOrderMapper.selfOrderDetailDesInfo(perNo, ensureCode, personId);
        if (dataMap != null) {
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "我的订单信息查询成功");
            resultMap.put("data", dataMap);
        } else {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "我的订单信息查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 订单确认
     *
     * @param token
     * @param orderNo
     * @param verifyCode
     * @param isCheck
     * @return
     */
    public String selfOrderDetailConfirm(String token, String orderNo, String verifyCode, String isCheck) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String payType = "";
        try {
            GlobalInput globalInput = userService.getSession(token);
            String grpNo = globalInput.getGrpNo();
            String ensureCode = globalInput.getEnsureCode();
            String perNo = globalInput.getCustomNo();
            //获取支付方式
            List<Map<String, String>> payTypeList = fcGrpOrderMapper.getPayType(grpNo, ensureCode);
            if (payTypeList != null && payTypeList.size() > 0) {
                Map<String, String> payTypeMap = payTypeList.get(0);
                if (payTypeMap != null) {
                    payType = payTypeMap.get("configValue");
                } else {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "订单提交失败,获取支付方式失败");
                    return JSON.toJSONString(resultMap);
                }
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "订单提交失败,获取支付方式失败");
                return JSON.toJSONString(resultMap);
            }
            if (payType.equals("1") || payType.equals("2")) {//1-企业缴纳，2-企业代扣代缴时，订单状态置为待生效
                updateFcorder("08", orderNo);
            } else if (payType.equals("0")) {//0-实时支付
                updateFcorder("08", orderNo);
            } else if (payType.equals("3")) {//3-个人批扣
                //判断是否需要提供付款信息
                if (StringUtils.isNotBlank(isCheck) && isCheck.equals("1")) {
                    if (verifyCode == null || verifyCode.equals("")) {
                        resultMap.put("code", "500");
                        resultMap.put("message", "请输入短信验证码");
                        return JSON.toJSONString(resultMap);
                    } else {
                        // 调用签约确认接口
                        bankSignService.signConfirm(new SignConfirmRequest(perNo, ensureCode, orderNo, verifyCode));
                    }
                }
                // 更新团体订单的信息
                updateFcorder("08", orderNo);
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "支付方式数据异常");
                return JSON.toJSONString(resultMap);
            }
            //记录订单轨迹表
            FCOrderLocus fcOrderLocus = new FCOrderLocus();
            String orderLocusSN = maxNoService.createMaxNo("orderLocusSN", "", 20);
            fcOrderLocus.setOrderLocusSN(orderLocusSN);
            fcOrderLocus.setOrderNo(orderNo);
            fcOrderLocus.setOrderStatus("08");
            fcOrderLocus.setOperator(globalInput.getUserNo());
            fcOrderLocus = CommonUtil.initObject(fcOrderLocus, "INSERT");
            fcOrderLocusMapper.insert(fcOrderLocus);

            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "确认成功");
        } catch (Exception e) {
            log.info("确认失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "订单确认失败");
        }
        return JSON.toJSONString(resultMap);
    }

    public void updateFcorder(String orderStatus, String orderNo) {
        //方法内加 insurePlan 更新为true 是否确认投保
        fcOrderMapper.updateFcorder(orderStatus, orderNo, DateTimeUtil.getCurrentDate(), DateTimeUtil.getCurrentTime());
    }

    public String eflexHealthyInform(String token, Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            String grpNo = globalInput.getGrpNo();
            String perNo = globalInput.getCustomNo();
            //从个人注册期表查询员工职级
            String levelCode = fcPerRegistDayMapper.selectLevelCodeByPerNo(perNo, ensureCode);
            FCEnsure fCEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
            List<Map<String, Object>> amountGrageCodeList = (List<Map<String, Object>>) map.get("AmountGrageCodeList");
            if (amountGrageCodeList != null && amountGrageCodeList.size() > 0) {
                boolean flag = false;
                String personId = map.get("personId").toString();
                String relation = map.get("relation").toString();
                if (relation.equals("0")) {
                    double amnt15070 = 0.0;
                    for (Map<String, Object> amountGrageCodeMap : amountGrageCodeList) {
                        double amntOther = 0.0;
                        String riskCode = amountGrageCodeMap.get("RiskCode").toString();
                        double defaultAmnt = Double.valueOf(amountGrageCodeMap.get("DefaultAmnt").toString());
                        if (riskCode.equals("15070")) {
                            amnt15070 = CommonUtil.add(amnt15070, defaultAmnt);
                            Object amObj = amountGrageCodeMap.get("Optional");
                            if (amObj != null) {
                                List<Map<String, Object>> amountGrageCodeListb = (List<Map<String, Object>>) amObj;
                                if (amountGrageCodeListb != null && amountGrageCodeListb.size() > 0) {
                                    for (Map<String, Object> optionalMap : amountGrageCodeListb) {
                                        double optionalAmnt = Double.valueOf(optionalMap.get("Amnt").toString());
                                        amnt15070 = CommonUtil.add(amnt15070, optionalAmnt);
                                    }
                                }
                            }
                        } else {
                            amntOther = CommonUtil.add(amntOther, defaultAmnt);
                            Object amObj = amountGrageCodeMap.get("Optional");
                            if (amObj != null) {
                                List<Map<String, Object>> amountGrageCodeListb = (List<Map<String, Object>>) amObj;
                                if (amountGrageCodeListb != null && amountGrageCodeListb.size() > 0) {
                                    for (Map<String, Object> optionalMap : amountGrageCodeListb) {
                                        double optionalAmnt = Double.valueOf(optionalMap.get("Amnt").toString());
                                        amntOther = CommonUtil.add(amntOther, optionalAmnt);
                                    }
                                }
                            }
                            amountGrageCodeMap.put("ensureCode", ensureCode);
                            amountGrageCodeMap.put("grpNo", grpNo);
                            amountGrageCodeMap.put("BirthDay", fcPerInfo.getBirthDay());
                            amountGrageCodeMap.put("Sex", fcPerInfo.getSex());
                            amountGrageCodeMap.put("LevelCode", levelCode);
                            amountGrageCodeMap.put("CvaliDate", fCEnsure.getCvaliDate());
                            amountGrageCodeMap.put("relation", relation);
                            amountGrageCodeMap.put("Amnt", amntOther);
                            int checkIsNeedInform = fCPlanHealthDesignRelaMapper.checkIsNeedInform(amountGrageCodeMap);
                            if (checkIsNeedInform > 0) {
                                flag = true;
                                break;
                            }
                        }
                    }
                    if (amnt15070 > 0) {
                        Map<String, Object> amountGrageCodeMap = new HashMap<String, Object>();
                        amountGrageCodeMap.put("RiskCode", "15070");
                        amountGrageCodeMap.put("ensureCode", ensureCode);
                        amountGrageCodeMap.put("grpNo", grpNo);
                        amountGrageCodeMap.put("BirthDay", fcPerInfo.getBirthDay());
                        amountGrageCodeMap.put("Sex", fcPerInfo.getSex());
                        amountGrageCodeMap.put("LevelCode", levelCode);
                        amountGrageCodeMap.put("CvaliDate", fCEnsure.getCvaliDate());
                        amountGrageCodeMap.put("relation", relation);
                        amountGrageCodeMap.put("Amnt", amnt15070);
                        int checkIsNeedInform = fCPlanHealthDesignRelaMapper.checkIsNeedInform(amountGrageCodeMap);
                        if (checkIsNeedInform > 0) {
                            flag = true;
                        }
                    }
                } else {
                    FCPerson fcperson = fcPersonMapper.selectByPrimaryKey(personId);
                    double amnt15070 = 0.0;
                    for (Map<String, Object> amountGrageCodeMap : amountGrageCodeList) {
                        double amntOther = 0.0;
                        String riskCode = amountGrageCodeMap.get("RiskCode").toString();
                        double defaultAmnt = Double.valueOf(amountGrageCodeMap.get("DefaultAmnt").toString());
                        if (riskCode.equals("15070")) {
                            amnt15070 = CommonUtil.add(amnt15070, defaultAmnt);
                            Object amObj = amountGrageCodeMap.get("Optional");
                            if (amObj != null) {
                                List<Map<String, Object>> amountGrageCodeListb = (List<Map<String, Object>>) amObj;
                                if (amountGrageCodeListb != null && amountGrageCodeListb.size() > 0) {
                                    for (Map<String, Object> optionalMap : amountGrageCodeListb) {
                                        double optionalAmnt = Double.valueOf(optionalMap.get("Amnt").toString());
                                        amnt15070 = CommonUtil.add(amnt15070, optionalAmnt);
                                    }
                                }
                            }
                        } else {
                            amntOther = CommonUtil.add(amntOther, defaultAmnt);
                            Object amObj = amountGrageCodeMap.get("Optional");
                            if (amObj != null) {
                                List<Map<String, Object>> amountGrageCodeListb = (List<Map<String, Object>>) amObj;
                                if (amountGrageCodeListb != null && amountGrageCodeListb.size() > 0) {
                                    for (Map<String, Object> optionalMap : amountGrageCodeListb) {
                                        double optionalAmnt = Double.valueOf(optionalMap.get("Amnt").toString());
                                        amntOther = CommonUtil.add(amntOther, optionalAmnt);
                                    }
                                }
                            }
                            amountGrageCodeMap.put("ensureCode", ensureCode);
                            amountGrageCodeMap.put("grpNo", grpNo);
                            amountGrageCodeMap.put("BirthDay", fcperson.getBirthDate());
                            amountGrageCodeMap.put("Sex", fcperson.getSex());
                            amountGrageCodeMap.put("CvaliDate", fCEnsure.getCvaliDate());
                            amountGrageCodeMap.put("relation", relation);
                            amountGrageCodeMap.put("Amnt", amntOther);
                            int checkIsNeedInform = fCPlanHealthDesignRelaMapper.checkIsNeedInform(amountGrageCodeMap);
                            if (checkIsNeedInform > 0) {
                                flag = true;
                                break;
                            }
                        }
                    }
                    if (amnt15070 > 0) {
                        Map<String, Object> amountGrageCodeMap = new HashMap<String, Object>();
                        amountGrageCodeMap.put("RiskCode", "15070");
                        amountGrageCodeMap.put("ensureCode", ensureCode);
                        amountGrageCodeMap.put("grpNo", grpNo);
                        amountGrageCodeMap.put("BirthDay", fcperson.getBirthDate());
                        amountGrageCodeMap.put("Sex", fcperson.getSex());
                        amountGrageCodeMap.put("CvaliDate", fCEnsure.getCvaliDate());
                        amountGrageCodeMap.put("relation", relation);
                        amountGrageCodeMap.put("Amnt", amnt15070);
                        int checkIsNeedInform = fCPlanHealthDesignRelaMapper.checkIsNeedInform(amountGrageCodeMap);
                        if (checkIsNeedInform > 0) {
                            flag = true;
                        }
                    }
                }
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "查询成功");
                resultMap.put("flag", flag);//flag=true则需要弹出健康告知，flag=false则不需要弹出健康告知。
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "保额档次不能为空");
            }
        } catch (Exception e) {
            log.info("确认失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "个人健康告知接口异常");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 个人订单查询接口
     *
     * @param token
     * @return
     */
    public String perOrderQuery(String token, String commitDate1, String commitDate2) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            List<Map<String, String>> orderInfoList = new ArrayList<>();
            String perNo = globalInput.getCustomNo();
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("perNo", perNo);
            mapInfo.put("commitDate1", commitDate1);
            mapInfo.put("commitDate2", commitDate2);
            List<FCOrder> fcOrderList = fcOrderMapper.getOrderListByPerNo(mapInfo);
            for (FCOrder fcOrderInfo : fcOrderList) {
                Map<String, String> orderInfo = fcOrderMapper.getOrderInfoByOrderNo(fcOrderInfo.getOrderNo());
                List<FCOrderItem> fcOrderItems = fcOrderItemMapper.selectOrderNo(fcOrderInfo.getOrderNo());
                double sumPrem = 0.00;
                double grpPrem = 0.00;
                double selPrem = 0.00;
                for (FCOrderItem orderItemInfo : fcOrderItems) {
                    grpPrem = CommonUtil.add(grpPrem, orderItemInfo.getGrpPrem());
                    selPrem = CommonUtil.add(selPrem, orderItemInfo.getSelfPrem());
                }
                sumPrem = CommonUtil.add(grpPrem, selPrem);
                orderInfo.put("sumPrem", String.valueOf(sumPrem));
                orderInfo.put("grpPrem", String.valueOf(grpPrem));
                orderInfo.put("selPrem", String.valueOf(selPrem));
                String closeDay = orderInfo.get("closeDaynew") + "";
                String s = DateTimeUtil.dateToStamp(closeDay + " 24:00:00");
                orderInfo.put("closeDaynew", s);
                orderInfoList.add(orderInfo);
            }
            resultMap.put("data", orderInfoList);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "订单查询成功。");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "订单查询失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 取消订单
     *
     * @param orderNo
     * @return
     */
    public String canceOrder(String token, String orderNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            if (StringUtils.isBlank(orderNo)) {
                resultMap.put("message", "订单编号缺失，取消订单失败。");
                return JSON.toJSONString(resultMap);
            }
            FCOrder fcOrder = fcOrderMapper.selectByPrimaryKey(orderNo);
            if (fcOrder == null) {
                resultMap.put("message", "未查询到该订单，取消订单失败。");
                return JSON.toJSONString(resultMap);
            }
            List<Map<String, String>> deleteOrderInfoList = fcOrderMapper.getDeleteOrderInfo(orderNo);
            if (deleteOrderInfoList != null && deleteOrderInfoList.size() > 0) {
                deleteOrderInfoList.forEach((Map<String, String> map) -> {
                    String ensureCode = map.get("ensureCode");
                    String perNo = map.get("perNo");
                    String personId = map.get("personId");
                    //删除保额档次临时表
                    fPInsureEflexPlanOptionalMapper.deletefPInsureEflexPlanOptional(personId, perNo, ensureCode);
                    fPInsureEflexPlanMapper.deletefPInsureEflexPlan(personId, perNo, ensureCode);
                });
            }
            //删除保额档次正式表
            fPInsureEflexPlanMapper.deleteAllFcInsureEflexPlan(orderNo);
            fPInsureEflexPlanMapper.deleteAllFcInsureEflexPlanOptional(orderNo);
            //删除子订单详情表
            fcOrderItemDetailMapper.deleteByOrderNo(orderNo);
            // 删除订单下子订单的健康告知信息表
            fcPerImpartResultMapper.deleteByOrderNo(orderNo);
            //删除子订单表
            fcOrderItemMapper.deleteByOrderNo(orderNo);
            //删除被保人表
            fcOrderInsuredMapper.deleteByOrderNo(orderNo);
            //删除订单表
            fcOrderMapper.deleteByPrimaryKey(orderNo);
            // 删除投保计划表
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectByPrimaryKey(fcOrder.getGrpOrderNo());
            FPInsurePlan insurePlan = new FPInsurePlan();
            insurePlan.setEnsureCode(fcGrpOrder.getEnsureCode());
            insurePlan.setPerno(fcOrder.getPerNo());
            fpInsurePlanMapper.deleteInsurePlan(insurePlan);
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "取消订单成功。");
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("message", "取消订单失败。");
            return JSON.toJSONString(resultMap);
        }
    }


    public String getDeductibleCompensationRatioList(String token, String amountGrageCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, Object> map = new HashMap<>();
            List<String> deductibleList = fcEnsureMapper.selectDeductibleList(amountGrageCode);
            List<String> compensationRatioList = fcEnsureMapper.selectCompensationRatioList(amountGrageCode);
            if (deductibleList != null && deductibleList.size() > 0) {
                List<Map<String, Object>> deductibleMapList = new ArrayList<Map<String, Object>>();
                for (String deductible : deductibleList) {
                    Map<String, Object> deductiblemap = new HashMap<String, Object>();
                    deductiblemap.put("Deductible", deductible);
                    deductiblemap.put("isChecked", "0");
                    deductibleMapList.add(deductiblemap);
                }
                map.put("deductibleList", deductibleMapList);
            } else {
                map.put("deductibleList", new ArrayList<Map<String, Object>>());
            }
            if (compensationRatioList != null && compensationRatioList.size() > 0) {
                List<Map<String, Object>> compensationRatioMapList = new ArrayList<Map<String, Object>>();
                for (String compensationRatio : compensationRatioList) {
                    Map<String, Object> compensationRatioMmap = new HashMap<String, Object>();
                    compensationRatioMmap.put("CompensationRatio", compensationRatio);
                    compensationRatioMmap.put("isChecked", "0");
                    compensationRatioMapList.add(compensationRatioMmap);
                }
                map.put("compensationRatioList", compensationRatioMapList);
            } else {
                map.put("compensationRatioList", new ArrayList<Map<String, Object>>());
            }
            resultMap.put("data", map);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "获取免赔额及赔付比例成功。");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "获取免赔额及赔付比例失败。");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 修改订单状态
     *
     * @param orderNo
     * @return
     */
    public String updateOrderState(String token, String orderNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            if (StringUtils.isBlank(orderNo)) {
                resultMap.put("message", "订单编号缺失，取消订单失败。");
                return JSON.toJSONString(resultMap);
            }
            GlobalInput globalInput = userService.getSession(token);
            FCOrder fcOrder = fcOrderMapper.selectByPrimaryKey(orderNo);
            if (fcOrder == null) {
                resultMap.put("message", "未查询到该订单，取消订单失败。");
                return JSON.toJSONString(resultMap);
            }
            fcOrder.setOrderStatus("01");
            fcOrder.setOperator(globalInput.getUserNo());
            fcOrder = CommonUtil.initObject(fcOrder, "UPDATE");
            int i = fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
            if (i < 1) {
                resultMap.put("message", "取消订单失败。");
                return JSON.toJSONString(resultMap);
            } else {
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "取消订单成功。");
                return JSON.toJSONString(resultMap);
            }
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("message", "取消订单失败。");
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 订单修改--更新token
     *
     * @param token
     * @param ensureCode
     * @return
     */
    public String updateToken(String token, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            String userinfo = redisUtil.get(token);
            GlobalInput globalInput = JSON.parseObject(userinfo, GlobalInput.class);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            Map<String, Object> mapInfo = new HashMap<>();
            mapInfo.put("IDNo", fcPerInfoMapper.selectByPrimaryKey(globalInput.getCustomNo()).getIDNo());
            mapInfo.put("grpNo", fcEnsure.getGrpNo());
            List<FCPerInfo> fcPerInfo = fcPerInfoMapper.isExitPerInfo(mapInfo);
            globalInput.setEnsureCode(ensureCode);
            globalInput.setGrpNo(fcEnsure.getGrpNo());
            globalInput.setCustomNo(fcPerInfo.get(0).getPerNo());
            Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
            redisUtil.put(token, JSON.toJSONString(globalInput), loginValidity);
            if (JSON.parseObject(redisUtil.get(token), GlobalInput.class).getGrpNo().equals(fcEnsure.getGrpNo())) {
                resultMap.put("globalInput", globalInput);
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "Token更新成功");
            } else {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "Token更新失败");
            }
            return JSON.toJSONString(resultMap);
        } catch (Exception e) {
            log.info("error", e);
        }
        return JSON.toJSONString(resultMap);
    }


    public String eflexGetFamilyTotalPrem(String token, Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        Double totlePrem = 0.0;
        try {
            Object object = map.get("AmountGrageCodeList");
            if (object != null) {
                List<Map<String, Object>> amountGrageCodeList = (List<Map<String, Object>>) map.get("AmountGrageCodeList");
                if (amountGrageCodeList != null && amountGrageCodeList.size() > 0) {
                    for (Map<String, Object> amountGrageCodeMap : amountGrageCodeList) {
                        Object premObj = amountGrageCodeMap.get("Prem");
                        if (premObj != null) {
                            String prem = premObj.toString();
                            totlePrem = CommonUtil.add(totlePrem, Double.valueOf(prem == null || "".equals(prem) ? "0.0" : prem));
                        }
                    }
                }
            }
            resultMap.put("totlePrem", totlePrem);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "获取保费合计成功");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "获取保费合计失败");
        }
        return JSON.toJSONString(resultMap);
    }

    public String deleteElfexFamilyAmount(String token, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            String perNo = globalInput.getCustomNo();
            List<FCOrder> selectOrder = fcOrderMapper.selectOrder(perNo, ensureCode);
            if (selectOrder != null && selectOrder.size() > 0) {
                selectOrder.forEach((FCOrder fcOrder) -> {
                    //删除保额档次正式表
                    fPInsureEflexPlanMapper.deleteFcInsureEflexPlan(fcOrder.getOrderNo(), personId);
                    fPInsureEflexPlanMapper.deleteFcInsureEflexPlanOptional(fcOrder.getOrderNo(), personId);
                    //删除子订单详情表
                    fcOrderItemDetailMapper.deleteByPersonId(fcOrder.getOrderNo(), personId);
                    //删除子订单表
                    fcOrderItemMapper.deleteByPersonId(fcOrder.getOrderNo(), personId);
                    //删除被保人表
                    fcOrderInsuredMapper.deleteByPersonId(fcOrder.getOrderNo(), personId);
                });
            }
            fPInsureEflexPlanOptionalMapper.deletefPInsureEflexPlanOptional(personId, perNo, ensureCode);
            fPInsureEflexPlanMapper.deletefPInsureEflexPlan(personId, perNo, ensureCode);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "取消成功");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "取消失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询所投计划险种信息(二期)
     *
     * @param planCodeInfo
     * @param personId
     * @return
     */
    public String getRiskInfos(List<Map<String, String>> planCodeInfo, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        double sumAmnt1 = 0.00;
        double sumAmnt2 = 0.00;
        double sumAmnt3 = 0.00;
        double sumAmnt4 = 0.00;
        double sumAmnt5 = 0.00;
        double sumAmnt6 = 0.00;
        try {
            Map<String, Object> map1 = new HashMap<>();
            map1.put("riskType", "1");
            map1.put("isRisk", "0");
            map1.put("Amnt", 0.0);
            Map<String, Object> map2 = new HashMap<>();
            map2.put("riskType", "2");
            map2.put("isRisk", "0");
            map2.put("Amnt", 0.0);
            Map<String, Object> map3 = new HashMap<>();
            map3.put("riskType", "3");
            map3.put("isRisk", "0");
            map3.put("Amnt", 0.0);
            Map<String, Object> map4 = new HashMap<>();
            map4.put("riskType", "4");
            map4.put("isRisk", "0");
            map4.put("Amnt", 0.0);
            Map<String, Object> map5 = new HashMap<>();
            map5.put("riskType", "5");
            map5.put("isRisk", "0");
            map5.put("Amnt", 0.0);
            Map<String, Object> map6 = new HashMap<>();
            map6.put("riskType", "6");
            map6.put("isRisk", "0");
            map6.put("Amnt", 0.0);
            //四舍五入
//			DecimalFormat df = new DecimalFormat("#.00");
            if (planCodeInfo.size() > 0) {
                for (int j = 0; j < planCodeInfo.size(); j++) {
                    Map<String, String> map = planCodeInfo.get(j);
                    String ensureCode = map.get("ensureCode");
                    String planCode = map.get("planCode");
                    String number = fcEnsureMapper.getEffEctiveEnsure(ensureCode); //查询该计划所在福利是否失效  返回 1-有效
                    if ("1".equals(number)) {
                        //查询该计划下所有险种
                        List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectRiskInfo(ensureCode, planCode);
                        //查询每一个险种
                        for (int i = 0; i < fcPlanRiskList.size(); i++) {
                            //获取每一个险种编号
                            String riskCode = fcPlanRiskList.get(i).getRiskCode();
                            List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyInfo(ensureCode, planCode, riskCode);
                            Double Amnt = 0.00;
                            for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                                Double amnt = fcPlanRiskDuty.getAmnt();
                                Amnt += amnt;        //获取该险种下所有责任的总计保额
                            }
                            //查询险种对应的险种类型
                            FDRiskInfo fdRiskInfo = fdRiskInfoMapper.selectByPrimaryKey(riskCode);
                            String riskType = fdRiskInfo.getRiskType();
                            //获取已投保险种总称
                            switch (riskType) {
                                case "1":
                                    sumAmnt1 += Amnt;                //获取该用户下的所投保福利下同一险种下的保额总计
                                    map1.put("Amnt", sumAmnt1);
                                    map1.put("isRisk", "1");
                                    break;
                                case "2":
                                    sumAmnt2 += Amnt;
                                    map2.put("Amnt", sumAmnt2);
                                    map2.put("isRisk", "1");
                                    break;
                                case "3":
                                    sumAmnt3 += Amnt;
                                    map3.put("Amnt", sumAmnt3);
                                    map3.put("isRisk", "1");
                                    break;
                                case "4":
                                    sumAmnt4 += Amnt;
                                    map4.put("Amnt", sumAmnt4);
                                    map4.put("isRisk", "1");
                                    break;
                                case "5":
                                    sumAmnt5 += Amnt;
                                    map5.put("Amnt", sumAmnt5);
                                    map5.put("isRisk", "1");
                                    break;
                                case "6":
                                    sumAmnt6 += Amnt;
                                    map6.put("Amnt", sumAmnt6);
                                    map6.put("isRisk", "1");
                                    break;
                                default:
                            }
                        }
                    }
                }
            }
            if (StringUtil.isNotEmpty(personId)) {
                //弹性福利险种信息
                List<String> personList = fcPersonMapper.getAllPerson(personId);
                for (String personID : personList) {
                    List<Map<String, String>> fcInsureList = fcInsureEflexPlanMapper.selectByPersonId(personID);
                    for (Map<String, String> insureMap : fcInsureList) {
                        String orderItemDetailNo = insureMap.get("OrderItemDetailNo");
                        //得到投保必选责任档次编码以及详细信息
                        List<Map<String, String>> fcInsureEflexPlanList = fcInsureEflexPlanMapper.selectByorderItemDetailNo(orderItemDetailNo);
                        for (Map<String, String> insureEflexMap : fcInsureEflexPlanList) {
                            //得到每一个险种编号与保费
                            String riskCode = insureEflexMap.get("riskCode");
                            Double Amnt = 0.00;
                            Double amnt = Double.valueOf(insureEflexMap.get("amnt"));
                            Amnt += amnt;
                            //查询险种对应的险种类型
                            FDRiskInfo fdRiskInfo = fdRiskInfoMapper.selectByPrimaryKey(riskCode);
                            String riskType = fdRiskInfo.getRiskType();
                            //获取已投保险种总称
                            switch (riskType) {
                                case "1":
                                    sumAmnt1 = (double) map1.get("Amnt");
                                    sumAmnt1 += Amnt;                //获取该用户下的所投保福利下同一险种下的保额总计
                                    map1.put("Amnt", sumAmnt1);
                                    map1.put("isRisk", "1");
                                    break;
                                case "2":
                                    sumAmnt2 = (double) map2.get("Amnt");
                                    sumAmnt2 += Amnt;
                                    map2.put("Amnt", sumAmnt2);
                                    map2.put("isRisk", "1");
                                    break;
                                case "3":
                                    sumAmnt3 = (double) map3.get("Amnt");
                                    sumAmnt3 += Amnt;
                                    map3.put("Amnt", sumAmnt3);
                                    map3.put("isRisk", "1");
                                    break;
                                case "4":
                                    sumAmnt4 = (double) map4.get("Amnt");
                                    sumAmnt4 += Amnt;
                                    map4.put("Amnt", sumAmnt4);
                                    map4.put("isRisk", "1");
                                    break;
                                case "5":
                                    sumAmnt5 = (double) map5.get("Amnt");
                                    sumAmnt5 += Amnt;
                                    map5.put("Amnt", sumAmnt5);
                                    map5.put("isRisk", "1");
                                    break;
                                case "6":
                                    sumAmnt6 = (double) map6.get("Amnt");
                                    sumAmnt6 += Amnt;
                                    map6.put("Amnt", sumAmnt6);
                                    map6.put("isRisk", "1");
                                    break;
                                default:
                            }
                        }
                    }
                }
            }
            List<Map<String, Object>> list = new ArrayList<>();
            list.add(map1);
            list.add(map2);
            list.add(map3);
            list.add(map4);
            list.add(map5);
            list.add(map6);
            resultMap.put("isInsure", list);
            resultMap.put("code", "200");
            resultMap.put("message", "所投计划险种信息查询成功");
        } catch (Exception e) {
            log.info("所投计划险种信息查询失败", e);
            resultMap.put("code", "500");
            resultMap.put("message", "所投计划险种信息查询失败");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 福利查询(二期)
     *
     * @param personId
     * @return
     */
    public String getEnsureList(String token, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Object> insureInfoList = new ArrayList<>();
        try {
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
            //姓名
            resultMap.put("name", fcPerson.getName());
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectPersonIdInfo(personId);
            //关系
            resultMap.put("relation", fcStaffFamilyRela.getRelation());
            Map<String, Object> personIDMap = new HashMap<>();
            personIDMap.put("personId", personId);
            List<Map<String, String>> fpInsurePlan = fpInsurePlanMapper.getPlanCodeByPersonId(personIDMap);
            if (fpInsurePlan.size() > 0) {
                //判断员工及家属是否投过保
                resultMap.put("isPlan", "1");
                for (Map<String, String> planMap : fpInsurePlan) {
                    String planCode = planMap.get("PlanCode");
                    String personID = planMap.get("PersonId");
                    String ensureCode = planMap.get("EnsureCode");
                    FCEnsure fdEnsure = fcEnsureMapper.getFCEnsureByPlanCode(ensureCode, planCode);
                    //获取计划个人缴费以及企业缴费
                    FCOrderItem fcOrderItem = fcOrderItemMapper.getByPersonId(ensureCode, planCode, personID);
                    //查询该计划下所有险种
                    List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectRiskInfo(ensureCode, planCode);
                    //查询险种的信息及员工缴纳的费用
                    for (FCPlanRisk fcPlanRisk : fcPlanRiskList) {
                        String riskCode = fcPlanRisk.getRiskCode();
                        String riskName = fdRiskInfoMapper.selectRiskNameByRiskCode(riskCode);
                        List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyInfo(ensureCode, planCode, riskCode);
                        for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                            Map<String, Object> map1 = new HashMap<>();
                            map1.put("ensureName", fdEnsure.getEnsureName());
                            map1.put("ensureCode", fdEnsure.getEnsureCode());
                            map1.put("planType", fdEnsure.getPlanType());
                            map1.put("riskName", riskName);
                            map1.put("prem", fcPlanRiskDuty.getPrem());
                            map1.put("deductible", fcPlanRiskDuty.getGetLimit());
                            if (fcPlanRiskDuty.getGetRatio() != null) {
                                //此数据在数据库中存储的是小数，向前台传输数据得时候要恢复到百分比
                                map1.put("compensationRatio", fcPlanRiskDuty.getGetRatio() * 100);
                            } else {
                                map1.put("compensationRatio", fcPlanRiskDuty.getGetRatio());
                            }
                            map1.put("riskCode", fcPlanRisk.getRiskCode());
                            map1.put("dutyCode", fcPlanRiskDuty.getDutyCode());
                            map1.put("planCode", planCode);
                            map1.put("amnt", fcPlanRiskDuty.getAmnt());
                            //获取责任名称
                            Map<String, String> dutyMap = new HashMap<>();
                            dutyMap.put("dutyCode", fcPlanRiskDuty.getDutyCode());
                            dutyMap.put("riskCode", riskCode);
                            Map<String, Object> fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                            map1.put("dutyName", fdRiskDutyInfo.get("DutyRange"));
                            map1.put("selfPrem", fcOrderItem.getSelfPrem());
                            map1.put("grpPrem", fcOrderItem.getGrpPrem());
                            insureInfoList.add(map1);
                        }
                    }
                }
            }
            //弹性计划
            //得到所有personId
            List<String> personList = fcPersonMapper.getAllPerson(personId);
            for (String PersonID : personList) {
                List<Map<String, String>> fcInsureList = fcInsureEflexPlanMapper.getByPersonId(PersonID);
                if (fcInsureList.size() < 1) {
                    continue;
                }
                resultMap.put("isPlan", "1");
                for (Map<String, String> insureMap : fcInsureList) {
                    FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(insureMap.get("EnsureCode"));
                    String orderItemDetailNo = insureMap.get("OrderItemDetailNo");
                    //得到投保必选责任档次编码以及详细信息
                    List<Map<String, String>> fcInsureEflexPlanList = fcInsureEflexPlanMapper.selectByorderItemDetailNo(orderItemDetailNo);
                    for (Map<String, String> insureEflexMap : fcInsureEflexPlanList) {
                        Map<String, Object> map2 = new HashMap<>();
                        map2.put("ensureName", fcEnsure.getEnsureName());
                        map2.put("ensureCode", fcEnsure.getEnsureCode());
                        map2.put("planType", fcEnsure.getPlanType());
                        map2.put("selfPrem", insureMap.get("SelfPrem"));
                        map2.put("grpPrem", insureMap.get("GrpPrem"));
                        String riskCode = insureEflexMap.get("riskCode");
                        map2.put("riskCode", riskCode);
                        if ("17050".equals(riskCode)) {
                            String amnt = insureEflexMap.get("amnt");
                            if ("GD0070".equals(insureEflexMap.get("dutyCode"))) {
                                map2.put("amnt", Double.valueOf(amnt) / 2);
                                map2.put("prem", insureEflexMap.get("prem"));
                                map2.put("riskName", fdRiskInfoMapper.selectRiskNameByRiskCode(riskCode));
                                //获取责任名称
                                Map<String, String> dutyMap = new HashMap<>();
                                dutyMap.put("riskCode", riskCode);
                                dutyMap.put("dutyCode", "GD0070");
                                Map<String, Object> fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                                map2.put("dutyName", fdRiskDutyInfo.get("DutyRange"));
                                map2.put("deductible", insureEflexMap.get("deductible"));
                                map2.put("compensationRatio", insureEflexMap.get("compensationRatio"));
                                insureInfoList.add(map2);
                                map2 = new HashMap<>(map2);
                                dutyMap.put("dutyCode", "GD0071");
                                fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                                map2.put("dutyName", fdRiskDutyInfo.get("DutyRange"));
                                map2.put("prem", "");
                                insureInfoList.add(map2);
                            }
                        } else {
                            String dutyCode = insureEflexMap.get("dutyCode");
                            map2.put("riskName", fdRiskInfoMapper.selectRiskNameByRiskCode(riskCode));
                            map2.put("dutyCode", dutyCode);
                            Map<String, String> dutyMap = new HashMap<>();
                            dutyMap.put("riskCode", riskCode);
                            dutyMap.put("dutyCode", dutyCode);
                            Map<String, Object> fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                            map2.put("dutyName", fdRiskDutyInfo.get("DutyRange"));
                            //保额
                            map2.put("amnt", insureEflexMap.get("amnt"));
                            //保费
                            map2.put("prem", insureEflexMap.get("prem"));
                            map2.put("deductible", insureEflexMap.get("deductible"));
                            map2.put("compensationRatio", insureEflexMap.get("compensationRatio"));
                            insureInfoList.add(map2);
                        }
                    }
                }
            }
            if (insureInfoList.size() > 0) {
                resultMap.put("insureInfoList", insureInfoList);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "福利信息查询成功！");
            } else {
                resultMap.put("isPlan", "0");
                resultMap.put("insureInfoList", insureInfoList);
                resultMap.put("success", false);
                resultMap.put("code", "203");
                resultMap.put("message", "未查到福利信息！");
            }
        } catch (Exception e) {
            log.info("error", e);

            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "福利查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    public String getPhoneEnsureList(String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Object> insureInfoList = new ArrayList<>();
        try {
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
            //姓名
            resultMap.put("name", fcPerson.getName());
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectPersonIdInfo(personId);
            //关系
            resultMap.put("relation", fcStaffFamilyRela.getRelation());
            Map<String, Object> personIDMap = new HashMap<>();
            personIDMap.put("personId", personId);
            List<Map<String, String>> fpInsurePlan = fpInsurePlanMapper.getPlanCodeByPersonId(personIDMap);
            if (fpInsurePlan.size() > 0) {
                //判断员工及家属是否投过保
                resultMap.put("isPlan", "1");
                for (Map<String, String> planMap : fpInsurePlan) {
                    Map<String, Object> planInfoMap = new HashMap<>();
                    List<Map<String, Object>> fcPlanInfo = new ArrayList<>();
                    String planCode = planMap.get("PlanCode");
                    String personID = planMap.get("PersonId");
                    String ensureCode = planMap.get("EnsureCode");
                    Double planSumPrem = 0.00;
                    FCEnsure fdEnsure = fcEnsureMapper.getFCEnsureByPlanCode(ensureCode, planCode);
                    //获取计划个人缴费以及企业缴费
                    FCOrderItem fcOrderItem = fcOrderItemMapper.getByPersonId(ensureCode, planCode, personID);
                    //查询该计划下所有险种
                    List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectRiskInfo(ensureCode, planCode);
                    //查询险种的信息及员工缴纳的费用
                    for (FCPlanRisk fcPlanRisk : fcPlanRiskList) {
                        String riskCode = fcPlanRisk.getRiskCode();
                        FDRiskInfo fdRiskInfo = fdRiskInfoMapper.selectByPrimaryKey(riskCode);
                        List<FCPlanRiskDuty> fcPlanRiskDutyList = fcPlanRiskDutyMapper.selectDutyInfo(ensureCode, planCode, riskCode);
                        for (FCPlanRiskDuty fcPlanRiskDuty : fcPlanRiskDutyList) {
                            Map<String, Object> map1 = new HashMap<>();
                            map1.put("ensureName", fdEnsure.getEnsureName());
                            map1.put("ensureCode", fdEnsure.getEnsureCode());
                            map1.put("planType", fdEnsure.getPlanType());
                            map1.put("riskName", fdRiskInfo.getRiskName());
                            map1.put("riskType", fdRiskInfo.getRiskType());
                            boolean b = DateTimeUtil.checkDate(DateTimeUtil.getCurrentDate(), fdEnsure.getPolicyEndDate());
                            boolean a = DateTimeUtil.checkDate(fdEnsure.getCvaliDate(), DateTimeUtil.getCurrentDateTime());
                            map1.put("ensureState", (b && a));
                            map1.put("policyEndDate", fdEnsure.getPolicyEndDate());
                            map1.put("prem", fcPlanRiskDuty.getPrem());
                            map1.put("deductible", fcPlanRiskDuty.getGetLimit());
                            if (fcPlanRiskDuty.getGetRatio() != null) {
                                //此数据在数据库中存储的是小数，向前台传输数据得时候要恢复到百分比
                                map1.put("compensationRatio", fcPlanRiskDuty.getGetRatio() * 100);
                            } else {
                                map1.put("compensationRatio", fcPlanRiskDuty.getGetRatio());
                            }
                            map1.put("riskCode", fcPlanRisk.getRiskCode());
                            map1.put("dutyCode", fcPlanRiskDuty.getDutyCode());
                            map1.put("planCode", planCode);
                            map1.put("amnt", fcPlanRiskDuty.getAmnt());
                            //获取责任名称
                            Map<String, String> dutyMap = new HashMap<>();
                            dutyMap.put("dutyCode", fcPlanRiskDuty.getDutyCode());
                            dutyMap.put("riskCode", riskCode);
                            Map<String, Object> fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                            map1.put("dutyName", fdRiskDutyInfo.get("DutyRange"));
                            map1.put("selfPrem", fcOrderItem != null  ? fcOrderItem.getSelfPrem() : "0");
                            map1.put("grpPrem", fcOrderItem != null  ? fcOrderItem.getGrpPrem(): "0");
                            fcPlanInfo.add(map1);
                            planSumPrem = CommonUtil.add(planSumPrem, fcPlanRiskDuty.getPrem());
                        }
                    }
                    planInfoMap.put("planInfo", fcPlanInfo);
                    planInfoMap.put("planSumPrem", planSumPrem);
                    insureInfoList.add(planInfoMap);
                }
            }
            //弹性计划
            //得到所有personId
            List<String> personList = fcPersonMapper.getAllPerson(personId);
            for (String PersonID : personList) {
                List<Map<String, String>> fcInsureList = fcInsureEflexPlanMapper.getByPersonId(PersonID);
                if (fcInsureList.size() < 1) {
                    continue;
                }
                resultMap.put("isPlan", "1");
                for (Map<String, String> insureMap : fcInsureList) {
                    Double planSumPrem = 0.00;
                    Map<String, Object> ensureInfoMap = new HashMap<>();
                    List<Map<String, Object>> ensureDutyMap = new ArrayList<>();
                    FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(insureMap.get("EnsureCode"));
                    String orderItemDetailNo = insureMap.get("OrderItemDetailNo");
                    //得到投保必选责任档次编码以及详细信息
                    List<Map<String, String>> fcInsureEflexPlanList = fcInsureEflexPlanMapper.selectByorderItemDetailNo(orderItemDetailNo);
                    for (Map<String, String> insureEflexMap : fcInsureEflexPlanList) {
                        Map<String, Object> map2 = new HashMap<>();
                        map2.put("ensureName", fcEnsure.getEnsureName());
                        map2.put("ensureCode", fcEnsure.getEnsureCode());
                        map2.put("policyEndDate", fcEnsure.getPolicyEndDate());
                        boolean b = DateTimeUtil.checkDate(DateTimeUtil.getCurrentDate(), fcEnsure.getPolicyEndDate());
                        boolean a = DateTimeUtil.checkDate(fcEnsure.getCvaliDate(), DateTimeUtil.getCurrentDateTime());
                        map2.put("ensureState", (b && a));
                        map2.put("planType", fcEnsure.getPlanType());
                        map2.put("selfPrem", insureMap.get("SelfPrem"));
                        map2.put("grpPrem", insureMap.get("GrpPrem"));
                        String riskCode = insureEflexMap.get("riskCode");
                        map2.put("riskCode", riskCode);
                        if ("17050".equals(riskCode)) {
                            String amnt = insureEflexMap.get("amnt");
                            if ("GD0070".equals(insureEflexMap.get("dutyCode"))) {
                                FDRiskInfo fdRiskInfo = fdRiskInfoMapper.selectByPrimaryKey(riskCode);
                                map2.put("amnt", Double.parseDouble(amnt) / 2);
                                map2.put("prem", insureEflexMap.get("prem"));
                                map2.put("riskName", fdRiskInfo.getRiskName());
                                map2.put("riskType", fdRiskInfo.getRiskType());
                                //获取责任名称
                                Map<String, String> dutyMap = new HashMap<>();
                                dutyMap.put("riskCode", riskCode);
                                dutyMap.put("dutyCode", "GD0070");
                                Map<String, Object> fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                                map2.put("dutyName", fdRiskDutyInfo.get("DutyRange"));
                                map2.put("deductible", insureEflexMap.get("deductible"));
                                map2.put("compensationRatio", insureEflexMap.get("compensationRatio"));
                                ensureDutyMap.add(map2);
                                Double prem = Double.parseDouble(String.valueOf(insureEflexMap.get("prem")));
                                planSumPrem = CommonUtil.add(planSumPrem, prem);
                                map2 = new HashMap<>(map2);
                                dutyMap.put("dutyCode", "GD0071");
                                fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                                map2.put("dutyName", fdRiskDutyInfo.get("DutyRange"));
                                map2.put("prem", "");
                                ensureDutyMap.add(map2);
                            }
                        } else {
                            String dutyCode = insureEflexMap.get("dutyCode");
                            map2.put("riskName", fdRiskInfoMapper.selectRiskNameByRiskCode(riskCode));
                            map2.put("dutyCode", dutyCode);
                            Map<String, String> dutyMap = new HashMap<>();
                            dutyMap.put("riskCode", riskCode);
                            dutyMap.put("dutyCode", dutyCode);
                            Map<String, Object> fdRiskDutyInfo = fdRiskDutyInfoMapper.selectDutyName(dutyMap);
                            map2.put("dutyName", fdRiskDutyInfo.get("DutyRange"));
                            //保额
                            map2.put("amnt", insureEflexMap.get("amnt"));
                            //保费
                            map2.put("prem", insureEflexMap.get("prem"));
                            map2.put("deductible", insureEflexMap.get("deductible"));
                            map2.put("compensationRatio", insureEflexMap.get("compensationRatio"));
                            ensureDutyMap.add(map2);
                            Double prem = Double.parseDouble(String.valueOf(insureEflexMap.get("prem")));
                            planSumPrem = CommonUtil.add(planSumPrem, prem);
                        }
                    }

                    ensureInfoMap.put("planInfo", ensureDutyMap);
                    ensureInfoMap.put("planSumPrem", planSumPrem);
                    insureInfoList.add(ensureInfoMap);
                }
            }
            if (insureInfoList.size() > 0) {
                resultMap.put("insureInfoList", insureInfoList);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "福利信息查询成功！");
            } else {
                resultMap.put("isPlan", "0");
                resultMap.put("insureInfoList", insureInfoList);
                resultMap.put("success", false);
                resultMap.put("code", "203");
                resultMap.put("message", "未查到福利信息！");
            }
        } catch (Exception e) {
            log.error("InsureService.getPhoneEnsureList error", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "福利查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询个人保险凭证详情（二期）
     *
     * @param token
     * @param personId
     * @return
     */
    public String getInsureDetail(String token, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            List<Map<String, Object>> insureDetailList = new ArrayList<>();
            List<String> personList = fcPersonMapper.getAllPerson(personId);
            for (String personID : personList) {
                List<Map<String, String>> fcInsureList = fcInsureEflexPlanMapper.selectDetailByPersonId(personID);
                for (Map<String, String> insureMap : fcInsureList) {
                    Map<String, Object> map = new HashMap<>();
                    FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(insureMap.get("EnsureCode"));
                    FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(insureMap.get("OrderItemNo"));
                    //发票金额
                    double invoiceAmount = 0.0;
                    List<FCOrderItem> fcOrderItemList = fcOrderItemMapper.selectOrderNo(insureMap.get("OrderNo"));
                    for (FCOrderItem fcOrderItem1 : fcOrderItemList) {
                        invoiceAmount = CommonUtil.add(invoiceAmount, fcOrderItem1.getSelfPrem());
                    }
                    //1、保险合同号（团体保单号）
                    map.put("grpContNo", insureMap.get("GrpContNo"));
                    //2、个人保单号（对应核心个单号）
                    map.put("contNo", insureMap.get("ContNo"));
                    String cvaliDate = fcEnsure.getCvaliDate();
                    String policyEndDate = fcEnsure.getPolicyEndDate();
                    //3、合同生效日（保单生效日）
                    map.put("cvaliDate", cvaliDate);
                    //4、保险期间：保单生效日--保单截止日
                    map.put("ensureDate", "<p>" + cvaliDate + "</p>" + "<p>" + "至" + "</p>" + "<p>" + policyEndDate + "</p>");
                    //5、保险费合计
                    map.put("ensurePrem", CommonUtil.add(fcOrderItem.getSelfPrem(), fcOrderItem.getGrpPrem()));
                    //6、个人支付金额
                    map.put("selfPrem", fcOrderItem.getSelfPrem());
                    map.put("grpPrem", fcOrderItem.getGrpPrem());
                    map.put("invoiceAmount", invoiceAmount);
                    map.put("policyState", fcEnsure.getPolicyState());
                    map.put("personId", personID);
                    map.put("ensureCode", insureMap.get("EnsureCode"));
                    boolean b = DateTimeUtil.checkDate(DateTimeUtil.getCurrentDate(), policyEndDate);
                    boolean a = DateTimeUtil.checkDate(cvaliDate, DateTimeUtil.getCurrentDateTime());
                    map.put("ensureState", (b && a));
                    Map<String, Object> params = new HashMap<>();
                    params.put("personId", personID);
                    params.put("customType", ConstantUtil.EnsureState_1);
                    List<FCMailInfo> fcMailInfoList = fcMailInfoMapper.getMailInfoByPersonId(params);
                    if (fcMailInfoList.size() > 0) {
                        map.put("isMail", "1");
                    } else {
                        map.put("isMail", "0");
                    }
                    insureDetailList.add(map);
                }
            }
            insureDetailList.sort((o1, o2) -> {
                String name1 = (String) o1.get("cvaliDate");
                String name2 = (String) o2.get("cvaliDate");
                return name2.compareTo(name1);
            });
            resultMap.put("insureDetailList", insureDetailList);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询成功");
        } catch (Exception e) {
            log.info("error", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 选择被保险人下一步的校验规则（固定计划）
     *
     * @param token
     * @param choiceInsurePeopleNextStepReqs
     */
    public String choiceInsurePeopleNextStep(String token, List<ChoiceInsurePeopleNextStepReq> choiceInsurePeopleNextStepReqs) {
        // 定义返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", true);
        resultMap.put("code", "200");
        resultMap.put("message", "校验通过！");

        // 本人信息查询
        String mobilePhone = null;
        String name = null;
        List<String> idNoList = new ArrayList<>();
        ChoiceInsurePeopleNextStepReq choiceInsurePeople = choiceInsurePeopleNextStepReqs.stream().filter(c -> PlanObjectEnum.STAFF.getCode().equals(c.getPlanObject())).findFirst().orElse(null);
        if (Objects.nonNull(choiceInsurePeople)) {
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(choiceInsurePeople.getPersonid());
            mobilePhone = fcPerson.getMobilePhone();
            name = fcPerson.getName();
            idNoList.add(fcPerson.getIDNo());
        }
        // 校验人员是否存在可投保的计划
        for (ChoiceInsurePeopleNextStepReq choiceInsurePeopleNextStepReq : choiceInsurePeopleNextStepReqs) {
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(choiceInsurePeopleNextStepReq.getPersonid());
            if (StringUtils.isNotEmpty(fcPerson.getBirthDate()) && DateTimeUtil.getAgeInt(fcPerson.getBirthDate()) < 18 ){
                //未成年人手机号置空
                fcPerson.setMobilePhone("");
                fcPersonMapper.updateJuvenilePhone(fcPerson.getIDNo());
            }
            // 判断家庭成员信息与本人手机信息是否一致
            if (PlanObjectEnum.FAMILY.getCode().equals(choiceInsurePeopleNextStepReq.getPlanObject()) && StringUtil.isNotEmpty(mobilePhone) &&  StringUtil.isNotEmpty(fcPerson.getMobilePhone())) {
                if (mobilePhone.equals(fcPerson.getMobilePhone())) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    //resultMap.put("message", "[" + MaskUtils.maskPhone(fcPerson.getMobilePhone()) + "] 手机号重复。请确保手机号唯一，无法提供时可忽略家属号码录入");
                    resultMap.put("message", "被保险人【"+fcPerson.getName()+"】与【"+name+"】手机号重复。请确保手机号唯一，无法提供时可忽略家属号码录入");
                    return JSON.toJSONString(resultMap);
                }
                idNoList.add(fcPerson.getIDNo());
                FCPerson checkFcPerson = fcPersonMapper.checkPhoneFcPerson(idNoList,fcPerson.getMobilePhone());
                if (checkFcPerson != null && fcPerson.getMobilePhone().equals(checkFcPerson.getMobilePhone())) {
                    log.info("与系统已有客户PersonID:{}手机号重复",checkFcPerson.getPersonID());
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    //resultMap.put("message", "[" + MaskUtils.maskPhone(fcPerson.getMobilePhone()) + "] 手机号重复。请确保手机号唯一，无法提供时可忽略家属号码录入");
                    resultMap.put("message", "被保险人【"+fcPerson.getName()+"】与系统已有客户【"+checkFcPerson.getName()+"】手机号重复。请确保手机号唯一，请修改或置为空");
                    return JSON.toJSONString(resultMap);
                }
            }
            /*// 疑似重客校验
            String sameCustomer = addressCheckService.checkSameCustomer(CheckSameCustomerConvert.convert(fcPerson));
            if (StringUtils.isNotBlank(sameCustomer)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", sameCustomer);
                return JSON.toJSONString(resultMap);
            }*/
            List<CoreCustomerInfoResDTO> coreCustomerInfoList = coreCustomerService.sameCustomer(CheckSameCustomerConvert.convertFCPerson(fcPerson));
            List<CoreCustomerInfoResDTO> errors = coreCustomerInfoList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getErrorList())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(errors) && errors.get(0) != null &&  CollectionUtils.isNotEmpty(errors.get(0).getErrorList())){
                // 添加错误信息
                String errorInfo = errors.get(0).getErrorList().get(0).getErrorInfo();
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", errorInfo);
                return JSON.toJSONString(resultMap);
            }
            // 校验人员信息是否完整
            checkPerson(fcPerson);
            String result = "";
            // 员工校验规则
            if (choiceInsurePeopleNextStepReq.getPlanObject().equals(PlanObjectEnum.STAFF.getCode())) {
                log.info("员工校验规则！");
                result = selectPlanInfo(token, choiceInsurePeopleNextStepReq.getPlanObject());
                // 员工家属规则
            } else if (choiceInsurePeopleNextStepReq.getPlanObject().equals(PlanObjectEnum.FAMILY.getCode())) {
                log.info("员工家属规则！");
                result = familyInsure(token, choiceInsurePeopleNextStepReq.getPlanObject(), choiceInsurePeopleNextStepReq.getPersonid());
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "计划对象不存在！");
            }
            // 处理结果
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (ObjectUtils.isEmpty(jsonObject.get("mapList")) && ObjectUtils.isEmpty(jsonObject.get("data"))) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "当前福利下没有符合被保人" + fcPerson.getName() + "的保险计划！");
                return JSON.toJSONString(resultMap);
            }
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 选择被保险人下一步的校验规则（弹性计划）
     *
     * @param token
     * @param choiceInsurePeopleNextStepReqs
     */
    public String choiceInsurePeopleNextStepByEflex(String token, List<ChoiceInsurePeopleNextStepReq> choiceInsurePeopleNextStepReqs) {
        // 定义返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", true);
        resultMap.put("code", "200");
        resultMap.put("message", "校验通过！");

        // 本人信息查询
        String mobilePhone = null;
        String name = null;
        List<String> idNoList = new ArrayList<>();
        ChoiceInsurePeopleNextStepReq choiceInsurePeople = choiceInsurePeopleNextStepReqs.stream().filter(c -> PlanObjectEnum.STAFF.getCode().equals(c.getPlanObject())).findFirst().orElse(null);
        if (Objects.nonNull(choiceInsurePeople)) {
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(choiceInsurePeople.getPersonid());
            mobilePhone = fcPerson.getMobilePhone();
            name = fcPerson.getName();
            idNoList.add(fcPerson.getIDNo());
        }
        // 校验人员是否存在可投保的弹性计档次
        for (ChoiceInsurePeopleNextStepReq choiceInsurePeopleNextStepReq : choiceInsurePeopleNextStepReqs) {
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(choiceInsurePeopleNextStepReq.getPersonid());
            if (StringUtils.isNotEmpty(fcPerson.getBirthDate()) && DateTimeUtil.getAgeInt(fcPerson.getBirthDate()) < 18 ){
                //未成年人手机号置空
                fcPerson.setMobilePhone("");
                fcPersonMapper.updateJuvenilePhone(fcPerson.getIDNo());
            }
            // 判断家庭成员信息与本人手机信息是否一致
            if (PlanObjectEnum.FAMILY.getCode().equals(choiceInsurePeopleNextStepReq.getPlanObject()) && StringUtil.isNotEmpty(mobilePhone) &&  StringUtil.isNotEmpty(fcPerson.getMobilePhone())) {
                if (mobilePhone.equals(fcPerson.getMobilePhone())) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "[" + MaskUtils.maskPhone(fcPerson.getMobilePhone()) + "] 手机号重复。请确保手机号唯一，无法提供时可忽略家属号码录入");
                    return JSON.toJSONString(resultMap);
                }
                idNoList.add(fcPerson.getIDNo());
                FCPerson checkFcPerson = fcPersonMapper.checkPhoneFcPerson(idNoList,fcPerson.getMobilePhone());
                if (checkFcPerson != null && fcPerson.getMobilePhone().equals(checkFcPerson.getMobilePhone())) {
                    log.info("与系统已有客户PersonID:{}手机号重复",checkFcPerson.getPersonID());
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    //resultMap.put("message", "[" + MaskUtils.maskPhone(fcPerson.getMobilePhone()) + "] 手机号重复。请确保手机号唯一，无法提供时可忽略家属号码录入");
                    resultMap.put("message", "被保险人【"+fcPerson.getName()+"】与系统已有客户【"+checkFcPerson.getName()+"】手机号重复。请确保手机号唯一，请修改或置为空");
                    return JSON.toJSONString(resultMap);
                }
            }
            List<CoreCustomerInfoResDTO> coreCustomerInfoList = coreCustomerService.sameCustomer(CheckSameCustomerConvert.convertFCPerson(fcPerson));
            List<CoreCustomerInfoResDTO> errors = coreCustomerInfoList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getErrorList())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(errors) && errors.get(0) != null &&  CollectionUtils.isNotEmpty(errors.get(0).getErrorList())){
                // 添加错误信息
                String errorInfo = errors.get(0).getErrorList().get(0).getErrorInfo();
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", errorInfo);
                return JSON.toJSONString(resultMap);
            }
            String result = "";
            // 员工校验规则
            if (choiceInsurePeopleNextStepReq.getPlanObject().equals(PlanObjectEnum.STAFF.getCode())) {
                result = eflexEmployList(token, choiceInsurePeopleNextStepReq.getPersonid());
                // 员工家属规则
            } else if (choiceInsurePeopleNextStepReq.getPlanObject().equals(PlanObjectEnum.FAMILY.getCode())) {
                result = eflexEmployFamilyList(token, choiceInsurePeopleNextStepReq.getPersonid(), choiceInsurePeopleNextStepReq.getRelation());
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "计划对象不存在！");
            }
            // 处理结果
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (ObjectUtils.isEmpty(jsonObject.get("data"))) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "当前福利下没有符合被保人" + fcPerson.getName() + "的责任保额档次！");
                return JSON.toJSONString(resultMap);
            }
        }
        return JSON.toJSONString(resultMap);
    }





    /**
     * 校验人员信息是否完整
     *
     * @param fcPerson
     */
    public void checkPerson(FCPerson fcPerson) {
        List<String> errMsgList = new ArrayList<>();
        if ("8".equals(fcPerson.getIDType())) {
            throw new SystemException("证件类型不能为其他，请录入正确的证件类型");
        }
        if (StringUtils.isBlank(fcPerson.getNativeplace())) {
            errMsgList.add("国籍");
        }
        if (StringUtils.isBlank(fcPerson.getJoinMedProtect())) {
            errMsgList.add("有无医保");
        }
        if (errMsgList.size() > 0) {
            throw new SystemException("请先完善" + fcPerson.getName() + "的" + String.join("、", errMsgList) + "信息！");
        }
    }

}