package com.sinosoft.eflex.ctrl.admin;

import com.alibaba.fastjson.JSONObject;
import com.hqins.common.base.ApiResult;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.dao.FCEnsureMapper;
import com.sinosoft.eflex.enums.PlanTypeEnum;
import com.sinosoft.eflex.framework.annotation.SqlInject;
import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.model.GetEnsureAuditsReq;
import com.sinosoft.eflex.model.GetGrpListReq;
import com.sinosoft.eflex.model.HrRegist;
import com.sinosoft.eflex.model.request.RiskConfigReq;
import com.sinosoft.eflex.model.request.ToReviewAdoptRequest;
import com.sinosoft.eflex.model.vo.CheckClientNoVO;
import com.sinosoft.eflex.service.EnsureMakeService;
import com.sinosoft.eflex.service.admin.AuditEnsureService;
import com.sinosoft.eflex.service.admin.EnsureAuditService;
import com.sinosoft.eflex.util.StringUtil;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/11/20
 * 管理员计划定制/审核*
 */
@Api(value = "EnsureAuditController")
@RestController
@RequestMapping("/EnsureAuditController")
@Slf4j
@RequiredArgsConstructor
public class EnsureAuditController {

    private final EnsureAuditService ensureAuditService;
    private final EnsureMakeService ensureMakeService;
    private final FCEnsureMapper fcEnsureMapper;
    private final AuditEnsureService auditEnsureService;

    /**
     * 管理员计划审核信息查询
     *
     * @return
     */
    @ApiOperation(value = "计划审核查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"), @ApiImplicitParam(name = "pageNo", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"), @ApiImplicitParam(name = "pageSize", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "isReal", value = "权限标识", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getEnsureAudits", method = RequestMethod.POST)
    @SqlInject(KeywordPrevent = true)
    public String getEnsureAudits(@ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
                                  @ApiParam(value = "计划审核查询请求参数", required = true) @RequestBody GetEnsureAuditsReq getEnsureAuditsReq) {
        log.info("固定计划审核查询接口入参:" + token + "====:" + JSONObject.toJSONString(getEnsureAuditsReq));
        String responseInfo = ensureAuditService.getEnsureAuditList(token, getEnsureAuditsReq);
        log.info("固定计划审核查询返回报文:" + responseInfo);
        return responseInfo;
    }

    /**
     * 计划审核页面福利保障信息查询
     *
     * @param Authorization
     * @param params
     * @return
     */
    @ApiOperation(value = "福利保障信息查询")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getEnsureConfigList", method = RequestMethod.POST)
    public String getEnsureConfigList(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        log.info("福利保障信息查询接口入参:" + Authorization + "====" + JsonUtil.toJSON(params));
        String responseInfo = ensureAuditService.getEnsureConfigList(Authorization, params);
        log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }

    /**
     * 配置福利保障  医疗机构  其他约定
     *
     * @param Authorization
     * @param params
     * @return
     */
    @ApiOperation(value = "福利审核通过")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header")
    @RequestMapping(value = "/ensureEgis", method = RequestMethod.POST)
    public String ensureEgis(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        log.info("福利审核通过接口入参:" + Authorization + "====" + JsonUtil.toJSON(params));
        String responseInfo = ensureAuditService.ensureEgis(Authorization, params);
        log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }


    /**
     * 工号校验
     */
    @ApiOperation(value = "工号校验")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header")
    @GetMapping(value = "/check")
    public ApiResult<CheckClientNoVO> clientNoCheck(@RequestHeader String Authorization, @Param("clientNo") String clientNo, @Param("grpNo") String grpNo) {
        log.info("EnsureAuditController.clientNoCheck:{},{}", Authorization, clientNo);
        return ApiResult.ok(auditEnsureService.clientNoCheck(Authorization, clientNo, grpNo));
    }


    /**
     * 计划审核页面计划保障信息查询
     *
     * @param Authorization
     * @param params
     * @return
     */
    @ApiOperation(value = "计划保障信息查询")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getPlanConfigList", method = RequestMethod.POST)
    public String getPlanConfigList(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        log.info("计划保障信息查询接口入参:" + Authorization + "====" + JsonUtil.toJSON(params));
        String responseInfo = ensureAuditService.getPlanConfigList(Authorization, params);
        log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "配置计划保障信息")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/insertPlanConfig", method = RequestMethod.POST)
    public String insertPlanConfig(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        log.info("配置计划保障信息接口入参:" + Authorization + "====" + JsonUtil.toJSON(params));
        String responseInfo = ensureAuditService.insertPlanConfig(Authorization, params);
        log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "固定计划审核-险种信息查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNo", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10")
    })
    @RequestMapping(value = "/getPlanRiskList", method = RequestMethod.GET)
    public String getPlanRiskList(@RequestHeader String Authorization, String ensureCode) {
        log.info("固定计划审核-险种信息查询入参:" + Authorization + "====" + ensureCode);
        String responseInfo = ensureAuditService.getPlanRiskList(Authorization, ensureCode);
        log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "固定计划审核-险种信息配置")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header")
    @RequestMapping(value = "/insertRiskConfig", method = RequestMethod.POST)
    public ApiResult<Boolean> insertRiskConfig(@RequestHeader String Authorization, @RequestBody @Valid RiskConfigReq riskConfigReq) {
        return ApiResult.ok(auditEnsureService.updatePlanRisk(Authorization, riskConfigReq));
    }


    /**
     * Eflex002
     * 管理员查看福利信息页面初始化
     *
     * @param ensureCode
     * @return
     */
    @ApiOperation(value = "福利保障信息页面初始化")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "grpNo", value = "企业客户号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customNo", value = "用户编码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isOperation", value = "权限标识符", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/initEnsureInfo", method = RequestMethod.GET)
    public String initEnsureInfo(@RequestHeader String Authorization, String ensureCode, String grpNo, String customNo, String isOperation) {
        log.info("固定计划审核请求报文：Authorization-{}，ensureCode-{}，grpNo-{}，customNo-{}，isOperation-{}", Authorization, ensureCode, grpNo, customNo, isOperation);
        //定义返回报文
        String responseInfo;
        /**
         * 判断固定计划还是弹性计划
         */
        //弹性计划
        if (!StringUtil.isEmpty(ensureCode)) {
            // 查询福利保障信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if (fcEnsure.getPlanType().equals(PlanTypeEnum.EFLEXPLAN.getCode())) {
                responseInfo = ensureMakeService.initEnsureInfoAdmin(Authorization, ensureCode, grpNo, customNo, isOperation);
                log.info("固定计划审核返回报文-弹性计划: {}", responseInfo);
                return responseInfo;
            }
        }
        //固定计划
        responseInfo = ensureMakeService.initEnsureInfoAdmin1(Authorization, ensureCode, grpNo, customNo, isOperation);
        log.info("固定计划审核返回报文-固定计划: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "福利初审回退Hr(固定)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpNo", value = "企业客户号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/auditReturn", method = RequestMethod.POST)
    public String auditReturn(@RequestHeader String Authorization, @RequestBody Map<String, String> params, String grpNo) {
        log.info("福利审核回退接口入参:" + Authorization + "====" + JsonUtil.toJSON(params));
        String responseInfo = ensureAuditService.auditReturn(Authorization, params, grpNo);
        log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }


    /**
     * 被保险人清单查询
     *
     * @return
     */
    @ApiOperation(value = "被保险人清单查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "defaultPlan", value = "默认计划编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "grpNo", value = "企业客户号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "name", value = "姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sex", value = "性别", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDType", value = "证件类型", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDNo", value = "证件号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "nativeplace", value = "国籍", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "nativePlace", value = "国籍", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "rows", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")
    })
    @RequestMapping(value = "/queryStaffAdmin", method = RequestMethod.GET)
    public String queryStaffAdmin(@RequestHeader String Authorization, String defaultPlan, String grpNo, String name, String sex, String iDType, String iDNo, String ensureCode, String nativePlace, int page, int rows) {
        log.info("被保险人清单查询,入参:" + Authorization);
        String responseInfo = ensureMakeService.queryStaffAdmin(Authorization, defaultPlan, grpNo, name, sex, iDType, iDNo, ensureCode, nativePlace, page, rows);
        //log.info("员工清单查询,返回报文:" + responseInfo);
        return responseInfo;
    }

    /**
     * 复核通过
     *
     * @param Authorization        登陆态
     * @param toReviewAdoptRequest 请求信息
     * @return
     */
    @ApiOperation(value = "复核通过")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header")
    @RequestMapping(value = "/toReviewAdopt", method = RequestMethod.POST)
    public ApiResult<Void> toReviewAdopt(@RequestHeader String Authorization, @RequestBody @Valid ToReviewAdoptRequest toReviewAdoptRequest) {
        log.info("福利复核通过接口入参:{},{}", Authorization, JsonUtil.toJSON(toReviewAdoptRequest));
        auditEnsureService.toReviewAdopt(Authorization, toReviewAdoptRequest);
        return ApiResult.ok();
    }

    /**
     * 复核退回
     *
     * @param Authorization
     * @param
     * @return
     */
    @ApiOperation(value = "复核退回初审(固定计划)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "returnReason", value = "退回原因", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/toReviewReturn", method = RequestMethod.GET)
    public String toReviewReturn(@RequestHeader String Authorization, String ensureCode, String returnReason) {
        log.info("回退福利编号:" + ensureCode + "===退回原因:" + returnReason);
        String responseInfo = ensureAuditService.toReviewReturn(Authorization, ensureCode, returnReason);
        log.info("复核退回返回结果:" + responseInfo);
        return responseInfo;
    }

    /**
     * 管理员修改联系人及企业信息
     *
     * @param Authorization
     * @param fchrregisttemp
     * @return
     */
    @ApiOperation(value = "管理员修改联系人及企业信息（数据管理平台 ）")
    @ApiImplicitParams({@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/upGrpAndContactInfo", method = RequestMethod.POST)
    public String upGrpAndContactInfo(@RequestHeader String Authorization, @RequestBody HrRegist fchrregisttemp) {
        log.info("管理员修改联系人及企业信息请求报文: {}", JSONObject.toJSONString(fchrregisttemp));
        String responseInfo = ensureAuditService.upGrpAndContactInfo(Authorization, fchrregisttemp);
        log.info("管理员修改联系人及企业信息返回报文: {}", responseInfo);
        return responseInfo;
    }

    /**
     * 弹性计划定制查询企业信息
     *
     * @param getGrpListReq
     * @return
     */
    @ApiOperation(value = "企业信息查询接口")
    @RequestMapping(value = "/getGrpList", method = RequestMethod.POST)
    public String getGrpList(
            @ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
            @ApiParam(value = "签约申请信息", required = true) @RequestBody GetGrpListReq getGrpListReq) {
        log.info("企业信息查询入参: {}", JSONObject.toJSONString(getGrpListReq));
        String responseInfo = ensureAuditService.getGrpList(token, getGrpListReq);
        log.info("企业信息查询结果:" + responseInfo);
        return responseInfo;
    }

    /**
     * 弹性计划定制 福利设置页面 初始化
     *
     * @param Authorization
     * @param ensureCode
     * @return
     */
    @ApiOperation(value = "弹性计划福利设置页面初始化")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getEnsureInfoByCode", method = RequestMethod.GET)
    public String getEnsureInfoByCode(@RequestHeader String Authorization, String ensureCode) {
        log.info("福利设置初始化入参:" + ensureCode);
        String responseInfo = ensureAuditService.getEnsureInfoByCode(Authorization, ensureCode);
        log.info("福利设置初始化结果:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "获取未添加险种列表信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "riskCode", value = "险种编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "riskName", value = "险种名称", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getRiskInfo", method = RequestMethod.GET)
    public String getRiskInfo(@RequestHeader String Authorization, String ensureCode, String riskCode, String riskName) {
        log.info("获取未添加险种列表信息接口入参：ensureCode" + ensureCode + "======riskCode" + riskCode + "======riskName" + riskName);
        String responseInfo = ensureAuditService.getRiskInfo(ensureCode, riskCode, riskName);
        log.info("获取未添加险种列表信息返回结果:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "获取已添加险种列表信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getAddedRiskInfo", method = RequestMethod.GET)
    public String getAddedRiskInfo(@RequestHeader String Authorization, String ensureCode) {
        log.info("获取已添加险种入参  福利编号:" + ensureCode);
        String responseInfo = ensureAuditService.getAddedRiskInfo(Authorization, ensureCode);
        log.info("获取已添加险种结果:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "获取所选险种责任配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "riskCode", value = "险种编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dutyCode", value = "责任编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getRiskAndDutyInfo", method = RequestMethod.GET)
    public String getRiskAndDutyInfo(@RequestHeader String Authorization, String ensureCode, String riskCode, String dutyCode) {
        log.info("获取所选险种责任信息入参：esureCode:" + ensureCode + "======riskCode:" + riskCode + "======dutyCode:" + dutyCode);
        String responseInfo = ensureAuditService.getRiskAndDutyInfo(Authorization, ensureCode, riskCode, dutyCode);
        log.info("获取所选险种责任信息结果:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "获取所选险种保额档次信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "险种编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "riskCode", value = "险种编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dutyCode", value = "责任编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getDutyAmountGradeInfo", method = RequestMethod.GET)
    public String getDutyAmountGradeInfo(@RequestHeader String Authorization, String ensureCode, String riskCode, String dutyCode) {
        log.info("获取所选险种保额档次信息入参：esureCode:" + ensureCode + "======riskCode:" + riskCode + "======dutyCode:" + dutyCode);
        String responseInfo = ensureAuditService.getDutyAmountGradeInfo(Authorization, ensureCode, riskCode, dutyCode);
        log.info("获取所选险种保额档次信息结果:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划退回（复核）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "returnNode", value = "退回节点", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "returnReason", value = "退回原因", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/efelxEnsureReturn", method = RequestMethod.GET)
    public String efelxEnsureReturn(@RequestHeader String Authorization, String ensureCode, String returnNode, String returnReason) {
        //  returnNode  0--退回HR定制
        log.info("弹性计划复核退回参数：福利编号:" + ensureCode + "====退回节点:" + returnNode + "退回原因:" + returnReason);
        String responseInfo = ensureAuditService.efelxEnsureReturn(Authorization, ensureCode, returnNode, returnReason);
        log.info("弹性计划复核退回结果:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "整单确认--订单查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderStatus", value = "订单状态", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNo", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10")
    })
    @RequestMapping(value = "/getOrderPerInfoByEnsureCode", method = RequestMethod.GET)
    @SqlInject(KeywordPrevent = true)
    public String getOrderPerInfoByEnsureCode(@RequestHeader String Authorization, String ensureCode, String orderStatus, String name, int pageNo, int pageSize) {
        log.info("整单确认--订单查询接口入参 福利编号:" + ensureCode + "======订单状态:" + orderStatus);
        String responseInfo = ensureAuditService.getOrderPerInfoByEnsureCode(Authorization, ensureCode, orderStatus, name, pageNo, pageSize);
        log.info("整单确认--订单查询接口返回结果:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "整单确认--订单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderNo", value = "订单编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "perNo", value = "个人客户号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/queryOrderDetails", method = RequestMethod.GET)
    public String queryOrderDetails(@RequestHeader String Authorization, String ensureCode, String orderNo, String perNo) {
        log.info("整单确认--订单详情接口入参 福利编号:" + ensureCode + "===订单编号:" + orderNo + "===个人客户号:" + perNo);
        String responseInfo = ensureAuditService.queryOrderDetails(Authorization, ensureCode, orderNo, perNo);
        log.info("整单确认--订单详情接口返回结果:" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "整单确认--重置投保日期")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "closeDay", value = "结束日期", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/resetInsureDate", method = RequestMethod.GET)
    public String resetInsureDate(@RequestHeader String Authorization, String ensureCode, String closeDay) {
        log.info("重置投保日期接口入参：福利编号:" + ensureCode + "====结束日期:" + closeDay);
        String responseInfo = ensureAuditService.resetInsureDate(Authorization, ensureCode, closeDay);
        log.info("重置投保日期返回结果:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "整单确认 - - 导出投保清单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderStatus", value = "投保状态", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/eflexExportInsureInfoExcel", method = RequestMethod.GET)
    public String eflexExportInsureInfoExcel(String Authorization, HttpServletResponse response, String ensureCode, String orderStatus) {
        log.info("整单确认--导出投保清单请求报文:" + ensureCode + "===投保状态:" + orderStatus);
        String responseInfo = ensureAuditService.eflexExportInsureInfoExcel(Authorization, response, ensureCode, orderStatus);
        log.info("整单确认--导出投保清单返回报文:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "Hr--整单确认--启用/禁用员工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "lockSign", value = "操作标识", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/updatePerInfoLockState", method = RequestMethod.GET)
    public String updatePerInfoLockState(String Authorization, String ensureCode, @RequestParam("perNo") List<String> perNo, String lockSign) {
        log.info("启用/禁用员工:ensureCode:" + ensureCode + "=====perNo:" + perNo + "=====" + ("0".equals(lockSign) ? "启用" : "禁用"));
        String responseInfo = ensureAuditService.updatePerInfoLockState(Authorization, ensureCode, perNo, lockSign);
        log.info("启用/禁用员工:结果 :" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "Hr--整单确认--未投保人员清单查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "name", value = "姓名", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sex", value = "性别", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "nativeplace", value = "国籍", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "idType", value = "证件类型", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "idNo", value = "证件号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNo", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10")
    })
    @RequestMapping(value = "/queryUnInsuredInfo", method = RequestMethod.GET)
    public String queryUnInsuredInfo(String Authorization, String ensureCode, String name, String sex, String nativeplace, String idType, String idNo, int pageNo, int pageSize) {
        log.info("未投保人员清单查询:福利:" + ensureCode + "==姓名:" + name + "==性别:" + sex + "==国籍:" + nativeplace + "==证件类型:" + idType + "==证件号:" + idNo);
        String responseInfo = ensureAuditService.queryUnInsuredInfo(ensureCode, name, sex, nativeplace, idType, idNo, pageNo, pageSize);
        log.info("未投保人员清单查询:结果:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "Hr--整单确认--导出未投保及禁用人员清单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "name", value = "姓名", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sex", value = "性别", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "nativeplace", value = "国籍", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "idType", value = "证件类型", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "idNo", value = "证件号", required = true, dataType = "String", paramType = "query"),
    })
    @RequestMapping(value = "/exportEmployUnInsurdPerInfo", method = RequestMethod.GET, produces = {"application/vnd.ms-excel; charset=UTF-8"})
    public String exportEmployUnInsurdPerInfo(String ensureCode, String name, String sex, String nativeplace, String idType, String idNo, HttpServletResponse response) {
        log.info("未投保清单导出：EnsureCode:" + ensureCode);
        Map<String, String> requestMap = new HashMap<String, String>(6);
        requestMap.put("ensureCode", ensureCode);
        requestMap.put("name", name);
        requestMap.put("sex", sex);
        requestMap.put("nativeplace", nativeplace);
        requestMap.put("idType", idType);
        requestMap.put("idNo", idNo);
        String responseInfo = ensureAuditService.exportEmployUnInsurdPerInfo(requestMap, response);
        log.info("返回前台数据:" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "签单前校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
    })
    @RequestMapping(value = "/sign-bills-check", method = RequestMethod.GET)
    public ApiResult<List<String>> signBillsCheck(@RequestParam("ensureCode") String ensureCode) {
        log.info("signBillsCheck ensureCode:{}", ensureCode);
        return ApiResult.ok(auditEnsureService.signBillsCheck(ensureCode));
    }


    /**
     * 得到所有的企业
     *
     * @param Authorization
     * @param
     * @return
     */
    @ApiOperation(value = "得到所有的企业")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getGrpInfo", method = RequestMethod.GET)
    public String getGrpInfo(@RequestHeader String Authorization) {
        String responseInfo = ensureAuditService.getGrpInfo(Authorization);
        log.info("前台返回结果:" + responseInfo);
        return responseInfo;
    }

    /**
     * 重置token
     *
     * @param Authorization
     * @param
     * @return
     */
    @ApiOperation(value = "重置企业token")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpNo", value = "企业编号", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/resetToken", method = RequestMethod.GET)
    public String resetToken(@RequestHeader String Authorization, String grpNo) {
        log.info("企业编号:" + grpNo);
        String responseInfo = ensureAuditService.resetToken(Authorization, grpNo);
        log.info("前台返回结果:" + responseInfo);
        return responseInfo;
    }

}