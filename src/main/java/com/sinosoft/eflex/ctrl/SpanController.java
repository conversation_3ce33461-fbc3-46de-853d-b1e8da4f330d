package com.sinosoft.eflex.ctrl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FCGrpInfoMapper;
import com.sinosoft.eflex.dao.FCStaffFamilyRelaMapper;
import com.sinosoft.eflex.dao.FDFTPInfoMapper;
import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.FCStaffFamilyRela;
import com.sinosoft.eflex.model.FDFTPInfo;
import com.sinosoft.eflex.model.HrRegist;
import com.sinosoft.eflex.model.PolicyUpdate.UpdateRequest;
import com.sinosoft.eflex.model.review.Result;
import com.sinosoft.eflex.service.SpanService;
import com.sinosoft.eflex.service.edor.EdorService;
import com.sinosoft.eflex.util.FileUtil;
import com.sinosoft.eflex.util.SFtpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;


/**
 * 第三方接口API
 * <AUTHOR>
 * @date 2018-08-24 17:51
 */
@RestController
@RequestMapping("/hqeflex/services")
@Api(value = "SpanController", description = "第三方接口API")
public class SpanController{

	private static final Logger log = LoggerFactory.getLogger(SpanController.class);
    @Autowired
    private SpanService spanService;
	@Autowired
	private EdorService edorService;
	@Autowired
	private FDFTPInfoMapper fdftpInfoMapper;
	@Autowired
	private FCGrpInfoMapper fcGrpInfoMapper;
	@Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
	@Autowired
    private MyProps myProps;

    @ApiOperation(value="获取五要素信息",notes="获取五要素信息")
    @RequestMapping(value="/getCustomerInfo",method= RequestMethod.GET)
    public String getCustomerInfo(@RequestParam String token){
		log.info("获取五要素信息请求报文=====================："+token);
        String getToken=spanService.getToken(token);
        log.info("返回报文============================："+getToken);
        return  getToken;
    }


	@ApiOperation(value = "代理人同步", notes = "代理人同步")
	@RequestMapping(value = "/syncAgentInfo", method = RequestMethod.POST)
	public String insertAgentInfo(@RequestBody String requestInfo) {
		log.info("代理人同步接口，核心请求报文：" + requestInfo);
		String insertAgentInfo = spanService.addAgentInfo(requestInfo);
		log.info("代理人同步接口，响应报文：" + insertAgentInfo);
		return insertAgentInfo;
	}

    @ApiOperation(value = "承保结果反馈", notes = "承保结果反馈，核心执行完保单打印后请求")
	@RequestMapping(value = "/updatePolicy", method = RequestMethod.POST)
	public String updatePolicy(@RequestBody String backInfo) {
		log.info("承保反馈接口，核心请求报文：" + backInfo);
		String updatePolicy = spanService.updatePolicyStatus(backInfo);
		log.info("承保反馈接口，响应报文：" + updatePolicy);
		return updatePolicy;
	}

    @ApiOperation(value = "批量投保（结果）回调接口", notes = "承保接口-核心异步处理反馈接口")
	@RequestMapping(value = "/insureCallback", method = RequestMethod.POST)
	public String insureCallback(@RequestBody String backInfo) {
		log.info("批量投保（结果）回调接口，核心请求报文：" + backInfo);
		String responseInfo = spanService.insureCallback(backInfo);
		log.info("批量投保（结果）回调接口，响应报文：" + responseInfo);
		return responseInfo;
	}

	@ApiOperation(value = "保全申请结果反馈接口", notes = "保全申请结果反馈接口")
	@RequestMapping(value = "/insuredReponseResult", method = RequestMethod.POST)
	public String insuredReponseResult(@RequestBody String requestInfo){
		log.info("平台接收核心请求报文:"+requestInfo);
		String responseInfo=edorService.insuredReponseResult(requestInfo);
		log.info("核心返回保全申请结果："+responseInfo);
		return responseInfo;
	}
	
	@ApiOperation(value = "支付后台通知接口", notes = "支付后台通知接口")
	@RequestMapping(value = "/payStatusNotice", method = RequestMethod.POST)
	public String payStatusNotice(@RequestBody String requestInfo){
		log.info("支付后台通知接口请求报文:"+requestInfo);
		String responseInfo=spanService.payStatusNotice(requestInfo);
		log.info("支付后台通知接口返回报文："+responseInfo);
		return responseInfo;
	}
	
	@ApiOperation(value = "自然人投保-承保反馈接口", notes = "自然人投保-承保反馈接口")
	@RequestMapping(value = "/insureStatusNotice", method = RequestMethod.POST)
	public String insureStatusNotice(@RequestParam("message") String message) throws IOException {
		log.info("自然人投保-承保反馈接口请求报文:"+message);
		ObjectMapper objectMapper = new ObjectMapper();
	    objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
	    Result<String> result = objectMapper.readValue(message, Result.class);
		String responseInfo=spanService.insureStatusNotice(result);
		log.info("自然人投保-承保反馈接口返回报文："+responseInfo);
		return responseInfo;
	}
	
	@ApiOperation(value = "自然人投保-核保结论通知接口", notes = "自然人投保-核保结论通知接口")
	@RequestMapping(value = "/reviewStatusNotice",method = RequestMethod.POST)
	public String reviewStatusNotice(@RequestParam("message") String message) throws IOException {
		log.info("自然人投保-核保结论通知接口请求报文："+message);
	    ObjectMapper objectMapper = new ObjectMapper();
	    objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
	    Result<String> result = objectMapper.readValue(message, Result.class);
		String responseInfo=spanService.reviewStatusNotice(result);
		log.info("自然人投保-核保结论通知接口返回报文："+responseInfo);
		return responseInfo;
	}

    @ApiOperation(value = "base64转MultipartFile并上传至ftp服务器（企业相关信息）", notes = "base64转MultipartFile并上传至ftp服务器")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unifiedsociCode", value = "统一社会信用代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "grpIdNo", value = "企业证件号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "grpNo", value = "企业客户号", required = false, dataType = "String", paramType = "query") })
	@RequestMapping(value = "/base",method = RequestMethod.GET)
    public void base(String unifiedsociCode, String grpIdNo, String grpNo) {
        // 获取ftp配置信息
        FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
        // 获取文件存放位置
        String ftpFilePath = fdftpInfo.getFtprootpath() + FileUtil.getFtpPath("0305", "");
        // 连接FTP服务器目录
        SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
        sFtp.myProps = myProps;
        /**
         * 修改企业信息表和联系人表的信息
         */
        // 查询临时表企业HR影像件信息
        List<HrRegist> allGrpHrInfo = fcGrpInfoMapper.getAllGrpHrInfo(unifiedsociCode, grpIdNo);
        if (allGrpHrInfo.size() > 0) {
            allGrpHrInfo = sFtp.uploadHrSftpBase(ftpFilePath, allGrpHrInfo);
            fcGrpInfoMapper.updateHrImagePath(allGrpHrInfo);
        }
        // 查询企业影像件信息
        List<FCGrpInfo> allGrpInfo = fcGrpInfoMapper.getAllGrpInfo(grpNo);
        if (allGrpInfo.size() > 0) {
            allGrpInfo = sFtp.uploadSftpBase(ftpFilePath, allGrpInfo);
            fcGrpInfoMapper.updateImagePath(allGrpInfo);
        }
	}

    @ApiOperation(value = "base64转MultipartFile并上传至ftp服务器（家属关系证明-fcstafffamilyrela）", notes = "base64转MultipartFile并上传至ftp服务器")
    @RequestMapping(value = "/dealRelationprove",method = RequestMethod.GET)
    @Transactional
    public void dealRelationprove(){
        //获取ftp配置信息
        FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
        //获取文件存放位置
        String ftpFilePath = fdftpInfo.getFtprootpath() + FileUtil.getFtpPath("0305", "");
        //连接FTP服务器目录
        SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
        // 查询家属关系证明需要替换的图片信息（一次仅查10条，二进制文件较大）
        List<FCStaffFamilyRela> fcStaffFamilyRelaList = fcStaffFamilyRelaMapper.selectBase64RelationProve();
        if(fcStaffFamilyRelaList.size() != 0){
            log.info("本次应处理家属关系影像件: {}条。",fcStaffFamilyRelaList.size());
            //将myProps传入SFtpClientUtil类
            sFtp.myProps = myProps;
            List<FCStaffFamilyRela> fcStaffFamilyRelas = sFtp.uploadRelationproveSftpBase(ftpFilePath, fcStaffFamilyRelaList);
            log.info("本次家属关系影像件成功上传: {}条。",fcStaffFamilyRelas.size());
            //批量修改关系证明信息
            fcStaffFamilyRelaMapper.updateBase64RelationProvePath(fcStaffFamilyRelas);
            log.info("本次家属关系影像件成功修改！");
        }else{
            log.info("本次家属关系影像件无需修改。");
        }
    }


    @ApiOperation(value = "自然人投保-撤件通知接口", notes = "自然人投保-撤件通知接口")
	@RequestMapping(value = "/removeParts",method = RequestMethod.POST)
	public String removeParts(@RequestParam("message") String message) throws IOException {
		log.info("自然人投保-撤件通知接口请求报文："+message);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
		Result<String> result = objectMapper.readValue(message, Result.class);
		String responseInfo=spanService.removeParts(result);
		log.info("自然人投保-撤件通知接口返回报文："+responseInfo);
		return responseInfo;
	}

	@ApiOperation(value = "保全信息同步", notes = "保全信息同步")
	@RequestMapping(value = "/syncPolicyUpdate", method = RequestMethod.POST)
	public String syncPolicyUpdate(@RequestBody UpdateRequest request) {
		log.info("保全信息同步接口，核心请求报文：" + JSON.toJSONString(request));
		String result = spanService.syncPolicyUpdate(request);
		log.info("保全信息同步接口，响应报文：" + result);
		return result;
	}
}
