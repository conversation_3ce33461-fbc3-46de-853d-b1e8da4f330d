package com.sinosoft.eflex.ctrl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sinosoft.eflex.model.ChoiceInsurePeopleNextStepReq;
import com.sinosoft.eflex.model.EflexConfigExplain;
import com.sinosoft.eflex.model.FCMailInfo;
import com.sinosoft.eflex.model.SelectOrderDetailInfoReq;
import com.sinosoft.eflex.model.confirmInsure.ConfirmInsureResp;
import com.sinosoft.eflex.model.confirmInsure.InsureConfirmSubmitReq;
import com.sinosoft.eflex.model.confirmInsure.InsuredConfirmPageReq;
import com.sinosoft.eflex.model.confirmInsure.PeopleInsurePlanInfo;
import com.sinosoft.eflex.model.confirmInsureEflex.EflexConfirmInsureResp;
import com.sinosoft.eflex.model.confirmInsureEflex.PeopleInsureEflexPlanInfo;
import com.sinosoft.eflex.model.confirmInsureEflex.PremTrailReq;
import com.sinosoft.eflex.model.confirmInsureEflex.SelectSelfOrderDetailInfoReq;
import com.sinosoft.eflex.model.insureEflexPlanPage.InsureEflexPlanPageResp;
import com.sinosoft.eflex.model.insurePlanPage.InsurePlanPageReq;
import com.sinosoft.eflex.model.insurePlanPage.InsurePlanPageResp;
import com.sinosoft.eflex.model.insurePlanPage.PeopleZoneData;
import com.sinosoft.eflex.model.sign.apply.SignApply;
import com.sinosoft.eflex.service.InsurePlanPage.InsurePlanService;
import com.sinosoft.eflex.service.InsureService;
import com.sinosoft.eflex.util.rest.util.ResponseResult;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * @DESCRIPTION
 * @create 2018-08-23 15:37
 **/

@RestController
@RequestMapping("InsureController")
@Api(value = "InsureController", description = "个人端投保")
public class InsureController {

    private static Logger Log = LoggerFactory.getLogger(InsureController.class);

    @Autowired
    private InsureService insureService;
    @Autowired
    private InsurePlanService insurePlanService;

    /***************************************************
     * 移动端优化-使用的接口 Start
     *******************************************************/

    @ApiOperation(value = "查询是否存在符合的计划（固定计划）", notes = "选择被保人后点击下一步校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/choiceInsurePeopleNextStep", method = RequestMethod.POST)
    public String choiceInsurePeopleNextStep(@RequestHeader String Authorization,
                                             @RequestBody List<ChoiceInsurePeopleNextStepReq> choiceInsurePeopleNextStepReqs) {
        Log.info("固定计划选择被保人后点击下一步校验接口请求报文: {}", JSONObject.toJSONString(choiceInsurePeopleNextStepReqs));
        String responseInfo = insureService.choiceInsurePeopleNextStep(Authorization, choiceInsurePeopleNextStepReqs);
        Log.info("固定计划选择被保人后点击下一步校验接口返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "投保专区（固定计划）", notes = "投保专区默认计划加载，同时满足上一步的需求")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "insurePlanPage", method = RequestMethod.POST)
    public ResponseResult<InsurePlanPageResp> insurePlanPage(@RequestHeader String Authorization,
                                                             @RequestBody InsurePlanPageReq insurePlanPageReq) {
        Log.info("投保专区页面请求报文: {}", JSONObject.toJSONString(insurePlanPageReq));
        InsurePlanPageResp insurePlanPageResp = insurePlanService.initInsurePlanPage(Authorization, insurePlanPageReq);
        Log.info("投保专区页面返回报文: {}", JSONObject.toJSONString(insurePlanPageResp));
        return ResponseResultUtil.success(insurePlanPageResp);
    }

    @ApiOperation(value = "投保专区-修改单个人的投保计划信息加载（固定计划）", notes = "点击修改时，默认计划信息查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "getInsurePlan", method = RequestMethod.POST)
    public ResponseResult<PeopleZoneData> getInsurePlan(@RequestHeader String Authorization,
                                                        @RequestBody ChoiceInsurePeopleNextStepReq choiceInsurePeopleNextStepReq) {
        Log.info("获取人员投保计划请求报文: {}", JSONObject.toJSONString(choiceInsurePeopleNextStepReq));
        PeopleZoneData peopleZoneData = insurePlanService.getInsurePlan(Authorization, choiceInsurePeopleNextStepReq);
        Log.info("获取人员投保计划返回报文: {}", JSONObject.toJSONString(peopleZoneData));
        return ResponseResultUtil.success(peopleZoneData);
    }


    @ApiOperation(value = "投保确认页面（固定计划）", notes = "投保确认页面信息加载")
    @ApiImplicitParams({@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "insuredConfirmPage", method = RequestMethod.POST)
    public String insuredConfirmPage2(@RequestHeader String Authorization, @RequestBody InsuredConfirmPageReq insuredConfirmPageReq) {
        Log.info("固定计划-获取投保确认页面请求报文:{}", JSONObject.toJSONString(insuredConfirmPageReq));
        if (StringUtils.isNotEmpty(insuredConfirmPageReq.getOrderNo())) {
            ConfirmInsureResp confirmInsureResp = insurePlanService.insuredConfirmPage2(Authorization, insuredConfirmPageReq);
            Log.info("固定计划-获取投保确认页面返回报文: {}", JSONObject.toJSONString(confirmInsureResp));
            return JSONObject.toJSONString(ResponseResultUtil.success(confirmInsureResp));
        } else {
            ConfirmInsureResp confirmInsureResp = insurePlanService.insuredConfirmPage(Authorization, insuredConfirmPageReq);
            Log.info("固定计划-获取投保确认页面返回报文: {}", JSONObject.toJSONString(confirmInsureResp));
            return JSONObject.toJSONString(ResponseResultUtil.success(confirmInsureResp));
        }
    }

    @ApiOperation(value = "确认提交（固定计划）", notes = "确认提交")
    @ApiImplicitParams({@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/insureConfirmSubmit", method = RequestMethod.POST)
    public String insureConfirmSubmit(@RequestHeader String Authorization, @RequestBody InsureConfirmSubmitReq insureConfirmSubmitReq) {
        Log.info("确认提交（固定计划）请求报文: {}", JSONObject.toJSONString(insureConfirmSubmitReq));
        String responseInfo = insurePlanService.insureConfirmSubmit(Authorization, insureConfirmSubmitReq);
        Log.info("确认提交（固定计划）返回报文: {}", responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "保费试算接口-固定计划（选择计划页面动态变化）")
    @ApiImplicitParams({@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "insurePremTrail", method = RequestMethod.POST)
    public String personPermTotal(@RequestHeader String Authorization, @RequestBody List<PeopleInsurePlanInfo> peopleInsurePlanInfos) {
        Log.info("保费试算接口（选择计划页面动态变化）请求报文：token: {}，peopleInsurePlanInfos: {}", Authorization, peopleInsurePlanInfos);
        String responseInfo = insureService.insurePremTrail(Authorization, peopleInsurePlanInfos);
        Log.info("保费试算接口（选择计划页面动态变化）返回报文: {}", responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "查询是否存在符合的档次（弹性计划）", notes = "选择被保人后点击下一步校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/choiceInsurePeopleNextStepByEflex", method = RequestMethod.POST)
    public String choiceInsurePeopleNextStepByEflex(@RequestHeader String Authorization,
                                                    @RequestBody List<ChoiceInsurePeopleNextStepReq> choiceInsurePeopleNextStepReqs) {
        Log.info("弹性计划选择被保人后点击下一步校验接口请求报文: {}", JSONObject.toJSONString(choiceInsurePeopleNextStepReqs));
        String responseInfo = insureService.choiceInsurePeopleNextStepByEflex(Authorization, choiceInsurePeopleNextStepReqs);
        Log.info("弹性计划选择被保人后点击下一步校验接口返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "投保专区（弹性计划）", notes = "投保专区默认计划加载，同时满足上一步的需求")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/insureEflexPlanPage", method = RequestMethod.POST)
    public String insureEflexPlanPage(@RequestHeader String Authorization,
                                      @RequestBody InsurePlanPageReq insurePlanPageReq) {
        Log.info("投保专区页面请求报文: {}", JSONObject.toJSONString(insurePlanPageReq));
        InsureEflexPlanPageResp insureEflexPlanPageResp = insurePlanService.insureEflexPlanPage(Authorization,
                insurePlanPageReq);
        Log.info("投保专区页面返回报文: {}", JSONObject.toJSONString(insureEflexPlanPageResp));
        return JSONObject.toJSONString(ResponseResultUtil.success(insureEflexPlanPageResp),
                SerializerFeature.DisableCircularReferenceDetect);
    }

    @ApiOperation(value = "保额档次信息说明加载（弹性计划）", notes = "获取弹性福利的投保配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "getEflexConfigExplain", method = RequestMethod.POST)
    public ResponseResult<EflexConfigExplain> getEflexConfigExplain(@RequestHeader String Authorization,
                                                                    @RequestBody List<String> amountGradeCodeList) {
        Log.info("获取弹性福利的投保配置信息请求报文: {}", JSONObject.toJSONString(amountGradeCodeList));
        EflexConfigExplain eflexConfigExplain = insureService.getEflexConfigExplain(Authorization, amountGradeCodeList);
        Log.info("获取弹性福利的投保配置信息返回报文: {}", JSONObject.toJSONString(eflexConfigExplain));
        return ResponseResultUtil.success(eflexConfigExplain);
    }

    @ApiOperation(value = "保费明细汇总查询（弹性计划）", notes = "弹性计划保费明细查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")})
    @RequestMapping(value = "insuredEflexPremTotal", method = RequestMethod.GET)
    public String insuredEflexPremTotal(@RequestHeader String Authorization) {
        Log.info("保费明细汇总查询请求入参：" + Authorization);
        String responseInfo = insureService.personEflexPermTotal(Authorization);
        Log.info("保费明细汇总查询返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "确认投保（弹性计划）", notes = "1、校验投保规则 2、保存投保险种保档次信息 ")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/confirmInsureEflex", method = RequestMethod.POST)
    public String confirmInsureEflex(@RequestHeader String Authorization,
                                     @RequestBody List<PeopleInsureEflexPlanInfo> peopleInsureEflexPlanInfos) {
        Log.info("弹性计划-确认投保请求报文: {}", JSONObject.toJSONString(peopleInsureEflexPlanInfos));
        EflexConfirmInsureResp confirmInsureResp = insurePlanService.confirmInsureEflex(Authorization,
                peopleInsureEflexPlanInfos);
        if (!ObjectUtils.isEmpty(confirmInsureResp.getCheckTBResultInfo())) {
            Log.info("弹性计划-确认投保返回报文: {}", JSONObject.toJSONString(confirmInsureResp));
            return JSONObject.toJSONString(ResponseResultUtil.success("300",
                    confirmInsureResp.getCheckTBResultInfo().getCheckTBResultMsg(), confirmInsureResp));
        } else {
            Log.info("弹性计划-确认投保成功！");
            return JSONObject.toJSONString(ResponseResultUtil.success());
        }
    }

    @ApiOperation(value = "投保确认页面（弹性计划）", notes = "投保确认页面信息加载")
    @ApiImplicitParams({@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "insuredConfirmPageEflex", method = RequestMethod.POST)
    public String insuredConfirmPageEflex(@RequestHeader String Authorization) {
        Log.info("弹性计划-获取投保确认页面请求报文: {}", Authorization);
        EflexConfirmInsureResp eflexConfirmInsureResp = insurePlanService.insuredConfirmPageEflex(Authorization);
        Log.info("弹性计划-获取投保确认页面返回报文: {}", JSONObject.toJSONString(eflexConfirmInsureResp));
        return JSONObject.toJSONString(ResponseResultUtil.success(eflexConfirmInsureResp));
    }

    @ApiOperation(value = "个人保费试算（弹性计划）", notes = "选中或变更保额档次时进行保费试算")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/premTrail", method = RequestMethod.POST)
    public ResponseResult<Object> premTrail(@RequestBody PremTrailReq premTrailReq) {
        Log.info("保费试算请求报文: {}", JSONObject.toJSONString(premTrailReq));
        Object prem = insurePlanService.premTrail(premTrailReq);
        Log.info("保费试算返回报文: {}", prem);
        return ResponseResultUtil.success(prem);
    }

    @ApiOperation(value = "我的订单详情信息查看详情查询接口（属于使用之前的旧接口）", notes = "主要包含险种信息内容")
    @PostMapping("/selectSelfOrderDetailInfo")
    public String selectSelfOrderDetailInfo(@RequestHeader String Authorization,
                                            @RequestBody SelectSelfOrderDetailInfoReq selectSelfOrderDetailInfoReq) {
        Log.info("我的订单详情信息查看详情查询接口请求报文: {}", JSONObject.toJSONString(selectSelfOrderDetailInfoReq));
        String responseInfo = insureService.selectSelfOrderDetailInfo(Authorization, selectSelfOrderDetailInfoReq);
        Log.info("我的订单详情信息查看详情查询接口返回报文: {}", responseInfo);
        return responseInfo;
    }

    /*******************************************************
     * 移动端优化-使用的接口 End
     ********************************************************/

    @ApiOperation(value = "查询订单详情信息", notes = "等于投保确认页面信息的查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "selectOrderDetailInfo", method = RequestMethod.POST)
    public ResponseResult<ConfirmInsureResp> selectOrderDetailInfo(@RequestHeader String Authorization,
                                                                   @RequestBody SelectOrderDetailInfoReq selectOrderDetailInfoReq) {
        Log.info("查询订单详情信息请求报文: {}", JSONObject.toJSONString(selectOrderDetailInfoReq));
        ConfirmInsureResp confirmInsureResp = insurePlanService.selectOrderDetailInfo(Authorization,
                selectOrderDetailInfoReq);
        Log.info("查询订单详情信息返回报文: {}", JSONObject.toJSONString(confirmInsureResp));
        return ResponseResultUtil.success(confirmInsureResp);
    }

    @ApiOperation(value = "个人家属投保专区-计划信息查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "planObject", value = "使用人", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "familyInsureByInfo", method = RequestMethod.GET)
    public String familyInsureByInfo(@RequestHeader String Authorization, @RequestParam String planObject, @RequestParam String personId) {
        Log.info("个人家属接口获取计划请求报文:planObject-{}，personId-{}", planObject, personId);
        String responseInfo = insureService.familyInsure(Authorization, planObject, personId);
        Log.info("个人家属接口获取计划返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "个人投保临时暂存保存接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "personInsureSave", method = RequestMethod.GET)
    public String personInsureSave(@RequestHeader String Authorization, @RequestParam String personId, @RequestParam String planCode) {
        Log.info("返回前台入参：" + personId);
        String responseInfo = insureService.saveInsureInfoYUAN(Authorization, personId, planCode);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "个人投保临时暂存修改接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "insurePlanNo", value = "个人投保行为编号", dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "planCode", value = "保障计划编码", dataType = "String", paramType = "body")
    })
    @RequestMapping(value = "personInsureModify", method = RequestMethod.PUT)
    public String personInsureModify(@RequestHeader String Authorization, String insurePlanNo, String planCode) {
        Log.info("返回前台入参：" + insurePlanNo);
        String responseInfo = insureService.updateInsureInfo(Authorization, insurePlanNo, planCode);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "个人投保临时暂存删除接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
    })
    @RequestMapping(value = "personInsureDelete", method = RequestMethod.GET)
    public String personInsureDelete(@RequestHeader String Authorization, @RequestParam String planCode, @RequestParam String personId) {
        Log.info("个人投保临时暂存删除接口请求入参-personid: {}，planCode: {}", personId, planCode);
        String responseInfo = insureService.deleteInsureInfo(Authorization, planCode, personId);
        Log.info("个人投保临时暂存删除接口返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "个人保费汇总接口（仅用于投保后的保费明细汇总）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "personPermTotal", method = RequestMethod.GET)
    public String personPermTotal(@RequestHeader String Authorization) {
        Log.info("返回前台入参：" + Authorization);
        String responseInfo = insureService.personPermTotal(Authorization);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划个人保费汇总接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "personEflexPermTotal", method = RequestMethod.GET)
    public String personEflexPermTotal(@RequestHeader String Authorization) {
        Log.info("返回前台入参：" + Authorization);
        String responseInfo = insureService.personEflexPermTotal(Authorization);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "个人投保确认接口")
    @ApiImplicitParams({@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"), @ApiImplicitParam(name = "verifyCode", value = "短信验证码", required = false, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "isCheck", value = "是否校验付款信息", required = false, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "orderSource", value = "订单来源", required = false, dataType = "String", paramType = "query")})
    @RequestMapping(value = "/personInsureConfirm", method = RequestMethod.POST)
    public String personInsureConfirm(@RequestHeader String Authorization, String verifyCode, String isCheck, String orderSource) {
        Log.info("返回前台入参：" + Authorization + "=======短信验证码：" + verifyCode + "========是否校验付款信息：" + isCheck + "========订单来源：" + orderSource);
        String responseInfo = insureService.personInsureConfirm(Authorization, verifyCode, isCheck, orderSource);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "累计风险保额查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "被保人编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getRiskAmnt", method = RequestMethod.GET)
    public String getRiskAmnt(@RequestHeader String Authorization, String personId) {
        Log.info("getRiskAmnt前台传参数据:" + Authorization + "--" + personId);
        String insuredInfo = insureService.getRiskAmnt(personId);
        Log.info("getRiskAmnt响应结果:" + insuredInfo);
        return insuredInfo;
    }

    @ApiOperation(value = "查询计划信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "planObject", value = "使用人", required = true, dataType = "String", paramType = "query"),
    })
    @RequestMapping(value = "/getPlanInfo", method = RequestMethod.GET)
    public String getPlanInfo(@RequestHeader String Authorization, @RequestParam String planObject, String ensureType) {
        Log.info("接收前台保障计划参数" + planObject);
        String responseInfo = insureService.selectPlanInfo(Authorization, planObject);
        Log.info("返回前台报文" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "查询福利是否存在")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/checkEnsureCodeInfo", method = RequestMethod.GET)
    public String checkEnsureCodeInfo(@RequestHeader String Authorization) {
        String responseInfo = insureService.checkEnsure(Authorization);
        Log.info("返回前台报文" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询计划的险种的详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getRiskinfo", method = RequestMethod.GET)
    public String getRiskinfo(@RequestHeader String Authorization, String planCode, String ensureCode) {
        Log.info("dsd" + planCode);
        String responseInfo = insureService.getRiskinfo(Authorization, planCode, ensureCode);
        Log.info("返回前台报文");
        return responseInfo;
    }

    @ApiOperation(value = "查询投过保后计划的信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getPSPlanInfo", method = RequestMethod.GET)
    public String getPSPlanInfo(@RequestHeader String Authorization, @RequestParam String planObject, @RequestParam String personId) {
        Log.info("详情列表planObject" + planObject + "!!!!!!!!!!!!" + personId);
        String responseInfo = insureService.getPSPlanInfo(Authorization, planObject, personId);
        Log.info("返回前台报文" + responseInfo);
        return responseInfo;
    }

    /**
     * 投保-健康告知
     *
     * @param Authorization
     * @param planCode
     * @return
     */
    @ApiOperation(value = "健康告知")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "投保人Id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planCode", value = "计划编码", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/healthyInform", method = RequestMethod.GET)
    public String healthyInform(@RequestHeader String Authorization, @RequestParam String personId, @RequestParam String planCode, @RequestParam String ensureCode) throws ParseException {
        Log.info("健康告知入参:" + personId + "===" + planCode + "===" + ensureCode);
        String responseInfo = insureService.healthyInform(Authorization, personId, planCode, ensureCode);
        Log.info("健康告知出参:" + responseInfo);
        return responseInfo;
    }


    /**
     * 未成年附带被保险人投保确认函
     *
     * @param Authorization
     * @param planCode
     * @return
     */
    @ApiOperation(value = "未成年附带被保险人投保确认函")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "投保人Id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planCode", value = "计划编码", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/juvenilesConfirmation", method = RequestMethod.GET)
    public String juvenilesConfirmation(@RequestHeader String Authorization, @RequestParam String personId, @RequestParam String planCode) {
        Log.info("接收前台参数:" + personId + "===" + planCode);
        String responseInfo = insureService.juvenilesConfirmation(Authorization, personId, planCode);
        Log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "健康问卷调查")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getflaninform", method = RequestMethod.GET)
    public String getflaninform(@RequestHeader String Authorization, @RequestParam String planCode) {
        Log.info("接收前台参数:" + planCode);
        String responseInfo = insureService.selectflaninform(Authorization, planCode);
        //Log.info("返回前台报文:"+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "移动端查询计划的险种的详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getYDRiskinfo", method = RequestMethod.GET)
    public String getYDRiskinfo(@RequestHeader String Authorization, @RequestParam String planCode, @RequestParam String personId) {
        Log.info("token" + planCode);
        Log.info("personId" + personId);
        String responseInfo = insureService.getYDRiskinfo(Authorization, planCode, personId);
        Log.info("返回前台报文");
        return responseInfo;
    }

    @ApiOperation(value = "个人投保临时保存及修改判断接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "个人编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planCode", value = "保障计划编码", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/personInsureSaveByUpdate", method = RequestMethod.PUT)
    public String personInsureSaveByUpdate(@RequestHeader String Authorization, @RequestParam String personId, @RequestParam String planCode) {
        Log.info("接收前台参数personId:" + personId);
        Log.info("接收前台参数planCode:" + planCode);
        String responseInfo = insureService.getInsureSaveByUpdate(Authorization, personId, planCode);
        Log.info("返回接口报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "判断家属是否投过保")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/isPlanExist", method = RequestMethod.GET)
    public String isPlanExist(@RequestHeader String Authorization) {
        String responseInfo = insureService.isPlanExist(Authorization);
        Log.info("接收返回参数：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "雷达图查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getRiskType", method = RequestMethod.GET)
    public String getRiskType(@RequestHeader String Authorization, @RequestParam String planCode) {
        String responseInfo = insureService.getRiskType(Authorization, planCode);
        Log.info("返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询所投计划险种信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getCheckRisk", method = RequestMethod.POST)
    public String getCheckRisk(@RequestHeader String Authorization, @RequestBody List<Map<String, String>> planCodeInfo) {
        Log.info("接收参数：" + planCodeInfo);
        String responseInfo = insureService.checkRisk(planCodeInfo);
        Log.info("返回参数:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询所投计划信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getPlanList", method = RequestMethod.GET)
    public String getPlanList(@RequestHeader String Authorization, @RequestParam String personId, @RequestParam String choiceRisk) {
        Log.info("接收参数：" + personId);
        String responseInfo = insureService.getPlanList(personId, choiceRisk);
        return responseInfo;
    }

    @ApiOperation(value = "查询个人保险凭证信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getEnsureInfo", method = RequestMethod.GET)
    public String getEnsureInfo(@RequestHeader String Authorization, @RequestParam String personId) {
        Log.info("接收参数：" + personId);
        String responseInfo = insureService.getEnsureInfo(Authorization, personId);
        Log.info("返回参数:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "投保须知", notes = "by:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "保障计划编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getInsureNotes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String getInsureNotes(@RequestHeader String Authorization, @RequestParam(required = false) String ensureCode) {
        String responseInfo = insureService.getInsureNotes(Authorization, ensureCode);
        Log.info("投保须知返回参数" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "投保规则校验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "个人编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planCode", value = "保障计划编码", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "checkTBRules", method = RequestMethod.GET)
    public String checkTBRules(@RequestHeader String Authorization, @RequestParam String personId, @RequestParam String planCode) {
        Log.info("投保规则校验接口请求报文：personId={},planCode={}", personId, planCode);
        String responseInfo = insureService.checkTBRules(Authorization, personId, planCode);
        Log.info("投保规则校验接口出参：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划-投保规则校验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "个人编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "eflexCheckTBRules", method = RequestMethod.POST)
    public String eflexCheckTBRules(@RequestHeader String Authorization, @RequestBody Map<String, Object> map) {
        Log.info("弹性计划-投保规则校验接口请求报文: {}", JSONObject.toJSONString(map));
        String responseInfo = insureService.eflexCheckTBRules(Authorization, map);
        Log.info("弹性计划-投保规则校验接口返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "判断该福利是否有家属计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/isExistFamilyPlan", method = RequestMethod.GET)
    public String isExistFamilyPlan(@RequestHeader String Authorization) {
        Log.info("接收参数：", Authorization);
        String responseInfo = insureService.isExistFamilyPlan(Authorization);
        Log.info("返回参数报文：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "员工申请发票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpOrderNo", value = "保险合同号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/referMailInfo", method = RequestMethod.POST)
    public String referMailInfo(@RequestHeader String Authorization, @RequestBody FCMailInfo fcMailInfo, String grpOrderNo) {
        Log.info("接收参数：" + fcMailInfo + "+" + grpOrderNo);
        String resposeInfo = insureService.referMailInfo(Authorization, fcMailInfo, grpOrderNo);
        Log.info("返回报文：" + resposeInfo);
        return resposeInfo;
    }

    @ApiOperation(value = "查看申请记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getMailInfo", method = RequestMethod.GET)
    public String getMailInfo(@RequestHeader String Authorization, @RequestParam String personId) {
        Log.info("接收请求参数:" + personId);
        String resposeInfo = insureService.getMailInfo(personId, Authorization);
        Log.info("返回参数:" + resposeInfo);
        return resposeInfo;
    }

    @ApiOperation(value = "计划保障说明")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getConfigExplain", method = RequestMethod.GET)
    public String getConfigExplain(@RequestHeader String Authorization, @RequestParam String planCode) {
        Log.info("前台参数" + planCode);
        String resposeInfo = insureService.getConfigExplain(Authorization, planCode);
        Log.info("返回参数:" + resposeInfo);
        return resposeInfo;
    }

    @ApiOperation(value = "电子发票短连接查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpContNo", value = "团体保单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "contNo", value = "个人保单号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/invoiceQuery", method = RequestMethod.GET)
    public String invoiceQuery(@RequestHeader String Authorization, String grpContNo, String ContNo) {
        Log.info("前台传参" + ContNo);
        String resposeInfo = insureService.invoiceQuery(Authorization, grpContNo, ContNo);
        Log.info("返回参数" + resposeInfo);
        return resposeInfo;
    }

    @ApiOperation(value = "电子发票申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ContNo", value = "保单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "PayNo", value = "缴费收据号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/invoiceApply", method = RequestMethod.GET)
    public String invoiceApply(@RequestHeader String Authorization, String ContNo, String PayNo) {
        Log.info("前台传参" + ContNo + "================" + PayNo);
        String resposeInfo = insureService.invoiceApply(Authorization, ContNo, PayNo);
        Log.info("返回参数" + resposeInfo);
        return resposeInfo;
    }

    @ApiOperation(value = "员工家属投保时的校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "个人编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/checkPeople", method = RequestMethod.GET)
    public String checkPeople(@RequestHeader String Authorization, @RequestParam String personId) {
        Log.info("接收请求参数:" + personId);
        String resposeInfo = insureService.checkPeople(Authorization, personId);
        Log.info("返回参数:" + resposeInfo);
        return resposeInfo;
    }

    //查询员工下所有的福利
    @ApiOperation(value = "查询福利列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"), @ApiImplicitParam(name = "grpNo", value = "企业客户号", required = true, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "page", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"), @ApiImplicitParam(name = "rows", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10")})
    @RequestMapping(value = "/selectAllEnsure", method = RequestMethod.GET)
    public String selctAllEnsure(@RequestHeader String Authorization, String grpNo, int page, int rows) {
        Log.info("查询福利列表请求参数: {}", Authorization);
        String resposeInfo = insureService.selctAllEnsure(Authorization, grpNo, page, rows);
        Log.info("查询福利列表返回报文: {}", resposeInfo);
        return resposeInfo;
    }

    @ApiOperation(value = "选中福利信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpNo", value = "企业号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "perNo", value = "对应福利员工编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/selectionEnsure", method = RequestMethod.GET)
    public String selectionEnsure(@RequestHeader String Authorization, @RequestParam String grpNo, @RequestParam String ensureCode, @RequestParam String perNo) {
        Log.info("选中福利信息接口请求报文：grpNo-{}，ensureCode-{}，perNo-{}", grpNo, ensureCode, perNo);
        String responseInfo = insureService.selectionEnsure(Authorization, grpNo, ensureCode, perNo);
        Log.info("选中福利信息接口返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "健康告知提示")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personName", value = "被保人姓名", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/choicePartYes", method = RequestMethod.GET)
    public String choicePartYes(@RequestHeader String Authorization, @RequestParam String personName) {
        Log.info("健康告知选择部分是入参：" + Authorization);
        String responseInfo = insureService.choicePartYes(Authorization, personName);
        Log.info("健康告知选择部分是返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "签约申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "SignApply", value = "签约申请信息", required = true, dataType = "SignApply", paramType = "body")

    })
    @RequestMapping(value = "/signApply", method = RequestMethod.POST)
    public String signApply(@RequestHeader String Authorization, @RequestBody SignApply signApply) {
        String resposeInfo = insureService.signApply(Authorization, signApply);
        Log.info("返回报文：" + resposeInfo);
        return resposeInfo;
    }

    @ApiOperation(value = "带出持卡人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/signApplyMainPerInfo", method = RequestMethod.GET)
    public String signApplyMainPerInfo(@RequestHeader String Authorization) {
        String resposeInfo = insureService.signApplyMainPerInfo(Authorization);
        Log.info("返回报文：" + resposeInfo);
        return resposeInfo;
    }

    @ApiOperation(value = "弹性计划员工投保专区查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "客户编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/eflexEmployList", method = RequestMethod.GET)
    public String eflexEmployList(@RequestHeader String Authorization, @RequestParam String personId) {
        String responseInfo = insureService.eflexEmployList(Authorization, personId);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划单个家属投保专区查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "客户编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "relation", value = "与员工关系", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "eflexEmployFamilyList", method = RequestMethod.GET)
    public String eflexEmployFamilyList(@RequestHeader String Authorization, @RequestParam String personId, @RequestParam String relation) {
        String responseInfo = insureService.eflexEmployFamilyList(Authorization, personId, relation);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划-未成年附带被保险人投保确认函")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/eflexJuvenilesConfirmation", method = RequestMethod.POST)
    public String eflexJuvenilesConfirmation(@RequestHeader String Authorization, @RequestBody Map<String, Object> map) {
        String responseInfo = insureService.eflexJuvenilesConfirmation(Authorization, map);
        Log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划个人投保临时暂存保存接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "eflexPersonInsureSave", method = RequestMethod.POST)
    public String eflexPersonInsureSave(@RequestHeader String Authorization, @RequestBody Map<String, Object> map) {
        Map<String, Object> responseInfo = insureService.eflexPersonInsureSave(Authorization, map);
        Log.info("返回前台报文：" + JSON.toJSONString(responseInfo));
        return JSON.toJSONString(responseInfo);
    }

    @ApiOperation(value = "弹性计划个人投保确认接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderSource", value = "订单来源", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "eflexPersonInsureConfirm", method = RequestMethod.GET)
    public String eflexPersonInsureConfirm(@RequestHeader String Authorization, @RequestParam String orderSource) {
        String responseInfo = insureService.eflexPersonInsureConfirm(Authorization, orderSource);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "我的订单信息查询接口")
    @ApiImplicitParams({@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/selfOrderInfo", method = RequestMethod.GET)
    public String selfOrderInfo(@RequestHeader String Authorization) {
        String responseInfo = insureService.selfOrderInfo(Authorization);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "我的订单详情信息查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "selfOrderDetailInfo", method = RequestMethod.GET)
    public String selfOrderDetailInfo(@RequestHeader String Authorization) {
        String responseInfo = insureService.selfOrderDetailInfo(Authorization);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "我的订单详情信息查看详情查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "客户编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "selfOrderDetailDesInfo", method = RequestMethod.GET)
    public String selfOrderDetailDesInfo(@RequestHeader String Authorization, @RequestParam String personId) {
        String responseInfo = insureService.selfOrderDetailDesInfo(Authorization, personId);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "我的订单详情信息确认接口")
    @ApiImplicitParams({@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"), @ApiImplicitParam(name = "orderNo", value = "订单号", required = false, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "verifyCode", value = "短信验证码", required = false, dataType = "String", paramType = "query"), @ApiImplicitParam(name = "isCheck", value = "是否校验付款信息", required = false, dataType = "String", paramType = "query")})
    @RequestMapping(value = "/selfOrderDetailConfirm", method = RequestMethod.GET)
    public String selfOrderDetailConfirm(@RequestHeader String Authorization, String orderNo, String verifyCode, String isCheck) {
        String responseInfo = insureService.selfOrderDetailConfirm(Authorization, orderNo, verifyCode, isCheck);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划-个人健康告知规则校验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "eflexHealthyInform", method = RequestMethod.POST)
    public String eflexHealthyInform(@RequestHeader String Authorization, @RequestBody Map<String, Object> map) {
        String responseInfo = insureService.eflexHealthyInform(Authorization, map);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "我的查询--订单查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "commitDate1", value = "订单提交日期1", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "commitDate2", value = "订单提交日期2", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/perOrderQuery", method = RequestMethod.GET)
    public String perOrderQuery(@RequestHeader String Authorization, String commitDate1, String commitDate2) {
        Log.info("订单查询入参：订单提交日期：" + commitDate1 + "----------" + commitDate2);
        String responseInfo = insureService.perOrderQuery(Authorization, commitDate1, commitDate2);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "根据必选保额档次编码获取免赔额及赔付比例")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "保额档次编码", value = "amountGrageCode", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getDeductibleCompensationRatioList", method = RequestMethod.GET)
    public String getDeductibleCompensationRatioList(@RequestHeader String Authorization, @RequestParam String amountGrageCode) {
        String responseInfo = insureService.getDeductibleCompensationRatioList(Authorization, amountGrageCode);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "订单修改--更新token")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/updateToken", method = RequestMethod.GET)
    public String updateToken(@RequestHeader String Authorization, String ensureCode) {
        String responseInfo = insureService.updateToken(Authorization, ensureCode);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "我的查询--取消订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderNo", value = "订单编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/canceOrder", method = RequestMethod.GET)
    public String canceOrder(@RequestHeader String Authorization, String orderNo) {
        Log.info("取消订单入参：订单编号：" + orderNo);
        String responseInfo = insureService.canceOrder(Authorization, orderNo);
        Log.info("取消订单结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "投保确认返回--修改订单状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderNo", value = "订单编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/updateOrderState", method = RequestMethod.GET)
    public String updateOrderState(@RequestHeader String Authorization, String orderNo) {
        Log.info("取消订单入参：订单编号：" + orderNo);
        String responseInfo = insureService.updateOrderState(Authorization, orderNo);
        Log.info("取消订单结果：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "弹性计划-获取家属保费合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "eflexGetFamilyTotalPrem", method = RequestMethod.POST)
    public String eflexGetFamilyTotalPrem(@RequestHeader String Authorization, @RequestBody Map<String, Object> map) {
        String responseInfo = insureService.eflexGetFamilyTotalPrem(Authorization, map);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询所投计划险种信息(二期)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "被保人ID", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getRiskInfos", method = RequestMethod.POST)
    public String getRiskInfos(@RequestHeader String Authorization, @RequestBody List<Map<String, String>> planCodeInfo, String personId) {
        Log.info("接收参数：" + planCodeInfo + "+" + personId);
        String responseInfo = insureService.getRiskInfos(planCodeInfo, personId);
        Log.info("返回参数:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "福利查询(二期)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "被保人ID", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getEnsureList", method = RequestMethod.GET)
    public String getEnsureList(@RequestHeader String Authorization, String personId) {
        Log.info("接收参数：" + personId);
        String responseInfo = insureService.getEnsureList(Authorization, personId);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "福利查询(移动端)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "被保人ID", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getPhoneEnsureList", method = RequestMethod.GET)
    public String getPhoneEnsureList(@RequestHeader String Authorization, String personId) {
        Log.info("接收参数:{}", personId);
        String responseInfo = insureService.getPhoneEnsureList(personId);
        Log.info("getPhoneEnsureList response:{}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询个人保险凭证详情（二期）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getInsureDetail", method = RequestMethod.GET)
    public String getInsureDetail(@RequestHeader String Authorization, @RequestParam String personId) {
        Log.info("接收参数：" + personId);
        String responseInfo = insureService.getInsureDetail(Authorization, personId);
        Log.info("返回参数:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划-删除家属暂存档次")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "客户编号", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/deleteElfexFamilyAmount", method = RequestMethod.GET)
    public String deleteElfexFamilyAmount(@RequestHeader String Authorization, String personId) {
        Log.info("接收参数：" + personId);
        String responseInfo = insureService.deleteElfexFamilyAmount(Authorization, personId);
        Log.info("返回参数:" + responseInfo);
        return responseInfo;
    }


}
