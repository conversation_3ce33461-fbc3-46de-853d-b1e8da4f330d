package com.sinosoft.eflex.framework.exception;

/**
 * <AUTHOR>
 */

public enum EFlexServiceExceptionEnum {

    /**
     * 码值
     */
    SUCCESS("00000", 200, "成功"),
    SUCCESS1("000000", 200, "成功"),
    UN_LOGIN("A0001", 401, "用户未登录"),
    RISK_CONFIG_ERROR("A0002", 401, "由于福利下险种riskCode存在保费不为0的数据，该险种不能设置为赠险！"),
    RISK_CONFIG_ZERO("A0003", 401, "由于福利下险种riskCode保费全部为0，该险种赠险标记应设置为“是”。！"),
    PLAN_NOT_EXIST("A0004", 401, "该计划不存在！"),
    NOT_ONE_EMPLOYEE("A0005", 401, "请您至少导入一个员工计划！"),
    NOT_ONE("A0006", 401, "请您至少导入一个EnsureType"),
    PER_NAME_ERROR("A0007", 401, "perName的计划编码有误，请修改！"),
    PROTOCOL_READ_ERROR("A0008", 401, "请先完成所有险种和条款的阅读！"),
    ID_CARD_ERROR("A0009", 401, "公安二要素异常！"),
    GRP_ORDER_NULL_ERROR("A0010", 401, " 未查询到该福利相关团单，请确认后福利编码无误后再次签单!"),
    POLICY_STATE_ERROR("A0011", 401, "当前福利已存在签单记录，不可再次签单!"),
    ORIGINAL_PREM_ERROR("A0012", 401, "原保费不能为空！"),
    SQL_INJECT_ERROR("A0022", 401, "请求参数异常！"),
    PER_INFO_ERROR("A0023", 200, "企业信息查询异常"),

    RISK_DELIST_ERROR("A0032", 401, "险种已下架！"),
    HEALTH_RECHECK_ERROR("A0033", 401, "该福利健康告知方案未复核完成，不能提交复核！"),
    HEALTH_REPEAT_ERROR("A0034", 401, "该福利下存在多个健康告知方案！"),
    BANK_NULL_ERROR("A0035", 401, "该福利为混合缴费方式,{name}下开户账号为空 不可转默认计划！"),
    NOT_ONE_RISK_ERROR("A0036", 401, "至少选择录入一条险种信息！"),
    NUM_OR_LETTER_ERROR("A0037", 401, "仅允许录入大于0的正整数或大写单个字母！"),
    SMALL_TO_BIG_ERROR("A0038", 401, "请从小到大，按顺序录入计划编号！"),
    LETTER_ERROR("A0039", 401, "请按照从A-Z顺序录入！"),
    NUM_LETTER_ERROR("A0040", 401, "计划编码只能为数字或字母，不支持混合编码，请按照已添加计划的编码类型填写计划编码！"),
    PLAN_REPEAT_ERROR("A0041", 401, "已存在相同的计划编码，请修改！"),
    NOT_ONE_DUTY_ERROR("A0042", 401, "险种配置失败:请至少配置一个险种责任"),
    SAME_CUSTOMER_ERROR("A0043", 401, "核心重复客户信息查询失败!"),
    SIGN_APPLY_ERROR("A0044", 401, "签约申请接口请求验证码发送失败!"),
    SIGN_CONFIRM_ERROR("A0045", 401, "中台签约确认接口失败!"),
    SIGN_CONFIRM_CODE_ERROR("A0046", 401, "请先发送短信验证码!"),
    NATIONALITY_INVALID("A00047", 400, "请输入正确的国籍"),
    CONTACT_ERROR("A00048", 400, "经办人信息异常"),
    SIGN_IN_ERROR("A00049", 400, "该手机号已注册！"),
    CONTACT_NULL_ERROR("A00050", 400, "缺少经办人或存在多个经办人，请在初审岗操作界面，授权经办人管理功能下设置企业的经办人"),


    ;


    private final String code;
    private final int httpStatus;
    private final String message;

    EFlexServiceExceptionEnum(String code, int httpStatus, String message) {
        this.code = code;
        this.httpStatus = httpStatus;
        this.message = message;
    }

    public int getHttpStatus() {
        return httpStatus;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
