<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCStaffFamilyRelaMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCStaffFamilyRela">
        <id column="PerNo" jdbcType="VARCHAR" property="perNo"/>
        <id column="PersonID" jdbcType="VARCHAR" property="personID"/>
        <result column="Relation" jdbcType="VARCHAR" property="relation"/>
        <result column="RelationProve" jdbcType="VARCHAR" property="relationProve"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        PerNo
        , PersonID, Relation, RelationProve, Operator,
		OperatorCom, MakeDate,
		MakeTime,
		ModifyDate, ModifyTime
    </sql>
    <!-- 导出计划清单excel -->
    <select id="selectInsuredDetail" parameterType="java.util.Map"
            resultMap="BaseResultMap">
        select Relation
        from fcstafffamilyrela
        where PersonID = #{personID,jdbcType=VARCHAR}
          and Perno = #{perNo,jdbcType=VARCHAR}
    </select>
    <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        fcstafffamilyrela
        where PerNo = #{perNo,jdbcType=VARCHAR}
        and
        PersonID = #{personID,jdbcType=VARCHAR}
        <if test="Relation != null and Relation !='' ">
            AND Relation = #{Relation,jdbcType=VARCHAR}
        </if>
    </select>

    <select id='selectRelaSameStaff' parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcstafffamilyrela where
        personid in (select personid from fcperson where idno=(select idno from fcperson where personid=#{personID}))
        and perno=#{perNo} limit 1
    </select>

    <select id="selectRelaSameStaffOne" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FCStaffFamilyRela">
        select
        <include refid="Base_Column_List"/>
        FROM fcstafffamilyrela
        WHERE perno IN (SELECT perno FROM fcperinfo WHERE idno IN (SELECT idno FROM fcperinfo WHERE
        perno=#{perNo,jdbcType=VARCHAR} and personid=#{personid,jdbcType=VARCHAR}))
    </select>

    <delete id="deleteByPrimaryKey" parameterType="map">
        delete
        from fcstafffamilyrela
        where PerNo = #{perNo,jdbcType=VARCHAR}
          and PersonID =
              #{personID,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCStaffFamilyRela">
        insert into fcstafffamilyrela
        (PerNo, PersonID, Relation,
         RelationProve, Operator, OperatorCom,
         MakeDate, MakeTime, ModifyDate,
         ModifyTime)
        values (#{perNo,jdbcType=VARCHAR}, #{personID,jdbcType=VARCHAR},
                #{relation,jdbcType=VARCHAR},
                #{relationProve,jdbcType=VARCHAR},
                #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR},
                #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
                #{modifyDate,jdbcType=DATE},
                #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCStaffFamilyRela">
        insert into fcstafffamilyrela
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="perNo != null">
                PerNo,
            </if>
            <if test="personID != null">
                PersonID,
            </if>
            <if test="relation != null">
                Relation,
            </if>
            <if test="relationProve != null">
                RelationProve,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="perNo != null">
                #{perNo,jdbcType=VARCHAR},
            </if>
            <if test="personID != null">
                #{personID,jdbcType=VARCHAR},
            </if>
            <if test="relation != null">
                #{relation,jdbcType=VARCHAR},
            </if>
            <if test="relationProve != null">
                #{relationProve,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCStaffFamilyRela">
        update fcstafffamilyrela
        <set>
            <if test="relation != null">
                Relation = #{relation,jdbcType=VARCHAR},
            </if>
            <if test="relationProve != null">
                RelationProve = #{relationProve,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where PerNo = #{perNo,jdbcType=VARCHAR}
        and PersonID =
        #{personID,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCStaffFamilyRela">
        update
            fcstafffamilyrela
        set Relation      = #{relation,jdbcType=VARCHAR},
            RelationProve = #{relationProve,jdbcType=VARCHAR},
            Operator      =
                #{operator,jdbcType=VARCHAR},
            OperatorCom   =
                #{operatorCom,jdbcType=VARCHAR},
            MakeDate      = #{makeDate,jdbcType=DATE},
            MakeTime      = #{makeTime,jdbcType=VARCHAR},
            ModifyDate    =
                #{modifyDate,jdbcType=DATE},
            ModifyTime    =
                #{modifyTime,jdbcType=VARCHAR}
        where PerNo = #{perNo,jdbcType=VARCHAR}
          and PersonID = #{personID,jdbcType=VARCHAR}
    </update>
    <delete id="deletePersonId" parameterType="java.lang.String">
        delete
        from fcstafffamilyrela
        where PersonID = #{personId,jdbcType=VARCHAR}
    </delete>

    <select id="selectPersonIdInfo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FCStaffFamilyRela">
        select
        <include refid="Base_Column_List"/>
        from fcstafffamilyrela
        where PersonID =#{personId,jdbcType=VARCHAR}
    </select>

    <select id="selectPersonIdInfos" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FCStaffFamilyRela">
        select
        <include refid="Base_Column_List"/>
        from fcstafffamilyrela
        where PersonID =#{personId,jdbcType=VARCHAR}
    </select>

    <select id="selectByPerNo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FCStaffFamilyRela">
        select
        <include refid="Base_Column_List"/>
        from fcstafffamilyrela
        where PerNo = #{perNo,jdbcType=VARCHAR} and Relation = '0'
    </select>

    <select id="selectByPerNo1">
        select
        <include refid="Base_Column_List"/>
        from fcstafffamilyrela
        where PerNo = #{perNo,jdbcType=VARCHAR} and Relation != '0'
    </select>

    <update id="updateStaffFamilyRela">
        update fcstafffamilyrela
        set Relation   =
                #{relation,jdbcType=VARCHAR},
            ModifyDate = #{modifyDate,jdbcType=DATE},
            ModifyTime = #{modifyTime,jdbcType=VARCHAR}
        where PersonID = #{personID,jdbcType=VARCHAR}
          and PerNo = #{perNo,jdbcType=VARCHAR}

    </update>

    <select id="selectStaffIdentityCount" parameterType="com.sinosoft.eflex.model.FCStaffFamilyRela"
            resultType="java.lang.Integer">
        select count(*)
        from fcstafffamilyrela
        where PersonID = #{personID,jdbcType=VARCHAR}
          and PerNo != #{perNo,jdbcType=VARCHAR}
          and Relation = '0'
    </select>


    <select id="getPersonByInfo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FCStaffFamilyRela">
        select
        <include refid="Base_Column_List"/>
        from fcstafffamilyrela
        where PerNo = #{perNo,jdbcType=VARCHAR}
    </select>

    <select id="getPersonByInsurePlan" parameterType="java.util.Map"
            resultType="com.sinosoft.eflex.model.FCStaffFamilyRela">
        SELECT *
        FROM fcstafffamilyrela
        WHERE PerNo = #{perNo,jdbcType=VARCHAR}
    </select>

    <!-- AND PersonID NOT IN (SELECT PersonID FROM fpinsureplan WHERE EnsureCode = #{ensureCode,jdbcType=VARCHAR}) -->

    <update id="updateFamilyRelaByIDNo" parameterType="java.util.List">
        <if test="list!=null">
            <foreach collection="list" item="FamilyRela" index="index" open="" close="" separator=";">
                update fcstafffamilyrela fr
                set fr.`RelationProve` = if(fr.`RelationProve` is null,#{FamilyRela.RelationProve},fr.`RelationProve`)
                WHERE fr.PersonID=(SELECT personid FROM fcperson WHERE IDNo = #{FamilyRela.IDNo})
            </foreach>
        </if>
    </update>

    <select id='selectStaffCount' parameterType="map" resultType="java.lang.String">
        select personid
        from fcstafffamilyrela a
        where a.personid in
              (select personid from fcperson where idno in (select idno from fcperson where personid = #{personId}))
          and perno = #{perNo}
    </select>

    <select id="selectCountPerson" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from fcstafffamilyrela a
        select count(*)
        from fcstafffamilyrela a
                 LEFT JOIN fcperregistday b on a.perno = b.perno
        where b.ensurecode = #{ensureCode}
    </select>
    <select id="selectStaffPersonid" parameterType="java.lang.String" resultType="java.lang.String">
        select personid
        from fcstafffamilyrela
        where perno = #{perNo}
          and relation = '0' limit 1
    </select>
    <select id="selectBase64RelationProve" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcstafffamilyrela where relationprove LIKE concat(concat('data','%')) limit 10
    </select>
    <select id="updateBase64RelationProvePath" parameterType="java.util.List" resultType="java.lang.Integer">
        <foreach collection="list" item="item" index="index" separator=";">
            update fcstafffamilyrela
            <set>
                <if test="item.relationProve != null">
                    relationProve = #{item.relationProve}
                </if>
            </set>
            where PerNo = #{item.perNo}
            and PersonID = #{item.personID}
        </foreach>
    </select>
</mapper>