<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcPlanRiskInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcPlanRiskInfo">
    <id column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <id column="RiskCode" jdbcType="VARCHAR" property="riskCode" />
    <id column="RiskType" jdbcType="VARCHAR" property="riskType" />
    <result column="RiskName" jdbcType="VARCHAR" property="riskName" />
    <result column="RiskRange" jdbcType="VARCHAR" property="riskRange" />
    <result column="reinsuranceMark" jdbcType="VARCHAR" property="reinsuranceMark" />
    <result column="feeRatio" jdbcType="DOUBLE" property="feeRatio" />
    <result column="commissionOrAllowanceRatio" jdbcType="DOUBLE" property="commissionOrAllowanceRatio" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
    <collection property="fcDutyAmountGradeList" ofType="com.sinosoft.eflex.model.FcDutyAmountGrade" column="DutyInfo">
      <id column="AmountGrageCode" jdbcType="VARCHAR" property="amountGrageCode"/>
      <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
      <result column="RiskCode" jdbcType="VARCHAR" property="riskCode" />
      <result column="RiskType" jdbcType="VARCHAR" property="riskType" />
      <result column="DutyCode" jdbcType="VARCHAR" property="dutyCode" />
      <result column="DutyName" jdbcType="VARCHAR" property="dutyName" />
      <result column="DutyRange" jdbcType="VARCHAR" property="dutyRange" />
      <result column="DutyType" jdbcType="VARCHAR" property="dutyType" />
      <result column="AmountGrageName" jdbcType="VARCHAR" property="amountGrageName" />
      <result column="Prem" jdbcType="DOUBLE" property="prem" />
      <result column="Amnt" jdbcType="DOUBLE" property="amnt" />
      <result column="DiscountRatio" jdbcType="DOUBLE" property="discountRatio" />
      <result column="AnnualTimeDeduction" jdbcType="VARCHAR" property="annualTimeDeduction" />
      <result column="WaitingPeriod" jdbcType="DOUBLE" property="waitingPeriod" />
      <result column="MaxGetDay" javaType="DECIMAL" property="maxGetDay"  />
      <result column="SpecialAgreement" jdbcType="VARCHAR" property="specialAgreement" />
    </collection>
  </resultMap>
  <sql id="Base_Column_List">
    EnsureCode, RiskCode, RiskType, reinsuranceMark, feeRatio, commissionOrAllowanceRatio,
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>

  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcplanriskinfo
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    <if test="riskCode != null and riskCode != ''">
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
    </if>
    <if test="riskType != null and riskType != ''">
      and RiskType = #{riskType,jdbcType=VARCHAR}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcplanriskinfo
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      <if test=" riskCode != null and riskCode != ''">
        and RiskCode = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test=" riskType != null and riskType != ''">
        and RiskType = #{riskType,jdbcType=VARCHAR}
      </if>
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FcPlanRiskInfo">
    insert into fcplanriskinfo (EnsureCode, RiskCode, RiskType, 
      reinsuranceMark, feeRatio, commissionOrAllowanceRatio, 
      Operator, OperatorCom, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{ensureCode,jdbcType=VARCHAR}, #{riskCode,jdbcType=VARCHAR}, #{riskType,jdbcType=VARCHAR}, 
      #{reinsuranceMark,jdbcType=VARCHAR}, #{feeRatio,jdbcType=DOUBLE}, #{commissionOrAllowanceRatio,jdbcType=DOUBLE},
      #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcPlanRiskInfo">
    insert into fcplanriskinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ensureCode != null">
        EnsureCode,
      </if>
      <if test="riskCode != null">
        RiskCode,
      </if>
      <if test="riskType != null">
        RiskType,
      </if>
      <if test="reinsuranceMark != null">
        reinsuranceMark,
      </if>
      <if test="feeRatio != null">
        feeRatio,
      </if>
      <if test="commissionOrAllowanceRatio != null">
        commissionOrAllowanceRatio,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ensureCode != null">
        #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="riskType != null">
        #{riskType,jdbcType=VARCHAR},
      </if>
      <if test="reinsuranceMark != null">
        #{reinsuranceMark,jdbcType=VARCHAR},
      </if>
      <if test="feeRatio != null">
        #{feeRatio,jdbcType=DOUBLE},
      </if>
      <if test="commissionOrAllowanceRatio != null">
        #{commissionOrAllowanceRatio,jdbcType=DOUBLE},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcPlanRiskInfo">
    update fcplanriskinfo
    <set>
      <if test="reinsuranceMark != null">
        reinsuranceMark = #{reinsuranceMark,jdbcType=VARCHAR},
      </if>
      <if test="feeRatio != null">
        feeRatio = #{feeRatio,jdbcType=DOUBLE},
      </if>
      <if test="commissionOrAllowanceRatio != null">
        commissionOrAllowanceRatio = #{commissionOrAllowanceRatio,jdbcType=DOUBLE},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
      and RiskType = #{riskType,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcPlanRiskInfo">
    update fcplanriskinfo
    set reinsuranceMark = #{reinsuranceMark,jdbcType=VARCHAR},
      feeRatio = #{feeRatio,jdbcType=DOUBLE},
      commissionOrAllowanceRatio = #{commissionOrAllowanceRatio,jdbcType=DOUBLE},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
      and RiskType = #{riskType,jdbcType=VARCHAR}
  </update>
  <update id="updateByRiskCode" parameterType="com.sinosoft.eflex.model.FcPlanRiskInfo">
    update fcplanriskinfo
    <set>
      <if test="reinsuranceMark != null">
        reinsuranceMark = #{reinsuranceMark,jdbcType=VARCHAR},
      </if>
      <if test="feeRatio != null">
        feeRatio = #{feeRatio,jdbcType=DOUBLE},
      </if>
      <if test="commissionOrAllowanceRatio != null">
        commissionOrAllowanceRatio = #{commissionOrAllowanceRatio,jdbcType=DOUBLE},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    and RiskCode = #{riskCode,jdbcType=VARCHAR}
  </update>



  <select id="getAddedRiskInfo" parameterType="java.util.Map" resultMap="BaseResultMap">
    select s.EnsureCode, s.RiskCode, s.RiskType, s.reinsuranceMark,(s.feeRatio*100) as feeRatio, (s.commissionOrAllowanceRatio * 100) as commissionOrAllowanceRatio,
    s.Operator, s.OperatorCom, s.MakeDate, s.MakeTime, s.ModifyDate, s.ModifyTime,d.*,r.riskName as riskName,r.riskRange as riskRange  from FcPlanRiskInfo s
     INNER JOIN FcDutyAmountGrade d on s.ensureCode = d.ensureCode AND s.riskCode = d.riskCode
     INNER JOIN fdriskinfo r on s.riskCode = r.riskCode
     where s.ensureCode = #{ensureCode,jdbcType=VARCHAR}
     <if test="isCheck == null">
       and s.riskCode not in (15070)
     </if>
  </select>

  <select id="getAddedRiskInfoBy15070" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT s.EnsureCode, s.RiskCode, s.RiskType, s.reinsuranceMark, (s.feeRatio*100) as feeRatio, (s.commissionOrAllowanceRatio * 100) as commissionOrAllowanceRatio,
    s.Operator, s.OperatorCom, s.MakeDate, s.MakeTime, s.ModifyDate, s.ModifyTime,d.*,'综合交通团体意外伤害保险' AS riskName,'1' AS riskRange FROM FcPlanRiskInfo s
    INNER JOIN FcDutyAmountGrade d ON s.RiskCode = d.RiskCode AND  s.RiskType = d.RiskType  AND s.EnsureCode = d.EnsureCode
    WHERE s.EnsureCode = #{ensureCode} AND s.riskCode = '15070'
  </select>

  <delete id="deleteByEnsureCodeRiskCode" parameterType="java.util.Map">
      DELETE FROM FcPlanRiskInfo WHERE EnsureCode = #{ensureCode} AND RiskCode = #{riskCode} AND RiskType = #{riskType,jdbcType=VARCHAR}
  </delete>
  
  <select id="getFeeRatioByEnsureCodeAndRiskCode" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcPlanRiskInfo">
    SELECT reinsuranceMark,(feeRatio*100) as feeRatio,(commissionOrAllowanceRatio*100) as commissionOrAllowanceRatio FROM fcplanriskinfo WHERE EnsureCode = #{ensureCode} AND RiskCode = #{riskCode} LIMIT 1
  </select>

  <select id="getFeeRatioByEnsureCodeAndRiskCodeByInsureSign" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcPlanRiskInfo">
    SELECT reinsuranceMark,feeRatio,commissionOrAllowanceRatio FROM fcplanriskinfo WHERE EnsureCode = #{ensureCode} AND RiskCode = #{riskCode} LIMIT 1
  </select>
  <select id="selectStopSaleRiskCodeByEnsureCode" resultType="java.lang.String">
      select DISTINCT a.RiskCode from FcPlanRiskInfo a inner join fdriskInfo b on a.RiskCode=b.riskCode and b.StopTime &lt; NOW()
      where a.ensureCode=#{ensureCode};
  </select>
    <select id="selectRiskInfoByEnsureCode" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.insureEflexPlanPage.InsureRiskInfo">
       select
	     DISTINCT a.RiskCode,b.riskName
        from FcPlanRiskInfo a
	    inner join fdriskInfo b on a.RiskCode=b.riskCode
        where a.ensureCode = #{ensureCode};
  </select>
</mapper>