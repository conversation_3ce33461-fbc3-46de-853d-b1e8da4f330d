<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCBatchPayBankInfoMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcBatchPayBankInfo">
        <id column="EnsureCode" jdbcType="VARCHAR" property="ensureCode"/>
        <id column="PerNo" jdbcType="VARCHAR" property="perNo"/>
        <result column="SignSN" jdbcType="VARCHAR" property="signSN"/>
        <result column="OrderNo" jdbcType="VARCHAR" property="orderNo"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="IdType" jdbcType="VARCHAR" property="idType"/>
        <result column="IdNo" jdbcType="VARCHAR" property="idNo"/>
        <result column="BankAccount" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="BankAccCode" jdbcType="VARCHAR" property="bankAccCode"/>
        <result column="TransId" jdbcType="VARCHAR" property="transId"/>
        <result column="PayBankCode" jdbcType="VARCHAR" property="payBankCode"/>
        <result column="ReservePhone" jdbcType="VARCHAR" property="reservePhone"/>
        <result column="IsSidned" jdbcType="VARCHAR" property="isSidned"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        EnsureCode
        , PerNo, SignSN, OrderNo, Name, IdType, IdNo, BankAccount, PayBankCode,TransId,bankAccCode,
    ReservePhone, IsSidned, Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcbatchpaybankinfo
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and PerNo = #{perNo,jdbcType=VARCHAR}
        and IsSidned = 'Y'
    </select>
    <delete id="deleteByPrimaryKey" parameterType="map">
        delete
        from fcbatchpaybankinfo
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PerNo = #{perNo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FcBatchPayBankInfo">
        insert into fcbatchpaybankinfo (EnsureCode, PerNo, SignSN,
                                        OrderNo, Name, IdType,
                                        IdNo, BankAccount, PayBankCode,
                                        ReservePhone, IsSidned, Operator,
                                        OperatorCom, MakeDate, MakeTime,
                                        ModifyDate, ModifyTime)
        values (#{ensureCode,jdbcType=VARCHAR}, #{perNo,jdbcType=VARCHAR}, #{signSN,jdbcType=VARCHAR},
                #{orderNo,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{idType,jdbcType=VARCHAR},
                #{idNo,jdbcType=VARCHAR}, #{bankAccount,jdbcType=VARCHAR}, #{payBankCode,jdbcType=VARCHAR},
                #{reservePhone,jdbcType=VARCHAR}, #{isSidned,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
                #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
                #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcBatchPayBankInfo">
        insert into fcbatchpaybankinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                EnsureCode,
            </if>
            <if test="perNo != null">
                PerNo,
            </if>
            <if test="signSN != null">
                SignSN,
            </if>
            <if test="orderNo != null">
                OrderNo,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="idType != null">
                IdType,
            </if>
            <if test="idNo != null">
                IdNo,
            </if>
            <if test="bankAccount != null">
                BankAccount,
            </if>
            <if test="payBankCode != null">
                PayBankCode,
            </if>
            <if test="reservePhone != null">
                ReservePhone,
            </if>
            <if test="isSidned != null">
                IsSidned,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="perNo != null">
                #{perNo,jdbcType=VARCHAR},
            </if>
            <if test="signSN != null">
                #{signSN,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null">
                #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="payBankCode != null">
                #{payBankCode,jdbcType=VARCHAR},
            </if>
            <if test="reservePhone != null">
                #{reservePhone,jdbcType=VARCHAR},
            </if>
            <if test="isSidned != null">
                #{isSidned,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcBatchPayBankInfo">
        update fcbatchpaybankinfo
        <set>
            <if test="signSN != null">
                SignSN = #{signSN,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                OrderNo = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                IdType = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                IdNo = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null">
                BankAccount = #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="payBankCode != null">
                PayBankCode = #{payBankCode,jdbcType=VARCHAR},
            </if>
            <if test="reservePhone != null">
                ReservePhone = #{reservePhone,jdbcType=VARCHAR},
            </if>
            <if test="isSidned != null">
                IsSidned = #{isSidned,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and PerNo = #{perNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcBatchPayBankInfo">
        update fcbatchpaybankinfo
        set SignSN       = #{signSN,jdbcType=VARCHAR},
            OrderNo      = #{orderNo,jdbcType=VARCHAR},
            Name         = #{name,jdbcType=VARCHAR},
            IdType       = #{idType,jdbcType=VARCHAR},
            IdNo         = #{idNo,jdbcType=VARCHAR},
            BankAccount  = #{bankAccount,jdbcType=VARCHAR},
            PayBankCode  = #{payBankCode,jdbcType=VARCHAR},
            ReservePhone = #{reservePhone,jdbcType=VARCHAR},
            IsSidned     = #{isSidned,jdbcType=VARCHAR},
            Operator     = #{operator,jdbcType=VARCHAR},
            OperatorCom  = #{operatorCom,jdbcType=VARCHAR},
            MakeDate     = #{makeDate,jdbcType=DATE},
            MakeTime     = #{makeTime,jdbcType=VARCHAR},
            ModifyDate   = #{modifyDate,jdbcType=DATE},
            ModifyTime   = #{modifyTime,jdbcType=VARCHAR}
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PerNo = #{perNo,jdbcType=VARCHAR}
    </update>


    <select id="selectFcBatchPayBankInfoByOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcbatchpaybankinfo
        where OrderNo = #{orderNo,jdbcType=VARCHAR}
    </select>

    <select id="selectBatchPayBankInfo" parameterType="com.sinosoft.eflex.model.FcBatchPayBankInfo"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcbatchpaybankinfo
        <where>
            ensureCode = #{ensureCode}
            <if test="perNo != null and perNo != ''">
                and perNo = #{perNo}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and orderNo = #{orderNo}
            </if>
        </where>
    </select>


    <delete id="deleteFcBatchPayBankInfoByOrderNo" parameterType="java.lang.String">
        delete
        from fcbatchpaybankinfo
        where OrderNo = #{orderNo,jdbcType=VARCHAR}
    </delete>
</mapper>