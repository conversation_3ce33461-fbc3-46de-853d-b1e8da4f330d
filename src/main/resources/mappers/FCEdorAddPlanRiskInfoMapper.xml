<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCEdorAddPlanRiskInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCEdorAddPlanRiskInfo">
    <id column="EdorAddPlanSN" jdbcType="VARCHAR" property="edorAddPlanSN" />
    <id column="RiskCode" jdbcType="VARCHAR" property="riskCode" />
    <result column="GrpContNo" jdbcType="VARCHAR" property="grpContNo"/>
    <result column="RiskName" jdbcType="VARCHAR" property="riskName" />
    <result column="RiskAmnt" jdbcType="DECIMAL" property="riskAmnt" />
    <result column="RiskPrem" jdbcType="DECIMAL" property="riskPrem" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    EdorAddPlanSN, RiskCode,GrpContNo, RiskName, RiskAmnt, RiskPrem, Operator, OperatorCom, MakeDate,
    MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcedoraddplanriskinfo
    where EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcedoraddplanriskinfo
    where EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCEdorAddPlanRiskInfo">
    insert into fcedoraddplanriskinfo (EdorAddPlanSN, RiskCode, RiskName, 
      RiskAmnt, RiskPrem, Operator, 
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{edorAddPlanSN,jdbcType=VARCHAR}, #{riskCode,jdbcType=VARCHAR}, #{riskName,jdbcType=VARCHAR}, 
      #{riskAmnt,jdbcType=DECIMAL}, #{riskPrem,jdbcType=DECIMAL}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCEdorAddPlanRiskInfo">
    insert into fcedoraddplanriskinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="edorAddPlanSN != null">
        EdorAddPlanSN,
      </if>
      <if test="riskCode != null">
        RiskCode,
      </if>
      <if test="riskName != null">
        RiskName,
      </if>
      <if test="riskAmnt != null">
        RiskAmnt,
      </if>
      <if test="riskPrem != null">
        RiskPrem,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="edorAddPlanSN != null">
        #{edorAddPlanSN,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="riskName != null">
        #{riskName,jdbcType=VARCHAR},
      </if>
      <if test="riskAmnt != null">
        #{riskAmnt,jdbcType=DECIMAL},
      </if>
      <if test="riskPrem != null">
        #{riskPrem,jdbcType=DECIMAL},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCEdorAddPlanRiskInfo">
    update fcedoraddplanriskinfo
    <set>
      <if test="riskName != null">
        RiskName = #{riskName,jdbcType=VARCHAR},
      </if>
      <if test="riskAmnt != null">
        RiskAmnt = #{riskAmnt,jdbcType=DECIMAL},
      </if>
      <if test="riskPrem != null">
        RiskPrem = #{riskPrem,jdbcType=DECIMAL},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCEdorAddPlanRiskInfo">
    update fcedoraddplanriskinfo
    set RiskName = #{riskName,jdbcType=VARCHAR},
      RiskAmnt = #{riskAmnt,jdbcType=DECIMAL},
      RiskPrem = #{riskPrem,jdbcType=DECIMAL},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
  </update>

  <insert id="insertEdorAddPlanRiskList" parameterType="java.util.List">
    insert into fcedoraddplanriskinfo
    (EdorAddPlanSN,RiskCode,GrpContNo,RiskName,RiskAmnt,RiskPrem,Operator,OperatorCom,MakeDate,MakeTime,ModifyDate,ModifyTime)
    VALUES <foreach collection="list"  item="item" index="index" open="" close="" separator=",">
    (#{list[${index}].edorAddPlanSN,jdbcType=VARCHAR},
    #{list[${index}].riskCode,jdbcType=VARCHAR},
    #{list[${index}].grpContNo,jdbcType=VARCHAR},
    #{list[${index}].riskName,jdbcType=VARCHAR},
    #{list[${index}].riskAmnt,jdbcType=DECIMAL},
    #{list[${index}].riskPrem,jdbcType=DECIMAL},
    #{list[${index}].operator,jdbcType=VARCHAR},
    #{list[${index}].operatorCom,jdbcType=VARCHAR},
    #{list[${index}].makeDate,jdbcType=DATE},
    #{list[${index}].makeTime,jdbcType=VARCHAR},
    #{list[${index}].modifyDate,jdbcType=DATE},
    #{list[${index}].modifyTime,jdbcType=VARCHAR})
  </foreach>
  </insert>

  <select id="getEdorPlanRiskListBygrpContNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCEdorAddPlanRiskInfo">
    SELECT
    <include refid="Base_Column_List" />
    FROM fcedoraddplanriskinfo WHERE grpContNo = #{grpContNo,jdbcType=VARCHAR}
  </select>

  <select id="getEdorPlanRiskListByedorAddPlanSN" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCEdorAddPlanRiskInfo">
    SELECT
      <include refid="Base_Column_List" />
        FROM fcedoraddplanriskinfo WHERE EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
  </select>
    <delete id="deleteByGrpContNo" parameterType="java.lang.String">
        delete from fcedoraddplanriskinfo
        where grpContNo = #{grpContNo}
    </delete>
</mapper>