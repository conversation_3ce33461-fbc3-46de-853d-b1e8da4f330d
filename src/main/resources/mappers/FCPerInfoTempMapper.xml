<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCPerInfoTempMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCPerInfoTemp">
        <result column="IDNo" jdbcType="VARCHAR" property="IDNo"/>
        <result column="codekey" jdbcType="VARCHAR" property="codekey"/>
    </resultMap>
    <resultMap id="BaseResultMapC" type="java.lang.String">
        <result column="codekey" jdbcType="VARCHAR" property="codekey"/>
    </resultMap>
    <resultMap id="BaseResultMapN" type="java.lang.String">
        <result column="codename" jdbcType="VARCHAR" property="codename"/>
    </resultMap>
    <resultMap id="BaseResultMapA" type="com.sinosoft.eflex.model.FCPerInfoTemp">
        <id column="PerTempNo" jdbcType="VARCHAR" property="perTempNo"/>
        <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode"/>
        <result column="CPerNo" jdbcType="VARCHAR" property="CPerNo"/>
        <result column="GrpNo" jdbcType="VARCHAR" property="grpNo"/>
        <result column="StaffNo" jdbcType="VARCHAR" property="staffNo"/>
        <result column="LevelCode" jdbcType="VARCHAR" property="levelCode"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sex" jdbcType="VARCHAR" property="sex"/>
        <result column="IDType" jdbcType="VARCHAR" property="IDType"/>
        <result column="IDNo" jdbcType="VARCHAR" property="IDNo"/>
        <result column="idTypeEndDate" jdbcType="DATE" property="idTypeEndDate"/>
        <result column="BirthDay" jdbcType="DATE" property="birthDay"/>
        <result column="Phone" jdbcType="VARCHAR" property="phone"/>
        <result column="MobilePhone" jdbcType="VARCHAR" property="mobilePhone"/>
        <result column="OccupationType" jdbcType="VARCHAR" property="occupationType"/>
        <result column="OccupationCode" jdbcType="VARCHAR" property="occupationCode"/>
        <result column="JoinMedProtect" jdbcType="VARCHAR" property="joinMedProtect"/>
        <result column="MedProtectType" jdbcType="VARCHAR" property="medProtectType"/>
        <result column="Email" jdbcType="VARCHAR" property="email"/>
        <result column="ZipCode" jdbcType="VARCHAR" property="zipCode"/>
        <result column="Address" jdbcType="VARCHAR" property="address"/>
        <result column="Nativeplace" jdbcType="VARCHAR" property="nativeplace"/>
        <result column="ServiceTerm" jdbcType="VARCHAR" property="serviceTerm"/>
        <result column="Retirement" jdbcType="VARCHAR" property="retirement"/>
        <result column="ImpotStatus" jdbcType="VARCHAR" property="impotStatus"/>
        <result column="SubStaus" jdbcType="VARCHAR" property="subStaus"/>
        <result column="ErrorMsg" jdbcType="VARCHAR" property="errorMsg"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
        <result column="DefaultPlan" jdbcType="VARCHAR" property="defaultPlan"/>
        <result column="openbank" jdbcType="VARCHAR" property="openBank"/>
        <result column="OpenAccount" jdbcType="VARCHAR" property="openAccount"/>
        <result column="StaffGrpPrem" jdbcType="DOUBLE" property="staffGrpPrem"/>
        <result column="FamilyGrpPrem" jdbcType="DOUBLE" property="familyGrpPrem"/>
        <result column="StudentGrpPrem" jdbcType="DOUBLE" property="studentGrpPrem"/>
    </resultMap>
    <sql id="Base_Column_List">
        PerTempNo
        , CPerNo, GrpNo, StaffNo, LevelCode, department, Name,
		Sex, nativeplace,ServiceTerm,Retirement,IDType,relationship,
		IDNo, idTypeEndDate,BirthDay,
		Phone, MobilePhone, OccupationType,
		OccupationCode, JoinMedProtect,
		MedProtectType,
		Email, ZipCode, Address,
		DefaultPlan, OpenBank, OpenAccount, OperatorCom,
		Operator,
		MakeDate,
		MakeTime, ModifyDate, ModifyTime
    </sql>

    <insert id="insert" parameterType="java.util.List">
        insert into FCPerInfoTemp (PerTempNo,EnsureCode,
        CPerNo, GrpNo,
        StaffNo, LevelCode, department,
        Name, Sex, IDType,
        IDNo,IdTypeEndDate,
        BirthDay, Phone,
        MobilePhone, OccupationType, OccupationCode,
        JoinMedProtect, MedProtectType, Email,
        ZipCode, Address,nativeplace,ServiceTerm,Retirement,DefaultPlan,
        OpenBank, OpenAccount,ImpotStatus,SubStaus,ErrorMsg,OperatorCom,
        Operator, MakeDate, MakeTime,
        ModifyDate, ModifyTime,StaffGrpPrem,FamilyGrpPrem,StudentGrpPrem,relationship)
        <foreach collection="list" item="item" index="index" separator="union all">
            select #{list[${index}].PerTempNo,jdbcType=VARCHAR},
            #{list[${index}].EnsureCode,jdbcType=VARCHAR},
            #{list[${index}].CPerNo,jdbcType=VARCHAR},
            #{list[${index}].grpNo,jdbcType=VARCHAR},
            #{list[${index}].staffNo,jdbcType=VARCHAR},
            #{list[${index}].levelCode,jdbcType=VARCHAR},
            #{list[${index}].department,jdbcType=VARCHAR},
            #{list[${index}].name,jdbcType=VARCHAR},
            #{list[${index}].sex,jdbcType=VARCHAR},
            #{list[${index}].IDType,jdbcType=VARCHAR},
            #{list[${index}].IDNo,jdbcType=VARCHAR},
            #{list[${index}].idTypeEndDate,jdbcType=DATE},
            #{list[${index}].birthDay,jdbcType=DATE},
            #{list[${index}].phone,jdbcType=VARCHAR},
            #{list[${index}].mobilePhone,jdbcType=VARCHAR},
            #{list[${index}].occupationType,jdbcType=VARCHAR},
            #{list[${index}].occupationCode,jdbcType=VARCHAR},
            #{list[${index}].joinMedProtect,jdbcType=VARCHAR},
            #{list[${index}].medProtectType,jdbcType=VARCHAR},
            #{list[${index}].email,jdbcType=VARCHAR},
            #{list[${index}].zipCode,jdbcType=VARCHAR},
            #{list[${index}].address,jdbcType=VARCHAR},
            #{list[${index}].nativeplace,jdbcType=VARCHAR},
            #{list[${index}].ServiceTerm,jdbcType=VARCHAR},
            #{list[${index}].Retirement,jdbcType=VARCHAR},
            #{list[${index}].defaultPlan,jdbcType=VARCHAR},
            #{list[${index}].openBank,jdbcType=VARCHAR},
            #{list[${index}].openAccount,jdbcType=VARCHAR},
            #{list[${index}].ImpotStatus,jdbcType=VARCHAR},
            #{list[${index}].SubStaus,jdbcType=VARCHAR},
            #{list[${index}].ErrorMsg,jdbcType=VARCHAR},
            #{list[${index}].operatorCom,jdbcType=VARCHAR},
            #{list[${index}].operator,jdbcType=VARCHAR},
            #{list[${index}].makeDate,jdbcType=DATE},
            #{list[${index}].makeTime,jdbcType=VARCHAR},
            #{list[${index}].modifyDate,jdbcType=DATE},
            #{list[${index}].modifyTime,jdbcType=VARCHAR},
            #{list[${index}].staffGrpPrem,jdbcType=DOUBLE},
            #{list[${index}].familyGrpPrem,jdbcType=DOUBLE},
            #{list[${index}].studentGrpPrem,jdbcType=DOUBLE},
            #{list[${index}].relationship,jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>
    <delete id="deleteByGrpNoAndEnsureCode">
        delete
        from fcperinfotemp
        where GrpNo = #{grpNo}
          and EnsureCode = ''
          and ImpotStatus = '01'
          and SubStaus = '01'
    </delete>

    <!-- 修改时根据证件号判断临时表表是否已存在 -->
    <select id="updateCheckIdNoIsExistsTemp" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1)
        from fcperinfotemp
        where grpNo = #{grpNo,jdbcType=VARCHAR}
          and SubStaus = '01'
          AND PerTempNo != #{perTempNo,jdbcType=VARCHAR}
          AND idno = #{idno,jdbcType=VARCHAR}
    </select>

    <!-- 修改时判断临时表是否存在证件号存在，但是其他四要素不同的数据   -->
    <select id="updateCheckOtherIsEsistsTemp" parameterType="java.util.Map"
            resultType="com.sinosoft.eflex.model.FCPerInfoTemp">
        select IDNo, grpNo
        from fcperinfotemp
        where grpNo = #{grpNo,jdbcType=VARCHAR}
          and SubStaus = '01'
          and PerTempNo != #{perTempNo,jdbcType=VARCHAR}
          and idno = #{idno,jdbcType=VARCHAR}
          and (name &lt;&gt; #{name,jdbcType=VARCHAR}
           or
            sex &lt;&gt; #{sex,jdbcType=VARCHAR}
           or
            date_format(birthday
            , '%Y-%m-%d') &lt;&gt; date_format(str_to_date(#{birthday,jdbcType=VARCHAR}
            , '%Y-%m-%d')
            , '%Y-%m-%d')
           or
            idtype &lt;&gt; #{idtype,jdbcType=VARCHAR})
    </select>


    <!-- 根据证件号判断临时表表是否已存在 -->
    <select id="checkIdNoIsExistsTemp" parameterType="java.util.List" resultType="java.lang.Integer">
        select count(1) from fcperinfotemp
        where grpNo = #{grpNo,jdbcType=VARCHAR} and SubStaus = '01' AND idno in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{list[${index}].idno,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 根据证件号判断正式表是否已存在 -->
    <select id="checkIdNoIsExists" parameterType="java.util.List" resultType="java.lang.Integer">
        select count(1) from fcperinfo
        where idno in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{list[${index}].idno,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 判断临时表是否存在证件号存在，但是其他四要素不同的数据 -->
    <select id="checkOtherIsEsistsTemp" parameterType="java.util.List" resultMap="BaseResultMap">
        select IDNo,grpNo from fcperinfotemp where grpNo = #{grpNo,jdbcType=VARCHAR} and SubStaus = '01' AND
        <foreach collection="list" item="item" index="index" separator="or">
            (idno=#{list[${index}].idno,jdbcType=VARCHAR}
            and (name &lt;&gt; #{list[${index}].name,jdbcType=VARCHAR} or
            sex &lt;&gt; #{list[${index}].sex,jdbcType=VARCHAR} or
            date_format(birthday,'%Y-%m-%d') &lt;&gt;
            date_format(str_to_date(#{list[${index}].birthday,jdbcType=VARCHAR},'%Y-%m-%d'),'%Y-%m-%d') or
            idtype &lt;&gt; #{list[${index}].idtype,jdbcType=VARCHAR}))
        </foreach>
    </select>

    <!-- 判断正式表是否存在证件号存在，但是其他四要素不同的数据 -->
    <select id="checkOtherIsEsists" parameterType="java.util.List" resultMap="BaseResultMap">
        select IDNo from fcperinfo where 1 = 1 AND
        <foreach collection="list" item="item" index="index" separator="or">
            (idno=#{list[${index}].idno,jdbcType=VARCHAR}
            and (name &lt;&gt; #{list[${index}].name,jdbcType=VARCHAR} or
            sex &lt;&gt; #{list[${index}].sex,jdbcType=VARCHAR} or
            date_format(birthday,'%Y-%m-%d') &lt;&gt;
            date_format(str_to_date(#{list[${index}].birthday,jdbcType=VARCHAR},'%Y-%m-%d'),'%Y-%m-%d') or
            idtype &lt;&gt; #{list[${index}].idtype,jdbcType=VARCHAR}))
        </foreach>
    </select>
    <select id="checkOneIdNoIsExists" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        from fcperinfo
        where idno = #{idNo,jdbcType=VARCHAR}
    </select>
    <select id="checkTempOneIdNoIsExists" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        from FCPerInfoTemp
        where idno = #{params.idNo,jdbcType=VARCHAR}
          and EnsureCode = #{params.ensureCode,jdbcType=VARCHAR}
          AND GrpNo = #{params.grpNo,jdbcType=VARCHAR}
    </select>
    <select id="checkTempOneNIdNoIsExists" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        from FCPerInfoTemp
        where idno = #{params.idNo,jdbcType=VARCHAR}
          and EnsureCode = #{params.ensureCode,jdbcType=VARCHAR}
          and PerTempNo &lt;&gt; #{params.perNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteFcPerInfoTemp" parameterType="java.lang.String">
        delete
        from FCPerInfoTemp
        where PerTempNo = #{perNo,jdbcType=VARCHAR}
    </delete>
    <select id="deleteAllFcPerInfoTemp" parameterType="java.lang.String">
        delete
        from FCPerInfoTemp
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </select>
    <select id="checkOneOtherIsEsists" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1)
        from fcperinfo
        where idno = #{params.idNo,jdbcType=VARCHAR}
          and (name &lt;&gt; #{params.name,jdbcType=VARCHAR} or
               sex &lt;&gt; #{params.sex,jdbcType=VARCHAR} or
               date_format(birthday, '%Y-%m-%d') &lt;&gt; #{params.birthday,jdbcType=VARCHAR} or
               idtype &lt;&gt; #{params.idType,jdbcType=VARCHAR})
    </select>
    <!-- 根据证件号、福利编号判断临时表是否已存在 -->
    <select id="checkTempIdNoIsExists" parameterType="java.util.List" resultMap="BaseResultMap">
        select PerTempNo,IDNo from FCPerInfoTemp where grpNo = #{grpNo} AND
        <foreach collection="list" item="item" index="index" separator="or">
            (idno=#{list[${index}].idno,jdbcType=VARCHAR}
            and EnsureCode=#{list[${index}].ensureCode,jdbcType=VARCHAR}
            <if test=" item.IDType != null">        <!--以下if是后来增加的,用于家属信息编辑时校验员工三要素是否一致  sunzh-->
                AND IDType = #{list[${index}].IDType,jdbcType=VARCHAR}
            </if>)
            <if test="item.Name != null">
                AND Name = #{list[${index}].Name,jdbcType=VARCHAR}
            </if>
        </foreach>
    </select>
    <!-- 获取需要同步的员工人数 -->
    <select id="getNeedSyncNum" parameterType="java.lang.String" resultMap="BaseResultMap">
        select PerTempNo, IDNo, DefaultPlan, IDType, Nativeplace, LevelCode
        from FCPerInfoTemp
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and ImpotStatus = '01'
          and SubStaus = '01'
    </select>
    <!-- 获取需要同步的监护人总数 -->
    <select id="getNeedSyncGarNum" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT PerTempNo, IDNo, DefaultPlan, IDType, Nativeplace, SUM(StudentGrpPrem) as studentGrpPrem
        FROM FCPerInfoTemp
        WHERE EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          AND ImpotStatus = '01'
        GROUP BY IDNo
    </select>
    <!-- 同步人员信息到FcPerSon -->
    <insert id="insertFcPerSon" parameterType="java.util.List">
        insert into fcperson (PersonID, Name, Sex, Nativeplace,levelCode,serviceTerm,retirement,
        BirthDate, IDType, IDNo,idTypeEndDate,relationship,
        MobilePhone, Phone, OccupationType,
        OccupationCode, JoinMedProtect, MedProtectType,
        EMail, ZipCode, Address,
        OpenBank, OpenAccount, Operator,
        OperatorCom, MakeDate, MakeTime,
        ModifyDate, ModifyTime)
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{list[${index}].PersonId,jdbcType=VARCHAR},name,sex,nativeplace,levelCode,serviceTerm,retirement,BirthDay,
            IDType, IDNo,idTypeEndDate,relationship,
            MobilePhone, Phone, OccupationType,
            OccupationCode, JoinMedProtect, MedProtectType,
            EMail, ZipCode, Address,
            OpenBank, OpenAccount,#{list[${index}].Operator,jdbcType=VARCHAR},OperatorCom,
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR}
            from FCPerInfoTemp where EnsureCode=#{list[${index}].ensureCode,jdbcType=VARCHAR}
            and ImpotStatus='01' and SubStaus='01' and idno=#{list[${index}].idNo,jdbcType=VARCHAR}
            and IDNo not in (select idno from fcperson WHERE PersonID = #{list[${index}].PersonId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <update id="updateFcPerInfo" parameterType="java.lang.String">
        update fcperinfo a,FCPerInfoTemp b
        set a.name=b.name,
        a.sex=b.sex,
        a.nativeplace = b.nativeplace,
        <if test=' params.planType == "1" '>
            a.levelCode = b.levelCode,
            a.serviceTerm = b.serviceTerm,
            a.retirement = b.retirement,
        </if>
        a.BirthDay=b.BirthDay,
        a.IDType=b.IDType,
        a.IDNo=b.IDNo,
        a.idTypeEndDate=b.idTypeEndDate,
        a.grpno=b.grpno,
        a.MobilePhone=b.MobilePhone,
        a.Phone=b.Phone,
        a.OccupationType=b.OccupationType,
        a.OccupationCode=b.OccupationCode,
        a.JoinMedProtect=b.JoinMedProtect,
        a.MedProtectType=b.MedProtectType,
        a.EMail=b.EMail,
        a.Address=b.Address,
        a.OpenBank=b.OpenBank,
        a.OpenAccount=b.OpenAccount,
        a.ModifyDate=str_to_date(#{params.currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
        a.ModifyTime=#{params.currentTime,jdbcType=VARCHAR}
        where a.idno=b.idno and a.grpNo = b.grpNo and b.ImpotStatus='01' and b.SubStaus='01' and
        b.EnsureCode=#{params.ensureCode,jdbcType=VARCHAR}
        <if test="params.perTempNo != null and params.perTempNo != '' ">
            AND PerTempNo = #{params.perTempNo}
        </if>
    </update>
    <update id="updateFcPerSon" parameterType="java.util.Map">
        update fcperson a,FCPerInfoTemp b
        set a.name=b.name,
        a.sex=b.sex,
        a.relationship=b.relationship,
        a.nativeplace = b.nativeplace,
        <if test=' params.planType == "1" '>
            a.levelCode = b.levelCode,
            a.serviceTerm = b.serviceTerm,
            a.retirement = b.retirement,
        </if>
        a.BirthDate=b.BirthDay,
        a.IDType=b.IDType,
        a.IDNo=b.IDNo,
        a.idTypeEndDate=b.idTypeEndDate,
        a.MobilePhone=b.MobilePhone,
        a.Phone=b.Phone,
        a.OccupationType=b.OccupationType,
        a.OccupationCode=b.OccupationCode,
        a.JoinMedProtect=b.JoinMedProtect,
        a.MedProtectType=b.MedProtectType,
        a.EMail=b.EMail,
        a.Address=b.Address,
        a.OpenBank=b.OpenBank,
        a.OpenAccount=b.OpenAccount,
        a.ModifyDate=str_to_date(#{params.currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
        a.ModifyTime=#{params.currentTime,jdbcType=VARCHAR}
        where a.idno=b.idno and b.ImpotStatus='01' and b.SubStaus='01' and
        b.EnsureCode=#{params.ensureCode,jdbcType=VARCHAR} and a.personID IN
        (SELECT PersonID FROM fcstafffamilyrela cs
        INNER JOIN fcperinfo per ON per.perNo = cs.perNo
        INNER JOIN fcperinfotemp pert ON pert.IDNo = per.IDNo
        WHERE pert.ensureCode = #{params.ensureCode,jdbcType=VARCHAR} AND per.grpNo = #{params.grpNo,jdbcType=VARCHAR}
        AND cs.Relation = '0')
    </update>

    <update id="updateFdUser" parameterType="java.util.Map">
        update fduser a,FCPerInfoTemp b
        set a.Phone=b.MobilePhone,
            a.ModifyDate=str_to_date(#{params.currentDate,jdbcType=VARCHAR}, '%Y-%m-%d'),
            a.ModifyTime=#{params.currentTime,jdbcType=VARCHAR}
        where a.idno=b.idno
          and b.ImpotStatus='01'
          and b.SubStaus='01'
          and b.EnsureCode=#{params.ensureCode,jdbcType=VARCHAR}
    </update>
    <update id="updateFCDefaultPlan" parameterType="java.util.Map">
        update fcdefaultplan a,fcperson b, FCPerInfoTemp c
        set a.plancode= c.DefaultPlan, a.ensureCode = c.ensureCode, a.AppntYear= #{params.year,jdbcType=VARCHAR},
            a.ModifyDate=str_to_date(#{params.currentDate,jdbcType=VARCHAR}, '%Y-%m-%d'),
            a.ModifyTime=#{params.currentTime,jdbcType=VARCHAR}
        where a.personid=b.personid
          and b.idno= c.idno
          and c.ImpotStatus='01'
          and c.SubStaus='01'
          and c.EnsureCode=#{params.ensureCode,jdbcType=VARCHAR}
    </update>
    <insert id="insertFcPerInfo" parameterType="java.util.List">
        insert into fcperinfo (PerNo,
        CPerNo, GrpNo,
        StaffNo,serviceTerm,retirement,department,
        Name, Sex,Nativeplace,IDType,
        IDNo,idTypeEndDate,
        BirthDay, Phone,
        MobilePhone, OccupationType, OccupationCode,
        JoinMedProtect, MedProtectType, Email,
        ZipCode, Address, DefaultPlan,
        OpenBank, OpenAccount, OperatorCom,
        Operator, MakeDate, MakeTime,
        ModifyDate, ModifyTime , LevelCode)
        <foreach collection="list" item="item" index="index" separator="union all">
            select #{list[${index}].PerNo,jdbcType=VARCHAR},CPerNo, GrpNo,
            StaffNo,serviceTerm,retirement,if(department is not NULL ,department,''),
            Name, Sex,Nativeplace,IDType,
            IDNo,idTypeEndDate,
            BirthDay, Phone,
            MobilePhone, OccupationType, OccupationCode,
            JoinMedProtect, MedProtectType, Email,
            ZipCode, Address, DefaultPlan,
            OpenBank, OpenAccount, OperatorCom,#{list[${index}].Operator,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR},
            #{list[${index}].levelCode,jdbcType=VARCHAR}
            from FCPerInfoTemp where EnsureCode=#{list[${index}].ensureCode,jdbcType=VARCHAR}
            and ImpotStatus='01' and SubStaus='01' and idno=#{list[${index}].idNo,jdbcType=VARCHAR} and IDNo not in
            (select idno from fcperinfo where GrpNo = #{list[${index}].grpNo,jdbcType=VARCHAR})
            <if test="item.perTempNo != null and item.perTempNo != '' ">
                AND PerTempNo = #{list[${index}].perTempNo,jdbcType=VARCHAR}
            </if>
        </foreach>
    </insert>
    <insert id="insertFCStaffFamilyRela" parameterType="java.util.List">
        insert into fcstafffamilyrela
        (PerNo, PersonID, Relation,
        RelationProve, Operator, OperatorCom,
        MakeDate, MakeTime, ModifyDate,
        ModifyTime)
        <foreach collection="list" item="item" index="index" separator="union all">
            select #{list[${index}].PerNo,jdbcType=VARCHAR},
            #{list[${index}].PersonId,jdbcType=VARCHAR},
            '0',
            '本人',
            #{list[${index}].Operator,jdbcType=VARCHAR},
            OperatorCom,
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR}
            from FCPerInfoTemp where EnsureCode=#{list[${index}].ensureCode,jdbcType=VARCHAR}
            and ImpotStatus='01' and SubStaus='01'
            and idno=#{list[${index}].idNo,jdbcType=VARCHAR}
            and '0' not IN (select Relation from fcstafffamilyrela where PerNo =
            #{list[${index}].PerNo,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertFCDefaultPlan" parameterType="java.util.List">
        insert into fcdefaultplan (SerialNo, personId, EnsureCode,
        AppntYear, PlanCode, Operator,
        OperatorCom, MakeDate, MakeTime,
        ModifyDate, ModifyTime)
        <foreach collection="list" item="item" index="index" separator="union all">
            select #{list[${index}].seriaNo,jdbcType=VARCHAR},
            #{list[${index}].PersonId,jdbcType=VARCHAR},
            #{list[${index}].ensureCode,jdbcType=VARCHAR},
            #{list[${index}].year,jdbcType=VARCHAR},
            DefaultPlan,#{list[${index}].Operator,jdbcType=VARCHAR},
            a.OperatorCom,
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR}
            from FCPerInfoTemp a where a.EnsureCode=#{list[${index}].ensureCode,jdbcType=VARCHAR} and a.ImpotStatus='01'
            and a.SubStaus='01' and a.idno=#{list[${index}].idNo,jdbcType=VARCHAR}
        </foreach>
    </insert>
    <update id="updateFCPerRegistDay" parameterType="java.util.Map">
        UPDATE fcperregistday a
        INNER JOIN fcperinfotemp c ON c.`EnsureCode` = a.`EnsureCode` AND c.`IDNo` = (SELECT IDNo FROM fcperinfo WHERE
        PerNo = a.`PerNo`)
        INNER JOIN fcensure d ON d.`EnsureCode` = a.`EnsureCode`
        SET a.OpenDay=d.StartAppntDate,
        <if test=' params.planType == "1" '>
            a.LevelCode = c.levelCode,
        </if>
        a.CloseDay=d.EndAppntDate,
        a.`StaffGrpPrem`=c.`StaffGrpPrem`,
        a.`FamilyGrpPrem`=c.`FamilyGrpPrem`,
        a.`StudentGrpPrem`=c.`StudentGrpPrem`,
        a.`LockState` = '0',
        a.ModifyDate=str_to_date(#{params.currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
        a.ModifyTime=#{params.currentTime,jdbcType=VARCHAR}
        WHERE a.EnsureCode = #{params.ensureCode,jdbcType=VARCHAR}
        AND a.`PerNo` IN (SELECT PerNo FROM fcperinfo WHERE IDNo IN (SELECT IDNo FROM fcperinfotemp WHERE EnsureCode =
        #{params.ensureCode,jdbcType=VARCHAR}))
    </update>
    <insert id="insertFCPerRegistDay" parameterType="java.util.List">
        insert into fcperregistday (RegistDayNo, EnsureCode, PerNo,
        OpenDay, CloseDay, PersonType, IsValidy,
        ValidydateType, RegistDayToManNo,LevelCode,
        Operator, OperatorCom, MakeDate, MakeTime, ModifyDate,
        ModifyTime,StaffGrpPrem,FamilyGrpPrem,StudentGrpPrem,LockState)
        <foreach collection="list" item="item" index="index" separator="union all">
            select #{list[${index}].RegDay,jdbcType=VARCHAR},
            #{list[${index}].ensureCode,jdbcType=VARCHAR},
            #{list[${index}].PerNo,jdbcType=VARCHAR},
            (select StartAppntDate from fcensure where EnsureCode =#{list[${index}].ensureCode,jdbcType=VARCHAR}),
            (select EndAppntDate from fcensure where EnsureCode =#{list[${index}].ensureCode,jdbcType=VARCHAR}),
            '1','1','','',
            #{list[${index}].levelCode,jdbcType=VARCHAR},
            #{list[${index}].Operator,jdbcType=VARCHAR},
            a.OperatorCom,
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR},
            a.StaffGrpPrem,a.FamilyGrpPrem,CAST(#{list[${index}].studentGrpPrem} AS decimal(20,2)),'0'
            from FCPerInfoTemp a where a.EnsureCode=#{list[${index}].ensureCode,jdbcType=VARCHAR}
            and a.ImpotStatus='01' and a.SubStaus='01' and a.idno=#{list[${index}].idNo,jdbcType=VARCHAR}
            and #{list[${index}].PerNo,jdbcType=VARCHAR} not in (select perNo from fcperregistday where EnsureCode =
            #{list[${index}].ensureCode,jdbcType=VARCHAR} AND IsValidy = '1')
            <if test="item.perTempNo != null and item.perTempNo != '' ">
                AND a.PerTempNo = #{list[${index}].perTempNo,jdbcType=VARCHAR}
            </if>
        </foreach>
    </insert>
    <insert id="insertFdUser" parameterType="java.util.List">
        insert into fduser (UserNo, UserName, NickName,
        PassWord, CustomType, CustomNo,
        IsLock, IsVIP, LoginFailTimes,
        UserState, Source, Email,
        Phone, IDNo, OpenID,
        Remark, OperatorCom, Operator,
        MakeDate, MakeTime, ModifyDate,
        ModifyTime, PWDState,
        LockTime)
        <foreach collection="list" item="item" index="index" separator="union all">
            select #{list[${index}].UserNo,jdbcType=VARCHAR},
            idno,name,#{list[${index}].PassWord,jdbcType=VARCHAR},'1',
            #{list[${index}].PerNo,jdbcType=VARCHAR},'0','N',0,'1','',Email,
            mobilePhone, IDNo,'','',
            OperatorCom,
            #{list[${index}].Operator,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR},'',''
            from FCPerInfoTemp where EnsureCode=#{list[${index}].ensureCode,jdbcType=VARCHAR}
            and ImpotStatus='01' and SubStaus='01' and idno=#{list[${index}].idNo,jdbcType=VARCHAR} and IDNo not in
            (select idno from fcperinfo where GrpNo = #{list[${index}].grpNo,jdbcType=VARCHAR})
            <if test="item.perTempNo != null and item.perTempNo != '' ">
                AND PerTempNo = #{list[${index}].perTempNo,jdbcType=VARCHAR}
            </if>
        </foreach>
    </insert>
    <insert id="insertFdUserRole" parameterType="java.util.List">
        insert into fduserrole (UserRoleSN, UserNo, RoleType,
        OperatorCom, Operator, MakeDate,
        MakeTime, ModifyDate, ModifyTime
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            select #{list[${index}].UserRole,jdbcType=VARCHAR},
            #{list[${index}].UserNo,jdbcType=VARCHAR},
            '1',OperatorCom,
            #{list[${index}].Operator,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR}
            from FCPerInfoTemp where EnsureCode=#{list[${index}].ensureCode,jdbcType=VARCHAR}
            and ImpotStatus='01' and SubStaus='01' and idno=#{list[${index}].idNo,jdbcType=VARCHAR} and IDNo not in
            (select idno from fcperinfo where GrpNo = #{list[${index}].grpNo,jdbcType=VARCHAR})
            <if test="item.perTempNo != null and item.perTempNo != '' ">
                AND PerTempNo = #{list[${index}].perTempNo,jdbcType=VARCHAR}
            </if>
        </foreach>
    </insert>
    <insert id="insertFdPwdHist" parameterType="java.util.List">
        insert into fdpwdhist (PassWordSN, UserNo, PassWord,
        OperatorCom, Operator, MakeDate,
        MakeTime, ModifyDate, ModifyTime
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            select #{list[${index}].PwdSN,jdbcType=VARCHAR},
            #{list[${index}].UserNo,jdbcType=VARCHAR},
            #{list[${index}].PassWord,jdbcType=VARCHAR},
            OperatorCom,
            #{list[${index}].Operator,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR}
            from FCPerInfoTemp where EnsureCode=#{list[${index}].ensureCode,jdbcType=VARCHAR}
            and ImpotStatus='01' and SubStaus='01' and idno=#{list[${index}].idNo,jdbcType=VARCHAR} and IDNo not in
            (select idno from fcperinfo where GrpNo = #{list[${index}].grpNo,jdbcType=VARCHAR})
            <if test="item.perTempNo != null and item.perTempNo != '' ">
                AND PerTempNo = #{list[${index}].perTempNo,jdbcType=VARCHAR}
            </if>
        </foreach>
    </insert>
    <!-- 用户菜单修改，HR与个人用户角色值进行了调换 update by wudezhong -->
    <insert id="insertFDusertomenugrp" parameterType="java.util.List">
        insert into fDusertomenugrp (userno, menugrpcode)
        <foreach collection="list" item="item" index="index" separator="union all">
            select #{list[${index}].UserNo,jdbcType=VARCHAR},
            '1'
            from dual where #{list[${index}].idNo,jdbcType=VARCHAR} not IN (select idno from fcperinfo where GrpNo =
            #{list[${index}].grpNo,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <update id="updateFCPerinfoTemp" parameterType="java.util.Map">
        update fcperinfotemp
        set SubStaus  ='02',
            ModifyDate=str_to_date(#{params.currentDate,jdbcType=VARCHAR}, '%Y-%m-%d'),
            ModifyTime=#{params.currentTime,jdbcType=VARCHAR}
        where EnsureCode = #{params.ensureCode,jdbcType=VARCHAR}
    </update>
    <update id="updateOneFcPerInfoTemp" parameterType="com.sinosoft.eflex.model.FCPerInfoTemp">
        update FcPerInfoTemp
        <set>
            <if test="CPerNo != null">
                CPerNo = #{CPerNo,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                GrpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="staffNo != null">
                StaffNo = #{staffNo,jdbcType=VARCHAR},
            </if>
            <if test="levelCode != null">
                LevelCode = #{levelCode,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != '' ">
                idTypeEndDate = #{idTypeEndDate,jdbcType=VARCHAR},
            </if>
            <if test="birthDay != null">
                BirthDay = #{birthDay,jdbcType=DATE},
            </if>
            <if test="phone != null">
                Phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                OccupationType = #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                MedProtectType = #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                Email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                Nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="levelCode != null">
                levelCode = #{levelCode,jdbcType=VARCHAR},
            </if>
            <if test="ServiceTerm != null">
                ServiceTerm = #{ServiceTerm,jdbcType=VARCHAR},
            </if>
            <if test="Retirement != null">
                Retirement = #{Retirement,jdbcType=VARCHAR},
            </if>
            <if test="defaultPlan != null">
                DefaultPlan = #{defaultPlan,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                OpenBank = #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                OpenAccount = #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
            StaffGrpPrem = #{staffGrpPrem,jdbcType=VARCHAR},
            FamilyGrpPrem = #{familyGrpPrem,jdbcType=VARCHAR},
            StudentGrpPrem = #{studentGrpPrem,jdbcType=VARCHAR}
        </set>
        where PerTempNo = #{PerTempNo,jdbcType=VARCHAR} and EnsureCode=#{EnsureCode,jdbcType=VARCHAR}
    </update>
    <resultMap id="BaseResultMapH" type="java.util.Map">
        <id column="occupationType" jdbcType="VARCHAR" property="occupationType"/>
        <result column="occupationTypeName" jdbcType="VARCHAR" property="occupationTypeName"/>
        <result column="occupationCode" jdbcType="VARCHAR" property="occupationCode"/>
        <result column="occupationCodeName" jdbcType="VARCHAR" property="occupationCodeName"/>
    </resultMap>
    <select id="selectOccupationList" parameterType="java.lang.String" resultMap="BaseResultMapH">
        select DISTINCT OtherSign as occupationType
        from fdcode
        where codetype = 'Occupationdetail'
    </select>
    <select id="selectOccupationCode" parameterType="java.lang.String" resultMap="BaseResultMapH">
        select codeKey occupationCode, trim(codeName) occupationCodeName
        from fdcode
        where codetype = 'Occupationdetail'
          and OtherSign = #{codeKey,jdbcType=VARCHAR}
        order by codekey
    </select>
    <insert id="insertFcplaninform" parameterType="java.util.Map">
        insert into fcplaninform(EnsureCode, plancode, InformNumber, Operator, MakeDate, MakeTime,
                                 ModifyDate, ModifyTime)
        select distinct a.EnsureCode,
                        a.plancode,
                        b.informnumber,
                        #{params.Operator,jdbcType=VARCHAR},
                        str_to_date(#{params.currentDate,jdbcType=VARCHAR}, '%Y-%m-%d'),
                        #{params.currentTime,jdbcType=VARCHAR},
                        str_to_date(#{params.currentDate,jdbcType=VARCHAR}, '%Y-%m-%d'),
                        #{params.currentTime,jdbcType=VARCHAR}
        from fcplanrisk a,
             fcriskinform b
        where a.riskcode = b.riskcode
          and a.EnsureCode = #{params.ensureCode,jdbcType=VARCHAR}
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCPerInfoTemp">
        insert into fcperinfotemp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="perTempNo != null">
                PerTempNo,
            </if>
            <if test="ensureCode != null">
                EnsureCode,
            </if>
            <if test="CPerNo != null">
                CPerNo,
            </if>
            <if test="grpNo != null">
                GrpNo,
            </if>
            <if test="staffNo != null">
                StaffNo,
            </if>
            <if test="levelCode != null">
                LevelCode,
            </if>
            <if test="department != null">
                department,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="sex != null">
                Sex,
            </if>
            <if test="IDType != null">
                IDType,
            </if>
            <if test="IDNo != null">
                IDNo,
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate,
            </if>
            <if test="birthDay != null">
                BirthDay,
            </if>
            <if test="phone != null">
                Phone,
            </if>
            <if test="mobilePhone != null">
                MobilePhone,
            </if>
            <if test="occupationType != null">
                OccupationType,
            </if>
            <if test="occupationCode != null">
                OccupationCode,
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect,
            </if>
            <if test="medProtectType != null">
                MedProtectType,
            </if>
            <if test="email != null">
                Email,
            </if>
            <if test="zipCode != null">
                ZipCode,
            </if>
            <if test="address != null">
                Address,
            </if>
            <if test="nativeplace != null">
                Nativeplace,
            </if>
            <if test="serviceTerm != null">
                ServiceTerm,
            </if>
            <if test="retirement != null">
                Retirement,
            </if>
            <if test="impotStatus != null">
                ImpotStatus,
            </if>
            <if test="subStaus != null">
                SubStaus,
            </if>
            <if test="errorMsg != null">
                ErrorMsg,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
            <if test="defaultPlan != null">
                DefaultPlan,
            </if>
            <if test="openBank != null">
                openbank,
            </if>
            <if test="openAccount != null">
                OpenAccount,
            </if>
            <if test="staffGrpPrem != null">
                StaffGrpPrem,
            </if>
            <if test="familyGrpPrem != null">
                FamilyGrpPrem,
            </if>
            <if test="studentGrpPrem != null">
                StudentGrpPrem,
            </if>
            <if test="relationship != null">
                relationship,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="perTempNo != null">
                #{perTempNo,jdbcType=VARCHAR},
            </if>
            <if test="ensureCode != null">
                #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="CPerNo != null">
                #{CPerNo,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="staffNo != null">
                #{staffNo,jdbcType=VARCHAR},
            </if>
            <if test="levelCode != null">
                #{levelCode,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                #{department,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="IDType != null">
                #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="birthDay != null">
                #{birthDay,jdbcType=DATE},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="serviceTerm != null">
                #{serviceTerm,jdbcType=VARCHAR},
            </if>
            <if test="retirement != null">
                #{retirement,jdbcType=VARCHAR},
            </if>
            <if test="impotStatus != null">
                #{impotStatus,jdbcType=VARCHAR},
            </if>
            <if test="subStaus != null">
                #{subStaus,jdbcType=VARCHAR},
            </if>
            <if test="errorMsg != null">
                #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="defaultPlan != null">
                #{defaultPlan,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="staffGrpPrem != null">
                #{staffGrpPrem,jdbcType=DOUBLE},
            </if>
            <if test="familyGrpPrem != null">
                #{familyGrpPrem,jdbcType=DOUBLE},
            </if>
            <if test="studentGrpPrem != null">
                #{studentGrpPrem,jdbcType=DOUBLE},
            </if>
            <if test="relationship != null">
                #{relationship,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updatetFcensureconfig" parameterType="java.util.Map">
        update fcensureconfig
        set ensureCode=#{params.ensureCode,jdbcType=VARCHAR},
            Operator=#{params.Operator,jdbcType=VARCHAR},
            ModifyDate=str_to_date(#{params.currentDate,jdbcType=VARCHAR}, '%Y-%m-%d'),
            ModifyTime=#{params.currentTime,jdbcType=VARCHAR}
        where grpno = #{params.grpNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCPerInfoTemp">
        update fcperinfotemp
        <set>
            <if test="ensureCode != null">
                EnsureCode = #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="CPerNo != null">
                CPerNo = #{CPerNo,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                GrpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="staffNo != null">
                StaffNo = #{staffNo,jdbcType=VARCHAR},
            </if>
            <if test="levelCode != null">
                LevelCode = #{levelCode,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="birthDay != null">
                BirthDay = #{birthDay,jdbcType=DATE},
            </if>
            <if test="phone != null">
                Phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                OccupationType = #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                MedProtectType = #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                Email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                Nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="serviceTerm != null">
                ServiceTerm = #{serviceTerm,jdbcType=VARCHAR},
            </if>
            <if test="retirement != null">
                Retirement = #{retirement,jdbcType=VARCHAR},
            </if>
            <if test="impotStatus != null">
                ImpotStatus = #{impotStatus,jdbcType=VARCHAR},
            </if>
            <if test="subStaus != null">
                SubStaus = #{subStaus,jdbcType=VARCHAR},
            </if>
            <if test="errorMsg != null">
                ErrorMsg = #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="defaultPlan != null">
                DefaultPlan = #{defaultPlan,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                openbank = #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                OpenAccount = #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="staffGrpPrem != null">
                StaffGrpPrem = #{staffGrpPrem,jdbcType=DOUBLE},
            </if>
            <if test="familyGrpPrem != null">
                FamilyGrpPrem = #{familyGrpPrem,jdbcType=DOUBLE},
            </if>
            <if test="studentGrpPrem != null">
                StudentGrpPrem = #{studentGrpPrem,jdbcType=DOUBLE},
            </if>
            <if test="relationship != null">
                relationship = #{relationship,jdbcType=VARCHAR},
            </if>
        </set>
        where PerTempNo = #{perTempNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByIdNo" parameterType="java.util.Map">
        update fcperinfotemp
        <set>
            <if test="mobilePhone != null and mobilePhone!='' ">
                MobilePhone=#{mobilePhone},
            </if>
        </set>
        where IDNo=#{idNo}
    </update>
    <update id="updateByensureCode">
        update fcperinfotemp
        <set>
            <if test="mobilePhone != null and mobilePhone!='' ">
                MobilePhone=#{mobilePhone},
            </if>
            <if test="staffGrpPrem != null and staffGrpPrem!='' ">
                StaffGrpPrem=#{staffGrpPrem},
            </if>
        </set>
        where IDNo=#{idNo} and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </update>
    <select id="getIdTypeList" parameterType="java.lang.String" resultMap="BaseResultMapN">
        select codename
        from fdcode
        where codetype = 'IDTypeNew'
    </select>
    <select id="getIdTypeCodeList" parameterType="java.lang.String" resultMap="BaseResultMapC">
        select codekey
        from fdcode
        where codetype = 'IDTypeNew'
    </select>
    <select id="getOccupationTypeList" parameterType="java.lang.String" resultMap="BaseResultMapC">
        select DISTINCT OtherSign as codekey
        from fdcode
        where codetype = 'Occupationdetail'
    </select>
    <select id="getOccupationCodeList" parameterType="java.lang.String" resultMap="BaseResultMapC">
        select codekey
        from fdcode
        where codetype = 'Occupationdetail'
    </select>
    <select id="getOpenBankList" parameterType="java.lang.String" resultMap="BaseResultMapC">
        select codekey
        from fdcode
        where codetype = 'Bank'
    </select>
    <select id="selectHas" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from fcperinfotemp
        where EnsureCode = #{params.ensureCode,jdbcType=VARCHAR}
    </select>
    <select id="selectFcPerInfoTemp" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FCPerInfoTemp">
        select *
        from FCPerInfoTemp
        where PerTempNo = #{perNo,jdbcType=VARCHAR}
    </select>

    <select id="checkOneOtherIsEsistsTemp" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1) from FCPerInfoTemp where idno=#{idNo,jdbcType=VARCHAR} AND ensureCode =
        #{ensureCode,jdbcType=VARCHAR}
        and (name &lt;&gt; #{name,jdbcType=VARCHAR} or
        sex &lt;&gt; #{sex,jdbcType=VARCHAR} or
        date_format(birthday,'%Y-%m-%d') &lt;&gt; #{birthday,jdbcType=VARCHAR} or
        idtype &lt;&gt; #{idType,jdbcType=VARCHAR})
        <if test=" perTempNo != null and perTempNo != '' ">
            AND perTempNo != #{perTempNo,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getPlanByEnsureCode" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT p.`name` AS perName, p.DefaultPlan AS defaultPlan
        FROM fcperinfotemp p
        WHERE p.EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          AND p.DefaultPlan IS NOT NULL
          AND p.DefaultPlan != ''
    </select>

    <select id="getPerInfoByEnsureCode" parameterType="java.util.Map"
            resultType="com.sinosoft.eflex.model.FCPerInfoTemp">
        select * from fcperinfotemp where ensureCode = #{ensureCode,jdbcType = VARCHAR} AND ImpotStatus = '01'
        <if test=" name != null and name != '' ">
            and Name LIKE CONCAT(CONCAT('%',#{name},'%'))
        </if>
        <if test=" sex != null and sex != '' ">
            and Sex = #{sex}
        </if>
        <if test=" iDType != null and iDType != '' ">
            and IDType = #{iDType}
        </if>
        <if test=" iDNo != null and iDNo != '' ">
            and IDNo = #{iDNo}
        </if>
    </select>
    <select id="selectLevelCodeByGrpNo" resultType="java.lang.String">
        select LevelCode
        from fcperinfotemp
        where GrpNo = #{grpNo}
          and LevelCode is not null
    </select>
    <select id="selectLevelCodeByEnsureCodeAndIdNo" resultType="java.lang.String">
        select LevelCode
        from fcperinfotemp
        where ensureCode = #{ensureCode}
          and idNo = #{idNo}
          and LevelCode is not null
    </select>
    <select id="selectByGrpNoAndParams" resultType="java.util.Map">
        SELECT a.PerNo AS perNo,
        a.GrpNo AS grpNo,
        IFNULL(a.LevelCode,'') AS levelCode,
        a.Name AS name,
        a.IDType AS iDType,
        f2.CodeName AS iDTypeName,
        a.IDNo AS iDNo,
        DATE_FORMAT(a.BirthDay,'%Y-%m-%d') AS birthDay,
        a.Sex AS sex,
        DATE_FORMAT(a.idTypeEndDate,'%Y-%m-%d') AS idTypeEndDate,
        f1.CodeName AS sexName,
        a.MobilePhone AS mobilePhone,
        a.OccupationType AS occupationType,
        a.OccupationCode AS occupationCode,
        a.nativeplace AS nativeplace,
        f5.CodeName AS nativeplaceName,
        f4.CodeName AS occupationCodeName,
        a.JoinMedProtect AS joinMedProtect,
        IFNULL(a.Retirement,'') AS retirement,
        IFNULL(f3.CodeName,'') AS retirementName,
        c.`GradeLevelCode` AS levelCodeName
        FROM fcperinfo a
        INNER JOIN fcbuspersontype c ON a.`LevelCode`=c.`OrderNum` AND a.`GrpNo`=c.`GrpNo`
        LEFT JOIN fdcode f1 ON f1.CodeType = 'Sex' AND f1.CodeKey = a.Sex
        LEFT JOIN fdcode f2 ON f2.CodeType = 'IDType' AND f2.CodeKey = a.IDType
        LEFT JOIN fdcode f3 ON f3.CodeType = 'isRetire' AND f3.CodeKey = a.Retirement
        LEFT JOIN fdcode f4 ON f4.CodeType = 'OccupationDetail' AND f4.CodeKey = a.OccupationCode
        LEFT JOIN fdcode f5 ON f5.CodeType = 'Nativeplace' AND f5.CodeKey = a.nativeplace
        WHERE a.GrpNo =#{grpNo}
        <if test="name != null and name != ''">
            and a.Name like concat('%',#{name},'%')
        </if>
        <if test="iDType != null and iDType != ''">
            and a.IDType = #{iDType}
        </if>
        <if test="iDNo != null and iDNo != ''">
            and a.IDNo = #{iDNo}
        </if>
        <if test="levelCode != null and levelCode != ''">
            and a.LevelCode = #{levelCode}
        </if>
        <if test="retirement != null and retirement != ''">
            and a.Retirement = #{retirement}
        </if>
        UNION
        SELECT a.PerNo AS perNo,
        a.GrpNo AS grpNo,
        IFNULL(a.LevelCode,'') AS levelCode,
        a.Name AS name,
        a.IDType AS iDType,
        f2.CodeName AS iDTypeName,
        a.IDNo AS iDNo,
        DATE_FORMAT(a.BirthDay,'%Y-%m-%d') AS birthDay,
        a.Sex AS sex,
        DATE_FORMAT(a.idTypeEndDate,'%Y-%m-%d') AS idTypeEndDate,
        f1.CodeName AS sexName,
        a.MobilePhone AS mobilePhone,
        a.OccupationType AS occupationType,
        a.OccupationCode AS occupationCode,
        a.nativeplace AS nativeplace,
        f5.CodeName AS nativeplaceName,
        f4.CodeName AS occupationCodeName,
        a.JoinMedProtect AS joinMedProtect,
        IFNULL(a.Retirement,'') AS retirement,
        IFNULL(f3.CodeName,'') AS retirementName,
        c.`GradeLevelCode` AS levelCodeName
        FROM fcperinfo a
        LEFT JOIN fdcode f1 ON f1.CodeType = 'Sex' AND f1.CodeKey = a.Sex
        LEFT JOIN fdcode f2 ON f2.CodeType = 'IDType' AND f2.CodeKey = a.IDType
        LEFT JOIN fdcode f3 ON f3.CodeType = 'isRetire' AND f3.CodeKey = a.Retirement
        LEFT JOIN fdcode f4 ON f4.CodeType = 'OccupationDetail' AND f4.CodeKey = a.OccupationCode
        LEFT JOIN fdcode f5 ON f5.CodeType = 'Nativeplace' AND f5.CodeKey = a.nativeplace
        LEFT JOIN fcbuspersontype c ON a.`LevelCode`=c.`OrderNum` AND a.`GrpNo`=c.`GrpNo`
        WHERE a.GrpNo =#{grpNo}
        and a.perno IN( SELECT DISTINCT z.perno FROM fcperregistday z WHERE z.ensureCode IN
        (SELECT h.ensureCode FROM fcensure h WHERE h.grpNo=#{grpNo} AND h.EnsureState IN ('1','015'))
        union
        select fd.PerNo from fcorder fd,fcgrporder fg where fd.GrpOrderNo=fg.GrpOrderNo
        and fg.grpNo=#{grpNo} and fg.GrpOrderStatus ='04'
        )
        <if test="name != null and name != ''">
            and a.Name like concat('%',#{name},'%')
        </if>
        <if test="iDType != null and iDType != ''">
            and a.IDType = #{iDType}
        </if>
        <if test="iDNo != null and iDNo != ''">
            and a.IDNo = #{iDNo}
        </if>
        <if test="levelCode != null and levelCode != ''">
            and a.LevelCode = #{levelCode}
        </if>
        <if test="retirement != null and retirement != ''">
            and a.Retirement = #{retirement}
        </if>
    </select>
    <!--    <select id="selectByGrpNoAndParams" resultType="java.util.Map">
        (SELECT  a.PerNo          AS perNo,
        a.GrpNo          AS grpNo,
        IFNULL(a.LevelCode,'')      AS levelCode,
        a.Name           AS name,
        a.IDType         AS iDType,
        f2.CodeName      AS iDTypeName,
        a.IDNo           AS iDNo,
        DATE_FORMAT(a.BirthDay,'%Y-%m-%d')   AS birthDay,
        a.Sex            AS sex,
        DATE_FORMAT(a.idTypeEndDate,'%Y-%m-%d') AS idTypeEndDate,
        f1.CodeName      AS sexName,
        a.MobilePhone    AS mobilePhone,
        a.OccupationType AS occupationType,
        a.OccupationCode AS occupationCode,
        a.nativeplace    AS nativeplace,
        f5.CodeName      AS nativeplaceName,
        f4.CodeName      AS occupationCodeName,
        a.JoinMedProtect AS joinMedProtect,
        IFNULL(a.Retirement,'')     AS retirement,
        IFNULL(f3.CodeName,'')        AS retirementName,
        c.`GradeLevelCode` AS levelCodeName
        FROM fcperinfo a
        INNER JOIN fcbuspersontype c ON a.`LevelCode`=c.`OrderNum` AND a.`GrpNo`=c.`GrpNo`
        LEFT JOIN fdcode f1 ON f1.CodeType = 'Sex' AND f1.CodeKey = a.Sex
        LEFT JOIN fdcode f2 ON f2.CodeType = 'IDType' AND f2.CodeKey = a.IDType
        LEFT JOIN fdcode f3 ON f3.CodeType = 'isRetire' AND f3.CodeKey = a.Retirement
        LEFT JOIN fdcode f4 ON f4.CodeType = 'OccupationDetail' AND f4.CodeKey = a.OccupationCode
        LEFT JOIN fdcode f5 ON f5.CodeType = 'Nativeplace' AND f5.CodeKey = a.nativeplace
        WHERE a.GrpNo = #{grpNo}
        <if test="name != null and name != ''">
            and a.Name like concat('%',#{name},'%')
        </if>
        <if test="iDType != null and iDType != ''">
            and a.IDType = #{iDType}
        </if>
        <if test="iDNo != null and iDNo != ''">
            and a.IDNo = #{iDNo}
        </if>
        <if test="levelCode != null and levelCode != ''">
            and a.LevelCode = #{levelCode}
        </if>
        <if test="retirement != null and retirement != ''">
            and a.Retirement = #{retirement}
        </if>)
        UNION(
        SELECT  d.PerNo          AS perNo,
        d.GrpNo          AS grpNo,
        IFNULL(d.LevelCode,'')      AS levelCode,
        d.Name           AS name,
        d.IDType         AS iDType,
        f2.CodeName      AS iDTypeName,
        d.IDNo           AS iDNo,
        DATE_FORMAT(d.BirthDay,'%Y-%m-%d')   AS birthDay,
        d.Sex            AS sex,
        DATE_FORMAT(d.idTypeEndDate,'%Y-%m-%d') AS idTypeEndDate,
        f1.CodeName      AS sexName,
        d.MobilePhone    AS mobilePhone,
        d.OccupationType AS occupationType,
        d.OccupationCode AS occupationCode,
        d.nativeplace    AS nativeplace,
        f5.CodeName      AS nativeplaceName,
        f4.CodeName      AS occupationCodeName,
        d.JoinMedProtect AS joinMedProtect,
        IFNULL(d.Retirement,'')     AS retirement,
        IFNULL(f3.CodeName,'')        AS retirementName,
        '' AS levelCodeName
        FROM fcensure a
        INNER JOIN fcgrporder b ON a.`GrpNo`=b.grpNo AND a.ensureCode=b.ensureCode
        INNER JOIN fcorder c ON a.grpNo=c.grpno AND b.GrpOrderNo=c.GrpOrderNo
        INNER JOIN fcperinfo d ON c.perno =d.perno
        LEFT JOIN fdcode f1 ON f1.CodeType = 'Sex' AND f1.CodeKey = d.Sex
        LEFT JOIN fdcode f2 ON f2.CodeType = 'IDType' AND f2.CodeKey = d.IDType
        LEFT JOIN fdcode f3 ON f3.CodeType = 'isRetire' AND f3.CodeKey = d.Retirement
        LEFT JOIN fdcode f4 ON f4.CodeType = 'OccupationDetail' AND f4.CodeKey = d.OccupationCode
        LEFT JOIN fdcode f5 ON f5.CodeType = 'Nativeplace' AND f5.CodeKey = d.nativeplace
        WHERE a.grpno= #{grpNo} AND a.EnsureState IN ('1','015')
        <if test="name != null and name != ''">
            and a.Name like concat('%',#{name},'%')
        </if>
        <if test="iDType != null and iDType != ''">
            and a.IDType = #{iDType}
        </if>
        <if test="iDNo != null and iDNo != ''">
            and a.IDNo = #{iDNo}
        </if>
        <if test="levelCode != null and levelCode != ''">
            and a.LevelCode = #{levelCode}
        </if>
        <if test="retirement != null and retirement != ''">
            and a.Retirement = #{retirement}
        </if>
        )
    </select>-->
    <select id="selectTempByGrpNo" resultType="java.util.Map">
        select DISTINCT
        a.PerTempNo as perTempNo,
        a.EnsureCode as ensureCode,
        a.GrpNo as grpNo,
        a.LevelCode as levelCode,
        a.Name as name,
        a.IDType as iDType,
        f2.CodeName as iDTypeName,
        a.IDNo as iDNo,
        date_format(a.BirthDay,'%Y-%m-%d') as birthDay,
        a.Sex as sex,
        date_format(a.idTypeEndDate,'%Y-%m-%d') as idTypeEndDate,
        f1.CodeName as sexName,
        a.MobilePhone as mobilePhone,
        a.OccupationType as occupationType,
        a.OccupationCode as occupationCode,
        a.nativeplace as nativeplace,
        f5.CodeName as nativeplaceName,
        f4.CodeName as occupationCodeName,
        a.JoinMedProtect as joinMedProtect,
        a.SubStaus as SubStaus,
        a.Retirement as retirement,
        a.relationship as relationship,
        f3.CodeName as retirementName,
        b.`GradeLevelCode` AS levelCodeName
        from fcperinfotemp a
        INNER JOIN fcbuspersontype b ON a.`GrpNo`=b.`GrpNo` AND a.`LevelCode`=b.`OrderNum`
        left join fdcode f1 on f1.CodeType = 'Sex' and f1.CodeKey = a.Sex
        left join fdcode f2 on f2.CodeType = 'IDType' and f2.CodeKey = a.IDType
        left join fdcode f3 on f3.CodeType = 'isRetire' and f3.CodeKey = a.Retirement
        left join fdcode f4 on f4.CodeType = 'OccupationDetail' and f4.CodeKey = a.OccupationCode
        left join fdcode f5 on f5.CodeType = 'Nativeplace' and f5.CodeKey = a.nativeplace
        where a.GrpNo = #{grpNo} and a.ImpotStatus = '01' and a.EnsureCode = '' and a.SubStaus = '01'
        <if test="name != null and name != ''">
            and a.Name like concat('%',#{name},'%')
        </if>
        <if test="iDType != null and iDType != ''">
            and a.IDType = #{iDType}
        </if>
        <if test="iDNo != null and iDNo != ''">
            and a.IDNo = #{iDNo}
        </if>
        <if test="levelCode != null and levelCode != ''">
            and a.LevelCode = #{levelCode}
        </if>
        <if test="retirement != null and retirement != ''">
            and a.Retirement = #{retirement}
        </if>
    </select>


    <select id="selectByGrpNo" resultMap="BaseResultMapA">
        select
        <include refid="Base_Column_List"/>
        from fcperinfotemp
        where GrpNo =#{grpNo} and ImpotStatus = '01' and EnsureCode = '' and SubStaus = '01'
    </select>

    <select id="selectByMobilePhone" resultMap="BaseResultMapA">
        select
        <include refid="Base_Column_List"/>
        from fcperinfotemp
        where MobilePhone =#{mobilePhone} and ImpotStatus = '01'
    </select>


    <select id="selectByGrpNoIdNo" resultMap="BaseResultMapA">
        select
        <include refid="Base_Column_List"/>
        from fcperinfotemp where GrpNo = #{grpNo} and IDNo = #{idNo}
        <if test="perTempNo != null and perTempNo != ''">
            and PerTempNo != #{perTempNo}
        </if>
    </select>
    <select id="selectByIdNo" resultMap="BaseResultMapA">
        select
        <include refid="Base_Column_List"/>
        from fcperinfotemp where IDNo = #{idNo}
    </select>

    <select id="selectByEnsureCode" resultMap="BaseResultMapA">
        select
        <include refid="Base_Column_List"/>
        from fcperinfotemp where ensureCode = #{ensureCode} order by  PerTempNo
    </select>
    <update id="updateBatchById">
        <foreach collection="list" item="item" separator=";">
            update fcperinfotemp
            <set>
                <if test="item.ensureCode != null">
                    EnsureCode = #{item.ensureCode,jdbcType=VARCHAR},
                </if>
                <if test="item.CPerNo != null">
                    CPerNo = #{item.CPerNo,jdbcType=VARCHAR},
                </if>
                <if test="item.grpNo != null">
                    GrpNo = #{item.grpNo,jdbcType=VARCHAR},
                </if>
                <if test="item.staffNo != null">
                    StaffNo = #{item.staffNo,jdbcType=VARCHAR},
                </if>
                <if test="item.levelCode != null">
                    LevelCode = #{item.levelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.department != null">
                    department = #{item.department,jdbcType=VARCHAR},
                </if>
                <if test="item.name != null">
                    Name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.sex != null">
                    Sex = #{item.sex,jdbcType=VARCHAR},
                </if>
                <if test="item.IDType != null">
                    IDType = #{item.IDType,jdbcType=VARCHAR},
                </if>
                <if test="item.IDNo != null">
                    IDNo = #{item.IDNo,jdbcType=VARCHAR},
                </if>
                <if test="item.idTypeEndDate != null">
                    idTypeEndDate = #{item.idTypeEndDate,jdbcType=DATE},
                </if>
                <if test="item.birthDay != null">
                    BirthDay = #{item.birthDay,jdbcType=DATE},
                </if>
                <if test="item.phone != null">
                    Phone = #{item.phone,jdbcType=VARCHAR},
                </if>
                <if test="item.mobilePhone != null">
                    MobilePhone = #{item.mobilePhone,jdbcType=VARCHAR},
                </if>
                <if test="item.occupationType != null">
                    OccupationType = #{item.occupationType,jdbcType=VARCHAR},
                </if>
                <if test="item.occupationCode != null">
                    OccupationCode = #{item.occupationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.joinMedProtect != null">
                    JoinMedProtect = #{item.joinMedProtect,jdbcType=VARCHAR},
                </if>
                <if test="item.medProtectType != null">
                    MedProtectType = #{item.medProtectType,jdbcType=VARCHAR},
                </if>
                <if test="item.email != null">
                    Email = #{item.email,jdbcType=VARCHAR},
                </if>
                <if test="item.zipCode != null">
                    ZipCode = #{item.zipCode,jdbcType=VARCHAR},
                </if>
                <if test="item.address != null">
                    Address = #{item.address,jdbcType=VARCHAR},
                </if>
                <if test="item.nativeplace != null">
                    Nativeplace = #{item.nativeplace,jdbcType=VARCHAR},
                </if>
                <if test="item.serviceTerm != null">
                    ServiceTerm = #{item.serviceTerm,jdbcType=VARCHAR},
                </if>
                <if test="item.retirement != null">
                    Retirement = #{item.retirement,jdbcType=VARCHAR},
                </if>
                <if test="item.impotStatus != null">
                    ImpotStatus = #{item.impotStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.subStaus != null">
                    SubStaus = #{item.subStaus,jdbcType=VARCHAR},
                </if>
                <if test="item.errorMsg != null">
                    ErrorMsg = #{item.errorMsg,jdbcType=VARCHAR},
                </if>
                <if test="item.operatorCom != null">
                    OperatorCom = #{item.operatorCom,jdbcType=VARCHAR},
                </if>
                <if test="item.operator != null">
                    Operator = #{item.operator,jdbcType=VARCHAR},
                </if>
                <if test="item.makeDate != null">
                    MakeDate = #{item.makeDate,jdbcType=DATE},
                </if>
                <if test="item.makeTime != null">
                    MakeTime = #{item.makeTime,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyDate != null">
                    ModifyDate = #{item.modifyDate,jdbcType=DATE},
                </if>
                <if test="item.modifyTime != null">
                    ModifyTime = #{item.modifyTime,jdbcType=VARCHAR},
                </if>
                <if test="item.defaultPlan != null">
                    DefaultPlan = #{item.defaultPlan,jdbcType=VARCHAR},
                </if>
                <if test="item.openBank != null">
                    openbank = #{item.openBank,jdbcType=VARCHAR},
                </if>
                <if test="item.openAccount != null">
                    OpenAccount = #{item.openAccount,jdbcType=VARCHAR},
                </if>
                <if test="item.staffGrpPrem != null">
                    StaffGrpPrem = #{item.staffGrpPrem,jdbcType=DOUBLE},
                </if>
                <if test="item.familyGrpPrem != null">
                    FamilyGrpPrem = #{item.familyGrpPrem,jdbcType=DOUBLE},
                </if>
                <if test="item.studentGrpPrem != null">
                    StudentGrpPrem = #{item.studentGrpPrem,jdbcType=DOUBLE},
                </if>
                <if test="item.relationship != null">
                    relationship = #{item.relationship,jdbcType=VARCHAR},
                </if>
            </set>
            where
            <choose>
                <when test="item.edorType == 'IC' and item.edorType != null and item.edorType != ''">
                    IDNo = #{item.oldIdNo}
                </when>
                <otherwise>
                    IDNo = #{item.IDNo}
                </otherwise>
            </choose>

        </foreach>
    </update>
    <select id="selectLevelCodeByEnsureCodeAndIdNo2" resultType="com.sinosoft.eflex.model.FCPerInfoTemp">
        select *
        from fcperinfotemp
        where EnsureCode = #{ensureCode}
          and IDNo = #{idNo} limit 1
    </select>
    <select id="selectByEnsureCodeAndIdNo" resultType="com.sinosoft.eflex.model.FCPerInfoTemp">
        select *
        from fcperinfotemp
        where EnsureCode = #{ensureCode}
          and IDNo = #{idNo} limit 1
    </select>
</mapper>
