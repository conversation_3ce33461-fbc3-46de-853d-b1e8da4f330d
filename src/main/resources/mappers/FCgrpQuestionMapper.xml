<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCgrpQuestionMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCgrpQuestion">
    <id column="SerialNum" jdbcType="VARCHAR" property="serialNum" />
    <result column="BigType" jdbcType="VARCHAR" property="bigType" />
    <result column="SmallType" jdbcType="VARCHAR" property="smallType" />
    <result column="Question" jdbcType="VARCHAR" property="question" />
    <result column="Answer" jdbcType="VARCHAR" property="answer" />
  </resultMap>
  <select id="getQuestionInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
      a.SerialNum, a.BigType, a.SmallType, a.Question, a.Answer 
    FROM  FCgrpQuestion a order by (a.SerialNum+0)
  </select>
</mapper>