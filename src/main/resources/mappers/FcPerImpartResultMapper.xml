<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcPerImpartResultMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcPerImpartResult">
        <id column="OrderItemNo" jdbcType="VARCHAR" property="orderItemNo"/>
        <id column="ImpartVer" jdbcType="VARCHAR" property="impartVer"/>
        <id column="ImpartCode" jdbcType="VARCHAR" property="impartCode"/>
        <result column="ImpartResult" jdbcType="VARCHAR" property="impartResult"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    OrderItemNo, ImpartVer, ImpartCode, ImpartResult, MakeDate, MakeTime, ModifyDate, 
    ModifyTime
  </sql>
    <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperimpartresult
        where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
        and ImpartVer = #{impartVer,jdbcType=VARCHAR}
        and ImpartCode = #{impartCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcperimpartresult
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
      and ImpartVer = #{impartVer,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </delete>
    <delete id="deleteByOrderItemNo" parameterType="java.lang.String">
    delete from fcperimpartresult
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
  </delete>
    <delete id="deleteByOrderNo" parameterType="java.lang.String">
    delete from fcperimpartresult
    where OrderItemNo in (select OrderItemNo from fcorderitem where orderno = #{orderNo,jdbcType=VARCHAR})
  </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FcPerImpartResult">
    insert into fcperimpartresult (OrderItemNo, ImpartVer, ImpartCode, 
      ImpartResult, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{orderItemNo,jdbcType=VARCHAR}, #{impartVer,jdbcType=VARCHAR}, #{impartCode,jdbcType=VARCHAR}, 
      #{impartResult,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcPerImpartResult">
        insert into fcperimpartresult
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderItemNo != null">
                OrderItemNo,
            </if>
            <if test="impartVer != null">
                ImpartVer,
            </if>
            <if test="impartCode != null">
                ImpartCode,
            </if>
            <if test="impartResult != null">
                ImpartResult,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderItemNo != null">
                #{orderItemNo,jdbcType=VARCHAR},
            </if>
            <if test="impartVer != null">
                #{impartVer,jdbcType=VARCHAR},
            </if>
            <if test="impartCode != null">
                #{impartCode,jdbcType=VARCHAR},
            </if>
            <if test="impartResult != null">
                #{impartResult,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcPerImpartResult">
        update fcperimpartresult
        <set>
            <if test="impartResult != null">
                ImpartResult = #{impartResult,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
        and ImpartVer = #{impartVer,jdbcType=VARCHAR}
        and ImpartCode = #{impartCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcPerImpartResult">
    update fcperimpartresult
    set ImpartResult = #{impartResult,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
      and ImpartVer = #{impartVer,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
    </update>
    <insert id="insertSameResultImpartList" parameterType="com.sinosoft.eflex.model.FcPerImpartResult">
        insert into fcperimpartresult (OrderItemNo, ImpartVer, ImpartCode,ImpartResult, MakeDate, MakeTime,ModifyDate, ModifyTime)
          select
            #{orderItemNo} as OrderItemNo,
            ImpartVer,
            ImpartCode,
            ImpartParamModle as ImpartResult,
            CURRENT_DATE as MakeDate,
            CURRENT_TIME as MakeTime,
            CURRENT_DATE as ModifyDate,
            CURRENT_TIME as ModifyTime
          from FdPerImpart where ImpartVer='D008' and state = '1';
    </insert>
    <select id="selectByOrderItemNo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.makeProposalForm.InsuredImpart">
        select
           a.ImpartVer as impver,
           a.ImpartCode,
           b.ImpartContent,
           a.ImpartResult as ImpartReply
        from fcperimpartresult a
        left join fdperimpart b on a.ImpartVer = b.ImpartVer and a.ImpartCode = b.ImpartCode
        where a.OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
    </select>
</mapper>