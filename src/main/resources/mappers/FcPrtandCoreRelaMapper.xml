<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcPrtandCoreRelaMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcPrtandCoreRela">
        <id column="RelaSn" jdbcType="VARCHAR" property="relaSn"/>
        <result column="PrtNo" jdbcType="VARCHAR" property="prtNo"/>
        <result column="tPrtNo" jdbcType="VARCHAR" property="tPrtNo"/>
        <result column="Status" jdbcType="VARCHAR" property="status"/>
        <result column="Describe" jdbcType="VARCHAR" property="describe"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        RelaSn
        , PrtNo, tPrtNo, `Status`,`Describe`, MakeDate, MakeTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcprtandcorerela
        where RelaSn = #{relaSn,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcprtandcorerela
    where RelaSn = #{relaSn,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FcPrtandCoreRela">
    insert into fcprtandcorerela (RelaSn, PrtNo, tPrtNo, 
      Status, Describe, MakeDate, 
      MakeTime)
    values (#{relaSn,jdbcType=VARCHAR}, #{prtNo,jdbcType=VARCHAR}, #{tPrtNo,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{describe,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcPrtandCoreRela">
        insert into fcprtandcorerela
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="relaSn != null">
                RelaSn,
            </if>
            <if test="prtNo != null">
                PrtNo,
            </if>
            <if test="tPrtNo != null">
                tPrtNo,
            </if>
            <if test="status != null">
                Status,
            </if>
            <if test="describe != null">
                `Describe`,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="relaSn != null">
                #{relaSn,jdbcType=VARCHAR},
            </if>
            <if test="prtNo != null">
                #{prtNo,jdbcType=VARCHAR},
            </if>
            <if test="tPrtNo != null">
                #{tPrtNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="describe != null">
                #{describe,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcPrtandCoreRela">
        update fcprtandcorerela
        <set>
            <if test="prtNo != null">
                PrtNo = #{prtNo,jdbcType=VARCHAR},
            </if>
            <if test="tPrtNo != null">
                tPrtNo = #{tPrtNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                Status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="describe != null">
                Describe = #{describe,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
        </set>
        where RelaSn = #{relaSn,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcPrtandCoreRela">
    update fcprtandcorerela
    set PrtNo = #{prtNo,jdbcType=VARCHAR},
      tPrtNo = #{tPrtNo,jdbcType=VARCHAR},
      Status = #{status,jdbcType=VARCHAR},
      Describe = #{describe,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR}
    where RelaSn = #{relaSn,jdbcType=VARCHAR}
  </update>
    <select id="selectCoreReturnMsg" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FcPrtandCoreRela">
        select a.RelaSn,
               a.PrtNo,
               a.tPrtNo,
               a.Status,
               a.Describe,
               a.MakeDate,
               a.MakeTime
        from fcprtandcorerela a
                 LEFT JOIN fcgrpOrder b ON a.PrtNo = b.PrtNo
        WHERE b.ensureCode = #{ensureCode}
        ORDER BY a.MakeDate DESC, a.MakeTime DESC limit 1
    </select>
    <select id="selectByTPrtNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcPrtandCoreRela">
        select
          a.RelaSn, a.PrtNo, a.tPrtNo, a.Status, a.Describe, a.MakeDate, a.MakeTime
        from fcprtandcorerela a
        where a.tPrtNo = #{tPrtNo};
  </select>
    <select id="selectByPrtNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcPrtandCoreRela">
        select
            a.RelaSn, a.PrtNo, a.tPrtNo, a.Status, a.Describe, a.MakeDate, a.MakeTime
        from fcprtandcorerela a
        where a.PrtNo = #{PrtNo} order by MakeDate,MakeTime desc limit 1;
    </select>
</mapper>