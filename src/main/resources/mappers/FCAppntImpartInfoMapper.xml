<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCAppntImpartInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCAppntImpartInfo">
    <id column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <id column="ImpartCode" jdbcType="VARCHAR" property="impartCode" />
    <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
    <result column="ImpartParamModle" jdbcType="VARCHAR" property="impartParamModle" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    EnsureCode, ImpartCode, GrpNo, ImpartParamModle, Operator, OperatorCom, MakeDate, 
    MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCAppntImpartInfoKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcappntimpartinfo
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCAppntImpartInfoKey">
    delete from fcappntimpartinfo
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCAppntImpartInfo">
    insert into fcappntimpartinfo (EnsureCode, ImpartCode, GrpNo, 
      ImpartParamModle, Operator, OperatorCom, 
      MakeDate, MakeTime, ModifyDate, 
      ModifyTime)
    values (#{ensureCode,jdbcType=VARCHAR}, #{impartCode,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR}, 
      #{impartParamModle,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, 
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, 
      #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCAppntImpartInfo">
    insert into fcappntimpartinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ensureCode != null">
        EnsureCode,
      </if>
      <if test="impartCode != null">
        ImpartCode,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
      <if test="impartParamModle != null">
        ImpartParamModle,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ensureCode != null">
        #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="impartCode != null">
        #{impartCode,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="impartParamModle != null">
        #{impartParamModle,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCAppntImpartInfo">
    update fcappntimpartinfo
    <set>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="impartParamModle != null">
        ImpartParamModle = #{impartParamModle,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCAppntImpartInfo">
    update fcappntimpartinfo
    set GrpNo = #{grpNo,jdbcType=VARCHAR},
      ImpartParamModle = #{impartParamModle,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </update>

  <delete id="deleteImpartInfo" parameterType="java.util.Map">
    delete from fcappntimpartinfo
    where 1=1
    <if test="ensureCode != null and ensureCode != ''">
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </if>
    <if test="impartCode != null and impartCode != ''">
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
    </if>
    <if test="grpNo != null and grpNo != ''">
      and GrpNo = #{grpNo,jdbcType=VARCHAR}
    </if>
  </delete>

  <select id="selectList" parameterType="java.util.Map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fcappntimpartinfo
    where 1=1
    <if test="ensureCode != null and ensureCode != ''">
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </if>
  </select>

  <!-- 批量投保接口发送投保人团体告之 -->
  <select id="selectAppntImpartList" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.BatchInsureInterface.AppntImpartInfo">
    select
      b.ImpartVer, a.ImpartCode ,b.ImpartContent, a.ImpartParamModle
    from fcappntimpartinfo a
    LEFT JOIN fdimpartinfo b on a.ImpartCode = b.ImpartCode
    where a.EnsureCode = #{ensureCode,jdbcType=VARCHAR}
  </select>
  <select id="selectImpartInfo" resultType="com.sinosoft.eflex.model.dailyplan.AppntImpart">
    select
    b.ImpartVer impartVer,
    a.ImpartCode impartCode,
    b.ImpartContent impartContent,
    a.ImpartParamModle impartReply
    from fcappntimpartinfo a
    LEFT JOIN fdimpartinfo b on a.ImpartCode = b.ImpartCode
    where 1=1
    <if test="ensureCode != null and ensureCode != ''">
      and a.EnsureCode = #{ensureCode}
    </if>
  </select>
  <select id="selectAllImpartInfo" resultType="com.sinosoft.eflex.model.dailyplan.AppntImpart">
    select a.ImpartVer impartVer,
           a.ImpartCode impartCode,
           a.ImpartContent impartContent,
           a.ImpartParamModle impartReply
    from fdimpartinfo a
  </select>
</mapper>