<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcInsureEflexPlanMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcInsureEflexPlan">
        <id column="OrderItemDetailNo" jdbcType="VARCHAR" property="orderItemDetailNo"/>
        <id column="AmountGrageCode" jdbcType="VARCHAR" property="amountGrageCode"/>
        <result column="prem" jdbcType="DOUBLE" property="prem"/>
        <result column="DeductibleAttr" jdbcType="VARCHAR" property="deductibleAttr"/>
        <result column="Deductible" jdbcType="DOUBLE" property="deductible"/>
        <result column="CompensationRatio" jdbcType="DOUBLE" property="compensationRatio"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    OrderItemDetailNo, AmountGrageCode, prem, DeductibleAttr, Deductible, CompensationRatio, 
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
    <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcinsureeflexplan
        where OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
        and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
    </select>
    <select id="selectByPersonId" resultType="java.util.Map">
    SELECT b.EnsureCode, d.OrderItemNo,c.OrderNo,d.SelfPrem,d.GrpPrem,d.OrderItemDetailNo
    from fcorderinsured a,fcgrporder b,fcorder c,fcorderitem d
    where a.GrpOrderNo = b.GrpOrderNo
    and b.GrpOrderNo = c.GrpOrderNo
    and c.OrderNo = d.OrderNo
    and a.OrderItemNo = d.OrderItemNo
    and b.GrpOrderStatus = '04' and a.PersonID = #{personID}
  </select>
    <select id="getByPersonId" resultType="java.util.Map">
    SELECT b.EnsureCode, d.OrderItemNo,c.OrderNo,d.SelfPrem,d.GrpPrem,d.OrderItemDetailNo
    from fcorderinsured a,fcgrporder b,fcorder c,fcorderitem d,fcensure e
    where a.GrpOrderNo = b.GrpOrderNo
    and b.GrpOrderNo = c.GrpOrderNo
    and c.OrderNo = d.OrderNo
    and a.OrderItemNo = d.OrderItemNo
    and b.ensurecode=e.ensurecode
    and b.GrpOrderStatus = '04' and a.PersonID = #{personID}
    and e.plantype='1'
  </select>
    <select id="selectByorderItemDetailNo" resultType="java.util.Map">
        select b.RiskCode as riskCode,
        b.DutyCode as dutyCode,
        a.prem as prem,
        CAST(b.Amnt AS CHAR ) as amnt,
        if(a.`Deductible` is NULL ,'',CONVERT(a.`Deductible`,DECIMAL(20,2))) as deductible,
        if(a.`CompensationRatio` is NULL ,'',CONVERT(a.`CompensationRatio`,DECIMAL(20,2))) as compensationRatio
        from fcinsureeflexplan a,fcdutyamountgrade b
        where a.AmountGrageCode = b.AmountGrageCode
        and a.OrderItemDetailNo = #{orderItemDetailNo}
        union
        select c.RiskCode as riskCode,
        b.optDutyCode as dutyCode,
        b.prem as prem,
        d.Amnt as amnt,
        '' as deductible ,
        '' as compensationRatio
        from FcInsureEflexPlan a,FcInsureEflexPlanOptional b,FcDutyAmountGrade c,FcDutyGradeOptionalAmountInfo d
        where a.OrderItemDetailNo = b.OrderItemDetailNo
				and a.AmountGrageCode = b.AmountGrageCode
				and c.AmountGrageCode = d.AmountGrageCode
				and a.AmountGrageCode = c.AmountGrageCode
        and b.OptDutyCode = d.OptDutyCode
        and a.OrderItemDetailNo = #{orderItemDetailNo}
  </select>
    <select id="selectDetailByPersonId" resultType="java.util.Map">
        SELECT b.EnsureCode,c.OrderNo,b.GrpContNo,d.ContNo,d.OrderItemNo
    from fcorderinsured a,fcgrporder b,fcorder c,fcorderitem d,fcensure f
    where a.GrpOrderNo = b.GrpOrderNo
    and b.GrpOrderNo = c.GrpOrderNo
    and c.OrderNo = d.OrderNo
    and a.OrderItemNo = d.OrderItemNo
    and b.ensurecode=f.ensurecode
    and f.plantype in ('0','1')
    and b.GrpOrderStatus = '04' and a.PersonID = #{personID}
    order by f.cvaliDate
    </select>
    <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcinsureeflexplan
    where OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FcInsureEflexPlan">
    insert into fcinsureeflexplan (OrderItemDetailNo, AmountGrageCode, 
      prem, DeductibleAttr, Deductible, 
      CompensationRatio, Operator, OperatorCom, 
      MakeDate, MakeTime, ModifyDate, 
      ModifyTime)
    values (#{orderItemDetailNo,jdbcType=VARCHAR}, #{amountGrageCode,jdbcType=VARCHAR}, 
      #{prem,jdbcType=DOUBLE}, #{deductibleAttr,jdbcType=VARCHAR}, #{deductible,jdbcType=DOUBLE}, 
      #{compensationRatio,jdbcType=DOUBLE}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, 
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, 
      #{modifyTime,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcInsureEflexPlan">
        insert into fcinsureeflexplan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderItemDetailNo != null">
                OrderItemDetailNo,
            </if>
            <if test="amountGrageCode != null">
                AmountGrageCode,
            </if>
            <if test="prem != null">
                prem,
            </if>
            <if test="deductibleAttr != null">
                DeductibleAttr,
            </if>
            <if test="deductible != null">
                Deductible,
            </if>
            <if test="compensationRatio != null">
                CompensationRatio,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderItemDetailNo != null">
                #{orderItemDetailNo,jdbcType=VARCHAR},
            </if>
            <if test="amountGrageCode != null">
                #{amountGrageCode,jdbcType=VARCHAR},
            </if>
            <if test="prem != null">
                #{prem,jdbcType=DOUBLE},
            </if>
            <if test="deductibleAttr != null">
                #{deductibleAttr,jdbcType=VARCHAR},
            </if>
            <if test="deductible != null">
                #{deductible,jdbcType=DOUBLE},
            </if>
            <if test="compensationRatio != null">
                #{compensationRatio,jdbcType=DOUBLE},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcInsureEflexPlan">
        update fcinsureeflexplan
        <set>
            <if test="prem != null">
                prem = #{prem,jdbcType=DOUBLE},
            </if>
            <if test="deductibleAttr != null">
                DeductibleAttr = #{deductibleAttr,jdbcType=VARCHAR},
            </if>
            <if test="deductible != null">
                Deductible = #{deductible,jdbcType=DOUBLE},
            </if>
            <if test="compensationRatio != null">
                CompensationRatio = #{compensationRatio,jdbcType=DOUBLE},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
        and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcInsureEflexPlan">
    update fcinsureeflexplan
    set prem = #{prem,jdbcType=DOUBLE},
      DeductibleAttr = #{deductibleAttr,jdbcType=VARCHAR},
      Deductible = #{deductible,jdbcType=DOUBLE},
      CompensationRatio = #{compensationRatio,jdbcType=DOUBLE},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
  </update>
    <insert id="insertList" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            insert into FcInsureEflexPlan
            (OrderItemDetailNo, AmountGrageCode,
            <if test="item.deductibleAttr != null and item.deductibleAttr != ''">
                DeductibleAttr,
            </if>
            <if test="item.deductible != null and item.deductible != ''">
                Deductible,
            </if>
            <if test="item.compensationRatio != null and item.compensationRatio != ''">
                CompensationRatio,
            </if>
            Prem, Operator,
            OperatorCom, MakeDate, MakeTime,
            ModifyDate, ModifyTime)
            values(#{list[${index}].orderItemDetailNo,jdbcType=VARCHAR},
            #{list[${index}].amountGrageCode,jdbcType=VARCHAR},
            <if test="item.deductibleAttr != null and item.deductibleAttr != ''">
                #{list[${index}].deductibleAttr,jdbcType=VARCHAR},
            </if>
            <if test="item.deductible != null and item.deductible != ''">
                #{list[${index}].deductible,jdbcType=VARCHAR},
            </if>
            <if test="item.compensationRatio != null and item.compensationRatio != ''">
                #{list[${index}].compensationRatio,jdbcType=VARCHAR},
            </if>
            #{list[${index}].prem,jdbcType=VARCHAR},
            #{list[${index}].operator,jdbcType=VARCHAR},
            #{list[${index}].operatorCom,jdbcType=VARCHAR},
            #{list[${index}].makeDate,jdbcType=DATE},
            #{list[${index}].makeTime,jdbcType=VARCHAR},
            #{list[${index}].modifyDate,jdbcType=DATE},
            #{list[${index}].modifyTime,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="getRiskPremByOrderItemDetailNo" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT a.Prem AS prem,b.`RiskCode`,d.`RiskName`,e.`DutyRange` FROM FcInsureEflexPlan a
        INNER JOIN FcDutyAmountGrade b ON a.`AmountGrageCode` = b.`AmountGrageCode`
        INNER JOIN fdriskinfo d ON d.`RiskCode` = b.`RiskCode`
        INNER JOIN fdriskdutyinfo e ON e.`DutyCode` = b.`DutyCode`
        WHERE a.OrderItemDetailNo = #{orderItemDetailNo} AND a.AmountGrageCode = #{amountGrageCode}
    </select>

    <delete id="deleteByOrderNo" parameterType="java.lang.String">
        DELETE FROM FcInsureEflexPlan WHERE OrderItemDetailNo IN (SELECT OrderItemDetailNo FROM fcorderitem WHERE OrderNo = #{orderNo})
    </delete>

    <select id="getInsureRiskDutyListByOrderItemDetailNo" parameterType="java.lang.String" resultType="java.util.Map">
      SELECT
        a.`AmountGrageCode` AS AmountGrageCode,
        a.`prem` as Prem,
        b.`RiskCode` AS RiskCode,
        b.`RiskType` AS RiskType,
        b.`DutyCode` AS DutyCode,
        c.`CompensationRatio` AS CompensationRatio,
        CASE WHEN (b.`RiskCode` = '15070') THEN '' ELSE d.`Deductible` END AS  Deductible,
        CASE WHEN (b.`RiskCode` = '15070') THEN '' ELSE b.`AnnualTimeDeduction` END AS DeductibleAttr
      FROM FcInsureEflexPlan a
      INNER JOIN FcDutyAmountGrade b ON b.`AmountGrageCode` = a.`AmountGrageCode`
      INNER JOIN FcDutGradeCompensationRatio c ON c.`AmountGrageCode` = b.`AmountGrageCode`
      INNER JOIN FcDutyGroupDeductible d ON d.`AmountGrageCode` = b.`AmountGrageCode`
      WHERE a.`OrderItemDetailNo` = #{orderItemDetailNo}
    </select>

    <select id="getDutyAmntByRiskCode" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
		  b.`DutyCode` AS dutyCode,
		  b.`Amnt` AS amnt,
		  b.DiscountRatio as discountRatio,
		  a.`Prem` as prem,
          b.MaxGetDay as maxGetDay,
		  CASE WHEN (b.`RiskCode` = '15070') THEN '' ELSE a.`CompensationRatio` END AS compensationRatio,
		  CASE WHEN (b.`RiskCode` = '15070') THEN '' ELSE a.`Deductible` END AS deductible,
		  CASE WHEN (b.`RiskCode` = '15070') THEN '' ELSE a.`DeductibleAttr` END AS deductibleAttr
        FROM FcInsureEflexPlan a
        INNER JOIN FcDutyAmountGrade b ON a.`AmountGrageCode` = b.`AmountGrageCode` AND b.`RiskCode` = #{riskCode}
        WHERE a.OrderItemDetailNo = #{orderItemDetailNo}

        UNION

        SELECT
		  d.`OptDutyCode` AS dutyCode,
		  d.`Amnt` AS amnt,
		  '' AS discountRatio,
		  c.`Prem` as prem,
          d.MaxGetDay as maxGetDay,
		  CASE WHEN (e.`RiskCode` = '15070') THEN r1.`CompensationRatio` ELSE '' END AS  compensationRatio,
		  CASE WHEN (e.`RiskCode` = '15070') THEN db1.`Deductible` ELSE '' END AS  deductible,
		  CASE WHEN (e.`RiskCode` = '15070') THEN e.`AnnualTimeDeduction` ELSE '' END AS deductibleAttr
        FROM FcInsureEflexPlanOptional c
        INNER JOIN FcDutyGradeOptionalAmountInfo d ON d.`AmountGrageCode` = c.`AmountGrageCode` AND c.`OptDutyCode` = d.`OptDutyCode`
        INNER JOIN FcDutyAmountGrade e ON e.`AmountGrageCode` = d.`AmountGrageCode` AND e.`RiskCode` = #{riskCode}
        LEFT JOIN FcDutGradeCompensationRatio r1 ON r1.`AmountGrageCode` = d.`AmountGrageCode`
        LEFT JOIN FcDutyGroupDeductible db1 ON db1.`AmountGrageCode` = d.`AmountGrageCode`
        WHERE c.`OrderItemDetailNo` = #{orderItemDetailNo}
    </select>

    <select id="getRiskCodeByOrderItemDetailNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT b.`RiskCode` FROM FcInsureEflexPlan a
        INNER JOIN FcDutyAmountGrade b ON b.`AmountGrageCode` = a.`AmountGrageCode`
        WHERE a.`OrderItemDetailNo` = #{orderItemDetailNo}
        GROUP BY b.`RiskCode`
    </select>
</mapper>