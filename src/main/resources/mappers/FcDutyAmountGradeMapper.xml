<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcDutyAmountGradeMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcDutyAmountGrade">
    <id column="AmountGrageCode" jdbcType="VARCHAR" property="amountGrageCode" />
    <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <result column="RiskCode" jdbcType="VARCHAR" property="riskCode" />
    <result column="RiskType" jdbcType="VARCHAR" property="riskType" />
    <result column="RiskName" jdbcType="VARCHAR" property="riskName" />
    <result column="DutyCode" jdbcType="VARCHAR" property="dutyCode" />
    <result column="AmountGrageName" jdbcType="VARCHAR" property="amountGrageName" />
    <result column="Prem" jdbcType="DOUBLE" property="prem" />
    <result column="Amnt" jdbcType="DOUBLE" property="amnt" />
    <result column="DiscountRatio" jdbcType="DOUBLE" property="discountRatio" />
    <result column="AnnualTimeDeduction" jdbcType="VARCHAR" property="annualTimeDeduction" />
    <result column="WaitingPeriod" jdbcType="DOUBLE" property="waitingPeriod" />
    <result column="SpecialAgreement" jdbcType="VARCHAR" property="specialAgreement" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
    <result column="DutyName" jdbcType="VARCHAR" property="dutyName" />
    <result column="DutyRange" jdbcType="VARCHAR" property="dutyRange" />
    <result column="DutyType" jdbcType="VARCHAR" property="dutyType" />
    <result column="ExistAmountGrageCode" jdbcType="VARCHAR" property="existAmountGrageCode"/>
    <result column="DefaultDeductible" jdbcType="VARCHAR"  property="defaultDeductible" />
    <result column="DefaultCompensationRatio" jdbcType="VARCHAR" property="defaultCompensationRatio"/>
    <result column="IsDefaultFlag" jdbcType="VARCHAR" property="isDefaultFlag"/>
    <collection property="fcDutyGroupDeductibleList" ofType="java.lang.Integer">
      <constructor>
        <arg column="deductible"/>
      </constructor>
    </collection>
    <collection property="fcDutGradeCompensationRatioList" ofType="java.lang.Integer">
      <constructor>
        <arg column="compensationRatio"/>
      </constructor>
    </collection>
  </resultMap>
  <sql id="Base_Column_List">
    AmountGrageCode, EnsureCode, RiskCode, RiskType, DutyCode,DutyName,DutyRange,DutyType, AmountGrageName, Prem,
    Amnt, DiscountRatio, AnnualTimeDeduction, WaitingPeriod, SpecialAgreement, Operator, 
    OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcDutyAmountGrade">
    select 
    DutyCode,Amnt
    from fcdutyamountgrade
    where AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcdutyamountgrade
    where AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FcDutyAmountGrade">
    insert into fcdutyamountgrade (AmountGrageCode, EnsureCode, RiskCode, 
      RiskType, DutyCode, AmountGrageName, 
      Prem, Amnt, DiscountRatio, 
      AnnualTimeDeduction, WaitingPeriod, SpecialAgreement, 
      Operator, OperatorCom, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{amountGrageCode,jdbcType=VARCHAR}, #{ensureCode,jdbcType=VARCHAR}, #{riskCode,jdbcType=VARCHAR}, 
      #{riskType,jdbcType=VARCHAR}, #{dutyCode,jdbcType=VARCHAR}, #{amountGrageName,jdbcType=VARCHAR}, 
      #{prem,jdbcType=DOUBLE}, #{amnt,jdbcType=DOUBLE}, #{discountRatio,jdbcType=DOUBLE}, 
      #{annualTimeDeduction,jdbcType=VARCHAR}, #{waitingPeriod,jdbcType=DOUBLE}, #{specialAgreement,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcDutyAmountGrade">
    insert into fcdutyamountgrade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="amountGrageCode != null">
        AmountGrageCode,
      </if>
      <if test="ensureCode != null">
        EnsureCode,
      </if>
      <if test="riskCode != null">
        RiskCode,
      </if>
      <if test="riskType != null">
        RiskType,
      </if>
      <if test="dutyCode != null">
        DutyCode,
      </if>
      <if test="dutyName != null">
        DutyName,
      </if>
      <if test="dutyRange != null">
        DutyRange,
      </if>
      <if test="dutyType != null">
        DutyType,
      </if>
      <if test="amountGrageName != null">
        AmountGrageName,
      </if>
      <if test="prem != null">
        Prem,
      </if>
      <if test="amnt != null">
        Amnt,
      </if>
      <if test="discountRatio != null">
        DiscountRatio,
      </if>
      <if test="annualTimeDeduction != null">
        AnnualTimeDeduction,
      </if>
      <if test="waitingPeriod != null">
        WaitingPeriod,
      </if>
      <if test="maxGetDay != null">
        maxGetDay,
      </if>
      <if test="specialAgreement != null">
        SpecialAgreement,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="amountGrageCode != null">
        #{amountGrageCode,jdbcType=VARCHAR},
      </if>
      <if test="ensureCode != null">
        #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="riskType != null">
        #{riskType,jdbcType=VARCHAR},
      </if>
      <if test="dutyCode != null">
        #{dutyCode,jdbcType=VARCHAR},
      </if>
      <if test="dutyName != null">
        #{dutyName,jdbcType=VARCHAR},
      </if>
      <if test="dutyRange != null">
        #{dutyRange,jdbcType=VARCHAR},
      </if>
      <if test="dutyType != null">
        #{dutyType,jdbcType=VARCHAR},
      </if>
      <if test="amountGrageName != null">
        #{amountGrageName,jdbcType=VARCHAR},
      </if>
      <if test="prem != null">
        #{prem,jdbcType=DOUBLE},
      </if>
      <if test="amnt != null">
        #{amnt,jdbcType=DOUBLE},
      </if>
      <if test="discountRatio != null">
        #{discountRatio,jdbcType=DOUBLE},
      </if>
      <if test="annualTimeDeduction != null">
        #{annualTimeDeduction,jdbcType=VARCHAR},
      </if>
      <if test="waitingPeriod != null">
        #{waitingPeriod,jdbcType=DOUBLE},
      </if>
      <if test="maxGetDay != null">
        #{maxGetDay,jdbcType=DECIMAL},
      </if>
      <if test="specialAgreement != null">
        #{specialAgreement,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcDutyAmountGrade">
    update fcdutyamountgrade
    <set>
      <if test="ensureCode != null">
        EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        RiskCode = #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="riskType != null">
        RiskType = #{riskType,jdbcType=VARCHAR},
      </if>
      <if test="dutyCode != null">
        DutyCode = #{dutyCode,jdbcType=VARCHAR},
      </if>
      <if test="dutyName != null">
        DutyName = #{dutyName,jdbcType=VARCHAR},
      </if>
      <if test="dutyRange != null">
        DutyRange = #{dutyRange,jdbcType=VARCHAR},
      </if>
      <if test="dutyType != null">
        DutyType = #{dutyType,jdbcType=VARCHAR},
      </if>
      <if test="amountGrageName != null">
        AmountGrageName = #{amountGrageName,jdbcType=VARCHAR},
      </if>
      <if test="prem != null">
        Prem = #{prem,jdbcType=DOUBLE},
      </if>
      <if test="amnt != null">
        Amnt = #{amnt,jdbcType=DOUBLE},
      </if>
      <if test="discountRatio != null">
        DiscountRatio = #{discountRatio,jdbcType=DOUBLE},
      </if>
      <if test="annualTimeDeduction != null">
        AnnualTimeDeduction = #{annualTimeDeduction,jdbcType=VARCHAR},
      </if>
      <if test="waitingPeriod != null">
        WaitingPeriod = #{waitingPeriod,jdbcType=DOUBLE},
      </if>
      <if test="maxGetDay != null">
        maxGetDay = #{maxGetDay,jdbcType=DECIMAL},
      </if>
      <if test="specialAgreement != null">
        SpecialAgreement = #{specialAgreement,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcDutyAmountGrade">
    update fcdutyamountgrade
    set EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      RiskCode = #{riskCode,jdbcType=VARCHAR},
      RiskType = #{riskType,jdbcType=VARCHAR},
      DutyCode = #{dutyCode,jdbcType=VARCHAR},
      AmountGrageName = #{amountGrageName,jdbcType=VARCHAR},
      Prem = #{prem,jdbcType=DOUBLE},
      Amnt = #{amnt,jdbcType=DOUBLE},
      DiscountRatio = #{discountRatio,jdbcType=DOUBLE},
      AnnualTimeDeduction = #{annualTimeDeduction,jdbcType=VARCHAR},
      WaitingPeriod = #{waitingPeriod,jdbcType=DOUBLE},
      SpecialAgreement = #{specialAgreement,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
  </update>

  <sql id="getAmountGrageCodeByEnsureCode">
    ( select a.AmountGrageCode FROM (select AmountGrageCode FROM fcdutyamountgrade where ensureCode = #{ensureCode,jdbcType=VARCHAR}
    <if test=" riskCode != null and riskCode != ''">
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
    </if>
    <if test=" riskType != null and riskType != ''">
      and RiskType = #{riskType,jdbcType=VARCHAR}
    </if>
    <if test=" amountGrageCode != null and amountGrageCode != '' ">
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
    </if>) a )
  </sql>

  <delete id="deleteByEnsureCode" parameterType="java.util.Map">
    delete from fcdutyamountgrade
    where AmountGrageCode in (select AmountGrageCode FROM (select AmountGrageCode FROM fcdutyamountgrade where ensureCode = #{ensureCode,jdbcType=VARCHAR}
    <if test=" riskCode != null and riskCode != ''">
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
    </if>
    <if test=" riskType != null and riskType != ''">
      and RiskType = #{riskType,jdbcType=VARCHAR}
    </if>
    <if test=" amountGrageCode != null and amountGrageCode != '' ">
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
    </if>) a)
  </delete>

  <delete id="delByEnsureCode" parameterType="java.util.Map">
    delete from fcdutyamountgrade
    where ensureCode =#{ensureCode} and riskCode = #{riskCode}
    <if test=" dutyCode != null and dutyCode != '' ">
      AND DutyCode = #{dutyCode}
    </if>
  </delete>

  <select id="selectByEnsureCode" parameterType="java.lang.String" resultType="java.lang.Integer">
    select COUNT(*) from fcdutyamountgrade
    where ensureCode = #{ensureCode,jdbcType=VARCHAR}
    <if test="riskCode != null and riskCode != ''">
      AND RiskCode = #{riskCode,jdbcType=VARCHAR}
    </if>
    <if test="riskType != null and riskType != ''">
      and RiskType = #{riskType,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="getRiskInfoByEnsureCode" parameterType="java.lang.String" resultType="java.util.Map">
    SELECT a.riskCode,a.dutyCode,a.riskName,a.dutyRange,a.dutyType,a.prem,a.peoples,a.countNumber FROM (
      SELECT
      b.`RiskCode` AS riskCode,
      b.`DutyCode` AS dutyCode,
      j.`RiskName` AS riskName,
      CASE WHEN b.`RiskCode` = '17050' THEN k.`DutyRange` ELSE k.`DutyName` END AS dutyRange,
      k.`DutyType` as dutyType,
      0.00 as prem,
      0 as peoples,
      count(c.`OrderItemDetailNo`) as countNumber
      FROM FcBusinessProDutyGrpObject a
      LEFT JOIN FcDutyAmountGrade b ON b.`AmountGrageCode` = a.`AmountGrageCode`
      LEFT JOIN FcInsureEflexPlan c ON c.`AmountGrageCode` = a.`AmountGrageCode` 
      AND (SELECT Relation FROM fcstafffamilyrela WHERE PerNo = (SELECT PerNo FROM fcorder WHERE OrderNo = (SELECT OrderNo FROM fcorderitem WHERE OrderItemDetailNo = c.`OrderItemDetailNo`))
      AND PersonID = (SELECT PersonID FROM fcorderinsured WHERE OrderItemNo in (select OrderItemNo from fcorderitem where OrderItemDetailNo = c.`OrderItemDetailNo`))) IN (0)
      LEFT JOIN fdriskinfo j ON j.`RiskCode` = b.`RiskCode`
      LEFT JOIN fdriskdutyinfo k ON k.`DutyCode` = b.`DutyCode`
      WHERE a.`EnsureCode` =   #{ensureCode,jdbcType=VARCHAR} AND a.`InsuredType` = '0'
      GROUP BY b.`RiskCode`,b.`DutyCode`
      UNION
      SELECT
      b.`RiskCode` AS riskCode,
      a.`OptDutyCode` AS dutyCode,
      j.`RiskName` AS riskName,
      k.`DutyName` AS dutyRange,
      k.`DutyType` as dutyType,
      0.00 as prem,
      0 as peoples,
      count(c.`OrderItemDetailNo`) as countNumber
      FROM fcdutygradeoptionalamountinfo a
      LEFT JOIN FcDutyAmountGrade b ON b.`AmountGrageCode` = a.`AmountGrageCode`
      LEFT JOIN fcinsureeflexplanoptional c ON c.`AmountGrageCode` = a.`AmountGrageCode` AND c.`OptDutyCode` = a.`OptDutyCode` 
      AND (SELECT Relation FROM fcstafffamilyrela WHERE PerNo = (SELECT PerNo FROM fcorder WHERE OrderNo = (SELECT OrderNo FROM fcorderitem WHERE OrderItemDetailNo = c.`OrderItemDetailNo`)) 
      AND PersonID = (SELECT PersonID FROM fcorderinsured WHERE OrderItemNo in (select OrderItemNo from fcorderitem where OrderItemDetailNo = c.`OrderItemDetailNo`))) IN (0)
      LEFT JOIN fdriskinfo j ON j.`RiskCode` = b.`RiskCode`
      LEFT JOIN fdriskdutyinfo k ON k.`DutyCode` = a.`OptDutyCode`
      WHERE b.`EnsureCode` =  #{ensureCode,jdbcType=VARCHAR}
      GROUP BY a.`OptDutyCode`
    ) AS a
    ORDER BY a.riskCode,a.dutyCode
  </select>

  <select id="getRiskInfoByEnsureCodeFamily" parameterType="java.lang.String" resultType="java.util.Map">
    SELECT a.riskCode,a.dutyCode,a.riskName,a.dutyRange,a.dutyType,a.prem,a.peoples,a.countNumber FROM (
    SELECT
    b.`RiskCode` AS riskCode,
    b.`DutyCode` AS dutyCode,
    j.`RiskName` AS riskName,
    CASE WHEN b.`RiskCode` = '17050' THEN k.`DutyRange` ELSE k.`DutyName` END AS dutyRange,
    k.`DutyType` as dutyType,
    0.00 as prem,
    0 as peoples,
    count(c.`OrderItemDetailNo`) as countNumber
    FROM FcBusinessProDutyGrpObject a
    LEFT JOIN FcDutyAmountGrade b ON b.`AmountGrageCode` = a.`AmountGrageCode`
    LEFT JOIN FcInsureEflexPlan c ON c.`AmountGrageCode` = a.`AmountGrageCode` AND (SELECT Relation FROM fcstafffamilyrela WHERE PerNo = (SELECT PerNo FROM fcorder WHERE OrderNo = (SELECT OrderNo FROM fcorderitem WHERE OrderItemDetailNo = c.`OrderItemDetailNo`)) 
    AND PersonID = (SELECT PersonID FROM fcorderinsured WHERE OrderItemNo in (select OrderItemNo from fcorderitem where OrderItemDetailNo = c.`OrderItemDetailNo`))) IN (1,2,3)
    LEFT JOIN fdriskinfo j ON j.`RiskCode` = b.`RiskCode`
    LEFT JOIN fdriskdutyinfo k ON k.`DutyCode` = b.`DutyCode`
    WHERE a.`EnsureCode` = #{ensureCode,jdbcType=VARCHAR} AND a.`InsuredType` != '0'
    GROUP BY b.`RiskCode`,b.`DutyCode`
    UNION
    SELECT
    b.`RiskCode` AS riskCode,
    a.`OptDutyCode` AS dutyCode,
    j.`RiskName` AS riskName,
    k.`DutyName` AS dutyRange,
    k.`DutyType` as dutyType,
    0.00 as prem,
    0 as peoples,
    count(c.`OrderItemDetailNo`) as countNumber
    FROM fcdutygradeoptionalamountinfo a
    LEFT JOIN FcDutyAmountGrade b ON b.`AmountGrageCode` = a.`AmountGrageCode`
    LEFT JOIN fcinsureeflexplanoptional c ON c.`AmountGrageCode` = a.`AmountGrageCode` AND c.`OptDutyCode` = a.`OptDutyCode` AND (SELECT Relation FROM fcstafffamilyrela WHERE PerNo = (SELECT PerNo FROM fcorder WHERE OrderNo = (SELECT OrderNo FROM fcorderitem WHERE OrderItemDetailNo = c.`OrderItemDetailNo`)) 
    AND PersonID = (SELECT PersonID FROM fcorderinsured WHERE OrderItemNo in (select OrderItemNo from fcorderitem where OrderItemDetailNo = c.`OrderItemDetailNo`))) IN (1,2,3)
    LEFT JOIN fdriskinfo j ON j.`RiskCode` = b.`RiskCode`
    LEFT JOIN fdriskdutyinfo k ON k.`DutyCode` = a.`OptDutyCode`
    WHERE b.`EnsureCode` =  #{ensureCode,jdbcType=VARCHAR}
    GROUP BY a.`OptDutyCode`
    ) AS a
    ORDER BY a.riskCode,a.dutyCode
  </select>


  <select id="getWaitingPeriodListInfo" parameterType="java.util.List" resultType="java.util.Map">
    	select CASE WHEN b.RiskCode='15070' then 
								CASE WHEN a.RiskType='01' then CONCAT(b.RiskName,'(民航班机)')
									 WHEN a.RiskType='02' then CONCAT(b.RiskName,'(轨道交通工具)')
								     WHEN a.RiskType='03' then CONCAT(b.RiskName,'(水运公共交通工具)')
									 WHEN a.RiskType='04' then CONCAT(b.RiskName,'(公路公共交通工具)')
								else CONCAT(b.RiskName,'(私家车)') end
					 else b.RiskName end as RiskName,
       			CAST(FORMAT(IFNULL(a.WaitingPeriod,0),0) AS CHAR) WaitingPeriod 
		from FcDutyAmountGrade a,fdriskinfo b 
		where a.riskCode=b.RiskCode
		and a.AmountGrageCode in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">  
            #{item}  
        </foreach>
        order by b.RiskCode
    </select>

    <select id="getWaitingPeriodInfoList" parameterType="java.util.List"
            resultType="com.sinosoft.eflex.model.insureEflexPlanPage.WaitingPeriodInfo">
        select CASE WHEN b.RiskCode='15070' then
        CASE WHEN a.RiskType='01' then CONCAT(b.RiskName,'(民航班机)')
        WHEN a.RiskType='02' then CONCAT(b.RiskName,'(轨道交通工具)')
        WHEN a.RiskType='03' then CONCAT(b.RiskName,'(水运公共交通工具)')
        WHEN a.RiskType='04' then CONCAT(b.RiskName,'(公路公共交通工具)')
        else CONCAT(b.RiskName,'(私家车)') end
        else b.RiskName end as riskName,
        CAST(FORMAT(IFNULL(a.WaitingPeriod,0),0) AS CHAR) waitingPeriod
        from FcDutyAmountGrade a,fdriskinfo b
        where a.riskCode=b.RiskCode
        and a.AmountGrageCode in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by b.RiskCode
    </select>
</mapper>