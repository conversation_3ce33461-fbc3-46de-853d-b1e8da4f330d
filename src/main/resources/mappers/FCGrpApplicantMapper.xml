<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCGrpApplicantMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCGrpApplicant">
        <id column="GrpAppNo" jdbcType="VARCHAR" property="grpAppNo"/>
        <result column="GrpNo" jdbcType="VARCHAR" property="grpNo"/>
        <result column="GrpName" jdbcType="VARCHAR" property="grpName"/>
        <result column="GrpAddRess" jdbcType="VARCHAR" property="grpAddRess"/>
        <result column="ZipCode" jdbcType="VARCHAR" property="zipCode"/>
        <result column="UnifiedsociCode" jdbcType="VARCHAR" property="unifiedsociCode"/>
        <result column="GrpIdType" jdbcType="VARCHAR" property="grpIdType"/>
        <result column="GrpIdNo" jdbcType="VARCHAR" property="grpIdNo"/>
        <result column="GrpType" jdbcType="VARCHAR" property="grpType"/>
        <result column="AccName" jdbcType="VARCHAR" property="accName"/>
        <result column="grpBankCode" jdbcType="VARCHAR" property="grpBankCode"/>
        <result column="grpBankAccNo" jdbcType="VARCHAR" property="grpBankAccNo"/>
        <result column="Peoples" jdbcType="INTEGER" property="peoples"/>
        <result column="CorporationMan" jdbcType="VARCHAR" property="corporationMan"/>
        <result column="telphone" jdbcType="VARCHAR" property="telphone"/>
        <result column="regaddress" jdbcType="VARCHAR" property="regaddress"/>
        <result column="Email" jdbcType="VARCHAR" property="email"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="Clientno" jdbcType="VARCHAR" property="clientno"/>
        <result column="GrpTypeStartDate" jdbcType="DATE" property="grpTypeStartDate"/>
        <result column="GrpTypeEndDate" jdbcType="DATE" property="grpTypeEndDate"/>
        <result column="GrpEstablishDate" jdbcType="DATE" property="grpEstablishDate"/>
        <result column="GrpScaleType" jdbcType="VARCHAR" property="grpScaleType"/>
        <result column="SociologyPlanSign" jdbcType="VARCHAR" property="sociologyPlanSign"/>
        <result column="RegisteredCapital" jdbcType="VARCHAR" property="registeredCapital"/>
        <result column="GrpCategory" jdbcType="VARCHAR" property="grpCategory"/>
        <result column="Trade" jdbcType="VARCHAR" property="trade"/>
        <result column="BusinessTerm" jdbcType="DATE" property="businessTerm"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sinosoft.eflex.model.FCGrpApplicant">
        <result column="grpIDImage1" jdbcType="LONGVARCHAR" property="grpIDImage1"/>
        <result column="grpIDImage2" jdbcType="LONGVARCHAR" property="grpIDImage2"/>
        <result column="LegIDImage1" jdbcType="LONGVARCHAR" property="legIDImage1"/>
        <result column="LegIDImage2" jdbcType="LONGVARCHAR" property="legIDImage2"/>
    </resultMap>
    <sql id="Base_Column_List">
        GrpAppNo
        , GrpNo, GrpName, GrpAddRess, ZipCode, UnifiedsociCode, GrpIdType, GrpIdNo,
    GrpType, AccName, grpBankCode, grpBankAccNo, Peoples, CorporationMan, telphone, regaddress, 
    Email, Operator, Clientno, GrpTypeStartDate, GrpTypeEndDate, GrpEstablishDate, GrpScaleType, 
    SociologyPlanSign, RegisteredCapital, GrpCategory, Trade, BusinessTerm, OperatorCom, 
    MakeTime, MakeDate, ModifyDate, ModifyTime
    </sql>
    <sql id="Blob_Column_List">
        grpIDImage1
        , grpIDImage2, LegIDImage1, LegIDImage2
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from fcgrpapplicant
        where GrpAppNo = #{grpAppNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fcgrpapplicant
        where GrpAppNo = #{grpAppNo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCGrpApplicant">
        insert into fcgrpapplicant (GrpAppNo, GrpNo, GrpName,
                                    GrpAddRess, ZipCode, UnifiedsociCode,
                                    GrpIdType, GrpIdNo, GrpType,
                                    AccName, grpBankCode, grpBankAccNo,
                                    Peoples, CorporationMan, telphone,
                                    regaddress, Email, Operator,
                                    Clientno, GrpTypeStartDate, GrpTypeEndDate,
                                    GrpEstablishDate, GrpScaleType, SociologyPlanSign,
                                    RegisteredCapital, GrpCategory, Trade,
                                    BusinessTerm, OperatorCom, MakeTime,
                                    MakeDate, ModifyDate, ModifyTime,
                                    grpIDImage1, grpIDImage2, LegIDImage1,
                                    LegIDImage2)
        values (#{grpAppNo,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR}, #{grpName,jdbcType=VARCHAR},
                #{grpAddRess,jdbcType=VARCHAR}, #{zipCode,jdbcType=VARCHAR}, #{unifiedsociCode,jdbcType=VARCHAR},
                #{grpIdType,jdbcType=VARCHAR}, #{grpIdNo,jdbcType=VARCHAR}, #{grpType,jdbcType=VARCHAR},
                #{accName,jdbcType=VARCHAR}, #{grpBankCode,jdbcType=VARCHAR}, #{grpBankAccNo,jdbcType=VARCHAR},
                #{peoples,jdbcType=INTEGER}, #{corporationMan,jdbcType=VARCHAR}, #{telphone,jdbcType=VARCHAR},
                #{regaddress,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
                #{clientno,jdbcType=VARCHAR}, #{grpTypeStartDate,jdbcType=DATE}, #{grpTypeEndDate,jdbcType=DATE},
                #{grpEstablishDate,jdbcType=DATE}, #{grpScaleType,jdbcType=VARCHAR},
                #{sociologyPlanSign,jdbcType=VARCHAR},
                #{registeredCapital,jdbcType=VARCHAR}, #{grpCategory,jdbcType=VARCHAR}, #{trade,jdbcType=VARCHAR},
                #{businessTerm,jdbcType=DATE}, #{operatorCom,jdbcType=VARCHAR}, #{makeTime,jdbcType=VARCHAR},
                #{makeDate,jdbcType=DATE}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR},
                #{grpIDImage1,jdbcType=LONGVARCHAR}, #{grpIDImage2,jdbcType=LONGVARCHAR},
                #{legIDImage1,jdbcType=LONGVARCHAR},
                #{legIDImage2,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCGrpApplicant">
        insert into fcgrpapplicant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="grpAppNo != null">
                GrpAppNo,
            </if>
            <if test="grpNo != null">
                GrpNo,
            </if>
            <if test="grpName != null">
                GrpName,
            </if>
            <if test="grpAddRess != null">
                GrpAddRess,
            </if>
            <if test="zipCode != null">
                ZipCode,
            </if>
            <if test="unifiedsociCode != null">
                UnifiedsociCode,
            </if>
            <if test="grpIdType != null">
                GrpIdType,
            </if>
            <if test="grpIdNo != null">
                GrpIdNo,
            </if>
            <if test="grpType != null">
                GrpType,
            </if>
            <if test="accName != null">
                AccName,
            </if>
            <if test="grpBankCode != null">
                grpBankCode,
            </if>
            <if test="grpBankAccNo != null">
                grpBankAccNo,
            </if>
            <if test="peoples != null">
                Peoples,
            </if>
            <if test="corporationMan != null">
                CorporationMan,
            </if>
            <if test="telphone != null">
                telphone,
            </if>
            <if test="regaddress != null">
                regaddress,
            </if>
            <if test="email != null">
                Email,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="clientno != null">
                Clientno,
            </if>
            <if test="grpTypeStartDate != null">
                GrpTypeStartDate,
            </if>
            <if test="grpTypeEndDate != null">
                GrpTypeEndDate,
            </if>
            <if test="grpEstablishDate != null">
                GrpEstablishDate,
            </if>
            <if test="grpScaleType != null">
                GrpScaleType,
            </if>
            <if test="sociologyPlanSign != null">
                SociologyPlanSign,
            </if>
            <if test="registeredCapital != null">
                RegisteredCapital,
            </if>
            <if test="grpCategory != null">
                GrpCategory,
            </if>
            <if test="trade != null">
                Trade,
            </if>
            <if test="businessTerm != null">
                BusinessTerm,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
            <if test="grpIDImage1 != null">
                grpIDImage1,
            </if>
            <if test="grpIDImage2 != null">
                grpIDImage2,
            </if>
            <if test="legIDImage1 != null">
                LegIDImage1,
            </if>
            <if test="legIDImage2 != null">
                LegIDImage2,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="grpAppNo != null">
                #{grpAppNo,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="grpName != null">
                #{grpName,jdbcType=VARCHAR},
            </if>
            <if test="grpAddRess != null">
                #{grpAddRess,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="unifiedsociCode != null">
                #{unifiedsociCode,jdbcType=VARCHAR},
            </if>
            <if test="grpIdType != null">
                #{grpIdType,jdbcType=VARCHAR},
            </if>
            <if test="grpIdNo != null">
                #{grpIdNo,jdbcType=VARCHAR},
            </if>
            <if test="grpType != null">
                #{grpType,jdbcType=VARCHAR},
            </if>
            <if test="accName != null">
                #{accName,jdbcType=VARCHAR},
            </if>
            <if test="grpBankCode != null">
                #{grpBankCode,jdbcType=VARCHAR},
            </if>
            <if test="grpBankAccNo != null">
                #{grpBankAccNo,jdbcType=VARCHAR},
            </if>
            <if test="peoples != null">
                #{peoples,jdbcType=INTEGER},
            </if>
            <if test="corporationMan != null">
                #{corporationMan,jdbcType=VARCHAR},
            </if>
            <if test="telphone != null">
                #{telphone,jdbcType=VARCHAR},
            </if>
            <if test="regaddress != null">
                #{regaddress,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="clientno != null">
                #{clientno,jdbcType=VARCHAR},
            </if>
            <if test="grpTypeStartDate != null">
                #{grpTypeStartDate,jdbcType=DATE},
            </if>
            <if test="grpTypeEndDate != null">
                #{grpTypeEndDate,jdbcType=DATE},
            </if>
            <if test='grpEstablishDate != null'>
                <if test='grpEstablishDate == ""'>
                    null,
                </if>
                <if test='grpEstablishDate != ""'>
                    #{grpEstablishDate},
                </if>
            </if>
            <if test="grpScaleType != null">
                #{grpScaleType,jdbcType=VARCHAR},
            </if>
            <if test="sociologyPlanSign != null">
                #{sociologyPlanSign,jdbcType=VARCHAR},
            </if>
            <if test="registeredCapital != null">
                #{registeredCapital,jdbcType=VARCHAR},
            </if>
            <if test="grpCategory != null">
                #{grpCategory,jdbcType=VARCHAR},
            </if>
            <if test="trade != null">
                #{trade,jdbcType=VARCHAR},
            </if>
            <if test="businessTerm != null">
                #{businessTerm,jdbcType=DATE},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="grpIDImage1 != null">
                #{grpIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="grpIDImage2 != null">
                #{grpIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage1 != null">
                #{legIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage2 != null">
                #{legIDImage2,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCGrpApplicant">
        update fcgrpapplicant
        <set>
            <if test="grpNo != null">
                GrpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="grpName != null">
                GrpName = #{grpName,jdbcType=VARCHAR},
            </if>
            <if test="grpAddRess != null">
                GrpAddRess = #{grpAddRess,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="unifiedsociCode != null">
                UnifiedsociCode = #{unifiedsociCode,jdbcType=VARCHAR},
            </if>
            <if test="grpIdType != null">
                GrpIdType = #{grpIdType,jdbcType=VARCHAR},
            </if>
            <if test="grpIdNo != null">
                GrpIdNo = #{grpIdNo,jdbcType=VARCHAR},
            </if>
            <if test="grpType != null">
                GrpType = #{grpType,jdbcType=VARCHAR},
            </if>
            <if test="accName != null">
                AccName = #{accName,jdbcType=VARCHAR},
            </if>
            <if test="grpBankCode != null">
                grpBankCode = #{grpBankCode,jdbcType=VARCHAR},
            </if>
            <if test="grpBankAccNo != null">
                grpBankAccNo = #{grpBankAccNo,jdbcType=VARCHAR},
            </if>
            <if test="peoples != null">
                Peoples = #{peoples,jdbcType=INTEGER},
            </if>
            <if test="corporationMan != null">
                CorporationMan = #{corporationMan,jdbcType=VARCHAR},
            </if>
            <if test="telphone != null">
                telphone = #{telphone,jdbcType=VARCHAR},
            </if>
            <if test="regaddress != null">
                regaddress = #{regaddress,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                Email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="clientno != null">
                Clientno = #{clientno,jdbcType=VARCHAR},
            </if>
            <if test="grpTypeStartDate != null">
                GrpTypeStartDate = #{grpTypeStartDate,jdbcType=DATE},
            </if>
            <if test="grpTypeEndDate != null">
                GrpTypeEndDate = #{grpTypeEndDate,jdbcType=DATE},
            </if>
            <if test='grpEstablishDate != null'>
                <if test='grpEstablishDate == ""'>
                    grpEstablishDate = null,
                </if>
                <if test='grpEstablishDate != ""'>
                    grpEstablishDate = #{grpEstablishDate},
                </if>
            </if>
            <if test="grpScaleType != null">
                GrpScaleType = #{grpScaleType,jdbcType=VARCHAR},
            </if>
            <if test="sociologyPlanSign != null">
                SociologyPlanSign = #{sociologyPlanSign,jdbcType=VARCHAR},
            </if>
            <if test="registeredCapital != null">
                RegisteredCapital = #{registeredCapital,jdbcType=VARCHAR},
            </if>
            <if test="grpCategory != null">
                GrpCategory = #{grpCategory,jdbcType=VARCHAR},
            </if>
            <if test="trade != null">
                Trade = #{trade,jdbcType=VARCHAR},
            </if>
            <if test="businessTerm != null">
                BusinessTerm = #{businessTerm,jdbcType=DATE},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="grpIDImage1 != null">
                grpIDImage1 = #{grpIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="grpIDImage2 != null">
                grpIDImage2 = #{grpIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage1 != null">
                LegIDImage1 = #{legIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage2 != null">
                LegIDImage2 = #{legIDImage2,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where GrpAppNo = #{grpAppNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sinosoft.eflex.model.FCGrpApplicant">
        update fcgrpapplicant
        set GrpNo             = #{grpNo,jdbcType=VARCHAR},
            GrpName           = #{grpName,jdbcType=VARCHAR},
            GrpAddRess        = #{grpAddRess,jdbcType=VARCHAR},
            ZipCode           = #{zipCode,jdbcType=VARCHAR},
            UnifiedsociCode   = #{unifiedsociCode,jdbcType=VARCHAR},
            GrpIdType         = #{grpIdType,jdbcType=VARCHAR},
            GrpIdNo           = #{grpIdNo,jdbcType=VARCHAR},
            GrpType           = #{grpType,jdbcType=VARCHAR},
            AccName           = #{accName,jdbcType=VARCHAR},
            grpBankCode       = #{grpBankCode,jdbcType=VARCHAR},
            grpBankAccNo      = #{grpBankAccNo,jdbcType=VARCHAR},
            Peoples           = #{peoples,jdbcType=INTEGER},
            CorporationMan    = #{corporationMan,jdbcType=VARCHAR},
            telphone          = #{telphone,jdbcType=VARCHAR},
            regaddress        = #{regaddress,jdbcType=VARCHAR},
            Email             = #{email,jdbcType=VARCHAR},
            Operator          = #{operator,jdbcType=VARCHAR},
            Clientno          = #{clientno,jdbcType=VARCHAR},
            GrpTypeStartDate  = #{grpTypeStartDate,jdbcType=DATE},
            GrpTypeEndDate    = #{grpTypeEndDate,jdbcType=DATE},
            GrpEstablishDate  = #{grpEstablishDate,jdbcType=DATE},
            GrpScaleType      = #{grpScaleType,jdbcType=VARCHAR},
            SociologyPlanSign = #{sociologyPlanSign,jdbcType=VARCHAR},
            RegisteredCapital = #{registeredCapital,jdbcType=VARCHAR},
            GrpCategory       = #{grpCategory,jdbcType=VARCHAR},
            Trade             = #{trade,jdbcType=VARCHAR},
            BusinessTerm      = #{businessTerm,jdbcType=DATE},
            OperatorCom       = #{operatorCom,jdbcType=VARCHAR},
            MakeTime          = #{makeTime,jdbcType=VARCHAR},
            MakeDate          = #{makeDate,jdbcType=DATE},
            ModifyDate        = #{modifyDate,jdbcType=DATE},
            ModifyTime        = #{modifyTime,jdbcType=VARCHAR},
            grpIDImage1       = #{grpIDImage1,jdbcType=LONGVARCHAR},
            grpIDImage2       = #{grpIDImage2,jdbcType=LONGVARCHAR},
            LegIDImage1       = #{legIDImage1,jdbcType=LONGVARCHAR},
            LegIDImage2       = #{legIDImage2,jdbcType=LONGVARCHAR}
        where GrpAppNo = #{grpAppNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCGrpApplicant">
        update fcgrpapplicant
        set GrpNo             = #{grpNo,jdbcType=VARCHAR},
            GrpName           = #{grpName,jdbcType=VARCHAR},
            GrpAddRess        = #{grpAddRess,jdbcType=VARCHAR},
            ZipCode           = #{zipCode,jdbcType=VARCHAR},
            UnifiedsociCode   = #{unifiedsociCode,jdbcType=VARCHAR},
            GrpIdType         = #{grpIdType,jdbcType=VARCHAR},
            GrpIdNo           = #{grpIdNo,jdbcType=VARCHAR},
            GrpType           = #{grpType,jdbcType=VARCHAR},
            AccName           = #{accName,jdbcType=VARCHAR},
            grpBankCode       = #{grpBankCode,jdbcType=VARCHAR},
            grpBankAccNo      = #{grpBankAccNo,jdbcType=VARCHAR},
            Peoples           = #{peoples,jdbcType=INTEGER},
            CorporationMan    = #{corporationMan,jdbcType=VARCHAR},
            telphone          = #{telphone,jdbcType=VARCHAR},
            regaddress        = #{regaddress,jdbcType=VARCHAR},
            Email             = #{email,jdbcType=VARCHAR},
            Operator          = #{operator,jdbcType=VARCHAR},
            Clientno          = #{clientno,jdbcType=VARCHAR},
            GrpTypeStartDate  = #{grpTypeStartDate,jdbcType=DATE},
            GrpTypeEndDate    = #{grpTypeEndDate,jdbcType=DATE},
            GrpEstablishDate  = #{grpEstablishDate,jdbcType=DATE},
            GrpScaleType      = #{grpScaleType,jdbcType=VARCHAR},
            SociologyPlanSign = #{sociologyPlanSign,jdbcType=VARCHAR},
            RegisteredCapital = #{registeredCapital,jdbcType=VARCHAR},
            GrpCategory       = #{grpCategory,jdbcType=VARCHAR},
            Trade             = #{trade,jdbcType=VARCHAR},
            BusinessTerm      = #{businessTerm,jdbcType=DATE},
            OperatorCom       = #{operatorCom,jdbcType=VARCHAR},
            MakeTime          = #{makeTime,jdbcType=VARCHAR},
            MakeDate          = #{makeDate,jdbcType=DATE},
            ModifyDate        = #{modifyDate,jdbcType=DATE},
            ModifyTime        = #{modifyTime,jdbcType=VARCHAR}
        where GrpAppNo = #{grpAppNo,jdbcType=VARCHAR}
    </update>
    <!-- 导出计划清单excel -->
    <select id="selectInsuredDetail" parameterType="java.util.Map"
            resultMap="BaseResultMap">
        select GrpName
        from fcgrpapplicant
        where GrpAppNo = #{grpAppNo,jdbcType=VARCHAR}
    </select>
    <select id="selectByGrpNo" resultType="com.sinosoft.eflex.model.FCGrpApplicant">
        select * from fcgrpapplicant where GrpNo=#{grpNo}
    </select>
</mapper>