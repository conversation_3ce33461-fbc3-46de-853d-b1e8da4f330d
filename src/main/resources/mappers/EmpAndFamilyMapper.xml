<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.EmpAndFamilyMapper">
    <resultMap id="fcStaffFamilyRelaInfo" type="com.sinosoft.eflex.model.FCStaffFamilyRela">
        <id column="PerNo" jdbcType="VARCHAR" property="perNo"/>
        <result column="Relation" jdbcType="VARCHAR" property="relation"/>
        <result column="RelationProve" jdbcType="VARCHAR" property="relationProve"/>
        <collection property="fcPersonList" ofType="com.sinosoft.eflex.model.FCPerson" resultMap="fcPerson"/>
    </resultMap>
    <resultMap id="fcPerson" type="com.sinosoft.eflex.model.FCPerson">
        <id column="PersonID" jdbcType="VARCHAR" property="personID"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sex" jdbcType="VARCHAR" property="sex"/>
        <result column="BirthDate" jdbcType="DATE" property="birthDate"/>
        <result column="IDType" jdbcType="VARCHAR" property="IDType"/>
        <result column="IDTypeName" jdbcType="VARCHAR" property="IDTypeName"/>
        <result column="IDNo" jdbcType="VARCHAR" property="IDNo"/>
        <result column="MobilePhone" jdbcType="VARCHAR" property="mobilePhone"/>
        <result column="Phone" jdbcType="VARCHAR" property="phone"/>
        <result column="OccupationType" jdbcType="VARCHAR" property="occupationType"/>
        <result column="OccupationCode" jdbcType="VARCHAR" property="occupationCode"/>
        <result column="OccupationName" jdbcType="VARCHAR" property="occupationName"/>
        <result column="OpenBank" jdbcType="VARCHAR" property="openBank"/>
        <result column="OpenAccount" jdbcType="VARCHAR" property="openAccount"/>
        <result column="JoinMedProtect" jdbcType="VARCHAR" property="joinMedProtect"/>
        <result column="MedProtectType" jdbcType="VARCHAR" property="medProtectType"/>
        <result column="EMail" jdbcType="VARCHAR" property="EMail"/>
        <result column="ZipCode" jdbcType="VARCHAR" property="zipCode"/>
        <result column="Address" jdbcType="VARCHAR" property="address"/>
        <result column="Relation" jdbcType="VARCHAR" property="relation"/>
        <result column="RelationName" jdbcType="VARCHAR" property="relationName"/>
    </resultMap>
    <sql id="Base_Column_List">
        OrderItemNo, OrderNo, GrpOrderNo, PersonID, Name, Sex,
        Birthday, IDType, IDNo,
        MobilePhone,
        Phone, department, OccupationType,
        OccupationCode, JoinMedProtect,
        MedProtectType,
        EMail, ZipCode, Address,
        Operator, OperatorCom, MakeDate, MakeTime,
        ModifyDate, ModifyTime
    </sql>
    <select id="empAndFamilySelect" resultMap="fcStaffFamilyRelaInfo" parameterType="java.lang.String">
    select r.Relation,r.PersonID,p.Name ,p.Sex,p.BirthDate,p.IDType,p.IDNo,p.MobilePhone,p.Phone,p.OccupationType,
    p.OccupationCode,p.JoinMedProtect,p.MedProtectType,p.EMail,p.ZipCode,p.Address
    from  fcstafffamilyrela r inner join fcperson p on r.PersonID=p.PersonID where r.PerNo=#{perNo}
    </select>
    <select id="findFamilyName" resultMap="fcStaffFamilyRelaInfo">
        select r.Relation,r.RelationProve,p.Name,p.Sex,p.BirthDate,p.IDType,p.IDNo,p.MobilePhone,p.OccupationType,
        p.OccupationCode,p.JoinMedProtect,p.MedProtectType,p.EMail,p.ZipCode,p.Address,p.OpenBank,p.OpenAccount
        from fcstafffamilyrela r inner join fcperson p on r.PersonID=p.PersonID
        <where>
            <if test="personId !=null and personId !=''">
                r.PersonID=#{personId}
            </if>
        </where>
    </select>
    <select id="selectFamilyByIsManual" parameterType="java.util.HashMap"
            resultType="com.sinosoft.eflex.model.FCPerson">
        select
        d.idno as perIDNo,a.Relation,a.RelationProve,b.email,
        (select codename from fdcode where CodeType = 'relation' and CodeKey = a.Relation) as RelationName ,
        b.nativeplace,
        (select codename from fdcode where CodeType = 'nativeplace' and CodeKey = b.nativeplace) as nativeplaceName ,
        b.idTypeEndDate,
        b.PersonID, b.Name, b.Sex, b.BirthDate, b.IDType, b.IDNo,b.OpenBank,b.OpenAccount, b.mobilePhone,
        b.occupationCode,b.occupationType,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = b.occupationCode) as
        occupationName ,
        (select codename from fdcode where CodeType = 'IDType' and CodeKey = b.idtype) as IDTypeName
        from fcstafffamilyrela a
        LEFT JOIN fcperson b on a.PersonID = b.PersonID
        left join fcperinfo d on d.perno = a.perno
        INNER JOIN fcperinfofamilytemp c on b.idno=c.idno and c.SubStaus='02' and c.ensurecode=#{ensureCode}
        where a.PerNo=#{perNo}
    </select>
    <select id="selectFamilyByIsManual1" parameterType="java.util.HashMap"
            resultType="com.sinosoft.eflex.model.FCPerson">
        select
        d.idno as perIDNo,a.Relation,a.RelationProve,b.email,
        (select codename from fdcode where CodeType = 'relation' and CodeKey = a.Relation) as RelationName ,
        b.nativeplace,
        (select codename from fdcode where CodeType = 'nativeplace' and CodeKey = b.nativeplace) as nativeplaceName ,
        b.idTypeEndDate,
        b.PersonID, b.Name, b.Sex, b.BirthDate, b.IDType, b.IDNo,b.OpenBank,b.OpenAccount, b.mobilePhone, b.occupationCode,b.occupationType,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = b.occupationCode) as occupationName ,
        (select codename from fdcode where CodeType = 'IDType' and CodeKey = b.idtype) as IDTypeName
        from fcstafffamilyrela a
        LEFT JOIN fcperson b on a.PersonID = b.PersonID
        left join fcperinfo d on d.perno = a.perno
        INNER JOIN fcperinfofamilytemp c on b.idno=c.idno and c.SubStaus='02' and c.ensurecode=#{ensureCode}
        where a.PerNo=#{perNo}
        and b.personid in
        <foreach collection="perosnidlist" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectFamilyIDNO" parameterType="java.lang.String" resultType="java.lang.String">
    	select DISTINCT c.idno  from  fcstafffamilyrela a 
			LEFT JOIN fcperinfo b on a.perno=b.perno
			LEFT JOIN fcperson  c on a.personid=c.personid
		where b.idno in (select idno from fcperinfo where perno=#{perNo})
    </select>
    <select id="selectNewFamilyPersonid" parameterType="java.util.Map" resultType="java.lang.String">
		SELECT a.personid FROM  fcperson  a
    		LEFT JOIN fcstafffamilyrela b ON a.personid=b.personid
			LEFT JOIN fcperinfo c ON b.perNo=c.perNo
    	WHERE a.idno=#{IdNo} AND c.perno=#{perNo}
    		ORDER BY a.ModifyDate DESC,a.ModifyTime DESC LIMIT 1
    </select>
    <select id="selectNewFamilyPersonidEflex" parameterType="java.util.Map" resultType="java.lang.String">
		SELECT a.personid FROM  fcperson  a,fcstafffamilyrela b 
    	WHERE  a.idno=#{IdNo}
      		AND b.perno=#{perNo}
      		AND a.personid=b.personid
    </select>
    <select id="selectAllFamilyInfo" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCPerson">
        select
        p.IDNo as perIDNo,a.Relation,a.RelationProve,b.email,
        (select codename from fdcode where CodeType = 'relation' and CodeKey = a.Relation) as RelationName ,
        b.nativeplace,
        (select codename from fdcode where CodeType = 'nativeplace' and CodeKey = b.nativeplace) as nativeplaceName ,
        b.idTypeEndDate,
        b.PersonID, b.Name, b.Sex, b.BirthDate, b.IDType, b.IDNo,b.OpenBank,b.OpenAccount, b.mobilePhone, b.occupationCode,b.occupationType,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = b.occupationCode) as occupationName ,
        (select codename from fdcode where CodeType = 'IDType' and CodeKey = b.idtype) as IDTypeName,
        b.joinMedProtect
        from  fcperson b
        LEFT JOIN fcstafffamilyrela a on b.personid=a.personid
        LEFT JOIN fcperinfo p ON p.PerNo = a.PerNo
        and a.perno in (select perno from fcperinfo where idno=#{idNo})
        where 1=1
        <if test="perosnidlist != null and perosnidlist.size > 0">
            and b.personid in
            <foreach collection="perosnidlist" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by  a.Relation
    </select>
    <select id="selectSingleFamilyInfo" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCPerson">
        select
        p.IDNo as perIDNo,a.Relation,a.RelationProve,b.email,
        (select codename from fdcode where CodeType = 'relation' and CodeKey = a.Relation) as RelationName ,
        b.nativeplace,
        (select codename from fdcode where CodeType = 'nativeplace' and CodeKey = b.nativeplace) as nativeplaceName ,
        b.idTypeEndDate,
        b.PersonID, b.Name, b.Sex, b.BirthDate,
        b.IDType,
        (select codename from fdcode where CodeType = 'IDType' and CodeKey = b.idtype) as IDTypeName,
        b.IDNo,b.OpenBank,b.OpenAccount, b.mobilePhone,
        b.occupationCode,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = b.occupationCode) as occupationName,
        b.occupationType,
        (select codename from fdcode where CodeType = 'OccupationType' and CodeKey = b.occupationType) as occupationTypeName,
        b.joinMedProtect,
        b.phone,
        b.medProtectType,
        b.ZipCode,
        b.Address,
        b.OpenBank,
        b.OpenAccount,
        b.Operator,
        b.OperatorCom,
        b.MakeDate,
        b.MakeTime,
        b.ModifyDate,
        b.ModifyTime,
        b.Province,
        b.City,
        b.County,
        b.DetaileAddress,
        b.LevelCode,
        b.Retirement,
        b.EMail
        from  fcperson b
        LEFT JOIN fcstafffamilyrela a on b.personid=a.personid
        LEFT JOIN fcperinfo p ON p.PerNo = a.PerNo
        and a.perno in (select perno from fcperinfo where idno= #{idNo})
        where 1=1
        and b.personid = #{personid}
        order by  a.Relation
    </select>
    <select id="selectOrderInsuredInfo" parameterType="java.lang.String" resultType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from fcorderinsured
        where PersonID=#{personId,jdbcType=VARCHAR}
    </select>

    <select id="getFamilyEnsureInfo" resultType="java.util.Map" parameterType="java.lang.String">
        select r.Relation,p.Name,r.PersonID,e.PlanName,i.OrderNo
        from
        fcstafffamilyrela r left join fcperson p on r.PersonID=p.PersonID
        right join fcorderinsured i on r.PersonID=i.PersonID
        right join (fcorderitem item) on i.OrderItemNo=item.OrderItemNo
        right join (fcorderitemdetail tail) on item.OrderItemDetailNo=tail.OrderItemDetailNo
        right join (fcensureplan e) on tail.ProductCode=e.PlanCode
        <where>
            <if test="perNo !=null and perNo !=''">
                r.PerNo=#{perNo}
            </if>
        </where>
    </select>
    <select id="selectIsEflexPlan" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT fp.personid,fp.ensurecode,fp.AmountGrageCode FROM FPInsureEflexPlan fp 
        WHERE fp.personid = #{personId,jdbcType=VARCHAR}
        	 AND fp.ensurecode = #{ensureCode,jdbcType=VARCHAR}
        	 AND fp.perNo = #{perNo,jdbcType=VARCHAR}
    </select>
    <select id="selectIsPlan" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT fp.personid,fp.ensurecode,fp.plancode FROM fpinsureplan fp 
        WHERE fp.personid = #{personId,jdbcType=VARCHAR}
        	 AND fp.ensurecode = #{ensureCode,jdbcType=VARCHAR}
    </select>

    <select id="selectSameStaffPersonid" resultType="java.lang.String" parameterType="java.lang.String">
        <!-- 证件号相同的Personid -->
        select personid from fcperson where idno=(select idno from fcperson where personid=#{personid})
        and
        <!-- 同一员工下所有的Personid -->
        personid in (select personid from fcstafffamilyrela where perno in (select perno from fcperinfo where idno in
        (select idno from fcperinfo where perno=#{perno})))
    </select>

    <select id="selectSameStaffPersonidPerNo" resultType="java.util.Map" parameterType="java.util.Map">
        SELECT a.PersonID as personID,c.`PerNo` as perNo  FROM fcperson a
        INNER JOIN fcstafffamilyrela b ON a.`PersonID` = b.`PersonID` AND b.`Relation` != ''
        INNER JOIN fcperinfo c ON b.`PerNo` = c.`PerNo` AND c.`PerNo` IN (SELECT perno FROM fcperinfo WHERE idno IN (SELECT idno FROM fcperinfo WHERE perno= #{perno}))
        WHERE a.`IDNo` = (SELECT idno FROM fcperson WHERE personid= #{personid})
    </select>

    <select id="selectSingleSameStaffPersonid" resultType="java.lang.String" parameterType="java.lang.String">
        <!-- 证件号相同的Personid -->
        select personid from fcperson where idno=(select idno from fcperson where personid=#{personId})
        and
        <!-- 同一员工下所有的Personid -->
        personid in (select personid from fcstafffamilyrela where perno=#{perNo})
    </select>

    <!-- 查询是否为监护人 -->
    <select id="checkPerType" parameterType="java.lang.String" resultType="java.lang.Integer">
	     select  count(*) from  fcperson a
  			left join fcstafffamilyrela b on a.personid=b.personid
				LEFT JOIN  fcperinfo c on b.perno=c.perno 
			  where a.idno=(select idno from fcperinfo where perno=#{perNo})
				and c.perno in (select perno from fcperinfo where  idno in (select idno from fcperinfo where perno=#{perNo}))
     </select>
    <select id="checkPerTypeByIDNO" parameterType="java.lang.String" resultType="java.lang.Integer">
	    select  count(*) from  fcperson a
  			left join fcstafffamilyrela b on a.personid=b.personid
				LEFT JOIN  fcperinfo c on b.perno=c.perno 
			  where a.idno=#{IdNo}
				and c.perno in (select perno from fcperinfo where  idno=#{IdNo})
     </select>

    <!-- 查询监护人的信息-->
    <select id="selectGuardianInfo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPerInfo">
     	select
            b.email,
	        b.nativeplace,
	        (select codename from fdcode where CodeType = 'nativeplace' and CodeKey = b.nativeplace) as nativeplaceName ,
	        b.idTypeEndDate,
	        b.Name, b.Sex, b.Birthday, b.IDType, b.IDNo,b.OpenBank,b.OpenAccount, b.mobilePhone, b.occupationCode,b.occupationType,
	        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = b.occupationCode) as occupationName ,
	        (select codename from fdcode where CodeType = 'IDType' and CodeKey = b.idtype) as IDTypeName 
     	from  fcperinfo b where idno=(select idno from fcperinfo where perno=#{perNo}) order by MakeDate desc,MakeTime desc  limit 1 
     </select>

    <select id='selectStaffPersonId' parameterType="java.lang.String" resultType="java.lang.String">
    	select  personid from  fcstafffamilyrela a
		LEFT JOIN fcperinfo b on a.perno=b.perno
		LEFT JOIN fcperinfo c on b.idno=c.idno
		where  c.perno=#{perNo}
		and a.relation='0'
    </select>
    <select id="selectFamilyIdNo" resultType="java.lang.String">
        select DISTINCT c.idno  from  fcstafffamilyrela a
			LEFT JOIN fcperinfo b on a.perno=b.perno
			LEFT JOIN fcperson  c on a.personid=c.personid
		where b.idno in (select idno from fcperinfo where perno=#{perNo}) and a.Relation &lt;&gt; '0'
    </select>
    <select id="selectFamily" resultType="com.sinosoft.eflex.model.FCPerson">
        select
        a.Relation,a.RelationProve,b.email,
        (select codename from fdcode where CodeType = 'relation' and CodeKey = a.Relation) as RelationName ,
        b.nativeplace,
        (select codename from fdcode where CodeType = 'nativeplace' and CodeKey = b.nativeplace) as nativeplaceName ,
        b.idTypeEndDate,
        b.PersonID, b.Name, b.Sex, b.BirthDate, b.IDType, b.IDNo,b.OpenBank,b.OpenAccount, b.mobilePhone,
        b.occupationCode,b.occupationType,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = b.occupationCode) as
        occupationName ,
        (select codename from fdcode where CodeType = 'IDType' and CodeKey = b.idtype) as IDTypeName
        from fcstafffamilyrela a
        LEFT JOIN fcperson b on a.PersonID = b.PersonID
        where a.PerNo=#{perNo}
        <if test="personId !=null and personId !=''">
            and a.PersonID=#{personId}
        </if>
    </select>
    <select id="selectPersonInfo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.insurePlanPage.PersonInfo">
        SELECT
            a.NAME,
            a.sex,
            a.birthDate,
            a.mobilePhone,
            a.nativeplace,
            a.iDType,
            a.iDNo,
            a.idTypeEndDate,
            a.occupationCode,
            a.JoinMedProtect,
            a.openbank,
            a.openaccount,
            a.email,
            c.codename AS occupationName,
            b.relation
        FROM
            fcperson a,
            fcstafffamilyrela b,
            fdcode c
        WHERE a.personid = #{personId}
          AND a.personid = b.personid
          AND a.occupationCode = c.codekey
          AND c.codetype = 'OccupationDetail'
    </select>


</mapper>