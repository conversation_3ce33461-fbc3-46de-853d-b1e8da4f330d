<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCEdorAddPlanInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCEdorAddPlanInfo">
    <id column="EdorAddPlanSN" jdbcType="VARCHAR" property="edorAddPlanSN" />
    <result column="GrpContNo" jdbcType="VARCHAR" property="grpContNo" />
    <result column="Batch" jdbcType="VARCHAR" property="batch" />
    <result column="PlanCode" jdbcType="VARCHAR" property="planCode" />
    <result column="PlanName" jdbcType="VARCHAR" property="planName" />
    <result column="PlanObject" jdbcType="VARCHAR" property="planObject" />
    <result column="TotalPrem" jdbcType="DECIMAL" property="totalPrem" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    EdorAddPlanSN, GrpContNo, Batch, PlanCode, PlanName, PlanObject, TotalPrem, Operator, 
    OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcedoraddplaninfo
    where EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcedoraddplaninfo
    where EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCEdorAddPlanInfo">
    insert into fcedoraddplaninfo (EdorAddPlanSN, GrpContNo, Batch, 
      PlanCode, PlanName, PlanObject, 
      TotalPrem, Operator, OperatorCom, 
      MakeDate, MakeTime, ModifyDate, 
      ModifyTime)
    values (#{edorAddPlanSN,jdbcType=VARCHAR}, #{grpContNo,jdbcType=VARCHAR}, #{batch,jdbcType=VARCHAR}, 
      #{planCode,jdbcType=VARCHAR}, #{planName,jdbcType=VARCHAR}, #{planObject,jdbcType=VARCHAR}, 
      #{totalPrem,jdbcType=DECIMAL}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, 
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, 
      #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCEdorAddPlanInfo">
    insert into fcedoraddplaninfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="edorAddPlanSN != null">
        EdorAddPlanSN,
      </if>
      <if test="grpContNo != null">
        GrpContNo,
      </if>
      <if test="batch != null">
        Batch,
      </if>
      <if test="planCode != null">
        PlanCode,
      </if>
      <if test="planName != null">
        PlanName,
      </if>
      <if test="planObject != null">
        PlanObject,
      </if>
      <if test="totalPrem != null">
        TotalPrem,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="edorAddPlanSN != null">
        #{edorAddPlanSN,jdbcType=VARCHAR},
      </if>
      <if test="grpContNo != null">
        #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=VARCHAR},
      </if>
      <if test="planCode != null">
        #{planCode,jdbcType=VARCHAR},
      </if>
      <if test="planName != null">
        #{planName,jdbcType=VARCHAR},
      </if>
      <if test="planObject != null">
        #{planObject,jdbcType=VARCHAR},
      </if>
      <if test="totalPrem != null">
        #{totalPrem,jdbcType=DECIMAL},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCEdorAddPlanInfo">
    update fcedoraddplaninfo
    <set>
      <if test="grpContNo != null">
        GrpContNo = #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        Batch = #{batch,jdbcType=VARCHAR},
      </if>
      <if test="planCode != null">
        PlanCode = #{planCode,jdbcType=VARCHAR},
      </if>
      <if test="planName != null">
        PlanName = #{planName,jdbcType=VARCHAR},
      </if>
      <if test="planObject != null">
        PlanObject = #{planObject,jdbcType=VARCHAR},
      </if>
      <if test="totalPrem != null">
        TotalPrem = #{totalPrem,jdbcType=DECIMAL},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCEdorAddPlanInfo">
    update fcedoraddplaninfo
    set GrpContNo = #{grpContNo,jdbcType=VARCHAR},
      Batch = #{batch,jdbcType=VARCHAR},
      PlanCode = #{planCode,jdbcType=VARCHAR},
      PlanName = #{planName,jdbcType=VARCHAR},
      PlanObject = #{planObject,jdbcType=VARCHAR},
      TotalPrem = #{totalPrem,jdbcType=DECIMAL},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
  </update>

  <insert id="insertEdorAddPlanList" parameterType="java.util.List">
    insert into fcedoraddplaninfo
    (EdorAddPlanSN,GrpContNo,Batch,PlanCode,PlanName,PlanObject,TotalPrem,Operator,OperatorCom,MakeDate,MakeTime,ModifyDate,ModifyTime)
    VALUES <foreach collection="list"  item="item" index="index" open="" close="" separator=",">
    (#{list[${index}].edorAddPlanSN,jdbcType=VARCHAR},
    #{list[${index}].grpContNo,jdbcType=VARCHAR},
    #{list[${index}].batch,jdbcType=VARCHAR},
    #{list[${index}].planCode,jdbcType=VARCHAR},
    #{list[${index}].planName,jdbcType=VARCHAR},
    #{list[${index}].planObject,jdbcType=VARCHAR},
    #{list[${index}].totalPrem,jdbcType=DECIMAL},
    #{list[${index}].operator,jdbcType=VARCHAR},
    #{list[${index}].operatorCom,jdbcType=VARCHAR},
    #{list[${index}].makeDate,jdbcType=DATE},
    #{list[${index}].makeTime,jdbcType=VARCHAR},
    #{list[${index}].modifyDate,jdbcType=DATE},
    #{list[${index}].modifyTime,jdbcType=VARCHAR})
  </foreach>
  </insert>

  <select id="getPlanInfoByPlanCode" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCEdorAddPlanInfo">
    select
    <include refid="Base_Column_List" />
    from fcedoraddplaninfo WHERE Batch = #{batch,jdbcType=VARCHAR} AND PlanCode = #{planCode,jdbcType=VARCHAR}
  </select>
    <select id="getEdorPlanListByGrpContNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcedoraddplaninfo
        where grpContNo = #{grpContNo}
    </select>
    <delete id="deleteByGrpContNo" parameterType="java.lang.String">
        delete from fcedoraddplaninfo
        where grpContNo = #{grpContNo}
    </delete>
    <select id="selectEdorPlanInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcedoraddplaninfo
        where grpContNo = #{grpContNo}
        and PlanCode = #{planCode}
    </select>
</mapper>