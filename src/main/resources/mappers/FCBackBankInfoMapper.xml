<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCBackBankInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCBackBankInfo">
    <id column="OrderNo" jdbcType="VARCHAR" property="orderNo" />
    <id column="PayFlowNo" jdbcType="VARCHAR" property="payFlowNo" />
    <result column="NAME" jdbcType="VARCHAR" property="NAME" />
    <result column="BankAccount" jdbcType="VARCHAR" property="bankAccount" />
    <result column="PayBankCode" jdbcType="VARCHAR" property="payBankCode" />
    <result column="ReservePhone" jdbcType="VARCHAR" property="reservePhone" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    OrderNo, PayFlowNo, NAME, BankAccount, PayBankCode, ReservePhone, Operator, OperatorCom, 
    MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcbackbankinfo
    where OrderNo = #{orderNo,jdbcType=VARCHAR}
      and PayFlowNo = #{payFlowNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcbackbankinfo
    where OrderNo = #{orderNo,jdbcType=VARCHAR}
      and PayFlowNo = #{payFlowNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCBackBankInfo">
    insert into fcbackbankinfo (OrderNo, PayFlowNo, NAME, 
      BankAccount, PayBankCode, ReservePhone, 
      Operator, OperatorCom, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{payFlowNo,jdbcType=VARCHAR}, #{NAME,jdbcType=VARCHAR}, 
      #{bankAccount,jdbcType=VARCHAR}, #{payBankCode,jdbcType=VARCHAR}, #{reservePhone,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCBackBankInfo">
    insert into fcbackbankinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        OrderNo,
      </if>
      <if test="payFlowNo != null">
        PayFlowNo,
      </if>
      <if test="NAME != null">
        NAME,
      </if>
      <if test="bankAccount != null">
        BankAccount,
      </if>
      <if test="payBankCode != null">
        PayBankCode,
      </if>
      <if test="reservePhone != null">
        ReservePhone,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payFlowNo != null">
        #{payFlowNo,jdbcType=VARCHAR},
      </if>
      <if test="NAME != null">
        #{NAME,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="payBankCode != null">
        #{payBankCode,jdbcType=VARCHAR},
      </if>
      <if test="reservePhone != null">
        #{reservePhone,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCBackBankInfo">
    update fcbackbankinfo
    <set>
      <if test="NAME != null">
        NAME = #{NAME,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        BankAccount = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="payBankCode != null">
        PayBankCode = #{payBankCode,jdbcType=VARCHAR},
      </if>
      <if test="reservePhone != null">
        ReservePhone = #{reservePhone,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where OrderNo = #{orderNo,jdbcType=VARCHAR}
      and PayFlowNo = #{payFlowNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCBackBankInfo">
    update fcbackbankinfo
    set NAME = #{NAME,jdbcType=VARCHAR},
      BankAccount = #{bankAccount,jdbcType=VARCHAR},
      PayBankCode = #{payBankCode,jdbcType=VARCHAR},
      ReservePhone = #{reservePhone,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where OrderNo = #{orderNo,jdbcType=VARCHAR}
      and PayFlowNo = #{payFlowNo,jdbcType=VARCHAR}
  </update>
</mapper>