<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcHrRegistTempMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcHrRegistTemp">
        <id column="RegistSN" jdbcType="VARCHAR" property="registSN"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="mobilePhone" jdbcType="VARCHAR" property="mobilePhone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="nativeplace" jdbcType="VARCHAR" property="nativeplace"/>
        <result column="idType" jdbcType="VARCHAR" property="idType"/>
        <result column="idNo" jdbcType="VARCHAR" property="idNo"/>
        <result column="idTypeStartDate" jdbcType="DATE" property="idTypeStartDate"/>
        <result column="idTypeEndDate" jdbcType="DATE" property="idTypeEndDate"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="birthday" jdbcType="DATE" property="birthday"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="grpName" jdbcType="VARCHAR" property="grpName"/>
        <result column="grpIdType" jdbcType="VARCHAR" property="grpIdType"/>
        <result column="grpIdNo" jdbcType="VARCHAR" property="grpIdNo"/>
        <result column="zipCode" jdbcType="VARCHAR" property="zipCode"/>
        <result column="grpAddress" jdbcType="VARCHAR" property="grpAddress"/>
        <result column="unifiedsociCode" jdbcType="VARCHAR" property="unifiedsociCode"/>
        <result column="grpType" jdbcType="VARCHAR" property="grpType"/>
        <result column="GrpNatureType" jdbcType="VARCHAR" property="grpNatureType"/>
        <result column="accName" jdbcType="VARCHAR" property="accName"/>
        <result column="grpBankcode" jdbcType="VARCHAR" property="grpBankcode"/>
        <result column="grpBankaccno" jdbcType="VARCHAR" property="grpBankaccno"/>
        <result column="telphone" jdbcType="VARCHAR" property="telphone"/>
        <result column="regAddress" jdbcType="VARCHAR" property="regAddress"/>
        <result column="clientNo" jdbcType="VARCHAR" property="clientNo"/>
        <result column="corporationMan" jdbcType="VARCHAR" property="corporationMan"/>
        <result column="checkStatus" jdbcType="VARCHAR" property="checkStatus"/>
        <result column="GrpTypeStartDate" jdbcType="DATE" property="grpTypeStartDate"/>
        <result column="GrpTypeEndDate" jdbcType="DATE" property="grpTypeEndDate"/>
        <result column="GrpEstablishDate" jdbcType="DATE" property="grpEstablishDate"/>
        <result column="GrpScaleType" jdbcType="VARCHAR" property="grpScaleType"/>
        <result column="SociologyPlanSign" jdbcType="VARCHAR" property="sociologyPlanSign"/>
        <result column="RegisteredCapital" jdbcType="VARCHAR" property="registeredCapital"/>
        <result column="GrpCategory" jdbcType="VARCHAR" property="grpCategory"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="auditOpinion" jdbcType="VARCHAR" property="auditOpinion"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
        <result column="grpNo" jdbcType="VARCHAR" property="grpNo"/>
        <result column="grpContactNo" jdbcType="VARCHAR" property="grpContactNo"/>
        <result column="BusinessTerm" jdbcType="DATE" property="businessTerm"/>
        <result column="Trade" jdbcType="VARCHAR" property="trade"/>
        <result column="Peoples" jdbcType="BIGINT" property="peoples"/>
        <result column="shareBusiness" jdbcType="VARCHAR" property="shareBusiness"/>
        <result column="shareholdersName" jdbcType="VARCHAR" property="shareholdersName"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sinosoft.eflex.model.FcHrRegistTemp">
        <result column="idImage1" jdbcType="LONGVARCHAR" property="idImage1"/>
        <result column="idImage2" jdbcType="LONGVARCHAR" property="idImage2"/>
        <result column="grpIDImage1" jdbcType="LONGVARCHAR" property="grpIDImage1"/>
        <result column="legIDImage1" jdbcType="LONGVARCHAR" property="legIDImage1"/>
        <result column="grpIDImage2" jdbcType="LONGVARCHAR" property="grpIDImage2"/>
        <result column="legIDImage2" jdbcType="LONGVARCHAR" property="legIDImage2"/>
        <result column="grp_image_front" jdbcType="LONGVARCHAR" property="grpImageFront"/>
        <result column="grp_image_back" jdbcType="LONGVARCHAR" property="grpImageBack"/>
        <result column="legal_img_front" jdbcType="LONGVARCHAR" property="legalImgFront"/>
        <result column="legal_img_back" jdbcType="LONGVARCHAR" property="legalImgBack"/>
        <result column="id_card_front" jdbcType="LONGVARCHAR" property="idCardBack"/>
        <result column="id_card_back" jdbcType="LONGVARCHAR" property="idCardFront"/>
    </resultMap>
    <sql id="Base_Column_List">
        RegistSN
        , name, mobilePhone, email, nativeplace, idType, idNo, idTypeStartDate, idTypeEndDate,
    sex, birthday, department, grpName, grpIdType, grpIdNo, zipCode, grpAddress, unifiedsociCode, 
    grpType, accName, grpBankcode, grpBankaccno, telphone, regAddress, clientNo, corporationMan, 
    checkStatus, GrpTypeStartDate, GrpTypeEndDate, GrpEstablishDate, GrpScaleType, SociologyPlanSign, 
    RegisteredCapital, GrpCategory, OperatorCom, Operator, MakeDate, MakeTime, ModifyDate, 
    auditOpinion, ModifyTime, grpNo, grpContactNo, BusinessTerm, Trade, Peoples, shareBusiness, 
    shareholdersName
    </sql>
    <sql id="Blob_Column_List">
        idImage1
        , idImage2, grpIDImage1, legIDImage1, grpIDImage2, legIDImage2
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from fchrregisttemp
        where RegistSN = #{registSN,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fchrregisttemp
        where RegistSN = #{registSN,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FcHrRegistTemp">
        insert into fchrregisttemp (RegistSN, name, mobilePhone,
                                    email, nativeplace, idType,
                                    idNo, idTypeStartDate, idTypeEndDate,
                                    sex, birthday, department,
                                    grpName, grpIdType, grpIdNo,
                                    zipCode, grpAddress, unifiedsociCode,
                                    grpType, accName, grpBankcode,
                                    grpBankaccno, telphone, regAddress,
                                    clientNo, corporationMan, checkStatus,
                                    GrpTypeStartDate, GrpTypeEndDate, GrpEstablishDate,
                                    GrpScaleType, SociologyPlanSign, RegisteredCapital,
                                    GrpCategory, OperatorCom, Operator,
                                    MakeDate, MakeTime, ModifyDate,
                                    auditOpinion, ModifyTime, grpNo,
                                    grpContactNo, BusinessTerm, Trade,
                                    Peoples, shareBusiness, shareholdersName,
                                    idImage1, idImage2, grpIDImage1,
                                    legIDImage1, grpIDImage2, legIDImage2)
        values (#{registSN,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{mobilePhone,jdbcType=VARCHAR},
                #{email,jdbcType=VARCHAR}, #{nativeplace,jdbcType=VARCHAR}, #{idType,jdbcType=VARCHAR},
                #{idNo,jdbcType=VARCHAR}, #{idTypeStartDate,jdbcType=DATE}, #{idTypeEndDate,jdbcType=DATE},
                #{sex,jdbcType=VARCHAR}, #{birthday,jdbcType=DATE}, #{department,jdbcType=VARCHAR},
                #{grpName,jdbcType=VARCHAR}, #{grpIdType,jdbcType=VARCHAR}, #{grpIdNo,jdbcType=VARCHAR},
                #{zipCode,jdbcType=VARCHAR}, #{grpAddress,jdbcType=VARCHAR}, #{unifiedsociCode,jdbcType=VARCHAR},
                #{grpType,jdbcType=VARCHAR}, #{accName,jdbcType=VARCHAR}, #{grpBankcode,jdbcType=VARCHAR},
                #{grpBankaccno,jdbcType=VARCHAR}, #{telphone,jdbcType=VARCHAR}, #{regAddress,jdbcType=VARCHAR},
                #{clientNo,jdbcType=VARCHAR}, #{corporationMan,jdbcType=VARCHAR}, #{checkStatus,jdbcType=VARCHAR},
                #{grpTypeStartDate,jdbcType=DATE}, #{grpTypeEndDate,jdbcType=DATE}, #{grpEstablishDate,jdbcType=DATE},
                #{grpScaleType,jdbcType=VARCHAR}, #{sociologyPlanSign,jdbcType=VARCHAR},
                #{registeredCapital,jdbcType=VARCHAR},
                #{grpCategory,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
                #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
                #{auditOpinion,jdbcType=VARCHAR}, #{modifyTime,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR},
                #{grpContactNo,jdbcType=VARCHAR}, #{businessTerm,jdbcType=DATE}, #{trade,jdbcType=VARCHAR},
                #{peoples,jdbcType=BIGINT}, #{shareBusiness,jdbcType=VARCHAR}, #{shareholdersName,jdbcType=VARCHAR},
                #{idImage1,jdbcType=LONGVARCHAR}, #{idImage2,jdbcType=LONGVARCHAR}, #{grpIDImage1,jdbcType=LONGVARCHAR},
                #{legIDImage1,jdbcType=LONGVARCHAR}, #{grpIDImage2,jdbcType=LONGVARCHAR},
                #{legIDImage2,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcHrRegistTemp">
        insert into fchrregisttemp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="registSN != null">
                RegistSN,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="mobilePhone != null">
                mobilePhone,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="nativeplace != null">
                nativeplace,
            </if>
            <if test="idType != null">
                idType,
            </if>
            <if test="idNo != null">
                idNo,
            </if>
            <if test="idTypeStartDate != null">
                idTypeStartDate,
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="department != null">
                department,
            </if>
            <if test="grpName != null">
                grpName,
            </if>
            <if test="grpIdType != null">
                grpIdType,
            </if>
            <if test="grpIdNo != null">
                grpIdNo,
            </if>
            <if test="zipCode != null">
                zipCode,
            </if>
            <if test="grpAddress != null">
                grpAddress,
            </if>
            <if test="unifiedsociCode != null">
                unifiedsociCode,
            </if>
            <if test="grpType != null">
                grpType,
            </if>
            <if test="accName != null">
                accName,
            </if>
            <if test="grpBankcode != null">
                grpBankcode,
            </if>
            <if test="grpBankaccno != null">
                grpBankaccno,
            </if>
            <if test="telphone != null">
                telphone,
            </if>
            <if test="regAddress != null">
                regAddress,
            </if>
            <if test="clientNo != null">
                clientNo,
            </if>
            <if test="corporationMan != null">
                corporationMan,
            </if>
            <if test="checkStatus != null">
                checkStatus,
            </if>
            <if test="grpTypeStartDate != null">
                GrpTypeStartDate,
            </if>
            <if test="grpTypeEndDate != null">
                GrpTypeEndDate,
            </if>
            <if test="grpEstablishDate != null">
                GrpEstablishDate,
            </if>
            <if test="grpScaleType != null">
                GrpScaleType,
            </if>
            <if test="sociologyPlanSign != null">
                SociologyPlanSign,
            </if>
            <if test="registeredCapital != null">
                RegisteredCapital,
            </if>
            <if test="grpCategory != null">
                GrpCategory,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="auditOpinion != null">
                auditOpinion,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
            <if test="grpNo != null">
                grpNo,
            </if>
            <if test="grpContactNo != null">
                grpContactNo,
            </if>
            <if test="businessTerm != null">
                BusinessTerm,
            </if>
            <if test="trade != null">
                Trade,
            </if>
            <if test="peoples != null">
                Peoples,
            </if>
            <if test="shareBusiness != null">
                shareBusiness,
            </if>
            <if test="shareholdersName != null">
                shareholdersName,
            </if>
            <if test="idImage1 != null">
                idImage1,
            </if>
            <if test="idImage2 != null">
                idImage2,
            </if>
            <if test="grpIDImage1 != null">
                grpIDImage1,
            </if>
            <if test="legIDImage1 != null">
                legIDImage1,
            </if>
            <if test="grpIDImage2 != null">
                grpIDImage2,
            </if>
            <if test="legIDImage2 != null">
                legIDImage2,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="registSN != null">
                #{registSN,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeStartDate != null">
                #{idTypeStartDate,jdbcType=DATE},
            </if>
            <if test="idTypeEndDate != null">
                #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="department != null">
                #{department,jdbcType=VARCHAR},
            </if>
            <if test="grpName != null">
                #{grpName,jdbcType=VARCHAR},
            </if>
            <if test="grpIdType != null">
                #{grpIdType,jdbcType=VARCHAR},
            </if>
            <if test="grpIdNo != null">
                #{grpIdNo,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="grpAddress != null">
                #{grpAddress,jdbcType=VARCHAR},
            </if>
            <if test="unifiedsociCode != null">
                #{unifiedsociCode,jdbcType=VARCHAR},
            </if>
            <if test="grpType != null">
                #{grpType,jdbcType=VARCHAR},
            </if>
            <if test="accName != null">
                #{accName,jdbcType=VARCHAR},
            </if>
            <if test="grpBankcode != null">
                #{grpBankcode,jdbcType=VARCHAR},
            </if>
            <if test="grpBankaccno != null">
                #{grpBankaccno,jdbcType=VARCHAR},
            </if>
            <if test="telphone != null">
                #{telphone,jdbcType=VARCHAR},
            </if>
            <if test="regAddress != null">
                #{regAddress,jdbcType=VARCHAR},
            </if>
            <if test="clientNo != null">
                #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test="corporationMan != null">
                #{corporationMan,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null">
                #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="grpTypeStartDate != null">
                #{grpTypeStartDate,jdbcType=DATE},
            </if>
            <if test="grpTypeEndDate != null">
                #{grpTypeEndDate,jdbcType=DATE},
            </if>
            <if test='grpEstablishDate != null'>
                <if test='grpEstablishDate == ""'>
                    null,
                </if>
                <if test='grpEstablishDate != ""'>
                    #{grpEstablishDate},
                </if>
            </if>
            <if test="grpScaleType != null">
                #{grpScaleType,jdbcType=VARCHAR},
            </if>
            <if test="sociologyPlanSign != null">
                #{sociologyPlanSign,jdbcType=VARCHAR},
            </if>
            <if test="registeredCapital != null">
                #{registeredCapital,jdbcType=VARCHAR},
            </if>
            <if test="grpCategory != null">
                #{grpCategory,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="auditOpinion != null">
                #{auditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="grpContactNo != null">
                #{grpContactNo,jdbcType=VARCHAR},
            </if>
            <if test="businessTerm != null">
                #{businessTerm,jdbcType=DATE},
            </if>
            <if test="trade != null">
                #{trade,jdbcType=VARCHAR},
            </if>
            <if test="peoples != null">
                #{peoples,jdbcType=BIGINT},
            </if>
            <if test="shareBusiness != null">
                #{shareBusiness,jdbcType=VARCHAR},
            </if>
            <if test="shareholdersName != null">
                #{shareholdersName,jdbcType=VARCHAR},
            </if>
            <if test="idImage1 != null">
                #{idImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="idImage2 != null">
                #{idImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test="grpIDImage1 != null">
                #{grpIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage1 != null">
                #{legIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="grpIDImage2 != null">
                #{grpIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage2 != null">
                #{legIDImage2,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcHrRegistTemp">
        update fchrregisttemp
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                mobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                idType = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                idNo = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeStartDate != null">
                idTypeStartDate = #{idTypeStartDate,jdbcType=DATE},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="grpName != null">
                grpName = #{grpName,jdbcType=VARCHAR},
            </if>
            <if test="grpIdType != null">
                grpIdType = #{grpIdType,jdbcType=VARCHAR},
            </if>
            <if test="grpIdNo != null">
                grpIdNo = #{grpIdNo,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                zipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="grpAddress != null">
                grpAddress = #{grpAddress,jdbcType=VARCHAR},
            </if>
            <if test="unifiedsociCode != null">
                unifiedsociCode = #{unifiedsociCode,jdbcType=VARCHAR},
            </if>
            <if test="grpType != null">
                grpType = #{grpType,jdbcType=VARCHAR},
            </if>
            <if test="accName != null">
                accName = #{accName,jdbcType=VARCHAR},
            </if>
            <if test="grpBankcode != null">
                grpBankcode = #{grpBankcode,jdbcType=VARCHAR},
            </if>
            <if test="grpBankaccno != null">
                grpBankaccno = #{grpBankaccno,jdbcType=VARCHAR},
            </if>
            <if test="telphone != null">
                telphone = #{telphone,jdbcType=VARCHAR},
            </if>
            <if test="regAddress != null">
                regAddress = #{regAddress,jdbcType=VARCHAR},
            </if>
            <if test="clientNo != null">
                clientNo = #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test="corporationMan != null">
                corporationMan = #{corporationMan,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null">
                checkStatus = #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="grpTypeStartDate != null">
                GrpTypeStartDate = #{grpTypeStartDate,jdbcType=DATE},
            </if>
            <if test="grpTypeEndDate != null">
                GrpTypeEndDate = #{grpTypeEndDate,jdbcType=DATE},
            </if>
            <if test='grpEstablishDate != null'>
                <if test='grpEstablishDate == ""'>
                    grpEstablishDate = null,
                </if>
                <if test='grpEstablishDate != ""'>
                    grpEstablishDate = #{grpEstablishDate},
                </if>
            </if>
            <if test="grpScaleType != null">
                GrpScaleType = #{grpScaleType,jdbcType=VARCHAR},
            </if>
            <if test="sociologyPlanSign != null">
                SociologyPlanSign = #{sociologyPlanSign,jdbcType=VARCHAR},
            </if>
            <if test="registeredCapital != null">
                RegisteredCapital = #{registeredCapital,jdbcType=VARCHAR},
            </if>
            <if test="grpCategory != null">
                GrpCategory = #{grpCategory,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="auditOpinion != null">
                auditOpinion = #{auditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                grpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="grpContactNo != null">
                grpContactNo = #{grpContactNo,jdbcType=VARCHAR},
            </if>
            <if test="businessTerm != null">
                BusinessTerm = #{businessTerm,jdbcType=DATE},
            </if>
            <if test="trade != null">
                Trade = #{trade,jdbcType=VARCHAR},
            </if>
            <if test="peoples != null">
                Peoples = #{peoples,jdbcType=BIGINT},
            </if>
            <if test="shareBusiness != null">
                shareBusiness = #{shareBusiness,jdbcType=VARCHAR},
            </if>
            <if test="shareholdersName != null">
                shareholdersName = #{shareholdersName,jdbcType=VARCHAR},
            </if>
            <if test="idImage1 != null">
                idImage1 = #{idImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="idImage2 != null">
                idImage2 = #{idImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test="grpIDImage1 != null">
                grpIDImage1 = #{grpIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage1 != null">
                legIDImage1 = #{legIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="grpIDImage2 != null">
                grpIDImage2 = #{grpIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage2 != null">
                legIDImage2 = #{legIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test='grpImageFront != null'>
                grp_image_front = #{grpImageFront,jdbcType=LONGVARCHAR},
            </if>
            <if test='grpImageBack != null'>
                grp_image_back = #{grpImageBack,jdbcType=LONGVARCHAR},
            </if>
            <if test='legalImgFront != null'>
                legal_img_front = #{legalImgFront,jdbcType=LONGVARCHAR},
            </if>
            <if test='legalImgBack != null'>
                legal_img_back = #{legalImgBack,jdbcType=LONGVARCHAR},
            </if>
            <if test="idCardFront != null">
                id_card_front = #{idCardFront,jdbcType=LONGVARCHAR},
            </if>
            <if test="idCardBack != null">
                id_card_back = #{idCardBack,jdbcType=LONGVARCHAR},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
        </set>
        where RegistSN = #{registSN,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sinosoft.eflex.model.FcHrRegistTemp">
        update fchrregisttemp
        set name              = #{name,jdbcType=VARCHAR},
            mobilePhone       = #{mobilePhone,jdbcType=VARCHAR},
            email             = #{email,jdbcType=VARCHAR},
            nativeplace       = #{nativeplace,jdbcType=VARCHAR},
            idType            = #{idType,jdbcType=VARCHAR},
            idNo              = #{idNo,jdbcType=VARCHAR},
            idTypeStartDate   = #{idTypeStartDate,jdbcType=DATE},
            idTypeEndDate     = #{idTypeEndDate,jdbcType=DATE},
            sex               = #{sex,jdbcType=VARCHAR},
            birthday          = #{birthday,jdbcType=DATE},
            department        = #{department,jdbcType=VARCHAR},
            grpName           = #{grpName,jdbcType=VARCHAR},
            grpIdType         = #{grpIdType,jdbcType=VARCHAR},
            grpIdNo           = #{grpIdNo,jdbcType=VARCHAR},
            zipCode           = #{zipCode,jdbcType=VARCHAR},
            grpAddress        = #{grpAddress,jdbcType=VARCHAR},
            unifiedsociCode   = #{unifiedsociCode,jdbcType=VARCHAR},
            grpType           = #{grpType,jdbcType=VARCHAR},
            accName           = #{accName,jdbcType=VARCHAR},
            grpBankcode       = #{grpBankcode,jdbcType=VARCHAR},
            grpBankaccno      = #{grpBankaccno,jdbcType=VARCHAR},
            telphone          = #{telphone,jdbcType=VARCHAR},
            regAddress        = #{regAddress,jdbcType=VARCHAR},
            clientNo          = #{clientNo,jdbcType=VARCHAR},
            corporationMan    = #{corporationMan,jdbcType=VARCHAR},
            checkStatus       = #{checkStatus,jdbcType=VARCHAR},
            GrpTypeStartDate  = #{grpTypeStartDate,jdbcType=DATE},
            GrpTypeEndDate    = #{grpTypeEndDate,jdbcType=DATE},
            GrpEstablishDate  = #{grpEstablishDate,jdbcType=DATE},
            GrpScaleType      = #{grpScaleType,jdbcType=VARCHAR},
            SociologyPlanSign = #{sociologyPlanSign,jdbcType=VARCHAR},
            RegisteredCapital = #{registeredCapital,jdbcType=VARCHAR},
            GrpCategory       = #{grpCategory,jdbcType=VARCHAR},
            OperatorCom       = #{operatorCom,jdbcType=VARCHAR},
            Operator          = #{operator,jdbcType=VARCHAR},
            MakeDate          = #{makeDate,jdbcType=DATE},
            MakeTime          = #{makeTime,jdbcType=VARCHAR},
            ModifyDate        = #{modifyDate,jdbcType=DATE},
            auditOpinion      = #{auditOpinion,jdbcType=VARCHAR},
            ModifyTime        = #{modifyTime,jdbcType=VARCHAR},
            grpNo             = #{grpNo,jdbcType=VARCHAR},
            grpContactNo      = #{grpContactNo,jdbcType=VARCHAR},
            BusinessTerm      = #{businessTerm,jdbcType=DATE},
            Trade             = #{trade,jdbcType=VARCHAR},
            Peoples           = #{peoples,jdbcType=BIGINT},
            shareBusiness     = #{shareBusiness,jdbcType=VARCHAR},
            shareholdersName  = #{shareholdersName,jdbcType=VARCHAR},
            idImage1          = #{idImage1,jdbcType=LONGVARCHAR},
            idImage2          = #{idImage2,jdbcType=LONGVARCHAR},
            grpIDImage1       = #{grpIDImage1,jdbcType=LONGVARCHAR},
            legIDImage1       = #{legIDImage1,jdbcType=LONGVARCHAR},
            grpIDImage2       = #{grpIDImage2,jdbcType=LONGVARCHAR},
            legIDImage2       = #{legIDImage2,jdbcType=LONGVARCHAR}
        where RegistSN = #{registSN,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcHrRegistTemp">
        update fchrregisttemp
        set name              = #{name,jdbcType=VARCHAR},
            mobilePhone       = #{mobilePhone,jdbcType=VARCHAR},
            email             = #{email,jdbcType=VARCHAR},
            nativeplace       = #{nativeplace,jdbcType=VARCHAR},
            idType            = #{idType,jdbcType=VARCHAR},
            idNo              = #{idNo,jdbcType=VARCHAR},
            idTypeStartDate   = #{idTypeStartDate,jdbcType=DATE},
            idTypeEndDate     = #{idTypeEndDate,jdbcType=DATE},
            sex               = #{sex,jdbcType=VARCHAR},
            birthday          = #{birthday,jdbcType=DATE},
            department        = #{department,jdbcType=VARCHAR},
            grpName           = #{grpName,jdbcType=VARCHAR},
            grpIdType         = #{grpIdType,jdbcType=VARCHAR},
            grpIdNo           = #{grpIdNo,jdbcType=VARCHAR},
            zipCode           = #{zipCode,jdbcType=VARCHAR},
            grpAddress        = #{grpAddress,jdbcType=VARCHAR},
            unifiedsociCode   = #{unifiedsociCode,jdbcType=VARCHAR},
            grpType           = #{grpType,jdbcType=VARCHAR},
            accName           = #{accName,jdbcType=VARCHAR},
            grpBankcode       = #{grpBankcode,jdbcType=VARCHAR},
            grpBankaccno      = #{grpBankaccno,jdbcType=VARCHAR},
            telphone          = #{telphone,jdbcType=VARCHAR},
            regAddress        = #{regAddress,jdbcType=VARCHAR},
            clientNo          = #{clientNo,jdbcType=VARCHAR},
            corporationMan    = #{corporationMan,jdbcType=VARCHAR},
            checkStatus       = #{checkStatus,jdbcType=VARCHAR},
            GrpTypeStartDate  = #{grpTypeStartDate,jdbcType=DATE},
            GrpTypeEndDate    = #{grpTypeEndDate,jdbcType=DATE},
            GrpEstablishDate  = #{grpEstablishDate,jdbcType=DATE},
            GrpScaleType      = #{grpScaleType,jdbcType=VARCHAR},
            SociologyPlanSign = #{sociologyPlanSign,jdbcType=VARCHAR},
            RegisteredCapital = #{registeredCapital,jdbcType=VARCHAR},
            GrpCategory       = #{grpCategory,jdbcType=VARCHAR},
            OperatorCom       = #{operatorCom,jdbcType=VARCHAR},
            Operator          = #{operator,jdbcType=VARCHAR},
            MakeDate          = #{makeDate,jdbcType=DATE},
            MakeTime          = #{makeTime,jdbcType=VARCHAR},
            ModifyDate        = #{modifyDate,jdbcType=DATE},
            auditOpinion      = #{auditOpinion,jdbcType=VARCHAR},
            ModifyTime        = #{modifyTime,jdbcType=VARCHAR},
            grpNo             = #{grpNo,jdbcType=VARCHAR},
            grpContactNo      = #{grpContactNo,jdbcType=VARCHAR},
            BusinessTerm      = #{businessTerm,jdbcType=DATE},
            Trade             = #{trade,jdbcType=VARCHAR},
            Peoples           = #{peoples,jdbcType=BIGINT},
            shareBusiness     = #{shareBusiness,jdbcType=VARCHAR},
            shareholdersName  = #{shareholdersName,jdbcType=VARCHAR}
        where RegistSN = #{registSN,jdbcType=VARCHAR}
    </update>
    <select id="selectHrRegistTemp" resultType="com.sinosoft.eflex.model.FcHrRegistTemp">
        select RegistSN,
               idImage1,
               idImage2,
               grpIDImage1,
               legIDImage1,
               grpIDImage2,
               legIDImage2
        from fchrregisttemp
        where unifiedsociCode = #{unifiedsociCode}
          and idno = #{idNo}
    </select>
    <insert id="insertSelectiveByHrRegist" parameterType="com.sinosoft.eflex.model.HrRegist">
        insert into fchrregisttemp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="registSN != null">
                RegistSN,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="mobilePhone != null">
                mobilePhone,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="nativeplace != null">
                nativeplace,
            </if>
            <if test="idType != null">
                idType,
            </if>
            <if test="idNo != null">
                idNo,
            </if>
            <if test="grpRegisterAddress != null">
                grpRegisterAddress,
            </if>
            <if test="idTypeStartDate != null">
                idTypeStartDate,
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="department != null">
                department,
            </if>
            <if test="grpName != null">
                grpName,
            </if>
            <if test="grpIdType != null">
                grpIdType,
            </if>
            <if test="grpIdNo != null">
                grpIdNo,
            </if>
            <if test="zipCode != null">
                zipCode,
            </if>
            <if test="grpAddress != null">
                grpAddress,
            </if>
            <if test="unifiedsociCode != null">
                unifiedsociCode,
            </if>
            <if test="grpType != null">
                grpType,
            </if>
            <if test="grpNatureType != null">
                GrpNatureType,
            </if>
            <if test="accName != null">
                accName,
            </if>
            <if test="grpBankcode != null">
                grpBankcode,
            </if>
            <if test="grpBankaccno != null">
                grpBankaccno,
            </if>
            <if test="telphone != null">
                telphone,
            </if>
            <if test="regAddress != null">
                regAddress,
            </if>
            <if test="clientNo != null">
                clientNo,
            </if>
            <if test="corporationMan != null">
                corporationMan,
            </if>
            <if test="checkStatus != null">
                checkStatus,
            </if>
            <if test="grpTypeStartDate != null">
                GrpTypeStartDate,
            </if>
            <if test="grpTypeEndDate != null">
                GrpTypeEndDate,
            </if>
            <if test="grpScaleType != null">
                GrpScaleType,
            </if>
            <if test="sociologyPlanSign != null">
                SociologyPlanSign,
            </if>
            <if test="registeredCapital != null">
                RegisteredCapital,
            </if>
            <if test="grpEstablishDate != null">
                GrpEstablishDate,
            </if>
            <if test="grpCategory != null">
                GrpCategory,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="auditOpinion != null">
                auditOpinion,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
            <if test="businessTerm != null">
                BusinessTerm,
            </if>
            <if test="trade != null">
                Trade,
            </if>
            <if test="peoples != null">
                Peoples,
            </if>
            <if test="shareBusiness != null">
                shareBusiness,
            </if>
            <if test="shareholdersName != null">
                shareholdersName,
            </if>
            <if test="idImage1 != null">
                idImage1,
            </if>
            <if test="idImage2 != null">
                idImage2,
            </if>
            <if test="grpIDImage1 != null">
                grpIDImage1,
            </if>
            <if test="grpIDImage2 != null">
                grpIDImage2,
            </if>
            <if test="legIDImage1 != null">
                legIDImage1,
            </if>
            <if test="legIDImage2 != null">
                legIDImage2,
            </if>
            <if test='legID != null '>
                LegID ,
            </if>
            <if test='legIDType != null '>
                LegIDType ,
            </if>
            <if test='legSex != null '>
                LegSex ,
            </if>
            <if test='legNationality != null '>
                LegNationality ,
            </if>
            <if test='legBirthday != null '>
                LegBirthday ,
            </if>
            <if test='legIDStartDate != null '>
                LegIDStartDate ,
            </if>
            <if test='legIDEndDate != null '>
                LegIDEndDate ,
            </if>
            <if test='businesses != null '>
                businesses ,
            </if>
            <if test='grpImageFront != null'>
                grp_image_front ,
            </if>
            <if test='grpImageBack != null'>
                grp_image_back ,
            </if>
            <if test='legalImgFront != null'>
                legal_img_front ,
            </if>
            <if test='legalImgBack != null'>
                legal_img_back,
            </if>
            <if test="idCardFront != null">
                id_card_front ,
            </if>
            <if test="idCardBack != null">
                id_card_back,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="registSN != null">
                #{registSN,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="grpRegisterAddress != null">
                #{grpRegisterAddress,jdbcType=VARCHAR},
            </if>
            <if test="idTypeStartDate != null">
                #{idTypeStartDate,jdbcType=DATE},
            </if>
            <if test="idTypeEndDate != null">
                #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="department != null">
                #{department,jdbcType=VARCHAR},
            </if>
            <if test="grpName != null">
                #{grpName,jdbcType=VARCHAR},
            </if>
            <if test="grpIdType != null">
                #{grpIdType,jdbcType=VARCHAR},
            </if>
            <if test="grpIdNo != null">
                #{grpIdNo,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="grpAddress != null">
                #{grpAddress,jdbcType=VARCHAR},
            </if>
            <if test="unifiedsociCode != null">
                #{unifiedsociCode,jdbcType=VARCHAR},
            </if>
            <if test="grpType != null">
                #{grpType,jdbcType=VARCHAR},
            </if>
            <if test="grpNatureType != null">
                #{grpNatureType,jdbcType=VARCHAR},
            </if>
            <if test="accName != null">
                #{accName,jdbcType=VARCHAR},
            </if>
            <if test="grpBankcode != null">
                #{grpBankcode,jdbcType=VARCHAR},
            </if>
            <if test="grpBankaccno != null">
                #{grpBankaccno,jdbcType=VARCHAR},
            </if>
            <if test="telphone != null">
                #{telphone,jdbcType=VARCHAR},
            </if>
            <if test="regAddress != null">
                #{regAddress,jdbcType=VARCHAR},
            </if>
            <if test="clientNo != null">
                #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test="corporationMan != null">
                #{corporationMan,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null">
                #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="grpTypeStartDate != null">
                #{grpTypeStartDate,jdbcType=DATE},
            </if>
            <if test="grpTypeEndDate != null">
                #{grpTypeEndDate,jdbcType=DATE},
            </if>
            <if test="grpScaleType != null">
                #{grpScaleType,jdbcType=VARCHAR},
            </if>
            <if test="sociologyPlanSign != null">
                #{sociologyPlanSign,jdbcType=VARCHAR},
            </if>
            <if test="registeredCapital != null">
                #{registeredCapital,jdbcType=VARCHAR},
            </if>
            <if test='grpEstablishDate != null'>
                <if test='grpEstablishDate == ""'>
                    null,
                </if>
                <if test='grpEstablishDate != ""'>
                    #{grpEstablishDate},
                </if>
            </if>
            <if test="grpCategory != null">
                #{grpCategory,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="auditOpinion != null">
                #{auditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="businessTerm != null">
                #{businessTerm,jdbcType=DATE},
            </if>
            <if test="trade != null">
                #{trade,jdbcType=VARCHAR},
            </if>
            <if test="peoples != null">
                #{peoples,jdbcType=BIGINT},
            </if>
            <if test="shareBusiness != null">
                #{shareBusiness,jdbcType=VARCHAR},
            </if>
            <if test="shareholdersName != null">
                #{shareholdersName,jdbcType=VARCHAR},
            </if>
            <if test="idImage1 != null">
                #{idImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="idImage2 != null">
                #{idImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test="grpIDImage1 != null">
                #{grpIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="grpIDImage2 != null">
                #{grpIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage1 != null">
                #{legIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage2 != null">
                #{legIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test='legID != null '>
                #{legID,jdbcType=VARCHAR},
            </if>
            <if test='legIDType != null '>
                #{legIDType,jdbcType=VARCHAR},
            </if>
            <if test='legSex != null '>
                #{legSex,jdbcType=VARCHAR},
            </if>
            <if test='legNationality != null'>
                #{legNationality,jdbcType=VARCHAR},
            </if>
            <if test='legBirthday != null '>
                #{legBirthday,jdbcType=DATE},
            </if>
            <if test='legIDStartDate != null '>
                #{legIDStartDate,jdbcType=DATE},
            </if>
            <if test='legIDEndDate != null '>
                #{legIDEndDate,jdbcType=DATE},
            </if>
            <if test='businesses != null '>
                #{businesses,jdbcType=VARCHAR},
            </if>
            <if test='grpImageFront != null'>
                #{grpImageFront,jdbcType=LONGVARCHAR},
            </if>
            <if test='grpImageBack != null'>
                #{grpImageBack,jdbcType=LONGVARCHAR},
            </if>
            <if test='legalImgFront != null'>
                #{legalImgFront,jdbcType=LONGVARCHAR},
            </if>
            <if test='legalImgBack != null'>
                #{legalImgBack,jdbcType=LONGVARCHAR},
            </if>
            <if test="idCardFront != null">
                #{idCardFront,jdbcType=LONGVARCHAR},
            </if>
            <if test="idCardBack != null">
                #{idCardBack,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateHrRegistTemp" parameterType="com.sinosoft.eflex.model.FcHrRegistTemp">
        update fchrregisttemp
        <set>
            <if test='name != null and name != ""'>
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test='mobilePhone != null and mobilePhone != ""'>
                mobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test='nativeplace != null and nativeplace != ""'>
                nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test='idType != null and idType != ""'>
                idType = #{idType,jdbcType=VARCHAR},
            </if>
            <if test='idNo != null and idNo != ""'>
                idNo = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test='idTypeEndDate != null and idTypeEndDate != ""'>
                idTypeEndDate = #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test='sex != null and sex != ""'>
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test='birthday != null and birthday != ""'>
                birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test='grpName != null and grpName != ""'>
                grpName = #{grpName,jdbcType=VARCHAR},
            </if>
            <if test='grpIdType != null and grpIdType != ""'>
                grpIdType = #{grpIdType,jdbcType=VARCHAR},
            </if>
            <if test='grpIdNo != null and grpIdNo != ""'>
                grpIdNo = #{grpIdNo,jdbcType=VARCHAR},
            </if>
            <if test='zipCode != null and zipCode != ""'>
                zipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test='grpAddress != null and grpAddress != ""'>
                grpAddress = #{grpAddress,jdbcType=VARCHAR},
            </if>
            <if test='unifiedsociCode != null and unifiedsociCode != ""'>
                unifiedsociCode = #{unifiedsociCode,jdbcType=VARCHAR},
            </if>
            <if test='grpType != null and grpType != ""'>
                grpType = #{grpType,jdbcType=VARCHAR},
            </if>
            <if test='accName != null and accName != ""'>
                accName = #{accName,jdbcType=VARCHAR},
            </if>
            <if test='grpBankcode != null and grpBankcode != ""'>
                grpBankcode = #{grpBankcode,jdbcType=VARCHAR},
            </if>
            <if test='grpBankaccno != null and grpBankaccno != ""'>
                grpBankaccno = #{grpBankaccno,jdbcType=VARCHAR},
            </if>
            <if test='telphone != null and telphone != ""'>
                telphone = #{telphone,jdbcType=VARCHAR},
            </if>
            <if test='regAddress != null and regAddress != ""'>
                regAddress = #{regAddress,jdbcType=VARCHAR},
            </if>
            <if test='clientNo != null and clientNo != ""'>
                clientNo = #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test='corporationMan != null and corporationMan != ""'>
                corporationMan = #{corporationMan,jdbcType=VARCHAR},
            </if>
            <if test='checkStatus != null and checkStatus != ""'>
                checkStatus = #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test='grpTypeStartDate != null and grpTypeStartDate != ""'>
                GrpTypeStartDate = #{grpTypeStartDate,jdbcType=DATE},
            </if>
            <if test='grpTypeEndDate != null and grpTypeEndDate != ""'>
                GrpTypeEndDate = #{grpTypeEndDate,jdbcType=DATE},
            </if>
            <if test='grpEstablishDate != null'>
                <if test='grpEstablishDate == ""'>
                    grpEstablishDate = null,
                </if>
                <if test='grpEstablishDate != ""'>
                    grpEstablishDate = #{grpEstablishDate},
                </if>
            </if>
            <if test='grpScaleType != null and grpScaleType != ""'>
                GrpScaleType = #{grpScaleType,jdbcType=VARCHAR},
            </if>
            <if test='sociologyPlanSign != null and sociologyPlanSign != ""'>
                SociologyPlanSign = #{sociologyPlanSign,jdbcType=VARCHAR},
            </if>
            <if test='registeredCapital != null and registeredCapital != ""'>
                RegisteredCapital = #{registeredCapital,jdbcType=VARCHAR},
            </if>
            <if test='grpCategory != null and grpCategory != ""'>
                GrpCategory = #{grpCategory,jdbcType=VARCHAR},
            </if>
            <if test='operatorCom != null and operatorCom != ""'>
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test='operator != null and operator != ""'>
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test='makeDate != null and makeDate != ""'>
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test='makeTime != null and makeTime != ""'>
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test='modifyDate != null and modifyDate != ""'>
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test='auditOpinion != null and auditOpinion != ""'>
                auditOpinion = #{auditOpinion,jdbcType=VARCHAR},
            </if>
            <if test='modifyTime != null and modifyTime != ""'>
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test='grpNo != null and grpNo != ""'>
                grpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test='businessTerm != null and businessTerm != ""'>
                BusinessTerm = #{businessTerm,jdbcType=DATE},
            </if>
            <if test='trade != null and trade != ""'>
                Trade = #{trade,jdbcType=VARCHAR},
            </if>
            <if test='peoples != null and peoples != ""'>
                Peoples = #{peoples,jdbcType=BIGINT},
            </if>
            <if test='shareBusiness != null and shareBusiness != ""'>
                shareBusiness = #{shareBusiness,jdbcType=VARCHAR},
            </if>
            <if test='shareholdersName != null and shareholdersName != ""'>
                shareholdersName = #{shareholdersName,jdbcType=VARCHAR},
            </if>
            <if test='idImage1 != null and idImage1 != ""'>
                idImage1 = #{idImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test='idImage2 != null and idImage2 != ""'>
                idImage2 = #{idImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test='grpIDImage1 != null'>
                grpIDImage1 = #{grpIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test='grpIDImage2 != null'>
                grpIDImage2 = #{grpIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test='legIDImage1 != null'>
                legIDImage1 = #{legIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test='legIDImage2 != null'>
                legIDImage2 = #{legIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test='grpImageFront != null'>
                grp_image_front = #{grpImageFront,jdbcType=LONGVARCHAR},
            </if>
            <if test='grpImageBack != null'>
                grp_image_back = #{grpImageBack,jdbcType=LONGVARCHAR},
            </if>
            <if test='legalImgFront != null'>
                legal_img_front = #{legalImgFront,jdbcType=LONGVARCHAR},
            </if>
            <if test='legalImgBack != null'>
                legal_img_back = #{legalImgBack,jdbcType=LONGVARCHAR},
            </if>
            <if test="idCardFront != null">
                id_card_front = #{idCardFront,jdbcType=LONGVARCHAR},
            </if>
            <if test="idCardBack != null">
                id_card_back = #{idCardBack,jdbcType=LONGVARCHAR},
            </if>
            <if test='legID != null and legID != ""'>
                LegID = #{legID,jdbcType=VARCHAR},
            </if>
            <if test='legIDType != null and legIDType != ""'>
                LegIDType = #{legIDType,jdbcType=VARCHAR},
            </if>
            <if test='legSex != null and legSex != ""'>
                LegSex = #{legSex,jdbcType=VARCHAR},
            </if>
            <if test='legNationality != null and legNationality != ""'>
                LegNationality = #{legNationality,jdbcType=VARCHAR},
            </if>
            <if test='legBirthday != null and legBirthday != ""'>
                LegBirthday = #{legBirthday,jdbcType=DATE},
            </if>
            <if test='legIDStartDate != null and legIDStartDate != ""'>
                LegIDStartDate = #{legIDStartDate,jdbcType=DATE},
            </if>
            <if test='legIDEndDate != null and legIDEndDate != ""'>
                LegIDEndDate = #{legIDEndDate,jdbcType=DATE},
            </if>
            <if test='businesses != null and businesses != ""'>
                businesses = #{businesses,jdbcType=VARCHAR},
            </if>
        </set>
        where grpIdType = #{grpIdType,jdbcType=VARCHAR}
        and grpIdNo = #{grpIdNo,jdbcType=VARCHAR}
    </update>
    <update id="updateFcHrRegistTempInfo" parameterType="com.sinosoft.eflex.model.FcHrRegistTemp">
        update fchrregisttemp
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                mobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                idType = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                idNo = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test="grpName != null">
                grpName = #{grpName,jdbcType=VARCHAR},
            </if>
            <if test="grpIdType != null">
                grpIdType = #{grpIdType,jdbcType=VARCHAR},
            </if>
            <if test="grpIdNo != null">
                grpIdNo = #{grpIdNo,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                zipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="grpAddress != null">
                grpAddress = #{grpAddress,jdbcType=VARCHAR},
            </if>
            <if test="unifiedsociCode != null">
                unifiedsociCode = #{unifiedsociCode,jdbcType=VARCHAR},
            </if>
            <if test="grpType != null">
                grpType = #{grpType,jdbcType=VARCHAR},
            </if>
            <if test="accName != null">
                accName = #{accName,jdbcType=VARCHAR},
            </if>
            <if test="grpBankcode != null">
                grpBankcode = #{grpBankcode,jdbcType=VARCHAR},
            </if>
            <if test="grpBankaccno != null">
                grpBankaccno = #{grpBankaccno,jdbcType=VARCHAR},
            </if>
            <if test="telphone != null">
                telphone = #{telphone,jdbcType=VARCHAR},
            </if>
            <if test="regAddress != null">
                regAddress = #{regAddress,jdbcType=VARCHAR},
            </if>
            <if test="clientNo != null">
                clientNo = #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test="corporationMan != null">
                corporationMan = #{corporationMan,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null">
                checkStatus = #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="grpTypeStartDate != null">
                GrpTypeStartDate = #{grpTypeStartDate,jdbcType=DATE},
            </if>
            <if test="grpTypeEndDate != null">
                GrpTypeEndDate = #{grpTypeEndDate,jdbcType=DATE},
            </if>
            <if test='grpEstablishDate != null'>
                <if test='grpEstablishDate == ""'>
                    grpEstablishDate = null,
                </if>
                <if test='grpEstablishDate != ""'>
                    grpEstablishDate = #{grpEstablishDate},
                </if>
            </if>
            <if test="grpScaleType != null">
                GrpScaleType = #{grpScaleType,jdbcType=VARCHAR},
            </if>
            <if test="sociologyPlanSign != null">
                SociologyPlanSign = #{sociologyPlanSign,jdbcType=VARCHAR},
            </if>
            <if test="registeredCapital != null">
                RegisteredCapital = #{registeredCapital,jdbcType=VARCHAR},
            </if>
            <if test="grpCategory != null">
                GrpCategory = #{grpCategory,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="auditOpinion != null">
                auditOpinion = #{auditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                grpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="businessTerm != null">
                BusinessTerm = #{businessTerm,jdbcType=DATE},
            </if>
            <if test="trade != null">
                Trade = #{trade,jdbcType=VARCHAR},
            </if>
            <if test="peoples != null">
                Peoples = #{peoples,jdbcType=BIGINT},
            </if>
            <if test="shareBusiness != null">
                shareBusiness = #{shareBusiness,jdbcType=VARCHAR},
            </if>
            <if test="shareholdersName != null">
                shareholdersName = #{shareholdersName,jdbcType=VARCHAR},
            </if>
            <if test="idImage1 != null">
                idImage1 = #{idImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="idImage2 != null">
                idImage2 = #{idImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test="grpIDImage1 != null">
                grpIDImage1 = #{grpIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="grpIDImage2 != null">
                grpIDImage2 = #{grpIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage1 != null">
                legIDImage1 = #{legIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage2 != null">
                legIDImage2 = #{legIDImage2,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where 1 = 1
        <!--
        更新企业经办人注册信息
        and grpIdType = #{oldGrpIdType}
        and grpIdNo = #{oldGrpIdNo}
        -->
        and idType = #{oldIdType}
        and idNo = #{oldIdNo}
    </update>
</mapper>