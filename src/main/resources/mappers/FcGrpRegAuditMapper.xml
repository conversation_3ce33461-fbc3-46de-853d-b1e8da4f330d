<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcGrpRegAuditMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcGrpRegAudit">
    <id column="grpRegNo" jdbcType="VARCHAR" property="grpRegNo" />
    <result column="ContactNo" jdbcType="VARCHAR" property="contactNo" />
    <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
    <result column="AuditResult" jdbcType="VARCHAR" property="auditResult" />
    <result column="AuditOpinion" jdbcType="VARCHAR" property="auditOpinion" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
    <result column="AuditResultName" jdbcType="VARCHAR" property="AuditResultName" />
  </resultMap>
  <sql id="Base_Column_List">
    grpRegNo, ContactNo, GrpNo, AuditResult, AuditOpinion, Operator, OperatorCom, MakeDate, 
    MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcgrpregaudit
    where grpRegNo = #{grpRegNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcgrpregaudit
    where grpRegNo = #{grpRegNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FcGrpRegAudit">
    insert into fcgrpregaudit (grpRegNo, ContactNo, GrpNo, 
      AuditResult, AuditOpinion, Operator, 
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{grpRegNo,jdbcType=VARCHAR}, #{contactNo,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR}, 
      #{auditResult,jdbcType=VARCHAR}, #{auditOpinion,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcGrpRegAudit">
    insert into fcgrpregaudit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="grpRegNo != null">
        grpRegNo,
      </if>
      <if test="contactNo != null">
        ContactNo,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
      <if test="auditResult != null">
        AuditResult,
      </if>
      <if test="auditOpinion != null">
        AuditOpinion,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="grpRegNo != null">
        #{grpRegNo,jdbcType=VARCHAR},
      </if>
      <if test="contactNo != null">
        #{contactNo,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditOpinion != null">
        #{auditOpinion,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcGrpRegAudit">
    update fcgrpregaudit
    <set>
      <if test="contactNo != null">
        ContactNo = #{contactNo,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        AuditResult = #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditOpinion != null">
        AuditOpinion = #{auditOpinion,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where grpRegNo = #{grpRegNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcGrpRegAudit">
    update fcgrpregaudit
    set ContactNo = #{contactNo,jdbcType=VARCHAR},
      GrpNo = #{grpNo,jdbcType=VARCHAR},
      AuditResult = #{auditResult,jdbcType=VARCHAR},
      AuditOpinion = #{auditOpinion,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where grpRegNo = #{grpRegNo,jdbcType=VARCHAR}
  </update>
  <select id="selectAuditResult" resultType="com.sinosoft.eflex.model.FcGrpRegAudit" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List" />
     from fcgrpregaudit where
    GrpNo=#{grpNo}
  </select>
    <update id="updateAuditResult" parameterType="com.sinosoft.eflex.model.FcGrpRegAudit">
    update fcgrpregaudit
    set
          AuditResult = #{auditResult,jdbcType=VARCHAR},
          AuditOpinion = #{auditOpinion,jdbcType=VARCHAR},
          ModifyDate = #{modifyDate,jdbcType=DATE},
          ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where grpRegNo = #{grpRegNo,jdbcType=VARCHAR}
    </update>
    <!-- 转码查询 -->
    <select id="selectTranscodingAuditResult" resultType="com.sinosoft.eflex.model.HrRegist" parameterType="java.lang.String">
	    select
	    checkstatus auditResult,
	    (select codeName from FDcode where codeKey = fc.checkstatus and codeType = 'AuditResult') as auditResultName,
	     auditOpinion, Operator, OperatorCom, MakeDate,MakeTime, ModifyDate, ModifyTime,ShareBusiness,ShareholdersName
	     from FCHrRegistTemp fc where
	    RegistSN=#{registSN}
  </select>
  <update id="updateFCHrRegistTemp" parameterType="java.util.Map">
  	update FCHrRegistTemp set checkstatus=#{params.checkStatus,jdbcType=VARCHAR},
  							  ModifyDate = #{params.currentDate,jdbcType=DATE},
          					  ModifyTime = #{params.currentTime,jdbcType=VARCHAR},
          					  Operator=#{params.oprator,jdbcType=VARCHAR},
          					  auditOpinion=#{params.auditOpinion,jdbcType=VARCHAR},
          					  grpNo=#{params.grpNo,jdbcType=VARCHAR}
          where RegistSN=#{params.registSN,jdbcType=VARCHAR}
  </update>
</mapper>