<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcEnsureContactMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcEnsureContact">
      <id column="EnsureCode" jdbcType="VARCHAR" property="ensureCode"/>
      <result column="name" jdbcType="VARCHAR" property="name"/>
      <result column="nativeplace" jdbcType="VARCHAR" property="nativeplace"/>
      <result column="idType" jdbcType="VARCHAR" property="idType"/>
      <result column="idNo" jdbcType="VARCHAR" property="idNo"/>
      <result column="sex" jdbcType="VARCHAR" property="sex"/>
      <result column="birthDay" jdbcType="VARCHAR" property="birthDay"/>
      <result column="mobilePhone" jdbcType="VARCHAR" property="mobilePhone"/>
      <result column="department" jdbcType="VARCHAR" property="department"/>
      <result column="idTypeStartDate" jdbcType="DATE" property="idTypeStartDate"/>
      <result column="idTypeEndDate" jdbcType="DATE" property="idTypeEndDate"/>
      <result column="email" jdbcType="VARCHAR" property="email"/>
      <result column="idImage1" jdbcType="VARCHAR" property="idImage1"/>
      <result column="idImage2" jdbcType="VARCHAR" property="idImage2"/>
      <result column="Operator" jdbcType="VARCHAR" property="operator"/>
      <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
      <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
      <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
      <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
      <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
  </resultMap>
  <sql id="Base_Column_List">
      EnsureCode
      , name, nativeplace, idType, idNo, sex, birthDay, mobilePhone, department,
    idTypeStartDate, idTypeEndDate, email, idImage1, idImage2, Operator, OperatorCom, 
    MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcensurecontact
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
  </select>
    <select id="selectByEnsureCode" resultType="com.sinosoft.eflex.model.FcEnsureContact">
        select * from fcensurecontact where EnsureCode=#{ensureCode}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcensurecontact
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FcEnsureContact">
      insert into fcensurecontact (EnsureCode, name, nativeplace,
                                   idType, idNo, sex,
                                   birthDay, mobilePhone, department,
                                   idTypeStartDate, idTypeEndDate, email,
                                   idImage1, idImage2, Operator,
                                   OperatorCom, MakeDate, MakeTime,
                                   ModifyDate, ModifyTime)
      values (#{ensureCode,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{nativeplace,jdbcType=VARCHAR},
              #{idType,jdbcType=VARCHAR}, #{idNo,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR},
              #{birthDay,jdbcType=VARCHAR}, #{mobilePhone,jdbcType=VARCHAR}, #{department,jdbcType=VARCHAR},
              #{idTypeStartDate,jdbcType=DATE}, #{idTypeEndDate,jdbcType=DATE}, #{email,jdbcType=VARCHAR},
              #{idImage1,jdbcType=VARCHAR}, #{idImage2,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
              #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
              #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcEnsureContact">
        insert into fcensurecontact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                EnsureCode,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="nativeplace != null">
                nativeplace,
            </if>
            <if test="idType != null">
                idType,
            </if>
            <if test="idNo != null">
                idNo,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="birthDay != null">
                birthDay,
            </if>
            <if test="mobilePhone != null">
                mobilePhone,
            </if>
            <if test="department != null">
                department,
            </if>
            <if test="idTypeStartDate != null">
                idTypeStartDate,
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="idImage1 != null">
                idImage1,
            </if>
            <if test="idImage2 != null">
                idImage2,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthDay != null">
                #{birthDay,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                #{department,jdbcType=VARCHAR},
            </if>
            <if test="idTypeStartDate != null">
                #{idTypeStartDate,jdbcType=DATE},
            </if>
            <if test="idTypeEndDate != null">
                #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="idImage1 != null">
                #{idImage1,jdbcType=VARCHAR},
            </if>
            <if test="idImage2 != null">
                #{idImage2,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
  </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcEnsureContact">
        update fcensurecontact
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                idType = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                idNo = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthDay != null">
                birthDay = #{birthDay,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                mobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="idTypeStartDate != null">
                idTypeStartDate = #{idTypeStartDate,jdbcType=DATE},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="idImage1 != null">
                idImage1 = #{idImage1,jdbcType=VARCHAR},
            </if>
            <if test="idImage2 != null">
                idImage2 = #{idImage2,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcEnsureContact">
      update fcensurecontact
      set name            = #{name,jdbcType=VARCHAR},
          nativeplace     = #{nativeplace,jdbcType=VARCHAR},
          idType          = #{idType,jdbcType=VARCHAR},
          idNo            = #{idNo,jdbcType=VARCHAR},
          sex             = #{sex,jdbcType=VARCHAR},
          birthDay        = #{birthDay,jdbcType=VARCHAR},
          mobilePhone     = #{mobilePhone,jdbcType=VARCHAR},
          department      = #{department,jdbcType=VARCHAR},
          idTypeStartDate = #{idTypeStartDate,jdbcType=DATE},
          idTypeEndDate   = #{idTypeEndDate,jdbcType=DATE},
          email           = #{email,jdbcType=VARCHAR},
          idImage1        = #{idImage1,jdbcType=VARCHAR},
          idImage2        = #{idImage2,jdbcType=VARCHAR},
          Operator        = #{operator,jdbcType=VARCHAR},
          OperatorCom     = #{operatorCom,jdbcType=VARCHAR},
          MakeDate        = #{makeDate,jdbcType=DATE},
          MakeTime        = #{makeTime,jdbcType=VARCHAR},
          ModifyDate      = #{modifyDate,jdbcType=DATE},
          ModifyTime      = #{modifyTime,jdbcType=VARCHAR}
      where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
  </update>
</mapper>