<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcDailyInsureRiskInfoMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcDailyInsureRiskInfo">
        <id column="DeployNo" jdbcType="VARCHAR" property="deployNo"/>
        <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode"/>
        <result column="RiskCode" jdbcType="VARCHAR" property="riskCode"/>
        <result column="OperatorCom" jdbcType="DATE" property="operatorCom"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>

    <insert id="insert" parameterType="com.sinosoft.eflex.model.FcDailyInsureRiskInfo">
		insert into FcDailyInsureRiskInfo
		(DeployNo,
		EnsureCode,
		RiskCode,
		FeeRatio,
		CommissionOrAllowanceRatio,
		OperatorCom,
		Operator,
		MakeDate,
		MakeTime,
		ModifyDate,
		ModifyTime)
		values (#{DeployNo,jdbcType=VARCHAR},
		#{EnsureCode,jdbcType=VARCHAR},
		#{RiskCode,jdbcType=VARCHAR},
		#{FeeRatio,jdbcType=DOUBLE},
		#{CommissionOrAllowanceRatio,jdbcType=DOUBLE},
		#{OperatorCom,jdbcType=VARCHAR},
		#{Operator,jdbcType=DATE},
		#{MakeDate,jdbcType=VARCHAR},
		#{MakeTime,jdbcType=VARCHAR},
		#{ModifyDate,jdbcType=VARCHAR},
		#{ModifyTime,jdbcType=DATE})
	</insert>
	<delete id="deleteByEnsureCode" parameterType="java.lang.String">
    delete from FcDailyInsureRiskInfo
    where EnsureCode = #{EnsureCode,jdbcType=VARCHAR}
  </delete>
  <select id="selectStopSaleRiskCodeByEnsureCode" resultType="java.lang.String">
      select DISTINCT a.riskCode from fcdailyinsureriskinfo a inner join fdriskInfo b on a.RiskCode=b.riskCode and b.StopTime &lt; NOW()
      where a.ensureCode=#{ensureCode};
  </select>
    <select id="selectDailyRiskCodeByEnsureCode" resultType="java.lang.String">
      select DISTINCT a.riskCode from fcdailyinsureriskinfo a where a.ensureCode=#{ensureCode};
  </select>
	<select id="selectByEnsureCodeAndRiskCode" parameterType="map"
			resultType="com.sinosoft.eflex.model.FcDailyInsureRiskInfo">
		select FeeRatio,CommissionOrAllowanceRatio
		from fcdailyinsureriskinfo
		where EnsureCode = #{ensureCode,jdbcType=VARCHAR} and RiskCode = #{riskCode,jdbcType=VARCHAR}
	</select>
	<select id="selectByEnsureCode" parameterType="java.lang.String" resultType="string">
		select riskCode
		from fcdailyinsureriskinfo
		where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
	</select>

	<update id="updateRatioByEnsureCode" parameterType="com.sinosoft.eflex.model.FcDailyInsureRiskInfo">
		update fcdailyinsureriskinfo
		<set>
			<if test="DeployNo != null">
				DeployNo = #{DeployNo,jdbcType=VARCHAR},
			</if>
			<if test="RiskCode != null">
				RiskCode = #{RiskCode,jdbcType=VARCHAR},
			</if>
			<if test="FeeRatio != null">
				FeeRatio = #{FeeRatio,jdbcType=DOUBLE},
			</if>
			<if test="CommissionOrAllowanceRatio != null">
				CommissionOrAllowanceRatio = #{CommissionOrAllowanceRatio,jdbcType=DOUBLE},
			</if>
			<if test="OperatorCom != null">
				OperatorCom = #{OperatorCom,jdbcType=VARCHAR},
			</if>
			<if test="Operator != null">
				Operator = #{Operator,jdbcType=VARCHAR},
			</if>
			<if test="MakeDate != null">
				MakeDate = #{MakeDate,jdbcType=DATE},
			</if>
			<if test="MakeTime != null">
				MakeTime = #{MakeTime,jdbcType=VARCHAR},
			</if>
			<if test="ModifyDate != null">
				ModifyDate = #{ModifyDate,jdbcType=DATE},
			</if>
			<if test="ModifyTime != null">
				ModifyTime = #{ModifyTime,jdbcType=VARCHAR}
			</if>
		</set>
		where EnsureCode = #{EnsureCode,jdbcType=VARCHAR}
	</update>

</mapper>