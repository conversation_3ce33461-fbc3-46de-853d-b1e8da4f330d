<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDValidateCodeMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDValidateCode">
    <id column="ValidateCodeSN" jdbcType="VARCHAR" property="validatecodesn" />
    <result column="UserNo" jdbcType="VARCHAR" property="userno" />
    <result column="ValidateCode" jdbcType="VARCHAR" property="validatecode" />
    <result column="CodeType" jdbcType="VARCHAR" property="codetype" />
    <result column="IsValid" jdbcType="VARCHAR" property="isvalid" />
    <result column="MakeDate" jdbcType="DATE" property="makedate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="maketime" />
    <result column="EndDate" jdbcType="DATE" property="enddate" />
    <result column="EndTime" jdbcType="VARCHAR" property="endtime" />
  </resultMap>
  <sql id="Base_Column_List">
    ValidateCodeSN, UserNo, ValidateCode, CodeType, IsValid, MakeDate, MakeTime, EndDate, 
    EndTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fdvalidatecode
    where ValidateCodeSN = #{validatecodesn,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fdvalidatecode
    where ValidateCodeSN = #{validatecodesn,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDValidateCode">
    insert into fdvalidatecode (ValidateCodeSN, UserNo, ValidateCode, 
      CodeType, IsValid, MakeDate, 
      MakeTime, EndDate, EndTime
      )
    values (#{validatecodesn,jdbcType=VARCHAR}, #{userno,jdbcType=VARCHAR}, #{validatecode,jdbcType=VARCHAR}, 
      #{codetype,jdbcType=VARCHAR}, #{isvalid,jdbcType=VARCHAR}, #{makedate,jdbcType=DATE}, 
      #{maketime,jdbcType=VARCHAR}, #{enddate,jdbcType=DATE}, #{endtime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDValidateCode">
    insert into fdvalidatecode
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="validatecodesn != null">
        ValidateCodeSN,
      </if>
      <if test="userno != null">
        UserNo,
      </if>
      <if test="validatecode != null">
        ValidateCode,
      </if>
      <if test="codetype != null">
        CodeType,
      </if>
      <if test="isvalid != null">
        IsValid,
      </if>
      <if test="makedate != null">
        MakeDate,
      </if>
      <if test="maketime != null">
        MakeTime,
      </if>
      <if test="enddate != null">
        EndDate,
      </if>
      <if test="endtime != null">
        EndTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="validatecodesn != null">
        #{validatecodesn,jdbcType=VARCHAR},
      </if>
      <if test="userno != null">
        #{userno,jdbcType=VARCHAR},
      </if>
      <if test="validatecode != null">
        #{validatecode,jdbcType=VARCHAR},
      </if>
      <if test="codetype != null">
        #{codetype,jdbcType=VARCHAR},
      </if>
      <if test="isvalid != null">
        #{isvalid,jdbcType=VARCHAR},
      </if>
      <if test="makedate != null">
        #{makedate,jdbcType=DATE},
      </if>
      <if test="maketime != null">
        #{maketime,jdbcType=VARCHAR},
      </if>
      <if test="enddate != null">
        #{enddate,jdbcType=DATE},
      </if>
      <if test="endtime != null">
        #{endtime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDValidateCode">
    update fdvalidatecode
    <set>
      <if test="userno != null">
        UserNo = #{userno,jdbcType=VARCHAR},
      </if>
      <if test="validatecode != null">
        ValidateCode = #{validatecode,jdbcType=VARCHAR},
      </if>
      <if test="codetype != null">
        CodeType = #{codetype,jdbcType=VARCHAR},
      </if>
      <if test="isvalid != null">
        IsValid = #{isvalid,jdbcType=VARCHAR},
      </if>
      <if test="makedate != null">
        MakeDate = #{makedate,jdbcType=DATE},
      </if>
      <if test="maketime != null">
        MakeTime = #{maketime,jdbcType=VARCHAR},
      </if>
      <if test="enddate != null">
        EndDate = #{enddate,jdbcType=DATE},
      </if>
      <if test="endtime != null">
        EndTime = #{endtime,jdbcType=VARCHAR},
      </if>
    </set>
    where ValidateCodeSN = #{validatecodesn,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDValidateCode">
    update fdvalidatecode
    set UserNo = #{userno,jdbcType=VARCHAR},
      ValidateCode = #{validatecode,jdbcType=VARCHAR},
      CodeType = #{codetype,jdbcType=VARCHAR},
      IsValid = #{isvalid,jdbcType=VARCHAR},
      MakeDate = #{makedate,jdbcType=DATE},
      MakeTime = #{maketime,jdbcType=VARCHAR},
      EndDate = #{enddate,jdbcType=DATE},
      EndTime = #{endtime,jdbcType=VARCHAR}
    where ValidateCodeSN = #{validatecodesn,jdbcType=VARCHAR}
  </update>
  <select id="selectLastCodeByUserNo" resultMap="BaseResultMap">
		select fvc.ValidateCodeSN,
			   fvc.ValidateCode,
		       fvc.IsValid,
		       fvc.EndDate,
		       fvc.EndTime
		  from FDValidateCode fvc
		 where fvc.userno = #{userNo,jdbcType=VARCHAR}
		   and fvc.codetype = #{codeType,jdbcType=VARCHAR}
		   and fvc.validatecodesn = (select max(f.validatecodesn)
		                               from FDValidateCode f
		                              where f.userno = #{userNo,jdbcType=VARCHAR}
		                                and f.codetype = #{codeType,jdbcType=VARCHAR})
  </select>
 <select id="selectByUserNo" parameterType="java.lang.String" resultMap="BaseResultMap">
   select
   <include refid="Base_Column_List" />
   from fdvalidatecode
   where  UserNo = #{userno,jdbcType=VARCHAR}
 </select>
</mapper>