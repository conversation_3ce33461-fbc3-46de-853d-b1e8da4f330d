<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.InsuredReadingLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.entity.InsuredReadingLog">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="ensure_code" property="ensureCode" />
        <result column="read_id" property="readId" />
        <result column="read_status" property="readStatus" />
        <result column="read_time" property="readTime" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, ensure_code, read_id, read_status, read_time, created_time, updated_time
    </sql>

</mapper>
