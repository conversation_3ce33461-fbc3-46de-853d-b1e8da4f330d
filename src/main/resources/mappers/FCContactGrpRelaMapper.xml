<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCContactGrpRelaMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCContactGrpRela">
        <id column="contactNo" jdbcType="VARCHAR" property="contactNo"/>
        <id column="contactType" jdbcType="VARCHAR" property="contactType"/>
        <id column="grpNo" jdbcType="VARCHAR" property="grpNo"/>
        <result column="LockState" jdbcType="VARCHAR" property="lockState"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        contactNo
        ,contactType,grpNo, LockState, Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCContactGrpRelaKey"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fccontactgrprela
        where contactNo = #{contactNo,jdbcType=VARCHAR}
        and grpNo = #{grpNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCContactGrpRelaKey">
        delete
        from fccontactgrprela
        where contactNo = #{contactNo,jdbcType=VARCHAR}
          and contactType = #{contactType,jdbcType=VARCHAR}
          and grpNo = #{grpNo,jdbcType=VARCHAR}
    </delete>


    <delete id="deleteByGrpNo" parameterType="com.sinosoft.eflex.model.FCContactGrpRelaKey">
        delete
        from fccontactgrprela
        where grpNo = #{grpNo,jdbcType=VARCHAR}
    </delete>


    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCContactGrpRela">
        insert into fccontactgrprela (contactNo, contactType, grpNo, Operator,
                                      OperatorCom, MakeDate, MakeTime,
                                      ModifyDate, ModifyTime, LockState)
        values (#{contactNo,jdbcType=VARCHAR}, #{contactType,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR},
                #{operator,jdbcType=VARCHAR},
                #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
                #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}, #{lockState,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCContactGrpRela">
        insert into fccontactgrprela
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contactNo != null">
                contactNo,
            </if>
            <if test="contactType != null">
                contactType,
            </if>
            <if test="grpNo != null">
                grpNo,
            </if>
            <if test="lockState != null">
                lockState,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contactNo != null">
                #{contactNo,jdbcType=VARCHAR},
            </if>
            <if test="contactType != null">
                #{contactType,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="lockState != null">
                #{lockState,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCContactGrpRela">
        update fccontactgrprela
        <set>
            <if test="lockState != null">
                LockState = #{lockState,jdbcType=VARCHAR},
            </if>
            <if test="contactType != null">
                ContactType = #{contactType,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where contactNo = #{contactNo,jdbcType=VARCHAR}
        <if test="grpNo != null and grpNo != ''">
            and grpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCContactGrpRela">
        update fccontactgrprela
        set ContactType = #{contactType,jdbcType=VARCHAR},
            Operator    = #{operator,jdbcType=VARCHAR},
            OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            MakeDate    = #{makeDate,jdbcType=DATE},
            MakeTime    = #{makeTime,jdbcType=VARCHAR},
            ModifyDate  = #{modifyDate,jdbcType=DATE},
            ModifyTime  = #{modifyTime,jdbcType=VARCHAR}
        where contactNo = #{contactNo,jdbcType=VARCHAR}
          and contactType = #{contactType,jdbcType=VARCHAR}
          and grpNo = #{grpNo,jdbcType=VARCHAR}
    </update>

    <select id="checkHrLockState" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM fccontactgrprela
        WHERE ContactNo IN (SELECT ContactNo FROM fcgrpcontact WHERE IDNo = #{idNo})
          AND LockState = '0'
    </select>

    <select id="selectContactNoByGrpNoandcontactType" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT contactNo
        FROM fccontactgrprela
        WHERE grpNo = #{grpNo}
          AND contactType = '01'
    </select>

    <select id="selectByGrpNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fccontactgrprela
        where grpNo = #{grpNo,jdbcType=VARCHAR}
    </select>

    <select id="seletcContactListByMap" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fccontactgrprela
        where 1= 1
        <if test=" contactNo != null ">
            and contactNo = #{contactNo,jdbcType=VARCHAR}
        </if>
        <if test=" grpNo != null ">
            and grpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test=" contactType != null ">
            and ContactType = #{contactType,jdbcType=VARCHAR}
        </if>
        <if test=" lockState != null ">
            and LockState = #{lockState,jdbcType=VARCHAR}
        </if>
        Order by MakeDate,MakeTime
    </select>
</mapper>