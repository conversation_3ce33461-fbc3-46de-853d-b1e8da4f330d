<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCPersonImageMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCPersonImage">
    <id column="ImageNo" jdbcType="VARCHAR" property="imageNo" />
    <result column="OrderItemNo" jdbcType="VARCHAR" property="orderItemNo" />
    <result column="Relation" jdbcType="VARCHAR" property="relation" />
    <result column="ImageType" jdbcType="VARCHAR" property="imageType" />
    <result column="ImageOrder" jdbcType="VARCHAR" property="imageOrder" />
    <result column="ImageUrl" jdbcType="VARCHAR" property="imageUrl" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ImageNo, OrderItemNo, Relation, ImageType, ImageOrder, ImageUrl, Operator, 
    OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcpersonimage
    where ImageNo = #{imageNo,jdbcType=VARCHAR}
  </select>
  <select id="selectByOrderItemNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcpersonimage
    where orderItemNo = #{orderItemNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcpersonimage
    where ImageNo = #{imageNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCPersonImage">
    insert into fcpersonimage (ImageNo, OrderItemNo, PersonId, 
      Relation, ImageType, ImageOrder, 
      ImageUrl, Operator, OperatorCom, 
      MakeDate, MakeTime, ModifyDate, 
      ModifyTime)
    values (#{imageNo,jdbcType=VARCHAR}, #{orderItemNo,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR}, 
      #{relation,jdbcType=VARCHAR}, #{imageType,jdbcType=VARCHAR}, #{imageOrder,jdbcType=VARCHAR}, 
      #{imageUrl,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, 
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, 
      #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCPersonImage">
    insert into fcpersonimage
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="imageNo != null">
        ImageNo,
      </if>
      <if test="orderItemNo != null">
        OrderItemNo,
      </if>
      <if test="relation != null">
        Relation,
      </if>
      <if test="imageType != null">
        ImageType,
      </if>
      <if test="imageOrder != null">
        ImageOrder,
      </if>
      <if test="imageUrl != null">
        ImageUrl,
      </if>
      <if test="imageFtpUrl != null">
        ImageFtpUrl,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
      <if test="imageNumber != null">
        ImageNumber,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="imageNo != null">
        #{imageNo,jdbcType=VARCHAR},
      </if>
      <if test="orderItemNo != null">
        #{orderItemNo,jdbcType=VARCHAR},
      </if>
      <if test="relation != null">
        #{relation,jdbcType=VARCHAR},
      </if>
      <if test="imageType != null">
        #{imageType,jdbcType=VARCHAR},
      </if>
      <if test="imageOrder != null">
        #{imageOrder,jdbcType=INTEGER},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
       <if test="imageFtpUrl != null">
       #{imageFtpUrl,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
      <if test="imageNumber != null">
        #{imageNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getImageOrderInfo" parameterType="java.lang.String" resultType="java.lang.Integer">
  	select MAX(ImageOrder)+1 from fcpersonimage 
  	where orderItemNo=#{orderItemNo,jdbcType=VARCHAR} 
  	and imageType=#{imageType,jdbcType=VARCHAR}
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCPersonImage">
    update fcpersonimage
    <set>
      <if test="orderItemNo != null">
        OrderItemNo = #{orderItemNo,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        PersonId = #{personId,jdbcType=VARCHAR},
      </if>
      <if test="relation != null">
        Relation = #{relation,jdbcType=VARCHAR},
      </if>
      <if test="imageType != null">
        ImageType = #{imageType,jdbcType=VARCHAR},
      </if>
      <if test="imageOrder != null">
        ImageOrder = #{imageOrder,jdbcType=VARCHAR},
      </if>
      <if test="imageUrl != null">
        ImageUrl = #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
      <if test="imageNumber != null">
        #{imageNumber,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where ImageNo = #{imageNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPersonImage">
    update fcpersonimage
    set OrderItemNo = #{orderItemNo,jdbcType=VARCHAR},
      PersonId = #{personId,jdbcType=VARCHAR},
      Relation = #{relation,jdbcType=VARCHAR},
      ImageType = #{imageType,jdbcType=VARCHAR},
      ImageOrder = #{imageOrder,jdbcType=VARCHAR},
      ImageUrl = #{imageUrl,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where ImageNo = #{imageNo,jdbcType=VARCHAR}
  </update>
  <select id="selectCountByOrderItemNo" parameterType="java.lang.String" resultType="java.lang.Integer">
    select
    count(1)
    from fcpersonimage
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
    and  PersonId=#{personID,jdbcType=VARCHAR}
  </select>
    <select id="selectImages" parameterType="java.util.Map"  resultType="com.sinosoft.eflex.model.FCPersonImage">
      select ImageNo imageNo,
             ImageType imageType,
             ImageOrder imageOrder,
             ImageUrl imageUrl
      from fcpersonimage
      where OrderItemNo = #{orderItemNo}
      <if test="relation != ''">
        and Relation = #{relation}
      </if>
      <if test="fileType != ''">
        and ImageType = #{fileType}
      </if>
      <if test="relation == ''">
        and Relation != '0'
      </if>

    </select>
    <select id="selectImagesSign" parameterType="java.util.Map"  resultType="com.sinosoft.eflex.model.FCPersonImage">
      select
      <include refid="Base_Column_List" />
      from fcpersonimage
      where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
        and ImageType = #{fileType,jdbcType=VARCHAR}
    </select>
    <select id="selectImagesInfo" resultType="com.sinosoft.eflex.model.FCPersonImage" parameterType="java.util.Map">
      select ImageNo imageNo,
             ImageType imageType,
             ImageOrder imageOrder,
             ImageUrl imageUrl
      from fcpersonimage
      where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
        and Relation = #{relation,jdbcType=VARCHAR}
		and imageType = #{imageType,jdbcType=VARCHAR}
    </select>
  <select id="selectOrderFace" resultType="java.lang.String">
    select bestFrame from fcorderface
    where OrderNo in (select orderno from fcorderitem where orderitemno=#{orderItemNo})  
    and personId = #{personId} and IsValid = '1' and IsPass = '1'
  </select>
  <select id="selectOrderFaceishad" resultType="java.lang.Integer">
    select count(*) from fcorderface
    where OrderNo in (select orderno from fcorderitem where orderitemno=#{orderItemNo})
    and personId = #{personId} and IsValid = '1'
  </select>
    <select id="querySignImage" resultType="com.sinosoft.eflex.model.FCPersonImage">
      select fe.*
      from fcpersonimage fe inner join  fcorderitem fm on fe.OrderItemNo=fm.OrderItemNo
                            inner join fcorder fr on fm.OrderNo = fr.OrderNo
      where  ImageType = '0804'  and status=false  and  fe.MakeDate >=#{date} order by fe.MakeDate asc limit 800
    </select>
  <select id="selectByOrderItemNoAndImageType" resultType="com.sinosoft.eflex.model.FCPersonImage">
    select
    <include refid="Base_Column_List" />
    from fcpersonimage
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR} and ImageType =#{imageType,jdbcType=VARCHAR}
  </select>
  <update id="updateImageFtpUrlByImageNo" parameterType="java.util.List">
  	<if test="list != null and list.size() > 0 ">
            UPDATE fcpersonimage SET
            imageFtpUrl = CASE ImageNo
              <foreach collection="list" item="item">
                  WHEN #{item.imageNo,jdbcType=VARCHAR}
                  THEN #{item.imageFtpUrl,jdbcType=VARCHAR}
              </foreach>
              </if>
            END
            WHERE ImageNo IN
            <if test="list != null and list.size() > 0 ">
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item.imageNo,jdbcType=VARCHAR}
                </foreach>
            </if>
  </update>
  <update id="updateByImageNo">
    update fcpersonimage SET status=#{status} where ImageNo =#{imageNo,jdbcType=VARCHAR}
  </update>

</mapper>