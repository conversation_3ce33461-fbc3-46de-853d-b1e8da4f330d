<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCPayMoneyDealMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCPayMoneyDeal">
    <id column="OrderNo" jdbcType="VARCHAR" property="orderNo" />
    <id column="PayFlowNo" jdbcType="VARCHAR" property="payFlowNo" />
    <result column="Amount" jdbcType="DOUBLE" property="amount" />
    <result column="PayStatus" jdbcType="VARCHAR" property="payStatus" />
    <result column="PaPayDeskFlowNo" jdbcType="VARCHAR" property="paPayDeskFlowNo" />
    <result column="PayType" jdbcType="VARCHAR" property="payType" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    OrderNo, PayFlowNo, Amount, PayStatus, PaPayDeskFlowNo, PayType, Operator, OperatorCom, 
    MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcpaymoneydeal
    where OrderNo = #{orderNo,jdbcType=VARCHAR}
      and PayFlowNo = #{payFlowNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcpaymoneydeal
    where OrderNo = #{orderNo,jdbcType=VARCHAR}
      and PayFlowNo = #{payFlowNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCPayMoneyDeal">
    insert into fcpaymoneydeal (OrderNo, PayFlowNo, Amount, 
      PayStatus, PaPayDeskFlowNo, PayType, 
      Operator, OperatorCom, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{payFlowNo,jdbcType=VARCHAR}, #{amount,jdbcType=DOUBLE}, 
      #{payStatus,jdbcType=VARCHAR}, #{paPayDeskFlowNo,jdbcType=VARCHAR}, #{payType,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCPayMoneyDeal">
    insert into fcpaymoneydeal
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        OrderNo,
      </if>
      <if test="payFlowNo != null">
        PayFlowNo,
      </if>
      <if test="amount != null">
        Amount,
      </if>
      <if test="payStatus != null">
        PayStatus,
      </if>
      <if test="paPayDeskFlowNo != null">
        PaPayDeskFlowNo,
      </if>
      <if test="payType != null">
        PayType,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payFlowNo != null">
        #{payFlowNo,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DOUBLE},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=VARCHAR},
      </if>
      <if test="paPayDeskFlowNo != null">
        #{paPayDeskFlowNo,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCPayMoneyDeal">
    update fcpaymoneydeal
    <set>
      <if test="amount != null">
        Amount = #{amount,jdbcType=DOUBLE},
      </if>
      <if test="payStatus != null">
        PayStatus = #{payStatus,jdbcType=VARCHAR},
      </if>
      <if test="paPayDeskFlowNo != null">
        PaPayDeskFlowNo = #{paPayDeskFlowNo,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        PayType = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where OrderNo = #{orderNo,jdbcType=VARCHAR}
      and PayFlowNo = #{payFlowNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPayMoneyDeal">
    update fcpaymoneydeal
    set Amount = #{amount,jdbcType=DOUBLE},
      PayStatus = #{payStatus,jdbcType=VARCHAR},
      PaPayDeskFlowNo = #{paPayDeskFlowNo,jdbcType=VARCHAR},
      PayType = #{payType,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where OrderNo = #{orderNo,jdbcType=VARCHAR}
      and PayFlowNo = #{payFlowNo,jdbcType=VARCHAR}
  </update>
</mapper>