<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCEdorReduInsuredMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCEdorReduInsured">
    <id column="DecInsuredSn" jdbcType="VARCHAR" property="decInsuredSn" />
    <result column="GrpContNo" jdbcType="VARCHAR" property="grpContNo" />
    <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
    <result column="Batch" jdbcType="VARCHAR" property="batch" />
    <result column="Name" jdbcType="VARCHAR" property="name" />
    <result column="Sex" jdbcType="VARCHAR" property="sex" />
    <result column="BirthDay" jdbcType="VARCHAR" property="birthDay" />
    <result column="IdNo" jdbcType="VARCHAR" property="idNo" />
    <result column="IdType" jdbcType="VARCHAR" property="idType" />
    <result column="ZtaliDate" jdbcType="VARCHAR" property="ztaliDate" />
    <result column="RefundInstruct" jdbcType="VARCHAR" property="refundInstruct" />
    <result column="EdorType" jdbcType="VARCHAR" property="edorType" />
    <result column="TrialStatus" jdbcType="VARCHAR" property="trialStatus" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="VARCHAR" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="VARCHAR" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    DecInsuredSn, GrpContNo, GrpNo, Batch, Name, Sex, BirthDay, IdNo, IdType, ZtaliDate, 
    RefundInstruct, EdorType, TrialStatus, Operator, OperatorCom, MakeDate, MakeTime, 
    ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcedorreduinsured
    where DecInsuredSn = #{decInsuredSn,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcedorreduinsured
    where DecInsuredSn = #{decInsuredSn,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCEdorReduInsured">
    insert into fcedorreduinsured (DecInsuredSn, GrpContNo, GrpNo, 
      Batch, Name, Sex, BirthDay, 
      IdNo, IdType, ZtaliDate, 
      RefundInstruct, EdorType, TrialStatus, 
      Operator, OperatorCom, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{decInsuredSn,jdbcType=VARCHAR}, #{grpContNo,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR}, 
      #{batch,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR}, #{birthDay,jdbcType=VARCHAR}, 
      #{idNo,jdbcType=VARCHAR}, #{idType,jdbcType=VARCHAR}, #{ztaliDate,jdbcType=VARCHAR}, 
      #{refundInstruct,jdbcType=VARCHAR}, #{edorType,jdbcType=VARCHAR}, #{trialStatus,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=VARCHAR}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=VARCHAR}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCEdorReduInsured">
    insert into fcedorreduinsured
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="decInsuredSn != null">
        DecInsuredSn,
      </if>
      <if test="grpContNo != null">
        GrpContNo,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
      <if test="batch != null">
        Batch,
      </if>
      <if test="name != null">
        Name,
      </if>
      <if test="sex != null">
        Sex,
      </if>
      <if test="birthDay != null">
        BirthDay,
      </if>
      <if test="idNo != null">
        IdNo,
      </if>
      <if test="idType != null">
        IdType,
      </if>
      <if test="ztaliDate != null">
        ZtaliDate,
      </if>
      <if test="refundInstruct != null">
        RefundInstruct,
      </if>
      <if test="edorType != null">
        EdorType,
      </if>
      <if test="trialStatus != null">
        TrialStatus,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="decInsuredSn != null">
        #{decInsuredSn,jdbcType=VARCHAR},
      </if>
      <if test="grpContNo != null">
        #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=VARCHAR},
      </if>
      <if test="birthDay != null">
        #{birthDay,jdbcType=VARCHAR},
      </if>
      <if test="idNo != null">
        #{idNo,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        #{idType,jdbcType=VARCHAR},
      </if>
      <if test="ztaliDate != null">
        #{ztaliDate,jdbcType=VARCHAR},
      </if>
      <if test="refundInstruct != null">
        #{refundInstruct,jdbcType=VARCHAR},
      </if>
      <if test="edorType != null">
        #{edorType,jdbcType=VARCHAR},
      </if>
      <if test="trialStatus != null">
        #{trialStatus,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=VARCHAR},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCEdorReduInsured">
    update fcedorreduinsured
    <set>
      <if test="grpContNo != null">
        GrpContNo = #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        Batch = #{batch,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        Name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        Sex = #{sex,jdbcType=VARCHAR},
      </if>
      <if test="birthDay != null">
        BirthDay = #{birthDay,jdbcType=VARCHAR},
      </if>
      <if test="idNo != null">
        IdNo = #{idNo,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        IdType = #{idType,jdbcType=VARCHAR},
      </if>
      <if test="ztaliDate != null">
        ZtaliDate = #{ztaliDate,jdbcType=VARCHAR},
      </if>
      <if test="refundInstruct != null">
        RefundInstruct = #{refundInstruct,jdbcType=VARCHAR},
      </if>
      <if test="edorType != null">
        EdorType = #{edorType,jdbcType=VARCHAR},
      </if>
      <if test="trialStatus != null">
        TrialStatus = #{trialStatus,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=VARCHAR},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where DecInsuredSn = #{decInsuredSn,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCEdorReduInsured">
    update fcedorreduinsured
    set GrpContNo = #{grpContNo,jdbcType=VARCHAR},
      GrpNo = #{grpNo,jdbcType=VARCHAR},
      Batch = #{batch,jdbcType=VARCHAR},
      Name = #{name,jdbcType=VARCHAR},
      Sex = #{sex,jdbcType=VARCHAR},
      BirthDay = #{birthDay,jdbcType=VARCHAR},
      IdNo = #{idNo,jdbcType=VARCHAR},
      IdType = #{idType,jdbcType=VARCHAR},
      ZtaliDate = #{ztaliDate,jdbcType=VARCHAR},
      RefundInstruct = #{refundInstruct,jdbcType=VARCHAR},
      EdorType = #{edorType,jdbcType=VARCHAR},
      TrialStatus = #{trialStatus,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=VARCHAR},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=VARCHAR},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where DecInsuredSn = #{decInsuredSn,jdbcType=VARCHAR}
  </update>

  <select id="getInsuredExist" parameterType="java.lang.Long" resultType="com.sinosoft.eflex.model.FCEdorReduInsured">
    select
    <include refid="Base_Column_List" />
    from fcedorreduinsured  where Batch=#{batch,jdbcType=VARCHAR} and TrialStatus="0"
  </select>

  <insert id="insertNext" parameterType="java.util.List">
    insert into fcedorreduinsured (DecInsuredSn, GrpContNo, GrpNo,
      Batch, Name, Sex, BirthDay,
      IdNo, IdType, ZtaliDate,
      RefundInstruct, EdorType, TrialStatus,
      Operator, OperatorCom, MakeDate,
      MakeTime, ModifyDate, ModifyTime
      )
    <foreach collection="list" item="item" index="index" separator="union all">
     select #{list[${index}].decInsuredSn},#{list[${index}].grpContNo},#{list[${index}].grpNo},#{list[${index}].batch},
      #{list[${index}].name},#{list[${index}].sex},#{list[${index}].birthDay},#{list[${index}].idNo},
      #{list[${index}].idType},#{list[${index}].ztaliDate},
      #{list[${index}].refundInstruct},#{list[${index}].edorType},#{list[${index}].trialStatus},#{list[${index}].operator},
      #{list[${index}].operatorCom},#{list[${index}].makeDate},#{list[${index}].makeTime},#{list[${index}].modifyDate},#{list[${index}].modifyTime}
      from dual
    </foreach>
  </insert>

  <select id="getDecInsuredInfo" resultType="com.sinosoft.eflex.model.FCEdorReduInsured">
    select
    <include refid="Base_Column_List" />
    from fcedorreduinsured
    where Batch = #{decBatch,jdbcType=VARCHAR} and  GrpNo = #{grpNo,jdbcType=VARCHAR} ORDER BY  DecInsuredSn
  </select>

  <delete id="deleteByBacth"  parameterType="java.util.Map">
    delete from fcedorreduinsured
    where GrpContNo = #{grpContNo} AND GrpNo = #{grpNo} AND batch = #{decBatch}
  </delete>

  <select id="selectByReduInsuredBatch" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCEdorReduInsured">
    select
    <include refid="Base_Column_List" />
    from fcedorreduinsured
    where Batch = #{reduInsuredBatch,jdbcType=VARCHAR}
  </select>
    <select id="selectEdorReduInsured" parameterType="com.sinosoft.eflex.model.edor.SelectEdorReduInsuredReq"
            resultType="com.sinosoft.eflex.model.FCEdorReduInsured">
        select
        a.DecInsuredSn,
        a.GrpContNo,
        a.GrpNo,
        a.Batch,
        a.Name,
        a.Sex,
        a.BirthDay,
        a.IdNo,
        a.IdType,
        a.ZtaliDate,
        a.RefundInstruct,
        a.EdorType,
        a.TrialStatus,
        a.Operator,
        a.OperatorCom,
        a.MakeDate,
        a.MakeTime,
        a.ModifyDate,
        a.ModifyTime
        from fcedorreduinsured a
        where Batch = #{reduInsuredBatch,jdbcType=VARCHAR}
        <if test="name != null and name != '' ">
            and a.Name like concat('%',#{name},'%')
        </if>
        <if test="idType != null and idType != '' ">
            and a.idType = #{idType}
        </if>
        <if test="idNo != null and idNo != '' ">
            and a.idNo = #{idNo}
        </if>
    </select>

</mapper>