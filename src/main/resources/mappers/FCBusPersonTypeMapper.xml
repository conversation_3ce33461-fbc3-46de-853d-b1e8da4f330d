<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCBusPersonTypeMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCBusPersonType">
        <id column="EnsureCode" jdbcType="VARCHAR" property="ensureCode"/>
        <id column="GrpNo" jdbcType="VARCHAR" property="grpNo"/>
        <id column="GradeLevelCode" jdbcType="VARCHAR" property="gradeLevelCode"/>
        <result column="GradeDesc" jdbcType="VARCHAR" property="gradeDesc"/>
        <result column="OrderNum" jdbcType="VARCHAR" property="orderNum"/>
        <result column="PlanType" jdbcType="VARCHAR" property="planType"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    EnsureCode, GrpNo, GradeLevelCode, GradeDesc, OrderNum, PlanType, Operator, OperatorCom, MakeDate,
    MakeTime, ModifyDate, ModifyTime
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcbuspersontype
        where 1=1
        <if test=" gradeLevelCode != null and gradeLevelCode != ''">
            and GradeLevelCode = #{gradeLevelCode,jdbcType=VARCHAR}
        </if>
        <if test=" orderNum != null and orderNum != ''">
            and OrderNum = #{orderNum,jdbcType=VARCHAR}
        </if>
        <if test=" ensureCode != null and ensureCode != ''">
            and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        </if>
        <if test=" grpNo != null and grpNo != ''">
            and GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectByEnsurecode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcbuspersontype
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        order by OrderNum ASC
    </select>
    <select id="selectByGrpNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcbuspersontype
        where GrpNo = #{grpNo}
        order by OrderNum asc
    </select>
    <select id="selectAllLevelCodeByGrpNo" resultType="java.lang.String">
      select GradeLevelCode from fcbuspersontype where GrpNo = #{grpNo}
    </select>
    <select id="selectOrderNum" resultType="java.lang.String">
        select OrderNum from fcbuspersontype where GrpNo = #{grpNo}
        <if test="levelCode != null and levelCode != ''">
            and GradeLevelCode = #{levelCode}
        </if>
    </select>
    <select id="selectAllRanks" resultType="java.util.Map">
        select GradeLevelCode codeName,
               OrderNum codeKey
        from fcbuspersontype where GrpNo = #{grpNo}
        order by codeKey
    </select>
    <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcbuspersontype
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and GradeLevelCode = #{gradeLevelCode,jdbcType=VARCHAR}
  </delete>

    <delete id="deleteByEnsureCode" parameterType="java.lang.String">
    delete from fcbuspersontype
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
  </delete>
    <delete id="deleteByGrpNoAndOrderNum">
    delete from fcbuspersontype
    where GrpNo = #{grpNo} and EnsureCode = #{ensureCode} and OrderNum = #{orderNum}
  </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCBusPersonType">
    insert into fcbuspersontype (EnsureCode, GrpNo, GradeLevelCode, 
      GradeDesc, OrderNum, Operator, 
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{ensureCode,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR}, #{gradeLevelCode,jdbcType=VARCHAR}, 
      #{gradeDesc,jdbcType=VARCHAR}, #{orderNum,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCBusPersonType">
        insert into fcbuspersontype
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                EnsureCode,
            </if>
            <if test="grpNo != null">
                GrpNo,
            </if>
            <if test="gradeLevelCode != null">
                GradeLevelCode,
            </if>
            <if test="gradeDesc != null">
                GradeDesc,
            </if>
            <if test="orderNum != null">
                OrderNum,
            </if>
            <if test="planType != null">
                PlanType,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="gradeLevelCode != null">
                #{gradeLevelCode,jdbcType=VARCHAR},
            </if>
            <if test="gradeDesc != null">
                #{gradeDesc,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null">
                #{orderNum,jdbcType=VARCHAR},
            </if>
            <if test="planType != null">
                #{planType,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCBusPersonType">
        update fcbuspersontype
        <set>
            <if test="gradeLevelCode != null">
                GradeLevelCode = #{gradeLevelCode,jdbcType=VARCHAR},
            </if>
            <if test="gradeDesc != null">
                GradeDesc = #{gradeDesc,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null">
                OrderNum = #{orderNum,jdbcType=VARCHAR},
            </if>
            <if test="planType != null">
                PlanType = #{planType,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and GrpNo = #{grpNo,jdbcType=VARCHAR}
        and GradeLevelCode = #{gradeLevelCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCBusPersonType">
    update fcbuspersontype
    set GradeDesc = #{gradeDesc,jdbcType=VARCHAR},
      OrderNum = #{orderNum,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and GrpNo = #{grpNo,jdbcType=VARCHAR}
      and GradeLevelCode = #{gradeLevelCode,jdbcType=VARCHAR}
  </update>
    <update id="updateByParams" parameterType="java.util.Map">
        update fcbuspersontype
        <set>
            <if test="gradeLevelCode != null">
                GradeLevelCode = #{gradeLevelCode,jdbcType=VARCHAR},
            </if>
            <if test="gradeDesc != null">
                GradeDesc = #{gradeDesc,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null">
                OrderNum = #{orderNum,jdbcType=VARCHAR},
            </if>
            <if test="planType != null">
                PlanType = #{planType,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR}
            </if>
        </set>
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and GrpNo = #{grpNo,jdbcType=VARCHAR}
        and GradeLevelCode = #{oldGradeLevelCode,jdbcType=VARCHAR}
    </update>

    <insert id="insertFCBusPersonTypeList" parameterType="com.sinosoft.eflex.model.FCBusPersonType">
        INSERT FCBusPersonType
        (EnsureCode,GrpNo,GradeLevelCode,GradeDesc,OrderNum,Operator,ManageCom,MakeDate,MakeTime,ModifyDate,ModifyTime)
        VALUES
        <foreach collection="list" item="list" index="index" separator=",">
            (#{list.[${index}].ensureCode,jdbcType=VARCHAR},
            #{list.[${index}].grpNo,jdbcType=VARCHAR},
            #{list.[${index}].gradeLevelCode,jdbcType=VARCHAR},
            #{list.[${index}].gradeDesc,jdbcType=VARCHAR},
            #{list.[${index}].orderNum,jdbcType=VARCHAR},
            #{list.[${index}].operator,jdbcType=VARCHAR},
            #{list.[${index}].manageCom,jdbcType=VARCHAR},
            (select D FROM (SELECT CURDATE() as D FROM fdmenu LIMIT 1) person) as makeDate,
            (select T FROM (SELECT CURTIME() as T FROM fdmenu LIMIT 1) person) as makeTime,
            (select D FROM (SELECT CURDATE() as D FROM fdmenu LIMIT 1) person) as modifyDate,
            (select T FROM (SELECT CURTIME() as T FROM fdmenu LIMIT 1) person) as modifyTime)
        </foreach>
    </insert>
    <select id="selectLevelNameByGrpNo" resultType="java.lang.String">
        select GradeLevelCode from fcbuspersontype where GrpNo = #{grpNo}
        <if test="levelCode != null and levelCode != ''">
            and GradeLevelCode = #{levelCode}
        </if>
    </select>
</mapper>