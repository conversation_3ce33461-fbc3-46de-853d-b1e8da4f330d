<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCPerinfoFamilyTempMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCPerinfoFamilyTemp">
        <id column="FamilyTempNo" jdbcType="VARCHAR" property="familyTempNo"/>
        <result column="PerTempNo" jdbcType="VARCHAR" property="perTempNo"/>
        <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sex" jdbcType="VARCHAR" property="sex"/>
        <result column="IDType" jdbcType="VARCHAR" property="IDType"/>
        <result column="IDNo" jdbcType="VARCHAR" property="IDNo"/>
        <result column="idTypeEndDate" jdbcType="DATE" property="idTypeEndDate"/>
        <result column="BirthDay" jdbcType="VARCHAR" property="birthDay"/>
        <result column="Phone" jdbcType="VARCHAR" property="phone"/>
        <result column="OccupationType" jdbcType="VARCHAR" property="occupationType"/>
        <result column="OccupationCode" jdbcType="VARCHAR" property="occupationCode"/>
        <result column="JoinMedProtect" jdbcType="VARCHAR" property="joinMedProtect"/>
        <result column="NativePlace" jdbcType="VARCHAR" property="nativeplace"/>
        <result column="Relation" jdbcType="VARCHAR" property="relation"/>
        <result column="PerName" jdbcType="VARCHAR" property="perName"/>
        <result column="PerIDType" jdbcType="VARCHAR" property="perIDType"/>
        <result column="PerIDNo" jdbcType="VARCHAR" property="perIDNo"/>
        <result column="SubStaus" jdbcType="VARCHAR" property="subStaus"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="MakeDate" jdbcType="VARCHAR" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="VARCHAR" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        FamilyTempNo
        ,PerTempNo, EnsureCode, Name, Sex, IDType, IDNo,idTypeEndDate, BirthDay, Phone, OccupationType,
    OccupationCode, JoinMedProtect, NativePlace,Relation, PerName, PerIDType, PerIDNo, SubStaus,
    OperatorCom, Operator, MakeDate, MakeTime, ModifyDate, ModifyTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperinfofamilytemp
        where FamilyTempNo = #{familyTempNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.util.HashMap">
        delete from fcperinfofamilytemp
        where 1 = 1
        <if test="familyTempNo != null">
            AND FamilyTempNo = #{familyTempNo,jdbcType=VARCHAR}
        </if>
        <if test="perTempNo != null">
            AND PerTempNo = #{perTempNo,jdbcType = VARCHAR}
        </if>
        <if test="ensureCode != null">
            AND EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        </if>
        <if test="perIDType != null">
            AND PerIDType = #{perIDType,jdbcType=VARCHAR}
        </if>
        <if test="perIDNo != null">
            AND PerIDNo = #{perIDNo,jdbcType=VARCHAR}
        </if>
    </delete>
    <delete id="deleteByPerTempNo">
        delete
        from fcperinfofamilytemp
        where PerTempNo = #{perTempNo}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCPerinfoFamilyTemp">
        insert into fcperinfofamilytemp (FamilyTempNo, PerTempNo, EnsureCode, Name,
                                         Sex, IDType, IDNo, idTypeEndDate,
                                         BirthDay, Phone, OccupationType,
                                         OccupationCode, JoinMedProtect, NativePlace, Relation,
                                         PerName, PerIDType, PerIDNo,
                                         SubStaus, OperatorCom, Operator,
                                         MakeDate, MakeTime, ModifyDate,
                                         ModifyTime)
        values (#{familyTempNo,jdbcType=VARCHAR}, #{perTempNo,jdbcType=VARCHAR}, #{ensureCode,jdbcType=VARCHAR},
                #{name,jdbcType=VARCHAR},
                #{sex,jdbcType=VARCHAR}, #{IDType,jdbcType=VARCHAR}, #{IDNo,jdbcType=VARCHAR},
                #{idTypeEndDate,jdbcType=DATE},
                #{birthDay,jdbcType=DATE}, #{phone,jdbcType=VARCHAR}, #{occupationType,jdbcType=VARCHAR},
                #{occupationCode,jdbcType=VARCHAR}, #{joinMedProtect,jdbcType=VARCHAR}, #{nativeplace,jdbcType=VARCHAR},
                #{relation,jdbcType=VARCHAR},
                #{perName,jdbcType=VARCHAR}, #{perIDType,jdbcType=VARCHAR}, #{perIDNo,jdbcType=VARCHAR},
                #{subStaus,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
                #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
                #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCPerinfoFamilyTemp">
        insert into fcperinfofamilytemp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="familyTempNo != null">
                FamilyTempNo,
            </if>
            <if test="perTempNo != null">
                PerTempNo,
            </if>
            <if test="ensureCode != null">
                EnsureCode,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="sex != null">
                Sex,
            </if>
            <if test="IDType != null">
                IDType,
            </if>
            <if test="IDNo != null">
                IDNo,
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate,
            </if>
            <if test="birthDay != null">
                BirthDay,
            </if>
            <if test="phone != null">
                Phone,
            </if>
            <if test="occupationType != null">
                OccupationType,
            </if>
            <if test="occupationCode != null">
                OccupationCode,
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect,
            </if>
            <if test="nativeplace != null">
                nativeplace,
            </if>
            <if test="relation != null">
                Relation,
            </if>
            <if test="perName != null">
                PerName,
            </if>
            <if test="perIDType != null">
                PerIDType,
            </if>
            <if test="perIDNo != null">
                PerIDNo,
            </if>
            <if test="subStaus != null">
                SubStaus,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="familyTempNo != null">
                #{familyTempNo,jdbcType=VARCHAR},
            </if>
            <if test="perTempNo != null">
                #{perTempNo,jdbcType=VARCHAR},
            </if>
            <if test="ensureCode != null">
                #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="IDType != null">
                #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="birthDay != null">
                #{birthDay,jdbcType=DATE},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="relation != null">
                #{relation,jdbcType=VARCHAR},
            </if>
            <if test="perName != null">
                #{perName,jdbcType=VARCHAR},
            </if>
            <if test="perIDType != null">
                #{perIDType,jdbcType=VARCHAR},
            </if>
            <if test="perIDNo != null">
                #{perIDNo,jdbcType=VARCHAR},
            </if>
            <if test="subStaus != null">
                #{subStaus,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCPerinfoFamilyTemp">
        update fcperinfofamilytemp
        <set>
            <if test="perTempNo != null and perTempNo != '' ">
                PerTempNo = #{oldPerTempNo,jdbcType=VARCHAR},
            </if>
            <if test="ensureCode != null">
                EnsureCode = #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != '' ">
                idTypeEndDate =#{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="birthDay != null">
                BirthDay = #{birthDay,jdbcType=DATE},
            </if>
            <if test="phone != null">
                Phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                OccupationType = #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                Nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="relation != null">
                Relation = #{relation,jdbcType=VARCHAR},
            </if>
            <if test="perName != null">
                PerName = #{perName,jdbcType=VARCHAR},
            </if>
            <if test="perIDType != null">
                PerIDType = #{perIDType,jdbcType=VARCHAR},
            </if>
            <if test="perIDNo != null">
                PerIDNo = #{perIDNo,jdbcType=VARCHAR},
            </if>
            <if test="subStaus != null">
                SubStaus = #{subStaus,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where 1 = 1
        <if test="familyTempNo != null and familyTempNo != '' ">
            AND FamilyTempNo = #{familyTempNo,jdbcType=VARCHAR}
        </if>
        <if test="ensureCode != null and ensureCode != '' ">
            AND EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        </if>
        <if test="perTempNo != null and perTempNo != '' ">
            AND PerTempNo = #{perTempNo,jdbcType=VARCHAR}
        </if>
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPerinfoFamilyTemp">
        update fcperinfofamilytemp
        set EnsureCode     = #{ensureCode,jdbcType=VARCHAR},
            Name           = #{name,jdbcType=VARCHAR},
            Sex            = #{sex,jdbcType=VARCHAR},
            IDType         = #{IDType,jdbcType=VARCHAR},
            IDNo           = #{IDNo,jdbcType=VARCHAR},
            idTypeEndDate  = #{idTypeEndDate,jdbcType=VARCHAR},
            BirthDay       = #{birthDay,jdbcType=DATE},
            Phone          = #{phone,jdbcType=VARCHAR},
            OccupationType = #{occupationType,jdbcType=VARCHAR},
            OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            Nativeplace    = #{nativeplace,jdbcType=VARCHAR},
            Relation       = #{relation,jdbcType=VARCHAR},
            PerName        = #{perName,jdbcType=VARCHAR},
            PerIDType      = #{perIDType,jdbcType=VARCHAR},
            PerIDNo        = #{perIDNo,jdbcType=VARCHAR},
            SubStaus       = #{subStaus,jdbcType=VARCHAR},
            OperatorCom    = #{operatorCom,jdbcType=VARCHAR},
            Operator       = #{operator,jdbcType=VARCHAR},
            MakeDate       = #{makeDate,jdbcType=DATE},
            MakeTime       = #{makeTime,jdbcType=VARCHAR},
            ModifyDate     = #{modifyDate,jdbcType=DATE},
            ModifyTime     = #{modifyTime,jdbcType=VARCHAR}
        where FamilyTempNo = #{familyTempNo,jdbcType=VARCHAR}
    </update>

    <insert id="insertList" parameterType="com.sinosoft.eflex.model.FCPerinfoFamilyTemp">
        INSERT INTO fcperinfofamilytemp
        (FamilyTempNo,PerTempNo,EnsureCode,Name,Sex,IDType,IDNo,idTypeEndDate,BirthDay,Phone,OccupationType,OccupationCode,JoinMedProtect,NativePlace,Relation,PerName,PerIDType,PerIDNo,SubStaus,MakeDate,
        MakeTime,ModifyDate, ModifyTime)
        <foreach collection="list" item="item" index="index" separator="union all">
            SELECT #{list[${index}].familyTempNo,jdbcType=VARCHAR},
            #{list[${index}].perTempNo,jdbcType=VARCHAR },
            #{list[${index}].ensureCode,jdbcType=VARCHAR},
            #{list[${index}].name,jdbcType=VARCHAR},
            #{list[${index}].sex,jdbcType=VARCHAR},
            #{list[${index}].IDType,jdbcType=VARCHAR},
            #{list[${index}].IDNo,jdbcType=VARCHAR},
            #{list[${index}].idTypeEndDate,jdbcType=DATE},
            #{list[${index}].birthDay,jdbcType=VARCHAR},
            #{list[${index}].phone,jdbcType=VARCHAR},
            #{list[${index}].occupationType,jdbcType=VARCHAR},
            #{list[${index}].occupationCode,jdbcType=VARCHAR},
            #{list[${index}].joinMedProtect,jdbcType=VARCHAR},
            #{list[${index}].nativeplace,jdbcType=VARCHAR},
            #{list[${index}].relation,jdbcType=VARCHAR},
            #{list[${index}].perName,jdbcType=VARCHAR},
            #{list[${index}].perIDType,jdbcType=VARCHAR},
            #{list[${index}].perIDNo,jdbcType=VARCHAR},
            #{list[${index}].subStaus,jdbcType=VARCHAR},
            #{list[${index}].makeDate,jdbcType=DATE},
            #{list[${index}].makeTime,jdbcType=VARCHAR},
            #{list[${index}].modifyDate,jdbcType=DATE},
            #{list[${index}].modifyTime,jdbcType=VARCHAR}
            from DUAL
        </foreach>
    </insert>

    <select id="getFamilyByEnsureCode" parameterType="java.util.Map"
            resultType="com.sinosoft.eflex.model.FCPerinfoFamilyTemp">
        SELECT *,b.codeName nativeplaceName FROM fcperinfofamilytemp a
        left join fdcode b on a.nativeplace = b.codeKey and b.codeType = 'nativeplace'
        WHERE a.ensurecode = #{ensureCode}
        <if test="subStaus != null and subStaus != '' ">
            and a.SubStaus = #{subStaus}
        </if>
        <if test="name != null and name != '' ">
            and a.PerName LIKE CONCAT('%',#{name},'%')
        </if>
        <if test="IDType != null and IDType != '' ">
            and a.PerIDType = #{IDType}
        </if>
        <if test="IDNo != null and IDNo != '' ">
            and a.PerIDNo = #{IDNo}
        </if>
    </select>

    <select id="getFamilyCountByEnsureCode" parameterType="com.sinosoft.eflex.model.FCPerinfoFamilyTemp"
            resultType="java.lang.Integer">
        SELECT count(*) FROM fcperinfofamilytemp WHERE 1 = 1
        <if test="familyTempNo != null">
            AND FamilyTempNo != #{familyTempNo}
        </if>
        <if test="ensureCode != null">
            AND ensurecode = #{ensureCode}
        </if>
        <if test="IDType != null ">
            AND IDType = #{IDType}
        </if>
        <if test="IDNo != null">
            AND IDNo = #{IDNo}
        </if>
        <if test="perTempNo != null">
            AND PerTempNo = #{perTempNo}
        </if>
    </select>

    <update id="updateSubStaus" parameterType="java.util.Map">
        update fcperinfofamilytemp
        set SubStaus  ='02',
            ModifyDate=str_to_date(#{params.currentDate,jdbcType=VARCHAR}, '%Y-%m-%d'),
            ModifyTime=#{params.currentTime,jdbcType=VARCHAR}
        where EnsureCode = #{params.ensureCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByIdNo" parameterType="java.util.Map">
        update fcPerinfoFamilyTemp
        set Phone=#{mobilePhone}
        where IDNo = #{idNo}
    </update>

    <select id="queryStudent" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT f. FamilyTempNo AS familyTempNo,f.PerTempNo AS perTempNo, f.name AS `name`,f.sex AS
        sex,DATE_FORMAT(f.BirthDay, '%Y-%m-%d') AS birthDay,
        f.Nativeplace AS nativeplace,f.IDType AS iDType,f.IDNo AS idNo,DATE_FORMAT(f.idTypeEndDate, '%Y-%m-%d') AS
        idTypeEndDate,p.DefaultPlan AS planCode,
        f.OccupationType AS occupationType,f.OccupationCode AS occupationCode,f.JoinMedProtect AS
        joinMedProtect,f.Relation AS relation,p.StudentGrpPrem AS studentGrpPrem,
        p.Name AS `garName`,p.Sex AS garSex,DATE_FORMAT(p.BirthDay, '%Y-%m-%d') AS garBirthDay,p.Nativeplace AS
        garNativeplace,p.IDType AS garIDType,p.IDNo AS garIDNo,
        DATE_FORMAT(p.idTypeEndDate, '%Y-%m-%d') AS garIdTypeEndDate,p.MobilePhone AS garMobilePhone,p.openbank AS
        garOpenbank,p.OpenAccount AS garOpenAccount,
        (select TotalPrem from fcensureplan where PlanCode = p.DefaultPlan and EnsureCode=#{ensureCode}) as totalPrem
        FROM fcperinfofamilytemp f
        INNER JOIN fcperinfotemp p ON p.PerTempNo = f.PerTempNo
        WHERE f.EnsureCode = #{ensureCode}
        <if test="defaultPlan != null and defaultPlan != '' ">
            and p.DefaultPlan = #{defaultPlan}
        </if>
        <if test="grpNo != null and grpNo != '' ">
            and p.GrpNo = #{grpNo}
        </if>
        <if test="name != null and name != '' ">
            and f.Name LIKE concat('%',#{name},'%')
        </if>
        <if test="sex != null and sex != '' ">
            and f.Sex = #{sex}
        </if>
        <if test="IDType != null and IDType != '' ">
            and f.IDType = #{IDType}
        </if>
        <if test="IDNo != null and IDNo != '' ">
            and f.IDNo = #{IDNo}
        </if>
        <if test="nativeplace != null and nativeplace != '' ">
            and f.nativeplace = #{nativeplace}
        </if>
    </select>

    <select id="getPlanByEnsureCode" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT f.`name` AS perName, p.DefaultPlan AS defaultPlan
        FROM fcperinfofamilytemp f
                 INNER JOIN fcperinfotemp p ON f.PerTempNo = p.PerTempNo
        WHERE f.EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          AND p.DefaultPlan IS NOT NULL
          AND p.DefaultPlan != ''
    </select>


    <select id="selectByFamilyTemp" parameterType="com.sinosoft.eflex.model.FCPerinfoFamilyTemp"
            resultType="com.sinosoft.eflex.model.FCPerinfoFamilyTemp">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fcperinfofamilytemp
        WHERE EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        <if test="phone != null">
            and Phone = #{phone,jdbcType=VARCHAR}
        </if>
    </select>


    <select id="getPerInfoByEnsureCode" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT f.name as name,f.sex as sex,date_format(f.BirthDay,'%Y-%m-%d')as birthDay,f.IDType as idType,
        f.IDNo as idNo,date_format(f.idTypeEndDate,'%Y-%m-%d') as idTypeEndDate,f.Nativeplace as nativePlace,
        f.Phone as phone,f.OccupationType as occupationType,f.OccupationCode as occupationCode,
        f.JoinMedProtect as JoinMedProtect,f.Relation as relation,p.IDType as perIDType,p.IDNo as perIDNo
        FROM FCPerinfoFamilyTemp f
        INNER JOIN fcperinfotemp p ON f.PerTempNo = p.PerTempNo
        WHERE f.EnsureCode = #{ensureCode}
        <if test=" name != null and name != '' ">
            and p.Name LIKE CONCAT(CONCAT('%',#{name},'%'))
        </if>
        <if test=" sex != null and sex != '' ">
            and p.Sex = #{sex}
        </if>
        <if test=" iDType != null and iDType != '' ">
            and p.IDType = #{iDType}
        </if>
        <if test=" iDNo != null and iDNo != '' ">
            and p.IDNo = #{iDNo}
        </if>
    </select>
    <select id="selectByIdNo" resultType="com.sinosoft.eflex.model.FCPerinfoFamilyTemp">
        select *
        from fcPerinfoFamilyTemp
        where IDNo = #{idNo}
    </select>
    <select id="selectByIdNoAndEnsureCode" resultType="com.sinosoft.eflex.model.FCPerinfoFamilyTemp">
        select *
        from fcPerinfoFamilyTemp
        where IDNo = #{idNo}
          and EnsureCode = #{ensureCode}
    </select>
    <select id="selectByEnsureCode" resultType="com.sinosoft.eflex.model.FCPerinfoFamilyTemp">
        select *
        from fcPerinfoFamilyTemp
        where  EnsureCode = #{ensureCode} order by  FamilyTempNo
    </select>
    <update id="updateBatchById">
        <foreach collection="list" item="item" separator=";">
            update fcperinfofamilytemp
            <set>
                <if test="item.perTempNo != null and perTempNo != '' ">
                    PerTempNo = #{item.oldPerTempNo,jdbcType=VARCHAR},
                </if>
                <if test="item.ensureCode != null">
                    EnsureCode = #{item.ensureCode,jdbcType=VARCHAR},
                </if>
                <if test="item.name != null">
                    Name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.sex != null">
                    Sex = #{item.sex,jdbcType=VARCHAR},
                </if>
                <if test="item.IDType != null">
                    IDType = #{item.IDType,jdbcType=VARCHAR},
                </if>
                <if test="item.IDNo != null">
                    IDNo = #{item.IDNo,jdbcType=VARCHAR},
                </if>
                <if test="item.idTypeEndDate != '' ">
                    idTypeEndDate =#{item.idTypeEndDate,jdbcType=DATE},
                </if>
                <if test="item.birthDay != null">
                    BirthDay = #{item.birthDay,jdbcType=DATE},
                </if>
                <if test="item.phone != null">
                    Phone = #{item.phone,jdbcType=VARCHAR},
                </if>
                <if test="item.occupationType != null">
                    OccupationType = #{item.occupationType,jdbcType=VARCHAR},
                </if>
                <if test="item.occupationCode != null">
                    OccupationCode = #{item.occupationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.joinMedProtect != null">
                    JoinMedProtect = #{item.joinMedProtect,jdbcType=VARCHAR},
                </if>
                <if test="item.nativeplace != null">
                    Nativeplace = #{item.nativeplace,jdbcType=VARCHAR},
                </if>
                <if test="item.relation != null">
                    Relation = #{item.relation,jdbcType=VARCHAR},
                </if>
                <if test="item.perName != null">
                    PerName = #{item.perName,jdbcType=VARCHAR},
                </if>
                <if test="item.perIDType != null">
                    PerIDType = #{item.perIDType,jdbcType=VARCHAR},
                </if>
                <if test="item.perIDNo != null">
                    PerIDNo = #{item.perIDNo,jdbcType=VARCHAR},
                </if>
                <if test="item.subStaus != null">
                    SubStaus = #{item.subStaus,jdbcType=VARCHAR},
                </if>
                <if test="item.operatorCom != null">
                    OperatorCom = #{item.operatorCom,jdbcType=VARCHAR},
                </if>
                <if test="item.operator != null">
                    Operator = #{item.operator,jdbcType=VARCHAR},
                </if>
                <if test="item.makeDate != null">
                    MakeDate = #{item.makeDate,jdbcType=DATE},
                </if>
                <if test="item.makeTime != null">
                    MakeTime = #{item.makeTime,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyDate != null">
                    ModifyDate = #{item.modifyDate,jdbcType=DATE},
                </if>
                <if test="item.modifyTime != null">
                    ModifyTime = #{item.modifyTime,jdbcType=VARCHAR},
                </if>
            </set>
            where
            <choose>
                <when test="item.edorType == 'IC' and item.edorType != null and item.edorType != ''">
                    IDNo = #{item.oldIdNo}
                </when>
                <otherwise>
                    IDNo = #{item.IDNo}
                </otherwise>
            </choose>

        </foreach>
    </update>
</mapper>
