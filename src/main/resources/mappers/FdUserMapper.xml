<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FdUserMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FdUser">
    <id column="UserNo" jdbcType="VARCHAR" property="userNo" />
    <result column="UserName" jdbcType="VARCHAR" property="userName" />
    <result column="NickName" jdbcType="VARCHAR" property="nickName" />
    <result column="PassWord" jdbcType="VARCHAR" property="passWord" />
    <result column="CustomType" jdbcType="VARCHAR" property="customType" />
    <result column="CustomNo" jdbcType="VARCHAR" property="customNo" />
    <result column="IsLock" jdbcType="VARCHAR" property="isLock" />
    <result column="IsVIP" jdbcType="VARCHAR" property="isVIP" />
    <result column="LoginFailTimes" jdbcType="INTEGER" property="loginFailTimes" />
    <result column="UserState" jdbcType="VARCHAR" property="userState" />
    <result column="Source" jdbcType="VARCHAR" property="source" />
    <result column="Email" jdbcType="VARCHAR" property="email" />
    <result column="Phone" jdbcType="VARCHAR" property="phone" />
    <result column="IDNo" jdbcType="VARCHAR" property="IDNo" />
    <result column="OpenID" jdbcType="VARCHAR" property="openID" />
      <result column="ManageCom" jdbcType="VARCHAR" property="manageCom"/>
    <result column="Remark" jdbcType="VARCHAR" property="remark" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
    <result column="PWDState" jdbcType="VARCHAR" property="PWDState" />
    <result column="LockDate" jdbcType="DATE" property="lockDate" />
    <result column="LockTime" jdbcType="VARCHAR" property="lockTime" />
      <result column="PWDChangeDate" jdbcType="DATE" property="pWDChangeDate"/>
  </resultMap>
  <sql id="Base_Column_List">
    UserNo, UserName, NickName, PassWord, CustomType, CustomNo, IsLock, IsVIP, LoginFailTimes,
    UserState, Source, Email, Phone, IDNo, OpenID, ManageCom,Remark, OperatorCom, Operator, MakeDate,
    MakeTime, ModifyDate, ModifyTime, PWDState, LockDate, LockTime,IsNeedCaptcha,PWDChangeDate
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fduser
    where UserNo = #{userNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fduser
    where UserNo = #{userNo}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FdUser">
    insert into fduser (UserNo, UserName, NickName,
      PassWord, CustomType, CustomNo,
      IsLock, IsVIP, LoginFailTimes,
      UserState, Source, Email,
      Phone, IDNo, OpenID,
      ManageCom,
      Remark, OperatorCom, Operator,
      MakeDate, MakeTime, ModifyDate,
      ModifyTime, PWDState, LockDate,
      LockTime)
    values (#{userNo,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{nickName,jdbcType=VARCHAR},
      #{passWord,jdbcType=VARCHAR}, #{customType,jdbcType=VARCHAR}, #{customNo,jdbcType=VARCHAR},
      #{isLock,jdbcType=VARCHAR}, #{isVIP,jdbcType=VARCHAR}, #{loginFailTimes,jdbcType=INTEGER},
      #{userState,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR},
      #{phone,jdbcType=VARCHAR}, #{IDNo,jdbcType=VARCHAR}, #{openID,jdbcType=VARCHAR},
      #{manageCom,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
      #{modifyTime,jdbcType=VARCHAR}, #{PWDState,jdbcType=VARCHAR}, #{lockDate,jdbcType=DATE},
      #{lockTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FdUser">
    insert into fduser
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userNo != null">
        UserNo,
      </if>
      <if test="userName != null">
        UserName,
      </if>
      <if test="nickName != null">
        NickName,
      </if>
      <if test="passWord != null">
        PassWord,
      </if>
      <if test="customType != null">
        CustomType,
      </if>
      <if test="customNo != null">
        CustomNo,
      </if>
      <if test="isLock != null">
        IsLock,
      </if>
      <if test="isVIP != null">
        IsVIP,
      </if>
      <if test="loginFailTimes != null">
        LoginFailTimes,
      </if>
      <if test="userState != null">
        UserState,
      </if>
      <if test="source != null">
        Source,
      </if>
      <if test="email != null">
        Email,
      </if>
      <if test="phone != null">
        Phone,
      </if>
      <if test="IDNo != null">
        IDNo,
      </if>
      <if test="openID != null">
        OpenID,
      </if>
        <if test="manageCom != null">
            ManageCom,
        </if>
      <if test="remark != null">
        Remark,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
      <if test="PWDState != null">
        PWDState,
      </if>
      <if test="lockDate != null">
        LockDate,
      </if>
      <if test="lockTime != null">
        LockTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userNo != null">
        #{userNo,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="passWord != null">
        #{passWord,jdbcType=VARCHAR},
      </if>
      <if test="customType != null">
        #{customType,jdbcType=VARCHAR},
      </if>
      <if test="customNo != null">
        #{customNo,jdbcType=VARCHAR},
      </if>
      <if test="isLock != null">
        #{isLock,jdbcType=VARCHAR},
      </if>
      <if test="isVIP != null">
        #{isVIP,jdbcType=VARCHAR},
      </if>
      <if test="loginFailTimes != null">
        #{loginFailTimes,jdbcType=INTEGER},
      </if>
      <if test="userState != null">
        #{userState,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="IDNo != null">
        #{IDNo,jdbcType=VARCHAR},
      </if>
      <if test="openID != null">
        #{openID,jdbcType=VARCHAR},
      </if>
        <if test="manageCom != null">
            #{manageCom,jdbcType=VARCHAR},
        </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
      <if test="PWDState != null">
        #{PWDState,jdbcType=VARCHAR},
      </if>
      <if test="lockDate != null">
        #{lockDate,jdbcType=DATE},
      </if>
      <if test="lockTime != null">
        #{lockTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FdUser">
    update fduser
    <set>
      <if test="userName != null">
        UserName = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        NickName = #{nickName,jdbcType=VARCHAR},
      </if>
        <if test="passWord != null and passWord != ''">
        PassWord = #{passWord,jdbcType=VARCHAR},
      </if>
      <if test="customType != null">
        CustomType = #{customType,jdbcType=VARCHAR},
      </if>
      <if test="customNo != null">
        CustomNo = #{customNo,jdbcType=VARCHAR},
      </if>
      <if test="isLock != null">
        IsLock = #{isLock,jdbcType=VARCHAR},
      </if>
      <if test="isVIP != null">
        IsVIP = #{isVIP,jdbcType=VARCHAR},
      </if>
      <if test="loginFailTimes != null">
        LoginFailTimes = #{loginFailTimes,jdbcType=INTEGER},
      </if>
      <if test="userState != null">
        UserState = #{userState,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        Source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        Email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        Phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="IDNo != null">
        IDNo = #{IDNo,jdbcType=VARCHAR},
      </if>
      <if test="openID != null">
        OpenID = #{openID,jdbcType=VARCHAR},
      </if>
        <if test="manageCom != null">
            ManageCom = #{manageCom,jdbcType=VARCHAR},
        </if>
      <if test="remark != null">
        Remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
      <if test="PWDState != null">
        PWDState = #{PWDState,jdbcType=VARCHAR},
      </if>
      <if test="lockDate != null">
        LockDate = #{lockDate,jdbcType=DATE},
      </if>
      <if test="lockTime != null">
        LockTime = #{lockTime,jdbcType=VARCHAR},
      </if>
      <if test="PWDChangeDate != null">
        PWDChangeDate = #{PWDChangeDate,jdbcType=DATE},
      </if>
    </set>
    where UserNo = #{userNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FdUser">
    update fduser
    set UserName = #{userName,jdbcType=VARCHAR},
      NickName = #{nickName,jdbcType=VARCHAR},
      PassWord = #{passWord,jdbcType=VARCHAR},
      CustomType = #{customType,jdbcType=VARCHAR},
      CustomNo = #{customNo,jdbcType=VARCHAR},
      IsLock = #{isLock,jdbcType=VARCHAR},
      IsVIP = #{isVIP,jdbcType=VARCHAR},
      LoginFailTimes = #{loginFailTimes,jdbcType=INTEGER},
      UserState = #{userState,jdbcType=VARCHAR},
      Source = #{source,jdbcType=VARCHAR},
      Email = #{email,jdbcType=VARCHAR},
      Phone = #{phone,jdbcType=VARCHAR},
      IDNo = #{IDNo,jdbcType=VARCHAR},
      OpenID = #{openID,jdbcType=VARCHAR},
      ManageCom = #{manageCom,jdbcType=VARCHAR},
      Remark = #{remark,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      PWDState = #{PWDState,jdbcType=VARCHAR},
      LockDate = #{lockDate,jdbcType=DATE},
      LockTime = #{lockTime,jdbcType=VARCHAR}
    where UserNo = #{userNo,jdbcType=VARCHAR}
  </update>

  <update id="updataPwd" parameterType="com.sinosoft.eflex.model.FdUser" >
    update fduser
    <set>
      <if test="phone != null">
        Phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        Email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="passWord != null">
        PassWord = #{passWord,jdbcType=VARCHAR},
      </if>
      <if test="PWDState != null">
        PWDState = #{PWDState,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where UserName = #{userName,jdbcType=VARCHAR}
    and CustomType = #{customType,jdbcType=VARCHAR}
  </update>

  <select id="findUsersByUsername" parameterType="String" resultType="com.sinosoft.eflex.model.FdUser">
    select * from FDUser
    where UserName=#{userName}
  </select>
  <update id="updateUserState" parameterType="com.sinosoft.eflex.model.FdUser">
    update fduser
   <set>
        <if test="userState != null">
          UserState = #{userState,jdbcType=VARCHAR}
        </if>
   </set>
    where UserNo = #{userNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByIdNo" parameterType="java.util.Map">
    update  fduser set Phone=#{mobilePhone} where IDNo=#{idNo}
  </update>
    <select id="findGlobalInfoByUserNo" parameterType="String" resultType="com.sinosoft.eflex.model.GlobalInput">
	select fd.userNo, fd.userName, fd.nickName, fd.customType, fd.customNo,
	fc.grpNo, fc.name, fc.sex,fr.roleType
	from fduser fd
	LEFT JOIN fcperinfo fc on fd.CustomNo = fc.PerNo
    LEFT JOIN fduserrole fr on fd.userNo = fr.userNo
	where fd.userno = #{userNo,jdbcType=VARCHAR}
  </select>
  <select id="findGlobalInfoForGrp" parameterType="String" resultType="com.sinosoft.eflex.model.GlobalInput">
	select fd.userNo, fd.userName, fd.nickName, fd.customType, fd.customNo,
	fc.grpNo, fr.name, fr.sex,fu.roleType
	from fduser fd
	LEFT JOIN fccontactgrprela fc on fd.CustomNo = fc.ContactNo
	LEFT JOIN fcgrpcontact fr on fr.ContactNo = fc.ContactNo
	LEFT JOIN fduserrole fu on fd.userNo = fu.userNo
	where fd.userno = #{userNo,jdbcType=VARCHAR} AND fc.LockState !='1' limit 1
  </select>
  <select id="findGlobalInfoForAdmin" parameterType="String" resultType="com.sinosoft.eflex.model.GlobalInput">
	select fd.userNo, fd.userName, fd.nickName, fd.customType, fd.userName name,fd.manageCom
	from fduser fd
	where fd.userno = #{userNo,jdbcType=VARCHAR}
  </select>

  <select id="findUserByCustNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from fduser fd
    where fd.CustomNo = #{custNo,jdbcType=VARCHAR}
    and fd.Customtype = '2'
  </select>
  <select id="findUserForLogin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fduser
        where UserName = #{username,jdbcType=VARCHAR}
        <if test='userType == "3"'>
            and (CustomType = '3' or CustomType = '3,5')
        </if>
        <if test='userType == "4,5"'>
            and (FIND_IN_SET(CustomType,'4,5') or CustomType = '3,5')
        </if>
        <if test='userType !="3" and userType != "4,5"'>
            and CustomType = #{userType}
        </if>
        and UserState = '1'
        ORDER BY UserNo ASC
        LIMIT 1
    </select>
  <select id="findUserByFiveElement" resultMap="BaseResultMap" parameterType="java.util.Map">
	select <include refid="Base_Column_List" />
	  from fduser
	 where Customtype = '1' and CustomNo IN (select fc.perno
	                        from fcperinfo fc
	                       where fc.name = #{name,jdbcType=VARCHAR}
	                         and fc.sex = #{gender,jdbcType=VARCHAR}
	                         and fc.IDType = #{certificateType,jdbcType=VARCHAR}
	                         and fc.IDNo = #{certificateNo,jdbcType=VARCHAR}
	                         and fc.BirthDay = #{birthday,jdbcType=VARCHAR})
            and UserState = '1'
            ORDER BY UserNo ASC
            LIMIT 1
  </select>

  <select id="isExistByUser" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from fduser
    where IDNo = #{idNo,jdbcType=VARCHAR}
    and Phone = #{phone,jdbcType=VARCHAR}
  </select>

  <!-- 找回密码 -->
  <select id="findUserInfo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from fduser
    where 1=1
    <if test="idNo != null">
       and IDNo = #{idNo,jdbcType=VARCHAR}
    </if>
    <if test="phone != null">
       and Phone = #{phone,jdbcType=VARCHAR}
    </if>
    <if test="customType != null">
       and CustomType = #{customType,jdbcType=VARCHAR}
    </if>
    and UserState = '1'
    ORDER BY UserNo ASC
    LIMIT 1
  </select>
  <select id="findUserInfoByPhone" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FdUser">
    select <include refid="Base_Column_List" />
    from fduser
    where 1=1
    <if test="phone != null">
       and Phone = #{phone,jdbcType=VARCHAR}
    </if>
    <if test="customType != null">
       and CustomType = #{customType,jdbcType=VARCHAR}
    </if>
    and UserState = '1'
    ORDER BY UserNo ASC
  </select>

  <select id="selectCustomNoByType" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FdUser">
    select <include refid="Base_Column_List" />
    from fduser
    where CustomNo = #{customNo,jdbcType=VARCHAR}
    and CustomType = #{customType,jdbcType=VARCHAR}
  </select>
  <select id="findUserByThreeElement" parameterType="com.sinosoft.eflex.model.FCPerInfo" resultType="com.sinosoft.eflex.model.FdUser">
	select *
	  from fduser
	 where Customtype = '1' and CustomNo = (select fc.perno
	                        from fcperinfo fc
	                       where fc.name = #{name,jdbcType=VARCHAR}
	                         and fc.IDType = #{IDType,jdbcType=VARCHAR}
	                         and fc.IDNo = #{IDNo,jdbcType=VARCHAR})
  </select>
  <select id="findUserByIdno" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FdUser">
      select *
      from fduser
      where Customtype = '2'
        and idNo = #{idNo,jdbcType=VARCHAR}
  </select>

  <select id="selectByPwd" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FdUser">
    SELECT `PassWord` FROM fduser WHERE IDNo = #{IDNo} AND Customtype = '1' ORDER BY ModifyDate,ModifyTime ASC LIMIT 1
  </select>
  <select id="selectByIdNo" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from fduser where IDNo = #{idNo}
    </select>
  <select id="selectByPerNoAndIdNo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from fduser where CustomNo = #{perNo} and IDNo = #{idNo} and CustomType = '1'
  </select>
  <select id="selectAuditUserIsExist" parameterType="com.sinosoft.eflex.model.configmanage.SelectAuditUserIsExistReq" resultType="java.lang.Integer">
      select
        count(*)
      from
      Fduser
      where CustomType in ('3','4','5','3,5')
      and username = #{loginName}
      <if test="notEqualUserNo != null">
        and userNo != #{notEqualUserNo}
      </if>
  </select>
  <select id="selectAuditUserPhoneIsExist"
            parameterType="com.sinosoft.eflex.model.configmanage.SelectAuditUserPhoneIsExistReq"
            resultType="java.lang.Integer">
        select
        count(*)
        from
        Fduser
        where CustomType in ('3','4','5','3,5')
        and phone = #{phone}
        <if test="notEqualUserNo != null">
            and userNo != #{notEqualUserNo}
        </if>
    </select>
  <select id="selectAuditUserList" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.configmanage.AuditUserInfo">
    select
        a.userNo,
        a.userName as loginName,
        a.nickName as userName,
        a.Phone as phone,
        a.userState,
        (select codeName from fdcode where codeKey = a.userState and codeType='Userstate') userStateName,
        (select
          GROUP_CONCAT(f1.RoleType)
         from fduserRole f1
         left join fdcode f2 on f1.RoleType = f2.CodeKey and f2.CodeType = 'RoleType'
        where f1.userNo = a.userNo) as userRole,
         (select
            GROUP_CONCAT(f2.codeName)
         from fduserRole f1
         left join fdcode f2 on f1.RoleType = f2.CodeKey and f2.CodeType = 'RoleType'
        where f1.userNo = a.userNo) as userRoleName,
       a.ManageCom as manageCom,
         (select
            GROUP_CONCAT(f3.shortName)
         from fdcom f3 where find_in_set(a.ManageCom, f3.ManageCom)) as manageComName
    from
     fduser a
        where CustomType in ('3','4','5','3,5')
        <if test="manageCom != null and manageCom != ''">
            and find_in_set(#{manageCom},a.ManageCom)
        </if>
        <if test="loginName != null and loginName != ''">
            and a.userName = #{loginName}
        </if>
        <if test="userName != null and userName != ''">
            and a.nickName = #{userName}
        </if>
        <if test="userState != null and userState != ''">
            and a.userState = #{userState}
        </if>
        order by FIELD(userNo,'admin002','admin001') desc,ModifyDate desc,ModifyTime desc
  </select>
  <select id="selectSingleAuditUserInfo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.configmanage.AuditUserInfo">
        select
        a.userNo,
        a.userName as loginName,
        a.nickName as userName,
        a.Phone as phone,
        a.userState,
        (select codeName from fdcode where codeKey = a.userState and codeType='Userstate') userStateName,
        (select
        GROUP_CONCAT(f1.RoleType)
        from fduserRole f1
        left join fdcode f2 on f1.RoleType = f2.CodeKey and f2.CodeType = 'RoleType'
        where f1.userNo = a.userNo) as userRole,
        (select
        GROUP_CONCAT(f2.codeName)
        from fduserRole f1
        left join fdcode f2 on f1.RoleType = f2.CodeKey and f2.CodeType = 'RoleType'
        where f1.userNo = a.userNo) as userRoleName,
        a.ManageCom as manageCom,
        (select
        GROUP_CONCAT(f3.shortName)
        from fdcom f3 where find_in_set(a.ManageCom, f3.ManageCom)) as manageComName
        from
        fduser a
        where CustomType in ('3','4','5') and a.userNo = #{userNo}
    </select>
  <select id="selectAuditUserPhone" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List"/>
        from fduser where phone = #{phone}
        <if test='userType == "3"'>
            and (CustomType = '3' or CustomType = '3,5')
        </if>
        <if test='userType == "4,5"'>
            and (FIND_IN_SET(CustomType,'4,5') or CustomType = '3,5')
        </if>
        and UserState = '1'
    </select>
  <select id="selectCountByIdNo" resultType="java.lang.Integer">
    select count(1) from  fduser where IDNo = #{idNo}
  </select>
    <select id="selectByIdNoAndType" resultType="com.sinosoft.eflex.model.FdUser">
      select * from fduser where IDNo=#{idNo} and CustomType=#{type}
    </select>

    <update id="updateBatchById">
    <foreach collection="list" item="item" separator=";">
      update
      fduser
      <set>
        <if test="item.userName != null">
          UserName = #{item.userName,jdbcType=VARCHAR},
        </if>
        <if test="item.nickName != null">
          NickName = #{item.nickName,jdbcType=VARCHAR},
        </if>
        <if test="item.passWord != null and passWord != ''">
          PassWord = #{item.passWord,jdbcType=VARCHAR},
        </if>
        <if test="item.customType != null">
          CustomType = #{item.customType,jdbcType=VARCHAR},
        </if>
        <if test="item.customNo != null">
          CustomNo = #{item.customNo,jdbcType=VARCHAR},
        </if>
        <if test="item.isLock != null">
          IsLock = #{item.isLock,jdbcType=VARCHAR},
        </if>
        <if test="item.isVIP != null">
          IsVIP = #{item.isVIP,jdbcType=VARCHAR},
        </if>
        <if test="item.loginFailTimes != null">
          LoginFailTimes = #{item.loginFailTimes,jdbcType=INTEGER},
        </if>
        <if test="item.userState != null">
          UserState = #{item.userState,jdbcType=VARCHAR},
        </if>
        <if test="item.source != null">
          Source = #{item.source,jdbcType=VARCHAR},
        </if>
        <if test="item.email != null">
          Email = #{item.email,jdbcType=VARCHAR},
        </if>
        <if test="item.phone != null">
          Phone = #{item.phone,jdbcType=VARCHAR},
        </if>
        <if test="item.IDNo != null">
          IDNo = #{item.IDNo,jdbcType=VARCHAR},
        </if>
        <if test="item.openID != null">
          OpenID = #{item.openID,jdbcType=VARCHAR},
        </if>
        <if test="item.manageCom != null">
          ManageCom = #{item.manageCom,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null">
          Remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.operatorCom != null">
          OperatorCom = #{item.operatorCom,jdbcType=VARCHAR},
        </if>
        <if test="item.operator != null">
          Operator = #{operator,jdbcType=VARCHAR},
        </if>
        <if test="item.makeDate != null">
          MakeDate = #{item.makeDate,jdbcType=DATE},
        </if>
        <if test="item.makeTime != null">
          MakeTime = #{item.makeTime,jdbcType=VARCHAR},
        </if>
        <if test="item.modifyDate != null">
          ModifyDate = #{item.modifyDate,jdbcType=DATE},
        </if>
        <if test="item.modifyTime != null">
          ModifyTime = #{item.modifyTime,jdbcType=VARCHAR},
        </if>
        <if test="item.PWDState != null">
          PWDState = #{item.PWDState,jdbcType=VARCHAR},
        </if>
        <if test="item.lockDate != null">
          LockDate = #{item.lockDate,jdbcType=DATE},
        </if>
        <if test="item.lockTime != null">
          LockTime = #{item.lockTime,jdbcType=VARCHAR},
        </if>
        <if test="item.PWDChangeDate != null">
          PWDChangeDate = #{item.PWDChangeDate,jdbcType=DATE},
        </if>
      </set>
      where
      <choose>
        <when test="item.edorType == 'IC' and item.edorType != null and item.edorType != ''">
          IDNo = #{item.oldIdNo}
        </when>
        <otherwise>
          IDNo = #{item.IDNo}
        </otherwise>
      </choose>
    </foreach>
  </update>
</mapper>
