<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCHealthDesignDetailMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCHealthDesignDetail">
        <id column="HealthDesignNo" jdbcType="VARCHAR" property="healthDesignNo"/>
        <result column="RiskCode" jdbcType="VARCHAR" property="riskCode"/>
        <result column="GradeLevelTopLimit" jdbcType="VARCHAR" property="gradeLevelTopLimit"/>
        <result column="GradeLevelLowLimit" jdbcType="VARCHAR" property="gradeLevelLowLimit"/>
        <result column="AmntTopLimit" jdbcType="DOUBLE" property="amntTopLimit"/>
        <result column="AmntLowLimit" jdbcType="DOUBLE" property="amntLowLimit"/>
        <result column="AgeTopLimit" jdbcType="VARCHAR" property="ageTopLimit"/>
        <result column="AgeLowLimit" jdbcType="VARCHAR" property="ageLowLimit"/>
        <result column="Sex" jdbcType="VARCHAR" property="sex"/>
        <result column="InsuredType" jdbcType="VARCHAR" property="insuredType"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    HealthDesignNo, RiskCode, GradeLevelTopLimit, GradeLevelLowLimit, AmntTopLimit, AmntLowLimit, 
    AgeTopLimit, AgeLowLimit, Sex, InsuredType, Operator, OperatorCom, MakeDate, MakeTime, 
    ModifyDate, ModifyTime
  </sql>
    <select id="selectByParams" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    HealthDesignNo, RiskCode,
    (select GradeLevelCode from FCBusPersonType
    where EnsureCode = #{ensureCode}  and OrderNum = GradeLevelTopLimit) as GradeLevelTopLimit,
    (select GradeLevelCode from FCBusPersonType
    where EnsureCode = #{ensureCode}  and OrderNum = GradeLevelLowLimit) as GradeLevelLowLimit,
    AmntTopLimit, AmntLowLimit,
    AgeTopLimit, AgeLowLimit,
    (SELECT codename from  fdcode WHERE codetype = 'sex' and codekey = Sex) as Sex,
    (SELECT codename from  fdcode WHERE codetype = 'InsuredType' and codekey = InsuredType) as InsuredType
    from fchealthdesigndetail
    where HealthDesignNo = #{healthDesignNo,jdbcType=VARCHAR}
  </select>
    <select id="selectByPrimaryKey" resultType="com.sinosoft.eflex.model.FCHealthDesignDetail">
        select * from fchealthdesigndetail
        where HealthDesignNo = #{healthDesignNo}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fchealthdesigndetail
    where HealthDesignNo = #{healthDesignNo,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCHealthDesignDetail">
    insert into fchealthdesigndetail (HealthDesignNo, RiskCode, GradeLevelTopLimit, 
      GradeLevelLowLimit, AmntTopLimit, AmntLowLimit, 
      AgeTopLimit, AgeLowLimit, Sex, 
      InsuredType, Operator, OperatorCom, 
      MakeDate, MakeTime, ModifyDate, 
      ModifyTime)
    values (#{healthDesignNo,jdbcType=VARCHAR}, #{riskCode,jdbcType=VARCHAR}, #{gradeLevelTopLimit,jdbcType=VARCHAR}, 
      #{gradeLevelLowLimit,jdbcType=VARCHAR}, #{amntTopLimit,jdbcType=DOUBLE}, #{amntLowLimit,jdbcType=DOUBLE}, 
      #{ageTopLimit,jdbcType=VARCHAR}, #{ageLowLimit,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR}, 
      #{insuredType,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, 
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, 
      #{modifyTime,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCHealthDesignDetail">
        insert into fchealthdesigndetail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="healthDesignNo != null">
                HealthDesignNo,
            </if>
            <if test="riskCode != null">
                RiskCode,
            </if>
            <if test="gradeLevelTopLimit != null">
                GradeLevelTopLimit,
            </if>
            <if test="gradeLevelLowLimit != null">
                GradeLevelLowLimit,
            </if>
            <if test="amntTopLimit != null">
                AmntTopLimit,
            </if>
            <if test="amntLowLimit != null">
                AmntLowLimit,
            </if>
            <if test="ageTopLimit != null">
                AgeTopLimit,
            </if>
            <if test="ageLowLimit != null">
                AgeLowLimit,
            </if>
            <if test="sex != null">
                Sex,
            </if>
            <if test="insuredType != null">
                InsuredType,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="healthDesignNo != null">
                #{healthDesignNo,jdbcType=VARCHAR},
            </if>
            <if test="riskCode != null">
                #{riskCode,jdbcType=VARCHAR},
            </if>
            <if test="gradeLevelTopLimit != null">
                #{gradeLevelTopLimit,jdbcType=VARCHAR},
            </if>
            <if test="gradeLevelLowLimit != null">
                #{gradeLevelLowLimit,jdbcType=VARCHAR},
            </if>
            <if test="amntTopLimit != null">
                #{amntTopLimit,jdbcType=DOUBLE},
            </if>
            <if test="amntLowLimit != null">
                #{amntLowLimit,jdbcType=DOUBLE},
            </if>
            <if test="ageTopLimit != null">
                #{ageTopLimit,jdbcType=VARCHAR},
            </if>
            <if test="ageLowLimit != null">
                #{ageLowLimit,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="insuredType != null">
                #{insuredType,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertList" parameterType="java.util.List">
        insert into fchealthdesigndetail
        (HealthDesignNo, RiskCode, GradeLevelTopLimit,
        GradeLevelLowLimit, AmntTopLimit, AmntLowLimit,
        AgeTopLimit,AgeLowLimit, Sex,InsuredType,Operator, OperatorCom,
        MakeDate, MakeTime, ModifyDate,
        ModifyTime) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.healthDesignNo,jdbcType=VARCHAR},
            #{item.riskCode,jdbcType=VARCHAR},
            #{item.gradeLevelTopLimit,jdbcType=VARCHAR},
            #{item.gradeLevelLowLimit,jdbcType=VARCHAR},
            #{item.amntTopLimit,jdbcType=DATE},
            #{item.amntLowLimit,jdbcType=INTEGER},
            #{item.ageTopLimit,jdbcType=DOUBLE},
            #{item.ageLowLimit,jdbcType=VARCHAR},
            #{item.sex,jdbcType=VARCHAR},
            #{item.insuredType,jdbcType=VARCHAR},
            #{item.operator,jdbcType=VARCHAR},
            #{item.operatorCom,jdbcType=VARCHAR},
            #{item.makeDate,jdbcType=DATE},
            #{item.makeTime,jdbcType=VARCHAR},
            #{item.modifyDate,jdbcType=DATE},
            #{item.modifyTime,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCHealthDesignDetail">
        update fchealthdesigndetail
        <set>
            <if test="riskCode != null">
                RiskCode = #{riskCode,jdbcType=VARCHAR},
            </if>
            <if test="gradeLevelTopLimit != null">
                GradeLevelTopLimit = #{gradeLevelTopLimit,jdbcType=VARCHAR},
            </if>
            <if test="gradeLevelLowLimit != null">
                GradeLevelLowLimit = #{gradeLevelLowLimit,jdbcType=VARCHAR},
            </if>
            <if test="amntTopLimit != null">
                AmntTopLimit = #{amntTopLimit,jdbcType=DOUBLE},
            </if>
            <if test="amntLowLimit != null">
                AmntLowLimit = #{amntLowLimit,jdbcType=DOUBLE},
            </if>
            <if test="ageTopLimit != null">
                AgeTopLimit = #{ageTopLimit,jdbcType=VARCHAR},
            </if>
            <if test="ageLowLimit != null">
                AgeLowLimit = #{ageLowLimit,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="insuredType != null">
                InsuredType = #{insuredType,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where HealthDesignNo = #{healthDesignNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCHealthDesignDetail">
    update fchealthdesigndetail
    set RiskCode = #{riskCode,jdbcType=VARCHAR},
      GradeLevelTopLimit = #{gradeLevelTopLimit,jdbcType=VARCHAR},
      GradeLevelLowLimit = #{gradeLevelLowLimit,jdbcType=VARCHAR},
      AmntTopLimit = #{amntTopLimit,jdbcType=DOUBLE},
      AmntLowLimit = #{amntLowLimit,jdbcType=DOUBLE},
      AgeTopLimit = #{ageTopLimit,jdbcType=VARCHAR},
      AgeLowLimit = #{ageLowLimit,jdbcType=VARCHAR},
      Sex = #{sex,jdbcType=VARCHAR},
      InsuredType = #{insuredType,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where HealthDesignNo = #{healthDesignNo,jdbcType=VARCHAR}
  </update>
</mapper>