<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCOrderItemDetailMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCOrderItemDetail">
        <id column="OrderItemDetailNo" jdbcType="VARCHAR" property="orderItemDetailNo"/>
        <id column="ProductCode" jdbcType="VARCHAR" property="productCode"/>
        <id column="ProductEleCode" jdbcType="VARCHAR" property="productEleCode"/>
        <result column="Value" jdbcType="VARCHAR" property="value"/>
        <result column="DutyCode" jdbcType="VARCHAR" property="dutyCode"/>
        <result column="DutyGroupCode" jdbcType="VARCHAR" property="dutyGroupCode"/>
        <result column="InsuredAmount" jdbcType="DOUBLE" property="insuredAmount"/>
        <result column="InsurePeriod" jdbcType="VARCHAR" property="insurePeriod"/>
        <result column="PayFrequency" jdbcType="VARCHAR" property="payFrequency"/>
        <result column="PayPeriod" jdbcType="VARCHAR" property="payPeriod"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>

    <!-- 投保清单导出excel -->
    <resultMap type="com.sinosoft.eflex.model.FCOrderItemDetail"
               id="InsuredDetails" extends="BaseResultMap">
        <association property="fcEnsurePlan"
                     column="{ensureCode=ensureCode,OrderItemNo=OrderItemNo,planCode=ProductCode,ensureType=ensureType,garName=garName,garSex=garSex,stuName=stuName,planName=planName,planObject=planObject,department=department}"
                     javaType="com.sinosoft.eflex.model.FCEnsurePlan"
                     select="com.sinosoft.eflex.dao.FCEnsurePlanMapper.selectInsuredDetail"
                     fetchType="lazy"/>
    </resultMap>
    <!-- 投保清单导出excel -->
    <select id="selectInsuredDetail" parameterType="java.util.Map"
            resultMap="InsuredDetails">
        select case when ('${ensureType}' != '') then '${ensureType}' else '' end   as ensureType,
               case when ('${garName}' != '') then '${garName}' else '' end         as garName,
               case when ('${garSex}' != '') then '${garSex}' else '' end           as garSex,
               case when ('${department}' != '') then '${department}' else '' end   as department,
               case when ('${stuName}' != '') then '${stuName}' else '' end         as stuName,
               case when ('${planName}' != '') then '${planName}' else '' end       as planName,
               case WHEN ('${planObject}' != '') then '${planObject}' else '' end   as planObject,
               case WHEN ('${ensureCode}' != '') then '${ensureCode}' else '' end   as ensureCode,
               case WHEN ('${OrderItemNo}' != '') then '${OrderItemNo}' else '' end as OrderItemNo,
               ProductCode
        from fcorderitemdetail
        where OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
    </select>
    <sql id="Base_Column_List">
        OrderItemDetailNo, ProductCode, ProductEleCode, Value,
		DutyCode, DutyGroupCode,InsuredAmount,InsurePeriod,PayFrequency,PayPeriod,
		Operator,
		OperatorCom, MakeDate, MakeTime,
		ModifyDate, ModifyTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCOrderItemDetailKey"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorderitemdetail
        where OrderItemDetailNo =
        #{orderItemDetailNo,jdbcType=VARCHAR}
        and ProductCode =
        #{productCode,jdbcType=VARCHAR}
        and ProductEleCode =
        #{productEleCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCOrderItemDetailKey">
        delete
        from fcorderitemdetail
        where OrderItemDetailNo =
              #{orderItemDetailNo,jdbcType=VARCHAR}
          and ProductCode =
              #{productCode,jdbcType=VARCHAR}
          and ProductEleCode =
              #{productEleCode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCOrderItemDetail">
        insert into fcorderitemdetail
        (OrderItemDetailNo, ProductCode,
         ProductEleCode,
         Value, DutyCode,
         DutyGroupCode,
         Operator, OperatorCom, MakeDate,
         MakeTime, ModifyDate,
         ModifyTime)
        values (#{orderItemDetailNo,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR}, #{productEleCode,jdbcType=VARCHAR},
                (select a.planobject from fcensureplan a where a.plancode = #{productCode,jdbcType=VARCHAR} and a.ensureCode=#{ensureCode,jdbcType=VARCHAR}),
                #{dutyCode,jdbcType=VARCHAR},
                #{dutyGroupCode,jdbcType=VARCHAR},
                #{operator,jdbcType=VARCHAR},
                #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE},
                #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
                #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCOrderItemDetail">
        insert into fcorderitemdetail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderItemDetailNo != null">
                OrderItemDetailNo,
            </if>
            <if test="productCode != null">
                ProductCode,
            </if>
            <if test="productEleCode != null">
                ProductEleCode,
            </if>
            <if test="value != null">
                Value,
            </if>
            <if test="dutyCode != null">
                DutyCode,
            </if>
            <if test="dutyGroupCode != null">
                DutyGroupCode,
            </if>
            <if test="insuredAmount != null">
                InsuredAmount,
            </if>
            <if test="insurePeriod != null">
                InsurePeriod,
            </if>
            <if test="payFrequency != null">
                PayFrequency,
            </if>
            <if test="payPeriod != null">
                PayPeriod,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderItemDetailNo != null">
                #{orderItemDetailNo,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productEleCode != null">
                #{productEleCode,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyGroupCode != null">
                #{dutyGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="insuredAmount != null">
                #{insuredAmount,jdbcType=VARCHAR},
            </if>
            <if test="insurePeriod != null">
                #{insurePeriod,jdbcType=VARCHAR},
            </if>
            <if test="payFrequency != null">
                #{payFrequency,jdbcType=VARCHAR},
            </if>
            <if test="payPeriod != null">
                #{payPeriod,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCOrderItemDetail">
        update fcorderitemdetail
        <set>
            <if test="value != null">
                Value = #{value,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                DutyCode = #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyGroupCode != null">
                DutyGroupCode = #{dutyGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime =#{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="InsuredAmount != null">
                InsuredAmount=#{InsuredAmount,jdbcType=VARCHAR},
            </if>
            <if test="InsurePeriod != null">
                InsurePeriod=#{InsurePeriod,jdbcType=DATE},
            </if>
            <if test="PayFrequency != null">
                PayFrequency=#{PayFrequency,jdbcType=VARCHAR},
            </if>
            <if test="PayPeriod != null">
                PayPeriod=#{PayPeriod,jdbcType=DATE},
            </if>
        </set>
        where OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
        and
        ProductCode = #{productCode,jdbcType=VARCHAR}
        and ProductEleCode =
        #{productEleCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKeySelect" parameterType="com.sinosoft.eflex.model.FCOrderItemDetail">
        update fcorderitemdetail
        <set>
            <if test="dutyCode != null">
                DutyCode = #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime =#{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="InsuredAmount != null">
                InsuredAmount=#{InsuredAmount,jdbcType=VARCHAR},
            </if>
            <if test="InsurePeriod != null">
                InsurePeriod=#{InsurePeriod,jdbcType=VARCHAR},
            </if>
            <if test="PayFrequency != null">
                PayFrequency=#{PayFrequency,jdbcType=VARCHAR},
            </if>
            <if test="PayPeriod != null">
                PayPeriod=#{PayPeriod,jdbcType=VARCHAR},
            </if>
        </set>
        where OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCOrderItemDetail">
        update
            fcorderitemdetail
        set Value         = #{value,jdbcType=VARCHAR},
            DutyCode      =
                #{dutyCode,jdbcType=VARCHAR},
            DutyGroupCode =
                #{dutyGroupCode,jdbcType=VARCHAR},
            Operator      =
                #{operator,jdbcType=VARCHAR},
            OperatorCom   =
                #{operatorCom,jdbcType=VARCHAR},
            MakeDate      = #{makeDate,jdbcType=DATE},
            MakeTime      = #{makeTime,jdbcType=VARCHAR},
            ModifyDate    =
                #{modifyDate,jdbcType=DATE},
            ModifyTime    =
                #{modifyTime,jdbcType=VARCHAR}
        where OrderItemDetailNo =
              #{orderItemDetailNo,jdbcType=VARCHAR}
          and ProductCode =
              #{productCode,jdbcType=VARCHAR}
          and ProductEleCode =
              #{productEleCode,jdbcType=VARCHAR}
    </update>
    <select id="selectList" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorderitemdetail
        where 1=1
        <if test="orderItemDetailNo != null">
            and OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
        </if>
        <if test="productCode != null">
            and ProductCode = #{productCode,jdbcType=VARCHAR}
            and OrderItemDetailNo in (select a.OrderItemDetailNo from fcorderitem a where a.orderno in
             (select b.orderno from fcorder b where b.grporderno in (select c.grporderno from fcgrporder c where c.ensurecode=#{ensureCode,jdbcType=VARCHAR})))
        </if>
        <if test="productEleCode != null">
            and ProductEleCode = #{productEleCode,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectDailyInsuredInfo" resultType="java.util.Map">
        select c.ProductCode          riskCode,
               f2.RiskName            riskName,
               c.DutyCode             dutyCode,
               f1.PlanName            planName,
               c.InsuredAmount        insuredAmount,
               b.SelfPrem + b.GrpPrem totalPrem
        from fcorderinsured a
                 inner join fcorderitem b on b.OrderItemNo = a.OrderItemNo
                 inner join fcorderitemdetail c on c.OrderItemDetailNo = b.OrderItemDetailNo
                 left join FdRIskPlanInfo f1 on f1.PlanCode = c.DutyCode
                 left join fdriskinfo f2 on f2.RiskCode = c.ProductCode
        where a.orderItemNo = #{orderItemNo,jdbcType=VARCHAR}
    </select>
    <select id="selectTotalPremInfo" resultType="java.util.Map">
        select sum(b.SelfPrem + b.GrpPrem) sumPrem,
               sum(b.GrpPrem)              grpPrem,
               sum(b.SelfPrem)             selfPrem
        from fcorderinsured a
                 inner join fcorderitem b on b.OrderItemNo = a.OrderItemNo
                 inner join fcorderitemdetail c on c.OrderItemDetailNo = b.OrderItemDetailNo
        where a.orderItemNo = #{orderItemNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByKey" parameterType="java.lang.String">
        delete
        from fcorderitemdetail
        where OrderItemDetailNo =
              #{orderItemDetailNo,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByPersonId" parameterType="java.lang.String">
        delete
        from fcorderitemdetail
        where OrderItemDetailNo in
              (select a.OrderItemDetailNo
               from fcorderitem a,
                    fcorderinsured b
               where a.orderitemno = b.orderitemno
                 and a.orderno = b.orderno
                 and b.orderno = #{orderNo,jdbcType=VARCHAR}
                 and b.personid = #{personId,jdbcType=VARCHAR})
    </delete>
    <delete id="deleteByOrderNo" parameterType="java.lang.String">
        DELETE fc
        FROM fcorderitemdetail fc,
             (SELECT a.OrderItemDetailNo
              FROM fcorderitem a
              WHERE a.orderno = #{orderNo,jdbcType=VARCHAR}) b
        WHERE fc.OrderItemDetailNo = b.OrderItemDetailNo
    </delete>
    <delete id="updateInsureAmountContNo" parameterType="java.util.Map">
       update fcorderitemdetail
		<set>
		      <if test="insuredAmount != null">
		        insuredAmount = #{insuredAmount,jdbcType=DOUBLE},
		      </if>
			  <if test="modifyDate != null">
				ModifyDate = #{modifyDate,jdbcType=DATE},
			  </if>
			  <if test="modifyTime != null">
				ModifyTime = #{modifyTime,jdbcType=VARCHAR},
			  </if>
		</set>
		where orderitemdetailNO in (select orderitemdetailNO from fcorderitem where contNo=#{contNo,jdbcType=VARCHAR})
    </delete>
    <insert id="insertAll" parameterType="com.sinosoft.eflex.model.FCOrderItemDetail">
        insert into fcorderitemdetail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderItemDetailNo != null">
                OrderItemDetailNo,
            </if>
            <if test="productCode != null">
                ProductCode,
            </if>
            <if test="productEleCode != null">
                ProductEleCode,
            </if>
            <if test="value != null">
                Value,
            </if>
            <if test="dutyCode != null">
                DutyCode,
            </if>
            <if test="dutyGroupCode != null">
                DutyGroupCode,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
            <if test="InsuredAmount != null">
                InsuredAmount,
            </if>
            <if test="InsurePeriod != null">
                InsurePeriod,
            </if>
            <if test="PayFrequency != null">
                PayFrequency,
            </if>
            <if test="PayPeriod != null">
                PayPeriod,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderItemDetailNo != null">
                #{orderItemDetailNo,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productEleCode != null">
                #{productEleCode,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyGroupCode != null">
                #{dutyGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="InsuredAmount != null">
                #{InsuredAmount,jdbcType=VARCHAR},
            </if>
            <if test="InsurePeriod != null">
                #{InsurePeriod,jdbcType=DATE},
            </if>
            <if test="PayFrequency != null">
                #{PayFrequency,jdbcType=VARCHAR},
            </if>
            <if test="PayPeriod != null">
                #{PayPeriod,jdbcType=DATE},
            </if>
        </trim>
    </insert>
    <select id="selectByPrimaryKey_No" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorderitemdetail
        where OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
    </select>
    <select id="selectOrderItemInfo" resultType="java.util.Map">
        select f1.OrderItemDetailNo orderItemDetailNo,
               CAST(f1.SelfPrem as char)           selfPrem,
               CAST(f1.GrpPrem as char)           grpPrem,
               f2.ProductCode       productCode,
               f4.RiskName          riskName,
               f2.DutyCode          dutyCode,
               f3.PlanName          planName,
               f2.PayFrequency      payFrequency,
               f2.PayPeriod         payPeriod,
               f2.InsurePeriod      insurePeriod,
--             (CASE WHEN f2.PayFrequency='1'
--                 THEN '一次交清'
--                 ELSE f5.CodeName END) AS payFrequencyName,
-- 		    (CASE WHEN f2.PayPeriod='01'
--                 THEN '一次交清'
--                 ELSE f6.CodeName END) AS payPeriodName,
               f5.CodeName      payFrequencyName,
               f6.CodeName      payPeriodName,
               f7.CodeName      insurePeriodName,
               CAST(f2.InsuredAmount as char)     insuredAmount
        from fcorderitem f1
                 inner join fcorderitemdetail f2 on f1.OrderItemDetailNo = f2.OrderItemDetailNo
                 inner join fdriskplaninfo f3 on f2.DutyCode = f3.PlanCode
                 inner join fdriskinfo f4 on f4.RiskCode = f2.ProductCode
            left join fdcode f5 on f5.CodeType = 'payFrequency' and f5.CodeKey = f2.PayFrequency
            left join fdcode f6 on f6.CodeType = 'payPeriod' and f6.CodeKey = f2.payPeriod
            left join fdcode f7 on f7.CodeType = 'insurePeriod' and f7.CodeKey = f2.insurePeriod
        where f1.OrderItemNo = #{orderItemNo}
    </select>
    <select id="updateInsureAmountBeDailySign" parameterType="java.util.List" resultType="java.lang.Integer">
        <foreach collection="list" item="fcOrderItemDetail" separator=" union all ">
            update fcorderitemdetail
            <set>
                <if test="fcOrderItemDetail.InsuredAmount != null">
                    insuredAmount = #{fcOrderItemDetail.InsuredAmount},
                </if>
                <if test="fcOrderItemDetail.modifyDate != null">
                    ModifyDate = #{fcOrderItemDetail.modifyDate},
                </if>
                <if test="fcOrderItemDetail.modifyTime != null">
                    ModifyTime = #{fcOrderItemDetail.modifyTime}
                </if>
            </set>
            where orderitemdetailNO = #{FCOrderItemDetail.orderItemDetailNo}
        </foreach>
    </select>
    <select id="selectDetailByOrderItemDetailNo" resultType="com.sinosoft.eflex.model.FCOrderItemDetail">
        select * from fcorderitemdetail where OrderItemDetailNo=#{orderItemDetailNo}
    </select>
    <update id="updateByOrderItemDetailNo">
        update fcorderitemdetail set ProductCode=#{contPlanCode} where OrderItemDetailNo=#{orderItemDetailNo}
    </update>
</mapper>
