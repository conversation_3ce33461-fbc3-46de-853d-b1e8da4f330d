<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCEdorPlanInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCEdorPlanInfo">
    <id column="edorPlanSN" jdbcType="VARCHAR" property="edorPlanSN" />
    <result column="planCode" jdbcType="VARCHAR" property="planCode" />
    <result column="batch" jdbcType="VARCHAR" property="batch" />
    <result column="planName" jdbcType="VARCHAR" property="planName" />
    <result column="planObject" jdbcType="VARCHAR" property="planObject" />
    <result column="riskName" jdbcType="VARCHAR" property="riskName" />
    <result column="riskCode" jdbcType="VARCHAR" property="riskCode" />
    <result column="dutyName" jdbcType="VARCHAR" property="dutyName" />
    <result column="dutyCode" jdbcType="VARCHAR" property="dutyCode" />
    <result column="Amnt" jdbcType="DOUBLE" property="amnt" />
    <result column="prem" jdbcType="DOUBLE" property="prem" />
    <result column="GetLimit" jdbcType="DOUBLE" property="getLimit" />
    <result column="GetRatio" jdbcType="DOUBLE" property="getRatio" />
    <result column="spare1" jdbcType="VARCHAR" property="spare1" />
    <result column="spare2" jdbcType="VARCHAR" property="spare2" />
    <result column="spare3" jdbcType="VARCHAR" property="spare3" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="VARCHAR" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="VARCHAR" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
    <result column="GetLimitType" jdbcType="VARCHAR" property="getLimitType" />
  </resultMap>
  <sql id="Base_Column_List">
    edorPlanSN, planCode, batch, planName, planObject, riskName, riskCode, dutyName, 
    dutyCode, Amnt, prem, GetLimit,GetLimitType, GetRatio, spare1, spare2, spare3, Operator, OperatorCom,
    MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcedorplaninfo
    where edorPlanSN = #{edorPlanSN,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcedorplaninfo
    where edorPlanSN = #{edorPlanSN,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCEdorPlanInfo">
    insert into fcedorplaninfo (edorPlanSN, planCode, batch, 
      planName, planObject, riskName, 
      riskCode, dutyName, dutyCode, 
      Amnt, prem, GetLimit,GetLimitType,
      GetRatio, spare1, spare2, 
      spare3, Operator, OperatorCom, 
      MakeDate, MakeTime, ModifyDate, 
      ModifyTime)
    values (#{edorPlanSN,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, #{batch,jdbcType=VARCHAR}, 
      #{planName,jdbcType=VARCHAR}, #{planObject,jdbcType=VARCHAR}, #{riskName,jdbcType=VARCHAR}, 
      #{riskCode,jdbcType=VARCHAR}, #{dutyName,jdbcType=VARCHAR}, #{dutyCode,jdbcType=VARCHAR}, 
      #{amnt,jdbcType=DOUBLE}, #{prem,jdbcType=DOUBLE}, #{getLimit,jdbcType=DOUBLE}, #{getLimitType,jdbcType=VARCHAR},
      #{getRatio,jdbcType=DOUBLE}, #{spare1,jdbcType=VARCHAR}, #{spare2,jdbcType=VARCHAR}, 
      #{spare3,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, 
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, 
      #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCEdorPlanInfo">
    insert into fcedorplaninfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="edorPlanSN != null">
        edorPlanSN,
      </if>
      <if test="planCode != null">
        planCode,
      </if>
      <if test="batch != null">
        batch,
      </if>
      <if test="planName != null">
        planName,
      </if>
      <if test="planObject != null">
        planObject,
      </if>
      <if test="riskName != null">
        riskName,
      </if>
      <if test="riskCode != null">
        riskCode,
      </if>
      <if test="dutyName != null">
        dutyName,
      </if>
      <if test="dutyCode != null">
        dutyCode,
      </if>
      <if test="amnt != null">
        Amnt,
      </if>
      <if test="prem != null">
        prem,
      </if>
      <if test="getLimit != null">
        GetLimit,
      </if>
      <if test="getLimitType != null">
        GetLimitType,
      </if>
      <if test="getRatio != null">
        GetRatio,
      </if>
      <if test="spare1 != null">
        spare1,
      </if>
      <if test="spare2 != null">
        spare2,
      </if>
      <if test="spare3 != null">
        spare3,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="edorPlanSN != null">
        #{edorPlanSN,jdbcType=VARCHAR},
      </if>
      <if test="planCode != null">
        #{planCode,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=VARCHAR},
      </if>
      <if test="planName != null">
        #{planName,jdbcType=VARCHAR},
      </if>
      <if test="planObject != null">
        #{planObject,jdbcType=VARCHAR},
      </if>
      <if test="riskName != null">
        #{riskName,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="dutyName != null">
        #{dutyName,jdbcType=VARCHAR},
      </if>
      <if test="dutyCode != null">
        #{dutyCode,jdbcType=VARCHAR},
      </if>
      <if test="amnt != null">
        #{amnt,jdbcType=DOUBLE},
      </if>
      <if test="prem != null">
        #{prem,jdbcType=DOUBLE},
      </if>
      <if test="getLimit != null">
        #{getLimit,jdbcType=DOUBLE},
      </if>
      <if test="getLimitType != null">
        #{getLimitType,jdbcType=DOUBLE},
      </if>
      <if test="getRatio != null">
        #{getRatio,jdbcType=DOUBLE},
      </if>
      <if test="spare1 != null">
        #{spare1,jdbcType=VARCHAR},
      </if>
      <if test="spare2 != null">
        #{spare2,jdbcType=VARCHAR},
      </if>
      <if test="spare3 != null">
        #{spare3,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCEdorPlanInfo">
    update fcedorplaninfo
    <set>
      <if test="planCode != null">
        planCode = #{planCode,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        batch = #{batch,jdbcType=VARCHAR},
      </if>
      <if test="planName != null">
        planName = #{planName,jdbcType=VARCHAR},
      </if>
      <if test="planObject != null">
        planObject = #{planObject,jdbcType=VARCHAR},
      </if>
      <if test="riskName != null">
        riskName = #{riskName,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        riskCode = #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="dutyName != null">
        dutyName = #{dutyName,jdbcType=VARCHAR},
      </if>
      <if test="dutyCode != null">
        dutyCode = #{dutyCode,jdbcType=VARCHAR},
      </if>
      <if test="amnt != null">
        Amnt = #{amnt,jdbcType=DOUBLE},
      </if>
      <if test="prem != null">
        prem = #{prem,jdbcType=DOUBLE},
      </if>
      <if test="getLimit != null">
        GetLimit = #{getLimit,jdbcType=DOUBLE},
      </if>
      <if test="getLimitType != null">
        GetLimitType = #{getLimitType,jdbcType=VARCHAR},
      </if>
      <if test="getRatio != null">
        GetRatio = #{getRatio,jdbcType=DOUBLE},
      </if>
      <if test="spare1 != null">
        spare1 = #{spare1,jdbcType=VARCHAR},
      </if>
      <if test="spare2 != null">
        spare2 = #{spare2,jdbcType=VARCHAR},
      </if>
      <if test="spare3 != null">
        spare3 = #{spare3,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where edorPlanSN = #{edorPlanSN,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCEdorPlanInfo">
    update fcedorplaninfo
    set planCode = #{planCode,jdbcType=VARCHAR},
      batch = #{batch,jdbcType=VARCHAR},
      planName = #{planName,jdbcType=VARCHAR},
      planObject = #{planObject,jdbcType=VARCHAR},
      riskName = #{riskName,jdbcType=VARCHAR},
      riskCode = #{riskCode,jdbcType=VARCHAR},
      dutyName = #{dutyName,jdbcType=VARCHAR},
      dutyCode = #{dutyCode,jdbcType=VARCHAR},
      Amnt = #{amnt,jdbcType=DOUBLE},
      prem = #{prem,jdbcType=DOUBLE},
      GetLimit = #{getLimit,jdbcType=DOUBLE},
      GetLimitType = #{getLimitType,jdbcType=VARCHAR},
      GetRatio = #{getRatio,jdbcType=DOUBLE},
      spare1 = #{spare1,jdbcType=VARCHAR},
      spare2 = #{spare2,jdbcType=VARCHAR},
      spare3 = #{spare3,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where edorPlanSN = #{edorPlanSN,jdbcType=VARCHAR}
  </update>

  <delete id="deleteBatch" parameterType="java.lang.String" >
    delete from fcedorplaninfo
    where  batch = #{batch,jdbcType=VARCHAR}
  </delete>

  <insert id="insertPlan" parameterType="java.util.List">
    insert into fcedorplaninfo (edorPlanSN, planCode, batch,
    planName, planObject, riskName,
    riskCode, dutyName, dutyCode,
    Amnt, prem, GetLimit,GetLimitType,
    GetRatio, spare1, spare2,
    spare3, Operator, OperatorCom,
    MakeDate, MakeTime, ModifyDate,
    ModifyTime)
    <foreach collection="list" item="item" index="index" separator="union all">
      select #{list[${index}].edorPlanSN},#{list[${index}].planCode},#{list[${index}].batch},
      #{list[${index}].planName},#{list[${index}].planObject},
      #{list[${index}].riskName},#{list[${index}].riskCode},
      #{list[${index}].dutyName},#{list[${index}].dutyCode},
      #{list[${index}].amnt},#{list[${index}].prem},
      #{list[${index}].getLimit},#{list[${index}].getLimitType},#{list[${index}].getRatio},
      #{list[${index}].spare1},#{list[${index}].spare2},
      #{list[${index}].spare3},#{list[${index}].operator},
      #{list[${index}].operatorCom},#{list[${index}].makeDate},
      #{list[${index}].makeTime},#{list[${index}].modifyDate},
      #{list[${index}].modifyTime}
      from dual
    </foreach>
  </insert>

  <select id="exitPlanCodeNum" parameterType="java.util.Map" resultType="int">
    select count(*) from fcedorplaninfo where
    planCode = #{planCode,jdbcType=VARCHAR} 
    and batch = #{batch,jdbcType=VARCHAR}
    and planObject = #{planObject,jdbcType=VARCHAR}
  </select>

  <!-- 查询某一计划下的险种层信息， 一个险种一条数据 -->
  <select id="selectRiskListByPlan" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCEdorPlanInfo">
    select planCode,batch,planName,planObject,riskCode,riskName,sum(Amnt) as Amnt,SUM(prem) as prem from fcEdorPlanInfo where batch = #{batch,jdbcType=VARCHAR} and planCode = #{planCode,jdbcType=VARCHAR} GROUP BY planCode,batch,riskCode;
  </select>

  <!-- 查询某一险种下的责任层信息， 一个责任一条数据 -->
  <select id="selectRiskDutyListByPlanRisk" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCEdorPlanInfo">
    select <include refid="Base_Column_List" /> from fcedorplaninfo where 1=1
    and planCode = #{planCode,jdbcType=VARCHAR}
    and batch = #{batch,jdbcType=VARCHAR}
    and riskCode = #{riskCode,jdbcType=VARCHAR}
  </select>

  <!-- 查询计划的保费 -->
  <select id="selectPermByPlanCode" parameterType="java.util.Map"  resultType="java.lang.Double">
  	select sum(prem) from fcedorplaninfo where 1=1 
  	and planCode = #{planCode,jdbcType=VARCHAR}
    and batch = #{batch,jdbcType=VARCHAR}
  </select>


</mapper>