<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcGrpContactMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcGrpContact">
        <id column="ContactNo" jdbcType="VARCHAR" property="contactNo"/>
        <result column="ContactType" jdbcType="VARCHAR" property="contactType"/>
        <result column="grpNo" jdbcType="VARCHAR" property="grpNo"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="nativeplace" jdbcType="VARCHAR" property="nativeplace"/>
        <result column="idType" jdbcType="VARCHAR" property="idType"/>
        <result column="idNo" jdbcType="VARCHAR" property="idNo"/>
        <result column="idTypeStartDate" jdbcType="DATE" property="idTypeStartDate"/>
        <result column="idTypeEndDate" jdbcType="DATE" property="idTypeEndDate"/>
        <result column="mobilePhone" jdbcType="VARCHAR" property="mobilePhone"/>
        <result column="birthDay" jdbcType="VARCHAR" property="birthDay"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="idImage1" jdbcType="VARCHAR" property="idImage1"/>
        <result column="idImage2" jdbcType="VARCHAR" property="idImage2"/>
        <result column="id_card_front" jdbcType="VARCHAR" property="idCardBack"/>
        <result column="id_card_back" jdbcType="VARCHAR" property="idCardFront"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>


    <resultMap id="ResultMap" type="com.sinosoft.eflex.model.HrRegist">
        <id column="RegistSN" jdbcType="VARCHAR" property="registSN"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="mobilePhone" jdbcType="VARCHAR" property="mobilePhone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="nativeplace" jdbcType="VARCHAR" property="nativeplace"/>
        <result column="idType" jdbcType="VARCHAR" property="idType"/>
        <result column="idNo" jdbcType="VARCHAR" property="idNo"/>
        <result column="idTypeStartDate" jdbcType="DATE" property="idTypeStartDate"/>
        <result column="idTypeEndDate" jdbcType="DATE" property="idTypeEndDate"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="birthday" jdbcType="DATE" property="birthday"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="grpName" jdbcType="VARCHAR" property="grpName"/>
        <result column="grpIdType" jdbcType="VARCHAR" property="grpIdType"/>
        <result column="grpIdNo" jdbcType="VARCHAR" property="grpIdNo"/>
        <result column="zipCode" jdbcType="VARCHAR" property="zipCode"/>
        <result column="grpAddress" jdbcType="VARCHAR" property="grpAddress"/>
        <result column="unifiedsociCode" jdbcType="VARCHAR" property="unifiedsociCode"/>
        <result column="grpType" jdbcType="VARCHAR" property="grpType"/>
        <result column="accName" jdbcType="VARCHAR" property="accName"/>
        <result column="grpBankcode" jdbcType="VARCHAR" property="grpBankcode"/>
        <result column="grpBankaccno" jdbcType="VARCHAR" property="grpBankaccno"/>
        <result column="telphone" jdbcType="VARCHAR" property="telphone"/>
        <result column="regAddress" jdbcType="VARCHAR" property="regAddress"/>
        <result column="clientNo" jdbcType="VARCHAR" property="clientNo"/>
        <result column="corporationMan" jdbcType="VARCHAR" property="corporationMan"/>
        <result column="GrpTypeStartDate" jdbcType="DATE" property="grpTypeStartDate"/>
        <result column="GrpTypeEndDate" jdbcType="DATE" property="grpTypeEndDate"/>
        <result column="GrpEstablishDate" jdbcType="DATE" property="grpEstablishDate"/>
        <result column="GrpScaleType" jdbcType="VARCHAR" property="grpScaleType"/>
        <result column="SociologyPlanSign" jdbcType="VARCHAR" property="sociologyPlanSign"/>
        <result column="RegisteredCapital" jdbcType="VARCHAR" property="registeredCapital"/>
        <result column="GrpCategory" jdbcType="VARCHAR" property="grpCategory"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="auditOpinion" jdbcType="VARCHAR" property="auditOpinion"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
        <result column="grpNo" jdbcType="VARCHAR" property="grpNo"/>
        <result column="grpContactNo" jdbcType="VARCHAR" property="grpContactNo"/>
        <result column="BusinessTerm" jdbcType="DATE" property="businessTerm"/>
        <result column="Trade" jdbcType="VARCHAR" property="trade"/>
        <result column="Peoples" jdbcType="BIGINT" property="peoples"/>
        <result column="shareBusiness" jdbcType="VARCHAR" property="shareBusiness"/>
        <result column="shareholdersName" jdbcType="VARCHAR" property="shareholdersName"/>
    </resultMap>
    <resultMap extends="ResultMap" id="ResultMapWithBLOBs" type="com.sinosoft.eflex.model.HrRegist">
        <result column="idImage1" jdbcType="LONGVARCHAR" property="idImage1"/>
        <result column="idImage2" jdbcType="LONGVARCHAR" property="idImage2"/>
        <result column="grpIDImage1" jdbcType="LONGVARCHAR" property="grpIDImage1"/>
        <result column="legIDImage1" jdbcType="LONGVARCHAR" property="legIDImage1"/>
        <result column="grpIDImage2" jdbcType="LONGVARCHAR" property="grpIDImage2"/>
        <result column="legIDImage2" jdbcType="LONGVARCHAR" property="legIDImage2"/>
        <result column="grp_image_front" jdbcType="LONGVARCHAR" property="grpImageFront"/>
        <result column="grp_image_back" jdbcType="LONGVARCHAR" property="grpImageBack"/>
        <result column="legal_img_front" jdbcType="LONGVARCHAR" property="legalImgFront"/>
        <result column="legal_img_back" jdbcType="LONGVARCHAR" property="legalImgBack"/>
        <result column="id_card_front" jdbcType="LONGVARCHAR" property="idCardBack"/>
        <result column="id_card_back" jdbcType="LONGVARCHAR" property="idCardFront"/>
    </resultMap>

    <sql id="Base_Column_List">
        ContactNo
        , ContactType, grpNo, name, sex, nativeplace, idType, idNo, idTypeStartDate,id_card_front,id_card_back,
    idTypeEndDate, mobilePhone, birthDay, email, idImage1, idImage2, department, Operator, 
    OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcgrpcontact
        where ContactNo = #{contactNo,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fcgrpcontact
        where ContactNo = #{contactNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.sinosoft.eflex.model.FcGrpContact">
        insert into fcgrpcontact (ContactNo, ContactType, grpNo,
                                  name, sex, nativeplace,
                                  idType, idNo, idTypeStartDate,
                                  idTypeEndDate, mobilePhone, birthDay,
                                  email, idImage1, idImage2,
                                  department, Operator, OperatorCom,
                                  MakeDate, MakeTime, ModifyDate,
                                  ModifyTime)
        values (#{contactNo,jdbcType=VARCHAR}, #{contactType,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR},
                #{name,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR}, #{nativeplace,jdbcType=VARCHAR},
                #{idType,jdbcType=VARCHAR}, #{idNo,jdbcType=VARCHAR}, #{idTypeStartDate,jdbcType=DATE},
                #{idTypeEndDate,jdbcType=DATE}, #{mobilePhone,jdbcType=VARCHAR}, #{birthDay,jdbcType=VARCHAR},
                #{email,jdbcType=VARCHAR}, #{idImage1,jdbcType=VARCHAR}, #{idImage2,jdbcType=VARCHAR},
                #{department,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR},
                #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
                #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcGrpContact">
        insert into fcgrpcontact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contactNo != null">
                ContactNo,
            </if>
            <if test="contactType != null">
                ContactType,
            </if>
            <if test="grpNo != null">
                grpNo,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="nativeplace != null">
                nativeplace,
            </if>
            <if test="idType != null">
                idType,
            </if>
            <if test="idNo != null">
                idNo,
            </if>
            <if test="idTypeStartDate != null">
                idTypeStartDate,
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate,
            </if>
            <if test="mobilePhone != null">
                mobilePhone,
            </if>
            <if test="birthDay != null">
                birthDay,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="idImage1 != null">
                idImage1,
            </if>
            <if test="idImage2 != null">
                idImage2,
            </if>
            <if test="idCardFront != null">
                id_card_front ,
            </if>
            <if test="idCardBack != null">
                id_card_back ,
            </if>
            <if test="department != null">
                department,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contactNo != null">
                #{contactNo,jdbcType=VARCHAR},
            </if>
            <if test="contactType != null">
                #{contactType,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeStartDate != null">
                #{idTypeStartDate,jdbcType=DATE},
            </if>
            <if test="idTypeEndDate != null">
                #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="mobilePhone != null">
                #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="birthDay != null">
                #{birthDay,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="idImage1 != null">
                #{idImage1,jdbcType=VARCHAR},
            </if>
            <if test="idImage2 != null">
                #{idImage2,jdbcType=VARCHAR},
            </if>
            <if test="idCardFront != null">
                #{idCardFront,jdbcType=VARCHAR},
            </if>
            <if test="idCardBack != null">
                #{idCardBack,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                #{department,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcGrpContact">
        update fcgrpcontact
        <set>
            <if test="contactType != null">
                ContactType = #{contactType,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                grpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                idType = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                idNo = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeStartDate != null">
                idTypeStartDate = #{idTypeStartDate,jdbcType=DATE},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="mobilePhone != null">
                mobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="birthDay != null">
                birthDay = #{birthDay,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="idImage1 != null">
                idImage1 = #{idImage1,jdbcType=VARCHAR},
            </if>
            <if test="idImage2 != null">
                idImage2 = #{idImage2,jdbcType=VARCHAR},
            </if>
            <if test="idCardFront != null">
                id_card_front = #{idCardFront,jdbcType=VARCHAR},
            </if>
            <if test="idCardBack != null">
                id_card_back = #{idCardBack,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where ContactNo = #{contactNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcGrpContact">
        update fcgrpcontact
        set ContactType     = #{contactType,jdbcType=VARCHAR},
            grpNo           = #{grpNo,jdbcType=VARCHAR},
            name            = #{name,jdbcType=VARCHAR},
            sex             = #{sex,jdbcType=VARCHAR},
            nativeplace     = #{nativeplace,jdbcType=VARCHAR},
            idType          = #{idType,jdbcType=VARCHAR},
            idNo            = #{idNo,jdbcType=VARCHAR},
            idTypeStartDate = #{idTypeStartDate,jdbcType=DATE},
            idTypeEndDate   = #{idTypeEndDate,jdbcType=DATE},
            mobilePhone     = #{mobilePhone,jdbcType=VARCHAR},
            birthDay        = #{birthDay,jdbcType=VARCHAR},
            email           = #{email,jdbcType=VARCHAR},
            idImage1        = #{idImage1,jdbcType=VARCHAR},
            idImage2        = #{idImage2,jdbcType=VARCHAR},
            department      = #{department,jdbcType=VARCHAR},
            Operator        = #{operator,jdbcType=VARCHAR},
            OperatorCom     = #{operatorCom,jdbcType=VARCHAR},
            MakeDate        = #{makeDate,jdbcType=DATE},
            MakeTime        = #{makeTime,jdbcType=VARCHAR},
            ModifyDate      = #{modifyDate,jdbcType=DATE},
            ModifyTime      = #{modifyTime,jdbcType=VARCHAR}
        where ContactNo = #{contactNo,jdbcType=VARCHAR}
    </update>
    <select id="checkIdNoIsExists" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1) from fcgrpcontact
        where 1=1
        and idNo = #{idNo}
        and GrpNo = #{grpNo}
        <if test="contactNo != null and contactNo !=''">
            and ContactNo != #{contactNo}
        </if>
    </select>
    <select id="isExist" parameterType="java.util.HashMap" resultType="java.lang.Integer">
        select
        count(1)
        from fcgrpcontact
        where 1=1
        <if test="contactNo != null and contactNo !=''">
            and ContactNo != #{contactNo}
        </if>
        <if test="idType != null and idType !=''">
            and idType = #{idType}
        </if>
        <if test="idNo != null and idNo !=''">
            and idNo = #{idNo}
        </if>
        <if test="grpNo != null and grpNo !=''">
            and grpNo = #{grpNo}
        </if>
    </select>
    <select id="selectIDFCHrRegistTemp" parameterType="com.sinosoft.eflex.model.HrRegist"
            resultType="java.lang.Integer">
        select count(1)
        from FCHrRegistTemp
        where idType = #{idType}
          and idno = #{idNo}
          and name = #{name}
          and grpName = #{grpName}
          and grpIdNo = #{grpIdNo}
          and checkStatus!='2'
    </select>
    <select id="selectIDFCHrRegistTemp1" parameterType="com.sinosoft.eflex.model.HrRegist"
            resultType="java.lang.Integer">
        select count(1)
        from FCHrRegistTemp
        where idno = #{idNo}
          and checkStatus!='2'
    </select>
    <select id="selectFCHrRegistTempOldInfo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.HrRegist">
        select *
        from FCHrRegistTemp
        where idno = #{idNo}
          and checkStatus!='2' LIMIT 1
    </select>
    <select id="selectFCHrRegistTempOldInfo3" parameterType="com.sinosoft.eflex.model.HrRegist"
            resultType="com.sinosoft.eflex.model.HrRegist">
        select *
        from FCHrRegistTemp
        where idno = #{idNo}
          and name = #{name}
          and idType = #{idType}
          and grpName = #{grpName}
          and grpIdNo = #{grpIdNo}
          and checkStatus!='2'
    </select>
    <select id="selectGrpFCHrRegistTemp" parameterType="com.sinosoft.eflex.model.HrRegist"
            resultType="java.lang.Integer">
        select count(1)
        from FCHrRegistTemp
        where grpIdNo = #{grpIdNo}
          and checkStatus!='2'
    </select>
    <select id="selectAllFCHrRegistTemp" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select *
        from FCHrRegistTemp
        where RegistSN = #{registSN,jdbcType=VARCHAR}
    </select>
    <select id="selectCountFCHrRegistTemp" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(RegistSN)
        from FCHrRegistTemp
        where idNo = #{idNo,jdbcType=VARCHAR}
    </select>
    <select id="selectGrpInfo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcGrpContact">
        select
        <include refid="Base_Column_List"/>
        from fcgrpcontact
        where GrpNo=#{grpNo,jdbcType=VARCHAR}
    </select>
    <select id="selectGrpContact" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcGrpContact">
        select ft.ContactNo,
               fr.ContactType,
               ft.GrpNo,
               ft.Name,
               ft.Sex,
               ft.Nativeplace,
               ft.IDType,
               ft.IDNo,
               ft.idTypeStartDate,
               ft.idTypeEndDate,
               ft.MobilePhone,
               ft.BirthDay,
               ft.EMail,
               ft.Operator,
               ft.OperatorCom,
               ft.MakeDate,
               ft.MakeTime,
               ft.ModifyDate,
               ft.ModifyTime,
               ft.IDImage1,
               ft.IDImage2,
               ft.id_card_front as idCardFront,
               ft.id_card_back  as idCardBack,
               ft.department
        from fcgrpcontact ft,
             fccontactgrprela fr
        where ft.contactno = fr.contactno
          and idNo = #{idNo,jdbcType=VARCHAR} limit 1
    </select>
    <select id="selectContacts" parameterType="java.util.HashMap" resultType="com.sinosoft.eflex.model.FcGrpContact">
        select
        <include refid="Base_Column_List"/>
        from fcgrpcontact
        where 1=1
        <if test="contactNo != null and contactNo !=''">
            and contactNo = #{contactNo}
        </if>
        <if test="IDNo != null and IDNo !=''">
            and IDNo = #{IDNo}
        </if>
        <if test="grpNo != null and grpNo !=''">
            and grpNo = #{grpNo}
        </if>
        <if test="contactType != null and contactType !=''">
            and contactType = #{contactType}
        </if>
    </select>
    <select id="selectContactCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1) from fccontactgrprela
        where 1=1
        <if test="contactNo != null and contactNo !=''">
            and ContactNo = #{contactNo}
        </if>
    </select>
    <select id="selectContactsInfo" parameterType="java.util.HashMap"
            resultType="com.sinosoft.eflex.model.FcGrpContact">
        select
        ft.ContactNo,fr.ContactType,ft.GrpNo,ft.Name,ft.Sex,ft.Nativeplace,ft.IDType, ft.IDNo,
        ft.idTypeEndDate,ft.MobilePhone, ft.BirthDay, ft.EMail,
        ft.Operator, ft.OperatorCom, ft.MakeDate, ft.MakeTime, ft.ModifyDate, ft.ModifyTime,ft.IDImage1,ft.IDImage2
        from fcgrpcontact ft,fccontactgrprela fr
        where ft.contactno=fr.contactno
        <if test="grpNo != null and grpNo !=''">
            and fr.grpNo = #{grpNo}
        </if>
        <if test="contactType != null and contactType !=''">
            and fr.contactType = #{contactType}
        </if>
    </select>
    <select id="selectContactsTemp" parameterType="java.util.HashMap" resultType="com.sinosoft.eflex.model.HrRegist">
        select
        registSN, name, sex, nativeplace,idType, iDNo, idTypeEndDate,idTypeStartDate,mobilePhone,email,
        birthDay,department,
        operator, operatorCom, makeDate, makeTime, modifyDate,
        modifyTime,iDImage1,iDImage2,id_card_back as idCardBack ,id_card_front as idCardFront
        from fchrregisttemp
        where 1=1
        <if test="registSN != null and registSN !=''">
            and registSN = #{registSN}
        </if>
    </select>

    <update id="updateByshareholders" parameterType="com.sinosoft.eflex.model.HrRegist">
        update FCHrRegistTemp
        set ShareBusiness=#{shareBusiness,jdbcType=VARCHAR},
            ShareholdersName=#{shareholdersName,jdbcType=VARCHAR}
        where RegistSN = #{registSN,jdbcType=VARCHAR}
    </update>
    <update id="updateHRInfoByIdno" parameterType="com.sinosoft.eflex.model.HrRegist">
        UPDATE FCHrRegistTemp
        <set>
            `name` = #{name,jdbcType=VARCHAR},
            mobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            nativeplace = #{nativeplace,jdbcType=VARCHAR},
            idType = #{idType,jdbcType=VARCHAR},
            idNo= #{idNo,jdbcType=VARCHAR},
            idTypeEndDate = #{idTypeEndDate,jdbcType=VARCHAR},
            idTypeStartDate = #{idTypeEndDate,jdbcType=VARCHAR},
            sex= #{sex,jdbcType=VARCHAR},
            birthday = #{birthday,jdbcType=VARCHAR},
            idImage1= #{idImage1},
            idImage2 = #{idImage2},
            OperatorCom= #{operatorCom,jdbcType=VARCHAR},
            Operator= #{operator,jdbcType=VARCHAR},
            ModifyDate= #{modifyDate,jdbcType=DATE},
            ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            email = #{email,jdbcType=VARCHAR},
            department = #{department,jdbcType=VARCHAR}
        </set>
        WHERE idno = #{idNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByRegistSN" parameterType="com.sinosoft.eflex.model.HrRegist">
        UPDATE FCHrRegistTemp
        SET `name`            = #{name,jdbcType=VARCHAR},
            mobilePhone       = #{mobilePhone,jdbcType=VARCHAR},
            nativeplace       = #{nativeplace,jdbcType=VARCHAR},
            idType            = #{idType,jdbcType=VARCHAR},
            idNo= #{idNo,jdbcType=VARCHAR},
            idTypeEndDate     = #{idTypeEndDate,jdbcType=VARCHAR},
            sex= #{sex,jdbcType=VARCHAR},
            birthday          = #{birthday,jdbcType=VARCHAR},
            grpName= #{grpName,jdbcType=VARCHAR},
            zipCode= #{zipCode,jdbcType=VARCHAR},
            grpAddress= #{grpAddress,jdbcType=VARCHAR},
            unifiedsociCode= #{unifiedsociCode,jdbcType=VARCHAR},
            grpType= #{grpType,jdbcType=VARCHAR},
            accName= #{accName,jdbcType=VARCHAR},
            grpBankcode= #{grpBankcode,jdbcType=VARCHAR},
            grpBankaccno= #{grpBankaccno,jdbcType=VARCHAR},
            telphone= #{telphone,jdbcType=VARCHAR},
            regAddress= #{regAddress,jdbcType=VARCHAR},
            clientNo= #{clientNo,jdbcType=VARCHAR},
            idImage1= #{idImage1},
            idImage2          = #{idImage2},
            legIDImage1=#{legIDImage1},
            legIDImage2=#{legIDImage2},
            grpIDImage1= #{grpIDImage1},
            grpIDImage2       = #{grpIDImage2},
            corporationMan= #{corporationMan,jdbcType=VARCHAR},
            OperatorCom= #{operatorCom,jdbcType=VARCHAR},
            Operator= #{operator,jdbcType=VARCHAR},
            ModifyDate= #{modifyDate,jdbcType=DATE},
            ModifyTime        = #{modifyTime,jdbcType=VARCHAR},
            BusinessTerm      = #{businessTerm,jdbcType=DATE},
            Trade             = #{trade,jdbcType=VARCHAR},
            Peoples           = #{peoples,jdbcType=VARCHAR},
            GrpTypeStartDate  = #{grpTypeStartDate,jdbcType=DATE},
            GrpTypeEndDate    = #{grpTypeEndDate,jdbcType=DATE},
            GrpEstablishDate  = #{grpEstablishDate,jdbcType=DATE},
            GrpScaleType      = #{grpScaleType,jdbcType=VARCHAR},
            SociologyPlanSign = #{sociologyPlanSign,jdbcType=VARCHAR},
            RegisteredCapital = #{registeredCapital,jdbcType=VARCHAR},
            GrpCategory       = #{grpCategory,jdbcType=VARCHAR}
        where RegistSN = #{registSN,jdbcType=VARCHAR}
    </update>
    <update id="updateHRinfo" parameterType="com.sinosoft.eflex.model.HrRegist">
        update FCHrRegistTemp
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                mobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                idType = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                idNo = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeStartDate != null">
                idTypeStartDate = #{idTypeStartDate,jdbcType=DATE},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="grpName != null">
                grpName = #{grpName,jdbcType=VARCHAR},
            </if>
            <if test="grpIdType != null">
                grpIdType = #{grpIdType,jdbcType=VARCHAR},
            </if>
            <if test="grpIdNo != null">
                grpIdNo = #{grpIdNo,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                zipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="grpAddress != null">
                grpAddress = #{grpAddress,jdbcType=VARCHAR},
            </if>
            <if test="unifiedsociCode != null">
                unifiedsociCode = #{unifiedsociCode,jdbcType=VARCHAR},
            </if>
            <if test="grpType != null">
                grpType = #{grpType,jdbcType=VARCHAR},
            </if>
            <if test="accName != null">
                accName = #{accName,jdbcType=VARCHAR},
            </if>
            <if test="grpBankcode != null">
                grpBankcode = #{grpBankcode,jdbcType=VARCHAR},
            </if>
            <if test="grpBankaccno != null">
                grpBankaccno = #{grpBankaccno,jdbcType=VARCHAR},
            </if>
            <if test="telphone != null">
                telphone = #{telphone,jdbcType=VARCHAR},
            </if>
            <if test="regAddress != null">
                regAddress = #{regAddress,jdbcType=VARCHAR},
            </if>
            <if test="clientNo != null">
                clientNo = #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test="corporationMan != null">
                corporationMan = #{corporationMan,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null">
                checkStatus = #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="grpTypeStartDate != null">
                GrpTypeStartDate = #{grpTypeStartDate,jdbcType=DATE},
            </if>
            <if test="grpTypeEndDate != null">
                GrpTypeEndDate = #{grpTypeEndDate,jdbcType=DATE},
            </if>
            <if test='grpEstablishDate != null'>
                <if test='grpEstablishDate == ""'>
                    grpEstablishDate = null,
                </if>
                <if test='grpEstablishDate != ""'>
                    grpEstablishDate = #{grpEstablishDate},
                </if>
            </if>
            <if test="grpScaleType != null">
                GrpScaleType = #{grpScaleType,jdbcType=VARCHAR},
            </if>
            <if test="sociologyPlanSign != null">
                SociologyPlanSign = #{sociologyPlanSign,jdbcType=VARCHAR},
            </if>
            <if test="registeredCapital != null">
                RegisteredCapital = #{registeredCapital,jdbcType=VARCHAR},
            </if>
            <if test="grpCategory != null">
                GrpCategory = #{grpCategory,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="auditOpinion != null">
                auditOpinion = #{auditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                grpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="grpContactNo != null">
                grpContactNo = #{grpContactNo,jdbcType=VARCHAR},
            </if>
            <if test="businessTerm != null">
                BusinessTerm = #{businessTerm,jdbcType=DATE},
            </if>
            <if test="trade != null">
                Trade = #{trade,jdbcType=VARCHAR},
            </if>
            <if test="peoples != null">
                Peoples = #{peoples,jdbcType=BIGINT},
            </if>
            <if test="shareBusiness != null">
                shareBusiness = #{shareBusiness,jdbcType=VARCHAR},
            </if>
            <if test="shareholdersName != null">
                shareholdersName = #{shareholdersName,jdbcType=VARCHAR},
            </if>
            <if test="idImage1 != null">
                idImage1 = #{idImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="idImage2 != null">
                idImage2 = #{idImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test="grpIDImage1 != null">
                grpIDImage1 = #{grpIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage1 != null">
                legIDImage1 = #{legIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test="grpIDImage2 != null">
                grpIDImage2 = #{grpIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test="legIDImage2 != null">
                legIDImage2 = #{legIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test='grpImageFront != null'>
                grp_image_front = #{grpImageFront,jdbcType=LONGVARCHAR},
            </if>
            <if test='grpImageBack != null'>
                grp_image_back = #{grpImageBack,jdbcType=LONGVARCHAR},
            </if>
            <if test='legalImgFront != null'>
                legal_img_front = #{legalImgFront,jdbcType=LONGVARCHAR},
            </if>
            <if test='legalImgBack != null'>
                legal_img_back = #{legalImgBack,jdbcType=LONGVARCHAR},
            </if>
            <if test="idCardFront != null">
                id_card_front = #{idCardFront,jdbcType=LONGVARCHAR},
            </if>
            <if test="idCardBack != null">
                id_card_back = #{idCardBack,jdbcType=LONGVARCHAR},
            </if>
            <if test='legID != null and legID != ""'>
                LegID = #{legID,jdbcType=VARCHAR},
            </if>
            <if test='legIDType != null and legIDType != ""'>
                LegIDType = #{legIDType,jdbcType=VARCHAR},
            </if>
            <if test='legSex != null and legSex != ""'>
                LegSex = #{legSex,jdbcType=VARCHAR},
            </if>
            <if test='legNationality != null and legNationality != ""'>
                LegNationality = #{legNationality,jdbcType=VARCHAR},
            </if>
            <if test='legBirthday != null and legBirthday != ""'>
                LegBirthday = #{legBirthday,jdbcType=DATE},
            </if>
            <if test='legIDStartDate != null and legIDStartDate != ""'>
                LegIDStartDate = #{legIDStartDate,jdbcType=DATE},
            </if>
            <if test='legIDEndDate != null and legIDEndDate != ""'>
                LegIDEndDate = #{legIDEndDate,jdbcType=DATE},
            </if>
        </set>
        where RegistSN = #{registSN,jdbcType=VARCHAR}
    </update>
    <update id="updateByIdNo" parameterType="java.util.Map">
        update fcgrpcontact
        set MobilePhone=#{mobilePhone}
        where IDNo = #{idNo}
    </update>
    <select id="getGrpContactCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        from fcgrpcontact
        where 1 = 1
          and IDNo = #{IDNo,jdbcType=VARCHAR}
    </select>
    <select id="getmobileByUnifiedsociCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT c.`MobilePhone`
        FROM fcgrpinfo a
                 INNER JOIN fccontactgrprela b ON a.GrpNo = b.`grpNo`
                 INNER JOIN fcgrpcontact c ON b.`contactNo` = c.`ContactNo`
        WHERE a.`UnifiedsociCode` = #{UnifiedsociCode};
    </select>
    <select id="selectContactByNo" parameterType="java.lang.String" resultType="java.util.Map">
        select b.ContactNo,
               b.contactType,
               b.GrpNo,
               a.Name,
               a.Sex,
               a.IDNo,
               a.MobilePhone,
               b.LockState
        from fcgrpcontact a
        inner join fccontactgrprela b on a.ContactNo = b.ContactNo
        where b.GrpNo = #{grpNo}
          and b.ContactNo = #{contactNo}
    </select>
    <select id="selectGrpContactInfo" parameterType="com.sinosoft.eflex.model.FcGrpContact"
            resultType="com.sinosoft.eflex.model.FcGrpContact">
        select
        <include refid="Base_Column_List"/>
        from fcgrpcontact
        where 1=1
        <if test="idType != null and idType !=''">
            and idType = #{idType}
        </if>
        <if test="idNo != null and idNo !=''">
            and idNo = #{idNo}
        </if>
        limit 1
    </select>
    <select id="selectGrpContactInfo1" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FcGrpContact">
        SELECT a.ContactNo,
               a.ContactType,
               a.GrpNo,
               a.NAME,
               a.Sex,
               c.codeName         sexName,
               a.Nativeplace,
               d.CodeName         nativePlaceName,
               a.IDType,
               b.CodeName         idTypeName,
               a.IDNo,
               a.IdTypeStartDate,
               a.IdTypeEndDate,
               a.MobilePhone,
               a.EMail,
               a.BirthDay,
               a.EMail,
               a.Operator,
               a.OperatorCom,
               a.MakeDate,
               a.MakeTime,
               a.ModifyDate,
               a.ModifyTime,
               a.IDImage1,
               a.IDImage2,
               a.id_card_front as idCardFront,
               a.id_card_back  as idCardBack,
               a.department
        FROM fcgrpcontact a
                 left join fdcode b on a.IDType = b.codeKey and b.CodeType = 'IDType'
                 left join fdcode c on a.sex = c.codeKey and c.CodeType = 'sex'
                 left join fdcode d on a.Nativeplace = d.codeKey and d.CodeType = 'Nativeplace'
        WHERE ContactNo = #{contactNo}
    </select>
    <select id="selectGrpContactByIdNoAndGrpNo" resultType="com.sinosoft.eflex.model.FcGrpContact">
        select *
        from fcgrpcontact
        where GrpNo = #{grpNo}
          and idNo = #{idNo}
    </select>
    <select id="selectGrpContactList" resultType="com.sinosoft.eflex.model.FcGrpContact">
        select *
        from fcgrpcontact
        where idNo = #{idNo}
    </select>


    <select id="selectFcGrpContactImageNull" resultType="com.sinosoft.eflex.model.FcGrpContact">
        select ContactNo,
               idImage1,
               idImage2,
               id_card_front as idCardFront,
               id_card_back  as idCardBack
        from fcgrpcontact
        where id_card_front is null
           or id_card_back is null
    </select>


    <delete id="deleteByContactNo">
        delete  from fcgrpcontact where contactNo != #{contactNo}   and   IDNo=#{idNo}
    </delete>
</mapper>