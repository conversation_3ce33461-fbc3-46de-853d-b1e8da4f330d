<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDRiskDutyInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDRiskDutyInfo">
    <id column="Riskcode" jdbcType="VARCHAR" property="riskcode" />
    <id column="DutyCode" jdbcType="VARCHAR" property="dutyCode" />
    <result column="DutyName" jdbcType="VARCHAR" property="dutyName" />
    <result column="GetLimit" jdbcType="DECIMAL" property="getLimit" />
    <result column="PayDay" jdbcType="DECIMAL" property="payDay" />
    <result column="NoGetDay" jdbcType="DECIMAL" property="noGetDay" />
    <result column="GetRate" jdbcType="DECIMAL" property="getRate" />

    <result column="FloatRate" jdbcType="DECIMAL" property="floatRate" />
    <result column="CalRule" jdbcType="VARCHAR" property="calRule" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    Riskcode, DutyCode, DutyName, GetLimit, PayDay, NoGetDay, GetRate, FloatRate, CalRule, 
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDRiskDutyInfoKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fdriskdutyinfo
    where Riskcode = #{riskcode,jdbcType=VARCHAR}
      and DutyCode = #{dutyCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDRiskDutyInfoKey">
    delete from fdriskdutyinfo
    where Riskcode = #{riskcode,jdbcType=VARCHAR}
      and DutyCode = #{dutyCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDRiskDutyInfo">
    insert into fdriskdutyinfo (Riskcode, DutyCode, DutyName, 
      GetLimit, PayDay, NoGetDay, 
      GetRate, FloatRate, CalRule, 
      Operator, OperatorCom, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{riskcode,jdbcType=VARCHAR}, #{dutyCode,jdbcType=VARCHAR}, #{dutyName,jdbcType=VARCHAR}, 
      #{getLimit,jdbcType=DECIMAL}, #{payDay,jdbcType=DECIMAL}, #{noGetDay,jdbcType=DECIMAL}, 
      #{getRate,jdbcType=DECIMAL}, #{floatRate,jdbcType=DECIMAL}, #{calRule,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDRiskDutyInfo">
    insert into fdriskdutyinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="riskcode != null">
        Riskcode,
      </if>
      <if test="dutyCode != null">
        DutyCode,
      </if>
      <if test="dutyName != null">
        DutyName,
      </if>
      <if test="getLimit != null">
        GetLimit,
      </if>
      <if test="payDay != null">
        PayDay,
      </if>
      <if test="noGetDay != null">
        NoGetDay,
      </if>
      <if test="getRate != null">
        GetRate,
      </if>
      <if test="floatRate != null">
        FloatRate,
      </if>
      <if test="calRule != null">
        CalRule,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="riskcode != null">
        #{riskcode,jdbcType=VARCHAR},
      </if>
      <if test="dutyCode != null">
        #{dutyCode,jdbcType=VARCHAR},
      </if>
      <if test="dutyName != null">
        #{dutyName,jdbcType=VARCHAR},
      </if>
      <if test="getLimit != null">
        #{getLimit,jdbcType=DECIMAL},
      </if>
      <if test="payDay != null">
        #{payDay,jdbcType=DECIMAL},
      </if>
      <if test="noGetDay != null">
        #{noGetDay,jdbcType=DECIMAL},
      </if>
      <if test="getRate != null">
        #{getRate,jdbcType=DECIMAL},
      </if>
      <if test="floatRate != null">
        #{floatRate,jdbcType=DECIMAL},
      </if>
      <if test="calRule != null">
        #{calRule,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDRiskDutyInfo">
    update fdriskdutyinfo
    <set>
      <if test="dutyName != null">
        DutyName = #{dutyName,jdbcType=VARCHAR},
      </if>
      <if test="getLimit != null">
        GetLimit = #{getLimit,jdbcType=DECIMAL},
      </if>
      <if test="payDay != null">
        PayDay = #{payDay,jdbcType=DECIMAL},
      </if>
      <if test="noGetDay != null">
        NoGetDay = #{noGetDay,jdbcType=DECIMAL},
      </if>
      <if test="getRate != null">
        GetRate = #{getRate,jdbcType=DECIMAL},
      </if>
      <if test="floatRate != null">
        FloatRate = #{floatRate,jdbcType=DECIMAL},
      </if>
      <if test="calRule != null">
        CalRule = #{calRule,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where Riskcode = #{riskcode,jdbcType=VARCHAR}
      and DutyCode = #{dutyCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDRiskDutyInfo">
    update fdriskdutyinfo
    set DutyName = #{dutyName,jdbcType=VARCHAR},
      GetLimit = #{getLimit,jdbcType=DECIMAL},
      PayDay = #{payDay,jdbcType=DECIMAL},
      NoGetDay = #{noGetDay,jdbcType=DECIMAL},
      GetRate = #{getRate,jdbcType=DECIMAL},
      FloatRate = #{floatRate,jdbcType=DECIMAL},
      CalRule = #{calRule,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where Riskcode = #{riskcode,jdbcType=VARCHAR}
      and DutyCode = #{dutyCode,jdbcType=VARCHAR}
  </update>
  <select id="selectByDutyCode" parameterType="String" resultType="java.util.HashMap">
    select a.Riskcode, b.RiskName, a.DutyCode, a.DutyName
    from fdriskdutyinfo a
    LEFT JOIN fdriskinfo b on a.Riskcode = b.RiskCode
    where a.DutyCode = #{params.dutyCode,jdbcType=VARCHAR}
     and a.RiskCode = #{params.riskCode,jdbcType=VARCHAR}
  </select>

  <select id="selectDutyName" parameterType="java.util.Map" resultType="java.util.Map">
    select a.Riskcode, b.RiskName, a.DutyCode, a.DutyName,a.DutyRange
    from fdriskdutyinfo a
    LEFT JOIN fdriskinfo b on a.Riskcode = b.RiskCode
    where a.DutyCode = #{dutyCode,jdbcType=VARCHAR}
     and a.RiskCode = #{riskCode,jdbcType=VARCHAR}
  </select>

  <select id="getOptDutyByRiskCode" parameterType="java.lang.String" resultType="java.util.Map">
    SELECT d.riskCode as riskCode,d.riskName as riskName,r.dutyCode as dutyCode,r.dutyRange as dutyRange,r.dutyType as dutyType FROM fdriskdutyinfo r
    inner JOIN fdriskinfo d on r.riskCode = d.riskCode
    WHERE r.RiskCode =#{riskCode} AND r.DutyType = '1'
      <if test=' riskCode == "15070" '>
        <choose>
          <when test=' dutyCode == "GD0050" '>
            and r.DutyCode = 'GD0055'
          </when>
          <when test=' dutyCode == "GD0051" '>
            and r.DutyCode = 'GD0056'
          </when>
          <when test=' dutyCode == "GD0052" '>
            and r.DutyCode = 'GD0057'
          </when>
          <when test=' dutyCode == "GD0053" '>
            and r.DutyCode = 'GD0058'
          </when>
          <when test=' dutyCode == "GD0054" '>
            and r.DutyCode = 'GD0059'
          </when>
        </choose>
      </if>
  </select>

  <select id="getRisk_15070ByEnsureCode" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FDRiskDutyInfo">
    SELECT * FROM fdriskdutyinfo WHERE RiskCode = '15070' AND DutyType = '0' AND DutyCode NOT IN
    (
      SELECT (CASE RiskType WHEN '01' THEN 'GD0053' WHEN '02' THEN 'GD0051'  WHEN '03' THEN 'GD0052'  WHEN '04' THEN 'GD0050' ELSE 'GD0054'  END) AS DutyCode FROM FcPlanRiskInfo WHERE EnsureCode = #{ensureCode}  AND RiskCode = '15070'
    )
  </select>

    <select id="getRisk_15070ByEnsureCodeAndPlanCode" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FDRiskDutyInfo">
        SELECT *
        FROM fdriskdutyinfo WHERE RiskCode = '15070' AND DutyType = '0' AND DutyCode NOT IN
        (
        SELECT DutyCode from fcplanriskduty WHERE EnsureCode = #{ensureCode} AND RiskCode = '15070'
        <if test=" planCode != null and planCode != '' ">
            AND planCode = #{planCode}
        </if>
        )
    </select>

  <select id="getRiskAndDutyInfo" parameterType="java.lang.String" resultType="java.util.Map">
    SELECT r.riskCode AS riskCode,
    r.riskName AS riskName,
    d.DutyCode AS dutyCode,
    d.DutyName AS dutyName,
    d.DutyRange AS dutyRange,
    d.DutyType AS dutyType FROM fdriskinfo r
    INNER JOIN fdriskdutyinfo d ON r.riskCode = d.riskCode
    WHERE r.riskCode = #{riskCode} AND d.DutyType='0'
    <if test=" dutyCode != null and dutyCode != ''">
      AND d.DutyCode = #{dutyCode}
    </if>
    ORDER BY d.DutyCode ASC
  </select>

  <select id="getRiskAndDutyInfoByDutyCode" parameterType="java.util.Map" resultType="java.util.Map">
    SELECT a.`AmountGrageCode` AS amountGrageCode,
            a.`AmountGrageName` AS amountGrageName,
            a.`RiskCode` AS riskCode,
            d.`RiskName` AS riskName,
            a.`DutyCode` AS dutyCode,
            a.`DutyName` AS dutyName,
            a.`DutyRange` AS dutyRange,
            a.`DutyType` AS dutyType,
            a.`DiscountRatio` AS discountRatio,
            a.`WaitingPeriod` AS waitingPeriod,
            a.`maxGetDay` AS maxGetDay,
            a.`SpecialAgreement` AS specialAgreement,
            a.`AnnualTimeDeduction` AS annualTimeDeduction,
            a.`Amnt` AS amnt,
            a.RiskType as riskType FROM FcDutyAmountGrade a
    INNER JOIN fdriskinfo d ON a.`RiskCode` = d.`RiskCode`
    WHERE a.EnsureCode = #{ensureCode} and a.`RiskCode` = #{riskCode} AND a.DutyCode = #{dutyCode}
  </select>
  <select id="selectByRiskCodeAndDutyCode" resultType="java.lang.String">
    select DutyName from fdriskdutyinfo
    where Riskcode = #{riskCode,jdbcType=VARCHAR}
    and DutyCode = #{dutyCode,jdbcType=VARCHAR}
  </select>
  <select id="selectByRiskCode" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FDRiskDutyInfo">
    select  a.riskCode AS riskCode,
            b.riskName AS riskName,
            a.dutyCode AS dutyCode,
            case when #{riskCode} = '17050' then '一般医疗、恶性肿瘤医疗保险金'
            else a.dutyName end AS dutyName
        from fdriskdutyinfo a
        left join fdriskinfo b on a.riskCode = b.riskCode
        WHERE a.riskCode = #{riskCode,jdbcType=VARCHAR} order by dutyCode
  </select>

  <select id="selectByRiskCodes"  resultType="com.sinosoft.eflex.model.FDRiskDutyInfo">
    select  a.riskCode AS riskCode,
            b.riskName AS riskName,
            a.dutyCode AS dutyCode,
            a.dutyName  AS dutyName
    from fdriskdutyinfo a
           left join fdriskinfo b on a.riskCode = b.riskCode
    WHERE
    <foreach collection="riskCodes" item="item" index="index" separator="or" open="(" close=")">
      a.riskCode = #{item,jdbcType=VARCHAR}
    </foreach>
    order by dutyCode
  </select>

    <select id="getRisk_15070ByDutyCode" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FDRiskDutyInfo">
    select a.*,b.riskName from fdriskdutyinfo a
        left join fdriskinfo b on a.riskCode = b.riskCode
        where a.riskCode = '15070' and
       case when #{dutyCode} = 'GD0050' then dutyCode in ('GD0050','GD0055')
		   when #{dutyCode} = 'GD0051' then dutyCode in ('GD0051','GD0056')
		   when #{dutyCode} = 'GD0052' then dutyCode in ('GD0052','GD0057')
		   when #{dutyCode} = 'GD0053' then dutyCode in ('GD0053','GD0058')
		   when #{dutyCode} = 'GD0054' then dutyCode in ('GD0054','GD0059')
	 END
  </select>
</mapper>