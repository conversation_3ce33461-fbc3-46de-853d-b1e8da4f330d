<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCHealthDesignMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCHealthDesign">
        <id column="DesignNo" jdbcType="VARCHAR" property="designNo"/>
        <result column="DesignName" jdbcType="VARCHAR" property="designName"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>


    <sql id="Base_Column_List">
    DesignNo, DesignName, Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fchealthdesign
        where DesignNo = #{designNo,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fchealthdesign
    where DesignNo = #{designNo,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCHealthDesign">
    insert into fchealthdesign (DesignNo, DesignName, Operator, 
      OperatorCom, MakeDate, MakeTime,
      ModifyDate, ModifyTime)
    values (#{designNo,jdbcType=VARCHAR}, #{designName,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCHealthDesign">
        insert into fchealthdesign
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="designNo != null">
                DesignNo,
            </if>
            <if test="designName != null">
                DesignName,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="designNo != null">
                #{designNo,jdbcType=VARCHAR},
            </if>
            <if test="designName != null">
                #{designName,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCHealthDesign">
        update fchealthdesign
        <set>
            <if test="designName != null">
                DesignName = #{designName,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where DesignNo = #{designNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCHealthDesign">
    update fchealthdesign
    set DesignName = #{designName,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where DesignNo = #{designNo,jdbcType=VARCHAR}
  </update>
    <select id="selectPlanHealthInfoByParams" resultType="java.util.HashMap">
        SELECT c.designNo,c.designName,b.designStatus,
        (SELECT CodeName FROM fdcode WHERE CodeType = 'designStatus' AND CodeKey = b.designStatus) AS designStatusName,
        b.returnReason,a.ensureCode,a.ensureName,d.grpName,d.GrpNo
        FROM fcensure a,fcplanhealthdesignrela b,fchealthdesign c,fcgrpinfo d,fdagentinfo e
        WHERE a.ensureCode = b.ensureCode
        AND a.grpNo = d.grpNo
        AND b.designNo = c.designNo
        AND a.ClientNo = e.AgentCode
        AND e.manageCom like #{manageCom}"%"
        <if test="designNo!=null and designNo !=''">
            AND c.DesignNo = #{designNo}
        </if>
        <if test="designName!=null and designName !=''">
            AND c.DesignName LIKE CONCAT(CONCAT('%', #{designName}), '%')
        </if>
        <if test="designStatus!=null and designStatus !=''">
            AND b.DesignStatus =#{designStatus}
        </if>
        <if test="ensureCode!=null and ensureCode !=''">
            AND a.Ensurecode = #{ensureCode}
        </if>
        <if test="ensureName!=null and ensureName !=''">
            AND a.EnsureName LIKE CONCAT(CONCAT('%', #{ensureName}), '%')
        </if>
        <if test="grpName!=null and grpName !=''">
            AND d.GrpName LIKE CONCAT(CONCAT('%', #{grpName}), '%')
        </if>
        <if test=' isReal == "1" '>
            AND b.DesignStatus IN (1,3,4) ORDER BY FIELD(b.designStatus,'3','1','4'),c.designNo DESC
        </if>
        <if test=' isReal == "0" '>
            order by b.designStatus,c.designNo DESC
        </if>
    </select>
    <select id="selectHealthDesignDetail" resultType="com.sinosoft.eflex.model.FCHealthDesignDetail">
        SELECT c.HealthDesignNo, c.RiskCode,
        (SELECT GradeLevelCode FROM FCBusPersonType
        where EnsureCode = #{ensureCode} AND OrderNum = c.GradeLevelTopLimit) AS GradeLevelTopLimit,
        (SELECT GradeLevelCode FROM FCBusPersonType
        where EnsureCode = #{ensureCode} AND OrderNum = c.GradeLevelLowLimit) AS GradeLevelLowLimit,
        c.AmntTopLimit,c.AmntLowLimit,
        c.AgeTopLimit,c.AgeLowLimit,
        (SELECT codename FROM fdcode WHERE codetype = 'sex' AND codekey = c.Sex) AS Sex,
        (SELECT codename FROM fdcode WHERE codetype = 'InsuredType' AND codekey = c.InsuredType) AS InsuredType
        FROM fchealthdesign a ,fchealthdesigndetailrela b,fchealthdesigndetail c
        WHERE a.DesignNo = b.DesignNo
        AND b.HealthDesignNo = c.HealthDesignNo
        AND a.DesignNo = #{designNo}
    </select>
    <select id="selectHealthDesignDetailByGrpNo" resultType="com.sinosoft.eflex.model.FCHealthDesignDetail">
        SELECT c.HealthDesignNo, c.RiskCode,
        (SELECT GradeLevelCode FROM FCBusPersonType
        where GrpNo = #{grpNo} AND OrderNum = c.GradeLevelTopLimit) AS GradeLevelTopLimit,
        (SELECT GradeLevelCode FROM FCBusPersonType
        where GrpNo = #{grpNo} AND OrderNum = c.GradeLevelLowLimit) AS GradeLevelLowLimit,
        c.AmntTopLimit,c.AmntLowLimit,
        c.AgeTopLimit,c.AgeLowLimit,
        (SELECT codename FROM fdcode WHERE codetype = 'sex' AND codekey = c.Sex) AS Sex,
        (SELECT codename FROM fdcode WHERE codetype = 'InsuredType' AND codekey = c.InsuredType) AS InsuredType
        FROM fchealthdesign a ,fchealthdesigndetailrela b,fchealthdesigndetail c
        WHERE a.DesignNo = b.DesignNo
        AND b.HealthDesignNo = c.HealthDesignNo
        AND a.DesignNo = #{designNo}
    </select>
    <select id="selectPlanByEnsureCode" resultType="com.sinosoft.eflex.model.FCPlanHealthDesignRela">
        select EnsureCode,DesignNo from fcplanhealthdesignrela
        where EnsureCode = #{ensureCode}
    </select>
    <select id="selectDetailByDesignNo" resultType="java.lang.Integer">
        select count(1) FROM fchealthdesign a ,fchealthdesigndetailrela b,fchealthdesigndetail c
        WHERE a.DesignNo = b.DesignNo
        AND b.HealthDesignNo = c.HealthDesignNo
        AND a.DesignNo = #{designNo}
    </select>
    <select id="selectPlanByDesignNo" resultType="java.util.Map">
        select a.EnsureCode,a.DesignNo,a.DesignStatus,b.DesignName from fcplanhealthdesignrela a,fchealthdesign b
        where a.DesignNo = b.DesignNo
        and a.DesignNo = #{designNo}
    </select>


</mapper>