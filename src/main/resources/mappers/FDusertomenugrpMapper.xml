<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDusertomenugrpMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDusertomenugrpKey">
    <id column="UserNo" jdbcType="VARCHAR" property="userNo" />
    <id column="MenuGrpCode" jdbcType="VARCHAR" property="menuGrpCode" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDusertomenugrpKey">
    delete from fdusertomenugrp
    where UserNo = #{userNo,jdbcType=VARCHAR}
      and MenuGrpCode = #{menuGrpCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDusertomenugrpKey">
    insert into fdusertomenugrp (UserNo, MenuGrpCode)
    values (#{userNo,jdbcType=VARCHAR}, #{menuGrpCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDusertomenugrpKey">
    insert into fdusertomenugrp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userNo != null">
        UserNo,
      </if>
      <if test="menuGrpCode != null">
        MenuGrpCode,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userNo != null">
        #{userNo,jdbcType=VARCHAR},
      </if>
      <if test="menuGrpCode != null">
        #{menuGrpCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="findMenuInfo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FDusertomenugrpKey">
    select * FROM fdusertomenugrp where UserNo = #{userNo,jdbcType=VARCHAR}
  </select>
    <delete id="deleteByUserNo" parameterType="java.lang.String">
    delete from fdusertomenugrp where UserNo = #{userNo}
  </delete>
</mapper>