<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCDefaultPlanMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCDefaultPlan">
    <id column="SerialNo" jdbcType="VARCHAR" property="serialNo" />
    <result column="personId" jdbcType="VARCHAR" property="personId" />
    <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <result column="AppntYear" jdbcType="VARCHAR" property="appntYear" />
    <result column="PlanCode" jdbcType="DATE" property="planCode" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    SerialNo, personId, EnsureCode, AppntYear, PlanCode, Operator, OperatorCom, MakeDate, 
    MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcdefaultplan
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcdefaultplan
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCDefaultPlan">
    insert into fcdefaultplan (SerialNo, personId, EnsureCode, 
      AppntYear, PlanCode, Operator, 
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{serialNo,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR}, #{ensureCode,jdbcType=VARCHAR}, 
      #{appntYear,jdbcType=VARCHAR}, #{planCode,jdbcType=DATE}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCDefaultPlan">
    insert into fcdefaultplan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        SerialNo,
      </if>
      <if test="personId != null">
        personId,
      </if>
      <if test="ensureCode != null">
        EnsureCode,
      </if>
      <if test="appntYear != null">
        AppntYear,
      </if>
      <if test="planCode != null">
        PlanCode,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=VARCHAR},
      </if>
      <if test="ensureCode != null">
        #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="appntYear != null">
        #{appntYear,jdbcType=VARCHAR},
      </if>
      <if test="planCode != null">
        #{planCode,jdbcType=DATE},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCDefaultPlan">
    update fcdefaultplan
    <set>
      <if test="personId != null">
        personId = #{personId,jdbcType=VARCHAR},
      </if>
      <if test="ensureCode != null">
        EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="appntYear != null">
        AppntYear = #{appntYear,jdbcType=VARCHAR},
      </if>
      <if test="planCode != null">
        PlanCode = #{planCode,jdbcType=DATE},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCDefaultPlan">
    update fcdefaultplan
    set personId = #{personId,jdbcType=VARCHAR},
      EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      AppntYear = #{appntYear,jdbcType=VARCHAR},
      PlanCode = #{planCode,jdbcType=DATE},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </update>

  <select id="selectDefaultPlans" parameterType="java.util.HashMap" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fcdefaultplan where 1=1
    <if test="personId != null and personId != '' ">
      and personId = #{personId,jdbcType=VARCHAR}
    </if>
    <if test="ensureCode != null and ensureCode !=''">
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </if>
    <if test="planCode != null and planCode != '' ">
      and PlanCode = #{planCode,jdbcType=DATE}
    </if>
  </select>

 <!-- 弹性计划查询>个人默认计划总保费 -->
 <select id="selectPayment" parameterType="java.util.Map" resultType="java.util.Map">
 	select plan.TotalPrem as totalPrem
 	from fcdefaultplan de
 	join fcensureplan plan
 	on de.PlanCode = plan.PlanCode and de.EnsureCode=plan.EnsureCode
 	where
 	de.personId = #{personId,jdbcType=VARCHAR}
 	and de.EnsureCode = #{ensureCode ,jdbcType=VARCHAR}
 </select>

  <select id="selectByPerNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
      a.SerialNo, a.personId, a.EnsureCode, a.AppntYear, a.PlanCode, a.Operator
    FROM  fcdefaultplan a , fcstafffamilyrela b
    where a.personId = b.PersonID and b.Relation = '0' and b.PerNo = #{perNo,jdbcType=VARCHAR};
  </select>

  <delete id="deleteByParams" parameterType="java.util.HashMap">
    delete from fcdefaultplan
    where 1=1
    <if test="personId != null and personId != '' ">
      and personId = #{personId,jdbcType=VARCHAR}
    </if>
    <if test="ensureCode != null">
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </if>
    <if test="planCode != null and planCode != '' ">
      and PlanCode = #{planCode,jdbcType=DATE}
    </if>
  </delete>
  <delete id="deleteByPerId">
    delete from fcdefaultplan
    where personId = #{personId,jdbcType=VARCHAR}
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
  </delete>
  <select id="selectDoublePlan" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCDefaultPlan">
    select
    <include refid="Base_Column_List" />
    from fcdefaultplan
    <where>
      <if test="ensureCode != null and ensureCode !=''">
         EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      </if>
      <if test="personId != null and personId != '' ">
        and personId = #{personId,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
  <!-- relation='0' 本人 -->
  <select id="getEmployPersonid" parameterType="java.lang.String" resultType="java.lang.String">
  	select a.personid from fcstafffamilyrela a 
  	where a.perno = #{perNo,jdbcType=VARCHAR} and a.relation='0'
  	
  </select>

  <select id="selectDoublePlanByPerNo" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCDefaultPlan">
    select
    a.SerialNo, a.personId, a.EnsureCode, a.AppntYear, a.PlanCode, a.Operator
    from fcdefaultplan a,fcstafffamilyrela b
    <where>
        a.personId =b.PersonID and  b.Relation = '0'
      <if test="perNo != null and perNo !=''">
        and b.PerNo = #{perNo,jdbcType=VARCHAR}
      </if>
      <if test="ensureCode != null and ensureCode !=''">
        and a.EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>