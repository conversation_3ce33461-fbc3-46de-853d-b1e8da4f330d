<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCEdorAddInsuredMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCEdorAddInsured">
      <id column="PlusInsuredSN" jdbcType="VARCHAR" property="plusInsuredSN"/>
      <result column="GrpContNo" jdbcType="VARCHAR" property="grpContNo"/>
      <result column="GrpNo" jdbcType="VARCHAR" property="grpNo"/>
      <result column="Batch" jdbcType="VARCHAR" property="batch"/>
      <result column="Name" jdbcType="VARCHAR" property="name"/>
      <result column="Nativeplace" jdbcType="VARCHAR" property="nativeplace"/>
      <result column="Birthday" jdbcType="VARCHAR" property="birthday"/>
      <result column="Sex" jdbcType="VARCHAR" property="sex"/>
      <result column="IdType" jdbcType="VARCHAR" property="idType"/>
      <result column="IdNo" jdbcType="VARCHAR" property="idNo"/>
      <result column="idTypeEndDate" jdbcType="DATE" property="idTypeEndDate"/>
      <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
      <result column="RelationToAppnt" jdbcType="VARCHAR" property="relationToAppnt"/>
      <result column="PlusEffectDate" jdbcType="VARCHAR" property="plusEffectDate"/>
      <result column="InsuYear" jdbcType="VARCHAR" property="insuYear"/>
      <result column="InsuYearFlag" jdbcType="VARCHAR" property="insuYearFlag"/>
      <result column="PlanCode" jdbcType="VARCHAR" property="planCode"/>
      <result column="MedicareStatus" jdbcType="VARCHAR" property="medicareStatus"/>
      <result column="EdorType" jdbcType="VARCHAR" property="edorType"/>
      <result column="JobType" jdbcType="VARCHAR" property="jobType"/>
      <result column="JobCode" jdbcType="VARCHAR" property="jobCode"/>
      <result column="staffName" jdbcType="VARCHAR" property="staffName"/>
      <result column="MainIdNo" jdbcType="VARCHAR" property="mainIdNo"/>
      <result column="MainIdType" jdbcType="VARCHAR" property="mainIdType"/>
      <result column="relation" jdbcType="VARCHAR" property="relation"/>
      <result column="PayMethod" jdbcType="VARCHAR" property="payMethod"/>
      <result column="ComPayment" jdbcType="DOUBLE" property="comPayment"/>
      <result column="PerPayment" jdbcType="DOUBLE" property="perPayment"/>
      <result column="DebitPayBank" jdbcType="VARCHAR" property="debitPayBank"/>
      <result column="DebitPayCode" jdbcType="VARCHAR" property="debitPayCode"/>
      <result column="DebitPayName" jdbcType="VARCHAR" property="debitPayName"/>
      <result column="DeathBenefiName" jdbcType="VARCHAR" property="deathBenefiName"/>
      <result column="DeathBenefiRelation" jdbcType="VARCHAR" property="deathBenefiRelation"/>
      <result column="AutoInsured" jdbcType="VARCHAR" property="autoInsured"/>
      <result column="subsidiaryInsuredFlag" jdbcType="VARCHAR" property="subsidiaryInsuredFlag"/>
      <result column="TrialStatus" jdbcType="VARCHAR" property="trialStatus"/>
      <result column="IsError" jdbcType="VARCHAR" property="isError"/>
      <result column="ErrorDesc" jdbcType="VARCHAR" property="errorDesc"/>
      <result column="PremSource" jdbcType="VARCHAR" property="premSource"/>
      <result column="AccountType" jdbcType="VARCHAR" property="accountType"/>
      <result column="Prem" jdbcType="VARCHAR" property="prem"/>
      <result column="ContNo" jdbcType="VARCHAR" property="contNo"/>
      <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
      <result column="DeathBenefiPrem" jdbcType="DOUBLE" property="deathBenefiPrem"/>
      <result column="Operator" jdbcType="VARCHAR" property="operator"/>
      <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
      <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
      <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
      <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
      <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
  </resultMap>
  <sql id="Base_Column_List">
      PlusInsuredSN
      , GrpContNo, GrpNo, Batch, Name, Nativeplace, Birthday, Sex, IdType,
    IdNo, idTypeEndDate, mobile, RelationToAppnt, PlusEffectDate, InsuYear, InsuYearFlag, 
    PlanCode, MedicareStatus, EdorType, JobType, JobCode, staffName, MainIdNo, MainIdType, 
    relation, PayMethod, ComPayment, PerPayment, DebitPayBank, DebitPayCode, DebitPayName, 
    DeathBenefiName, DeathBenefiRelation, AutoInsured, subsidiaryInsuredFlag, TrialStatus, 
    IsError, ErrorDesc, PremSource, AccountType, Prem, ContNo, remarks, DeathBenefiPrem, 
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcedoraddinsured
    where PlusInsuredSN = #{plusInsuredSN,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcedoraddinsured
    where PlusInsuredSN = #{plusInsuredSN,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured">
      insert into fcedoraddinsured (PlusInsuredSN, GrpContNo, GrpNo,
                                    Batch, Name, Nativeplace,
                                    Birthday, Sex, IdType,
                                    IdNo, idTypeEndDate, mobile,
                                    RelationToAppnt, PlusEffectDate, InsuYear,
                                    InsuYearFlag, PlanCode, MedicareStatus,
                                    EdorType, JobType, JobCode,
                                    staffName, MainIdNo, MainIdType,
                                    relation, PayMethod, ComPayment,
                                    PerPayment, DebitPayBank, DebitPayCode,
                                    DebitPayName, DeathBenefiName, DeathBenefiRelation,
                                    AutoInsured, subsidiaryInsuredFlag, TrialStatus,
                                    IsError, ErrorDesc, PremSource,
                                    AccountType, Prem, ContNo,
                                    remarks, DeathBenefiPrem, Operator,
                                    OperatorCom, MakeDate, MakeTime,
                                    ModifyDate, ModifyTime)
      values (#{plusInsuredSN,jdbcType=VARCHAR}, #{grpContNo,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR},
              #{batch,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{nativeplace,jdbcType=VARCHAR},
              #{birthday,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR}, #{idType,jdbcType=VARCHAR},
              #{idNo,jdbcType=VARCHAR}, #{idTypeEndDate,jdbcType=DATE}, #{mobile,jdbcType=VARCHAR},
              #{relationToAppnt,jdbcType=VARCHAR}, #{plusEffectDate,jdbcType=VARCHAR}, #{insuYear,jdbcType=VARCHAR},
              #{insuYearFlag,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, #{medicareStatus,jdbcType=VARCHAR},
              #{edorType,jdbcType=VARCHAR}, #{jobType,jdbcType=VARCHAR}, #{jobCode,jdbcType=VARCHAR},
              #{staffName,jdbcType=VARCHAR}, #{mainIdNo,jdbcType=VARCHAR}, #{mainIdType,jdbcType=VARCHAR},
              #{relation,jdbcType=VARCHAR}, #{payMethod,jdbcType=VARCHAR}, #{comPayment,jdbcType=DOUBLE},
              #{perPayment,jdbcType=DOUBLE}, #{debitPayBank,jdbcType=VARCHAR}, #{debitPayCode,jdbcType=VARCHAR},
              #{debitPayName,jdbcType=VARCHAR}, #{deathBenefiName,jdbcType=VARCHAR},
              #{deathBenefiRelation,jdbcType=VARCHAR},
              #{autoInsured,jdbcType=VARCHAR}, #{subsidiaryInsuredFlag,jdbcType=VARCHAR},
              #{trialStatus,jdbcType=VARCHAR},
              #{isError,jdbcType=VARCHAR}, #{errorDesc,jdbcType=VARCHAR}, #{premSource,jdbcType=VARCHAR},
              #{accountType,jdbcType=VARCHAR}, #{prem,jdbcType=VARCHAR}, #{contNo,jdbcType=VARCHAR},
              #{remarks,jdbcType=VARCHAR}, #{deathBenefiPrem,jdbcType=DOUBLE}, #{operator,jdbcType=VARCHAR},
              #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
              #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured">
    insert into fcedoraddinsured
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="plusInsuredSN != null">
        PlusInsuredSN,
      </if>
      <if test="grpContNo != null">
        GrpContNo,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
        <if test="batch != null">
            Batch,
        </if>
        <if test="name != null">
            Name,
        </if>
        <if test="nativeplace != null">
            Nativeplace,
        </if>
        <if test="birthday != null">
            Birthday,
        </if>
        <if test="sex != null">
            Sex,
        </if>
        <if test="idType != null">
            IdType,
        </if>
        <if test="idNo != null">
            IdNo,
        </if>
        <if test='idTypeEndDate != null and idTypeEndDate != "" '>
            idTypeEndDate,
        </if>
        <if test="mobile != null">
            mobile,
        </if>
        <if test="relationToAppnt != null">
            RelationToAppnt,
        </if>
        <if test="plusEffectDate != null">
            PlusEffectDate,
        </if>
        <if test="insuYear != null">
            InsuYear,
        </if>
        <if test="insuYearFlag != null">
            InsuYearFlag,
        </if>
        <if test="planCode != null">
            PlanCode,
        </if>
        <if test="medicareStatus != null">
            MedicareStatus,
        </if>
        <if test="edorType != null">
            EdorType,
        </if>
      <if test="jobType != null">
        JobType,
      </if>
      <if test="jobCode != null">
        JobCode,
      </if>
      <if test="staffName != null">
        staffName,
      </if>
      <if test="mainIdNo != null">
        MainIdNo,
      </if>
      <if test="mainIdType != null">
        MainIdType,
      </if>
      <if test="relation != null">
        relation,
      </if>
      <if test="payMethod != null">
        PayMethod,
      </if>
      <if test="comPayment != null">
        ComPayment,
      </if>
      <if test="perPayment != null">
        PerPayment,
      </if>
      <if test="debitPayBank != null">
        DebitPayBank,
      </if>
      <if test="debitPayCode != null">
        DebitPayCode,
      </if>
      <if test="debitPayName != null">
        DebitPayName,
      </if>
        <if test="deathBenefiName != null">
            DeathBenefiName,
        </if>
        <if test="deathBenefiRelation != null">
            DeathBenefiRelation,
        </if>
        <if test="autoInsured != null">
            AutoInsured,
        </if>
        <if test="subsidiaryInsuredFlag != null">
            subsidiaryInsuredFlag,
        </if>
        <if test="trialStatus != null">
            TrialStatus,
        </if>
        <if test="isError != null">
            IsError,
        </if>
        <if test="errorDesc != null">
            ErrorDesc,
        </if>
        <if test="premSource != null">
            PremSource,
        </if>
        <if test="accountType != null">
            AccountType,
        </if>
        <if test="prem != null">
            Prem,
        </if>
        <if test="contNo != null">
            ContNo,
        </if>
        <if test="remarks != null">
            remarks,
        </if>
        <if test="deathBenefiPrem != null">
            DeathBenefiPrem,
        </if>
        <if test="operator != null">
            Operator,
        </if>
        <if test="operatorCom != null">
            OperatorCom,
        </if>
        <if test="makeDate != null">
            MakeDate,
        </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="plusInsuredSN != null">
        #{plusInsuredSN,jdbcType=VARCHAR},
      </if>
      <if test="grpContNo != null">
        #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
        <if test="batch != null">
            #{batch,jdbcType=VARCHAR},
        </if>
        <if test="name != null">
            #{name,jdbcType=VARCHAR},
        </if>
        <if test="nativeplace != null">
            #{nativeplace,jdbcType=VARCHAR},
        </if>
        <if test="birthday != null">
            #{birthday,jdbcType=VARCHAR},
        </if>
        <if test="sex != null">
            #{sex,jdbcType=VARCHAR},
        </if>
        <if test="idType != null">
            #{idType,jdbcType=VARCHAR},
        </if>
        <if test="idNo != null">
            #{idNo,jdbcType=VARCHAR},
        </if>
        <if test='idTypeEndDate != null and idTypeEndDate != "" '>
            #{idTypeEndDate},
        </if>
        <if test="mobile != null">
            #{mobile,jdbcType=VARCHAR},
        </if>
        <if test="relationToAppnt != null">
            #{relationToAppnt,jdbcType=VARCHAR},
        </if>
        <if test="plusEffectDate != null">
            #{plusEffectDate,jdbcType=VARCHAR},
        </if>
        <if test="insuYear != null">
            #{insuYear,jdbcType=VARCHAR},
        </if>
        <if test="insuYearFlag != null">
            #{insuYearFlag,jdbcType=VARCHAR},
        </if>
        <if test="planCode != null">
            #{planCode,jdbcType=VARCHAR},
        </if>
        <if test="medicareStatus != null">
            #{medicareStatus,jdbcType=VARCHAR},
        </if>
        <if test="edorType != null">
            #{edorType,jdbcType=VARCHAR},
        </if>
        <if test="jobType != null">
        #{jobType,jdbcType=VARCHAR},
      </if>
      <if test="jobCode != null">
        #{jobCode,jdbcType=VARCHAR},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="mainIdNo != null">
        #{mainIdNo,jdbcType=VARCHAR},
      </if>
      <if test="mainIdType != null">
        #{mainIdType,jdbcType=VARCHAR},
      </if>
      <if test="relation != null">
        #{relation,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="comPayment != null">
        #{comPayment,jdbcType=DOUBLE},
      </if>
      <if test="perPayment != null">
        #{perPayment,jdbcType=DOUBLE},
      </if>
      <if test="debitPayBank != null">
        #{debitPayBank,jdbcType=VARCHAR},
      </if>
      <if test="debitPayCode != null">
        #{debitPayCode,jdbcType=VARCHAR},
      </if>
      <if test="debitPayName != null">
        #{debitPayName,jdbcType=VARCHAR},
      </if>
      <if test="deathBenefiName != null">
        #{deathBenefiName,jdbcType=VARCHAR},
      </if>
        <if test="deathBenefiRelation != null">
            #{deathBenefiRelation,jdbcType=VARCHAR},
        </if>
        <if test="autoInsured != null">
            #{autoInsured,jdbcType=VARCHAR},
        </if>
        <if test="subsidiaryInsuredFlag != null">
            #{subsidiaryInsuredFlag,jdbcType=VARCHAR},
        </if>
        <if test="trialStatus != null">
            #{trialStatus,jdbcType=VARCHAR},
        </if>
        <if test="isError != null">
            #{isError,jdbcType=VARCHAR},
        </if>
        <if test="errorDesc != null">
            #{errorDesc,jdbcType=VARCHAR},
        </if>
        <if test="premSource != null">
            #{premSource,jdbcType=VARCHAR},
        </if>
        <if test="accountType != null">
            #{accountType,jdbcType=VARCHAR},
        </if>
        <if test="prem != null">
            #{prem,jdbcType=VARCHAR},
        </if>
        <if test="contNo != null">
            #{contNo,jdbcType=VARCHAR},
        </if>
        <if test="remarks != null">
            #{remarks,jdbcType=VARCHAR},
        </if>
        <if test="deathBenefiPrem != null">
            #{deathBenefiPrem,jdbcType=DOUBLE},
        </if>
        <if test="operator != null">
            #{operator,jdbcType=VARCHAR},
        </if>
        <if test="operatorCom != null">
            #{operatorCom,jdbcType=VARCHAR},
        </if>
        <if test="makeDate != null">
            #{makeDate,jdbcType=DATE},
        </if>
        <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
          #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured">
    update fcedoraddinsured
    <set>
      <if test="grpContNo != null">
        GrpContNo = #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
        <if test="batch != null">
            Batch = #{batch,jdbcType=VARCHAR},
        </if>
        <if test="name != null">
            Name = #{name,jdbcType=VARCHAR},
        </if>
        <if test="nativeplace != null">
            Nativeplace = #{nativeplace,jdbcType=VARCHAR},
        </if>
        <if test="birthday != null">
            Birthday = #{birthday,jdbcType=VARCHAR},
        </if>
        <if test="sex != null">
            Sex = #{sex,jdbcType=VARCHAR},
        </if>
        <if test="idType != null">
            IdType = #{idType,jdbcType=VARCHAR},
        </if>
        <if test="idNo != null">
            IdNo = #{idNo,jdbcType=VARCHAR},
        </if>
        <if test="idTypeEndDate != null">
            idTypeEndDate = #{idTypeEndDate,jdbcType=DATE},
        </if>
        <if test="mobile != null">
            mobile = #{mobile,jdbcType=VARCHAR},
        </if>
        <if test="relationToAppnt != null">
            RelationToAppnt = #{relationToAppnt,jdbcType=VARCHAR},
        </if>
        <if test="plusEffectDate != null">
            PlusEffectDate = #{plusEffectDate,jdbcType=VARCHAR},
        </if>
        <if test="insuYear != null">
            InsuYear = #{insuYear,jdbcType=VARCHAR},
        </if>
        <if test="insuYearFlag != null">
            InsuYearFlag = #{insuYearFlag,jdbcType=VARCHAR},
        </if>
        <if test="planCode != null">
            PlanCode = #{planCode,jdbcType=VARCHAR},
        </if>
        <if test="medicareStatus != null">
            MedicareStatus = #{medicareStatus,jdbcType=VARCHAR},
        </if>
        <if test="edorType != null">
            EdorType = #{edorType,jdbcType=VARCHAR},
        </if>
      <if test="jobType != null">
        JobType = #{jobType,jdbcType=VARCHAR},
      </if>
      <if test="jobCode != null">
        JobCode = #{jobCode,jdbcType=VARCHAR},
      </if>
      <if test="staffName != null">
        staffName = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="mainIdNo != null">
        MainIdNo = #{mainIdNo,jdbcType=VARCHAR},
      </if>
      <if test="mainIdType != null">
        MainIdType = #{mainIdType,jdbcType=VARCHAR},
      </if>
      <if test="relation != null">
        relation = #{relation,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        PayMethod = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="comPayment != null">
        ComPayment = #{comPayment,jdbcType=DOUBLE},
      </if>
      <if test="perPayment != null">
        PerPayment = #{perPayment,jdbcType=DOUBLE},
      </if>
      <if test="debitPayBank != null">
        DebitPayBank = #{debitPayBank,jdbcType=VARCHAR},
      </if>
      <if test="debitPayCode != null">
        DebitPayCode = #{debitPayCode,jdbcType=VARCHAR},
      </if>
      <if test="debitPayName != null">
        DebitPayName = #{debitPayName,jdbcType=VARCHAR},
      </if>
      <if test="deathBenefiName != null">
        DeathBenefiName = #{deathBenefiName,jdbcType=VARCHAR},
      </if>
        <if test="deathBenefiRelation != null">
            DeathBenefiRelation = #{deathBenefiRelation,jdbcType=VARCHAR},
        </if>
        <if test="autoInsured != null">
            AutoInsured = #{autoInsured,jdbcType=VARCHAR},
        </if>
        <if test="subsidiaryInsuredFlag != null">
            subsidiaryInsuredFlag = #{subsidiaryInsuredFlag,jdbcType=VARCHAR},
        </if>
        <if test="trialStatus != null">
            TrialStatus = #{trialStatus,jdbcType=VARCHAR},
        </if>
        <if test="isError != null">
            IsError = #{isError,jdbcType=VARCHAR},
        </if>
        <if test="errorDesc != null">
            ErrorDesc = #{errorDesc,jdbcType=VARCHAR},
        </if>
        <if test="premSource != null">
            PremSource = #{premSource,jdbcType=VARCHAR},
        </if>
        <if test="accountType != null">
            AccountType = #{accountType,jdbcType=VARCHAR},
        </if>
        <if test="prem != null">
            Prem = #{prem,jdbcType=VARCHAR},
        </if>
        <if test="contNo != null">
            ContNo = #{contNo,jdbcType=VARCHAR},
        </if>
        <if test="remarks != null">
            remarks = #{remarks,jdbcType=VARCHAR},
        </if>
        <if test="deathBenefiPrem != null">
            DeathBenefiPrem = #{deathBenefiPrem,jdbcType=DOUBLE},
        </if>
        <if test="operator != null">
            Operator = #{operator,jdbcType=VARCHAR},
        </if>
        <if test="operatorCom != null">
            OperatorCom = #{operatorCom,jdbcType=VARCHAR},
        </if>
        <if test="makeDate != null">
            MakeDate = #{makeDate,jdbcType=DATE},
        </if>
        <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
          ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where PlusInsuredSN = #{plusInsuredSN,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured">
      update fcedoraddinsured
      set GrpContNo             = #{grpContNo,jdbcType=VARCHAR},
          GrpNo                 = #{grpNo,jdbcType=VARCHAR},
          Batch                 = #{batch,jdbcType=VARCHAR},
          Name                  = #{name,jdbcType=VARCHAR},
          Nativeplace           = #{nativeplace,jdbcType=VARCHAR},
          Birthday              = #{birthday,jdbcType=VARCHAR},
          Sex                   = #{sex,jdbcType=VARCHAR},
          IdType                = #{idType,jdbcType=VARCHAR},
          IdNo                  = #{idNo,jdbcType=VARCHAR},
          idTypeEndDate         = #{idTypeEndDate,jdbcType=DATE},
          mobile                = #{mobile,jdbcType=VARCHAR},
          RelationToAppnt       = #{relationToAppnt,jdbcType=VARCHAR},
          PlusEffectDate        = #{plusEffectDate,jdbcType=VARCHAR},
          InsuYear              = #{insuYear,jdbcType=VARCHAR},
          InsuYearFlag          = #{insuYearFlag,jdbcType=VARCHAR},
          PlanCode              = #{planCode,jdbcType=VARCHAR},
          MedicareStatus        = #{medicareStatus,jdbcType=VARCHAR},
          EdorType              = #{edorType,jdbcType=VARCHAR},
          JobType               = #{jobType,jdbcType=VARCHAR},
          JobCode               = #{jobCode,jdbcType=VARCHAR},
          staffName             = #{staffName,jdbcType=VARCHAR},
          MainIdNo              = #{mainIdNo,jdbcType=VARCHAR},
          MainIdType            = #{mainIdType,jdbcType=VARCHAR},
          relation              = #{relation,jdbcType=VARCHAR},
          PayMethod             = #{payMethod,jdbcType=VARCHAR},
          ComPayment            = #{comPayment,jdbcType=DOUBLE},
          PerPayment            = #{perPayment,jdbcType=DOUBLE},
          DebitPayBank          = #{debitPayBank,jdbcType=VARCHAR},
          DebitPayCode          = #{debitPayCode,jdbcType=VARCHAR},
          DebitPayName          = #{debitPayName,jdbcType=VARCHAR},
          DeathBenefiName       = #{deathBenefiName,jdbcType=VARCHAR},
          DeathBenefiRelation   = #{deathBenefiRelation,jdbcType=VARCHAR},
          AutoInsured           = #{autoInsured,jdbcType=VARCHAR},
          subsidiaryInsuredFlag = #{subsidiaryInsuredFlag,jdbcType=VARCHAR},
          TrialStatus           = #{trialStatus,jdbcType=VARCHAR},
          IsError               = #{isError,jdbcType=VARCHAR},
          ErrorDesc             = #{errorDesc,jdbcType=VARCHAR},
          PremSource            = #{premSource,jdbcType=VARCHAR},
          AccountType           = #{accountType,jdbcType=VARCHAR},
          Prem                  = #{prem,jdbcType=VARCHAR},
          ContNo                = #{contNo,jdbcType=VARCHAR},
          remarks               = #{remarks,jdbcType=VARCHAR},
          DeathBenefiPrem       = #{deathBenefiPrem,jdbcType=DOUBLE},
          Operator              = #{operator,jdbcType=VARCHAR},
          OperatorCom           = #{operatorCom,jdbcType=VARCHAR},
          MakeDate              = #{makeDate,jdbcType=DATE},
          MakeTime              = #{makeTime,jdbcType=VARCHAR},
          ModifyDate            = #{modifyDate,jdbcType=DATE},
          ModifyTime            = #{modifyTime,jdbcType=VARCHAR}
      where PlusInsuredSN = #{plusInsuredSN,jdbcType=VARCHAR}
  </update>
    <insert id="insertNext" parameterType="java.util.List">
        insert into fcedoraddinsured (PlusInsuredSN, GrpContNo, GrpNo,
        Batch, Name,Nativeplace,Sex, IdNo,
        IdType, IdTypeEndDate,Birthday, mobile,
        RelationToAppnt, PlusEffectDate,
        InsuYear,InsuYearFlag,PlanCode,
        MedicareStatus, EdorType, TrialStatus,
        JobType, JobCode, staffName,
        MainIdNo, MainIdType, relation,
        PayMethod, ComPayment, PerPayment,
        DebitPayBank, DebitPayCode, DebitPayName,
        DeathBenefiName, DeathBenefiRelation, AutoInsured,
        remarks, DeathBenefiPrem,SubsidiaryInsuredFlag,Operator,
        OperatorCom, MakeDate, MakeTime,
        ModifyDate, ModifyTime)
        <foreach collection="list" index="index" item="item" separator="union all">
            select
            #{list[${index}].plusInsuredSN},#{list[${index}].grpContNo},#{list[${index}].grpNo},#{list[${index}].batch},#{list[${index}].name},#{list[${index}].nativeplace},#{list[${index}].sex},
            #{list[${index}].idNo},#{list[${index}].idType},#{list[${index}].idTypeEndDate},#{list[${index}].birthday},#{list[${index}].mobile},#{list[${index}].relationToAppnt},#{list[${index}].plusEffectDate},
            #{list[${index}].insuYear},#{list[${index}].insuYearFlag},
            #{list[${index}].planCode},#{list[${index}].medicareStatus},#{list[${index}].edorType},#{list[${index}].trialStatus},#{list[${index}].jobType},#{list[${index}].jobCode},
            #{list[${index}].staffName},#{list[${index}].mainIdNo},#{list[${index}].mainIdType},#{list[${index}].relation},#{list[${index}].payMethod},#{list[${index}].comPayment},#{list[${index}].perPayment},
            #{list[${index}].debitPayBank},#{list[${index}].debitPayCode},#{list[${index}].debitPayName},#{list[${index}].deathBenefiName},#{list[${index}].deathBenefiRelation},#{list[${index}].autoInsured},
            #{list[${index}].remarks},#{list[${index}].deathBenefiPrem},#{list[${index}].subsidiaryInsuredFlag},#{list[${index}].operator},#{list[${index}].operatorCom},#{list[${index}].makeDate},#{list[${index}].makeTime},#{list[${index}].modifyDate},#{list[${index}].modifyTime}

            from dual
        </foreach>
    </insert>

    <select id="selectInsuredCount" parameterType="java.util.Map" resultType="int">
        select COUNT(*)
        from fcedoraddinsured c
        where c.GrpNo = #{grpNo,jdbcType=VARCHAR}
          and c.Batch = #{batch,jdbcType=VARCHAR}
    </select>


    <select id="getInsuredExist" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.EdorAddInsuredInfo">
        select a.plusInsuredSN,
               a.name,
               a.nativeplace,
               d.codeName nativeplaceName,
               a.idType,
               c.codeName idTypeName,
               a.idNo,
               a.idTypeEndDate,
               a.sex,
               e.codeName sexName,
               a.birthday,
               a.planCode,
               a.plusEffectDate,
               a.jobType,
               f.codeName jobTypeName,
               a.jobCode,
               b.codeName jobCodeName,
               a.medicareStatus,
               g.codeName medicareStatusName,
               a.mobile,
               a.relation,
               i.codeName relationName,
               a.staffName,
               a.mainIdType,
               h.codeName mainIdTypeName,
               a.mainIdNo
        from fcedoraddinsured a
                 left join fdcode b on b.codeType = 'OccupationDetail' and b.codeKey = a.jobCode
                 left join fdcode c on c.codeType = 'IDType' and c.codeKey = a.IDType
                 left join fdcode d on d.codeType = 'nativeplace' and d.codeKey = a.nativeplace
                 left join fdcode e on e.codeType = 'sex' and e.codeKey = a.sex
                 left join fdcode f on f.codeType = 'OccupationType' and f.codeKey = a.jobType
                 left join fdcode g on g.codeType = 'isHas' and g.codeKey = a.medicareStatus
                 left join fdcode h on h.codeType = 'IDType' and h.codeKey = a.mainIdType
                 left join fdcode i on i.codeType = 'relation' and i.codeKey = a.relation
        where a.Batch = #{batch,jdbcType=VARCHAR}
          and a.TrialStatus = '0'
    </select>
    <select id="getInsuredExist1" parameterType="com.sinosoft.eflex.model.edor.SelectfcedoraddinsuredReq"
            resultType="com.sinosoft.eflex.model.EdorAddInsuredInfo">
        select
        @i := ifnull(@i, 0) + 1 as orderNumber,
        t1.*
        from
        (select
        a.plusInsuredSN,
        a.name,
        a.nativeplace,
        d.codeName nativeplaceName,
        a.idType,
        c.codeName idTypeName,
        a.idNo,
        a.idTypeEndDate,
        a.sex,
        e.codeName sexName,
        a.birthday,
        a.planCode,
        a.plusEffectDate,
        a.jobType,
        f.codeName jobTypeName,
        a.jobCode,
        b.codeName jobCodeName,
        a.medicareStatus,
        g.codeName medicareStatusName,
        a.mobile,
        a.relation,
        i.codeName relationName,
        a.staffName,
        a.mainIdType,
        h.codeName mainIdTypeName,
        a.mainIdNo,
        a.TrialStatus,
        a.accountType,
        j.codeName accountTypeName,
        a.premSource,
        k.codeName premSourceName,
        a.Prem prem,
        a.isError,
        a.ErrorDesc
        from fcedoraddinsured a
        left join fdcode b on b.codeType = 'OccupationDetail' and b.codeKey = a.jobCode
        left join fdcode c on c.codeType = 'IDType' and c.codeKey = a.IDType
        left join fdcode d on d.codeType = 'nativeplace' and d.codeKey = a.nativeplace
        left join fdcode e on e.codeType = 'sex' and e.codeKey = a.sex
        left join fdcode f on f.codeType = 'OccupationType' and f.codeKey = a.jobType
        left join fdcode g on g.codeType = 'isHas' and g.codeKey = a.medicareStatus
        left join fdcode h on h.codeType = 'IDType' and h.codeKey = a.mainIdType
        left join fdcode i on i.codeType = 'relation' and i.codeKey = a.relation
        left join fdcode j on j.codeType = 'accountType' and j.codeKey = a.accountType
        left join fdcode k on k.codeType = 'premSource' and k.codeKey = a.premSource
        where a.Batch=#{addInsuredBatch,jdbcType=VARCHAR}
        and (
        a.TrialStatus in
        <foreach collection="trialStates" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="isError != null and isError != '' ">
            or a.isError = #{isError}
        </if>
        )
        <if test="name != null and name != '' ">
            and a.Name like concat('%',#{name},'%')
        </if>
        <if test="idType != null and idType != '' ">
            and a.idType = #{idType}
        </if>
        <if test="idNo != null and idNo != '' ">
            and a.idNo = #{idNo}
        </if>
        <if test="staffName != null and staffName != '' ">
            and a.staffName like concat('%',#{staffName},'%')
        </if>
        <if test="mainIdType != null and mainIdType != '' ">
            and a.mainIdType = #{mainIdType}
        </if>
        <if test="mainIdNo != null and mainIdNo != '' ">
            and a.mainIdNo = #{mainIdNo}
        </if>
        order by a.isError desc,a.ModifyDate desc,HOUR(a.ModifyTime) desc,MINUTE(a.ModifyTime) desc) t1,(select @i := 0)
        as it
    </select>

    <select id="getAddInsuredInfo" resultType="com.sinosoft.eflex.model.FCEdorAddInsured">
        select
        <include refid="Base_Column_List"/>
        from fcedoraddinsured where Batch=#{addBatch,jdbcType=VARCHAR} and GrpNo = #{grpNo,jdbcType=VARCHAR}
        and trialStatus='1' and isError = '0' ORDER BY RELATION , PLUSINSUREDSN
    </select>
    <delete id="deleteAddinsured" parameterType="java.lang.String">
        delete
        from fcedoraddinsured
        where batch = #{batch,jdbcType=VARCHAR}
    </delete>

    <select id="checkIdNoIsExists" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        from fcEdorAddInsured
        where idno = #{params.idNo,jdbcType=VARCHAR}
          AND batch = #{params.batch,jdbcType=VARCHAR}
    </select>

    <!-- 根据证件号判断临时表表是否已存在 -->
    <select id="checkIdNoIsExists2" parameterType="java.util.List" resultType="java.lang.Integer">
        select count(1) from fcEdorAddInsured
        where batch = #{batch,jdbcType=VARCHAR} AND idno in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{list[${index}].idno,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 判断保全增人表是否存在证件号存在，但是其他四要素不同的数据 -->
    <select id="checkOtherIsEsists" parameterType="java.util.List" resultMap="BaseResultMap">
        select IDNo from fcperinfo where batch = #{batch,jdbcType=VARCHAR} AND
        <foreach collection="list" item="item" index="index" separator="or">
            (idno=#{list[${index}].idno,jdbcType=VARCHAR}
            and (name &lt;&gt; #{list[${index}].name,jdbcType=VARCHAR} or
            sex &lt;&gt; #{list[${index}].sex,jdbcType=VARCHAR} or
            date_format(birthday,'%Y-%m-%d') &lt;&gt;
            date_format(str_to_date(#{list[${index}].birthday,jdbcType=VARCHAR},'%Y-%m-%d'),'%Y-%m-%d') or
            idtype &lt;&gt; #{list[${index}].idtype,jdbcType=VARCHAR}))
        </foreach>
    </select>


    <insert id="insertfcEdorAddInsured" parameterType="java.util.List">
        insert into fcedoraddinsured (
        PlusInsuredSN,Batch,GrpContNo,GrpNo,Name, Sex,
        BirthDay,mobile,
        nativeplace,IdType,IdNo,
        idTypeEndDate,JobType,JobCode,
        MedicareStatus,PlanCode,PlusEffectDate,
        Operator,TrialStatus,MakeDate,MakeTime,ModifyDate,ModifyTime)
        <foreach collection="list" item="item" index="index" separator="union all">
            select #{list[${index}].plusInsuredSN},
            #{list[${index}].batch},
            #{list[${index}].grpContNo},
            #{list[${index}].grpNo},
            #{list[${index}].name},
            #{list[${index}].sex},
            #{list[${index}].birthday},
            #{list[${index}].mobile},
            #{list[${index}].nativeplace},
            #{list[${index}].idType},
            #{list[${index}].idNo},
            #{list[${index}].idTypeEndDate},
            #{list[${index}].jobType},
            #{list[${index}].jobCode},
            #{list[${index}].medicareStatus},
            #{list[${index}].planCode},
            #{list[${index}].plusEffectDate},
            #{list[${index}].operator},
            #{list[${index}].trialStatus},
            #{list[${index}].makeDate},
            #{list[${index}].makeTime},
            #{list[${index}].modifyDate},
            #{list[${index}].modifyTime}
            from dual
        </foreach>
    </insert>

    <update id="updatefcEdorAddInsured" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured">
        update fcedoraddinsured
        <set>
            <if test="grpContNo != null">
                GrpContNo = #{grpContNo,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                GrpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="batch != null">
                Batch = #{batch,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                Nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                IdNo = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                IdType = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                IdTypeEndDate = #{idTypeEndDate,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                Birthday = #{birthday,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="relationToAppnt != null">
                RelationToAppnt = #{relationToAppnt,jdbcType=VARCHAR},
            </if>
            <if test="plusEffectDate != null">
                PlusEffectDate = #{plusEffectDate,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                PlanCode = #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="medicareStatus != null">
                MedicareStatus = #{medicareStatus,jdbcType=VARCHAR},
            </if>
            <if test="edorType != null">
                EdorType = #{edorType,jdbcType=VARCHAR},
            </if>
            <if test="trialStatus != null">
                TrialStatus = #{trialStatus,jdbcType=VARCHAR},
            </if>
            <if test="jobType != null">
                JobType = #{jobType,jdbcType=VARCHAR},
            </if>
            <if test="jobCode != null">
                JobCode = #{jobCode,jdbcType=VARCHAR},
            </if>
            <if test="staffName != null">
                staffName = #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="mainIdNo != null">
                MainIdNo = #{mainIdNo,jdbcType=VARCHAR},
            </if>
            <if test="mainIdType != null">
                MainIdType = #{mainIdType,jdbcType=VARCHAR},
            </if>
            <if test="relation != null">
                relation = #{relation,jdbcType=VARCHAR},
            </if>
            <if test="payMethod != null">
                PayMethod = #{payMethod,jdbcType=VARCHAR},
            </if>
            <if test="comPayment != null">
                ComPayment = #{comPayment,jdbcType=DOUBLE},
            </if>
            <if test="perPayment != null">
                PerPayment = #{perPayment,jdbcType=DOUBLE},
            </if>
            <if test="debitPayBank != null">
                DebitPayBank = #{debitPayBank,jdbcType=VARCHAR},
            </if>
            <if test="debitPayCode != null">
                DebitPayCode = #{debitPayCode,jdbcType=VARCHAR},
            </if>
            <if test="debitPayName != null">
                DebitPayName = #{debitPayName,jdbcType=VARCHAR},
            </if>
            <if test="deathBenefiName != null">
                DeathBenefiName = #{deathBenefiName,jdbcType=VARCHAR},
            </if>
            <if test="deathBenefiRelation != null">
                DeathBenefiRelation = #{deathBenefiRelation,jdbcType=VARCHAR},
            </if>
            <if test="autoInsured != null">
                AutoInsured = #{autoInsured,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deathBenefiPrem != null">
                DeathBenefiPrem = #{deathBenefiPrem,jdbcType=DOUBLE},
            </if>
            <if test="subsidiaryInsuredFlag != null">
                SubsidiaryInsuredFlag = #{subsidiaryInsuredFlag,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where PlusInsuredSN = #{plusInsuredSN,jdbcType=VARCHAR}
    </update>

    <delete id="deleteEdorAddInsured" parameterType="java.lang.String">
        delete
        from fcedoraddinsured
        where PlusInsuredSN = #{plusInsuredSN}
    </delete>
    <delete id="deleteEdorAddInsuredList" parameterType="java.util.List">
        delete from fcedoraddinsured where PlusInsuredSN in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="checkOneIdNoIsExists" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured"
            resultType="java.lang.Integer">
        select count(1)
        from fcedoraddinsured
        where idno = #{idNo,jdbcType=VARCHAR}
          and batch = #{batch,jdbcType=VARCHAR}
    </select>
    <select id="checkOneIdNoIsExistsUpdate" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured"
            resultType="java.lang.Integer">
        select count(1)
        from fcedoraddinsured
        where idno = #{idNo,jdbcType=VARCHAR}
          and batch = #{batch,jdbcType=VARCHAR}
          and plusInsuredSN != #{plusInsuredSN}
    </select>

    <select id="checkOneOtherIsEsists" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1)
        from fcedoraddinsured
        where idno = #{params.idNo,jdbcType=VARCHAR}
          and batch = #{params.batch,jdbcType=VARCHAR}
          and (name &lt;&gt; #{params.name,jdbcType=VARCHAR} or
               sex &lt;&gt; #{params.sex,jdbcType=VARCHAR} or
               date_format(birthday, '%Y-%m-%d') &lt;&gt; #{params.birthday,jdbcType=VARCHAR} or
               idtype &lt;&gt; #{params.idType,jdbcType=VARCHAR})
    </select>

    <select id="selectByPhone" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured" resultType="java.lang.Integer">
        select count(1)
        from fcedoraddinsured
        where batch = #{batch,jdbcType=VARCHAR}
          and mobile = #{mobile,jdbcType=VARCHAR}
    </select>
    <select id="selectByPhoneUpdate" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured"
            resultType="java.lang.Integer">
        select count(1)
        from fcedoraddinsured
        where batch = #{batch,jdbcType=VARCHAR}
          and mobile = #{mobile,jdbcType=VARCHAR}
          and plusInsuredSN != #{plusInsuredSN}
    </select>
    <select id="selectMainEdorInsured" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured"
            resultType="com.sinosoft.eflex.model.FCEdorAddInsured">
        select
        <include refid="Base_Column_List"/>
        from fcedoraddinsured a
        where a.GrpContNo = #{grpContNo}
        <if test="batch != null">
            and a.Batch = #{batch}
        </if>
        <if test="name != null">
            and a.name = #{name}
        </if>
        <if test="idType != null">
            and a.idType = #{idType}
        </if>
        <if test="idNo != null">
            and a.IdNo = #{idNo}
        </if>
        and a.subsidiaryInsuredFlag = #{subsidiaryInsuredFlag}
    </select>
    <select id="selectSubEdorInsured" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured"
            resultType="com.sinosoft.eflex.model.FCEdorAddInsured">
        select
        <include refid="Base_Column_List"/>
        from fcedoraddinsured
        where GrpContNo = #{grpContNo}
        and Batch = #{batch}
        and staffName = #{staffName}
        and MainIdType = #{mainIdType}
        and MainIdNo = #{mainIdNo}
    </select>
    <select id="selectEdorAddInsuredLst" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured"
            resultType="com.sinosoft.eflex.model.FCEdorAddInsured">
        select
        <include refid="Base_Column_List"/>
        from fcedoraddinsured
        where GrpContNo = #{grpContNo,jdbcType=VARCHAR}
        and Batch=#{batch,jdbcType=VARCHAR}
    </select>
    <select id="selectEdorAddInsured" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured"
            resultType="com.sinosoft.eflex.model.FCEdorAddInsured">
        select
        <include refid="Base_Column_List"/>
        from fcedoraddinsured
        where GrpContNo = #{grpContNo,jdbcType=VARCHAR}
        and idno=#{idNo,jdbcType=VARCHAR}
    </select>
    <select id="selectEdorAddInsuredForUpdate" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured"
            resultType="com.sinosoft.eflex.model.FCEdorAddInsured">
        select
        <include refid="Base_Column_List"/>
        from fcedoraddinsured
        where GrpContNo = #{grpContNo,jdbcType=VARCHAR}
        and idno = #{idNo,jdbcType=VARCHAR}
        and plusInsuredSN != #{plusInsuredSN}
    </select>
    <update id="updateEdorAddInsured" parameterType="com.sinosoft.eflex.model.FCEdorAddInsured">
        update fcedoraddinsured
        <set>
            <if test="grpNo != null">
                GrpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                NAME = #{name,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                Birthday = #{birthday,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="relationToAppnt != null">
                RelationToAppnt = #{relationToAppnt,jdbcType=VARCHAR},
            </if>
            <if test="plusEffectDate != null">
                PlusEffectDate = #{plusEffectDate,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                PlanCode = #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="medicareStatus != null">
                MedicareStatus = #{medicareStatus,jdbcType=VARCHAR},
            </if>
            <if test="edorType != null">
                EdorType = #{edorType,jdbcType=VARCHAR},
            </if>
            <if test="jobType != null">
                JobType = #{jobType,jdbcType=VARCHAR},
            </if>
            <if test="jobCode != null">
                JobCode = #{jobCode,jdbcType=VARCHAR},
            </if>
            <if test="staffName != null">
                staffName = #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="mainIdNo != null">
                MainIdNo = #{mainIdNo,jdbcType=VARCHAR},
            </if>
            <if test="mainIdType != null">
                MainIdType = #{mainIdType,jdbcType=VARCHAR},
            </if>
            <if test="relation != null">
                relation = #{relation,jdbcType=VARCHAR},
            </if>
            <if test="payMethod != null">
                PayMethod = #{payMethod,jdbcType=VARCHAR},
            </if>
            <if test="comPayment != null">
                ComPayment = #{comPayment,jdbcType=DOUBLE},
            </if>
            <if test="perPayment != null">
                PerPayment = #{perPayment,jdbcType=DOUBLE},
            </if>
            <if test="debitPayBank != null">
                DebitPayBank = #{debitPayBank,jdbcType=VARCHAR},
            </if>
            <if test="debitPayCode != null">
                DebitPayCode = #{debitPayCode,jdbcType=VARCHAR},
            </if>
            <if test="debitPayName != null">
                DebitPayName = #{debitPayName,jdbcType=VARCHAR},
            </if>
            <if test="deathBenefiName != null">
                DeathBenefiName = #{deathBenefiName,jdbcType=VARCHAR},
            </if>
            <if test="deathBenefiRelation != null">
                DeathBenefiRelation = #{deathBenefiRelation,jdbcType=VARCHAR},
            </if>
            <if test="autoInsured != null">
                AutoInsured = #{autoInsured,jdbcType=VARCHAR},
            </if>
            <if test="deathBenefiPrem != null">
                DeathBenefiPrem = #{deathBenefiPrem,jdbcType=DOUBLE},
            </if>
            <if test="subsidiaryInsuredFlag != null">
                subsidiaryInsuredFlag = #{subsidiaryInsuredFlag,jdbcType=VARCHAR},
            </if>
            <if test="trialStatus != null">
                TrialStatus = #{trialStatus,jdbcType=VARCHAR},
            </if>
            <if test="isError != null">
                IsError = #{isError,jdbcType=VARCHAR},
            </if>
            <if test="errorDesc != null">
                ErrorDesc = #{errorDesc,jdbcType=VARCHAR},
            </if>
            <if test="premSource != null">
                PremSource = #{premSource,jdbcType=VARCHAR},
            </if>
            <if test="accountType != null">
                AccountType = #{accountType,jdbcType=VARCHAR},
            </if>
            <if test="prem != null">
                Prem = #{prem,jdbcType=VARCHAR},
            </if>
            <if test="contNo != null">
                ContNo = #{contNo,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where 1=1
        and GrpContNo = #{grpContNo,jdbcType=VARCHAR}
        and Batch = #{batch,jdbcType=VARCHAR}
        and IdType = #{idType,jdbcType=VARCHAR}
        and IdNo = #{idNo,jdbcType=VARCHAR}
    </update>
    <select id="selectErrorEdorAddInsured" resultType="java.lang.Integer">
        select count(*)
        from fcedoraddinsured
        where grpContNo = #{grpContNo}
          and batch = #{batch}
          and IsError = '1';
    </select>
</mapper>