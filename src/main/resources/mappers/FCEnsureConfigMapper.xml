<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCEnsureConfigMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCEnsureConfig">
    <id column="SerialNo" jdbcType="VARCHAR" property="serialNo" />
    <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
    <result column="ConfigNo" jdbcType="VARCHAR" property="configNo" />
    <result column="ConfigValue" jdbcType="VARCHAR" property="configValue" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    SerialNo, EnsureCode, GrpNo, ConfigNo, ConfigValue, Operator, OperatorCom, MakeDate, 
    MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcensureconfig
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcensureconfig
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCEnsureConfig">
    insert into fcensureconfig (SerialNo, EnsureCode, GrpNo, 
      ConfigNo, ConfigValue, Operator, 
      OperatorCom, MakeDate, MakeTime,
      ModifyDate, ModifyTime)
    values (#{serialNo,jdbcType=VARCHAR}, #{ensureCode,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR},
      #{configNo,jdbcType=VARCHAR}, #{configValue,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="inserts" parameterType="com.sinosoft.eflex.model.FCEnsureConfig">
    insert into fcensureconfig (SerialNo, EnsureCode, GrpNo,
    ConfigNo, ConfigValue, Operator,
    OperatorCom, MakeDate, MakeTime,
    ModifyDate, ModifyTime)
    values
    <foreach collection ="list" item="list" index= "index" separator =",">
      (#{list.serialNo,jdbcType=VARCHAR}, #{list.ensureCode,jdbcType=VARCHAR}, #{list.grpNo,jdbcType=VARCHAR},
      #{list.configNo,jdbcType=VARCHAR}, #{list.configValue,jdbcType=VARCHAR}, #{list.operator,jdbcType=VARCHAR},
      #{list.operatorCom,jdbcType=VARCHAR}, #{list.makeDate,jdbcType=DATE}, #{list.makeTime,jdbcType=VARCHAR},
      #{list.modifyDate,jdbcType=DATE}, #{list.modifyTime,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCEnsureConfig">
    insert into fcensureconfig
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        SerialNo,
      </if>
      <if test="ensureCode != null">
        EnsureCode,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
      <if test="configNo != null">
        ConfigNo,
      </if>
      <if test="configValue != null">
        ConfigValue,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="ensureCode != null">
        #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="configNo != null">
        #{configNo,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null">
        #{configValue,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCEnsureConfig">
    update fcensureconfig
    <set>
      <if test="ensureCode != null">
        EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="configNo != null">
        ConfigNo = #{configNo,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null">
        ConfigValue = #{configValue,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCEnsureConfig">
    update fcensureconfig
    set EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      GrpNo = #{grpNo,jdbcType=VARCHAR},
      ConfigNo = #{configNo,jdbcType=VARCHAR},
      ConfigValue = #{configValue,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </update>
  <insert id="insertConfig" parameterType="map">
    insert into fcensureconfig(SerialNo,EnsureCode,GrpNo,ConfigNo,ConfigValue,Operator,MakeDate,MakeTime,ModifyDate,ModifyTime)
    values
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      (#{serialNo,jdbcType=VARCHAR}, #{ensureCode,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR},
      #{configNo,jdbcType=VARCHAR}, #{configValue,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectOnlyValue" parameterType="java.util.Map" resultType="java.lang.String">
    select
      ConfigValue
    from fcensureconfig
    where 1=1 and ConfigNo = #{configNo}
    <if test="ensureCode != null and ensureCode != ''">
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </if>
    <if test="grpNo != null and grpNo != ''">
      and GrpNo = #{grpNo,jdbcType=VARCHAR}
    </if>
    order by ModifyDate DESC ,ModifyTime DESC  limit 1
  </select>
  <select id="selectByensureCode018" parameterType="java.util.Map" resultType="java.lang.String">
    select
      ConfigValue
    from fcensureconfig
    where 1=1
    <if test="ensureCode != null and ensureCode != ''">
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </if>
    <if test="grpNo != null and grpNo != ''">
      and GrpNo = #{grpNo,jdbcType=VARCHAR}
    </if>
    order by ModifyDate DESC ,ModifyTime DESC  limit 1
  </select>
  <select id="selectOnlyValueByContNo" parameterType="java.util.Map" resultType="java.lang.String">
    select
      ConfigValue
    from fcensureconfig
    where 1=1 and ConfigNo = #{configNo}
      and EnsureCode in (select distinct a.EnsureCode from fcgrporder a,fcorder b,fcorderitem c where a.grporderno=b.grporderno and b.orderno=c.orderno and c.contno=#{contNo,jdbcType=VARCHAR})
    order by ModifyDate DESC ,ModifyTime DESC  limit 1
  </select>

  <select id="selectFcensureconfig" parameterType="java.util.Map" resultMap="BaseResultMap">
  	select
    	<include refid="Base_Column_List" />
    from fcensureconfig
    <where>
         1=1
	     <if test="grpNo != null and grpNo != ''">
	       and GrpNo = #{grpNo,jdbcType=VARCHAR}
	     </if>
	     <if test="ensureCode != null and ensureCode != ''">
	       and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
	     </if>
	     <if test="configNo != null and configNo != ''">
	       and ConfigNo = #{configNo,jdbcType=VARCHAR}
	     </if>
     </where>
    order by ModifyDate DESC ,ModifyTime DESC  limit 1
  </select>
  
  <!-- 删除企业保障配置信息 -->
  <delete id="deleteFcEnsureConfigByGrpNo" parameterType="java.lang.String">
    delete from fcensureconfig
    where GrpNo = #{grpNo,jdbcType=VARCHAR}
     and  ConfigNo in ('001','002','003','004','005','006')
  </delete>
  <delete id="deleteShareholdersByGrpNo" parameterType="java.lang.String">
    delete from fcensureconfig
    where GrpNo = #{grpNo,jdbcType=VARCHAR}
    and  ConfigNo ="00"
  </delete>
  <select id="selectFcensureconfigList" parameterType="java.util.Map" resultMap="BaseResultMap">
  	select
    	<include refid="Base_Column_List" />
    from fcensureconfig
    <where>
         1=1
	     <if test="grpNo != null and grpNo != ''">
	       and GrpNo = #{grpNo,jdbcType=VARCHAR}
	     </if>
	     and  ConfigNo in ("001","002","003","004","005","006")
     </where> 
     
  </select>
   
  <select id="selectOnceFcensureconfig" parameterType="java.lang.String" resultType="java.lang.Integer" >
      select  count(*) from fcensureconfig where 1=1
      and GrpNo = #{grpNo,jdbcType=VARCHAR}
      and  ConfigNo = '007'
  </select>
  <select id="selectShareholder" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCEnsureConfig" >
    select
    <include refid="Base_Column_List" />
     from fcensureconfig where 1=1
    and GrpNo = #{grpNo,jdbcType=VARCHAR}
    and  ConfigNo in ("007","009")
  </select>

  <delete id="deleteByEnsureCode"  parameterType="java.lang.String">
    delete from fcensureconfig
      where ensureCode = #{ensureCode,jdbcType=VARCHAR}
      and  ConfigNo in (010)
  </delete>

    <select id="getInsureNote" parameterType="com.sinosoft.eflex.model.FCEnsureConfig"
            resultType="com.sinosoft.eflex.model.FCEnsureConfig">
    select
    ConfigNo,ConfigValue
    from fcensureconfig where 1=1
    and GrpNo = #{grpNo,jdbcType=VARCHAR}
    AND  EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    and  ConfigNo in ("003","005","006","018")
  </select>

  <select id="selectNoAndValue" parameterType="java.util.Map" resultType="java.util.Map" >
    select
    ConfigNo,ConfigValue
    from fcensureconfig where 1=1
    and GrpNo = #{grpNo,jdbcType=VARCHAR}
    AND  (EnsureCode = #{ensureCode,jdbcType=VARCHAR} or EnsureCode = null)
  </select>
  <select id="selectEnsureConfigByEnsureCode" resultType="java.lang.Integer">
    select count(1) from fcensureconfig
    where ensurecode = #{ensureCode}
  </select>

  <delete id="deleteByConfigNo_008" parameterType="java.lang.String">
    delete from fcensureconfig
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR} AND ConfigNo = '008'
  </delete>
 <delete id="deleteByConfigNo_021" parameterType="java.lang.String">
    delete from fcensureconfig
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR} AND ConfigNo = '021'
  </delete>
<delete id="deleteByConfigNo_018" parameterType="java.lang.String">
    delete from fcensureconfig
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR} AND ConfigNo = '018'
  </delete>
  <delete id="deleteByConfigNo_011" parameterType="java.lang.String">
    delete from fcensureconfig
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR} AND ConfigNo = '011'
  </delete>
  <select id="selectPayType" parameterType="java.util.Map" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM
        FCEnsureConfig
    WHERE  1=1
    <if test="EnsureCode != null and EnsureCode != ''">
      and EnsureCode = #{EnsureCode,jdbcType=VARCHAR}
    </if>
    <if test="ConfigNo != null and ConfigNo != ''">
      and ConfigNo = #{ConfigNo,jdbcType=VARCHAR}
    </if>
  </select>
    <select id="selectcountByensureCode011" parameterType="java.util.Map" resultMap="BaseResultMap">
      SELECT <include refid="Base_Column_List" />
      FROM (
          SELECT *
          FROM FCEnsureConfig
          WHERE  EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          AND ConfigNo =  #{configNo,jdbcType=VARCHAR}) a
      GROUP BY a.ConfigNo
  </select>
    <select id="getDailyMessageInfo" parameterType="java.lang.String" resultType="java.util.Map" >
      select b.grpname,d.tprtno,SUM(f.SelfPrem+f.GrpPrem) totalPrem from fcensure a 
		left join fcgrpinfo b on a.grpno=b.grpno
		left join fcgrporder c left join fcprtandcorerela d on c.prtno=d.prtno 
		left join fcorder e left join fcorderitem f on e.OrderNo=f.orderno on c.GrpOrderNo=e.GrpOrderNo
		on a.ensurecode=c.ensurecode
		where a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
		GROUP BY b.grpname,d.tprtno
  </select>
    <select id="getInsuredMessageInfo" parameterType="java.lang.String" resultType="java.util.Map" >
      select g.name,(f.SelfPrem+f.GrpPrem) prem,f.ContNo from fcensure a 
		left join fcgrporder c left join fcprtandcorerela d on c.prtno=d.prtno 
		left join fcorder e left join fcorderitem f inner join fcorderinsured g on f.orderitemno=g.orderitemno on e.OrderNo=f.orderno on c.GrpOrderNo=e.GrpOrderNo
		on a.ensurecode=c.ensurecode
		where a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
  </select>
</mapper>