<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcDutyGroupDeductibleMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcDutyGroupDeductible">
    <id column="AmountGrageCode" jdbcType="VARCHAR" property="amountGrageCode" />
    <id column="Deductible" jdbcType="DOUBLE" property="deductible" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    AmountGrageCode, Deductible, Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, 
    ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcdutygroupdeductible
    where AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
    <if test = " deductible != null and deductible != ''">
      and Deductible = #{deductible,jdbcType=DOUBLE}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcdutygroupdeductible
    where AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and Deductible = #{deductible,jdbcType=DOUBLE}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FcDutyGroupDeductible">
    insert into fcdutygroupdeductible (AmountGrageCode, Deductible, Operator, 
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{amountGrageCode,jdbcType=VARCHAR}, #{deductible,jdbcType=DOUBLE}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcDutyGroupDeductible">
    insert into fcdutygroupdeductible
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="amountGrageCode != null">
        AmountGrageCode,
      </if>
      <if test="deductible != null">
        Deductible,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="amountGrageCode != null">
        #{amountGrageCode,jdbcType=VARCHAR},
      </if>
      <if test="deductible != null">
        #{deductible,jdbcType=DOUBLE},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcDutyGroupDeductible">
    update fcdutygroupdeductible
    <set>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and Deductible = #{deductible,jdbcType=DOUBLE}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcDutyGroupDeductible">
    update fcdutygroupdeductible
    set Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and Deductible = #{deductible,jdbcType=DOUBLE}
  </update>

  <delete id="deleteByEnsureCode" parameterType="java.util.Map">
    delete from fcdutygroupdeductible
    where AmountGrageCode IN  (select AmountGrageCode FROM fcdutyamountgrade where ensureCode = #{ensureCode,jdbcType=VARCHAR}
    <if test=" riskCode != null and riskCode != ''">
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
    </if>
    <if test=" riskType != null and riskType != ''">
      and RiskType = #{riskType,jdbcType=VARCHAR}
    </if>
    <if test=" amountGrageCode != null and amountGrageCode != '' ">
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
    </if>)
    <if test=" deductible != null and deductible != ''  ">
      and Deductible = #{deductible,jdbcType=VARCHAR}
    </if>
  </delete>

  <insert id="insertDutyDeductibleList" parameterType="java.util.List">
    insert into fcdutygroupdeductible
    (AmountGrageCode,Deductible,Operator,MakeDate,MakeTime,ModifyDate,ModifyTime)
    VALUES <foreach collection="list"  item="item" index="index" open="" close="" separator=",">
    (#{list[${index}].amountGrageCode,jdbcType=VARCHAR},
      #{list[${index}].deductible,jdbcType=DOUBLE},
      #{list[${index}].operator,jdbcType=VARCHAR},
      #{list[${index}].makeDate,jdbcType=DATE},
      #{list[${index}].makeTime,jdbcType=VARCHAR},
      #{list[${index}].modifyDate,jdbcType=DATE},
      #{list[${index}].modifyTime,jdbcType=VARCHAR})
    </foreach>
  </insert>

</mapper>