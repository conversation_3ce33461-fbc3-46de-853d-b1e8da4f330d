<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCFileDocMainMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCFileDocMain">
    <id column="DocID" jdbcType="VARCHAR" property="docID" />
    <result column="DocType" jdbcType="VARCHAR" property="docType" />
    <result column="FileName" jdbcType="VARCHAR" property="fileName" />
    <result column="FileSaveName" jdbcType="VARCHAR" property="fileSaveName" />
    <result column="FileSuffix" jdbcType="VARCHAR" property="fileSuffix" />
    <result column="FileURL" jdbcType="VARCHAR" property="fileURL" />
    <result column="FilePath" jdbcType="VARCHAR" property="filePath" />
    <result column="ValidFlag" jdbcType="VARCHAR" property="validFlag" />
    <result column="Remark" jdbcType="VARCHAR" property="remark" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    DocID, DocType, FileName, FileSaveName, FileSuffix, FileURL, FilePath, ValidFlag, 
    Remark, Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcfiledocmain
    where DocID = #{docID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcfiledocmain
    where DocID = #{docID,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCFileDocMain">
    insert into fcfiledocmain (DocID, DocType, FileName, 
      FileSaveName, FileSuffix, FileURL, 
      FilePath, ValidFlag, Remark, 
      Operator, OperatorCom, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{docID,jdbcType=VARCHAR}, #{docType,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, 
      #{fileSaveName,jdbcType=VARCHAR}, #{fileSuffix,jdbcType=VARCHAR}, #{fileURL,jdbcType=VARCHAR}, 
      #{filePath,jdbcType=VARCHAR}, #{validFlag,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCFileDocMain">
    insert into fcfiledocmain
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="docID != null">
        DocID,
      </if>
      <if test="docType != null">
        DocType,
      </if>
      <if test="fileName != null">
        FileName,
      </if>
      <if test="fileSaveName != null">
        FileSaveName,
      </if>
      <if test="fileSuffix != null">
        FileSuffix,
      </if>
      <if test="fileURL != null">
        FileURL,
      </if>
      <if test="filePath != null">
        FilePath,
      </if>
      <if test="validFlag != null">
        ValidFlag,
      </if>
      <if test="remark != null">
        Remark,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="docID != null">
        #{docID,jdbcType=VARCHAR},
      </if>
      <if test="docType != null">
        #{docType,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileSaveName != null">
        #{fileSaveName,jdbcType=VARCHAR},
      </if>
      <if test="fileSuffix != null">
        #{fileSuffix,jdbcType=VARCHAR},
      </if>
      <if test="fileURL != null">
        #{fileURL,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        #{validFlag,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCFileDocMain">
    update fcfiledocmain
    <set>
      <if test="docType != null">
        DocType = #{docType,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        FileName = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileSaveName != null">
        FileSaveName = #{fileSaveName,jdbcType=VARCHAR},
      </if>
      <if test="fileSuffix != null">
        FileSuffix = #{fileSuffix,jdbcType=VARCHAR},
      </if>
      <if test="fileURL != null">
        FileURL = #{fileURL,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        FilePath = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        ValidFlag = #{validFlag,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        Remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where DocID = #{docID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCFileDocMain">
    update fcfiledocmain
    set DocType = #{docType,jdbcType=VARCHAR},
      FileName = #{fileName,jdbcType=VARCHAR},
      FileSaveName = #{fileSaveName,jdbcType=VARCHAR},
      FileSuffix = #{fileSuffix,jdbcType=VARCHAR},
      FileURL = #{fileURL,jdbcType=VARCHAR},
      FilePath = #{filePath,jdbcType=VARCHAR},
      ValidFlag = #{validFlag,jdbcType=VARCHAR},
      Remark = #{remark,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where DocID = #{docID,jdbcType=VARCHAR}
  </update>
</mapper>