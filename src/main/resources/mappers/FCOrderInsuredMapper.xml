<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCOrderInsuredMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCOrderInsured">
        <id column="OrderItemNo" jdbcType="VARCHAR" property="orderItemNo"/>
        <result column="OrderNo" jdbcType="VARCHAR" property="orderNo"/>
        <result column="GrpOrderNo" jdbcType="VARCHAR" property="grpOrderNo"/>
        <result column="PersonID" jdbcType="VARCHAR" property="personID"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sex" jdbcType="VARCHAR" property="sex"/>
        <result column="Birthday" jdbcType="DATE" property="birthday"/>
        <result column="Nativeplace" jdbcType="VARCHAR" property="Nativeplace"/>
        <result column="IDType" jdbcType="VARCHAR" property="IDType"/>
        <result column="IDNo" jdbcType="VARCHAR" property="IDNo"/>
        <result column="MobilePhone" jdbcType="VARCHAR" property="mobilePhone"/>
        <result column="Phone" jdbcType="VARCHAR" property="phone"/>
        <result column="Department" jdbcType="VARCHAR" property="department"/>
        <result column="OccupationType" jdbcType="VARCHAR" property="occupationType"/>
        <result column="OccupationCode" jdbcType="VARCHAR" property="occupationCode"/>
        <result column="JoinMedProtect" jdbcType="VARCHAR" property="joinMedProtect"/>
        <result column="MedProtectType" jdbcType="VARCHAR" property="medProtectType"/>
        <result column="EMail" jdbcType="VARCHAR" property="EMail"/>
        <result column="ZipCode" jdbcType="VARCHAR" property="zipCode"/>
        <result column="Address" jdbcType="VARCHAR" property="address"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="grpPrem" jdbcType="VARCHAR" property="grpPrem"/>
        <result column="empPrem" jdbcType="VARCHAR" property="empPrem"/>
        <result column="Province" jdbcType="VARCHAR" property="province"/>
        <result column="City" jdbcType="VARCHAR" property="city"/>
        <result column="County" jdbcType="VARCHAR" property="county"/>
        <result column="DetaileAddress" jdbcType="VARCHAR" property="detaileAddress"/>
        <result column="MainProvince" jdbcType="VARCHAR" property="mainProvince"/>
        <result column="MainCity" jdbcType="VARCHAR" property="mainCity"/>
        <result column="MainCounty" jdbcType="VARCHAR" property="mainCounty"/>
        <result column="MainDetaileAddress" jdbcType="VARCHAR" property="mainDetaileAddress"/>
        <result column="MainMobilePhone" jdbcType="VARCHAR" property="mainMobilePhone"/>
        <result column="MainZipCode" jdbcType="VARCHAR" property="mainZipCode"/>
        <result column="MainEMail" jdbcType="VARCHAR" property="mainEMail"/>
        <result column="MainYearSalary" jdbcType="VARCHAR" property="mainYearSalary"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <!-- 投保清单导出exccel -->
    <resultMap type="com.sinosoft.eflex.model.FCOrderInsured" id="InsuredDetail"
               extends="BaseResultMap">
        <association property="fcStaffFamilyRela"
                     javaType="com.sinosoft.eflex.model.FCStaffFamilyRela" column="{perNo=PerNo,personID=PersonID}"
                     select="com.sinosoft.eflex.dao.FCStaffFamilyRelaMapper.selectInsuredDetail"
                     fetchType="lazy"/>
    </resultMap>
    <!-- 投保清单导出exccel -->
    <select id="selectInsuredDetail" parameterType="java.util.Map"
            resultMap="InsuredDetail">
        select l.PerNo,e.ProductCode,a.OrderItemNo, a.OrderNo, a.GrpOrderNo, a.PersonID, a.Name, a.Sex,
        a.Birthday, (select codename from fdcode where codetype='IDType' and codekey=a.IDType) IDType, a.IDNo,
        a.MobilePhone,
        a.Phone, a.Department, a.OccupationType,
        a.OccupationCode, a.JoinMedProtect,
        a.MedProtectType,
        a.EMail, a.ZipCode, a.Address,
        a.Operator, a.OperatorCom, a.MakeDate, a.MakeTime,
        a.ModifyDate, a.ModifyTime,
        case when l.relation='0' then (select f.totalprem from fcensureplan f,fcdefaultplan g
        where f.EnsureCode=g.EnsureCode and f.PlanCode=g.PlanCode
        and g.personId=a.personid and g.EnsureCode=d.ensurecode)
        else 0 end as grpPrem,
        case when l.relation='0' then
        case when e.ProductCode in (select f.plancode from fcdefaultplan f where f.personid=a.PersonID and
        f.ensurecode=d.EnsureCode)
        then 0
        else (select f.totalprem from fcensureplan f where f.plancode=e.ProductCode and f.ensurecode=d.EnsureCode)-
        (select f.totalprem from fcensureplan f,fcdefaultplan g
        where f.EnsureCode=g.EnsureCode and f.PlanCode=g.PlanCode
        and g.personId=a.personid and g.EnsureCode=d.ensurecode) end
        else (select f.totalprem from fcensureplan f
        where f.EnsureCode=d.EnsureCode and f.PlanCode=e.ProductCode
        ) end as empPrem
        from fcorderinsured a,fcorder b,
        fcorderitem c,fcgrporder d,fcorderitemdetail e,fcstafffamilyrela l,fcperinfo p,fcensureplan n
        where a.orderitemno=c.orderitemno and a.orderno=b.orderno and b.orderno=c.orderno
        and d.grporderno=b.grporderno and l.perno=b.PerNo and l.personid=a.PersonID
        and c.orderitemdetailno=e.orderitemdetailno and b.PerNo = p.PerNo and e.ProductCode = n.PlanCode
        and n.ensureCode=d.ensureCode
        <if test="orderNo != null and orderNo != ''">
            and b.OrderNo = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderItemNo != null and orderItemNo != ''">
            and c.OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
        </if>
        <if test=' ensureType == "0" '>
            <if test="garName != null and garName != ''">
                AND p.name LIKE CONCAT(CONCAT('%',#{garName},'%'))
            </if>
            <if test="department != null and department != ''">
                AND a.Department LIKE CONCAT(CONCAT('%',#{department},'%'))
            </if>
            <if test="garSex != null and garSex != ''">
                AND a.Sex = #{garSex}
            </if>
            <if test="planObject != null and planObject != ''">
                AND n.PlanObject = #{planObject}
            </if>
        </if>
        <if test=' ensureType == "1" '>
            <if test=" stuName != null and stuName != ''">
                AND a.`name` LIKE CONCAT(CONCAT('%',#{stuName},'%'))
            </if>
            <if test="garName != null and garName != ''">
                AND p.name LIKE CONCAT(CONCAT('%',#{garName},'%'))
            </if>
            <if test="planName != null and planName != ''">
                AND n.PlanName LIKE CONCAT(CONCAT('%',#{planName},'%'))
            </if>
        </if>
    </select>
    <sql id="Base_Column_List">
        OrderItemNo
        , OrderNo, GrpOrderNo, PersonID, Name, Sex,
		Birthday, Nativeplace, IDType, IDNo,
		MobilePhone,
		Phone, Department, OccupationType,
		OccupationCode, JoinMedProtect,
		MedProtectType,
		EMail, ZipCode, Address,
		Operator, OperatorCom,Province,City,County,DetaileAddress,MainProvince,MainCity,MainCounty,MainDetaileAddress,MainMobilePhone,MainZipCode,MainEMail,MainYearSalary,
		MakeDate, MakeTime,
		ModifyDate, ModifyTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorderinsured
        where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fcorderinsured
        where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCOrderInsured">
        insert into fcorderinsured
        (OrderItemNo, OrderNo, GrpOrderNo,
         PersonID, Name, Sex,
         Birthday, Nativeplace,
         IDType, IDNo,
         MobilePhone, Phone, Department,
         OccupationType,
         OccupationCode, JoinMedProtect,
         MedProtectType, EMail, ZipCode,
         Address, Operator, OperatorCom,
         MakeDate, MakeTime, ModifyDate,
         ModifyTime)
        values (#{orderItemNo,jdbcType=VARCHAR},
                #{orderNo,jdbcType=VARCHAR},
                #{grpOrderNo,jdbcType=VARCHAR},
                #{personID,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
                #{sex,jdbcType=VARCHAR},
                #{birthday,jdbcType=DATE},
                #{Nativeplace,jdbcType=VARCHAR},
                #{IDType,jdbcType=VARCHAR},
                #{IDNo,jdbcType=VARCHAR},
                #{mobilePhone,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR},
                #{department,jdbcType=VARCHAR},
                #{occupationType,jdbcType=VARCHAR},
                #{occupationCode,jdbcType=VARCHAR},
                #{joinMedProtect,jdbcType=VARCHAR},
                #{medProtectType,jdbcType=VARCHAR}, #{EMail,jdbcType=VARCHAR},
                #{zipCode,jdbcType=VARCHAR},
                #{address,jdbcType=VARCHAR},
                #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR},
                #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
                #{modifyDate,jdbcType=DATE},
                #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCOrderInsured">
        insert into fcorderinsured
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderItemNo != null">
                OrderItemNo,
            </if>
            <if test="orderNo != null">
                OrderNo,
            </if>
            <if test="grpOrderNo != null">
                GrpOrderNo,
            </if>
            <if test="personID != null">
                PersonID,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="sex != null">
                Sex,
            </if>
            <if test="birthday != null">
                Birthday,
            </if>
            <if test="IDType != null">
                IDType,
            </if>
            <if test="Nativeplace != null">
                Nativeplace,
            </if>
            <if test="IDNo != null">
                IDNo,
            </if>
            <if test="mobilePhone != null">
                MobilePhone,
            </if>
            <if test="phone != null">
                Phone,
            </if>
            <if test="department != null">
                Department,
            </if>
            <if test="occupationType != null">
                OccupationType,
            </if>
            <if test="occupationCode != null">
                OccupationCode,
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect,
            </if>
            <if test="medProtectType != null">
                MedProtectType,
            </if>
            <if test="EMail != null">
                EMail,
            </if>
            <if test="zipCode != null">
                ZipCode,
            </if>
            <if test="address != null">
                Address,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="province  != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="county != null">
                county,
            </if>
            <if test="detaileAddress != null">
                detaileAddress,
            </if>
            <if test="mainProvince != null">
                mainProvince,
            </if>
            <if test="mainCity != null">
                mainCity,
            </if>
            <if test="mainCounty != null">
                mainCounty,
            </if>
            <if test="mainDetaileAddress != null">
                mainDetaileAddress,
            </if>
            <if test="mainMobilePhone != null">
                mainMobilePhone,
            </if>
            <if test="mainZipCode != null">
                mainZipCode,
            </if>
            <if test="mainEMail != null">
                mainEMail,
            </if>
            <if test="mainYearSalary != null">
                mainYearSalary,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderItemNo != null">
                #{orderItemNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="grpOrderNo != null">
                #{grpOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="personID != null">
                #{personID,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="Nativeplace != null">
                #{Nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="IDType != null">
                #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                #{department,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="EMail != null">
                #{EMail,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                #{county,jdbcType=VARCHAR},
            </if>
            <if test="detaileAddress != null">
                #{detaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="mainProvince != null">
                #{mainProvince,jdbcType=VARCHAR},
            </if>
            <if test="mainCity != null">
                #{mainCity,jdbcType=VARCHAR},
            </if>
            <if test="mainCounty != null">
                #{mainCounty,jdbcType=VARCHAR},
            </if>
            <if test="mainDetaileAddress != null">
                #{mainDetaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="mainMobilePhone != null">
                #{mainMobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="mainZipCode != null">
                #{mainZipCode,jdbcType=VARCHAR},
            </if>
            <if test="mainEMail != null">
                #{mainEMail,jdbcType=VARCHAR},
            </if>
            <if test="mainYearSalary != null">
                #{mainYearSalary,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCOrderInsured">
        update fcorderinsured
        <set>
            <if test="orderNo != null">
                OrderNo = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="grpOrderNo != null">
                GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="personID != null">
                PersonID = #{personID,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                Birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test="Nativeplace != null">
                Nativeplace = #{Nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                Phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                Department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                OccupationType = #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                MedProtectType = #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="EMail != null">
                EMail = #{EMail,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                Province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                City =#{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                County = #{county,jdbcType=VARCHAR},
            </if>
            <if test="detaileAddress != null">
                DetaileAddress = #{detaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="mainProvince != null">
                MainProvince = #{mainProvince,jdbcType=VARCHAR},
            </if>
            <if test="mainCity != null">
                MainCity = #{mainCity,jdbcType=VARCHAR},
            </if>
            <if test="mainCounty != null">
                MainCounty = #{mainCounty,jdbcType=VARCHAR},
            </if>
            <if test="mainDetaileAddress != null">
                MainDetaileAddress = #{mainDetaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="mainMobilePhone != null">
                MainMobilePhone = #{mainMobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="mainZipCode != null">
                MainZipCode = #{mainZipCode,jdbcType=VARCHAR},
            </if>
            <if test="mainEMail != null">
                MainEMail = #{mainEMail,jdbcType=VARCHAR},
            </if>
            <if test="mainYearSalary != null">
                MainYearSalary = #{mainYearSalary,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCOrderInsured">
        update
            fcorderinsured
        set OrderNo        = #{orderNo,jdbcType=VARCHAR},
            GrpOrderNo     =
                #{grpOrderNo,jdbcType=VARCHAR},
            PersonID       =
                #{personID,jdbcType=VARCHAR},
            Name           = #{name,jdbcType=VARCHAR},
            Sex            =
                #{sex,jdbcType=VARCHAR},
            Birthday       = #{birthday,jdbcType=DATE},
            Nativeplace    = #{Nativeplace,jdbcType=VARCHAR},
            IDType         = #{IDType,jdbcType=VARCHAR},
            IDNo           = #{IDNo,jdbcType=VARCHAR},
            MobilePhone    = #{mobilePhone,jdbcType=VARCHAR},
            Phone          =
                #{phone,jdbcType=VARCHAR},
            Department     = #{department,jdbcType=VARCHAR},
            OccupationType = #{occupationType,jdbcType=VARCHAR},
            OccupationCode =
                #{occupationCode,jdbcType=VARCHAR},
            JoinMedProtect =
                #{joinMedProtect,jdbcType=VARCHAR},
            MedProtectType =
                #{medProtectType,jdbcType=VARCHAR},
            EMail          = #{EMail,jdbcType=VARCHAR},
            ZipCode        = #{zipCode,jdbcType=VARCHAR},
            Address        =
                #{address,jdbcType=VARCHAR},
            Operator       = #{operator,jdbcType=VARCHAR},
            OperatorCom    = #{operatorCom,jdbcType=VARCHAR},
            MakeDate       =
                #{makeDate,jdbcType=DATE},
            MakeTime       = #{makeTime,jdbcType=VARCHAR},
            ModifyDate     = #{modifyDate,jdbcType=DATE},
            ModifyTime     =
                #{modifyTime,jdbcType=VARCHAR}
        where OrderItemNo =
              #{orderItemNo,jdbcType=VARCHAR}
    </update>

    <select id="selectList" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorderinsured
        where 1=1
        <if test="grpOrderNo != null">
            and GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderNo != null">
            and OrderNo = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="personID != null and personID !=''">
            and PersonID =
            #{personID,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectOrderNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCOrderInsured">
        select OrderItemNo
        from fcorderinsured
        where OrderNo = #{orderNo,jdbcType=VARCHAR}
    </select>
    <select id="selectListPlan" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCOrderInsured">
        select
        <include refid="Base_Column_List"/>
        from fcorderinsured
        where 1=1
        <if test="grpOrderNo != null">
            and GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderNo != null">
            and OrderNo = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="personId != null and personId !=''">
            and PersonID = #{personId,jdbcType=VARCHAR}
        </if>
    </select>
    <delete id="deleteByOrderNo" parameterType="java.lang.String">
        delete
        from fcorderinsured
        where orderno = #{orderNo,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByPersonId" parameterType="java.lang.String">
        delete
        from fcorderinsured
        where orderno = #{orderNo,jdbcType=VARCHAR}
          and personid = #{personId,jdbcType=VARCHAR}
    </delete>
    <select id="getFcensureByPersonID" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCEnsure">
        select *
        FROM fcensure
        WHERE ensureCode in (select ensureCode from fcgrporder where grporderno = #{grpOrderNo})
    </select>
    <!--     <select id="getFcensureByPersonID" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCEnsure">
        select * FROM fcensure WHERE ensureCode =(
            SELECT ensureCode FROM fcensureplan
                WHERE PlanCode =(SELECT ProductCode FROM fcorderitemdetail
                    WHERE OrderItemDetailNo = (SELECT OrderItemDetailNo FROM fcorderitem
                        WHERE OrderItemNo = (SELECT OrderItemNo FROM fcorderinsured WHERE personID = #{personId} AND GrpOrderNo = #{grpOrderNo}))))
        </select> -->
    <select id="selectOrderItemNo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FCOrderInsured">
        SELECT OrderItemNo
        FROM fcorderinsured
        WHERE personID = #{personID}
          AND GrpOrderNo = #{grpOrderNo}
          AND OrderNo = #{orderNo}
    </select>

    <update id="updateByPersonID" parameterType="com.sinosoft.eflex.model.FCOrderInsured">
        update fcorderinsured
        <set>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                Birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test="Nativeplace != null">
                Nativeplace = #{Nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                Phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                Department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                OccupationType = #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                MedProtectType = #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="EMail != null">
                EMail = #{EMail,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where PersonID = #{personID,jdbcType=VARCHAR}
    </update>

    <select id="getOrderItemInfoList" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT d.`PersonID`                          AS personID,
               d.`Relation`                          AS relation,
               a.`Name`                              AS `name`,
               a.`Sex`                               AS sex,
               DATE_FORMAT(a.`Birthday`, '%Y-%m-%d') AS birthday,
               a.`IDType`                            AS iDType,
               a.`IDNo`                              AS iDNo,
               a.`OccupationType`                    AS occupationType,
               a.`OccupationCode`                    AS occupationCode,
               a.`JoinMedProtect`                    AS joinMedProtect,
               a.`MobilePhone`                       AS mobilePhone,
               a.`EMail`                             AS email,
               CAST(b.`GrpPrem` as DECIMAL(20, 2))   AS grpPrem,
               cast(b.`SelfPrem` as DECIMAL(20, 2))  AS selPrem,
               e.`ProductCode`                       AS planCode
        FROM fcorderinsured a
                 LEFT JOIN fcorderitem b ON a.`OrderItemNo` = b.`OrderItemNo`
                 LEFT JOIN fcorder c ON b.`OrderNo` = c.`OrderNo`
                 LEFT JOIN fcgrporder f ON f.`GrpOrderNo` = c.`GrpOrderNo`
                 LEFT JOIN fcensure g ON g.`EnsureCode` = f.`EnsureCode`
                 LEFT JOIN fcstafffamilyrela d ON d.`PersonID` = a.`PersonID`
                 LEFT JOIN fcorderitemdetail e ON e.`OrderItemDetailNo` = b.`OrderItemDetailNo` AND g.`PlanType` = '0'
        WHERE c.`OrderNo` = #{orderNo}
          AND d.`PerNo` = #{perNo}
    </select>

    <select id="selectInsuredByPersonID" parameterType="java.util.Map"
            resultType="com.sinosoft.eflex.model.FCOrderInsured">
        SELECT a.`OrderItemNo` AS orderItemNo
        FROM fcorderinsured a
                 INNER JOIN fcGrpOrder b ON a.`GrpOrderNo` = b.`GrpOrderNo`
        WHERE a.`PersonID` = #{personID}
          AND b.`EnsureCode` = #{ensureCode}
    </select>
    <select id="getFcensureByParams" resultType="com.sinosoft.eflex.model.FCEnsure">
        select *
        from fcensure
        where EnsureCode =
              (select EnsureCode
               from fcorderinsured a,
                    fcgrporder b
               where a.GrpOrderNo = b.GrpOrderNo
                 and a.PersonID = #{personId})
    </select>

    <select id="queryEfleInsuredDetail" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT a.`OrderItemDetailNo` AS orderItemDetailNo,
        i.PersonID AS personID,
        f.`Name` AS garName,
        f.`IDNo` AS garIDNo,
        f.`IDType` AS garIDType,
        i.`Name` AS insureName,
        e.`Relation` AS relation,
        i.`Sex` AS sex,
        date_format(i.`Birthday`,'%Y-%m-%d') AS birthday,
        i.`IDType` AS idType,
        i.`IDNo` AS idNo,
        i.`Department` AS departMent,
        i.`OccupationType` AS occupationType,
        i.`OccupationCode` AS occupationCode,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = i.`OccupationCode`) as
        occupationCodeName,
        i.`JoinMedProtect` AS joinMedProtect,
        i.`MobilePhone` AS mobilePhone,
        i.`EMail` AS email,
        date_format(d.`idTypeEndDate`,'%Y-%m-%d') AS idTypeEndDate,
        d.`nativeplace` AS nativePlace,
        a.`GrpPrem` AS grpPrem,
        a.`SelfPrem` AS selfPrem,
        h.Name AS openPer,
        h.PayBankCode AS openBank,
        (SELECT CodeName FROM fdcode WHERE CodeType = 'Bank' AND CodeKey = h.PayBankCode) as openBankName,
        h.BankAccount AS openAccount,
        f.`LevelCode` AS levelCode,
        f.`ServiceTerm` AS serviceTerm,
        f.`Retirement` AS retirement,
        g.`StaffGrpPrem` AS staffGrpPrem,
        g.`FamilyGrpPrem` AS familyGrpPrem,
        i.orderItemNo
        FROM fcorderinsured i
        INNER JOIN fcorderitem a ON a.`OrderItemNo` = i.`OrderItemNo`
        INNER JOIN fcorder b ON b.`OrderNo` = a.`OrderNo`
        INNER JOIN fcgrporder c ON c.`GrpOrderNo` = b.`GrpOrderNo`
        INNER JOIN fcperinfo f ON f.`PerNo` = b.`PerNo`
        INNER JOIN fcperson d ON i.`PersonID` = d.`PersonID`
        INNER JOIN fcstafffamilyrela e ON e.`PerNo` = b.`PerNo` AND e.`PersonID` = d.`PersonID`
        INNER JOIN fcperregistday g ON g.`EnsureCode` = c.`EnsureCode` AND g.`PerNo` = b.`PerNo`
        LEFT JOIN FcBatchPayBankInfo h ON h.ensureCode = c.ensureCode AND h.perNo = b.perNo AND h.IsSidned = 'Y'
        WHERE c.`EnsureCode` = #{ensureCode}
        <if test="department != null and department != '' ">
            AND f.`department` LIKE concat('%',#{department},'%')
        </if>
        <if test="garName != null and garName != '' ">
            AND f.`Name` LIKE concat('%',#{garName},'%')
        </if>
        <if test="famName != null and famName != '' ">
            AND d.`Name` LIKE concat('%',#{famName},'%')
        </if>
        <if test="gradeLevelCode != null and gradeLevelCode != '' ">
            AND f.`LevelCode` = #{gradeLevelCode}
        </if>
        <if test="garIDNo != null and garIDNo != '' ">
            AND f.`IDNo` = #{garIDNo}
        </if>
        <if test="famIDNo != null and famIDNo != '' ">
            AND d.`IDNo` = #{famIDNo}
        </if>
        <if test='isCheck == "0" '>
            AND e.`Relation` = '0'
        </if>
        <if test='isCheck != "0" '>
            AND e.`Relation` != '0'
        </if>
    </select>

    <select id="getInsuredOrderInfo" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT d.`EnsureCode`     AS ensureCode,
               d.`EnsureName`     AS ensureName,
               c.`GrpOrderStatus` AS grpOrderStatus,
               b.OrderStatus      as orderStatus,
               b.ordersign        as orderSign
        FROM fcorderinsured a
                 INNER JOIN fcorder b ON a.`OrderNo` = b.`OrderNo`
                 INNER JOIN fcgrporder c ON c.`GrpOrderNo` = b.`GrpOrderNo`
                 INNER JOIN fcensure d ON d.`EnsureCode` = c.`EnsureCode`
        WHERE a.`PersonID` = #{personID}
          AND b.`PerNo` = #{perNo}
    </select>
    <select id="selectDailyInsuredDetail" resultType="java.util.Map">
        SELECT
        i.PersonID AS personID,
        f.`Name` AS garName,
        f.`IDNo` AS garIDNo,
        f.`IDType` AS garIDType,
        i.`Name` AS insureName,
        e.`Relation` AS relation,
        i.`Sex` AS sex,
        date_format(i.`Birthday`,'%Y-%m-%d') AS birthday,
        i.`IDType` AS idType,
        i.`IDNo` AS idNo,
        i.`Department` AS departMent,
        i.`OccupationType` AS occupationType,
        i.`OccupationCode` AS occupationCode,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = i.`OccupationCode`) as
        occupationCodeName,
        i.`JoinMedProtect` AS joinMedProtect,
        i.`MobilePhone` AS mobilePhone,
        i.`EMail` AS email,
        date_format(d.`idTypeEndDate`,'%Y-%m-%d') AS idTypeEndDate,
        d.`nativeplace` AS nativePlace,
        h.Name AS openPer,
        h.PayBankCode AS openBank,
        (SELECT CodeName FROM fdcode WHERE CodeType = 'Bank' AND CodeKey = h.PayBankCode) as openBankName,
        h.BankAccount AS openAccount,
        f.`LevelCode` AS levelCode,
        f.`ServiceTerm` AS serviceTerm,
        f.`Retirement` AS retirement,
        i.`OrderItemNo` AS orderItemNo
        FROM fcorderinsured i
        INNER JOIN fcorderitem a ON a.`OrderItemNo` = i.`OrderItemNo`
        INNER JOIN fcorder b ON b.`OrderNo` = a.`OrderNo`
        INNER JOIN fcgrporder c ON c.`GrpOrderNo` = b.`GrpOrderNo`
        INNER JOIN fcperinfo f ON f.`PerNo` = b.`PerNo`
        INNER JOIN fcperson d ON i.`PersonID` = d.`PersonID`
        INNER JOIN fcstafffamilyrela e ON e.`PerNo` = b.`PerNo` AND e.`PersonID` = d.`PersonID`
        LEFT JOIN FcBatchPayBankInfo h ON h.ensureCode = c.ensureCode AND h.perNo = b.perNo AND h.IsSidned = 'Y'
        WHERE c.`EnsureCode` = #{ensureCode} and b.OrderStatus ='08'
        <if test="garName != null and garName != '' ">
            AND f.`Name` LIKE concat('%',#{garName},'%')
        </if>
        <if test="famName != null and famName != '' ">
            AND d.`Name` LIKE concat('%',#{famName},'%')
        </if>
        <if test="gradeLevelCode != null and gradeLevelCode != '' ">
            AND f.`LevelCode` = #{gradeLevelCode}
        </if>
        <if test="garIDNo != null and garIDNo != '' ">
            AND f.`IDNo` = #{garIDNo}
        </if>
        <if test="famIDNo != null and famIDNo != '' ">
            AND d.`IDNo` = #{famIDNo}
        </if>
        <if test='isCheck == "0" '>
            AND e.`Relation` = '0'
        </if>
        <if test='isCheck != "0" '>
            AND e.`Relation` != '0'
        </if>
        group by b.OrderNo
        order by f.`Name`
    </select>
    <select id="selectStaffFamilyInsured" resultType="java.lang.Integer">
        select count(*)
        from fcorderinsured
        where PersonID = #{personID}
    </select>
    <select id="selectStaffFamilyInsuredAndOrder" resultType="java.lang.Integer">
        select count(*)
        from fcorderinsured a
                 inner join fcorderitem b on a.OrderItemNo = b.OrderItemNo
                 inner join fcorder c on a.orderno = c.orderno
        where a.PersonID = #{personID}
          and c.OrderStatus in ('04', '06', '05', '012', '08', '09', '010', '015', '013')
    </select>
    <select id="selectStaffFamilyInsuredOrderInfo" resultType="java.util.Map">
        SELECT a.OrderNo           as orderNo,
               a.OrderItemNo       as orderItemNo,
               c.OrderItemDetailNo as orderItemDetailNo,
               b.OrderStatus       as orderStatus
        FROM fcorderinsured a
                 LEFT JOIN fcorder b on a.OrderNo = b.OrderNo
                 LEFT JOIN fcorderitem c on a.OrderItemNo = c.OrderItemNo
        where PersonID = #{personID}
    </select>
    <select id="selectPeopleNum" resultType="java.lang.Integer">
        select count(*)
        from fcorderinsured
        where IDNo = #{iDNo}
          and GrpOrderNo = #{grpOrderNo}
    </select>
    <select id="selectByOrderItemNo" resultType="java.util.Map">
        select f1.PersonID                            personId,
               f1.Name                                name,
               f1.IDType                              idType,
               f3.CodeName                            idTypeName,
               f1.IDNo                                idNo,
               date_format(f1.Birthday, '%Y-%m-%d')   birthday,
               f1.Sex                                 sex,
               f4.CodeName                            sexName,
               f1.OccupationCode                      occupationCode,
               (select codename
                from fdcode
                where CodeType = 'OccupationDetail'
                  and CodeKey = f1.OccupationCode) as occupationCodeName,
               f1.Province                            province,
               f1.City                                city,
               f1.County                              county,
               f6.PlaceName                           provinceName,
               f7.PlaceName                           cityName,
               f8.PlaceName                           countyName,
               f1.DetaileAddress                      detaileAddress,
               f1.ZipCode                             zipCode,
               f1.MobilePhone                         mobilePhone,
               f1.EMail                               eMail,
               f1.mainYearSalary,
               (select c.corecode
                from fcorder a,
                     fcstafffamilyrela b
                         left join fdcode c on b.relation = c.codekey and c.codetype = 'Relation'
                where a.perno = b.perno
                  and a.orderno = f1.orderno
                  and b.personid = f1.personid)       relation
        from fcorderinsured f1
                 left join fdcode f3 on f3.CodeType = 'IDType' and f3.CodeKey = f1.IDType
                 left join fdcode f4 on f4.CodeType = 'sex' and f4.CodeKey = f1.Sex
                 left join fdplace f6 on f6.PlaceCode = f1.Province
                 left join fdplace f7 on f7.PlaceCode = f1.City
                 left join fdplace f8 on f8.PlaceCode = f1.County
        where f1.OrderItemNo = #{orderItemNo}
    </select>
    <select id="selectByPersonID" resultType="java.lang.Integer">
        select count(*)
        from fcorderinsured
        where PersonID = #{personID}
    </select>

    <update id="updateInPhoneByNoCommitCore" parameterType="com.sinosoft.eflex.model.FCOrderInsured">
        UPDATE fcorderinsured i
        INNER JOIN fcorder o ON o.`OrderNo` = i.`OrderNo`
        SET
        <if test="mobilePhone != null">
            i.MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
        </if>
        <if test="EMail != null">
            i.EMail = #{EMail,jdbcType=VARCHAR},
        </if>
        <if test="modifyDate != null">
            i.ModifyDate = #{modifyDate,jdbcType=DATE},
        </if>
        <if test="modifyTime != null">
            i.ModifyTime = #{modifyTime,jdbcType=VARCHAR}
        </if>
        WHERE i.`IDNo` = #{IDNo,jdbcType=VARCHAR} AND o.`OrderStatus` = '01'
    </update>
    <update id="updateByIdNo" parameterType="java.util.Map">
        update fcorderinsured
        set MobilePhone=#{mobilePhone}
        where IDNo = #{idNo}
    </update>
    <select id="selectBasePerple" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT a.*
        FROM fcorderinsured a
                 INNER JOIN fcorderitem b ON a.`OrderItemNo` = b.`OrderItemNo`
                 INNER JOIN fcorder c ON b.`OrderNo` = c.`OrderNo`
                 INNER JOIN fcgrporder d ON d.`GrpOrderNo` = c.`GrpOrderNo`
                 INNER JOIN fcensure e ON d.`EnsureCode` = e.`EnsureCode`
        WHERE e.`EnsureCode` = #{ensureCode};
    </select>
    <select id="selectBaseInsuredPeople" parameterType="com.sinosoft.eflex.model.dailyplan.SelectBaseInsuredPeopleReq"
            resultType="com.sinosoft.eflex.model.dailyplan.SelectBaseInsuredPeopleResp">
        select a.OrderItemDetailNo      orderItemDetailNo,
               c.Birthday               birthDay,
               c.sex                    gender,
               a.InsurePeriod           insurePeriod,
               a.PayPeriod              payPeriod,
               (b.SelfPrem + b.GrpPrem) prem,
               #{insureDate}            insureDate,
               a.ProductCode            riskCode
        from fcorderitemdetail a
                 inner join fcorderitem b on a.OrderItemDetailNo = b.OrderItemDetailNo
                 inner join fcorderinsured c on b.OrderItemNo = c.OrderItemNo and b.OrderNo = c.OrderNo
                 inner join fcorder d on b.OrderNo = d.OrderNo and c.OrderNo = d.OrderNo
                 inner join fcgrporder e on d.GrpOrderNo = e.GrpOrderNo
        where e.EnsureCode = #{ensureCode}
    </select>
    <select id="selectPeopleInsureInfo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.confirmInsure.PeopleInsureInfo">
        select a.personId,
               b.name,
               b.birthDate,
               g.Relation  relation,
               h.CodeName  relationName,
               i.planCode,
               i.planName,
               i.TotalPrem totalPrem,
               c.GrpPrem   grpPrem,
               c.SelfPrem  selfPrem
        from fcorderinsured a
                 left join fcperson b on a.personId = b.personid
                 left join fcorderitem c on c.OrderItemNo = a.OrderItemNo
                 left join fcorderitemdetail d on c.OrderItemDetailNo = d.OrderItemDetailNo
                 left join fcorder e on e.orderNo = a.orderNo
                 left join fcgrpOrder f on e.GrpOrderNo = f.GrpOrderNo
                 left join fcstafffamilyrela g on e.perno = g.perNo and a.personid = g.personid
                 left join fdcode h on h.codeType = 'relation' and h.codeKey = g.Relation
                 left join fcensureplan i on d.ProductCode = i.planCode and i.EnsureCode = f.ensureCode
        where a.orderNo = #{orderNo};
    </select>
    <select id="selectOrderInsured" parameterType="com.sinosoft.eflex.model.FCOrderInsured"
            resultType="java.lang.Integer">
        select count(*)
        from fcorderinsured
        where 1 = 1
          and grpOrderNo = #{grpOrderNo}
          and idno = #{IDNo}
    </select>
    <select id="selectMianOrderInsured" parameterType="com.sinosoft.eflex.model.FCOrderInsured"
            resultType="java.lang.Integer">
        select count(*)
        from fcorderinsured a
                 left join fcstafffamilyrela b on a.personid = b.personid and b.Relation = '0'
        where a.GrpOrderNo = #{grpOrderNo}
          and a.name = #{name}
          and a.idType = #{idType}
          and a.idno = #{idno}
    </select>
    <select id="selectByIdNo" resultType="com.sinosoft.eflex.model.FCOrderInsured">
        select *
        from fcorderinsured
        where IDNo = #{idNo}
    </select>
    <select id="selectPersonByOrderItemNo" resultType="java.util.Map">
        select f1.PersonID                            personId,
               f1.Name                                name,
               f1.IDType                              idType,
               f3.CodeName                            idTypeName,
               f1.IDNo                                idNo,
               date_format(f1.Birthday, '%Y-%m-%d')   birthday,
               f1.Sex                                 sex,
               f4.CodeName                            sexName,
               f1.OccupationCode                      occupationCode,
               (select codename
                from fdcode
                where CodeType = 'OccupationDetail'
                  and CodeKey = f1.OccupationCode) as occupationCodeName,
               f1.Province                            province,
               f1.City                                city,
               f1.County                              county,
               f6.PlaceName                           provinceName,
               f7.PlaceName                           cityName,
               f8.PlaceName                           countyName,
               f1.DetaileAddress                      detaileAddress,
               f1.ZipCode                             zipCode,
               f1.MobilePhone                         mobilePhone,
               f1.EMail                               eMail,
               f1.mainYearSalary,
               (select c.corecode
                from fcorder a,
                     fcstafffamilyrela b
                         left join fdcode c on b.relation = c.codekey and c.codetype = 'Relation'
                where a.perno = b.perno
                  and a.orderno = f1.orderno
                  and b.personid = f1.personid)       relation
        from fcorderinsured f1
                 left join fdcode f3 on f3.CodeType = 'IDType' and f3.CodeKey = f1.IDType
                 left join fdcode f4 on f4.CodeType = 'sex' and f4.CodeKey = f1.Sex
                 left join fdplace f6 on f6.PlaceCode = f1.Province
                 left join fdplace f7 on f7.PlaceCode = f1.City
                 left join fdplace f8 on f8.PlaceCode = f1.County
        where f1.PersonID = #{personID}
        order by f1.MakeDate desc limit 1
    </select>
    <select id="getOrderItemCustomerInfoList" resultType="java.util.Map">
        SELECT a.`Name`                              AS `name`,
               a.`Sex`                               AS sex,
               DATE_FORMAT(a.`Birthday`, '%Y-%m-%d') AS birthday,
               a.`IDType`                            AS iDType,
               a.`IDNo`                              AS iDNo,
               fp.Nativeplace                        AS nationality
        FROM fcorderinsured a
                 LEFT JOIN fcorderitem b ON a.`OrderItemNo` = b.`OrderItemNo`
                 LEFT JOIN fcorder c ON b.`OrderNo` = c.`OrderNo`
                 LEFT JOIN fcgrporder f ON f.`GrpOrderNo` = c.`GrpOrderNo`
                 LEFT JOIN fcperregistday fc ON fc.ensureCode = f.ensureCode AND fc.perNo = c.perNo
                 LEFT JOIN fcensure g ON g.`EnsureCode` = f.`EnsureCode`
                 LEFT JOIN fcstafffamilyrela d ON d.`PersonID` = a.`PersonID`
                 LEFT JOIN fcperson fp ON fp.`PersonID` = a.`PersonID`
                 LEFT JOIN fcorderitemdetail e ON e.`OrderItemDetailNo` = b.`OrderItemDetailNo` AND g.`PlanType` = '0'
        where f.EnsureCode = #{ensureCode}
          and fc.LockState = '0'
    </select>
    <update id="updateBatchById">
        <foreach collection="list" item="item" separator=";">
            update fcorderinsured
            <set>
                <if test="item.name != null">
                    Name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.sex != null">
                    Sex = #{item.sex,jdbcType=VARCHAR},
                </if>
                <if test="item.birthday != null">
                    Birthday = #{item.birthday,jdbcType=DATE},
                </if>
                <if test="item.Nativeplace != null">
                    Nativeplace = #{item.Nativeplace,jdbcType=VARCHAR},
                </if>
                <if test="item.IDType != null">
                    IDType = #{item.IDType,jdbcType=VARCHAR},
                </if>
                <if test="item.IDNo != null">
                    IDNo = #{item.IDNo,jdbcType=VARCHAR},
                </if>
                <if test="item.mobilePhone != null">
                    MobilePhone = #{item.mobilePhone,jdbcType=VARCHAR},
                </if>
                <if test="item.phone != null">
                    Phone = #{item.phone,jdbcType=VARCHAR},
                </if>
                <if test="item.department != null">
                    Department = #{item.department,jdbcType=VARCHAR},
                </if>
                <if test="item.occupationType != null">
                    OccupationType = #{item.occupationType,jdbcType=VARCHAR},
                </if>
                <if test="item.occupationCode != null">
                    OccupationCode = #{item.occupationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.joinMedProtect != null">
                    JoinMedProtect = #{item.joinMedProtect,jdbcType=VARCHAR},
                </if>
                <if test="item.medProtectType != null">
                    MedProtectType = #{item.medProtectType,jdbcType=VARCHAR},
                </if>
                <if test="item.EMail != null">
                    EMail = #{item.EMail,jdbcType=VARCHAR},
                </if>
                <if test="item.zipCode != null">
                    ZipCode = #{item.zipCode,jdbcType=VARCHAR},
                </if>
                <if test="item.address != null">
                    Address = #{item.address,jdbcType=VARCHAR},
                </if>
                <if test="item.operator != null">
                    Operator = #{item.operator,jdbcType=VARCHAR},
                </if>
                <if test="item.operatorCom != null">
                    OperatorCom = #{item.operatorCom,jdbcType=VARCHAR},
                </if>
                <if test="item.makeDate != null">
                    MakeDate = #{item.makeDate,jdbcType=DATE},
                </if>
                <if test="item.makeTime != null">
                    MakeTime = #{item.makeTime,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyDate != null">
                    ModifyDate = #{item.modifyDate,jdbcType=DATE},
                </if>
                <if test="item.modifyTime != null">
                    ModifyTime = #{item.modifyTime,jdbcType=VARCHAR},
                </if>
            </set>
            where
            <choose>
                <when test="item.edorType == 'IC' and item.edorType != null and item.edorType != ''">
                    IDNo = #{item.oldIdNo}
                </when>
                <otherwise>
                    IDNo = #{item.IDNo}
                </otherwise>
            </choose>

        </foreach>
    </update>
    <select id="selectByGrpOrderNoAndIdNo" resultType="com.sinosoft.eflex.model.FCOrderInsured">
        select *
        from fcorderinsured
        where IDNo = #{idNo}
          and GrpOrderNo = #grpOrderNo{}
    </select>
    <select id="selectByPersonIDAndIdNo" resultType="com.sinosoft.eflex.model.FCOrderInsured">
        select *
        from fcorderinsured
        where IDNo = #{idNo}
          and PersonID = #{personID}
    </select>
    <select id="selectByIdNoAndGrpOrderNo" resultType="com.sinosoft.eflex.model.FCOrderInsured">
        select *
        from fcorderinsured
        where IDNo = #{idNo}
          and GrpOrderNo = #{grpOrderNo}
    </select>


    <select id="selectByOrderItemNoList" resultMap="BaseResultMap" parameterType="java.util.List">
        select * from fcorderinsured where
        Birthday <![CDATA[ <= ]]> DATE_SUB(CURDATE(), INTERVAL 18 YEAR)
        and
        OrderItemNo IN
        <foreach item="item" index="index" collection="list" open="(" separator=", " close=")">
            #{item}
        </foreach>
    </select>


</mapper>
