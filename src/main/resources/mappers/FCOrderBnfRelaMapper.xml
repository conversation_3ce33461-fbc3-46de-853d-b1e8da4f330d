<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCOrderBnfRelaMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCOrderBnfRela">
    <id column="OrderItemNo" jdbcType="VARCHAR" property="orderItemNo" />
    <result column="OrderNo" jdbcType="VARCHAR" property="orderNo" />
    <result column="BnfNo" jdbcType="VARCHAR" property="bnfNo" />
    <result column="BnfType" jdbcType="VARCHAR" property="bnfType" />
    <result column="Relation" jdbcType="VARCHAR" property="relation" />
    <result column="BnfKind" jdbcType="VARCHAR" property="bnfKind" />
    <result column="BnfOrder" jdbcType="INTEGER" property="bnfOrder" />
    <result column="BnfRatio" jdbcType="DOUBLE" property="bnfRatio" />
    <result column="BnfForm" jdbcType="VARCHAR" property="bnfForm" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    OrderItemNo, OrderNo, BnfNo, BnfType, Relation, BnfKind, BnfOrder, BnfRatio, BnfForm, 
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcorderbnfrela
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcorderbnfrela
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
  </delete>
    <delete id="deleteByOrderItemNoAndBnfNo">
      delete from fcorderbnfrela
      where OrderItemNo = #{orderItemNo}
      and BnfNo = #{bnfNo}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCOrderBnfRela">
    insert into fcorderbnfrela (OrderItemNo, OrderNo, BnfNo, 
      BnfType, Relation, BnfKind, 
      BnfOrder, BnfRatio, BnfForm, 
      Operator, OperatorCom, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{orderItemNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{bnfNo,jdbcType=VARCHAR}, 
      #{bnfType,jdbcType=VARCHAR}, #{relation,jdbcType=VARCHAR}, #{bnfKind,jdbcType=VARCHAR}, 
      #{bnfOrder,jdbcType=INTEGER}, #{bnfRatio,jdbcType=DOUBLE}, #{bnfForm,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCOrderBnfRela">
    insert into fcorderbnfrela
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderItemNo != null">
        OrderItemNo,
      </if>
      <if test="orderNo != null">
        OrderNo,
      </if>
      <if test="bnfNo != null">
        BnfNo,
      </if>
      <if test="bnfType != null">
        BnfType,
      </if>
      <if test="relation != null">
        Relation,
      </if>
      <if test="bnfKind != null">
        BnfKind,
      </if>
      <if test="bnfOrder != null">
        BnfOrder,
      </if>
      <if test="bnfRatio != null">
        BnfRatio,
      </if>
      <if test="bnfForm != null">
        BnfForm,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderItemNo != null">
        #{orderItemNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="bnfNo != null">
        #{bnfNo,jdbcType=VARCHAR},
      </if>
      <if test="bnfType != null">
        #{bnfType,jdbcType=VARCHAR},
      </if>
      <if test="relation != null">
        #{relation,jdbcType=VARCHAR},
      </if>
      <if test="bnfKind != null">
        #{bnfKind,jdbcType=VARCHAR},
      </if>
      <if test="bnfOrder != null">
        #{bnfOrder,jdbcType=INTEGER},
      </if>
      <if test="bnfRatio != null">
        #{bnfRatio,jdbcType=DOUBLE},
      </if>
      <if test="bnfForm != null">
        #{bnfForm,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCOrderBnfRela">
    update fcorderbnfrela
    <set>
      <if test="orderNo != null">
        OrderNo = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="bnfNo != null">
        BnfNo = #{bnfNo,jdbcType=VARCHAR},
      </if>
      <if test="bnfType != null">
        BnfType = #{bnfType,jdbcType=VARCHAR},
      </if>
      <if test="relation != null">
        Relation = #{relation,jdbcType=VARCHAR},
      </if>
      <if test="bnfKind != null">
        BnfKind = #{bnfKind,jdbcType=VARCHAR},
      </if>
      <if test="bnfOrder != null">
        BnfOrder = #{bnfOrder,jdbcType=INTEGER},
      </if>
      <if test="bnfRatio != null">
        BnfRatio = #{bnfRatio,jdbcType=DOUBLE},
      </if>
      <if test="bnfForm != null">
        BnfForm = #{bnfForm,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCOrderBnfRela">
    update fcorderbnfrela
    set OrderNo = #{orderNo,jdbcType=VARCHAR},
      BnfNo = #{bnfNo,jdbcType=VARCHAR},
      BnfType = #{bnfType,jdbcType=VARCHAR},
      Relation = #{relation,jdbcType=VARCHAR},
      BnfKind = #{bnfKind,jdbcType=VARCHAR},
      BnfOrder = #{bnfOrder,jdbcType=INTEGER},
      BnfRatio = #{bnfRatio,jdbcType=DOUBLE},
      BnfForm = #{bnfForm,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
  </update>
  <select id="selectlistByOrderItemNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fcorderbnfrela
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
  </select>
</mapper>