<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FPInsureEflexPlanMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FPInsureEflexPlan">
      <id column="InsureElfexPlanNo" jdbcType="VARCHAR" property="insureElfexPlanNo" />
    <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <result column="PersonId" jdbcType="VARCHAR" property="personId" />
    <result column="PerNo" jdbcType="VARCHAR" property="perNo" />
    <result column="AppntYear" jdbcType="VARCHAR" property="appntYear" />
    <result column="RiskCode" jdbcType="VARCHAR" property="riskCode" />
    <result column="RiskType" jdbcType="VARCHAR" property="riskType" />
    <result column="Prem" jdbcType="DOUBLE" property="prem" />
    <result column="AmountGrageCode" jdbcType="VARCHAR" property="amountGrageCode" />
    <result column="DeductibleAttr" jdbcType="VARCHAR" property="deductibleAttr" />
    <result column="Deductible" jdbcType="DOUBLE" property="deductible" />
    <result column="CompensationRatio" jdbcType="DOUBLE" property="compensationRatio" />
      <result column="InsureState" jdbcType="VARCHAR" property="insureState"/>
      <result column="Operator" jdbcType="VARCHAR" property="operator"/>
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    InsureElfexPlanNo, EnsureCode, PersonId, PerNo, AppntYear, RiskCode, RiskType, Prem, AmountGrageCode,
    DeductibleAttr, Deductible, CompensationRatio, InsureState, Operator, OperatorCom,
    MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fpinsureeflexplan
    where InsureElfexPlanNo = #{insureElfexPlanNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fpinsureeflexplan
    where InsureElfexPlanNo = #{insureElfexPlanNo,jdbcType=VARCHAR}
  </delete>
  <delete id="deletefPInsureEflexPlan" parameterType="java.lang.String">
    delete from fpinsureeflexplan
    where perNo = #{perNo,jdbcType=VARCHAR}
    and ensureCode = #{ensureCode,jdbcType=VARCHAR}
    <if test=" personId != null and personId != '' ">
        and  personId = #{personId,jdbcType=VARCHAR}
    </if>
  </delete>
  <delete id="deletefPEflexCheckRuleEflexPlan" parameterType="java.lang.String">
    delete from fPEflexCheckRuleEflexPlan
    where perNo = #{perNo,jdbcType=VARCHAR}
    and ensureCode = #{ensureCode,jdbcType=VARCHAR}
    <if test=" personId != null and personId != '' ">
        and  personId = #{personId,jdbcType=VARCHAR}
    </if>
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FPInsureEflexPlan">
    insert into fpinsureeflexplan (InsureElfexPlanNo, EnsureCode, PersonId,
      PerNo, AppntYear, RiskCode, RiskType, Prem,
      AmountGrageCode, DeductibleAttr, Deductible,
      CompensationRatio, InsureState, Operator,
      OperatorCom, MakeDate, MakeTime,
      ModifyDate, ModifyTime)
    values (#{insureElfexPlanNo,jdbcType=VARCHAR}, #{ensureCode,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR},
      #{perNo,jdbcType=VARCHAR}, #{appntYear,jdbcType=VARCHAR}, #{riskCode,jdbcType=VARCHAR},  #{riskType,jdbcType=VARCHAR},
       #{prem,jdbcType=DOUBLE},
      #{amountGrageCode,jdbcType=VARCHAR}, #{deductibleAttr,jdbcType=VARCHAR}, #{deductible,jdbcType=DOUBLE},
      #{compensationRatio,jdbcType=DOUBLE}, #{insureState,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FPInsureEflexPlan">
    insert into fpinsureeflexplan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="insureElfexPlanNo != null">
        InsureElfexPlanNo,
      </if>
      <if test="ensureCode != null">
        EnsureCode,
      </if>
      <if test="personId != null">
        PersonId,
      </if>
      <if test="perNo != null">
        PerNo,
      </if>
      <if test="appntYear != null">
        AppntYear,
      </if>
      <if test="riskCode != null">
        RiskCode,
      </if>
      <if test="riskType != null">
        RiskType,
      </if>
      <if test="prem != null">
        Prem,
      </if>
      <if test="amountGrageCode != null">
        AmountGrageCode,
      </if>
      <if test="deductibleAttr != null">
        DeductibleAttr,
      </if>
      <if test="deductible != null">
        Deductible,
      </if>
      <if test="compensationRatio != null">
        CompensationRatio,
      </if>
      <if test="insureState != null">
        InsureState,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="insureElfexPlanNo != null">
        #{insureElfexPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="ensureCode != null">
        #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=VARCHAR},
      </if>
      <if test="perNo != null">
        #{perNo,jdbcType=VARCHAR},
      </if>
      <if test="appntYear != null">
        #{appntYear,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="riskType != null">
        #{riskType,jdbcType=VARCHAR},
      </if>
      <if test="prem != null">
        #{prem,jdbcType=DOUBLE},
      </if>
      <if test="amountGrageCode != null">
        #{amountGrageCode,jdbcType=VARCHAR},
      </if>
      <if test="deductibleAttr != null">
        #{deductibleAttr,jdbcType=VARCHAR},
      </if>
      <if test="deductible != null">
        #{deductible,jdbcType=DOUBLE},
      </if>
      <if test="compensationRatio != null">
        #{compensationRatio,jdbcType=DOUBLE},
      </if>
      <if test="insureState != null">
        #{insureState,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FPInsureEflexPlan">
    update fpinsureeflexplan
    <set>
      <if test="ensureCode != null">
        EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        PersonId = #{personId,jdbcType=VARCHAR},
      </if>
      <if test="perNo != null">
        PerNo = #{perNo,jdbcType=VARCHAR},
      </if>
      <if test="appntYear != null">
        AppntYear = #{appntYear,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        RiskCode = #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="riskType != null">
        RiskType = #{riskType,jdbcType=VARCHAR},
      </if>
      <if test="prem != null">
        Prem = #{prem,jdbcType=VARCHAR},
      </if>
      <if test="amountGrageCode != null">
        AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR},
      </if>
      <if test="deductibleAttr != null">
        DeductibleAttr = #{deductibleAttr,jdbcType=VARCHAR},
      </if>
      <if test="deductible != null">
        Deductible = #{deductible,jdbcType=DOUBLE},
      </if>
      <if test="compensationRatio != null">
        CompensationRatio = #{compensationRatio,jdbcType=DOUBLE},
      </if>
      <if test="insureState != null">
        InsureState = #{insureState,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where InsureElfexPlanNo = #{insureElfexPlanNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FPInsureEflexPlan">
    update fpinsureeflexplan
    set EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      PersonId = #{personId,jdbcType=VARCHAR},
      PerNo = #{perNo,jdbcType=VARCHAR},
      AppntYear = #{appntYear,jdbcType=VARCHAR},
      RiskCode = #{riskCode,jdbcType=VARCHAR},
      RiskType = #{riskType,jdbcType=VARCHAR},
      Prem = #{prem,jdbcType=VARCHAR},
      AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR},
      DeductibleAttr = #{deductibleAttr,jdbcType=VARCHAR},
      Deductible = #{deductible,jdbcType=DOUBLE},
      CompensationRatio = #{compensationRatio,jdbcType=DOUBLE},
      InsureState = #{insureState,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where InsureElfexPlanNo = #{insureElfexPlanNo,jdbcType=VARCHAR}
  </update>
  <insert id="insertfPInsureEflexPlan" parameterType="com.sinosoft.eflex.model.FPInsureEflexPlan">
   INSERT INTO FPInsureEflexPlan
    (InsureElfexPlanNo,EnsureCode,PersonId,PerNo,AppntYear,RiskCode,RiskType,Prem
    ,AmountGrageCode,DeductibleAttr,Deductible,CompensationRatio,InsureState
    ,Operator,OperatorCom,MakeDate, MakeTime,ModifyDate, ModifyTime)
    <foreach collection="list" item="item" index="index" separator="union all">
      SELECT #{list[${index}].insureElfexPlanNo,jdbcType=VARCHAR},
      #{list[${index}].ensureCode,jdbcType=VARCHAR },
      #{list[${index}].personId,jdbcType=VARCHAR},
      #{list[${index}].perNo,jdbcType=VARCHAR},
      #{list[${index}].appntYear,jdbcType=VARCHAR},
      #{list[${index}].riskCode,jdbcType=VARCHAR},
      #{list[${index}].riskType,jdbcType=VARCHAR},
      #{list[${index}].prem,jdbcType=DOUBLE},
      #{list[${index}].amountGrageCode,jdbcType=VARCHAR},
      #{list[${index}].deductibleAttr,jdbcType=VARCHAR},
      #{list[${index}].deductible,jdbcType=DOUBLE},
      #{list[${index}].compensationRatio,jdbcType=DOUBLE},
      #{list[${index}].insureState,jdbcType=VARCHAR},
      #{list[${index}].operator,jdbcType=VARCHAR},
      #{list[${index}].operatorCom,jdbcType=VARCHAR},
      #{list[${index}].makeDate,jdbcType=DATE},
      #{list[${index}].makeTime,jdbcType=VARCHAR},
      #{list[${index}].modifyDate,jdbcType=DATE},
      #{list[${index}].modifyTime,jdbcType=VARCHAR}
      from DUAL
    </foreach>
  </insert>
  <insert id="insertfPEflexCheckRuleEflexPlan" parameterType="com.sinosoft.eflex.model.FPInsureEflexPlan">
   INSERT INTO fPEflexCheckRuleEflexPlan
    (InsureElfexPlanNo,EnsureCode,PersonId,PerNo,AppntYear,RiskCode,RiskType,Prem
    ,AmountGrageCode,DeductibleAttr,Deductible,CompensationRatio,InsureState
    ,Operator,OperatorCom,MakeDate, MakeTime,ModifyDate, ModifyTime)
    <foreach collection="list" item="item" index="index" separator="union all">
      SELECT #{list[${index}].insureElfexPlanNo,jdbcType=VARCHAR},
      #{list[${index}].ensureCode,jdbcType=VARCHAR },
      #{list[${index}].personId,jdbcType=VARCHAR},
      #{list[${index}].perNo,jdbcType=VARCHAR},
      #{list[${index}].appntYear,jdbcType=VARCHAR},
      #{list[${index}].riskCode,jdbcType=VARCHAR},
      #{list[${index}].riskType,jdbcType=VARCHAR},
      #{list[${index}].prem,jdbcType=DOUBLE},
      #{list[${index}].amountGrageCode,jdbcType=VARCHAR},
      #{list[${index}].deductibleAttr,jdbcType=VARCHAR},
      #{list[${index}].deductible,jdbcType=DOUBLE},
      #{list[${index}].compensationRatio,jdbcType=DOUBLE},
      #{list[${index}].insureState,jdbcType=VARCHAR},
      #{list[${index}].operator,jdbcType=VARCHAR},
      #{list[${index}].operatorCom,jdbcType=VARCHAR},
      #{list[${index}].makeDate,jdbcType=DATE},
      #{list[${index}].makeTime,jdbcType=VARCHAR},
      #{list[${index}].modifyDate,jdbcType=DATE},
      #{list[${index}].modifyTime,jdbcType=VARCHAR}
      from DUAL
    </foreach>
  </insert>
  <select id="getTotlePrem" parameterType="java.util.Map" resultType="java.util.Map">
  	SELECT
		t.personId,
		SUM(t.prem) prem
	FROM
		(
			SELECT
				a.personId,
				ROUND(sum(IFNULL(b.prem, 0)),2) prem
			FROM
				FPInsureEflexPlan a,
				FPInsureEflexPlanOptional b
			WHERE
				a.perNo = #{perNo,jdbcType=VARCHAR}
			AND a.ensureCode = #{ensureCode,jdbcType=VARCHAR}
			AND a.insureElfexPlanNo = b.insureElfexPlanNo
			GROUP BY
				a.personId
			UNION ALL
				SELECT
					a.personId,
					ROUND(sum(IFNULL(a.prem, 0)),2) prem
				FROM
					FPInsureEflexPlan a
				WHERE
					a.perNo = #{perNo,jdbcType=VARCHAR}
				AND a.ensureCode = #{ensureCode,jdbcType=VARCHAR}
				GROUP BY
					a.personId
		) t
	GROUP BY
		t.personId
  </select>
  <select id="getPersonAmountGrageCode" parameterType="java.util.Map" resultType="java.lang.String">
  	select DISTINCT a.amountGrageCode
  	from FPInsureEflexPlan a
  	where a.perNo = #{perNo,jdbcType=VARCHAR}
    and a.ensureCode = #{ensureCode,jdbcType=VARCHAR}
    and a.personId = #{personId,jdbcType=VARCHAR}
  </select>
  <update id="updateInsureState" parameterType="java.util.Map">
  		update FPInsureEflexPlan set insureState=#{insureState,jdbcType=VARCHAR}
  		where perNo = #{perNo,jdbcType=VARCHAR}
	    and ensureCode = #{ensureCode,jdbcType=VARCHAR}
	    and personId = #{personId,jdbcType=VARCHAR}
  </update>
  <delete id="deleteAllFcInsureEflexPlan" parameterType="java.lang.String">
  		delete from FcInsureEflexPlan where orderItemDetailNo in 
  		(select orderItemDetailNo from fcorderitem where orderno=#{orderNo,jdbcType=VARCHAR})
  </delete>
  <delete id="deleteAllFcInsureEflexPlanOptional" parameterType="java.lang.String">
  		delete from FcInsureEflexPlanOptional where orderItemDetailNo in 
  		(select orderItemDetailNo from fcorderitem where orderno=#{orderNo,jdbcType=VARCHAR})
  </delete>
  <delete id="deleteFcInsureEflexPlan" parameterType="java.lang.String">
  		delete from FcInsureEflexPlan where orderItemDetailNo in 
  		(select a.OrderItemDetailNo from fcorderitem a,fcorderinsured b 
		where a.orderitemno=b.orderitemno
		      and a.orderno=b.orderno
		      and b.orderno=#{orderNo,jdbcType=VARCHAR}
		      and b.personid=#{personId,jdbcType=VARCHAR})
  </delete>
  <delete id="deleteFcInsureEflexPlanOptional" parameterType="java.lang.String">
  		delete from FcInsureEflexPlanOptional where orderItemDetailNo in 
  		(select a.OrderItemDetailNo from fcorderitem a,fcorderinsured b 
		where a.orderitemno=b.orderitemno
		      and a.orderno=b.orderno
		      and b.orderno=#{orderNo,jdbcType=VARCHAR}
		      and b.personid=#{personId,jdbcType=VARCHAR})
  </delete>
  <select id="getNoInsuredFamilyInfo" parameterType="java.lang.String" resultType="java.util.Map">
	  select a.personId,
			 a.BirthDate BirthDay,
			 a.JoinMedProtect,
		     a.OccupationType,
		     (select IFNULL(InsuredNumber,0) from fcensure where ensureCode = #{ensureCode,jdbcType=VARCHAR}) InsureCount,
		     (select CvaliDate from fcensure where ensureCode = #{ensureCode,jdbcType=VARCHAR}) CvaliDate,
		     a.Sex,
		     b.Relation
      from fcperson a,fcstafffamilyrela b where
      a.personid=b.personid
      and b.perno= #{perNo,jdbcType=VARCHAR}
	  and a.personid in (
	  	select personId from fcstafffamilyrela where perNo = #{perNo,jdbcType=VARCHAR} and relation != '0'
	    and personId not in (select personId from FPInsureEflexPlan where perNo = #{perNo,jdbcType=VARCHAR}
	    and ensureCode = #{ensureCode,jdbcType=VARCHAR})
	  )
  </select>
  <select id="getPersonIdList" parameterType="java.lang.String" resultType="java.util.Map">
	  select DISTINCT a.personId,b.name personName from FPInsureEflexPlan a,fcperson b where a.personId=b.personId
	  and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
  	  and a.perNo=#{perNo,jdbcType=VARCHAR}
  </select>
  <select id="selectRiskCodeList" parameterType="java.lang.String" resultType="java.lang.String">
  	select distinct riskCode from FPInsureEflexPlan where perNo = #{perNo,jdbcType=VARCHAR}
	    and ensureCode = #{ensureCode,jdbcType=VARCHAR}
	    and personId = #{personId,jdbcType=VARCHAR}
  </select>
  <select id="selectCheckRuleRiskCodeList" parameterType="java.lang.String" resultType="java.lang.String">
  	select distinct riskCode from fPEflexCheckRuleEflexPlan where perNo = #{perNo,jdbcType=VARCHAR}
	    and ensureCode = #{ensureCode,jdbcType=VARCHAR}
	    and personId = #{personId,jdbcType=VARCHAR}
  </select>
  <select id="selectDutyListBY_15070" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPlanRiskDuty">
  	select a.prem,b.amnt,b.dutyCode from FPInsureEflexPlan a,FcDutyAmountGrade b
  	where a.AmountGrageCode=b.AmountGrageCode
  	and a.riskcode=#{riskCode,jdbcType=VARCHAR}
  	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
  	and a.personId=#{personId,jdbcType=VARCHAR}
  	and a.perNo=#{perNo,jdbcType=VARCHAR}
  	and b.dutycode in ('GD0050','GD0051','GD0052','GD0054')
  	order by b.Amnt desc limit 1
  </select>
  <select id="selectCheckRuleDutyListBY_15070" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPlanRiskDuty">
  	select a.prem,b.amnt,b.dutyCode from fPEflexCheckRuleEflexPlan a,FcDutyAmountGrade b
  	where a.AmountGrageCode=b.AmountGrageCode
  	and a.riskcode=#{riskCode,jdbcType=VARCHAR}
  	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
  	and a.personId=#{personId,jdbcType=VARCHAR}
  	and a.perNo=#{perNo,jdbcType=VARCHAR}
  	and b.dutycode in ('GD0050','GD0051','GD0052','GD0054')
  	order by b.Amnt desc limit 1
  </select>
  <select id="selectDutyListBY_other" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPlanRiskDuty">
  	select a.prem,b.amnt,b.dutyCode from FPInsureEflexPlan a,FcDutyAmountGrade b
  	where a.AmountGrageCode=b.AmountGrageCode
  	and a.riskcode=#{riskCode,jdbcType=VARCHAR}
  	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
  	and a.personId=#{personId,jdbcType=VARCHAR}
  	and a.perNo=#{perNo,jdbcType=VARCHAR}
  	union
  	select d.prem,c.amnt,c.OptDutyCode dutyCode
  	from FPInsureEflexPlan a,
  		 FcDutyGradeOptionalAmountInfo c,
  		 FPInsureEflexPlanOptional d
  	where a.AmountGrageCode=c.AmountGrageCode
  	and a.InsureElfexPlanNo=d.InsureElfexPlanNo
  	and c.OptDutyCode=d.OptDutyCode
  	and a.riskcode=#{riskCode,jdbcType=VARCHAR}
  	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
  	and a.personId=#{personId,jdbcType=VARCHAR}
  	and a.perNo=#{perNo,jdbcType=VARCHAR}
  </select>
  <select id="selectCheckRuleDutyListBY_other" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPlanRiskDuty">
  	select a.prem,b.amnt,b.dutyCode from fPEflexCheckRuleEflexPlan a,FcDutyAmountGrade b
  	where a.AmountGrageCode=b.AmountGrageCode
  	and a.riskcode=#{riskCode,jdbcType=VARCHAR}
  	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
  	and a.personId=#{personId,jdbcType=VARCHAR}
  	and a.perNo=#{perNo,jdbcType=VARCHAR}
  	union
  	select d.prem,c.amnt,c.OptDutyCode dutyCode
  	from fPEflexCheckRuleEflexPlan a,
  		 FcDutyGradeOptionalAmountInfo c,
  		 fPEflexCheckRuleEflexPlanOptional d
  	where a.AmountGrageCode=c.AmountGrageCode
  	and a.InsureElfexPlanNo=d.InsureElfexPlanNo
  	and c.OptDutyCode=d.OptDutyCode
  	and a.riskcode=#{riskCode,jdbcType=VARCHAR}
  	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
  	and a.personId=#{personId,jdbcType=VARCHAR}
  	and a.perNo=#{perNo,jdbcType=VARCHAR}
  </select>
  <select id="selectDutyListBY_17050" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPlanRiskDuty">
  	select a.prem,b.amnt/2 amnt,b.dutyCode from FPInsureEflexPlan a,FcDutyAmountGrade b
  	where a.AmountGrageCode=b.AmountGrageCode
  	and a.riskcode=#{riskCode,jdbcType=VARCHAR}
  	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
  	and a.personId=#{personId,jdbcType=VARCHAR}
  	and a.perNo=#{perNo,jdbcType=VARCHAR}
  </select>
  <select id="selectCheckRuleDutyListBY_17050" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPlanRiskDuty">
  	select a.prem,b.amnt/2 amnt,b.dutyCode from fPEflexCheckRuleEflexPlan a,FcDutyAmountGrade b
  	where a.AmountGrageCode=b.AmountGrageCode
  	and a.riskcode=#{riskCode,jdbcType=VARCHAR}
  	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
  	and a.personId=#{personId,jdbcType=VARCHAR}
  	and a.perNo=#{perNo,jdbcType=VARCHAR}
  </select>
  <select id="getAllSaveFamily" parameterType="java.lang.String" resultType="java.util.Map">
  	SELECT
		a.personId,
	  b.relation
	FROM
		FPInsureEflexPlan a,
		fcstafffamilyrela b
	WHERE
		a.perno = b.perno
	AND a.personid = b.personid
	AND b.relation &lt;&gt; '0'
	AND a.ensurecode = #{ensureCode,jdbcType=VARCHAR}
	AND a.perNo = #{perNo,jdbcType=VARCHAR}
  </select>
  <select id="checkInfoIsExist" parameterType="java.util.Map" resultType="java.util.Map">
  		select CAST(a.prem AS CHAR) prem,CAST(CONVERT(b.amnt,DECIMAL(20,0)) AS CHAR) amnt,
  		b.dutyCode,a.riskCode,
  		a.riskType,a.AmountGrageCode,'1' isChoice,REPLACE(CAST(ROUND(a.Deductible,0) AS CHAR),',','') Deductible,
  		REPLACE(CAST(ROUND(a.CompensationRatio,0) AS CHAR),',','') CompensationRatio,
  		CASE WHEN a.RiskCode='15070' then
								CASE WHEN a.RiskType='01' then CONCAT(c.RiskName,'(民航班机)')
									 WHEN a.RiskType='02' then CONCAT(c.RiskName,'(轨道交通工具)')
								     WHEN a.RiskType='03' then CONCAT(c.RiskName,'(水运公共交通工具)')
									 WHEN a.RiskType='04' then CONCAT(c.RiskName,'(公路公共交通工具)')
								else CONCAT(c.RiskName,'(私家车)') end
					 else c.RiskName end as riskName
  		from FPInsureEflexPlan a,FcDutyAmountGrade b,fdriskinfo c
	  	where a.AmountGrageCode=b.AmountGrageCode
	  	and a.riskCode=c.riskCode
	  	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
	  	and a.personId=#{personId,jdbcType=VARCHAR}
	  	and a.perNo=#{perNo,jdbcType=VARCHAR}
	  	union all
	  	select d.prem,CAST(CONVERT(c.amnt,DECIMAL(20,0))AS CHAR) amnt,c.OptDutyCode dutyCode,a.riskCode,a.riskType,a.AmountGrageCode,'2' isChoice,
	  	'' Deductible,'' CompensationRatio,
	  	CASE WHEN a.RiskCode='15070' then
								CASE WHEN a.RiskType='01' then CONCAT(b.RiskName,'(民航班机)')
									 WHEN a.RiskType='02' then CONCAT(b.RiskName,'(轨道交通工具)')
								     WHEN a.RiskType='03' then CONCAT(b.RiskName,'(水运公共交通工具)')
									 WHEN a.RiskType='04' then CONCAT(b.RiskName,'(公路公共交通工具)')
								else CONCAT(b.RiskName,'(私家车)') end
					 else b.RiskName end as riskName
	  	from FPInsureEflexPlan a,
	  		 FcDutyGradeOptionalAmountInfo c,
	  		 FPInsureEflexPlanOptional d,
	  		 fdriskinfo b
	  	where a.AmountGrageCode=c.AmountGrageCode
	  	and a.riskCode=b.riskCode
	  	and a.InsureElfexPlanNo=d.InsureElfexPlanNo
	  	and c.OptDutyCode=d.OptDutyCode
	  	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
	  	and a.personId=#{personId,jdbcType=VARCHAR}
	  	and a.perNo=#{perNo,jdbcType=VARCHAR}
    </select>

    <select id="getInsureRiskInfo" parameterType="com.sinosoft.eflex.model.insureEflexPlanPage.GetInsureRiskInfoReq"
            resultType="com.sinosoft.eflex.model.insureEflexPlanPage.GetInsureRiskInfoResp">
  		select CAST(a.prem AS CHAR) prem,CAST(CONVERT(b.amnt,DECIMAL(20,0)) AS CHAR) amnt,
  		b.dutyCode,a.riskCode,
  		a.riskType,a.AmountGrageCode,'1' isChoice,REPLACE(CAST(ROUND(a.Deductible,0) AS CHAR),',','') Deductible,
  		REPLACE(CAST(ROUND(a.CompensationRatio,0) AS CHAR),',','') CompensationRatio,
  		CASE WHEN a.RiskCode='15070' then
								CASE WHEN a.RiskType='01' then CONCAT(c.RiskName,'(民航班机)')
									 WHEN a.RiskType='02' then CONCAT(c.RiskName,'(轨道交通工具)')
								     WHEN a.RiskType='03' then CONCAT(c.RiskName,'(水运公共交通工具)')
									 WHEN a.RiskType='04' then CONCAT(c.RiskName,'(公路公共交通工具)')
								else CONCAT(c.RiskName,'(私家车)') end
					 else c.RiskName end as riskName
  		from FPInsureEflexPlan a,FcDutyAmountGrade b,fdriskinfo c
	  	where a.AmountGrageCode=b.AmountGrageCode
	  	and a.riskCode=c.riskCode
	  	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
	  	and a.personId=#{personId,jdbcType=VARCHAR}
	  	and a.perNo=#{perNo,jdbcType=VARCHAR}
	  	union all
	  	select d.prem,CAST(CONVERT(c.amnt,DECIMAL(20,0))AS CHAR) amnt,c.OptDutyCode dutyCode,a.riskCode,a.riskType,a.AmountGrageCode,'2' isChoice,
	  	'' Deductible,'' CompensationRatio,
	  	CASE WHEN a.RiskCode='15070' then
								CASE WHEN a.RiskType='01' then CONCAT(b.RiskName,'(民航班机)')
									 WHEN a.RiskType='02' then CONCAT(b.RiskName,'(轨道交通工具)')
								     WHEN a.RiskType='03' then CONCAT(b.RiskName,'(水运公共交通工具)')
									 WHEN a.RiskType='04' then CONCAT(b.RiskName,'(公路公共交通工具)')
								else CONCAT(b.RiskName,'(私家车)') end
					 else b.RiskName end as riskName
	  	from FPInsureEflexPlan a,
	  		 FcDutyGradeOptionalAmountInfo c,
	  		 FPInsureEflexPlanOptional d,
	  		 fdriskinfo b
	  	where a.AmountGrageCode=c.AmountGrageCode
	  	and a.riskCode=b.riskCode
	  	and a.InsureElfexPlanNo=d.InsureElfexPlanNo
	  	and c.OptDutyCode=d.OptDutyCode
	  	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
	  	and a.personId=#{personId,jdbcType=VARCHAR}
	  	and a.perNo=#{perNo,jdbcType=VARCHAR}
    </select>

    <select id="selectDutyListByPersonID" parameterType="java.lang.String" resultType="java.util.Map">
      SELECT a.riskCode,a.dutyCode,a.riskName,a.dutyName,a.dutyRange,a.prem,a.amnt,a.maxGetDay,a.getLimit,a.getLimitType,a.getRatio FROM (
        SELECT f.`RiskCode` AS riskCode,
                g.`DutyCode` AS dutyCode,
                f.`RiskName` AS riskName,
                CASE WHEN f.`RiskCode` = '17050' THEN g.`DutyRange` ELSE g.`DutyName` END AS dutyName,
                g.`DutyRange` AS dutyRange,
                a.`prem` AS prem,
                c.`Amnt` AS amnt,
                c.`MaxGetDay` AS MaxGetDay,
                if(a.`Deductible` is NULL ,'',CONVERT(a.`Deductible`,SIGNED)) AS getLimit,
                if(a.`DeductibleAttr` is NULL ,'',CONVERT(a.`DeductibleAttr`,SIGNED))AS getLimitType,
                if(a.`CompensationRatio` is NULL ,'',CONVERT(a.`CompensationRatio`,SIGNED))AS getRatio
        FROM FcInsureEflexPlan a
        LEFT JOIN fcorderitem b ON a.`OrderItemDetailNo` = b.`OrderItemDetailNo`
        LEFT JOIN FcDutyAmountGrade c ON a.`AmountGrageCode` = c.`AmountGrageCode`
        LEFT JOIN fdriskinfo f ON c.`RiskCode` = f.`RiskCode`
        LEFT JOIN fdriskdutyinfo g ON c.`DutyCode` = g.`DutyCode`
        WHERE b.`OrderItemNo` =#{orderItemNo}
        UNION
        SELECT d.`RiskCode` AS  riskCode,
                c.`OptDutyCode` AS dutyCode,
                f.`RiskName` AS riskName,
                CASE WHEN f.`RiskCode` = '17050' THEN g.`DutyRange` ELSE g.`DutyName` END AS dutyName,
                g.`DutyRange` AS dutyRange,
                a.`prem` AS prem,
                c.`Amnt` AS amnt,
                c.`MaxGetDay` AS MaxGetDay,
                if(d.RiskCode='15070',CONVERT(x.Deductible,SIGNED),'') AS getLimit,
                if(d.RiskCode='15070',CONVERT(d.AnnualTimeDeduction,SIGNED),'') AS getLimitType,
                if(d.RiskCode='15070',CONVERT(y.CompensationRatio,SIGNED),'') AS getRatio
        FROM FcInsureEflexPlanOptional a
        LEFT JOIN fcorderitem b ON a.`OrderItemDetailNo` = b.`OrderItemDetailNo`
        LEFT JOIN FcDutyGradeOptionalAmountInfo c ON a.`AmountGrageCode` = c.`AmountGrageCode` AND a.`OptDutyCode` = c.`OptDutyCode`
        LEFT JOIN FcDutyAmountGrade d ON d.`AmountGrageCode` = c.`AmountGrageCode`
        LEFT JOIN FcDutyGroupDeductible x on x.AmountGrageCode = d.AmountGrageCode
        LEFT JOIN FcDutGradeCompensationRatio y on y.AmountGrageCode = d.AmountGrageCode
        LEFT JOIN fdriskinfo f ON f.`RiskCode` = d.`RiskCode`
        LEFT JOIN fdriskdutyinfo g ON c.`OptDutyCode` = g.`DutyCode`
        WHERE b.`OrderItemNo` =#{orderItemNo}
        GROUP BY c.`OptDutyCode` ASC
      ) AS a
      ORDER BY a.riskCode,a.dutyCode
    </select>
    <select id="selectStaffFamilyInsured" resultType="java.lang.Integer">
        select count(*)
        from fpinsureeflexplan where PerNo = #{perNo} and PersonId = #{personId}
    </select>
    <insert id="saveFcInsureEflexPlan" parameterType="java.util.Map">
    	insert into FcInsureEflexPlan(OrderItemDetailNo,AmountGrageCode,
    	DeductibleAttr,Deductible,CompensationRatio,Prem,
    	Operator,OperatorCom,MakeDate,MakeTime,ModifyDate,ModifyTime)
    	select #{orderItemDetailNo,jdbcType=VARCHAR},
    		   AmountGrageCode,DeductibleAttr,Deductible,CompensationRatio,Prem,
    		   #{operator,jdbcType=VARCHAR},
    		   '',
    		   #{date,jdbcType=DATE},
    		   #{time,jdbcType=VARCHAR},
    		   #{date,jdbcType=DATE},
    		   #{time,jdbcType=VARCHAR}
        from FpInsureEflexPlan 
    	where ensurecode=#{ensureCode,jdbcType=VARCHAR}
	  	and personId=#{personId,jdbcType=VARCHAR}
	  	and perNo=#{perNo,jdbcType=VARCHAR}
    </insert>
    <insert id="saveFcInsureEflexPlanOptional" parameterType="java.util.Map">
    	insert into FcInsureEflexPlanOptional(OrderItemDetailNo,AmountGrageCode,
    	OptDutyCode,Prem,
    	Operator,OperatorCom,MakeDate,MakeTime,ModifyDate,ModifyTime)
    	select #{orderItemDetailNo,jdbcType=VARCHAR},
    		   a.AmountGrageCode,b.OptDutyCode,b.Prem,
    		   #{operator,jdbcType=VARCHAR},
    		   '',
    		   #{date,jdbcType=DATE},
    		   #{time,jdbcType=VARCHAR},
    		   #{date,jdbcType=DATE},
    		   #{time,jdbcType=VARCHAR}
        from FpInsureEflexPlan a,FPInsureEflexPlanOptional b
    	where a.InsureElfexPlanNo=b.InsureElfexPlanNo
    	and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
	  	and a.personId=#{personId,jdbcType=VARCHAR}
	  	and a.perNo=#{perNo,jdbcType=VARCHAR}
    </insert>
    <select id="getFcInsureEflexPlanList" parameterType="java.util.Map"
            resultType="com.sinosoft.eflex.model.FcInsureEflexPlan" flushCache="true">
    	select #{orderItemDetailNo,jdbcType=VARCHAR} as orderItemDetailNo,
    		   AmountGrageCode,
    		   DeductibleAttr,
    		   Deductible,
    		   CompensationRatio,
    		   Prem,
    		   #{operator,jdbcType=VARCHAR} as operator,
    		   '',
    		   #{date,jdbcType=DATE} as makeDate,
    		   #{time,jdbcType=VARCHAR} as makeTime,
    		   #{date,jdbcType=DATE} as modifyDate,
    		   #{time,jdbcType=VARCHAR} as modifyTime
        from FpInsureEflexPlan
    	where ensurecode=#{ensureCode,jdbcType=VARCHAR}
	  	and personId=#{personId,jdbcType=VARCHAR}
	  	and perNo=#{perNo,jdbcType=VARCHAR}
    </select>
    <insert id="saveFcInsureEflexPlanList" parameterType="java.util.List">
        insert into FcInsureEflexPlan
        (OrderItemDetailNo,
        AmountGrageCode,
        DeductibleAttr,
        Deductible,
        CompensationRatio,
        Prem,
        Operator,
        OperatorCom,
        MakeDate,
        MakeTime,
        ModifyDate,
        ModifyTime) values
        <foreach collection="list" item="item" separator=",">
            (#{item.OrderItemDetailNo},
            #{item.AmountGrageCode},
            #{item.DeductibleAttr},
            #{item.Deductible},
            #{item.CompensationRatio},
            #{item.Prem},
            #{item.Operator},
            #{item.OperatorCom},
            #{item.MakeDate},
            #{item.MakeTime},
            #{item.ModifyDate},
            #{item.ModifyTime})
        </foreach>
        and perNo=#{perNo,jdbcType=VARCHAR}
    </insert>
</mapper>