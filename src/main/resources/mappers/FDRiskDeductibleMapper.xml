<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDRiskDeductibleMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDRiskDeductible">
    <id column="RiskCode" jdbcType="VARCHAR" property="riskCode" />
    <id column="Deductible" jdbcType="DOUBLE" property="deductible" />
    <result column="adjustFactor" jdbcType="DOUBLE" property="adjustFactor" />
    <result column="isCheck" jdbcType="VARCHAR" property="isCheck" />
  </resultMap>
  <sql id="Base_Column_List">
    RiskCode, Deductible, adjustFactor
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultType="com.sinosoft.eflex.model.FDRiskDeductible">
    select
    RiskCode, Deductible, adjustFactor,if(Deductible IN (SELECT CAST(Deductible AS SIGNED) AS Deductible FROM FcDutyGroupDeductible WHERE AmountGrageCode = #{amountGrageCode}),'0','1') as isCheck
    from fdriskdeductible
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
      <if test=" deductible != null and deductible != '' ">
        and Deductible = #{deductible,jdbcType=DOUBLE}
      </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fdriskdeductible
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
      and Deductible = #{deductible,jdbcType=DOUBLE}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDRiskDeductible">
    insert into fdriskdeductible (RiskCode, Deductible, adjustFactor
      )
    values (#{riskCode,jdbcType=VARCHAR}, #{deductible,jdbcType=DOUBLE}, #{adjustFactor,jdbcType=DOUBLE}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDRiskDeductible">
    insert into fdriskdeductible
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="riskCode != null">
        RiskCode,
      </if>
      <if test="deductible != null">
        Deductible,
      </if>
      <if test="adjustFactor != null">
        adjustFactor,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="deductible != null">
        #{deductible,jdbcType=DOUBLE},
      </if>
      <if test="adjustFactor != null">
        #{adjustFactor,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDRiskDeductible">
    update fdriskdeductible
    <set>
      <if test="adjustFactor != null">
        adjustFactor = #{adjustFactor,jdbcType=DOUBLE},
      </if>
    </set>
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
      and Deductible = #{deductible,jdbcType=DOUBLE}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDRiskDeductible">
    update fdriskdeductible
    set adjustFactor = #{adjustFactor,jdbcType=DOUBLE}
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
      and Deductible = #{deductible,jdbcType=DOUBLE}
  </update>
</mapper>