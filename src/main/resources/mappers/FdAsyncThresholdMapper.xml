<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FdAsyncThresholdMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FdAsyncThreshold">
        <id column="BusinessCode" jdbcType="VARCHAR" property="businessCode"/>
        <result column="BusinessType" jdbcType="VARCHAR" property="businessType"/>
        <result column="PeopleLimit" jdbcType="INTEGER" property="peopleLimit"/>
        <result column="SingleDealTime" jdbcType="INTEGER" property="singleDealTime"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        BusinessCode
        , BusinessType, PeopleLimit, SingleDealTime, Remark, Operator, OperatorCom,
    MakeDate, MakeTime, ModifyDate, ModifyTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdasyncthreshold
        where BusinessCode = #{businessCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdasyncthreshold
        where BusinessCode = #{businessCode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FdAsyncThreshold">
        insert into fdasyncthreshold (BusinessCode, BusinessType, PeopleLimit,
                                      SingleDealTime, Remark, Operator,
                                      OperatorCom, MakeDate, MakeTime,
                                      ModifyDate, ModifyTime)
        values (#{businessCode,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, #{peopleLimit,jdbcType=INTEGER},
                #{singleDealTime,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
                #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
                #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FdAsyncThreshold">
        insert into fdasyncthreshold
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessCode != null">
                BusinessCode,
            </if>
            <if test="businessType != null">
                BusinessType,
            </if>
            <if test="peopleLimit != null">
                PeopleLimit,
            </if>
            <if test="singleDealTime != null">
                SingleDealTime,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessCode != null">
                #{businessCode,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="peopleLimit != null">
                #{peopleLimit,jdbcType=INTEGER},
            </if>
            <if test="singleDealTime != null">
                #{singleDealTime,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FdAsyncThreshold">
        update fdasyncthreshold
        <set>
            <if test="businessType != null">
                BusinessType = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="peopleLimit != null">
                PeopleLimit = #{peopleLimit,jdbcType=INTEGER},
            </if>
            <if test="singleDealTime != null">
                SingleDealTime = #{singleDealTime,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where BusinessCode = #{businessCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FdAsyncThreshold">
        update fdasyncthreshold
        set BusinessType   = #{businessType,jdbcType=VARCHAR},
            PeopleLimit    = #{peopleLimit,jdbcType=INTEGER},
            SingleDealTime = #{singleDealTime,jdbcType=INTEGER},
            Remark         = #{remark,jdbcType=VARCHAR},
            Operator       = #{operator,jdbcType=VARCHAR},
            OperatorCom    = #{operatorCom,jdbcType=VARCHAR},
            MakeDate       = #{makeDate,jdbcType=DATE},
            MakeTime       = #{makeTime,jdbcType=VARCHAR},
            ModifyDate     = #{modifyDate,jdbcType=DATE},
            ModifyTime     = #{modifyTime,jdbcType=VARCHAR}
        where BusinessCode = #{businessCode,jdbcType=VARCHAR}
    </update>
    <select id="selectPeopleLimit" parameterType="java.lang.String" resultType="java.lang.Integer">
        select PeopleLimit
        from fdasyncthreshold
        where BusinessCode = #{businessCode,jdbcType=VARCHAR}
    </select>
</mapper>