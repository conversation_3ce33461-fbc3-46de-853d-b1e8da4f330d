<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcPerImpartDetailMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcPerImpartDetail">
        <id column="OrderItemNo" jdbcType="VARCHAR" property="orderItemNo"/>
        <id column="ImpartVer" jdbcType="VARCHAR" property="impartVer"/>
        <id column="ImpartCode" jdbcType="VARCHAR" property="impartCode"/>
        <result column="ImpartContent" jdbcType="VARCHAR" property="impartContent"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    OrderItemNo, ImpartVer, ImpartCode, ImpartContent, MakeDate, MakeTime, ModifyDate, 
    ModifyTime
  </sql>
    <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperimpartdetail
        where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
        and ImpartVer = #{impartVer,jdbcType=VARCHAR}
        and ImpartCode = #{impartCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcperimpartdetail
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
      and ImpartVer = #{impartVer,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FcPerImpartDetail">
    insert into fcperimpartdetail (OrderItemNo, ImpartVer, ImpartCode, 
      ImpartContent, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{orderItemNo,jdbcType=VARCHAR}, #{impartVer,jdbcType=VARCHAR}, #{impartCode,jdbcType=VARCHAR}, 
      #{impartContent,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcPerImpartDetail">
        insert into fcperimpartdetail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderItemNo != null">
                OrderItemNo,
            </if>
            <if test="impartVer != null">
                ImpartVer,
            </if>
            <if test="impartCode != null">
                ImpartCode,
            </if>
            <if test="impartContent != null">
                ImpartContent,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderItemNo != null">
                #{orderItemNo,jdbcType=VARCHAR},
            </if>
            <if test="impartVer != null">
                #{impartVer,jdbcType=VARCHAR},
            </if>
            <if test="impartCode != null">
                #{impartCode,jdbcType=VARCHAR},
            </if>
            <if test="impartContent != null">
                #{impartContent,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcPerImpartDetail">
        update fcperimpartdetail
        <set>
            <if test="impartContent != null">
                ImpartContent = #{impartContent,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
        and ImpartVer = #{impartVer,jdbcType=VARCHAR}
        and ImpartCode = #{impartCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcPerImpartDetail">
    update fcperimpartdetail
    set ImpartContent = #{impartContent,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
      and ImpartVer = #{impartVer,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </update>
</mapper>