<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCPerRegistDayMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCPerRegistDay">
    <id column="RegistDayNo" jdbcType="VARCHAR" property="registDayNo" />
    <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <result column="PerNo" jdbcType="VARCHAR" property="perNo" />
    <result column="OpenDay" jdbcType="DATE" property="openDay" />
    <result column="CloseDay" jdbcType="DATE" property="closeDay" />
    <result column="PersonType" jdbcType="VARCHAR" property="personType" />
    <result column="IsValidy" jdbcType="VARCHAR" property="isValidy" />
    <result column="Validydate" jdbcType="DATE" property="validydate" />
    <result column="EndDate" jdbcType="DATE" property="endDate" />
    <result column="ValidydateType" jdbcType="VARCHAR" property="validydateType" />
    <result column="RegistDayToManNo" jdbcType="VARCHAR" property="registDayToManNo" />
    <result column="StaffGrpPrem" jdbcType="DOUBLE" property="staffGrpPrem" />
    <result column="FamilyGrpPrem" jdbcType="DOUBLE" property="familyGrpPrem" />
    <result column="LockState" jdbcType="VARCHAR" property="lockState" />
      <result column="LevelCode" jdbcType="VARCHAR" property="levelCode"/>
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    RegistDayNo, EnsureCode, PerNo, OpenDay, CloseDay, PersonType, IsValidy, Validydate, 
    EndDate, ValidydateType, RegistDayToManNo, StaffGrpPrem, FamilyGrpPrem,StudentGrpPrem,Operator, 
    OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime,LockState,LevelCode
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcperregistday
    where RegistDayNo = #{registDayNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcperregistday
    where RegistDayNo = #{registDayNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCPerRegistDay">
    insert into fcperregistday (RegistDayNo, EnsureCode, PerNo, 
      OpenDay, CloseDay, PersonType, 
      IsValidy, Validydate, EndDate, 
      ValidydateType, RegistDayToManNo, StaffGrpPrem, 
      FamilyGrpPrem, Operator, OperatorCom, 
      MakeDate, MakeTime, ModifyDate, 
      ModifyTime)
    values (#{registDayNo,jdbcType=VARCHAR}, #{ensureCode,jdbcType=VARCHAR}, #{perNo,jdbcType=VARCHAR}, 
      #{openDay,jdbcType=DATE}, #{closeDay,jdbcType=DATE}, #{personType,jdbcType=VARCHAR}, 
      #{isValidy,jdbcType=VARCHAR}, #{validydate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, 
      #{validydateType,jdbcType=VARCHAR}, #{registDayToManNo,jdbcType=VARCHAR}, #{staffGrpPrem,jdbcType=DOUBLE}, 
      #{familyGrpPrem,jdbcType=DOUBLE}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, 
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, 
      #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCPerRegistDay">
    insert into fcperregistday
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="registDayNo != null">
        RegistDayNo,
      </if>
      <if test="ensureCode != null">
        EnsureCode,
      </if>
      <if test="perNo != null">
        PerNo,
      </if>
      <if test="openDay != null">
        OpenDay,
      </if>
      <if test="closeDay != null">
        CloseDay,
      </if>
      <if test="personType != null">
        PersonType,
      </if>
      <if test="isValidy != null">
        IsValidy,
      </if>
      <if test="validydate != null">
        Validydate,
      </if>
      <if test="levelCode != null">
        LevelCode,
      </if>
      <if test="endDate != null">
        EndDate,
      </if>
      <if test="validydateType != null">
        ValidydateType,
      </if>
      <if test="registDayToManNo != null">
        RegistDayToManNo,
      </if>
      <if test="staffGrpPrem != null">
        StaffGrpPrem,
      </if>
      <if test="familyGrpPrem != null">
        FamilyGrpPrem,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="registDayNo != null">
        #{registDayNo,jdbcType=VARCHAR},
      </if>
      <if test="ensureCode != null">
        #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="perNo != null">
        #{perNo,jdbcType=VARCHAR},
      </if>
      <if test="openDay != null">
        #{openDay,jdbcType=DATE},
      </if>
      <if test="closeDay != null">
        #{closeDay,jdbcType=DATE},
      </if>
      <if test="personType != null">
        #{personType,jdbcType=VARCHAR},
      </if>
      <if test="isValidy != null">
        #{isValidy,jdbcType=VARCHAR},
      </if>
      <if test="validydate != null">
        #{validydate,jdbcType=DATE},
      </if>
      <if test="levelCode != null">
        #{levelCode,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DATE},
      </if>
      <if test="validydateType != null">
        #{validydateType,jdbcType=VARCHAR},
      </if>
      <if test="registDayToManNo != null">
        #{registDayToManNo,jdbcType=VARCHAR},
      </if>
      <if test="staffGrpPrem != null">
        #{staffGrpPrem,jdbcType=DOUBLE},
      </if>
      <if test="familyGrpPrem != null">
        #{familyGrpPrem,jdbcType=DOUBLE},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCPerRegistDay">
    update fcperregistday
    <set>
      <if test="ensureCode != null">
        EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="perNo != null">
        PerNo = #{perNo,jdbcType=VARCHAR},
      </if>
      <if test="openDay != null">
        OpenDay = #{openDay,jdbcType=DATE},
      </if>
      <if test="closeDay != null">
        CloseDay = #{closeDay,jdbcType=DATE},
      </if>
      <if test="personType != null">
        PersonType = #{personType,jdbcType=VARCHAR},
      </if>
      <if test="isValidy != null">
        IsValidy = #{isValidy,jdbcType=VARCHAR},
      </if>
      <if test="validydate != null">
        Validydate = #{validydate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        EndDate = #{endDate,jdbcType=DATE},
      </if>
      <if test="validydateType != null">
        ValidydateType = #{validydateType,jdbcType=VARCHAR},
      </if>
      <if test="registDayToManNo != null">
        RegistDayToManNo = #{registDayToManNo,jdbcType=VARCHAR},
      </if>
      <if test="staffGrpPrem != null">
        StaffGrpPrem = #{staffGrpPrem,jdbcType=DOUBLE},
      </if>
      <if test="familyGrpPrem != null">
        FamilyGrpPrem = #{familyGrpPrem,jdbcType=DOUBLE},
      </if>
      <if test="lockState != null">
        LockState = #{lockState,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where RegistDayNo = #{registDayNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPerRegistDay">
    update fcperregistday
    set EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      PerNo = #{perNo,jdbcType=VARCHAR},
      OpenDay = #{openDay,jdbcType=DATE},
      CloseDay = #{closeDay,jdbcType=DATE},
      PersonType = #{personType,jdbcType=VARCHAR},
      IsValidy = #{isValidy,jdbcType=VARCHAR},
      Validydate = #{validydate,jdbcType=DATE},
      EndDate = #{endDate,jdbcType=DATE},
      ValidydateType = #{validydateType,jdbcType=VARCHAR},
      RegistDayToManNo = #{registDayToManNo,jdbcType=VARCHAR},
      StaffGrpPrem = #{staffGrpPrem,jdbcType=DOUBLE},
      FamilyGrpPrem = #{familyGrpPrem,jdbcType=DOUBLE},
      LockState = #{lockState,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where RegistDayNo = #{registDayNo,jdbcType=VARCHAR}
  </update>

  <select id="selectFCPerRegistDayList" parameterType="java.util.HashMap" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fcperregistday
    where LockState = '0'
    <if test="ensureCode != null">
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </if>
    <if test="perNo != null">
      and PerNo = #{perNo,jdbcType=VARCHAR}
    </if>
    <if test="openDay != null">
      and OpenDay = #{openDay,jdbcType=DATE}
    </if>
    <if test="closeDay != null">
      and CloseDay = #{closeDay,jdbcType=DATE}
    </if>
    <if test="personType != null">
      and PersonType = #{personType,jdbcType=VARCHAR}
    </if>
    <if test="isValidy != null">
      and IsValidy = #{isValidy,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="selectFCPerRegistDayByKey" parameterType="java.util.HashMap" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fcperregistday
    where 1=1
    <if test="ensureCode != null">
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </if>
    <if test="perNo != null">
      and PerNo = #{perNo,jdbcType=VARCHAR}
    </if>
  </select>
    <select id="selectFCPerRegistDayByKey1" parameterType="com.sinosoft.eflex.model.FCPerRegistDay"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperregistday
        where 1=1
        <if test="ensureCode != null">
            and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        </if>
        <if test="perNo != null">
            and PerNo = #{perNo,jdbcType=VARCHAR}
        </if>
    </select>


  <select id="selectStaCount" parameterType="java.lang.String" resultType="java.lang.Integer">
    select count(*)
    from fcperregistday
    where PerNo not in (
      select r.PerNo
      from fcorder r
             join fcgrporder o on o.GrpOrderNo = r.GrpOrderNo
      where o.EnsureCode = #{ensureCode}
    )
      and EnsureCode = #{ensureCode};
  </select>

  <delete id="deleteByParams" parameterType="java.util.HashMap">
    delete from fcperregistday
    where 1=1
    <if test="perNo != null and perNo != '' ">
      and PerNo = #{perNo,jdbcType=VARCHAR}
    </if>
    <if test="ensureCode != null">
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </if>
  </delete>
  <select  id="findNewensureCode" parameterType="java.util.Map" resultType="java.lang.String">
    select EnsureCode from fcperregistday where
    1=1
    <if test="perNo != null and  perNo != ''">
      and PerNo = (select PerNo from fcperinfo where PerNo = #{perNo,jdbcType=VARCHAR} and LockState = '0' order by ModifyDATE,MOdifytime asc limit 1)
    </if>
    <if test="sysDate != null and sysDate !=''">
      <![CDATA[and DATE_FORMAT(OpenDay, '%Y-%m-%d') <=  DATE_FORMAT(#{sysDate}, '%Y-%m-%d')  and DATE_FORMAT(CloseDay, '%Y-%m-%d') >=  DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
    </if>
    and IsValidy = '1'
    order by ModifyDATE DESC,MOdifytime DESC limit 1
  </select>

  <select id="getEnsureByIdNo" parameterType="java.lang.String" resultType="java.util.Map">
    SELECT g.GrpName as grpName,g.GrpNo as grpNo,d.PerNo as perNo,e.EnsureName as ensureName,e.EnsureCode as ensureCode,e.PolicyState as policyState FROM fcensure e
      INNER JOIN fcgrpinfo g ON g.GrpNo = e.GrpNo
      INNER JOIN fcperregistday d ON d.ensureCode = e.ensureCode
      INNER JOIN fduser u ON u.CustomNo = d.PerNo
      WHERE u.IDNo = #{IDNo,jdbcType=VARCHAR} AND u.CustomType = '1'
      <![CDATA[AND DATE_FORMAT(d.OpenDay, '%Y-%m-%d') <=  DATE_FORMAT(NOW(), '%Y-%m-%d') AND DATE_FORMAT(d.CloseDay, '%Y-%m-%d') >=  DATE_FORMAT(NOW(), '%Y-%m-%d')]]>
      AND d.IsValidy = '1'
      ORDER BY e.ModifyDATE DESC,e.MOdifytime DESC
  </select>

  <select id="getPerRegistDayByPersonID" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPerRegistDay">
    SELECT * FROM fcperregistday d
      INNER JOIN fcperinfo p ON p.perno = d.perno
      INNER JOIN fcstafffamilyrela f ON f.perno = p.perno
      INNER JOIN fcperson s ON s.personid = f.personid
      WHERE s.personid = #{personID,jdbcType=VARCHAR}
        AND d.IsValidy = '1'
        <![CDATA[AND DATE_FORMAT(d.OpenDay, '%Y-%m-%d') <=  DATE_FORMAT(NOW(), '%Y-%m-%d') AND DATE_FORMAT(d.CloseDay, '%Y-%m-%d') >=  DATE_FORMAT(NOW(), '%Y-%m-%d')]]>
        ORDER BY d.ModifyDATE DESC,d.MOdifytime DESC
  </select>
    <select id="selectLevelCodeByPerNo" parameterType="java.lang.String" resultType="java.lang.String">
      select LevelCode from fcperregistday where PerNo = #{perNo} and EnsureCode = #{ensureCode}
    </select>

    <update id="updateCloseDayByEnsureCode" parameterType="com.sinosoft.eflex.model.FCPerRegistDay">
    update fcperregistday
    <set>
      <if test="isValidy != null">
        IsValidy = #{isValidy,jdbcType=VARCHAR},
      </if>
      <if test="closeDay != null">
        CloseDay = #{closeDay,jdbcType=DATE},
      </if>
      <if test="lockState != null">
        LockState = #{lockState,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPerNo">
    update fcperregistday
    <set>
      <if test="staffGrpPrem != null and staffGrpPrem !=''">
        StaffGrpPrem = #{staffGrpPrem,jdbcType=DOUBLE},
      </if>
    </set>
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR} and PerNo=#{perNo,jdbcType=VARCHAR}
  </update>
  <select id="selectEnsureCodeByPerNo" resultType="java.lang.String">
      select EnsureCode from fcperregistday where PerNo = #{PerNo}
    </select>
    <select id="selectByEnsureCode" resultType="com.sinosoft.eflex.model.FCPerRegistDay">
      select * from fcperregistday  where EnsureCode=#{ensureCode} and PerNo =#{perNo}
    </select>
</mapper>