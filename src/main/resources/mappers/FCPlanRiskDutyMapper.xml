<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCPlanRiskDutyMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCPlanRiskDuty">
        <id column="EnsureCode" jdbcType="VARCHAR" property="ensureCode"/>
        <id column="PlanCode" jdbcType="VARCHAR" property="planCode"/>
        <id column="RiskCode" jdbcType="VARCHAR" property="riskCode"/>
        <id column="DutyCode" jdbcType="VARCHAR" property="dutyCode"/>
        <result column="DutyName" jdbcType="VARCHAR" property="dutyName"/>
        <result column="Amnt" jdbcType="DOUBLE" property="amnt"/>
        <result column="Prem" jdbcType="DOUBLE" property="prem"/>
        <result column="GetLimit" jdbcType="DOUBLE" property="getLimit"/>
        <result column="GetLimitType" jdbcType="VARCHAR" property="getLimitType"/>
        <result column="GetRatio" jdbcType="DOUBLE" property="getRatio"/>
        <result column="MaxGetDay" jdbcType="DECIMAL" property="maxGetDay"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        EnsureCode
        , PlanCode, RiskCode, DutyCode, DutyName, Amnt,
		Prem, GetLimit,GetLimitType,
		GetRatio,MaxGetDay,
		Operator, OperatorCom, MakeDate, MakeTime,
		ModifyDate, ModifyTime
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPlanRiskDutyKey"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcplanriskduty
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and PlanCode = #{planCode,jdbcType=VARCHAR}
        and RiskCode =
        #{riskCode,jdbcType=VARCHAR}
        and DutyCode =
        #{dutyCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPlanRiskDutyKey">
        delete
        from fcplanriskduty
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PlanCode = #{planCode,jdbcType=VARCHAR}
          and RiskCode =
              #{riskCode,jdbcType=VARCHAR}
          and DutyCode =
              #{dutyCode,jdbcType=VARCHAR}
    </delete>

    <!--计划详情查询 -->
    <select id="selectPlanDetailList" resultMap="BaseResultMap">
        select
        DutyName,Amnt,Prem
        from
        fcplanriskduty
        <where>
            <if test="ensureCode != null and ensureCode !=''">
                and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            </if>
            <if test="planCode != null and planCode !=''">
                and PlanCode = #{planCode,jdbcType=VARCHAR}
            </if>
            <if test="riskCode != null and riskCode !=''">
                and RiskCode = #{riskCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCPlanRiskDuty">
        insert into fcplanriskduty
        (EnsureCode, PlanCode, RiskCode,
         DutyCode, DutyName, Amnt,
         Prem,
         GetLimit, GetLimitType, GetRatio,
         Operator, OperatorCom, MakeDate,
         MakeTime,
         ModifyDate, ModifyTime)
        values (#{ensureCode,jdbcType=VARCHAR},
                #{planCode,jdbcType=VARCHAR},
                #{riskCode,jdbcType=VARCHAR},
                #{dutyCode,jdbcType=VARCHAR}, #{dutyName,jdbcType=VARCHAR},
                #{amnt,jdbcType=DOUBLE},
                #{prem,jdbcType=DOUBLE},
                #{getLimit,jdbcType=DOUBLE},
                #{getLimitType,jdbcType=VARCHAR},
                #{getRatio,jdbcType=DOUBLE},
                #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR},
                #{makeDate,jdbcType=DATE},
                #{makeTime,jdbcType=VARCHAR},
                #{modifyDate,jdbcType=DATE},
                #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCPlanRiskDuty">
        insert into fcplanriskduty
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                EnsureCode,
            </if>
            <if test="planCode != null">
                PlanCode,
            </if>
            <if test="riskCode != null">
                RiskCode,
            </if>
            <if test="dutyCode != null">
                DutyCode,
            </if>
            <if test="dutyName != null">
                DutyName,
            </if>
            <if test="amnt != null">
                Amnt,
            </if>
            <if test="prem != null">
                Prem,
            </if>
            <if test="getLimit != null">
                GetLimit,
            </if>
            <if test="getLimitType != null">
                GetLimitType,
            </if>
            <if test="getRatio != null">
                GetRatio,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="riskCode != null">
                #{riskCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="amnt != null">
                #{amnt,jdbcType=DOUBLE},
            </if>
            <if test="prem != null">
                #{prem,jdbcType=DOUBLE},
            </if>
            <if test="getLimit != null">
                #{getLimit,jdbcType=DOUBLE},
            </if>
            <if test="getLimitType != null">
                #{getLimitType,jdbcType=VARCHAR},
            </if>
            <if test="getRatio != null">
                #{getRatio,jdbcType=DOUBLE},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCPlanRiskDuty">
        update fcplanriskduty
        <set>
            <if test="dutyName != null">
                DutyName = #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="amnt != null">
                Amnt = #{amnt,jdbcType=DOUBLE},
            </if>
            <if test="prem != null">
                Prem = #{prem,jdbcType=DOUBLE},
            </if>
            <if test="getLimit != null">
                GetLimit = #{getLimit,jdbcType=DOUBLE},
            </if>
            <if test="getLimitType != null">
                GetLimitType = #{getLimitType,jdbcType=VARCHAR},
            </if>
            <if test="getRatio != null">
                GetRatio = #{getRatio,jdbcType=DOUBLE},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and PlanCode =
        #{planCode,jdbcType=VARCHAR}
        and RiskCode =
        #{riskCode,jdbcType=VARCHAR}
        and DutyCode =
        #{dutyCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPlanRiskDuty">
        update
            fcplanriskduty
        set DutyName     = #{dutyName,jdbcType=VARCHAR},
            Amnt         =
                #{amnt,jdbcType=DOUBLE},
            Prem         = #{prem,jdbcType=DOUBLE},
            GetLimit     =
                #{getLimit,jdbcType=DOUBLE},
            GetLimitType = #{getLimitType,jdbcType=VARCHAR},
            GetRatio     = #{getRatio,jdbcType=DOUBLE},
            Operator     = #{operator,jdbcType=VARCHAR},
            OperatorCom  =
                #{operatorCom,jdbcType=VARCHAR},
            MakeDate     = #{makeDate,jdbcType=DATE},
            MakeTime     = #{makeTime,jdbcType=VARCHAR},
            ModifyDate   =
                #{modifyDate,jdbcType=DATE},
            ModifyTime   =
                #{modifyTime,jdbcType=VARCHAR}
        where EnsureCode =
              #{ensureCode,jdbcType=VARCHAR}
          and PlanCode =
              #{planCode,jdbcType=VARCHAR}
          and RiskCode =
              #{riskCode,jdbcType=VARCHAR}
          and DutyCode =
              #{dutyCode,jdbcType=VARCHAR}
    </update>

    <select id="selectRiskPremMap" parameterType="java.util.Map" resultType="java.util.HashMap">
        select
        RiskCode, SUM(Prem) as Prem
        from
        fcplanriskduty
        <where>
            1=1
            <if test="ensureCode != null and ensureCode !=''">
                and ensureCode = #{ensureCode,jdbcType=VARCHAR}
            </if>
            <if test="planCode != null and planCode !=''">
                and PlanCode = #{planCode,jdbcType=VARCHAR}
            </if>
        </where>
        group by RiskCode order by RiskCode
    </select>

    <select id="selectDutyList" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        fcplanriskduty
        <where>
            <if test="ensureCode != null and ensureCode !=''">
                and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            </if>
            <if test="planCode != null and planCode !=''">
                and PlanCode = #{planCode,jdbcType=VARCHAR}
            </if>
            <if test="riskCode != null and riskCode !=''">
                and RiskCode = #{riskCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectDutyListOrderByDuty" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        fcplanriskduty
        <where>
            1 = 1
            <if test="ensureCode != null and ensureCode !=''">
                and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            </if>
            <if test="planCode != null and planCode !=''">
                and PlanCode = #{planCode,jdbcType=VARCHAR}
            </if>
            <if test="riskCode != null and riskCode !=''">
                and RiskCode = #{riskCode,jdbcType=VARCHAR}
            </if>
            and dutyCode in ('GD0050','GD0051','GD0052','GD0054') order by Amnt desc limit 1;
        </where>
    </select>


    <select id="selectTotalPremPlan" parameterType="java.lang.String" resultType="java.lang.Double">
        select sum(Prem) as TotalPrem
        from fcplanriskduty
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PlanCode = #{planCode,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByEnsureCode" parameterType="java.lang.String">
        delete
        from fcplanriskduty
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </delete>

    <select id="selectDutyInfo" parameterType="Map" resultType="com.sinosoft.eflex.model.FCPlanRiskDuty">
        select DutyCode, DutyName, Amnt, Prem, GetLimit, GetRatio
        from fcplanriskduty
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PlanCode = #{planCode,jdbcType=VARCHAR}
          and RiskCode = #{riskCode,jdbcType=VARCHAR}
    </select>

    <insert id="insertList" parameterType="java.util.List">
        insert into fcplanriskduty
        (EnsureCode, PlanCode, RiskCode,
        DutyCode, DutyName, Amnt,
        Prem,
        GetLimit,GetLimitType, GetRatio,MaxGetDay,
        Operator, OperatorCom, MakeDate,
        MakeTime,
        ModifyDate, ModifyTime
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.ensureCode,jdbcType=VARCHAR},
            #{item.planCode,jdbcType=VARCHAR},
            #{item.riskCode,jdbcType=VARCHAR},
            #{item.dutyCode,jdbcType=VARCHAR},
            #{item.dutyName,jdbcType=VARCHAR},
            #{item.amnt,jdbcType=DOUBLE},
            #{item.prem,jdbcType=DOUBLE},
            #{item.getLimit,jdbcType=DOUBLE},
            #{item.getLimitType,jdbcType=VARCHAR},
            #{item.getRatio,jdbcType=DOUBLE},
            #{item.maxGetDay,jdbcType=DECIMAL},
            #{item.operator,jdbcType=VARCHAR},
            #{item.operatorCom,jdbcType=VARCHAR},
            #{item.makeDate,jdbcType=DATE},
            #{item.makeTime,jdbcType=VARCHAR},
            #{item.modifyDate,jdbcType=DATE},
            #{item.modifyTime,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <!-- 企业计划汇总 -->
    <select id="selectPlanCollection" resultMap="BaseResultMap"
            parameterType="java.util.Map">
        select
        sum(Amnt) Amnt,sum(Prem) Prem
        from
        fcplanriskduty
        <where>
            <if test="ensureCode != null and ensureCode !=''">
                and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            </if>
            <if test="planCode != null and planCode !=''">
                and PlanCode = #{planCode,jdbcType=VARCHAR}
            </if>
            <if test="riskCode != null and riskCode !=''">
                and RiskCode = #{riskCode,jdbcType=VARCHAR}
            </if>
        </where>
        GROUP BY EnsureCode,PlanCode,RiskCode
    </select>

    <select id="selectDutyListByPlanCode" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT r1.`RiskName`    AS riskName,
               r1.`RiskCode`    AS riskCode,
               d1.`DutyName`    AS dutyName,
               d1.`DutyRange`   AS dutyRange,
               d.`Amnt`         AS amnt,
               d.`Prem`         AS prem,
               d.`GetLimit`     AS getLimit,
               d.`GetLimitType` AS getLimitType,
               d.`GetRatio`     AS getRatio,
               d.`MaxGetDay`    AS maxGetDay
        FROM fcplanriskduty d
                 INNER JOIN fcplanrisk r ON r.`Riskcode` = d.`RiskCode`
                 INNER JOIN fdriskinfo r1 ON r1.`RiskCode` = r.`RiskCode`
                 INNER JOIN fdriskdutyinfo d1 ON d1.`DutyCode` = d.`DutyCode`
        WHERE d.`PlanCode` = #{planCode}
          AND d.`PlanCode` = r.`PlanCode`
          AND d.`EnsureCode` = r.`EnsureCode`
          AND d.`EnsureCode` = #{ensureCode}
    </select>

    <select id="selectCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from fcplanriskduty
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and RiskCode = #{riskCode,jdbcType=VARCHAR}
          and (Prem is not null and prem != 0)
    </select>

    <delete id="deleteByEnsureCodeAndPlanCode" parameterType="java.lang.String">
        delete
        from fcplanriskduty
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PlanCode = #{planCode,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteRiskInfo" parameterType="com.sinosoft.eflex.model.FCPlanRiskDutyKey">
        delete
        from fcplanriskduty
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PlanCode = #{planCode,jdbcType=VARCHAR}
          and RiskCode = #{riskCode,jdbcType=VARCHAR}
          and case
                  when #{riskCode} = '15070' then
                      case
                          when #{dutyCode} = 'GD0050' then dutyCode in ('GD0050', 'GD0055')
                          when #{dutyCode} = 'GD0051' then dutyCode in ('GD0051', 'GD0056')
                          when #{dutyCode} = 'GD0052' then dutyCode in ('GD0052', 'GD0057')
                          when #{dutyCode} = 'GD0053' then dutyCode in ('GD0053', 'GD0058')
                          else dutyCode in ('GD0054', 'GD0059') end
                  else 1 = 1 end
    </delete>
    <delete id="deleteRiskDutyInfo" parameterType="com.sinosoft.eflex.model.FCPlanRiskDutyKey">
        delete
        from fcplanriskduty
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PlanCode = #{planCode,jdbcType=VARCHAR}
          and RiskCode = #{riskCode,jdbcType=VARCHAR}
          and DutyCode = #{dutyCode,jdbcType=VARCHAR}
    </delete>

    <select id="slectRiskDutyAndPlanRisk" parameterType="com.sinosoft.eflex.model.FCPlanRiskDuty"
            resultType="com.sinosoft.eflex.model.FCPlanRiskDuty">
        SELECT d.`RiskCode`       AS riskCode,
               r1.`RiskName`      AS riskName,
               d.`DutyCode`       AS dutyCode,
               d1.`DutyName`      AS dutyName,
               d.`Amnt`           AS amnt,
               d.`Prem`           AS prem,
               d.`GetLimit`       AS getLimit,
               d.`GetLimitType`   AS getLimitType,
               d.`GetRatio` * 100 AS getRatio,
               d.`MaxGetDay`      AS maxGetDay
        FROM fcplanriskduty d
                 INNER JOIN fdriskinfo r1 ON r1.`RiskCode` = d.`RiskCode`
                 INNER JOIN fdriskdutyinfo d1 ON d1.`DutyCode` = d.`DutyCode`
        WHERE d.`PlanCode` = #{planCode,jdbcType=VARCHAR}
          AND d.`EnsureCode` = #{ensureCode,jdbcType=VARCHAR}
          AND d.`RiskCode` = #{riskCode,jdbcType=VARCHAR}
          AND d.`DutyCode` != 'GD0071'
        AND case when #{riskCode} = '15070' then
            case when #{dutyCode} = 'GD0050' then d.`dutyCode` in ('GD0050'
            , 'GD0055')
            when #{dutyCode} = 'GD0051' then d.`dutyCode` in ('GD0051'
            , 'GD0056')
            when #{dutyCode} = 'GD0052' then d.`dutyCode` in ('GD0052'
            , 'GD0057')
            when #{dutyCode} = 'GD0053' then d.`dutyCode` in ('GD0053'
            , 'GD0058')
            else d.`dutyCode` in ('GD0054'
            , 'GD0059')
        end
        else 1=1
        end
    </select>

    <select id="slectRiskDutyAndEnsurePlan" parameterType="com.sinosoft.eflex.model.FCPlanRiskDuty"
            resultType="com.sinosoft.eflex.model.FCPlanRiskDuty">
        SELECT d.`RiskCode`    AS riskCode,
               r1.`RiskName`   AS riskName,
               d.`DutyCode`    AS dutyCode,
               d1.`DutyName`   AS dutyName,
               d.`Amnt`        AS amnt,
               d.`Prem`        AS prem,
               fe.`PlanName`   AS planName,
               fe.`PlanObject` AS planObject,
               fe.`PlanKey`    AS planKey
        FROM fcplanriskduty d
                 INNER JOIN fdriskinfo r1 ON r1.`RiskCode` = d.`RiskCode`
                 INNER JOIN fdriskdutyinfo d1 ON d1.`DutyCode` = d.`DutyCode`
                 INNER JOIN fcensureplan fe
        WHERE d.`PlanCode` = #{planCode,jdbcType=VARCHAR}
          AND fe.`EnsureCode` = d.`EnsureCode`
          AND fe.`PlanCode` = d.`PlanCode`
          AND d.`EnsureCode` = #{ensureCode,jdbcType=VARCHAR}
          AND d.`DutyCode` != 'GD0071'
        ORDER BY d.`MakeDate`, d.`MakeTime` DESC
    </select>
    <delete id="deleteMakeingRiskDutyInfoByEnsureCode" parameterType="java.lang.String">
        delete
        from fcplanriskduty
        where EnsureCode = #{EnsureCode}
          and PlanCode in (select planCode from fcensureplan where EnsureCode = #{EnsureCode} and planState = '01')
    </delete>
    <select id="selectRiskDutyListByPlanCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        fcplanriskduty
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and PlanCode = #{planCode,jdbcType=VARCHAR}
    </select>
    <select id="selectSumPrem" resultMap="BaseResultMap">
        select
        sum(Prem) as prem,
        sum(amnt) as amnt
        from
        fcplanriskduty
        <where>
            <if test="ensureCode != null and ensureCode !=''">
                and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            </if>
            <if test="planCode != null and planCode !=''">
                and PlanCode = #{planCode,jdbcType=VARCHAR}
            </if>
            <if test="riskCode != null and riskCode !=''">
                and RiskCode = #{riskCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>


    <!--投保清单导出excel -->
    <select id="selectInsuredDetail" resultMap="BaseResultMap">
        select
        DutyCode,DutyName,Amnt,Prem,GetLimit,GetRatio*100 GetRatio,MaxGetDay
        from
        fcplanriskduty
        <where>
            EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            and PlanCode = #{planCode,jdbcType=VARCHAR}
            and RiskCode = #{riskCode,jdbcType=VARCHAR}
        </where>
    </select>


    <update id="updateByEnsureCode" parameterType="com.sinosoft.eflex.model.FCPlanRiskDuty">
        update fcplanriskduty
        <set>
            <if test="ensureCode != null">
                EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            </if>
        </set>
        where EnsureCode = #{jEnsureCode,jdbcType=VARCHAR}
    </update>

</mapper>