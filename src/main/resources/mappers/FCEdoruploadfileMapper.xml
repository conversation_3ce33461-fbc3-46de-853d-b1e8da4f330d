<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCEdoruploadfileMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCEdoruploadfile">
    <result column="GrpContNo" jdbcType="VARCHAR" property="grpContNo" />
    <result column="Batch" jdbcType="VARCHAR" property="batch" />
    <result column="DocType" jdbcType="VARCHAR" property="docType" />
    <result column="fileName" jdbcType="VARCHAR" property="fileName" />
    <result column="localPath" jdbcType="VARCHAR" property="localPath" />
    <result column="ftpPath" jdbcType="VARCHAR" property="ftpPath" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCEdoruploadfile">
    insert into fcedoruploadfile (GrpContNo, Batch, DocType, fileName,
      localPath, ftpPath, Operator, 
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{grpContNo,jdbcType=VARCHAR}, #{batch,jdbcType=VARCHAR}, #{docType,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR},
      #{localPath,jdbcType=VARCHAR}, #{ftpPath,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCEdoruploadfile">
    insert into fcedoruploadfile
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="grpContNo != null">
        GrpContNo,
      </if>
      <if test="batch != null">
        Batch,
      </if>
      <if test="docType != null">
        DocType,
      </if>
      <if test="fileName != null">
        fileName,
      </if>
      <if test="localPath != null">
        localPath,
      </if>
      <if test="ftpPath != null">
        ftpPath,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="grpContNo != null">
        #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=VARCHAR},
      </if>
      <if test="docType != null">
        #{docType,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="localPath != null">
        #{localPath,jdbcType=VARCHAR},
      </if>
      <if test="ftpPath != null">
        #{ftpPath,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <delete id="deleteUploadfile" parameterType="java.util.Map">
    delete from fcedoruploadfile
    where  
    	grpContNo = #{grpContNo,jdbcType=VARCHAR}
    and batch = #{batch,jdbcType=VARCHAR}    
 	and docType = #{docType,jdbcType=VARCHAR}    
 	<if test="fileName != null">
 	   and fileName = #{fileName,jdbcType=VARCHAR}    
 	</if>
 	<if test="localPath != null">
 	   and localPath like  CONCAT(CONCAT(#{localPath}), '%')
 	</if>
 	
  </delete>
  <select id="selectUploadfileByGrpContNo" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCEdoruploadfile">
  	 select * from fcedoruploadfile
    where 1=1
    <if test="grpContNo != null">
       and grpContNo = #{grpContNo,jdbcType=VARCHAR}
    </if>
    <if test="batch != null">
 	   and batch = #{batch,jdbcType=VARCHAR}    
 	</if>
 	<if test="docType != null">
 	   and docType = #{docType,jdbcType=VARCHAR}    
 	</if>
  </select>
</mapper>